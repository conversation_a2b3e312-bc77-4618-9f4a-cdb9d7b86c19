<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Debug Test - Blackveil Design Mind</title>
    <style>
        /* Direct CSS injection test - exactly as in main.tsx */
        :root {
            --aone-sage: 82 25% 45% !important;
            --aone-sage-foreground: 0 0% 100% !important;
            --aone-sage-light: 82 25% 65% !important;
            --aone-sage-dark: 82 25% 35% !important;
            --aone-cream: 48 20% 95% !important;
            --aone-cream-foreground: 82 25% 25% !important;
            --aone-charcoal: 0 0% 18% !important;
            --aone-charcoal-foreground: 0 0% 100% !important;
            --aone-warm-white: 48 10% 98% !important;
            --aone-warm-white-foreground: 0 0% 18% !important;
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
        }

        .dark {
            --aone-sage: 82 30% 60% !important;
            --aone-sage-foreground: 222.2 84% 4.9% !important;
            --aone-sage-light: 82 30% 70% !important;
            --aone-sage-dark: 82 30% 45% !important;
            --aone-cream: 48 15% 12% !important;
            --aone-cream-foreground: 48 25% 88% !important;
            --aone-charcoal: 0 0% 88% !important;
            --aone-charcoal-foreground: 222.2 84% 4.9% !important;
            --aone-warm-white: 222.2 84% 4.9% !important;
            --aone-warm-white-foreground: 0 0% 88% !important;
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: hsl(var(--background));
            color: hsl(var(--foreground));
            transition: all 0.3s ease;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 2px solid hsl(var(--aone-sage));
            border-radius: 12px;
            background: hsl(var(--aone-cream));
        }

        .sage-test {
            background: hsl(var(--aone-sage));
            color: hsl(var(--aone-sage-foreground));
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .charcoal-test {
            background: hsl(var(--aone-charcoal));
            color: hsl(var(--aone-charcoal-foreground));
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .theme-toggle {
            background: hsl(var(--aone-sage));
            color: hsl(var(--aone-sage-foreground));
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }

        .debug-info {
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }

        .dark .debug-info {
            background: #2a2a2a;
            border-color: #555;
            color: #fff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Theme Debug Test - Blackveil Design Mind</h1>
        
        <div class="sage-test">
            <h2>Sage Color Test</h2>
            <p>This should be sage green background (#6B7A4F equivalent) with white text</p>
        </div>

        <div class="charcoal-test">
            <h2>Charcoal Color Test</h2>
            <p>This should be charcoal background with white text</p>
        </div>

        <button class="theme-toggle" onclick="toggleTheme()">Toggle Dark Mode</button>
        <button class="theme-toggle" onclick="debugTheme()">Debug Theme</button>
        <button class="theme-toggle" onclick="testCSSProperties()">Test CSS Properties</button>

        <div id="debug-output" class="debug-info">
            Debug output will appear here...
        </div>
    </div>

    <script>
        function toggleTheme() {
            document.documentElement.classList.toggle('dark');
            debugTheme();
        }

        function debugTheme() {
            const root = document.documentElement;
            const computedStyle = getComputedStyle(root);
            
            const debugInfo = {
                timestamp: new Date().toISOString(),
                classList: Array.from(root.classList),
                isDark: root.classList.contains('dark'),
                cssProperties: {
                    sageColor: computedStyle.getPropertyValue('--aone-sage').trim(),
                    sageColorRaw: computedStyle.getPropertyValue('--aone-sage'),
                    charcoalColor: computedStyle.getPropertyValue('--aone-charcoal').trim(),
                    backgroundColor: computedStyle.getPropertyValue('--background').trim(),
                    foregroundColor: computedStyle.getPropertyValue('--foreground').trim()
                },
                computedColors: {
                    sageHSL: `hsl(${computedStyle.getPropertyValue('--aone-sage').trim()})`,
                    charcoalHSL: `hsl(${computedStyle.getPropertyValue('--aone-charcoal').trim()})`,
                    backgroundHSL: `hsl(${computedStyle.getPropertyValue('--background').trim()})`
                },
                documentReady: document.readyState,
                styleSheetsCount: document.styleSheets.length
            };

            document.getElementById('debug-output').textContent = JSON.stringify(debugInfo, null, 2);
            console.log('🎨 Theme Debug Info:', debugInfo);
        }

        function testCSSProperties() {
            const testElement = document.createElement('div');
            testElement.style.cssText = `
                background: hsl(var(--aone-sage));
                color: hsl(var(--aone-sage-foreground));
                padding: 10px;
                margin: 10px 0;
                border-radius: 4px;
            `;
            testElement.textContent = 'Dynamic CSS Property Test Element';
            
            document.querySelector('.test-container').appendChild(testElement);
            
            setTimeout(() => {
                const computedStyle = getComputedStyle(testElement);
                console.log('🧪 Dynamic Element CSS Test:', {
                    backgroundColor: computedStyle.backgroundColor,
                    color: computedStyle.color,
                    sageVariable: computedStyle.getPropertyValue('--aone-sage')
                });
            }, 100);
        }

        // Auto-debug on load
        window.addEventListener('load', () => {
            setTimeout(debugTheme, 100);
        });

        // Debug immediately
        debugTheme();
    </script>
</body>
</html>
