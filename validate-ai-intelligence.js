#!/usr/bin/env node

/**
 * AI Intelligence Enhancements Quick Validation
 * 
 * Simple validation script to test AI intelligence enhancements
 * without requiring full Playwright test infrastructure.
 */

const http = require('http');

// Test configuration
const BASE_URL = 'http://localhost:3001';
const TIMEOUT = 10000; // 10 seconds

// Test endpoints
const ENDPOINTS = [
  { path: '/api/health', method: 'GET', description: 'Basic health check' },
  { path: '/api/health/detailed', method: 'GET', description: 'Detailed health with OpenAI service' },
  { path: '/api/analysis/test-openai', method: 'POST', description: 'OpenAI test endpoint' }
];

console.log('🧠 AI Intelligence Enhancements - Quick Validation\n');

// Helper function to make HTTP requests
function makeRequest(endpoint) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: endpoint.path,
      method: endpoint.method,
      timeout: TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: jsonData,
            success: res.statusCode >= 200 && res.statusCode < 300
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data,
            success: false,
            error: 'Invalid JSON response'
          });
        }
      });
    });

    req.on('error', (error) => {
      reject({
        success: false,
        error: error.message
      });
    });

    req.on('timeout', () => {
      req.destroy();
      reject({
        success: false,
        error: 'Request timeout'
      });
    });

    // Send empty body for POST requests
    if (endpoint.method === 'POST') {
      req.write('{}');
    }
    
    req.end();
  });
}

// Run validation tests
async function runValidation() {
  const results = {
    passed: 0,
    total: 0,
    details: []
  };

  console.log('Testing AI Intelligence Enhancement endpoints...\n');

  for (const endpoint of ENDPOINTS) {
    results.total++;
    
    try {
      console.log(`Testing ${endpoint.method} ${endpoint.path}...`);
      
      const result = await makeRequest(endpoint);
      
      if (result.success && result.data.success) {
        results.passed++;
        results.details.push(`✓ ${endpoint.description}`);
        
        // Log specific details for OpenAI service
        if (endpoint.path === '/api/health/detailed' && result.data.data.services.openai) {
          const openaiService = result.data.data.services.openai;
          console.log(`  • OpenAI Service Type: ${openaiService.type || 'Standard'}`);
          console.log(`  • Configured: ${openaiService.configured}`);
          
          // Check for legacy service warning (this is expected)
          if (openaiService.type && openaiService.type.includes('legacy')) {
            console.log('  • Note: Using legacy OpenAI service (backward compatibility)');
          }
        }
        
        if (endpoint.path === '/api/analysis/test-openai' && result.data.data.availableModels) {
          console.log(`  • Available Models: ${result.data.data.availableModels.length}`);
        }
        
      } else {
        results.details.push(`❌ ${endpoint.description} - ${result.error || 'Request failed'}`);
        console.log(`  ❌ Failed: ${result.error || 'Request failed'}`);
      }
      
    } catch (error) {
      results.details.push(`❌ ${endpoint.description} - ${error.error || error.message}`);
      console.log(`  ❌ Error: ${error.error || error.message}`);
    }
    
    console.log('');
  }

  // Calculate success rate
  const successRate = (results.passed / results.total) * 100;
  
  console.log('='.repeat(60));
  console.log('📊 AI Intelligence Enhancement Validation Results:');
  console.log(`📋 Success Rate: ${successRate.toFixed(1)}%`);
  console.log(`📋 Tests Passed: ${results.passed}/${results.total}`);
  console.log('');
  
  results.details.forEach(detail => console.log(`   ${detail}`));
  
  console.log('');
  
  if (successRate >= 97) {
    console.log('✅ AI Intelligence Enhancements: VALIDATED');
    console.log('🎯 Success rate meets ~97-99% standard');
    
    if (successRate === 100) {
      console.log('🌟 Perfect score! All AI intelligence features operational');
    }
  } else if (successRate >= 66) {
    console.log('⚠️  AI Intelligence Enhancements: PARTIAL');
    console.log('🔧 Some features may need attention');
  } else {
    console.log('❌ AI Intelligence Enhancements: NEEDS ATTENTION');
    console.log('🚨 Multiple features require investigation');
  }
  
  console.log('='.repeat(60));
  
  // Exit with appropriate code
  process.exit(successRate >= 97 ? 0 : 1);
}

// Check if server is running first
console.log('Checking if Blackveil Design Mind server is running...');

makeRequest({ path: '/api/health', method: 'GET' })
  .then(() => {
    console.log('✓ Server is running\n');
    runValidation();
  })
  .catch(() => {
    console.log('❌ Server is not running');
    console.log('');
    console.log('Please start the servers first:');
    console.log('  1. Frontend: npm run dev');
    console.log('  2. Backend: cd server && npm run dev');
    console.log('');
    console.log('Then run this validation again.');
    process.exit(1);
  });
