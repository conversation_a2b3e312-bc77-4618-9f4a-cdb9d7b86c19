#!/usr/bin/env node

/**
 * Pricing Data Extractor for LM3.20.xlsm
 * Extracts and converts pricing data to PostgreSQL-compatible format
 */

import XLSX from 'xlsx';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class PricingDataExtractor {
  constructor(filePath) {
    this.filePath = filePath;
    this.workbook = null;
    this.extractedData = {
      materials: [],
      hardware: [],
      laborRates: [],
      regions: [],
      suppliers: [],
      rawData: {}
    };
    this.dataQuality = {
      totalRecords: 0,
      validRecords: 0,
      errors: [],
      warnings: []
    };
  }

  async extractData() {
    try {
      console.log('🔍 Starting pricing data extraction...');
      console.log(`📁 File: ${this.filePath}`);
      
      // Read the Excel file
      this.workbook = XLSX.readFile(this.filePath);
      
      // Extract data from Materials worksheet (main pricing data)
      await this.extractMaterialsData();
      
      // Extract data from Master worksheet (configuration data)
      await this.extractMasterData();
      
      // Generate default regional and supplier data
      await this.generateDefaultData();
      
      // Validate extracted data
      await this.validateData();
      
      // Save extracted data
      await this.saveExtractedData();
      
      // Generate migration scripts
      await this.generateMigrationScripts();
      
      console.log('✅ Data extraction completed successfully!');
      return this.extractedData;

    } catch (error) {
      console.error('❌ Error extracting pricing data:', error.message);
      throw error;
    }
  }

  async extractMaterialsData() {
    console.log('\n🔍 Extracting Materials worksheet data...');
    
    const worksheet = this.workbook.Sheets['Materials'];
    if (!worksheet) {
      throw new Error('Materials worksheet not found');
    }

    // Get raw data
    const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    const jsonData = XLSX.utils.sheet_to_json(worksheet);
    
    this.extractedData.rawData.materials = jsonData;
    
    // Parse the complex structure - Materials sheet has multiple categories in columns
    const categories = this.identifyMaterialCategories(rawData);
    
    console.log(`📊 Found ${categories.length} material categories`);
    
    // Extract data for each category
    for (const category of categories) {
      await this.extractCategoryData(category, rawData);
    }
    
    console.log(`✅ Extracted ${this.extractedData.materials.length} material records`);
    console.log(`✅ Extracted ${this.extractedData.hardware.length} hardware records`);
  }

  identifyMaterialCategories(rawData) {
    // The first row contains category headers
    const headerRow = rawData[0] || [];
    const categories = [];
    
    let currentCategory = null;
    let startCol = 0;
    
    for (let i = 0; i < headerRow.length; i++) {
      const header = headerRow[i];
      
      if (header && header.trim() !== '') {
        // Save previous category
        if (currentCategory) {
          currentCategory.endCol = i - 1;
          categories.push(currentCategory);
        }
        
        // Start new category
        currentCategory = {
          name: header.trim(),
          startCol: i,
          endCol: headerRow.length - 1, // Will be updated when next category starts
          type: this.categorizeMaterial(header.trim())
        };
      }
    }
    
    // Add the last category
    if (currentCategory) {
      categories.push(currentCategory);
    }
    
    return categories;
  }

  categorizeMaterial(categoryName) {
    const name = categoryName.toLowerCase();
    
    if (name.includes('board') || name.includes('panel') || name.includes('cabinet') || 
        name.includes('pvc') || name.includes('door')) {
      return 'material';
    } else if (name.includes('handle') || name.includes('hinge') || name.includes('drawer') || 
               name.includes('accessories') || name.includes('fitting')) {
      return 'hardware';
    } else if (name.includes('machining') || name.includes('cost')) {
      return 'labor';
    } else {
      return 'unknown';
    }
  }

  async extractCategoryData(category, rawData) {
    console.log(`  📋 Processing category: ${category.name} (${category.type})`);
    
    // Extract data rows for this category
    for (let rowIndex = 1; rowIndex < rawData.length; rowIndex++) {
      const row = rawData[rowIndex];
      if (!row || row.length === 0) continue;
      
      // Extract data from the category's column range
      const categoryData = this.extractRowData(row, category, rowIndex);
      
      if (categoryData && this.isValidRecord(categoryData)) {
        // Add to appropriate collection based on type
        switch (category.type) {
          case 'material':
            this.extractedData.materials.push(categoryData);
            break;
          case 'hardware':
            this.extractedData.hardware.push(categoryData);
            break;
          case 'labor':
            this.extractedData.laborRates.push(categoryData);
            break;
        }
        
        this.dataQuality.validRecords++;
      }
      
      this.dataQuality.totalRecords++;
    }
  }

  extractRowData(row, category, rowIndex) {
    // Extract data from the category's column range
    const categoryColumns = row.slice(category.startCol, category.endCol + 1);
    
    // Find the first non-empty cell as the item name/description
    const itemName = categoryColumns.find(cell => cell && cell.toString().trim() !== '');
    
    if (!itemName) return null;
    
    // Look for price data in the category columns
    const priceData = this.extractPriceData(categoryColumns);
    
    return {
      id: `${category.name.toLowerCase().replace(/\s+/g, '_')}_${rowIndex}`,
      category: category.name,
      subcategory: this.determineSubcategory(category.name, itemName),
      name: itemName.toString().trim(),
      description: itemName.toString().trim(),
      type: category.type,
      ...priceData,
      sourceRow: rowIndex,
      sourceCategory: category.name
    };
  }

  extractPriceData(categoryColumns) {
    const priceData = {
      basePrice: null,
      unitOfMeasure: 'each',
      specifications: {}
    };
    
    // Look for numeric values that could be prices
    for (const cell of categoryColumns) {
      if (typeof cell === 'number' && cell > 0) {
        if (!priceData.basePrice || cell > priceData.basePrice) {
          priceData.basePrice = cell;
        }
      } else if (typeof cell === 'string') {
        // Check if it's a price string
        const priceMatch = cell.match(/\$?([\d,]+\.?\d*)/);
        if (priceMatch) {
          const price = parseFloat(priceMatch[1].replace(/,/g, ''));
          if (!priceData.basePrice || price > priceData.basePrice) {
            priceData.basePrice = price;
          }
        }
        
        // Check for unit of measure
        if (cell.toLowerCase().includes('m²') || cell.toLowerCase().includes('sqm')) {
          priceData.unitOfMeasure = 'sq_m';
        } else if (cell.toLowerCase().includes('lm') || cell.toLowerCase().includes('linear')) {
          priceData.unitOfMeasure = 'linear_m';
        } else if (cell.toLowerCase().includes('each') || cell.toLowerCase().includes('unit')) {
          priceData.unitOfMeasure = 'each';
        }
      }
    }
    
    return priceData;
  }

  determineSubcategory(categoryName, itemName) {
    const category = categoryName.toLowerCase();
    const item = itemName.toLowerCase();
    
    if (category.includes('board')) {
      if (item.includes('mdf')) return 'mdf';
      if (item.includes('plywood')) return 'plywood';
      if (item.includes('particle')) return 'particleboard';
      return 'board';
    } else if (category.includes('handle')) {
      if (item.includes('knob')) return 'knob';
      if (item.includes('pull')) return 'pull';
      return 'handle';
    } else if (category.includes('hinge')) {
      if (item.includes('soft')) return 'soft_close';
      if (item.includes('concealed')) return 'concealed';
      return 'hinge';
    } else if (category.includes('drawer')) {
      if (item.includes('slide')) return 'slide';
      if (item.includes('box')) return 'drawer_box';
      return 'drawer';
    }
    
    return 'standard';
  }

  isValidRecord(record) {
    return record && 
           record.name && 
           record.name.trim() !== '' && 
           record.basePrice !== null && 
           record.basePrice > 0;
  }

  async extractMasterData() {
    console.log('\n🔍 Extracting Master worksheet configuration data...');
    
    const worksheet = this.workbook.Sheets['Master'];
    if (!worksheet) {
      console.log('⚠️ Master worksheet not found, skipping...');
      return;
    }

    const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    this.extractedData.rawData.master = rawData;
    
    // Extract configuration data like unit dimensions, pricing factors, etc.
    // This data can be used for labor calculations and regional adjustments
    console.log('✅ Master data extracted for reference');
  }

  async generateDefaultData() {
    console.log('\n🔍 Generating default regional and supplier data...');
    
    // Generate default regions
    this.extractedData.regions = [
      {
        id: 1,
        regionCode: 'AU_NATIONAL',
        regionName: 'Australia National',
        costOfLivingMultiplier: 1.0,
        taxRate: 0.10, // 10% GST
        shippingMultiplier: 1.0,
        marketConditions: 'average',
        seasonalAdjustment: 1.0
      },
      {
        id: 2,
        regionCode: 'AU_SYDNEY',
        regionName: 'Sydney Metro',
        costOfLivingMultiplier: 1.25,
        taxRate: 0.10,
        shippingMultiplier: 1.1,
        marketConditions: 'high',
        seasonalAdjustment: 1.0
      },
      {
        id: 3,
        regionCode: 'AU_MELBOURNE',
        regionName: 'Melbourne Metro',
        costOfLivingMultiplier: 1.20,
        taxRate: 0.10,
        shippingMultiplier: 1.05,
        marketConditions: 'high',
        seasonalAdjustment: 1.0
      }
    ];
    
    // Generate default suppliers
    this.extractedData.suppliers = [
      {
        id: 1,
        name: 'LM Kitchen Supplies',
        contactInfo: { email: '<EMAIL>', phone: '+61-xxx-xxx-xxx' },
        paymentTerms: '30 days',
        leadTimeDays: 14,
        minimumOrder: 500.00,
        qualityRating: 4.5,
        reliabilityRating: 4.8,
        isActive: true
      }
    ];
    
    console.log(`✅ Generated ${this.extractedData.regions.length} default regions`);
    console.log(`✅ Generated ${this.extractedData.suppliers.length} default suppliers`);
  }

  async validateData() {
    console.log('\n🔍 Validating extracted data...');

    // Validate materials
    this.validateMaterials();

    // Validate hardware
    this.validateHardware();

    // Validate labor rates
    this.validateLaborRates();

    // Generate validation report
    this.generateValidationReport();
  }

  validateMaterials() {
    const materials = this.extractedData.materials;
    let validCount = 0;

    for (const material of materials) {
      const errors = [];

      if (!material.name || material.name.trim() === '') {
        errors.push('Missing material name');
      }

      if (!material.basePrice || material.basePrice <= 0) {
        errors.push('Invalid or missing base price');
      }

      if (!material.category) {
        errors.push('Missing category');
      }

      if (errors.length > 0) {
        this.dataQuality.errors.push({
          type: 'material',
          id: material.id,
          errors: errors
        });
      } else {
        validCount++;
      }
    }

    console.log(`  📊 Materials: ${validCount}/${materials.length} valid records`);
  }

  validateHardware() {
    const hardware = this.extractedData.hardware;
    let validCount = 0;

    for (const item of hardware) {
      const errors = [];

      if (!item.name || item.name.trim() === '') {
        errors.push('Missing hardware name');
      }

      if (!item.basePrice || item.basePrice <= 0) {
        errors.push('Invalid or missing base price');
      }

      if (!item.category) {
        errors.push('Missing category');
      }

      if (errors.length > 0) {
        this.dataQuality.errors.push({
          type: 'hardware',
          id: item.id,
          errors: errors
        });
      } else {
        validCount++;
      }
    }

    console.log(`  📊 Hardware: ${validCount}/${hardware.length} valid records`);
  }

  validateLaborRates() {
    const laborRates = this.extractedData.laborRates;
    let validCount = 0;

    for (const rate of laborRates) {
      const errors = [];

      if (!rate.name || rate.name.trim() === '') {
        errors.push('Missing labor rate name');
      }

      if (!rate.basePrice || rate.basePrice <= 0) {
        errors.push('Invalid or missing hourly rate');
      }

      if (errors.length > 0) {
        this.dataQuality.errors.push({
          type: 'labor',
          id: rate.id,
          errors: errors
        });
      } else {
        validCount++;
      }
    }

    console.log(`  📊 Labor Rates: ${validCount}/${laborRates.length} valid records`);
  }

  generateValidationReport() {
    const { totalRecords, validRecords, errors, warnings } = this.dataQuality;
    const successRate = totalRecords > 0 ? (validRecords / totalRecords * 100).toFixed(1) : 0;

    console.log(`\n📊 VALIDATION SUMMARY:`);
    console.log(`Total records processed: ${totalRecords}`);
    console.log(`Valid records: ${validRecords}`);
    console.log(`Success rate: ${successRate}%`);
    console.log(`Errors: ${errors.length}`);
    console.log(`Warnings: ${warnings.length}`);

    if (errors.length > 0) {
      console.log(`\n❌ ERRORS FOUND:`);
      errors.slice(0, 5).forEach(error => {
        console.log(`  ${error.type} ${error.id}: ${error.errors.join(', ')}`);
      });
      if (errors.length > 5) {
        console.log(`  ... and ${errors.length - 5} more errors`);
      }
    }
  }

  async saveExtractedData() {
    console.log('\n💾 Saving extracted data...');

    const outputDir = path.join(__dirname, '../extracted-data');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Save as JSON
    const jsonPath = path.join(outputDir, 'pricing-data.json');
    fs.writeFileSync(jsonPath, JSON.stringify(this.extractedData, null, 2));

    // Save as CSV files
    await this.saveAsCSV(outputDir);

    // Save validation report
    const reportPath = path.join(outputDir, 'data-quality-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(this.dataQuality, null, 2));

    console.log(`📄 Data saved to: ${outputDir}`);
  }

  async saveAsCSV(outputDir) {
    // Save materials as CSV
    if (this.extractedData.materials.length > 0) {
      const materialsCSV = this.convertToCSV(this.extractedData.materials);
      fs.writeFileSync(path.join(outputDir, 'materials.csv'), materialsCSV);
    }

    // Save hardware as CSV
    if (this.extractedData.hardware.length > 0) {
      const hardwareCSV = this.convertToCSV(this.extractedData.hardware);
      fs.writeFileSync(path.join(outputDir, 'hardware.csv'), hardwareCSV);
    }

    // Save labor rates as CSV
    if (this.extractedData.laborRates.length > 0) {
      const laborCSV = this.convertToCSV(this.extractedData.laborRates);
      fs.writeFileSync(path.join(outputDir, 'labor_rates.csv'), laborCSV);
    }

    // Save regions as CSV
    const regionsCSV = this.convertToCSV(this.extractedData.regions);
    fs.writeFileSync(path.join(outputDir, 'regions.csv'), regionsCSV);

    // Save suppliers as CSV
    const suppliersCSV = this.convertToCSV(this.extractedData.suppliers);
    fs.writeFileSync(path.join(outputDir, 'suppliers.csv'), suppliersCSV);

    console.log('📊 CSV files generated');
  }

  convertToCSV(data) {
    if (data.length === 0) return '';

    const headers = Object.keys(data[0]);
    const csvRows = [headers.join(',')];

    for (const row of data) {
      const values = headers.map(header => {
        const value = row[header];
        if (value === null || value === undefined) return '';
        if (typeof value === 'object') return JSON.stringify(value);
        if (typeof value === 'string' && value.includes(',')) return `"${value}"`;
        return value.toString();
      });
      csvRows.push(values.join(','));
    }

    return csvRows.join('\n');
  }

  async generateMigrationScripts() {
    console.log('\n🔧 Generating PostgreSQL migration scripts...');

    const outputDir = path.join(__dirname, '../migrations');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Generate schema creation script
    await this.generateSchemaScript(outputDir);

    // Generate data insertion scripts
    await this.generateInsertScripts(outputDir);

    // Generate complete migration script
    await this.generateCompleteMigrationScript(outputDir);

    console.log(`📄 Migration scripts saved to: ${outputDir}`);
  }

  async generateSchemaScript(outputDir) {
    const schemaSQL = `-- PostgreSQL Pricing Database Schema
-- Generated from LM3.20.xlsm extraction

-- Create database (run separately if needed)
-- CREATE DATABASE cabinet_pricing;

-- Materials pricing table
CREATE TABLE IF NOT EXISTS materials (
    id SERIAL PRIMARY KEY,
    category VARCHAR(50) NOT NULL,
    subcategory VARCHAR(50),
    material_type VARCHAR(100) NOT NULL,
    grade VARCHAR(20) NOT NULL DEFAULT 'standard',
    finish VARCHAR(100),
    brand VARCHAR(100),
    unit_of_measure VARCHAR(20) NOT NULL DEFAULT 'each',
    base_price DECIMAL(10,2) NOT NULL,
    min_price DECIMAL(10,2),
    max_price DECIMAL(10,2),
    supplier_id INTEGER,
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiry_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Hardware pricing table
CREATE TABLE IF NOT EXISTS hardware (
    id SERIAL PRIMARY KEY,
    category VARCHAR(50) NOT NULL,
    subcategory VARCHAR(50),
    brand VARCHAR(100) NOT NULL,
    model VARCHAR(100),
    finish VARCHAR(50),
    specifications JSONB,
    unit_price DECIMAL(10,2) NOT NULL,
    bulk_pricing JSONB,
    supplier_id INTEGER,
    compatibility JSONB,
    installation_complexity VARCHAR(20) DEFAULT 'medium',
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiry_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Labor rates table
CREATE TABLE IF NOT EXISTS labor_rates (
    id SERIAL PRIMARY KEY,
    category VARCHAR(50) NOT NULL,
    subcategory VARCHAR(50),
    skill_level VARCHAR(20) NOT NULL DEFAULT 'journeyman',
    hourly_rate DECIMAL(8,2) NOT NULL,
    minimum_hours DECIMAL(4,2),
    complexity_multiplier DECIMAL(3,2) DEFAULT 1.0,
    region_id INTEGER,
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiry_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Regional factors table
CREATE TABLE IF NOT EXISTS regions (
    id SERIAL PRIMARY KEY,
    region_code VARCHAR(20) UNIQUE NOT NULL,
    region_name VARCHAR(100) NOT NULL,
    cost_of_living_multiplier DECIMAL(3,2) NOT NULL DEFAULT 1.0,
    tax_rate DECIMAL(4,4) NOT NULL DEFAULT 0.0,
    shipping_multiplier DECIMAL(3,2) DEFAULT 1.0,
    market_conditions VARCHAR(20) DEFAULT 'average',
    seasonal_adjustment DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Suppliers table
CREATE TABLE IF NOT EXISTS suppliers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    contact_info JSONB,
    payment_terms VARCHAR(100),
    lead_time_days INTEGER,
    minimum_order DECIMAL(10,2),
    bulk_discount_tiers JSONB,
    quality_rating DECIMAL(2,1),
    reliability_rating DECIMAL(2,1),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_materials_category ON materials(category);
CREATE INDEX IF NOT EXISTS idx_materials_type_grade ON materials(material_type, grade);
CREATE INDEX IF NOT EXISTS idx_hardware_category_brand ON hardware(category, brand);
CREATE INDEX IF NOT EXISTS idx_labor_category_region ON labor_rates(category, region_id);
CREATE INDEX IF NOT EXISTS idx_regions_code ON regions(region_code);
CREATE INDEX IF NOT EXISTS idx_suppliers_active ON suppliers(is_active);

-- Foreign key constraints
ALTER TABLE materials ADD CONSTRAINT fk_materials_supplier
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id);
ALTER TABLE hardware ADD CONSTRAINT fk_hardware_supplier
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id);
ALTER TABLE labor_rates ADD CONSTRAINT fk_labor_region
    FOREIGN KEY (region_id) REFERENCES regions(id);
`;

    fs.writeFileSync(path.join(outputDir, '001_create_pricing_schema.sql'), schemaSQL);
  }

  async generateInsertScripts(outputDir) {
    // Generate regions insert script
    const regionsSQL = this.generateInsertSQL('regions', this.extractedData.regions);
    fs.writeFileSync(path.join(outputDir, '002_insert_regions.sql'), regionsSQL);

    // Generate suppliers insert script
    const suppliersSQL = this.generateInsertSQL('suppliers', this.extractedData.suppliers);
    fs.writeFileSync(path.join(outputDir, '003_insert_suppliers.sql'), suppliersSQL);

    // Generate materials insert script
    if (this.extractedData.materials.length > 0) {
      const materialsSQL = this.generateMaterialsInsertSQL();
      fs.writeFileSync(path.join(outputDir, '004_insert_materials.sql'), materialsSQL);
    }

    // Generate hardware insert script
    if (this.extractedData.hardware.length > 0) {
      const hardwareSQL = this.generateHardwareInsertSQL();
      fs.writeFileSync(path.join(outputDir, '005_insert_hardware.sql'), hardwareSQL);
    }

    // Generate labor rates insert script
    if (this.extractedData.laborRates.length > 0) {
      const laborSQL = this.generateLaborInsertSQL();
      fs.writeFileSync(path.join(outputDir, '006_insert_labor_rates.sql'), laborSQL);
    }
  }

  generateInsertSQL(tableName, data) {
    if (data.length === 0) return `-- No data to insert for ${tableName}\n`;

    const columns = Object.keys(data[0]).filter(key => key !== 'id');
    let sql = `-- Insert data into ${tableName}\n`;
    sql += `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES\n`;

    const values = data.map(row => {
      const rowValues = columns.map(col => {
        const value = row[col];
        if (value === null || value === undefined) return 'NULL';
        if (typeof value === 'object') return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
        if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
        return value;
      });
      return `  (${rowValues.join(', ')})`;
    });

    sql += values.join(',\n');
    sql += '\nON CONFLICT DO NOTHING;\n';

    return sql;
  }

  generateMaterialsInsertSQL() {
    const materials = this.extractedData.materials.map(item => ({
      category: item.category || 'unknown',
      subcategory: item.subcategory || 'standard',
      material_type: item.name,
      grade: 'standard',
      finish: null,
      brand: 'LM Kitchen Supplies',
      unit_of_measure: item.unitOfMeasure || 'each',
      base_price: item.basePrice,
      min_price: item.basePrice * 0.9,
      max_price: item.basePrice * 1.2,
      supplier_id: 1,
      effective_date: new Date().toISOString().split('T')[0]
    }));

    return this.generateInsertSQL('materials', materials);
  }

  generateHardwareInsertSQL() {
    const hardware = this.extractedData.hardware.map(item => ({
      category: item.category || 'unknown',
      subcategory: item.subcategory || 'standard',
      brand: 'LM Kitchen Supplies',
      model: item.name,
      finish: 'standard',
      specifications: JSON.stringify(item.specifications || {}),
      unit_price: item.basePrice,
      bulk_pricing: null,
      supplier_id: 1,
      compatibility: JSON.stringify({ cabinet_types: ['all'] }),
      installation_complexity: 'medium',
      effective_date: new Date().toISOString().split('T')[0]
    }));

    return this.generateInsertSQL('hardware', hardware);
  }

  generateLaborInsertSQL() {
    const laborRates = this.extractedData.laborRates.map(item => ({
      category: item.category || 'installation',
      subcategory: item.subcategory || 'standard',
      skill_level: 'journeyman',
      hourly_rate: item.basePrice,
      minimum_hours: 1.0,
      complexity_multiplier: 1.0,
      region_id: 1,
      effective_date: new Date().toISOString().split('T')[0]
    }));

    return this.generateInsertSQL('labor_rates', laborRates);
  }

  async generateCompleteMigrationScript(outputDir) {
    const migrationScript = `#!/bin/bash
# Complete PostgreSQL Migration Script for LM3.20 Pricing Database
# Generated: ${new Date().toISOString()}

echo "🚀 Starting PostgreSQL pricing database migration..."

# Check if PostgreSQL is running
if ! pg_isready -q; then
    echo "❌ PostgreSQL is not running. Please start PostgreSQL first."
    exit 1
fi

# Set database connection parameters
DB_NAME=\${PRICING_DB_NAME:-cabinet_pricing}
DB_USER=\${PRICING_DB_USER:-postgres}
DB_HOST=\${PRICING_DB_HOST:-localhost}
DB_PORT=\${PRICING_DB_PORT:-5432}

echo "📊 Database: \$DB_NAME"
echo "👤 User: \$DB_USER"
echo "🌐 Host: \$DB_HOST:\$DB_PORT"

# Create database if it doesn't exist
echo "🔧 Creating database if not exists..."
createdb -h \$DB_HOST -p \$DB_PORT -U \$DB_USER \$DB_NAME 2>/dev/null || echo "Database already exists"

# Run migration scripts in order
echo "📋 Running schema creation..."
psql -h \$DB_HOST -p \$DB_PORT -U \$DB_USER -d \$DB_NAME -f 001_create_pricing_schema.sql

echo "🌍 Inserting regions..."
psql -h \$DB_HOST -p \$DB_PORT -U \$DB_USER -d \$DB_NAME -f 002_insert_regions.sql

echo "🏢 Inserting suppliers..."
psql -h \$DB_HOST -p \$DB_PORT -U \$DB_USER -d \$DB_NAME -f 003_insert_suppliers.sql

if [ -f "004_insert_materials.sql" ]; then
    echo "🪵 Inserting materials..."
    psql -h \$DB_HOST -p \$DB_PORT -U \$DB_USER -d \$DB_NAME -f 004_insert_materials.sql
fi

if [ -f "005_insert_hardware.sql" ]; then
    echo "🔧 Inserting hardware..."
    psql -h \$DB_HOST -p \$DB_PORT -U \$DB_USER -d \$DB_NAME -f 005_insert_hardware.sql
fi

if [ -f "006_insert_labor_rates.sql" ]; then
    echo "👷 Inserting labor rates..."
    psql -h \$DB_HOST -p \$DB_PORT -U \$DB_USER -d \$DB_NAME -f 006_insert_labor_rates.sql
fi

# Verify migration
echo "✅ Verifying migration..."
psql -h \$DB_HOST -p \$DB_PORT -U \$DB_USER -d \$DB_NAME -c "
SELECT
    'materials' as table_name, COUNT(*) as record_count FROM materials
UNION ALL
SELECT
    'hardware' as table_name, COUNT(*) as record_count FROM hardware
UNION ALL
SELECT
    'labor_rates' as table_name, COUNT(*) as record_count FROM labor_rates
UNION ALL
SELECT
    'regions' as table_name, COUNT(*) as record_count FROM regions
UNION ALL
SELECT
    'suppliers' as table_name, COUNT(*) as record_count FROM suppliers;
"

echo "🎉 Migration completed successfully!"
echo "📊 Database ready for quotation system integration"
`;

    fs.writeFileSync(path.join(outputDir, 'run_migration.sh'), migrationScript);

    // Make the script executable
    try {
      fs.chmodSync(path.join(outputDir, 'run_migration.sh'), '755');
    } catch (error) {
      console.log('⚠️ Could not make migration script executable:', error.message);
    }
  }
}

// Main execution
async function main() {
  try {
    const excelPath = path.join(__dirname, '../DB_SOURCE/LM3.20.xlsm');
    const extractor = new PricingDataExtractor(excelPath);

    const extractedData = await extractor.extractData();

    console.log('\n🎉 EXTRACTION COMPLETE!');
    console.log(`📊 Materials: ${extractedData.materials.length} records`);
    console.log(`🔧 Hardware: ${extractedData.hardware.length} records`);
    console.log(`👷 Labor Rates: ${extractedData.laborRates.length} records`);
    console.log(`🌍 Regions: ${extractedData.regions.length} records`);
    console.log(`🏢 Suppliers: ${extractedData.suppliers.length} records`);

  } catch (error) {
    console.error('❌ Extraction failed:', error.message);
    process.exit(1);
  }
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default PricingDataExtractor;
