#!/bin/bash

# Development startup script for Blackveil Design Mind
# Runs database, backend, and frontend services in development mode

echo "🚀 Starting Blackveil Design Mind Development Environment"
echo "=================================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose is not available. Please install Docker Compose and try again."
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Please run this script from the project root directory"
    exit 1
fi

# Create environment files if they don't exist
if [ ! -f ".env.local" ]; then
    echo "📝 Creating .env.local from .env.example"
    cp .env.example .env.local
fi

if [ ! -f "server/.env" ]; then
    echo "📝 Creating server/.env from server/.env.example"
    cp server/.env.example server/.env
    echo "⚠️  Please configure your OpenAI API key in server/.env"
fi

# Install dependencies if needed
echo "📦 Checking dependencies..."

if [ ! -d "node_modules" ]; then
    echo "Installing frontend dependencies..."
    npm install
fi

if [ ! -d "server/node_modules" ]; then
    echo "Installing backend dependencies..."
    cd server && npm install && cd ..
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p server/uploads server/temp server/logs

# Start database services
echo "🗄️ Starting database services (PostgreSQL + Redis)..."
docker compose -f docker-compose.dev.yml up -d

# Wait for databases to be ready
echo "⏳ Waiting for databases to be ready..."
sleep 10

# Check if databases are healthy
echo "🔍 Checking database health..."
POSTGRES_HEALTH=$(docker compose -f docker-compose.dev.yml ps postgres | grep "healthy" || echo "")
REDIS_HEALTH=$(docker compose -f docker-compose.dev.yml ps redis | grep "healthy" || echo "")

if [[ -n "$POSTGRES_HEALTH" && -n "$REDIS_HEALTH" ]]; then
    echo "✅ Databases are healthy and ready!"
else
    echo "⚠️ Databases may still be starting up. Services will continue to start..."
fi

# Run database migrations
echo "🔄 Running database migrations..."
if [ -f "migrations/run_migration.sh" ]; then
    cd migrations && ./run_migration.sh && cd ..
    echo "✅ Database migrations completed!"
else
    echo "⚠️ No migration script found. Database may need manual setup."
fi

echo "✅ Setup complete!"
echo ""
echo "🔧 Starting services..."
echo "Frontend: http://localhost:8080"
echo "Backend API: http://localhost:3001"
echo "Backend Health: http://localhost:3001/api/health"
echo "PostgreSQL: localhost:5433 (blackveil_design_mind)"
echo "Redis: localhost:6379"
echo "pgAdmin: http://localhost:8081 (<EMAIL> / blackveil_admin_2024)"
echo ""
echo "Press Ctrl+C to stop all services"
echo ""

# Function to cleanup background processes
cleanup() {
    echo ""
    echo "🛑 Stopping services..."

    # Stop Node.js processes
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
    fi

    # Wait for Node.js processes to stop
    if [ ! -z "$BACKEND_PID" ]; then
        wait $BACKEND_PID 2>/dev/null
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        wait $FRONTEND_PID 2>/dev/null
    fi

    # Stop database services
    echo "🗄️ Stopping database services..."
    docker compose -f docker-compose.dev.yml down

    echo "✅ All services stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start backend
echo "🔧 Starting backend server..."
(cd server && npm run dev) &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 3

# Start frontend
echo "🔧 Starting frontend development server..."
npm run dev &
FRONTEND_PID=$!

# Wait for both processes
wait $BACKEND_PID $FRONTEND_PID
