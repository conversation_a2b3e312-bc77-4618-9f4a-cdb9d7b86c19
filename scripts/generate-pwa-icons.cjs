#!/usr/bin/env node

/**
 * Generate placeholder PWA icons for Cabinet Insight Pro
 * This script creates simple colored squares as placeholder icons
 */

const fs = require('fs');
const path = require('path');

// Icon sizes needed for PWA
const iconSizes = [
  { size: 16, name: 'favicon-16x16.png' },
  { size: 32, name: 'favicon-32x32.png' },
  { size: 72, name: 'icon-72x72.png' },
  { size: 96, name: 'icon-96x96.png' },
  { size: 128, name: 'icon-128x128.png' },
  { size: 144, name: 'icon-144x144.png' },
  { size: 152, name: 'icon-152x152.png' },
  { size: 180, name: 'apple-touch-icon.png' },
  { size: 192, name: 'icon-192x192.png' },
  { size: 384, name: 'icon-384x384.png' },
  { size: 512, name: 'icon-512x512.png' },
  { size: 72, name: 'badge-72x72.png' },
  { size: 96, name: 'shortcut-upload.png' },
  { size: 96, name: 'shortcut-performance.png' },
  { size: 96, name: 'shortcut-projects.png' },
  { size: 32, name: 'action-view.png' },
  { size: 32, name: 'action-dismiss.png' }
];

// Create SVG icon template
function createSVGIcon(size, text = 'CIP') {
  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#14b8a6;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="${size}" height="${size}" rx="${size * 0.15}" fill="url(#grad)"/>
  <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" 
        fill="white" font-family="Arial, sans-serif" font-weight="bold" 
        font-size="${size * 0.25}">${text}</text>
</svg>`;
}

// Create specialized icons
function createSpecializedIcon(size, type) {
  const icons = {
    badge: createSVGIcon(size, 'C'),
    upload: createSVGIcon(size, '↑'),
    performance: createSVGIcon(size, '📊'),
    projects: createSVGIcon(size, '📁'),
    view: createSVGIcon(size, '👁'),
    dismiss: createSVGIcon(size, '✕')
  };
  
  return icons[type] || createSVGIcon(size);
}

// Ensure icons directory exists
const iconsDir = path.join(__dirname, '..', 'public', 'icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Generate icons
console.log('Generating PWA icons for Cabinet Insight Pro...');

iconSizes.forEach(({ size, name }) => {
  let svgContent;
  
  if (name.includes('badge')) {
    svgContent = createSpecializedIcon(size, 'badge');
  } else if (name.includes('upload')) {
    svgContent = createSpecializedIcon(size, 'upload');
  } else if (name.includes('performance')) {
    svgContent = createSpecializedIcon(size, 'performance');
  } else if (name.includes('projects')) {
    svgContent = createSpecializedIcon(size, 'projects');
  } else if (name.includes('view')) {
    svgContent = createSpecializedIcon(size, 'view');
  } else if (name.includes('dismiss')) {
    svgContent = createSpecializedIcon(size, 'dismiss');
  } else {
    svgContent = createSVGIcon(size);
  }
  
  // Save as SVG (browsers can use SVG for icons)
  const svgPath = path.join(iconsDir, name.replace('.png', '.svg'));
  fs.writeFileSync(svgPath, svgContent);
  
  // Also create a simple HTML file that can be used as a placeholder PNG
  const htmlContent = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    body { margin: 0; padding: 0; }
    svg { display: block; }
  </style>
</head>
<body>
  ${svgContent}
</body>
</html>`;
  
  const htmlPath = path.join(iconsDir, name.replace('.png', '.html'));
  fs.writeFileSync(htmlPath, htmlContent);
  
  console.log(`Generated: ${name} (${size}x${size})`);
});

// Create a simple favicon.ico placeholder
const faviconContent = createSVGIcon(32);
fs.writeFileSync(path.join(iconsDir, 'favicon.svg'), faviconContent);

// Create screenshots directory and placeholder files
const screenshotsDir = path.join(__dirname, '..', 'public', 'screenshots');
if (!fs.existsSync(screenshotsDir)) {
  fs.mkdirSync(screenshotsDir, { recursive: true });
}

// Create placeholder screenshots
const desktopScreenshot = createSVGIcon(1280, 'Desktop View');
const mobileScreenshot = createSVGIcon(390, 'Mobile View');

fs.writeFileSync(path.join(screenshotsDir, 'desktop-home.svg'), desktopScreenshot);
fs.writeFileSync(path.join(screenshotsDir, 'mobile-analysis.svg'), mobileScreenshot);

console.log('\n✅ PWA icons generated successfully!');
console.log('📁 Icons location: public/icons/');
console.log('📱 Screenshots location: public/screenshots/');
console.log('\n💡 Note: These are placeholder SVG icons. For production, replace with proper PNG/ICO files.');
console.log('🎨 Recommended: Use a tool like Figma or Adobe Illustrator to create professional icons.');

module.exports = { createSVGIcon, createSpecializedIcon };
