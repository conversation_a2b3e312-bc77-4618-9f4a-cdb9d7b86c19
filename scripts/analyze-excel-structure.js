#!/usr/bin/env node

/**
 * Excel File Structure Analyzer for LM3.20.xlsm
 * Analyzes the pricing database Excel file to understand its structure
 */

import XLSX from 'xlsx';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class ExcelStructureAnalyzer {
  constructor(filePath) {
    this.filePath = filePath;
    this.workbook = null;
    this.analysis = {
      worksheets: [],
      totalSheets: 0,
      dataStructure: {},
      summary: {}
    };
  }

  async analyzeFile() {
    try {
      console.log('🔍 Analyzing Excel file structure...');
      console.log(`📁 File: ${this.filePath}`);
      
      // Check if file exists
      if (!fs.existsSync(this.filePath)) {
        throw new Error(`File not found: ${this.filePath}`);
      }

      // Read the Excel file
      this.workbook = XLSX.readFile(this.filePath);
      this.analysis.totalSheets = this.workbook.SheetNames.length;
      
      console.log(`📊 Found ${this.analysis.totalSheets} worksheets`);
      console.log('📋 Worksheet names:', this.workbook.SheetNames);

      // Analyze each worksheet
      for (const sheetName of this.workbook.SheetNames) {
        await this.analyzeWorksheet(sheetName);
      }

      // Generate summary
      this.generateSummary();
      
      // Save analysis results
      await this.saveAnalysis();
      
      console.log('✅ Analysis completed successfully!');
      return this.analysis;

    } catch (error) {
      console.error('❌ Error analyzing Excel file:', error.message);
      throw error;
    }
  }

  analyzeWorksheet(sheetName) {
    console.log(`\n🔍 Analyzing worksheet: ${sheetName}`);
    
    const worksheet = this.workbook.Sheets[sheetName];
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
    
    // Get worksheet data
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    const jsonData = XLSX.utils.sheet_to_json(worksheet);
    
    // Analyze structure
    const analysis = {
      name: sheetName,
      range: worksheet['!ref'],
      totalRows: range.e.r + 1,
      totalCols: range.e.c + 1,
      dataRows: data.length,
      recordCount: jsonData.length,
      headers: data[0] || [],
      sampleData: data.slice(0, 5),
      columns: this.analyzeColumns(jsonData),
      dataTypes: this.analyzeDataTypes(jsonData),
      category: this.categorizeWorksheet(sheetName)
    };

    this.analysis.worksheets.push(analysis);
    this.analysis.dataStructure[sheetName] = analysis;
    
    console.log(`  📏 Range: ${analysis.range}`);
    console.log(`  📊 Rows: ${analysis.totalRows}, Cols: ${analysis.totalCols}`);
    console.log(`  📋 Records: ${analysis.recordCount}`);
    console.log(`  🏷️ Category: ${analysis.category}`);
    console.log(`  📝 Headers: ${analysis.headers.slice(0, 5).join(', ')}${analysis.headers.length > 5 ? '...' : ''}`);
  }

  analyzeColumns(data) {
    if (data.length === 0) return [];
    
    const columns = Object.keys(data[0] || {});
    return columns.map(col => ({
      name: col,
      sampleValues: data.slice(0, 3).map(row => row[col]).filter(val => val !== undefined),
      uniqueValues: [...new Set(data.map(row => row[col]).filter(val => val !== undefined))].length,
      nullCount: data.filter(row => row[col] === undefined || row[col] === null || row[col] === '').length
    }));
  }

  analyzeDataTypes(data) {
    if (data.length === 0) return {};
    
    const types = {};
    const sample = data[0] || {};
    
    Object.keys(sample).forEach(key => {
      const values = data.map(row => row[key]).filter(val => val !== undefined && val !== null && val !== '');
      if (values.length === 0) {
        types[key] = 'empty';
        return;
      }
      
      const firstValue = values[0];
      if (typeof firstValue === 'number') {
        types[key] = 'number';
      } else if (typeof firstValue === 'string') {
        // Check if it's a price/currency
        if (/^\$?[\d,]+\.?\d*$/.test(firstValue.toString())) {
          types[key] = 'currency';
        } else if (/^\d{4}-\d{2}-\d{2}/.test(firstValue.toString())) {
          types[key] = 'date';
        } else {
          types[key] = 'text';
        }
      } else {
        types[key] = typeof firstValue;
      }
    });
    
    return types;
  }

  categorizeWorksheet(sheetName) {
    const name = sheetName.toLowerCase();
    
    if (name.includes('material') || name.includes('cabinet') || name.includes('door') || name.includes('panel')) {
      return 'materials';
    } else if (name.includes('hardware') || name.includes('hinge') || name.includes('handle') || name.includes('slide')) {
      return 'hardware';
    } else if (name.includes('labor') || name.includes('install') || name.includes('rate')) {
      return 'labor';
    } else if (name.includes('region') || name.includes('location') || name.includes('area')) {
      return 'regional';
    } else if (name.includes('supplier') || name.includes('vendor') || name.includes('manufacturer')) {
      return 'suppliers';
    } else if (name.includes('price') || name.includes('cost')) {
      return 'pricing';
    } else {
      return 'unknown';
    }
  }

  generateSummary() {
    const categories = {};
    let totalRecords = 0;
    
    this.analysis.worksheets.forEach(sheet => {
      if (!categories[sheet.category]) {
        categories[sheet.category] = {
          sheets: [],
          totalRecords: 0
        };
      }
      categories[sheet.category].sheets.push(sheet.name);
      categories[sheet.category].totalRecords += sheet.recordCount;
      totalRecords += sheet.recordCount;
    });
    
    this.analysis.summary = {
      totalRecords,
      categories,
      recommendedMapping: this.generateMappingRecommendations(categories)
    };
  }

  generateMappingRecommendations(categories) {
    const mapping = {};
    
    Object.keys(categories).forEach(category => {
      switch (category) {
        case 'materials':
          mapping[category] = {
            targetTable: 'materials',
            sheets: categories[category].sheets,
            priority: 'high'
          };
          break;
        case 'hardware':
          mapping[category] = {
            targetTable: 'hardware',
            sheets: categories[category].sheets,
            priority: 'high'
          };
          break;
        case 'labor':
          mapping[category] = {
            targetTable: 'labor_rates',
            sheets: categories[category].sheets,
            priority: 'medium'
          };
          break;
        case 'regional':
          mapping[category] = {
            targetTable: 'regions',
            sheets: categories[category].sheets,
            priority: 'medium'
          };
          break;
        case 'suppliers':
          mapping[category] = {
            targetTable: 'suppliers',
            sheets: categories[category].sheets,
            priority: 'low'
          };
          break;
        default:
          mapping[category] = {
            targetTable: 'unknown',
            sheets: categories[category].sheets,
            priority: 'review'
          };
      }
    });
    
    return mapping;
  }

  async saveAnalysis() {
    const outputPath = path.join(__dirname, '../analysis/excel-structure-analysis.json');
    const outputDir = path.dirname(outputPath);
    
    // Create analysis directory if it doesn't exist
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // Save detailed analysis
    fs.writeFileSync(outputPath, JSON.stringify(this.analysis, null, 2));
    
    // Save summary report
    const summaryPath = path.join(__dirname, '../analysis/excel-analysis-summary.md');
    const summaryReport = this.generateMarkdownReport();
    fs.writeFileSync(summaryPath, summaryReport);
    
    console.log(`📄 Analysis saved to: ${outputPath}`);
    console.log(`📋 Summary report saved to: ${summaryPath}`);
  }

  generateMarkdownReport() {
    const { summary, worksheets } = this.analysis;
    
    let report = `# Excel File Structure Analysis Report\n\n`;
    report += `**File**: LM3.20.xlsm\n`;
    report += `**Analysis Date**: ${new Date().toISOString()}\n`;
    report += `**Total Worksheets**: ${this.analysis.totalSheets}\n`;
    report += `**Total Records**: ${summary.totalRecords}\n\n`;
    
    report += `## Summary by Category\n\n`;
    Object.keys(summary.categories).forEach(category => {
      const cat = summary.categories[category];
      report += `### ${category.toUpperCase()}\n`;
      report += `- **Sheets**: ${cat.sheets.join(', ')}\n`;
      report += `- **Total Records**: ${cat.totalRecords}\n`;
      report += `- **Target Table**: ${summary.recommendedMapping[category]?.targetTable || 'unknown'}\n\n`;
    });
    
    report += `## Detailed Worksheet Analysis\n\n`;
    worksheets.forEach(sheet => {
      report += `### ${sheet.name}\n`;
      report += `- **Category**: ${sheet.category}\n`;
      report += `- **Range**: ${sheet.range}\n`;
      report += `- **Records**: ${sheet.recordCount}\n`;
      report += `- **Headers**: ${sheet.headers.join(', ')}\n\n`;
    });
    
    return report;
  }
}

// Main execution
async function main() {
  try {
    const excelPath = path.join(__dirname, '../DB_SOURCE/LM3.20.xlsm');
    const analyzer = new ExcelStructureAnalyzer(excelPath);
    
    const analysis = await analyzer.analyzeFile();
    
    console.log('\n📊 ANALYSIS SUMMARY:');
    console.log(`Total worksheets: ${analysis.totalSheets}`);
    console.log(`Total records: ${analysis.summary.totalRecords}`);
    console.log('\nCategories found:');
    Object.keys(analysis.summary.categories).forEach(category => {
      const cat = analysis.summary.categories[category];
      console.log(`  ${category}: ${cat.totalRecords} records in ${cat.sheets.length} sheet(s)`);
    });
    
  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
    process.exit(1);
  }
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default ExcelStructureAnalyzer;
