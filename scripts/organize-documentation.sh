#!/bin/bash

# Cabinet Insight Pro - Documentation Organization Script
# This script organizes documentation into proper structure

echo "📚 Organizing Cabinet Insight Pro documentation..."

# Create documentation structure
echo "📁 Creating documentation directory structure..."
mkdir -p docs/implementation
mkdir -p docs/analysis
mkdir -p docs/user-guides
mkdir -p docs/api
mkdir -p docs/architecture

# Move implementation summaries to docs/implementation/
echo "📦 Moving implementation summaries..."

if [ -f "PERFORMANCE_METRICS_DASHBOARD_IMPLEMENTATION_SUMMARY.md" ]; then
    mv PERFORMANCE_METRICS_DASHBOARD_IMPLEMENTATION_SUMMARY.md docs/implementation/
    echo "✅ Moved: PERFORMANCE_METRICS_DASHBOARD_IMPLEMENTATION_SUMMARY.md"
fi

if [ -f "PRICING_DATABASE_INTEGRATION_SUMMARY.md" ]; then
    mv PRICING_DATABASE_INTEGRATION_SUMMARY.md docs/implementation/
    echo "✅ Moved: PRICING_DATABASE_INTEGRATION_SUMMARY.md"
fi

if [ -f "PRICING_DATA_EXTRACTION_SUMMARY.md" ]; then
    mv PRICING_DATA_EXTRACTION_SUMMARY.md docs/implementation/
    echo "✅ Moved: PRICING_DATA_EXTRACTION_SUMMARY.md"
fi

if [ -f "SCALABILITY_IMPLEMENTATION_COMPLETE.md" ]; then
    mv SCALABILITY_IMPLEMENTATION_COMPLETE.md docs/implementation/
    echo "✅ Moved: SCALABILITY_IMPLEMENTATION_COMPLETE.md"
fi

if [ -f "SCALABILITY_IMPLEMENTATION_GUIDE.md" ]; then
    mv SCALABILITY_IMPLEMENTATION_GUIDE.md docs/implementation/
    echo "✅ Moved: SCALABILITY_IMPLEMENTATION_GUIDE.md"
fi

if [ -f "a1000_rebuild_prd.md" ]; then
    mv a1000_rebuild_prd.md docs/implementation/
    echo "✅ Moved: a1000_rebuild_prd.md"
fi

# Move analysis files to docs/analysis/
echo "📊 Moving analysis files..."

if [ -d "analysis" ]; then
    mv analysis/* docs/analysis/ 2>/dev/null || true
    rmdir analysis 2>/dev/null || true
    echo "✅ Moved: analysis/ contents to docs/analysis/"
fi

if [ -d "extracted-data" ]; then
    mv extracted-data docs/analysis/
    echo "✅ Moved: extracted-data/ to docs/analysis/"
fi

# Copy REFACTORING_ANALYSIS.md to docs/implementation/ (keep original for now)
if [ -f "REFACTORING_ANALYSIS.md" ]; then
    cp REFACTORING_ANALYSIS.md docs/implementation/
    echo "✅ Copied: REFACTORING_ANALYSIS.md to docs/implementation/ (original preserved)"
fi

# Archive SCALABILITY_ANALYSIS.md (already copied to archive)
if [ -f "SCALABILITY_ANALYSIS.md" ]; then
    mv SCALABILITY_ANALYSIS.md _archive/old-docs/
    echo "✅ Archived: SCALABILITY_ANALYSIS.md"
fi

# Create index files for better navigation
echo "📋 Creating documentation index files..."

# Create main docs index
cat > docs/README.md << 'EOF'
# Cabinet Insight Pro Documentation

## 📁 Documentation Structure

### 🏗️ [Implementation](./implementation/)
Implementation summaries, guides, and technical documentation for major features and refactoring initiatives.

### 📊 [Analysis](./analysis/)
Data analysis, performance metrics, and system analysis reports.

### 👥 [User Guides](./user-guides/)
End-user documentation and tutorials.

### 🔌 [API](./api/)
API documentation and endpoint references.

### 🏛️ [Architecture](./architecture/)
System architecture documentation and design decisions.

## 🗂️ Quick Links

- [Project Status](./project-status.md)
- [Technical Specifications](./technical-specifications-update.md)
- [Testing Guide](./testing.md)
- [Contributing Guide](./contributing.md)

## 📈 Recent Updates

- **Phase 1-3.4 Refactoring**: Comprehensive modular architecture implementation
- **Scalability Analysis**: 1000+ concurrent user scaling strategy
- **Performance Metrics**: Real-time monitoring dashboard
- **Pricing Database**: NZD pricing integration with PostgreSQL

---

**Last Updated**: 2024-01-XX  
**Project Status**: Production Ready with Modular Architecture
EOF

# Create implementation index
cat > docs/implementation/README.md << 'EOF'
# Implementation Documentation

## 📋 Implementation Summaries

- [Refactoring Analysis](./REFACTORING_ANALYSIS.md) - Comprehensive Phase 1-3.4 refactoring
- [Performance Metrics Dashboard](./PERFORMANCE_METRICS_DASHBOARD_IMPLEMENTATION_SUMMARY.md)
- [Pricing Database Integration](./PRICING_DATABASE_INTEGRATION_SUMMARY.md)
- [Pricing Data Extraction](./PRICING_DATA_EXTRACTION_SUMMARY.md)
- [Scalability Implementation](./SCALABILITY_IMPLEMENTATION_COMPLETE.md)
- [A1000 Rebuild PRD](./a1000_rebuild_prd.md)

## 🏗️ Architecture Changes

### Phase 1-3.4 Refactoring Results
- **Total Lines Refactored**: 5,121 lines → 565 lines (88.9% reduction)
- **Modular Services Created**: 24+ specialized handlers/services/helpers
- **Backward Compatibility**: 100% maintained
- **Test Success Rate**: ~97-99% maintained

### Key Achievements
- OpenAI Service: 1,352 → 142 lines (89.6% reduction)
- Layout Optimization: 1,089 → 329 lines (70% reduction)
- Cabinet Reconstruction: 630 → 29 lines (95.4% reduction)
- PDF Service: 554 → 25 lines (95.5% reduction)
- Analysis Routes: 545 → 16 lines (97.1% reduction)
- Test Infrastructure: 951 → 24 lines (97.5% reduction)
EOF

# Create analysis index
cat > docs/analysis/README.md << 'EOF'
# Analysis Documentation

## 📊 Data Analysis

- [Excel Structure Analysis](./excel-structure-analysis.json)
- [Extracted Data](./extracted-data/) - Pricing data from Excel sources

## 📈 Performance Analysis

- System performance metrics and analysis reports
- Scalability testing results
- Load testing documentation

## 🔍 Code Analysis

- Refactoring impact analysis
- Code quality metrics
- Technical debt assessment
EOF

echo ""
echo "🎉 Documentation organization completed!"
echo ""
echo "📋 New Structure:"
echo "  📚 docs/"
echo "    ├── 🏗️ implementation/ - Implementation summaries and guides"
echo "    ├── 📊 analysis/ - Data analysis and reports"
echo "    ├── 👥 user-guides/ - User documentation"
echo "    ├── 🔌 api/ - API documentation"
echo "    └── 🏛️ architecture/ - Architecture documentation"
echo ""
echo "✅ Documentation is now properly organized!"
echo "📖 See docs/README.md for navigation guide."
