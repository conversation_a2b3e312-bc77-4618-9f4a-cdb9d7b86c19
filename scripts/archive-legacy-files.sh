#!/bin/bash

# Cabinet Insight Pro - Archive Legacy Files Script
# This script moves legacy files to the organized archive structure

echo "🗂️ Starting Cabinet Insight Pro Legacy File Archival..."

# Create archive directory structure
echo "📁 Creating archive directory structure..."
mkdir -p _archive/legacy-services
mkdir -p _archive/old-docs
mkdir -p _archive/refactoring-artifacts
mkdir -p _archive/working-files
mkdir -p _archive/analysis-data

# Archive Legacy Service Files
echo "📦 Archiving legacy service files..."

# OpenAI Service (Phase 1)
if [ -f "server/src/services/openaiService.ts" ]; then
    mv server/src/services/openaiService.ts _archive/legacy-services/openaiService-pre-modular.ts
    echo "✅ Archived: openaiService.ts → openaiService-pre-modular.ts"
fi

# Layout Optimization Service (Phase 2) - Clean artifact
if [ -f "server/src/services/layoutOptimizationService_clean.ts" ]; then
    mv server/src/services/layoutOptimizationService_clean.ts _archive/refactoring-artifacts/layoutOptimizationService-clean-artifact.ts
    echo "✅ Archived: layoutOptimizationService_clean.ts → layoutOptimizationService-clean-artifact.ts"
fi

# Cabinet Reconstruction Service (Phase 3.1)
if [ -f "server/src/services/cabinetReconstructionService.ts" ]; then
    mv server/src/services/cabinetReconstructionService.ts _archive/legacy-services/cabinetReconstructionService-pre-modular.ts
    echo "✅ Archived: cabinetReconstructionService.ts → cabinetReconstructionService-pre-modular.ts"
fi

# PDF Service (Phase 3.2)
if [ -f "server/src/services/pdfService.ts" ]; then
    mv server/src/services/pdfService.ts _archive/legacy-services/pdfService-pre-modular.ts
    echo "✅ Archived: pdfService.ts → pdfService-pre-modular.ts"
fi

# OpenAI Service Clean Artifact
if [ -f "server/src/services/openaiService_clean.ts" ]; then
    mv server/src/services/openaiService_clean.ts _archive/refactoring-artifacts/openaiService-clean-artifact.ts
    echo "✅ Archived: openaiService_clean.ts → openaiService-clean-artifact.ts"
fi

# Test Helpers Clean Artifact
if [ -f "tests/utils/test-helpers-clean.ts" ]; then
    mv tests/utils/test-helpers-clean.ts _archive/refactoring-artifacts/test-helpers-clean-artifact.ts
    echo "✅ Archived: test-helpers-clean.ts → test-helpers-clean-artifact.ts"
fi

# Archive Documentation Files
echo "📚 Archiving documentation files..."

# Implementation summaries
if [ -f "REFACTORING_ANALYSIS.md" ]; then
    cp REFACTORING_ANALYSIS.md _archive/old-docs/REFACTORING_ANALYSIS.md
    echo "✅ Copied: REFACTORING_ANALYSIS.md to archive (keeping original for now)"
fi

if [ -f "SCALABILITY_ANALYSIS.md" ]; then
    mv SCALABILITY_ANALYSIS.md _archive/old-docs/SCALABILITY_ANALYSIS.md
    echo "✅ Archived: SCALABILITY_ANALYSIS.md"
fi

if [ -f "SCALABILITY_IMPLEMENTATION_COMPLETE.md" ]; then
    mv SCALABILITY_IMPLEMENTATION_COMPLETE.md _archive/old-docs/SCALABILITY_IMPLEMENTATION_COMPLETE.md
    echo "✅ Archived: SCALABILITY_IMPLEMENTATION_COMPLETE.md"
fi

if [ -f "SCALABILITY_IMPLEMENTATION_GUIDE.md" ]; then
    mv SCALABILITY_IMPLEMENTATION_GUIDE.md _archive/old-docs/SCALABILITY_IMPLEMENTATION_GUIDE.md
    echo "✅ Archived: SCALABILITY_IMPLEMENTATION_GUIDE.md"
fi

if [ -f "PERFORMANCE_METRICS_DASHBOARD_IMPLEMENTATION_SUMMARY.md" ]; then
    mv PERFORMANCE_METRICS_DASHBOARD_IMPLEMENTATION_SUMMARY.md _archive/old-docs/PERFORMANCE_METRICS_DASHBOARD_IMPLEMENTATION_SUMMARY.md
    echo "✅ Archived: PERFORMANCE_METRICS_DASHBOARD_IMPLEMENTATION_SUMMARY.md"
fi

if [ -f "PRICING_DATABASE_INTEGRATION_SUMMARY.md" ]; then
    mv PRICING_DATABASE_INTEGRATION_SUMMARY.md _archive/old-docs/PRICING_DATABASE_INTEGRATION_SUMMARY.md
    echo "✅ Archived: PRICING_DATABASE_INTEGRATION_SUMMARY.md"
fi

if [ -f "PRICING_DATA_EXTRACTION_SUMMARY.md" ]; then
    mv PRICING_DATA_EXTRACTION_SUMMARY.md _archive/old-docs/PRICING_DATA_EXTRACTION_SUMMARY.md
    echo "✅ Archived: PRICING_DATA_EXTRACTION_SUMMARY.md"
fi

if [ -f "a1000_rebuild_prd.md" ]; then
    mv a1000_rebuild_prd.md _archive/old-docs/a1000_rebuild_prd.md
    echo "✅ Archived: a1000_rebuild_prd.md"
fi

# Archive Working Files and Data
echo "📊 Archiving working files and data..."

# Move PDFs to test fixtures and archive originals
if [ -d "PDFs" ]; then
    echo "📁 Moving PDFs to test fixtures and archiving..."
    mkdir -p tests/fixtures
    cp PDFs/* tests/fixtures/ 2>/dev/null || true
    mv PDFs _archive/working-files/PDFs-original
    echo "✅ Archived: PDFs/ → working-files/PDFs-original (copied to tests/fixtures/)"
fi

# Archive analysis data
if [ -d "analysis" ]; then
    mv analysis _archive/analysis-data/analysis-reports
    echo "✅ Archived: analysis/ → analysis-data/analysis-reports"
fi

if [ -d "extracted-data" ]; then
    mv extracted-data _archive/analysis-data/extracted-data
    echo "✅ Archived: extracted-data/ → analysis-data/extracted-data"
fi

# Archive temporary files (keep directories for runtime)
echo "🧹 Cleaning temporary directories..."

if [ -d "temp" ] && [ "$(ls -A temp)" ]; then
    mv temp _archive/working-files/temp-backup
    mkdir temp
    echo "✅ Archived: temp/ contents → working-files/temp-backup (recreated empty temp/)"
fi

echo ""
echo "🎉 Archive process completed!"
echo ""
echo "📋 Summary:"
echo "  📦 Legacy services archived to: _archive/legacy-services/"
echo "  🔧 Refactoring artifacts archived to: _archive/refactoring-artifacts/"
echo "  📚 Documentation archived to: _archive/old-docs/"
echo "  📊 Data files archived to: _archive/analysis-data/"
echo "  📁 Working files archived to: _archive/working-files/"
echo ""
echo "✅ Project structure is now clean while preserving all historical files!"
echo "📖 See _archive/README.md for detailed information about archived files."
