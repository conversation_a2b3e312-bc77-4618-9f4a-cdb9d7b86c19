#!/usr/bin/env node

/**
 * A.ONE Class Migration Script
 * Systematically replaces generic Tailwind classes with A.ONE design system classes
 * for improved theme consistency and design system adoption
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// A.ONE Class Migration Map - Generic to A.ONE Design System
const AONE_CLASS_MIGRATIONS = {
  // Button Patterns
  'bg-white border rounded-lg shadow-md': 'aone-card-enterprise',
  'bg-blue-500 hover:bg-blue-600': 'aone-button-primary',
  'bg-blue-600 hover:bg-blue-700': 'aone-button-primary',
  'bg-white hover:bg-gray-50 text-blue-600 border': 'aone-button-secondary',
  'bg-white hover:bg-gray-100': 'aone-button-secondary',
  'text-blue-600 hover:text-blue-500': 'aone-button-ghost text-aone-sage hover:text-aone-sage-dark',
  
  // Card Patterns
  'bg-white rounded-lg shadow-sm border': 'aone-card',
  'bg-white rounded-lg shadow-md': 'aone-card-interactive',
  'bg-white rounded-xl shadow-lg': 'aone-card-enterprise',
  'bg-card rounded-lg shadow-sm': 'aone-card',
  
  // Form Patterns
  'w-full px-3 py-2 border border-gray-300 rounded-md': 'aone-input w-full',
  'w-full px-4 py-3 border rounded-lg': 'aone-input-enterprise w-full',
  'block text-sm font-medium text-gray-700': 'aone-label',
  'text-sm font-medium text-gray-700': 'aone-label-enterprise',
  
  // Navigation Patterns
  'text-gray-600 hover:text-gray-800': 'aone-nav-link',
  'text-gray-700 hover:text-gray-900': 'aone-nav-link',
  'text-muted-foreground hover:text-foreground': 'aone-nav-link',
  
  // Layout Patterns
  'container mx-auto px-4': 'aone-container',
  'py-16 lg:py-24': 'aone-section',
  'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6': 'aone-grid-responsive',
  
  // Typography Patterns
  'text-2xl font-bold': 'text-aone-2xl font-aone-bold',
  'text-3xl font-bold': 'text-aone-3xl font-aone-bold',
  'text-xl': 'text-aone-xl',
  'text-lg': 'text-aone-lg',
  'text-sm': 'text-aone-sm',
  'font-medium': 'font-aone-medium',
  'font-semibold': 'font-aone-semibold',
  
  // Status Patterns
  'bg-red-50 border border-red-200 text-red-700': 'aone-status-error',
  'bg-green-50 border border-green-200 text-green-700': 'aone-status-success',
  'bg-yellow-50 border border-yellow-200 text-yellow-700': 'aone-status-warning',
  'bg-blue-50 border border-blue-200 text-blue-700': 'aone-status-info',
  
  // Animation Patterns
  'transition-all duration-200': 'aone-micro-interaction',
  'transition-colors duration-200': 'aone-micro-interaction',
  'hover:shadow-lg': 'aone-hover-lift',
  'hover:scale-105': 'aone-hover-lift',
  
  // Focus Patterns
  'focus:outline-none focus:ring-2': 'aone-focus-ring',
  'focus:ring-blue-500': 'aone-focus-ring',
  'focus:ring-2 focus:ring-blue-500': 'aone-focus-ring'
};

// Individual class replacements for more granular control
const INDIVIDUAL_CLASS_REPLACEMENTS = {
  // Colors
  'text-gray-600': 'text-muted-foreground',
  'text-gray-700': 'text-foreground',
  'text-gray-800': 'text-foreground',
  'text-gray-900': 'text-foreground',
  'text-blue-600': 'text-aone-sage',
  'text-blue-700': 'text-aone-sage-dark',
  'bg-gray-50': 'bg-muted',
  'bg-gray-100': 'bg-muted',
  'border-gray-300': 'border-border',
  'border-gray-200': 'border-border',

  // Typography patterns - high impact
  'text-2xl font-bold': 'aone-heading-secondary',
  'text-3xl font-bold': 'aone-heading-primary',
  'text-xl font-semibold': 'aone-heading-tertiary',
  'text-lg': 'aone-body-primary',
  'text-sm text-gray-600': 'aone-body-secondary',
  'text-xs font-medium': 'aone-caption',

  // Layout patterns - high impact
  'flex items-center justify-center': 'aone-flex-center',
  'flex items-center justify-between': 'aone-flex-between',
  'flex items-center justify-start': 'aone-flex-start',
  'flex items-center justify-end': 'aone-flex-end',
  'flex flex-col items-center justify-center': 'aone-flex-col-center',

  // Spacing using A.ONE design tokens
  'p-6': 'aone-spacing-lg',
  'p-8': 'aone-spacing-xl',
  'p-4': 'aone-spacing-md',
  'p-3': 'aone-spacing-sm',
  'p-2': 'aone-spacing-xs',
  'px-4': 'px-aone-md',
  'py-3': 'py-aone-sm',
  'mb-4': 'mb-aone-md',
  'mb-6': 'mb-aone-lg',
  'gap-4': 'gap-aone-md',
  'gap-6': 'gap-aone-lg',

  // Border patterns - high impact
  'border border-gray-300': 'aone-border-medium',
  'border border-gray-200': 'aone-border-light',
  'border-2 border-blue-500': 'aone-border-accent',

  // Border radius using A.ONE design tokens
  'rounded-md': 'rounded-aone-md',
  'rounded-lg': 'rounded-aone-lg',
  'rounded-xl': 'rounded-aone-xl'
};

class AOneClassMigrator {
  constructor() {
    this.results = {
      filesProcessed: 0,
      replacementsMade: 0,
      migrationDetails: []
    };
  }

  async migrateProject() {
    console.log('🎨 Starting A.ONE Class Migration for Blackveil Design Mind\n');
    
    const srcDir = path.join(process.cwd(), 'src');
    await this.processDirectory(srcDir);
    
    this.generateReport();
  }

  async processDirectory(dirPath) {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      
      if (entry.isDirectory()) {
        await this.processDirectory(fullPath);
      } else if (this.shouldProcessFile(entry.name)) {
        await this.processFile(fullPath);
      }
    }
  }

  shouldProcessFile(filename) {
    return filename.endsWith('.tsx') || filename.endsWith('.ts') || filename.endsWith('.jsx') || filename.endsWith('.js');
  }

  async processFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      const originalContent = content;
      let replacements = 0;

      // Apply pattern-based migrations first (more specific)
      for (const [pattern, replacement] of Object.entries(AONE_CLASS_MIGRATIONS)) {
        const regex = new RegExp(pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
        const matches = content.match(regex);
        if (matches) {
          content = content.replace(regex, replacement);
          replacements += matches.length;
        }
      }

      // Apply individual class replacements
      for (const [oldClass, newClass] of Object.entries(INDIVIDUAL_CLASS_REPLACEMENTS)) {
        // Use word boundaries to avoid partial matches
        const regex = new RegExp(`\\b${oldClass.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g');
        const matches = content.match(regex);
        if (matches) {
          content = content.replace(regex, newClass);
          replacements += matches.length;
        }
      }

      // Add aone-micro-interaction to interactive elements missing it
      content = this.addMicroInteractions(content);

      if (content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        this.results.filesProcessed++;
        this.results.replacementsMade += replacements;
        
        this.results.migrationDetails.push({
          file: path.relative(process.cwd(), filePath),
          replacements
        });
        
        console.log(`✅ Migrated ${replacements} classes in ${path.relative(process.cwd(), filePath)}`);
      }
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  addMicroInteractions(content) {
    // Add aone-micro-interaction to buttons, links, and interactive elements that don't have it
    const interactivePatterns = [
      /(<button[^>]*className="[^"]*)(hover:[^"]*)/g,
      /(<Link[^>]*className="[^"]*)(hover:[^"]*)/g,
      /(<a[^>]*className="[^"]*)(hover:[^"]*)/g
    ];

    for (const pattern of interactivePatterns) {
      content = content.replace(pattern, (match, prefix, hoverClass) => {
        if (!match.includes('aone-micro-interaction')) {
          return match.replace(hoverClass, `aone-micro-interaction ${hoverClass}`);
        }
        return match;
      });
    }

    return content;
  }

  generateReport() {
    console.log('\n📊 A.ONE CLASS MIGRATION REPORT');
    console.log('================================================================================');
    console.log(`Files Processed: ${this.results.filesProcessed}`);
    console.log(`Total Replacements: ${this.results.replacementsMade}`);
    console.log(`Average Replacements per File: ${(this.results.replacementsMade / Math.max(this.results.filesProcessed, 1)).toFixed(1)}`);
    
    if (this.results.migrationDetails.length > 0) {
      console.log('\n📋 Detailed Migration Results:');
      this.results.migrationDetails
        .sort((a, b) => b.replacements - a.replacements)
        .slice(0, 10)
        .forEach(detail => {
          console.log(`   • ${detail.file}: ${detail.replacements} replacements`);
        });
    }
    
    console.log('\n✅ A.ONE Class Migration Complete!');
    console.log('================================================================================');
    
    // Save detailed report
    const reportPath = path.join(process.cwd(), 'aone-migration-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    console.log(`📄 Detailed report saved to: ${reportPath}`);
  }
}

// Run migration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const migrator = new AOneClassMigrator();
  migrator.migrateProject().catch(console.error);
}

export default AOneClassMigrator;
