#!/usr/bin/env node

/**
 * Comprehensive Theme Consistency Audit for Blackveil Design Mind
 * 
 * Performs a thorough audit of theme consistency, A.ONE design system compliance,
 * CSS best practices, and ShadCN/UI integration assessment.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Audit configuration
const AUDIT_CONFIG = {
  sageGreenPrimary: '#6B7A4F',
  sageGreenRgb: '107 122 79',
  expectedThemeVariables: [
    '--aone-sage',
    '--aone-sage-light', 
    '--aone-sage-dark',
    '--aone-cream',
    '--aone-warm-white',
    '--aone-charcoal',
    '--aone-soft-gray'
  ],
  expectedTailwindClasses: [
    'aone-sage',
    'aone-sage-light',
    'aone-sage-dark',
    'aone-cream',
    'aone-warm-white',
    'aone-charcoal',
    'aone-soft-gray'
  ],
  hardcodedColorPatterns: [
    /#[0-9a-fA-F]{3,6}/g,  // Hex colors
    /rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)/g,  // RGB colors
    /rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)/g,  // RGBA colors
    /hsl\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*\)/g,  // HSL colors
  ],
  excludePatterns: [
    'node_modules',
    '.git',
    'dist',
    'build',
    '.next',
    'coverage',
    'playwright-report',
    'test-results'
  ]
};

class ThemeConsistencyAuditor {
  constructor() {
    this.results = {
      themeConsistency: {
        score: 0,
        issues: [],
        recommendations: []
      },
      shadcnIntegration: {
        score: 0,
        issues: [],
        recommendations: []
      },
      cssArchitecture: {
        score: 0,
        issues: [],
        recommendations: []
      },
      aoneCompliance: {
        score: 0,
        issues: [],
        recommendations: []
      },
      accessibility: {
        score: 0,
        issues: [],
        recommendations: []
      },
      overall: {
        score: 0,
        summary: '',
        criticalIssues: [],
        prioritizedRecommendations: []
      }
    };
    
    this.fileAnalysis = {
      totalFiles: 0,
      analyzedFiles: 0,
      cssFiles: [],
      tsxFiles: [],
      configFiles: []
    };
  }

  async runAudit() {
    console.log('🎨 Starting Comprehensive Theme Consistency Audit for Blackveil Design Mind\n');
    
    try {
      // 1. Theme Consistency Validation
      await this.auditThemeConsistency();
      
      // 2. ShadCN/UI Integration Assessment
      await this.auditShadcnIntegration();
      
      // 3. CSS Architecture Review
      await this.auditCssArchitecture();
      
      // 4. A.ONE Design System Compliance
      await this.auditAoneCompliance();
      
      // 5. Accessibility Compliance
      await this.auditAccessibility();
      
      // 6. Calculate overall score and generate report
      this.calculateOverallScore();
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Audit failed:', error.message);
      process.exit(1);
    }
  }

  async auditThemeConsistency() {
    console.log('📊 1. Theme Consistency Validation...');
    
    const issues = [];
    const recommendations = [];
    let score = 100;

    // Check CSS custom properties in index.css
    const indexCssPath = path.join(process.cwd(), 'src/index.css');
    if (fs.existsSync(indexCssPath)) {
      const cssContent = fs.readFileSync(indexCssPath, 'utf8');
      
      // Verify sage green color consistency
      const sageMatches = cssContent.match(/--aone-sage:\s*([^;]+)/);
      if (sageMatches) {
        const sageValue = sageMatches[1].trim();
        if (sageValue !== AUDIT_CONFIG.sageGreenRgb) {
          issues.push({
            type: 'COLOR_INCONSISTENCY',
            severity: 'HIGH',
            file: 'src/index.css',
            message: `Sage green color mismatch. Expected: ${AUDIT_CONFIG.sageGreenRgb}, Found: ${sageValue}`,
            line: this.findLineNumber(cssContent, sageMatches[0])
          });
          score -= 15;
        }
      } else {
        issues.push({
          type: 'MISSING_VARIABLE',
          severity: 'CRITICAL',
          file: 'src/index.css',
          message: 'Primary sage green CSS variable (--aone-sage) not found'
        });
        score -= 25;
      }

      // Check for all expected theme variables
      for (const variable of AUDIT_CONFIG.expectedThemeVariables) {
        if (!cssContent.includes(variable)) {
          issues.push({
            type: 'MISSING_VARIABLE',
            severity: 'MEDIUM',
            file: 'src/index.css',
            message: `Missing theme variable: ${variable}`
          });
          score -= 5;
        }
      }
    } else {
      issues.push({
        type: 'MISSING_FILE',
        severity: 'CRITICAL',
        file: 'src/index.css',
        message: 'Main CSS file not found'
      });
      score -= 30;
    }

    // Check Tailwind configuration
    const tailwindConfigPath = path.join(process.cwd(), 'tailwind.config.ts');
    if (fs.existsSync(tailwindConfigPath)) {
      const tailwindContent = fs.readFileSync(tailwindConfigPath, 'utf8');
      
      // Verify A.ONE color extensions
      for (const className of AUDIT_CONFIG.expectedTailwindClasses) {
        if (!tailwindContent.includes(className)) {
          issues.push({
            type: 'MISSING_TAILWIND_CLASS',
            severity: 'MEDIUM',
            file: 'tailwind.config.ts',
            message: `Missing Tailwind class: ${className}`
          });
          score -= 3;
        }
      }
    }

    // Scan for hardcoded colors in components
    await this.scanForHardcodedColors(issues);

    this.results.themeConsistency = {
      score: Math.max(0, score),
      issues,
      recommendations: this.generateThemeRecommendations(issues)
    };

    console.log(`   ✓ Theme consistency score: ${this.results.themeConsistency.score}/100`);
    console.log(`   ✓ Found ${issues.length} theme consistency issues\n`);
  }

  async scanForHardcodedColors(issues) {
    const componentDir = path.join(process.cwd(), 'src/components');
    if (!fs.existsSync(componentDir)) return;

    const scanDirectory = (dir) => {
      const files = fs.readdirSync(dir);
      
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          scanDirectory(filePath);
        } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
          this.scanFileForHardcodedColors(filePath, issues);
        }
      }
    };

    scanDirectory(componentDir);
  }

  scanFileForHardcodedColors(filePath, issues) {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(process.cwd(), filePath);
    
    for (const pattern of AUDIT_CONFIG.hardcodedColorPatterns) {
      const matches = content.match(pattern);
      if (matches) {
        for (const match of matches) {
          // Skip if it's the expected sage green color
          if (match.includes('6B7A4F') || match.includes('107, 122, 79')) continue;
          
          // Skip common acceptable colors (white, black, transparent)
          if (match.includes('#fff') || match.includes('#000') || 
              match.includes('255, 255, 255') || match.includes('0, 0, 0') ||
              match.includes('transparent')) continue;

          issues.push({
            type: 'HARDCODED_COLOR',
            severity: 'MEDIUM',
            file: relativePath,
            message: `Hardcoded color found: ${match}`,
            line: this.findLineNumber(content, match),
            suggestion: 'Consider using theme variables or Tailwind classes'
          });
        }
      }
    }
  }

  async auditShadcnIntegration() {
    console.log('🔧 2. ShadCN/UI Integration Assessment...');
    
    const issues = [];
    let score = 100;

    // Check components.json configuration
    const componentsConfigPath = path.join(process.cwd(), 'components.json');
    if (fs.existsSync(componentsConfigPath)) {
      const config = JSON.parse(fs.readFileSync(componentsConfigPath, 'utf8'));
      
      // Verify proper configuration
      if (!config.style || config.style !== 'default') {
        issues.push({
          type: 'CONFIG_ISSUE',
          severity: 'LOW',
          file: 'components.json',
          message: 'ShadCN style configuration should be "default"'
        });
        score -= 5;
      }

      if (!config.tailwind || !config.tailwind.css) {
        issues.push({
          type: 'CONFIG_ISSUE',
          severity: 'MEDIUM',
          file: 'components.json',
          message: 'Missing Tailwind CSS configuration in components.json'
        });
        score -= 10;
      }
    } else {
      issues.push({
        type: 'MISSING_FILE',
        severity: 'HIGH',
        file: 'components.json',
        message: 'ShadCN components configuration file not found'
      });
      score -= 20;
    }

    // Check UI components directory
    const uiComponentsDir = path.join(process.cwd(), 'src/components/ui');
    if (fs.existsSync(uiComponentsDir)) {
      const uiFiles = fs.readdirSync(uiComponentsDir);
      
      // Check for proper ShadCN component structure
      for (const file of uiFiles) {
        if (file.endsWith('.tsx')) {
          const filePath = path.join(uiComponentsDir, file);
          const content = fs.readFileSync(filePath, 'utf8');
          
          // Check for proper imports
          if (!content.includes('@radix-ui') && !content.includes('class-variance-authority')) {
            // Some components might not need Radix, so this is just a warning
          }
          
          // Check for proper theme integration
          if (!content.includes('cn(') && content.includes('className')) {
            issues.push({
              type: 'MISSING_CN_UTILITY',
              severity: 'LOW',
              file: `src/components/ui/${file}`,
              message: 'Component should use cn() utility for className merging'
            });
            score -= 2;
          }
        }
      }
    }

    this.results.shadcnIntegration = {
      score: Math.max(0, score),
      issues,
      recommendations: this.generateShadcnRecommendations(issues)
    };

    console.log(`   ✓ ShadCN integration score: ${this.results.shadcnIntegration.score}/100`);
    console.log(`   ✓ Found ${issues.length} ShadCN integration issues\n`);
  }

  async auditCssArchitecture() {
    console.log('🏗️ 3. CSS Architecture Review...');
    
    const issues = [];
    let score = 100;

    // Check for CSS organization
    const cssFiles = this.findCssFiles();
    
    // Check for duplicate CSS rules
    const duplicateRules = this.findDuplicateCssRules(cssFiles);
    if (duplicateRules.length > 0) {
      issues.push({
        type: 'DUPLICATE_CSS',
        severity: 'MEDIUM',
        message: `Found ${duplicateRules.length} duplicate CSS rules`,
        details: duplicateRules.slice(0, 5) // Show first 5
      });
      score -= duplicateRules.length * 2;
    }

    // Check for unused CSS (simplified check)
    const unusedSelectors = this.findUnusedCssSelectors();
    if (unusedSelectors.length > 0) {
      issues.push({
        type: 'UNUSED_CSS',
        severity: 'LOW',
        message: `Found ${unusedSelectors.length} potentially unused CSS selectors`,
        details: unusedSelectors.slice(0, 10)
      });
      score -= unusedSelectors.length;
    }

    this.results.cssArchitecture = {
      score: Math.max(0, score),
      issues,
      recommendations: this.generateCssRecommendations(issues)
    };

    console.log(`   ✓ CSS architecture score: ${this.results.cssArchitecture.score}/100`);
    console.log(`   ✓ Found ${issues.length} CSS architecture issues\n`);
  }

  async auditAoneCompliance() {
    console.log('🎯 4. A.ONE Design System Compliance...');
    
    const issues = [];
    let score = 100;

    // Check for A.ONE class usage - comprehensive list
    const aoneClasses = [
      // Core A.ONE Components
      'aone-header', 'aone-hero-section', 'aone-banner', 'aone-logo', 'aone-logo-accent',

      // Button System
      'aone-button-primary', 'aone-button-secondary', 'aone-button-ghost', 'aone-button-enterprise',

      // Card System
      'aone-card', 'aone-card-interactive', 'aone-card-enterprise', 'aone-card-glass',

      // Form System
      'aone-input', 'aone-input-enterprise', 'aone-label', 'aone-label-enterprise',

      // Navigation System
      'aone-nav-link', 'aone-nav-link-active', 'aone-nav-enterprise',

      // Layout System
      'aone-container', 'aone-section', 'aone-grid-responsive',

      // Typography System
      'aone-heading-primary', 'aone-heading-secondary', 'aone-heading-tertiary',
      'aone-body-primary', 'aone-body-secondary', 'aone-caption',

      // Utility System
      'aone-flex-center', 'aone-flex-between', 'aone-flex-start', 'aone-flex-end', 'aone-flex-col-center',
      'aone-spacing-xs', 'aone-spacing-sm', 'aone-spacing-md', 'aone-spacing-lg', 'aone-spacing-xl',
      'aone-margin-xs', 'aone-margin-sm', 'aone-margin-md', 'aone-margin-lg', 'aone-margin-xl',
      'aone-border-light', 'aone-border-medium', 'aone-border-strong', 'aone-border-accent',

      // Interaction System
      'aone-micro-interaction', 'aone-hover-lift', 'aone-hover-glow', 'aone-focus-ring', 'aone-pulse-sage',

      // Status System
      'aone-status-success', 'aone-status-warning', 'aone-status-error', 'aone-status-info'
    ];

    const componentFiles = this.findComponentFiles();
    let aoneClassUsage = 0;
    let totalClassUsage = 0;

    for (const file of componentFiles) {
      const content = fs.readFileSync(file, 'utf8');
      
      // Count A.ONE class usage
      for (const aoneClass of aoneClasses) {
        if (content.includes(aoneClass)) {
          aoneClassUsage++;
        }
      }
      
      // Count total className usage
      const classNameMatches = content.match(/className="[^"]*"/g);
      if (classNameMatches) {
        totalClassUsage += classNameMatches.length;
      }
    }

    // Calculate A.ONE compliance ratio
    const complianceRatio = totalClassUsage > 0 ? (aoneClassUsage / totalClassUsage) * 100 : 0;
    
    if (complianceRatio < 30) {
      issues.push({
        type: 'LOW_AONE_USAGE',
        severity: 'MEDIUM',
        message: `Low A.ONE design system usage: ${complianceRatio.toFixed(1)}%`,
        suggestion: 'Increase usage of A.ONE design system classes'
      });
      score -= 20;
    }

    this.results.aoneCompliance = {
      score: Math.max(0, score),
      issues,
      recommendations: this.generateAoneRecommendations(issues),
      metrics: {
        complianceRatio: complianceRatio.toFixed(1),
        aoneClassUsage,
        totalClassUsage
      }
    };

    console.log(`   ✓ A.ONE compliance score: ${this.results.aoneCompliance.score}/100`);
    console.log(`   ✓ A.ONE usage ratio: ${complianceRatio.toFixed(1)}%\n`);
  }

  async auditAccessibility() {
    console.log('♿ 5. Accessibility Compliance...');
    
    const issues = [];
    let score = 100;

    // Check theme toggle component for accessibility
    const themeTogglePath = path.join(process.cwd(), 'src/components/ui/theme-toggle.tsx');
    if (fs.existsSync(themeTogglePath)) {
      const content = fs.readFileSync(themeTogglePath, 'utf8');
      
      // Check for aria-label
      if (!content.includes('aria-label')) {
        issues.push({
          type: 'MISSING_ARIA_LABEL',
          severity: 'HIGH',
          file: 'src/components/ui/theme-toggle.tsx',
          message: 'Theme toggle missing aria-label for accessibility'
        });
        score -= 15;
      }

      // Check for focus management
      if (!content.includes('focus')) {
        issues.push({
          type: 'MISSING_FOCUS_MANAGEMENT',
          severity: 'MEDIUM',
          file: 'src/components/ui/theme-toggle.tsx',
          message: 'Theme toggle should include focus management'
        });
        score -= 10;
      }
    }

    // Check for high contrast support
    const indexCssPath = path.join(process.cwd(), 'src/index.css');
    if (fs.existsSync(indexCssPath)) {
      const content = fs.readFileSync(indexCssPath, 'utf8');
      
      if (!content.includes('high-contrast') && !content.includes('prefers-contrast')) {
        issues.push({
          type: 'MISSING_HIGH_CONTRAST',
          severity: 'MEDIUM',
          file: 'src/index.css',
          message: 'Missing high contrast mode support'
        });
        score -= 10;
      }
    }

    this.results.accessibility = {
      score: Math.max(0, score),
      issues,
      recommendations: this.generateAccessibilityRecommendations(issues)
    };

    console.log(`   ✓ Accessibility score: ${this.results.accessibility.score}/100`);
    console.log(`   ✓ Found ${issues.length} accessibility issues\n`);
  }

  // Helper methods
  findLineNumber(content, searchString) {
    const lines = content.split('\n');
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes(searchString)) {
        return i + 1;
      }
    }
    return null;
  }

  findCssFiles() {
    const cssFiles = [];
    const searchDir = (dir) => {
      if (!fs.existsSync(dir)) return;
      
      const files = fs.readdirSync(dir);
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory() && !AUDIT_CONFIG.excludePatterns.some(pattern => file.includes(pattern))) {
          searchDir(filePath);
        } else if (file.endsWith('.css') || file.endsWith('.scss')) {
          cssFiles.push(filePath);
        }
      }
    };
    
    searchDir(process.cwd());
    return cssFiles;
  }

  findComponentFiles() {
    const componentFiles = [];
    const searchDir = (dir) => {
      if (!fs.existsSync(dir)) return;
      
      const files = fs.readdirSync(dir);
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory() && !AUDIT_CONFIG.excludePatterns.some(pattern => file.includes(pattern))) {
          searchDir(filePath);
        } else if (file.endsWith('.tsx') || file.endsWith('.jsx')) {
          componentFiles.push(filePath);
        }
      }
    };
    
    searchDir(path.join(process.cwd(), 'src'));
    return componentFiles;
  }

  findDuplicateCssRules(cssFiles) {
    // Simplified duplicate detection
    const rules = new Map();
    const duplicates = [];
    
    for (const file of cssFiles) {
      const content = fs.readFileSync(file, 'utf8');
      const ruleMatches = content.match(/\.[a-zA-Z-]+\s*{[^}]+}/g);
      
      if (ruleMatches) {
        for (const rule of ruleMatches) {
          const selector = rule.split('{')[0].trim();
          if (rules.has(selector)) {
            duplicates.push({
              selector,
              files: [rules.get(selector), path.relative(process.cwd(), file)]
            });
          } else {
            rules.set(selector, path.relative(process.cwd(), file));
          }
        }
      }
    }
    
    return duplicates;
  }

  findUnusedCssSelectors() {
    // Simplified unused selector detection
    return []; // Would require more complex analysis
  }

  generateThemeRecommendations(issues) {
    const recommendations = [];
    
    if (issues.some(i => i.type === 'COLOR_INCONSISTENCY')) {
      recommendations.push({
        priority: 'HIGH',
        action: 'Standardize sage green color values',
        description: 'Ensure all sage green references use the consistent RGB value: 107 122 79'
      });
    }
    
    if (issues.some(i => i.type === 'HARDCODED_COLOR')) {
      recommendations.push({
        priority: 'MEDIUM',
        action: 'Replace hardcoded colors with theme variables',
        description: 'Convert hardcoded color values to use CSS custom properties or Tailwind classes'
      });
    }
    
    return recommendations;
  }

  generateShadcnRecommendations(issues) {
    const recommendations = [];
    
    if (issues.some(i => i.type === 'MISSING_CN_UTILITY')) {
      recommendations.push({
        priority: 'LOW',
        action: 'Implement cn() utility consistently',
        description: 'Use the cn() utility function for proper className merging in all components'
      });
    }
    
    return recommendations;
  }

  generateCssRecommendations(issues) {
    const recommendations = [];
    
    if (issues.some(i => i.type === 'DUPLICATE_CSS')) {
      recommendations.push({
        priority: 'MEDIUM',
        action: 'Consolidate duplicate CSS rules',
        description: 'Create reusable utility classes to eliminate CSS duplication'
      });
    }
    
    return recommendations;
  }

  generateAoneRecommendations(issues) {
    const recommendations = [];
    
    if (issues.some(i => i.type === 'LOW_AONE_USAGE')) {
      recommendations.push({
        priority: 'MEDIUM',
        action: 'Increase A.ONE design system adoption',
        description: 'Replace generic classes with A.ONE design system classes for better consistency'
      });
    }
    
    return recommendations;
  }

  generateAccessibilityRecommendations(issues) {
    const recommendations = [];
    
    if (issues.some(i => i.type === 'MISSING_ARIA_LABEL')) {
      recommendations.push({
        priority: 'HIGH',
        action: 'Add ARIA labels to interactive elements',
        description: 'Ensure all interactive elements have proper ARIA labels for screen readers'
      });
    }
    
    return recommendations;
  }

  calculateOverallScore() {
    const scores = [
      this.results.themeConsistency.score,
      this.results.shadcnIntegration.score,
      this.results.cssArchitecture.score,
      this.results.aoneCompliance.score,
      this.results.accessibility.score
    ];
    
    this.results.overall.score = Math.round(scores.reduce((a, b) => a + b, 0) / scores.length);
    
    // Collect critical issues
    const allIssues = [
      ...this.results.themeConsistency.issues,
      ...this.results.shadcnIntegration.issues,
      ...this.results.cssArchitecture.issues,
      ...this.results.aoneCompliance.issues,
      ...this.results.accessibility.issues
    ];
    
    this.results.overall.criticalIssues = allIssues.filter(issue => 
      issue.severity === 'CRITICAL' || issue.severity === 'HIGH'
    );
    
    // Collect prioritized recommendations
    const allRecommendations = [
      ...this.results.themeConsistency.recommendations,
      ...this.results.shadcnIntegration.recommendations,
      ...this.results.cssArchitecture.recommendations,
      ...this.results.aoneCompliance.recommendations,
      ...this.results.accessibility.recommendations
    ];
    
    this.results.overall.prioritizedRecommendations = allRecommendations
      .sort((a, b) => {
        const priorityOrder = { 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      })
      .slice(0, 10); // Top 10 recommendations
  }

  generateReport() {
    console.log('📋 COMPREHENSIVE THEME CONSISTENCY AUDIT REPORT');
    console.log('='.repeat(80));
    console.log(`Overall Score: ${this.results.overall.score}/100`);
    console.log('');
    
    // Individual scores
    console.log('📊 Detailed Scores:');
    console.log(`   • Theme Consistency: ${this.results.themeConsistency.score}/100`);
    console.log(`   • ShadCN Integration: ${this.results.shadcnIntegration.score}/100`);
    console.log(`   • CSS Architecture: ${this.results.cssArchitecture.score}/100`);
    console.log(`   • A.ONE Compliance: ${this.results.aoneCompliance.score}/100`);
    console.log(`   • Accessibility: ${this.results.accessibility.score}/100`);
    console.log('');
    
    // Critical issues
    if (this.results.overall.criticalIssues.length > 0) {
      console.log('🚨 Critical Issues:');
      this.results.overall.criticalIssues.forEach((issue, index) => {
        console.log(`   ${index + 1}. [${issue.severity}] ${issue.message}`);
        if (issue.file) console.log(`      File: ${issue.file}`);
        if (issue.line) console.log(`      Line: ${issue.line}`);
      });
      console.log('');
    }
    
    // Top recommendations
    if (this.results.overall.prioritizedRecommendations.length > 0) {
      console.log('💡 Priority Recommendations:');
      this.results.overall.prioritizedRecommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. [${rec.priority}] ${rec.action}`);
        console.log(`      ${rec.description}`);
      });
      console.log('');
    }
    
    // Summary
    if (this.results.overall.score >= 90) {
      console.log('✅ EXCELLENT: Theme consistency is excellent with minor improvements needed.');
    } else if (this.results.overall.score >= 80) {
      console.log('✅ GOOD: Theme consistency is good with some improvements recommended.');
    } else if (this.results.overall.score >= 70) {
      console.log('⚠️  FAIR: Theme consistency needs attention in several areas.');
    } else {
      console.log('❌ POOR: Theme consistency requires significant improvements.');
    }
    
    console.log('='.repeat(80));
    
    // Save detailed report
    const reportPath = path.join(process.cwd(), 'theme-consistency-audit-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    console.log(`📄 Detailed report saved to: ${reportPath}`);
  }
}

// Run the audit
const auditor = new ThemeConsistencyAuditor();
auditor.runAudit().catch(console.error);
