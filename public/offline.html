<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Cabinet Insight Pro</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        .offline-container {
            max-width: 500px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .offline-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .retry-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }
        
        .retry-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .features-list {
            text-align: left;
            margin: 30px 0;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
        }
        
        .features-list h3 {
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .features-list ul {
            list-style: none;
        }
        
        .features-list li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .features-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4ade80;
            font-weight: bold;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ef4444;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .connection-status {
            margin-top: 20px;
            padding: 15px;
            background: rgba(239, 68, 68, 0.2);
            border-radius: 10px;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        
        .connection-status.online {
            background: rgba(34, 197, 94, 0.2);
            border-color: rgba(34, 197, 94, 0.3);
        }
        
        .connection-status.online .status-indicator {
            background: #22c55e;
        }
        
        @media (max-width: 768px) {
            .offline-container {
                padding: 30px 20px;
                margin: 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .offline-icon {
                width: 60px;
                height: 60px;
                font-size: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📱</div>
        <h1>You're Offline</h1>
        <p>Cabinet Insight Pro is currently unavailable. Check your internet connection and try again.</p>
        
        <div class="features-list">
            <h3>Available Offline Features:</h3>
            <ul>
                <li>View previously cached analysis results</li>
                <li>Browse saved project data</li>
                <li>Access offline documentation</li>
                <li>Review performance metrics</li>
            </ul>
        </div>
        
        <div class="connection-status" id="connectionStatus">
            <span class="status-indicator"></span>
            <span id="statusText">No internet connection</span>
        </div>
        
        <a href="/" class="retry-button" onclick="window.location.reload()">
            Try Again
        </a>
        
        <a href="/" class="retry-button">
            Go to Home
        </a>
    </div>

    <script>
        // Monitor connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            const statusText = document.getElementById('statusText');
            
            if (navigator.onLine) {
                statusElement.className = 'connection-status online';
                statusText.textContent = 'Connection restored! Click "Try Again" to continue.';
            } else {
                statusElement.className = 'connection-status';
                statusText.textContent = 'No internet connection';
            }
        }
        
        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial status check
        updateConnectionStatus();
        
        // Auto-reload when connection is restored
        window.addEventListener('online', () => {
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        });
    </script>
</body>
</html>
