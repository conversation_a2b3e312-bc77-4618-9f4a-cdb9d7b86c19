import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function debugDocumentIntelligence() {
  console.log('🔍 Debugging Document Intelligence API...');

  try {
    // Test health endpoint first
    console.log('\n1. Testing health endpoint...');
    const healthResponse = await fetch('http://localhost:3001/api/document-intelligence/health');
    const healthData = await healthResponse.json();
    
    console.log('Health Status:', healthResponse.status);
    console.log('Health Data:', JSON.stringify(healthData, null, 2));

    if (healthData.data.status !== 'healthy') {
      console.log('❌ Service is not healthy, stopping test');
      return;
    }

    // Test file upload
    console.log('\n2. Testing file upload...');
    
    const testPdfPath = path.join(__dirname, 'tests/fixtures/kitchen-plan-test.pdf');
    console.log('Test PDF path:', testPdfPath);
    console.log('File exists:', fs.existsSync(testPdfPath));
    
    if (!fs.existsSync(testPdfPath)) {
      console.log('❌ Test PDF file does not exist');
      return;
    }

    const fileBuffer = fs.readFileSync(testPdfPath);
    console.log('File size:', fileBuffer.length, 'bytes');

    const formData = new FormData();
    const file = new File([fileBuffer], 'kitchen-plan-test.pdf', { type: 'application/pdf' });
    
    formData.append('document', file);
    formData.append('modelType', 'prebuilt-layout');
    formData.append('extractTables', 'true');
    formData.append('extractKeyValuePairs', 'true');
    formData.append('enableKitchenAnalysis', 'true');

    console.log('Sending request to analyze endpoint...');
    
    const response = await fetch('http://localhost:3001/api/document-intelligence/analyze', {
      method: 'POST',
      body: formData
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('Response body:', responseText);

    if (response.status !== 200) {
      console.log('❌ Request failed with status:', response.status);
      try {
        const errorData = JSON.parse(responseText);
        console.log('Error details:', JSON.stringify(errorData, null, 2));
      } catch (e) {
        console.log('Could not parse error response as JSON');
      }
    } else {
      console.log('✅ Request successful');
      try {
        const data = JSON.parse(responseText);
        console.log('Success data:', JSON.stringify(data, null, 2));
      } catch (e) {
        console.log('Could not parse success response as JSON');
      }
    }

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

// Run the debug
debugDocumentIntelligence();
