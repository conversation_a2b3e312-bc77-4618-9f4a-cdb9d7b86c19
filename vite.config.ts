import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false,
      },
    },
    // Enhanced HMR configuration for theme development
    hmr: {
      overlay: mode === 'development' ? {
        warnings: false,
        errors: true
      } : true,
      // Force CSS reload for theme changes
      clientPort: 8080
    }
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  define: {
    // Ensure process is not used in browser code
    'process.env': {}
  },
  build: {
    outDir: 'dist',
    sourcemap: mode === 'development' ? true : false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['lucide-react', '@radix-ui/react-slot'],
          three: ['three', '@react-three/fiber', '@react-three/drei']
        }
      },
      // Suppress source map warnings for external dependencies
      onwarn(warning, warn) {
        // Suppress source map warnings
        if (warning.code === 'SOURCEMAP_ERROR') return;
        // Suppress circular dependency warnings for known safe cases
        if (warning.code === 'CIRCULAR_DEPENDENCY') return;
        warn(warning);
      }
    }
  },
  optimizeDeps: {
    exclude: ['@vite/client', '@vite/env'],
    include: [
      'react',
      'react-dom',
      'react-dom/client',
      'lucide-react',
      'clsx',
      'tailwind-merge'
    ]
  },
  // Enhanced CSS processing with cache busting
  css: {
    devSourcemap: mode === 'development',
    postcss: {
      plugins: []
    },
    // Force CSS reload in development
    preprocessorOptions: {
      css: {
        additionalData: mode === 'development'
          ? `/* Cache bust: ${Date.now()} */\n`
          : ''
      }
    }
  },
  // Enhanced esbuild configuration
  esbuild: {
    logOverride: {
      'this-is-undefined-in-esm': 'silent'
    }
  }
}));
