version: '3.8'

# Cabinet Insight Pro - Complete Scalability Infrastructure
# Supports 1000+ concurrent users with intelligent load balancing,
# distributed caching, and database clustering

services:
  # Nginx Load Balancer
  nginx-lb:
    image: nginx:alpine
    container_name: cabinet-nginx-lb
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Status endpoint
    volumes:
      - ./nginx/nginx-lb.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx-cache:/var/cache/nginx
    depends_on:
      - cabinet-api-1
      - cabinet-api-2
      - cabinet-api-3
    networks:
      - cabinet-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/nginx_status"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Cabinet Insight Pro API Instances (Clustered)
  cabinet-api-1:
    build:
      context: ../server
      dockerfile: Dockerfile.production
    container_name: cabinet-api-1
    environment:
      - NODE_ENV=production
      - PORT=3001
      - WORKER_ID=1
      - REDIS_NODE_1_HOST=redis-node-1
      - REDIS_NODE_2_HOST=redis-node-2
      - REDIS_NODE_3_HOST=redis-node-3
      - POSTGRES_HOST=pgbouncer
      - POSTGRES_PORT=5432
      - POSTGRES_READ_HOST=postgres-replica-1
      - POSTGRES_READ_PORT=5432
      - MAX_WORKERS=2
      - ENABLE_CLUSTERING=true
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
    volumes:
      - api-1-uploads:/app/uploads
      - api-1-logs:/app/logs
    depends_on:
      - redis-node-1
      - redis-node-2
      - redis-node-3
      - pgbouncer
    networks:
      - cabinet-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  cabinet-api-2:
    build:
      context: ../server
      dockerfile: Dockerfile.production
    container_name: cabinet-api-2
    environment:
      - NODE_ENV=production
      - PORT=3001
      - WORKER_ID=2
      - REDIS_NODE_1_HOST=redis-node-1
      - REDIS_NODE_2_HOST=redis-node-2
      - REDIS_NODE_3_HOST=redis-node-3
      - POSTGRES_HOST=pgbouncer
      - POSTGRES_PORT=5432
      - POSTGRES_READ_HOST=postgres-replica-2
      - POSTGRES_READ_PORT=5432
      - MAX_WORKERS=2
      - ENABLE_CLUSTERING=true
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
    volumes:
      - api-2-uploads:/app/uploads
      - api-2-logs:/app/logs
    depends_on:
      - redis-node-1
      - redis-node-2
      - redis-node-3
      - pgbouncer
    networks:
      - cabinet-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  cabinet-api-3:
    build:
      context: ../server
      dockerfile: Dockerfile.production
    container_name: cabinet-api-3
    environment:
      - NODE_ENV=production
      - PORT=3001
      - WORKER_ID=3
      - REDIS_NODE_1_HOST=redis-node-1
      - REDIS_NODE_2_HOST=redis-node-2
      - REDIS_NODE_3_HOST=redis-node-3
      - POSTGRES_HOST=pgbouncer
      - POSTGRES_PORT=5432
      - POSTGRES_READ_HOST=postgres-replica-1
      - POSTGRES_READ_PORT=5432
      - MAX_WORKERS=2
      - ENABLE_CLUSTERING=true
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
    volumes:
      - api-3-uploads:/app/uploads
      - api-3-logs:/app/logs
    depends_on:
      - redis-node-1
      - redis-node-2
      - redis-node-3
      - pgbouncer
    networks:
      - cabinet-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cluster Nodes
  redis-node-1:
    image: redis:7-alpine
    container_name: cabinet-redis-node-1
    command: redis-server --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --port 7001 --bind 0.0.0.0 --protected-mode no
    ports:
      - "7001:7001"
      - "17001:17001"
    volumes:
      - redis-1-data:/data
    networks:
      - cabinet-network
    restart: unless-stopped

  redis-node-2:
    image: redis:7-alpine
    container_name: cabinet-redis-node-2
    command: redis-server --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --port 7002 --bind 0.0.0.0 --protected-mode no
    ports:
      - "7002:7002"
      - "17002:17002"
    volumes:
      - redis-2-data:/data
    networks:
      - cabinet-network
    restart: unless-stopped

  redis-node-3:
    image: redis:7-alpine
    container_name: cabinet-redis-node-3
    command: redis-server --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --port 7003 --bind 0.0.0.0 --protected-mode no
    ports:
      - "7003:7003"
      - "17003:17003"
    volumes:
      - redis-3-data:/data
    networks:
      - cabinet-network
    restart: unless-stopped

  # PostgreSQL Cluster
  postgres-primary:
    image: postgres:15
    container_name: cabinet-postgres-primary
    environment:
      POSTGRES_DB: cabinet_insight_pro
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-cabinet_insight_secure_2024}
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: ${REPLICATION_PASSWORD:-replicator_secure_2024}
    volumes:
      - postgres-primary-data:/var/lib/postgresql/data
      - ./postgres-cluster/postgres-primary.conf:/etc/postgresql/postgresql.conf
      - ./postgres-cluster/setup-primary.sh:/docker-entrypoint-initdb.d/setup-primary.sh
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    ports:
      - "5432:5432"
    networks:
      - cabinet-network
    restart: unless-stopped

  postgres-replica-1:
    image: postgres:15
    container_name: cabinet-postgres-replica-1
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-cabinet_insight_secure_2024}
      POSTGRES_PRIMARY_HOST: postgres-primary
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: ${REPLICATION_PASSWORD:-replicator_secure_2024}
    volumes:
      - postgres-replica-1-data:/var/lib/postgresql/data
      - ./postgres-cluster/setup-replica.sh:/docker-entrypoint-initdb.d/setup-replica.sh
    ports:
      - "5433:5432"
    depends_on:
      - postgres-primary
    networks:
      - cabinet-network
    restart: unless-stopped

  postgres-replica-2:
    image: postgres:15
    container_name: cabinet-postgres-replica-2
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-cabinet_insight_secure_2024}
      POSTGRES_PRIMARY_HOST: postgres-primary
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: ${REPLICATION_PASSWORD:-replicator_secure_2024}
    volumes:
      - postgres-replica-2-data:/var/lib/postgresql/data
      - ./postgres-cluster/setup-replica.sh:/docker-entrypoint-initdb.d/setup-replica.sh
    ports:
      - "5434:5432"
    depends_on:
      - postgres-primary
    networks:
      - cabinet-network
    restart: unless-stopped

  # PgBouncer Connection Pooler
  pgbouncer:
    image: pgbouncer/pgbouncer:latest
    container_name: cabinet-pgbouncer
    environment:
      DATABASES_HOST: postgres-primary
      DATABASES_PORT: 5432
      DATABASES_USER: postgres
      DATABASES_PASSWORD: ${POSTGRES_PASSWORD:-cabinet_insight_secure_2024}
      DATABASES_DBNAME: cabinet_insight_pro
      POOL_MODE: transaction
      MAX_CLIENT_CONN: 1000
      DEFAULT_POOL_SIZE: 25
    ports:
      - "6432:5432"
    depends_on:
      - postgres-primary
    networks:
      - cabinet-network
    restart: unless-stopped

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    container_name: cabinet-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    networks:
      - cabinet-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: cabinet-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - cabinet-network
    restart: unless-stopped

volumes:
  # API volumes
  api-1-uploads:
  api-1-logs:
  api-2-uploads:
  api-2-logs:
  api-3-uploads:
  api-3-logs:
  
  # Redis volumes
  redis-1-data:
  redis-2-data:
  redis-3-data:
  
  # PostgreSQL volumes
  postgres-primary-data:
  postgres-replica-1-data:
  postgres-replica-2-data:
  
  # Monitoring volumes
  prometheus-data:
  grafana-data:
  
  # Nginx cache
  nginx-cache:

networks:
  cabinet-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
