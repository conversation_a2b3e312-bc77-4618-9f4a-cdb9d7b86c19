#!/bin/bash
# Redis Cluster Initialization Script for Cabinet Insight Pro

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo -e "${BLUE}🔧 Initializing Redis Cluster for Cabinet Insight Pro${NC}"
echo -e "${BLUE}=================================================${NC}"
echo ""

# Check if Redis nodes are running
check_redis_nodes() {
    print_info "Checking Redis nodes..."
    
    local nodes=("cabinet-redis-node-1:7001" "cabinet-redis-node-2:7002" "cabinet-redis-node-3:7003")
    local ready_nodes=0
    
    for node in "${nodes[@]}"; do
        container_name=$(echo $node | cut -d':' -f1)
        port=$(echo $node | cut -d':' -f2)
        
        if docker exec $container_name redis-cli -p $port ping &> /dev/null; then
            print_status "Node $node is ready"
            ((ready_nodes++))
        else
            print_warning "Node $node is not ready"
        fi
    done
    
    if [ $ready_nodes -eq 3 ]; then
        print_status "All Redis nodes are ready"
        return 0
    else
        print_error "Not all Redis nodes are ready ($ready_nodes/3)"
        return 1
    fi
}

# Wait for Redis nodes to be ready
wait_for_nodes() {
    print_info "Waiting for Redis nodes to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if check_redis_nodes; then
            return 0
        fi
        
        print_info "Attempt $attempt/$max_attempts - waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    print_error "Redis nodes did not become ready within the timeout period"
    return 1
}

# Create Redis cluster
create_cluster() {
    print_info "Creating Redis cluster..."
    
    # Check if cluster already exists
    if docker exec cabinet-redis-node-1 redis-cli -p 7001 cluster nodes 2>/dev/null | grep -q "master"; then
        print_warning "Redis cluster already exists"
        return 0
    fi
    
    # Create the cluster
    docker exec cabinet-redis-node-1 redis-cli --cluster create \
        redis-node-1:7001 \
        redis-node-2:7002 \
        redis-node-3:7003 \
        --cluster-replicas 0 \
        --cluster-yes
    
    if [ $? -eq 0 ]; then
        print_status "Redis cluster created successfully"
    else
        print_error "Failed to create Redis cluster"
        return 1
    fi
}

# Verify cluster status
verify_cluster() {
    print_info "Verifying cluster status..."
    
    # Check cluster info
    echo ""
    echo -e "${BLUE}Cluster Info:${NC}"
    docker exec cabinet-redis-node-1 redis-cli -p 7001 cluster info
    
    echo ""
    echo -e "${BLUE}Cluster Nodes:${NC}"
    docker exec cabinet-redis-node-1 redis-cli -p 7001 cluster nodes
    
    # Test cluster functionality
    echo ""
    print_info "Testing cluster functionality..."
    
    # Set a test key
    docker exec cabinet-redis-node-1 redis-cli -p 7001 set test_key "cluster_test_value"
    
    # Try to get the key from different nodes
    local test_passed=true
    
    for port in 7001 7002 7003; do
        container_name="cabinet-redis-node-$((port-7000))"
        value=$(docker exec $container_name redis-cli -p $port get test_key 2>/dev/null || echo "ERROR")
        
        if [ "$value" = "cluster_test_value" ]; then
            print_status "Node $container_name:$port can access cluster data"
        else
            print_warning "Node $container_name:$port cannot access cluster data"
            test_passed=false
        fi
    done
    
    # Clean up test key
    docker exec cabinet-redis-node-1 redis-cli -p 7001 del test_key
    
    if [ "$test_passed" = true ]; then
        print_status "Cluster functionality test passed"
    else
        print_warning "Cluster functionality test had issues"
    fi
}

# Configure cluster for production
configure_cluster() {
    print_info "Configuring cluster for production..."
    
    # Set cluster configuration on all nodes
    for port in 7001 7002 7003; do
        container_name="cabinet-redis-node-$((port-7000))"
        
        # Configure memory policy
        docker exec $container_name redis-cli -p $port config set maxmemory-policy allkeys-lru
        
        # Configure save policy for persistence
        docker exec $container_name redis-cli -p $port config set save "900 1 300 10 60 10000"
        
        # Configure cluster timeouts
        docker exec $container_name redis-cli -p $port config set cluster-node-timeout 15000
        
        print_status "Node $container_name configured"
    done
}

# Show cluster status
show_status() {
    echo ""
    echo -e "${BLUE}📊 Redis Cluster Status${NC}"
    echo -e "${BLUE}======================${NC}"
    
    # Show cluster slots
    echo ""
    echo -e "${BLUE}Cluster Slots Distribution:${NC}"
    docker exec cabinet-redis-node-1 redis-cli -p 7001 cluster slots
    
    # Show memory usage
    echo ""
    echo -e "${BLUE}Memory Usage:${NC}"
    for port in 7001 7002 7003; do
        container_name="cabinet-redis-node-$((port-7000))"
        memory_info=$(docker exec $container_name redis-cli -p $port info memory | grep used_memory_human)
        echo "$container_name:$port - $memory_info"
    done
    
    # Show connection info
    echo ""
    echo -e "${BLUE}Connection Info:${NC}"
    for port in 7001 7002 7003; do
        container_name="cabinet-redis-node-$((port-7000))"
        connected_clients=$(docker exec $container_name redis-cli -p $port info clients | grep connected_clients)
        echo "$container_name:$port - $connected_clients"
    done
}

# Main execution
main() {
    if ! wait_for_nodes; then
        print_error "Failed to initialize Redis cluster - nodes not ready"
        exit 1
    fi
    
    if ! create_cluster; then
        print_error "Failed to create Redis cluster"
        exit 1
    fi
    
    verify_cluster
    configure_cluster
    show_status
    
    echo ""
    print_status "Redis cluster initialization completed successfully!"
    echo ""
    echo -e "${BLUE}Cluster endpoints:${NC}"
    echo "  - Node 1: localhost:7001"
    echo "  - Node 2: localhost:7002"
    echo "  - Node 3: localhost:7003"
    echo ""
    echo -e "${BLUE}Connection string for applications:${NC}"
    echo "  redis://localhost:7001,localhost:7002,localhost:7003"
    echo ""
    echo -e "${YELLOW}Note: The cluster is configured for development. Review security settings for production!${NC}"
}

# Run main function
main "$@"
