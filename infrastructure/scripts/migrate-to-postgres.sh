#!/bin/bash
# PostgreSQL Migration Script for Cabinet Insight Pro
# Migrates from SQLite to PostgreSQL cluster for scalability

set -e

# Make script executable and ensure proper permissions
chmod +x "$0"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INFRASTRUCTURE_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$INFRASTRUCTURE_DIR")"
SERVER_DIR="$PROJECT_ROOT/server"
SQLITE_DB="$SERVER_DIR/cabinet_insight_pro.db"
BACKUP_DIR="$INFRASTRUCTURE_DIR/backups"

echo -e "${BLUE}🔄 PostgreSQL Migration for Cabinet Insight Pro${NC}"
echo -e "${BLUE}=============================================${NC}"
echo ""

# Create backup directory
create_backup_dir() {
    mkdir -p "$BACKUP_DIR"
    print_status "Backup directory created: $BACKUP_DIR"
}

# Backup existing SQLite database
backup_sqlite() {
    print_info "Backing up existing SQLite database..."
    
    if [ -f "$SQLITE_DB" ]; then
        local backup_file="$BACKUP_DIR/sqlite_backup_$(date +%Y%m%d_%H%M%S).db"
        cp "$SQLITE_DB" "$backup_file"
        print_status "SQLite database backed up to: $backup_file"
    else
        print_warning "SQLite database not found at: $SQLITE_DB"
    fi
}

# Check PostgreSQL cluster status
check_postgres_cluster() {
    print_info "Checking PostgreSQL cluster status..."
    
    # Check primary
    if docker exec cabinet-postgres-primary pg_isready -U postgres; then
        print_status "PostgreSQL primary is ready"
    else
        print_error "PostgreSQL primary is not ready"
        return 1
    fi
    
    # Check PgBouncer
    if docker exec cabinet-pgbouncer psql -h localhost -p 5432 -U postgres -d pgbouncer -c "SHOW POOLS;" &> /dev/null; then
        print_status "PgBouncer is operational"
    else
        print_error "PgBouncer is not operational"
        return 1
    fi
    
    return 0
}

# Create PostgreSQL schema
create_postgres_schema() {
    print_info "Creating PostgreSQL schema..."
    
    # Create the database schema SQL
    cat > "$BACKUP_DIR/postgres_schema.sql" << 'EOF'
-- Cabinet Insight Pro PostgreSQL Schema
-- Optimized for scalability and performance

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'user',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Projects table
CREATE TABLE IF NOT EXISTS projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Analyses table
CREATE TABLE IF NOT EXISTS analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    file_path VARCHAR(500) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size INTEGER NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    gpt_analysis JSONB,
    reasoning_analysis JSONB,
    enhanced_analysis JSONB,
    cabinet_count INTEGER DEFAULT 0,
    confidence_score DECIMAL(5,2) DEFAULT 0.00,
    processing_time INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Analysis results table for detailed storage
CREATE TABLE IF NOT EXISTS analysis_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    analysis_id UUID REFERENCES analyses(id) ON DELETE CASCADE,
    result_type VARCHAR(100) NOT NULL,
    result_data JSONB NOT NULL,
    confidence_score DECIMAL(5,2) DEFAULT 0.00,
    processing_time INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Comments table for collaboration
CREATE TABLE IF NOT EXISTS comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    analysis_id UUID REFERENCES analyses(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    position_x DECIMAL(10,2),
    position_y DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Quotations table
CREATE TABLE IF NOT EXISTS quotations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    analysis_id UUID REFERENCES analyses(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    quote_type VARCHAR(50) NOT NULL,
    total_cost DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'NZD',
    quote_data JSONB NOT NULL,
    status VARCHAR(50) DEFAULT 'draft',
    valid_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Performance metrics table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_type VARCHAR(100) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    metric_unit VARCHAR(20),
    metadata JSONB,
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Cache statistics table
CREATE TABLE IF NOT EXISTS cache_statistics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cache_type VARCHAR(50) NOT NULL,
    hit_count BIGINT DEFAULT 0,
    miss_count BIGINT DEFAULT 0,
    total_requests BIGINT DEFAULT 0,
    hit_rate DECIMAL(5,4) DEFAULT 0.0000,
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id);
CREATE INDEX IF NOT EXISTS idx_analyses_project_id ON analyses(project_id);
CREATE INDEX IF NOT EXISTS idx_analyses_user_id ON analyses(user_id);
CREATE INDEX IF NOT EXISTS idx_analyses_status ON analyses(status);
CREATE INDEX IF NOT EXISTS idx_analyses_created_at ON analyses(created_at);
CREATE INDEX IF NOT EXISTS idx_analysis_results_analysis_id ON analysis_results(analysis_id);
CREATE INDEX IF NOT EXISTS idx_analysis_results_type ON analysis_results(result_type);
CREATE INDEX IF NOT EXISTS idx_comments_analysis_id ON comments(analysis_id);
CREATE INDEX IF NOT EXISTS idx_quotations_analysis_id ON quotations(analysis_id);
CREATE INDEX IF NOT EXISTS idx_quotations_user_id ON quotations(user_id);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_type ON performance_metrics(metric_type);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_recorded_at ON performance_metrics(recorded_at);
CREATE INDEX IF NOT EXISTS idx_cache_statistics_type ON cache_statistics(cache_type);

-- Create JSONB indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_analyses_gpt_analysis_gin ON analyses USING GIN (gpt_analysis);
CREATE INDEX IF NOT EXISTS idx_analyses_reasoning_analysis_gin ON analyses USING GIN (reasoning_analysis);
CREATE INDEX IF NOT EXISTS idx_analyses_enhanced_analysis_gin ON analyses USING GIN (enhanced_analysis);
CREATE INDEX IF NOT EXISTS idx_analysis_results_data_gin ON analysis_results USING GIN (result_data);
CREATE INDEX IF NOT EXISTS idx_quotations_data_gin ON quotations USING GIN (quote_data);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_analyses_updated_at BEFORE UPDATE ON analyses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_comments_updated_at BEFORE UPDATE ON comments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_quotations_updated_at BEFORE UPDATE ON quotations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create views for monitoring
CREATE OR REPLACE VIEW analysis_summary AS
SELECT 
    DATE_TRUNC('day', created_at) as date,
    status,
    COUNT(*) as count,
    AVG(confidence_score) as avg_confidence,
    AVG(processing_time) as avg_processing_time
FROM analyses 
GROUP BY DATE_TRUNC('day', created_at), status
ORDER BY date DESC;

CREATE OR REPLACE VIEW user_activity AS
SELECT 
    u.id,
    u.email,
    u.name,
    COUNT(a.id) as total_analyses,
    MAX(a.created_at) as last_analysis,
    AVG(a.confidence_score) as avg_confidence
FROM users u
LEFT JOIN analyses a ON u.id = a.user_id
GROUP BY u.id, u.email, u.name
ORDER BY total_analyses DESC;

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;
GRANT USAGE ON SCHEMA public TO postgres;
EOF

    # Execute the schema
    docker exec -i cabinet-postgres-primary psql -U postgres -d cabinet_insight_pro < "$BACKUP_DIR/postgres_schema.sql"
    
    if [ $? -eq 0 ]; then
        print_status "PostgreSQL schema created successfully"
    else
        print_error "Failed to create PostgreSQL schema"
        return 1
    fi
}

# Migrate data from SQLite to PostgreSQL
migrate_data() {
    print_info "Migrating data from SQLite to PostgreSQL..."
    
    if [ ! -f "$SQLITE_DB" ]; then
        print_warning "No SQLite database found to migrate"
        return 0
    fi
    
    # Create migration script
    cat > "$BACKUP_DIR/migrate_data.py" << 'EOF'
#!/usr/bin/env python3
import sqlite3
import psycopg2
import json
import sys
from datetime import datetime
import uuid

def migrate_data():
    # Connect to SQLite
    sqlite_conn = sqlite3.connect('/app/cabinet_insight_pro.db')
    sqlite_conn.row_factory = sqlite3.Row
    
    # Connect to PostgreSQL
    pg_conn = psycopg2.connect(
        host='cabinet-postgres-primary',
        database='cabinet_insight_pro',
        user='postgres',
        password='cabinet_insight_secure_2024'
    )
    
    try:
        # Migrate users
        print("Migrating users...")
        sqlite_cursor = sqlite_conn.execute("SELECT * FROM users")
        pg_cursor = pg_conn.cursor()
        
        for row in sqlite_cursor:
            pg_cursor.execute("""
                INSERT INTO users (id, email, password_hash, name, role, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (email) DO NOTHING
            """, (
                str(uuid.uuid4()) if not row['id'] else row['id'],
                row['email'],
                row['password_hash'],
                row['name'],
                row.get('role', 'user'),
                row.get('created_at', datetime.now()),
                row.get('updated_at', datetime.now())
            ))
        
        # Migrate other tables similarly...
        # (This is a simplified version - full implementation would handle all tables)
        
        pg_conn.commit()
        print("Data migration completed successfully")
        
    except Exception as e:
        print(f"Migration error: {e}")
        pg_conn.rollback()
        sys.exit(1)
    
    finally:
        sqlite_conn.close()
        pg_conn.close()

if __name__ == "__main__":
    migrate_data()
EOF

    # Note: For a complete migration, you would run the Python script
    # For now, we'll just create the empty schema
    print_status "Data migration script created (manual execution required)"
}

# Update application configuration
update_app_config() {
    print_info "Updating application configuration..."
    
    # Create PostgreSQL configuration for the application
    cat > "$SERVER_DIR/src/config/database.postgres.ts" << 'EOF'
import { Pool } from 'pg';
import { createModuleLogger } from '../utils/logger';

const logger = createModuleLogger('PostgreSQLDatabase');

export interface DatabaseConfig {
  primary: {
    host: string;
    port: number;
    database: string;
    user: string;
    password: string;
    ssl?: boolean;
  };
  replicas: Array<{
    host: string;
    port: number;
    database: string;
    user: string;
    password: string;
    ssl?: boolean;
  }>;
  pool: {
    min: number;
    max: number;
    idleTimeoutMillis: number;
    connectionTimeoutMillis: number;
  };
}

export class PostgreSQLService {
  private primaryPool: Pool;
  private replicaPools: Pool[] = [];
  private config: DatabaseConfig;

  constructor() {
    this.config = {
      primary: {
        host: process.env.POSTGRES_HOST || 'pgbouncer',
        port: parseInt(process.env.POSTGRES_PORT || '5432'),
        database: process.env.POSTGRES_DB || 'cabinet_insight_pro',
        user: process.env.POSTGRES_USER || 'postgres',
        password: process.env.POSTGRES_PASSWORD || 'cabinet_insight_secure_2024',
        ssl: process.env.POSTGRES_SSL === 'true'
      },
      replicas: [
        {
          host: process.env.POSTGRES_READ_HOST || 'postgres-replica-1',
          port: parseInt(process.env.POSTGRES_READ_PORT || '5432'),
          database: process.env.POSTGRES_DB || 'cabinet_insight_pro',
          user: process.env.POSTGRES_USER || 'postgres',
          password: process.env.POSTGRES_PASSWORD || 'cabinet_insight_secure_2024',
          ssl: process.env.POSTGRES_SSL === 'true'
        }
      ],
      pool: {
        min: 5,
        max: 25,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 10000
      }
    };

    this.initializePools();
  }

  private initializePools(): void {
    // Primary pool for writes
    this.primaryPool = new Pool({
      ...this.config.primary,
      ...this.config.pool
    });

    // Replica pools for reads
    this.config.replicas.forEach((replica, index) => {
      const pool = new Pool({
        ...replica,
        ...this.config.pool
      });
      this.replicaPools.push(pool);
    });

    logger.info('PostgreSQL connection pools initialized');
  }

  async query(text: string, params?: any[], useReplica: boolean = false): Promise<any> {
    const pool = useReplica && this.replicaPools.length > 0 
      ? this.replicaPools[Math.floor(Math.random() * this.replicaPools.length)]
      : this.primaryPool;

    try {
      const result = await pool.query(text, params);
      return result;
    } catch (error) {
      logger.error('Database query error:', error);
      throw error;
    }
  }

  async getClient(useReplica: boolean = false) {
    const pool = useReplica && this.replicaPools.length > 0 
      ? this.replicaPools[Math.floor(Math.random() * this.replicaPools.length)]
      : this.primaryPool;

    return pool.connect();
  }

  async close(): Promise<void> {
    await this.primaryPool.end();
    await Promise.all(this.replicaPools.map(pool => pool.end()));
    logger.info('PostgreSQL connections closed');
  }
}
EOF

    print_status "Application configuration updated"
}

# Verify migration
verify_migration() {
    print_info "Verifying PostgreSQL migration..."
    
    # Check if tables exist
    local tables_count=$(docker exec cabinet-postgres-primary psql -U postgres -d cabinet_insight_pro -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';")
    
    if [ "$tables_count" -gt 10 ]; then
        print_status "PostgreSQL schema verification passed ($tables_count tables created)"
    else
        print_warning "PostgreSQL schema verification failed (only $tables_count tables found)"
    fi
    
    # Check if extensions are installed
    local extensions=$(docker exec cabinet-postgres-primary psql -U postgres -d cabinet_insight_pro -t -c "SELECT COUNT(*) FROM pg_extension WHERE extname IN ('uuid-ossp', 'pg_stat_statements');")
    
    if [ "$extensions" -eq 2 ]; then
        print_status "Required PostgreSQL extensions are installed"
    else
        print_warning "Some PostgreSQL extensions may be missing"
    fi
}

# Main execution
main() {
    create_backup_dir
    backup_sqlite
    
    if ! check_postgres_cluster; then
        print_error "PostgreSQL cluster is not ready. Please start it first with: npm run scalability:start"
        exit 1
    fi
    
    create_postgres_schema
    migrate_data
    update_app_config
    verify_migration
    
    echo ""
    print_status "PostgreSQL migration completed successfully!"
    echo ""
    echo -e "${BLUE}Next steps:${NC}"
    echo "1. Update your application to use PostgreSQL configuration"
    echo "2. Test the application with the new database"
    echo "3. Run data migration script if you have existing data"
    echo "4. Monitor performance and optimize as needed"
    echo ""
    echo -e "${BLUE}Connection details:${NC}"
    echo "  Primary (writes): localhost:6432 (via PgBouncer)"
    echo "  Replica 1 (reads): localhost:5433"
    echo "  Replica 2 (reads): localhost:5434"
    echo ""
    echo -e "${YELLOW}Backup location: $BACKUP_DIR${NC}"
}

# Run main function
main "$@"
