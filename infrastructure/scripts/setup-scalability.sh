#!/bin/bash
# Cabinet Insight Pro - Scalability Infrastructure Setup Script
# Sets up complete scalability infrastructure for 1000+ concurrent users

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INFRASTRUCTURE_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$INFRASTRUCTURE_DIR")"

echo -e "${BLUE}🚀 Cabinet Insight Pro - Scalability Infrastructure Setup${NC}"
echo -e "${BLUE}=======================================================${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    print_status "Prerequisites check passed"
}

# Create necessary directories
create_directories() {
    print_info "Creating necessary directories..."
    
    mkdir -p "$INFRASTRUCTURE_DIR/nginx/ssl"
    mkdir -p "$INFRASTRUCTURE_DIR/monitoring/grafana/dashboards"
    mkdir -p "$INFRASTRUCTURE_DIR/monitoring/grafana/datasources"
    mkdir -p "$INFRASTRUCTURE_DIR/load-testing"
    mkdir -p "$INFRASTRUCTURE_DIR/logs"
    
    print_status "Directories created"
}

# Generate SSL certificates (self-signed for development)
generate_ssl_certificates() {
    print_info "Generating SSL certificates..."
    
    SSL_DIR="$INFRASTRUCTURE_DIR/nginx/ssl"
    
    if [ ! -f "$SSL_DIR/cert.pem" ] || [ ! -f "$SSL_DIR/key.pem" ]; then
        openssl req -x509 -newkey rsa:4096 -keyout "$SSL_DIR/key.pem" -out "$SSL_DIR/cert.pem" \
            -days 365 -nodes -subj "/C=NZ/ST=Auckland/L=Auckland/O=Cabinet Insight Pro/CN=localhost"
        
        print_status "SSL certificates generated"
    else
        print_status "SSL certificates already exist"
    fi
}

# Create environment file
create_environment_file() {
    print_info "Creating environment configuration..."
    
    ENV_FILE="$INFRASTRUCTURE_DIR/.env"
    
    if [ ! -f "$ENV_FILE" ]; then
        cat > "$ENV_FILE" << EOF
# Cabinet Insight Pro - Scalability Environment Configuration

# PostgreSQL Configuration
POSTGRES_PASSWORD=cabinet_insight_secure_2024
REPLICATION_PASSWORD=replicator_secure_2024

# Redis Configuration
REDIS_PASSWORD=

# Azure OpenAI Configuration (copy from main .env)
AZURE_OPENAI_API_KEY=
AZURE_OPENAI_ENDPOINT=
AZURE_OPENAI_API_VERSION=2024-12-01-preview
AZURE_OPENAI_GPT4O_DEPLOYMENT_NAME=gpt-4o
AZURE_OPENAI_GPT4O_MINI_DEPLOYMENT_NAME=o4-mini
AZURE_OPENAI_GPTO1_DEPLOYMENT_NAME=gpt-o1

# Monitoring Configuration
GRAFANA_PASSWORD=admin

# Performance Configuration
MAX_WORKERS=4
ENABLE_CLUSTERING=true
OPENAI_REQUESTS_PER_MINUTE=60
OPENAI_TOKENS_PER_MINUTE=150000

# Load Balancer Configuration
NGINX_WORKER_PROCESSES=auto
NGINX_WORKER_CONNECTIONS=2048

# Cache Configuration
REDIS_MAX_MEMORY=256mb
REDIS_MAX_MEMORY_POLICY=allkeys-lru

# Database Configuration
POSTGRES_MAX_CONNECTIONS=200
POSTGRES_SHARED_BUFFERS=256MB
POSTGRES_EFFECTIVE_CACHE_SIZE=1GB
EOF
        
        print_warning "Environment file created at $ENV_FILE"
        print_warning "Please update the Azure OpenAI configuration in $ENV_FILE"
    else
        print_status "Environment file already exists"
    fi
}

# Create monitoring configuration
create_monitoring_config() {
    print_info "Creating monitoring configuration..."
    
    # Prometheus configuration
    cat > "$INFRASTRUCTURE_DIR/monitoring/prometheus.yml" << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'cabinet-insight-api'
    static_configs:
      - targets: ['cabinet-api-1:3001', 'cabinet-api-2:3001', 'cabinet-api-3:3001']
    metrics_path: '/api/metrics'
    scrape_interval: 30s

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-lb:8080']
    metrics_path: '/nginx_status'
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-node-1:7001', 'redis-node-2:7002', 'redis-node-3:7003']
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-primary:5432']
    scrape_interval: 30s
EOF

    # Grafana datasource configuration
    mkdir -p "$INFRASTRUCTURE_DIR/monitoring/grafana/datasources"
    cat > "$INFRASTRUCTURE_DIR/monitoring/grafana/datasources/prometheus.yml" << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
EOF

    print_status "Monitoring configuration created"
}

# Create load testing configuration
create_load_testing_config() {
    print_info "Creating load testing configuration..."
    
    cat > "$INFRASTRUCTURE_DIR/load-testing/load-test.yml" << EOF
config:
  target: 'http://localhost'
  phases:
    - duration: 60
      arrivalRate: 10
      name: "Warm up"
    - duration: 120
      arrivalRate: 50
      name: "Ramp up load"
    - duration: 300
      arrivalRate: 100
      name: "Sustained load"
    - duration: 60
      arrivalRate: 200
      name: "Peak load"
  processor: "./load-test-processor.js"

scenarios:
  - name: "Health Check"
    weight: 10
    flow:
      - get:
          url: "/api/health"
          expect:
            - statusCode: 200

  - name: "API Analysis"
    weight: 70
    flow:
      - post:
          url: "/api/analysis/upload"
          formData:
            file: "@../PDFs/test-kitchen.pdf"
          expect:
            - statusCode: [200, 202]
      - think: 5

  - name: "Performance Metrics"
    weight: 20
    flow:
      - get:
          url: "/api/performance/overview"
          expect:
            - statusCode: 200
      - think: 2
EOF

    print_status "Load testing configuration created"
}

# Install dependencies
install_dependencies() {
    print_info "Installing scalability dependencies..."
    
    cd "$PROJECT_ROOT/server"
    
    # Install new dependencies
    npm install bull@^4.12.2 cluster@^0.7.7 @types/bull@^4.10.0
    
    print_status "Dependencies installed"
}

# Build Docker images
build_docker_images() {
    print_info "Building Docker images..."
    
    cd "$PROJECT_ROOT/server"
    
    # Build the production image
    docker build -f Dockerfile.production -t cabinet-insight-pro:latest .
    
    print_status "Docker images built"
}

# Initialize infrastructure
initialize_infrastructure() {
    print_info "Initializing scalability infrastructure..."
    
    cd "$INFRASTRUCTURE_DIR"
    
    # Start the infrastructure
    docker-compose -f docker-compose.scalability.yml up -d redis-node-1 redis-node-2 redis-node-3
    
    # Wait for Redis nodes to be ready
    print_info "Waiting for Redis nodes to be ready..."
    sleep 30
    
    # Initialize Redis cluster
    print_info "Initializing Redis cluster..."
    docker exec cabinet-redis-node-1 redis-cli --cluster create \
        redis-node-1:7001 redis-node-2:7002 redis-node-3:7003 \
        --cluster-replicas 0 --cluster-yes || true
    
    # Start PostgreSQL
    docker-compose -f docker-compose.scalability.yml up -d postgres-primary
    
    # Wait for PostgreSQL to be ready
    print_info "Waiting for PostgreSQL to be ready..."
    sleep 30
    
    # Start replicas
    docker-compose -f docker-compose.scalability.yml up -d postgres-replica-1 postgres-replica-2
    
    # Wait for replicas
    sleep 30
    
    # Start PgBouncer
    docker-compose -f docker-compose.scalability.yml up -d pgbouncer
    
    print_status "Infrastructure initialized"
}

# Validate setup
validate_setup() {
    print_info "Validating scalability setup..."
    
    cd "$INFRASTRUCTURE_DIR"
    
    # Check Redis cluster
    if docker exec cabinet-redis-node-1 redis-cli -p 7001 cluster nodes | grep -q "master"; then
        print_status "Redis cluster is operational"
    else
        print_warning "Redis cluster may not be fully operational"
    fi
    
    # Check PostgreSQL
    if docker exec cabinet-postgres-primary pg_isready -U postgres; then
        print_status "PostgreSQL primary is ready"
    else
        print_warning "PostgreSQL primary is not ready"
    fi
    
    # Check PgBouncer
    if docker exec cabinet-pgbouncer psql -h localhost -p 5432 -U postgres -d pgbouncer -c "SHOW POOLS;" &> /dev/null; then
        print_status "PgBouncer is operational"
    else
        print_warning "PgBouncer may not be operational"
    fi
}

# Main execution
main() {
    echo -e "${BLUE}Starting Cabinet Insight Pro scalability setup...${NC}"
    echo ""
    
    check_prerequisites
    create_directories
    generate_ssl_certificates
    create_environment_file
    create_monitoring_config
    create_load_testing_config
    install_dependencies
    build_docker_images
    initialize_infrastructure
    validate_setup
    
    echo ""
    echo -e "${GREEN}🎉 Scalability infrastructure setup completed!${NC}"
    echo ""
    echo -e "${BLUE}Next steps:${NC}"
    echo "1. Update Azure OpenAI configuration in $INFRASTRUCTURE_DIR/.env"
    echo "2. Start the full infrastructure: npm run scalability:start"
    echo "3. Monitor the setup: npm run scalability:status"
    echo "4. Run load tests: npm run load:test"
    echo "5. Access Grafana dashboard: http://localhost:3000 (admin/admin)"
    echo ""
    echo -e "${YELLOW}For production deployment, review and update security settings!${NC}"
}

# Run main function
main "$@"
