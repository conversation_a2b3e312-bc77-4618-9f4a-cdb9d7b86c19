# PostgreSQL Primary Configuration for Cabinet Insight Pro Scalability
# Optimized for high-performance primary-replica setup

# Connection Settings
listen_addresses = '*'
port = 5432
max_connections = 200
superuser_reserved_connections = 3

# Memory Settings
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# WAL (Write-Ahead Logging) Settings for Replication
wal_level = replica
max_wal_senders = 10
max_replication_slots = 10
wal_keep_size = 1GB
synchronous_commit = off
archive_mode = on
archive_command = 'test ! -f /var/lib/postgresql/archive/%f && cp %p /var/lib/postgresql/archive/%f'

# Checkpoint Settings
checkpoint_timeout = 5min
checkpoint_completion_target = 0.7
max_wal_size = 1GB
min_wal_size = 80MB

# Query Planner Settings
random_page_cost = 1.1
effective_io_concurrency = 200

# Logging Settings
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 10MB
log_min_duration_statement = 1000
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 10MB

# Performance Monitoring
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all

# Autovacuum Settings
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.2
autovacuum_analyze_scale_factor = 0.1

# Lock Management
deadlock_timeout = 1s
lock_timeout = 30s

# Background Writer
bgwriter_delay = 200ms
bgwriter_lru_maxpages = 100
bgwriter_lru_multiplier = 2.0

# Parallel Query Settings
max_parallel_workers_per_gather = 2
max_parallel_workers = 8
max_parallel_maintenance_workers = 2

# Hot Standby Settings (for replicas)
hot_standby = on
hot_standby_feedback = on
wal_receiver_timeout = 60s

# Security Settings
ssl = off
password_encryption = scram-sha-256

# Locale Settings
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'
default_text_search_config = 'pg_catalog.english'

# Timezone
timezone = 'UTC'

# Shared Preload Libraries
shared_preload_libraries = 'pg_stat_statements'

# Statement Statistics
pg_stat_statements.max = 10000
pg_stat_statements.track = all
