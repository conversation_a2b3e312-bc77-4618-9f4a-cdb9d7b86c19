#!/bin/bash
# PostgreSQL Replica Setup Script for Cabinet Insight Pro Scalability

set -e

echo "Setting up PostgreSQL replica server..."

# Wait for primary to be ready
echo "Waiting for primary server to be ready..."
until pg_isready -h "$POSTGRES_PRIMARY_HOST" -p "$POSTGRES_PRIMARY_PORT" -U "$POSTGRES_REPLICATION_USER"; do
    echo "Primary server not ready, waiting..."
    sleep 5
done

echo "Primary server is ready, starting replica setup..."

# Stop PostgreSQL if running
pg_ctl stop -D "$PGDATA" -m fast || true

# Remove existing data directory
rm -rf "$PGDATA"/*

# Create base backup from primary
echo "Creating base backup from primary..."
PGPASSWORD="$POSTGRES_REPLICATION_PASSWORD" pg_basebackup \
    -h "$POSTGRES_PRIMARY_HOST" \
    -p "$POSTGRES_PRIMARY_PORT" \
    -U "$POSTGRES_REPLICATION_USER" \
    -D "$PGDATA" \
    -P \
    -W \
    -R \
    -X stream

# Create recovery configuration
cat > "$PGDATA/postgresql.auto.conf" <<EOF
# Replica-specific configuration
primary_conninfo = 'host=$POSTGRES_PRIMARY_HOST port=$POSTGRES_PRIMARY_PORT user=$POSTGRES_REPLICATION_USER password=$POSTGRES_REPLICATION_PASSWORD'
primary_slot_name = 'replica_slot_$(hostname)'
hot_standby = on
hot_standby_feedback = on
wal_receiver_timeout = 60s
max_standby_streaming_delay = 30s
max_standby_archive_delay = 30s
EOF

# Create standby.signal file to indicate this is a replica
touch "$PGDATA/standby.signal"

# Set proper permissions
chown -R postgres:postgres "$PGDATA"
chmod 700 "$PGDATA"

echo "PostgreSQL replica setup completed successfully!"
echo "Primary host: $POSTGRES_PRIMARY_HOST:$POSTGRES_PRIMARY_PORT"
echo "Replication user: $POSTGRES_REPLICATION_USER"
echo "Replica is ready to start"
