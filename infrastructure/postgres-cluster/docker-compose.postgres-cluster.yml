version: '3.8'

# PostgreSQL Cluster Configuration for Cabinet Insight Pro Scalability
# Provides primary-replica setup with read scaling capabilities

services:
  postgres-primary:
    image: postgres:15
    container_name: cabinet-postgres-primary
    environment:
      POSTGRES_DB: cabinet_insight_pro
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-cabinet_insight_secure_2024}
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: ${REPLICATION_PASSWORD:-replicator_secure_2024}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres-primary-data:/var/lib/postgresql/data
      - ./postgres-primary.conf:/etc/postgresql/postgresql.conf
      - ./setup-primary.sh:/docker-entrypoint-initdb.d/setup-primary.sh
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    ports:
      - "5432:5432"
    networks:
      - postgres-cluster
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d cabinet_insight_pro"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped

  postgres-replica-1:
    image: postgres:15
    container_name: cabinet-postgres-replica-1
    environment:
      PGUSER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-cabinet_insight_secure_2024}
      POSTGRES_PRIMARY_HOST: postgres-primary
      POSTGRES_PRIMARY_PORT: 5432
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: ${REPLICATION_PASSWORD:-replicator_secure_2024}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres-replica-1-data:/var/lib/postgresql/data
      - ./setup-replica.sh:/docker-entrypoint-initdb.d/setup-replica.sh
    ports:
      - "5433:5432"
    depends_on:
      postgres-primary:
        condition: service_healthy
    networks:
      - postgres-cluster
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 60s
    restart: unless-stopped

  postgres-replica-2:
    image: postgres:15
    container_name: cabinet-postgres-replica-2
    environment:
      PGUSER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-cabinet_insight_secure_2024}
      POSTGRES_PRIMARY_HOST: postgres-primary
      POSTGRES_PRIMARY_PORT: 5432
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: ${REPLICATION_PASSWORD:-replicator_secure_2024}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres-replica-2-data:/var/lib/postgresql/data
      - ./setup-replica.sh:/docker-entrypoint-initdb.d/setup-replica.sh
    ports:
      - "5434:5432"
    depends_on:
      postgres-primary:
        condition: service_healthy
    networks:
      - postgres-cluster
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 60s
    restart: unless-stopped

  # PgBouncer Connection Pooler
  pgbouncer:
    image: pgbouncer/pgbouncer:latest
    container_name: cabinet-pgbouncer
    environment:
      DATABASES_HOST: postgres-primary
      DATABASES_PORT: 5432
      DATABASES_USER: postgres
      DATABASES_PASSWORD: ${POSTGRES_PASSWORD:-cabinet_insight_secure_2024}
      DATABASES_DBNAME: cabinet_insight_pro
      POOL_MODE: transaction
      SERVER_RESET_QUERY: DISCARD ALL
      MAX_CLIENT_CONN: 1000
      DEFAULT_POOL_SIZE: 25
      MIN_POOL_SIZE: 5
      RESERVE_POOL_SIZE: 5
      SERVER_LIFETIME: 3600
      SERVER_IDLE_TIMEOUT: 600
    ports:
      - "6432:5432"
    depends_on:
      postgres-primary:
        condition: service_healthy
    networks:
      - postgres-cluster
    healthcheck:
      test: ["CMD", "psql", "-h", "localhost", "-p", "5432", "-U", "postgres", "-d", "pgbouncer", "-c", "SHOW POOLS;"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    restart: unless-stopped

volumes:
  postgres-primary-data:
    driver: local
  postgres-replica-1-data:
    driver: local
  postgres-replica-2-data:
    driver: local

networks:
  postgres-cluster:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
