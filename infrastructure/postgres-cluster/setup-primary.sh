#!/bin/bash
# PostgreSQL Primary Setup Script for Cabinet Insight Pro Scalability

set -e

echo "Setting up PostgreSQL primary server for replication..."

# Create replication user
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Create replication user
    CREATE USER $POSTGRES_REPLICATION_USER WITH REPLICATION ENCRYPTED PASSWORD '$POSTGRES_REPLICATION_PASSWORD';
    
    -- Grant necessary permissions
    GRANT CONNECT ON DATABASE $POSTGRES_DB TO $POSTGRES_REPLICATION_USER;
    
    -- Create pg_stat_statements extension
    CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
    
    -- Create performance monitoring views
    CREATE OR REPLACE VIEW performance_stats AS
    SELECT 
        query,
        calls,
        total_time,
        mean_time,
        rows,
        100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
    FROM pg_stat_statements 
    ORDER BY total_time DESC;
    
    -- Create replication monitoring view
    CREATE OR REPLACE VIEW replication_status AS
    SELECT 
        client_addr,
        client_hostname,
        client_port,
        state,
        sent_lsn,
        write_lsn,
        flush_lsn,
        replay_lsn,
        write_lag,
        flush_lag,
        replay_lag,
        sync_state,
        sync_priority
    FROM pg_stat_replication;
    
    -- Create database size monitoring view
    CREATE OR REPLACE VIEW database_sizes AS
    SELECT 
        datname,
        pg_size_pretty(pg_database_size(datname)) as size,
        pg_database_size(datname) as size_bytes
    FROM pg_database 
    WHERE datistemplate = false
    ORDER BY pg_database_size(datname) DESC;
    
    -- Create table size monitoring view
    CREATE OR REPLACE VIEW table_sizes AS
    SELECT 
        schemaname,
        tablename,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
        pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
    FROM pg_tables 
    WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
    
    -- Create connection monitoring view
    CREATE OR REPLACE VIEW connection_stats AS
    SELECT 
        datname,
        usename,
        client_addr,
        state,
        query_start,
        state_change,
        query
    FROM pg_stat_activity 
    WHERE state IS NOT NULL
    ORDER BY query_start DESC;
    
    -- Create index usage monitoring view
    CREATE OR REPLACE VIEW index_usage AS
    SELECT 
        schemaname,
        tablename,
        indexname,
        idx_tup_read,
        idx_tup_fetch,
        idx_scan
    FROM pg_stat_user_indexes
    ORDER BY idx_scan DESC;
    
    -- Create slow query monitoring view
    CREATE OR REPLACE VIEW slow_queries AS
    SELECT 
        query,
        calls,
        total_time,
        mean_time,
        stddev_time,
        rows,
        100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
    FROM pg_stat_statements 
    WHERE mean_time > 1000  -- Queries taking more than 1 second on average
    ORDER BY mean_time DESC;
EOSQL

# Create archive directory
mkdir -p /var/lib/postgresql/archive
chown postgres:postgres /var/lib/postgresql/archive

# Update pg_hba.conf for replication
echo "# Replication connections" >> "$PGDATA/pg_hba.conf"
echo "host replication $POSTGRES_REPLICATION_USER 0.0.0.0/0 md5" >> "$PGDATA/pg_hba.conf"
echo "host all all 0.0.0.0/0 md5" >> "$PGDATA/pg_hba.conf"

echo "PostgreSQL primary server setup completed successfully!"
echo "Replication user: $POSTGRES_REPLICATION_USER"
echo "Archive directory: /var/lib/postgresql/archive"
echo "Performance monitoring views created"
