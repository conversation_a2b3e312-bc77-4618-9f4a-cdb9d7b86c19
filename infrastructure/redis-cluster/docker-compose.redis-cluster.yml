version: '3.8'

# Redis Cluster Configuration for Cabinet Insight Pro Scalability
# Provides distributed caching with high availability and automatic failover

services:
  redis-node-1:
    image: redis:7-alpine
    container_name: cabinet-redis-node-1
    command: redis-server --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --port 7001 --bind 0.0.0.0 --protected-mode no
    ports:
      - "7001:7001"
      - "17001:17001"  # Cluster bus port
    volumes:
      - redis-1-data:/data
      - redis-1-conf:/usr/local/etc/redis
    networks:
      - redis-cluster
    environment:
      - REDIS_CLUSTER_ANNOUNCE_IP=redis-node-1
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "7001", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    restart: unless-stopped

  redis-node-2:
    image: redis:7-alpine
    container_name: cabinet-redis-node-2
    command: redis-server --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --port 7002 --bind 0.0.0.0 --protected-mode no
    ports:
      - "7002:7002"
      - "17002:17002"  # Cluster bus port
    volumes:
      - redis-2-data:/data
      - redis-2-conf:/usr/local/etc/redis
    networks:
      - redis-cluster
    environment:
      - REDIS_CLUSTER_ANNOUNCE_IP=redis-node-2
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "7002", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    restart: unless-stopped

  redis-node-3:
    image: redis:7-alpine
    container_name: cabinet-redis-node-3
    command: redis-server --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --port 7003 --bind 0.0.0.0 --protected-mode no
    ports:
      - "7003:7003"
      - "17003:17003"  # Cluster bus port
    volumes:
      - redis-3-data:/data
      - redis-3-conf:/usr/local/etc/redis
    networks:
      - redis-cluster
    environment:
      - REDIS_CLUSTER_ANNOUNCE_IP=redis-node-3
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "7003", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    restart: unless-stopped

  # Redis Cluster Initialization Service
  redis-cluster-init:
    image: redis:7-alpine
    container_name: cabinet-redis-cluster-init
    depends_on:
      redis-node-1:
        condition: service_healthy
      redis-node-2:
        condition: service_healthy
      redis-node-3:
        condition: service_healthy
    networks:
      - redis-cluster
    command: >
      sh -c "
        echo 'Waiting for Redis nodes to be ready...' &&
        sleep 10 &&
        echo 'Creating Redis cluster...' &&
        redis-cli --cluster create 
          redis-node-1:7001 
          redis-node-2:7002 
          redis-node-3:7003 
          --cluster-replicas 0 
          --cluster-yes &&
        echo 'Redis cluster created successfully!' &&
        echo 'Cluster status:' &&
        redis-cli -h redis-node-1 -p 7001 cluster nodes
      "
    restart: "no"

volumes:
  redis-1-data:
    driver: local
  redis-1-conf:
    driver: local
  redis-2-data:
    driver: local
  redis-2-conf:
    driver: local
  redis-3-data:
    driver: local
  redis-3-conf:
    driver: local

networks:
  redis-cluster:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
