/**
 * Artillery Load Test Processor for Cabinet Insight Pro
 * Provides custom functions and metrics collection for scalability testing
 */

const fs = require('fs');
const path = require('path');

// Test data and configuration
const testFiles = [
  'test-kitchen-1.pdf',
  'test-kitchen-2.pdf', 
  'test-kitchen-3.pdf',
  'test-kitchen-4.pdf'
];

const analysisTypes = ['basic', 'standard', 'premium'];

const userEmails = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
];

// Custom metrics tracking
let customMetrics = {
  analysisRequests: 0,
  cacheHits: 0,
  cacheMisses: 0,
  totalProcessingTime: 0,
  successfulAnalyses: 0,
  failedAnalyses: 0,
  websocketConnections: 0,
  authenticationAttempts: 0,
  successfulAuthentications: 0
};

/**
 * Generate random string for unique identifiers
 */
function randomString(length = 8) {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Generate random integer between min and max
 */
function randomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Pick random item from array
 */
function pick(array) {
  return array[Math.floor(Math.random() * array.length)];
}

/**
 * Generate timestamp
 */
function timestamp() {
  return new Date().toISOString();
}

/**
 * Generate random test file name
 */
function randomTestFile() {
  return pick(testFiles);
}

/**
 * Generate random analysis type
 */
function randomAnalysisType() {
  return pick(analysisTypes);
}

/**
 * Generate random user email
 */
function randomUserEmail() {
  return pick(userEmails);
}

/**
 * Create test user data
 */
function createTestUser() {
  return {
    email: `loadtest_${randomString()}@example.com`,
    password: 'testpassword123',
    name: `Load Test User ${randomInt(1, 1000)}`
  };
}

/**
 * Before request hook - set up request data
 */
function beforeRequest(requestParams, context, ee, next) {
  // Add custom headers
  requestParams.headers = requestParams.headers || {};
  requestParams.headers['X-Load-Test'] = 'true';
  requestParams.headers['X-Test-Session'] = context.vars.testSession || randomString();
  
  // Track request metrics
  if (requestParams.url && requestParams.url.includes('/analysis/')) {
    customMetrics.analysisRequests++;
  }
  
  if (requestParams.url && requestParams.url.includes('/auth/')) {
    customMetrics.authenticationAttempts++;
  }
  
  return next();
}

/**
 * After response hook - collect metrics
 */
function afterResponse(requestParams, response, context, ee, next) {
  // Track response metrics
  if (response.statusCode >= 200 && response.statusCode < 300) {
    if (requestParams.url && requestParams.url.includes('/analysis/')) {
      customMetrics.successfulAnalyses++;
      
      // Track processing time if available
      if (response.body && typeof response.body === 'object') {
        const processingTime = response.body.processingTime;
        if (processingTime) {
          customMetrics.totalProcessingTime += processingTime;
        }
      }
    }
    
    if (requestParams.url && requestParams.url.includes('/auth/login')) {
      customMetrics.successfulAuthentications++;
    }
  } else {
    if (requestParams.url && requestParams.url.includes('/analysis/')) {
      customMetrics.failedAnalyses++;
    }
  }
  
  // Track cache metrics from headers
  if (response.headers && response.headers['x-cache-status']) {
    if (response.headers['x-cache-status'] === 'hit') {
      customMetrics.cacheHits++;
    } else {
      customMetrics.cacheMisses++;
    }
  }
  
  return next();
}

/**
 * WebSocket connection handler
 */
function onWebSocketConnection(ws, context, ee, next) {
  customMetrics.websocketConnections++;
  
  // Set up WebSocket event handlers
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data);
      
      // Track different message types
      if (message.type === 'analysis_complete') {
        customMetrics.successfulAnalyses++;
      }
      
      if (message.type === 'analysis_error') {
        customMetrics.failedAnalyses++;
      }
      
    } catch (error) {
      // Ignore JSON parse errors
    }
  });
  
  return next();
}

/**
 * Custom scenario for file upload testing
 */
function uploadTestFile(context, events, done) {
  const fileName = randomTestFile();
  const analysisType = randomAnalysisType();
  
  // Simulate file upload
  context.vars.uploadFileName = fileName;
  context.vars.uploadAnalysisType = analysisType;
  context.vars.uploadTimestamp = Date.now();
  
  return done();
}

/**
 * Custom scenario for performance validation
 */
function validatePerformance(context, events, done) {
  const startTime = context.vars.uploadTimestamp;
  const endTime = Date.now();
  const processingTime = endTime - startTime;
  
  // Emit custom metrics
  events.emit('customStat', 'analysis_processing_time', processingTime);
  
  // Validate performance thresholds
  if (processingTime > 30000) { // 30 seconds
    events.emit('customStat', 'slow_analysis', 1);
  }
  
  return done();
}

/**
 * Generate load test report
 */
function generateReport() {
  const report = {
    timestamp: new Date().toISOString(),
    metrics: customMetrics,
    calculated: {
      cacheHitRate: customMetrics.cacheHits / (customMetrics.cacheHits + customMetrics.cacheMisses) * 100,
      averageProcessingTime: customMetrics.totalProcessingTime / customMetrics.successfulAnalyses,
      successRate: customMetrics.successfulAnalyses / customMetrics.analysisRequests * 100,
      authSuccessRate: customMetrics.successfulAuthentications / customMetrics.authenticationAttempts * 100
    }
  };
  
  // Write report to file
  const reportPath = path.join(__dirname, 'load-test-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log('\n📊 Load Test Report Generated:');
  console.log(`Cache Hit Rate: ${report.calculated.cacheHitRate.toFixed(2)}%`);
  console.log(`Average Processing Time: ${report.calculated.averageProcessingTime.toFixed(2)}ms`);
  console.log(`Analysis Success Rate: ${report.calculated.successRate.toFixed(2)}%`);
  console.log(`Authentication Success Rate: ${report.calculated.authSuccessRate.toFixed(2)}%`);
  console.log(`Report saved to: ${reportPath}`);
}

/**
 * Test completion handler
 */
function onTestComplete(context, events, done) {
  generateReport();
  return done();
}

/**
 * Error handler
 */
function onError(error, context, events, done) {
  console.error('Load test error:', error);
  return done();
}

// Export functions for Artillery
module.exports = {
  // Utility functions
  randomString,
  randomInt,
  pick,
  timestamp,
  randomTestFile,
  randomAnalysisType,
  randomUserEmail,
  createTestUser,
  
  // Hook functions
  beforeRequest,
  afterResponse,
  onWebSocketConnection,
  
  // Custom scenarios
  uploadTestFile,
  validatePerformance,
  
  // Event handlers
  onTestComplete,
  onError,
  
  // Custom metrics
  getMetrics: () => customMetrics,
  resetMetrics: () => {
    customMetrics = {
      analysisRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      totalProcessingTime: 0,
      successfulAnalyses: 0,
      failedAnalyses: 0,
      websocketConnections: 0,
      authenticationAttempts: 0,
      successfulAuthentications: 0
    };
  }
};
