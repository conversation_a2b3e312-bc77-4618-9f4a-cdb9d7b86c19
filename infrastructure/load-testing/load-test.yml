config:
  target: 'http://localhost'
  phases:
    # Warm-up phase
    - duration: 60
      arrivalRate: 5
      name: "Warm up - 5 users/sec"
    
    # Gradual ramp-up
    - duration: 120
      arrivalRate: 25
      name: "Ramp up - 25 users/sec"
    
    # Sustained load testing
    - duration: 300
      arrivalRate: 50
      name: "Sustained load - 50 users/sec"
    
    # Peak load testing
    - duration: 180
      arrivalRate: 100
      name: "Peak load - 100 users/sec"
    
    # Stress testing
    - duration: 120
      arrivalRate: 200
      name: "Stress test - 200 users/sec"
    
    # Cool down
    - duration: 60
      arrivalRate: 10
      name: "Cool down - 10 users/sec"

  processor: "./load-test-processor.js"
  
  # Performance thresholds
  ensure:
    maxErrorRate: 5  # Max 5% error rate
    maxResponseTime: 5000  # Max 5 second response time
    
  # HTTP configuration
  http:
    timeout: 30
    pool: 50  # Connection pool size
    
  # Metrics collection
  statsInterval: 10
  
  # Variables for dynamic testing
  variables:
    testFiles:
      - "test-kitchen-1.pdf"
      - "test-kitchen-2.pdf"
      - "test-kitchen-3.pdf"
    analysisTypes:
      - "basic"
      - "standard"
      - "premium"

scenarios:
  # Health check scenario (10% of traffic)
  - name: "Health Check"
    weight: 10
    flow:
      - get:
          url: "/api/health"
          expect:
            - statusCode: 200
            - hasHeader: "content-type"
          capture:
            - json: "$.status"
              as: "healthStatus"

  # API analysis scenario (60% of traffic)
  - name: "PDF Analysis"
    weight: 60
    flow:
      # Upload PDF for analysis
      - post:
          url: "/api/analysis/upload"
          formData:
            file: "@../PDFs/{{ $randomString() }}.pdf"
            analysisType: "{{ $pick(analysisTypes) }}"
          expect:
            - statusCode: [200, 202]
          capture:
            - json: "$.analysisId"
              as: "analysisId"
      
      # Wait for processing
      - think: 3
      
      # Check analysis status
      - get:
          url: "/api/analysis/{{ analysisId }}/status"
          expect:
            - statusCode: 200
      
      # Get analysis results (if completed)
      - get:
          url: "/api/analysis/{{ analysisId }}/results"
          expect:
            - statusCode: [200, 202]

  # Real-time WebSocket scenario (15% of traffic)
  - name: "WebSocket Connection"
    weight: 15
    engine: ws
    flow:
      - connect:
          url: "ws://localhost/socket.io/?EIO=4&transport=websocket"
      
      # Send analysis request via WebSocket
      - send:
          payload: |
            {
              "type": "analysis_request",
              "data": {
                "analysisType": "{{ $pick(analysisTypes) }}",
                "priority": "normal"
              }
            }
      
      # Wait for response
      - think: 5
      
      # Send status check
      - send:
          payload: |
            {
              "type": "status_check",
              "timestamp": "{{ $timestamp() }}"
            }

  # Performance metrics scenario (10% of traffic)
  - name: "Performance Monitoring"
    weight: 10
    flow:
      # Get system metrics
      - get:
          url: "/api/performance/overview"
          expect:
            - statusCode: 200
      
      # Get cache statistics
      - get:
          url: "/api/performance/cache"
          expect:
            - statusCode: 200
      
      # Get queue statistics
      - get:
          url: "/api/performance/queue"
          expect:
            - statusCode: 200

  # User authentication scenario (5% of traffic)
  - name: "User Authentication"
    weight: 5
    flow:
      # Login
      - post:
          url: "/api/auth/login"
          json:
            email: "test{{ $randomInt(1, 1000) }}@example.com"
            password: "testpassword123"
          expect:
            - statusCode: [200, 401]
          capture:
            - json: "$.token"
              as: "authToken"
      
      # Get user profile (if authenticated)
      - get:
          url: "/api/user/profile"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: [200, 401]

# Custom functions for load testing
functions:
  # Generate random file names
  randomFileName: |
    function() {
      const files = ['kitchen-1.pdf', 'kitchen-2.pdf', 'kitchen-3.pdf', 'kitchen-4.pdf'];
      return files[Math.floor(Math.random() * files.length)];
    }
  
  # Generate random analysis types
  randomAnalysisType: |
    function() {
      const types = ['basic', 'standard', 'premium'];
      return types[Math.floor(Math.random() * types.length)];
    }
  
  # Generate timestamp
  timestamp: |
    function() {
      return new Date().toISOString();
    }

# Plugins for enhanced reporting
plugins:
  expect: {}
  metrics-by-endpoint: {}
  
# Custom metrics
metrics:
  - name: "analysis_processing_time"
    unit: "ms"
  - name: "cache_hit_rate"
    unit: "percent"
  - name: "queue_depth"
    unit: "count"
