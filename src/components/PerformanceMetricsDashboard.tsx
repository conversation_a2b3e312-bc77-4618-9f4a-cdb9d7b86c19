import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  BarChart3,
  TrendingUp,
  DollarSign,
  Clock,
  AlertTriangle,
  Download,
  RefreshCw,
  Settings,
  Activity,
  Zap,
  Target,
  Brain,
  Database,
  Cpu,
  Network,
  Shield,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart,
  Radial<PERSON>ar<PERSON><PERSON>,
  RadialBar
} from 'recharts';
import { Socket } from 'socket.io-client';
import { useRealtimeDashboard } from '../hooks/useRealtimeDashboard';
import { ConnectionStatusIndicator, DashboardStatusBadge } from './ConnectionStatusIndicator';

interface ModelPerformanceMetrics {
  modelName: 'GPT4O' | 'GPT4O_MINI' | 'GPTO1';
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  averageTokensUsed: number;
  totalCost: number;
  averageConfidence: number;
  peakUsageTime: string;
  errorRate: number;
  lastUsed: string;
}

interface PerformanceAlert {
  id: string;
  type: 'RESPONSE_TIME' | 'ERROR_RATE' | 'COST_THRESHOLD' | 'USAGE_SPIKE';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  message: string;
  currentValue: number;
  threshold: number;
  timestamp: string;
  acknowledged: boolean;
}

interface UsagePattern {
  hour: number;
  day: string;
  requests: number;
  averageResponseTime: number;
  modelDistribution: Record<string, number>;
}

interface CostAnalysis {
  dailyCost: number;
  weeklyCost: number;
  monthlyCost: number;
  projectedMonthlyCost: number;
  costByModel: Record<string, number>;
  costTrend: 'INCREASING' | 'DECREASING' | 'STABLE';
  optimizationSuggestions: string[];
}

interface PerformanceOverview {
  modelComparison: {
    timeRange: string;
    models: ModelPerformanceMetrics[];
    totalRequests: number;
    totalCost: number;
    mostEfficientModel: string;
    costOptimizationOpportunities: string[];
  };
  usagePatterns: UsagePattern[];
  costAnalysis: CostAnalysis;
  activeAlerts: PerformanceAlert[];
  summary: {
    totalRequests: number;
    totalCost: number;
    mostEfficientModel: string;
    alertCount: number;
    criticalAlerts: number;
  };
}

// Enhanced interfaces for GPT-o1 Integration Enhancement
interface GPTO1Analytics {
  reasoningChainPerformance: {
    averageStepsPerChain: number;
    averageStepCompletionTime: number;
    complexityDistribution: { low: number; medium: number; high: number };
    stepSuccessRate: number;
    chainCompletionRate: number;
    recentChains: Array<{
      id: string;
      analysisType: string;
      steps: number;
      completionTime: number;
      complexity: string;
      accuracy: number;
    }>;
  };
  complexityAnalysis: {
    averageComplexityScore: number;
    complexityTrends: Array<{ time: string; value: number }>;
    factorsInfluencingComplexity: Array<{ factor: string; impact: number }>;
  };
  tokenUsageOptimization: {
    averageTokensPerAnalysis: number;
    tokenEfficiencyScore: number;
    costPerAnalysis: number;
    optimizationRecommendations: string[];
  };
  accuracyMetrics: {
    overallAccuracy: number;
    accuracyByComplexity: { low: number; medium: number; high: number };
    confidenceDistribution: Array<{ range: string; count: number }>;
    accuracyTrends: Array<{ time: string; value: number }>;
  };
}

interface CachingEfficiency {
  semanticSimilarity: {
    hitRate: number;
    semanticHitRate: number;
    exactHitRate: number;
    averageSimilarityScore: number;
    embeddingGenerationTime: number;
    similarityCalculationTime: number;
    similarityDistribution: Array<{ range: string; count: number }>;
  };
  apiCallReduction: {
    totalRequests: number;
    cacheHits: number;
    reductionPercentage: number;
    targetReduction: number;
    trend: Array<{ time: string; value: number }>;
    projectedMonthlySavings: number;
  };
  cacheWarming: {
    warmupEffectiveness: number;
    preGeneratedEmbeddings: number;
    warmupPatterns: string[];
    lastWarmupTime: string;
    nextScheduledWarmup: string;
  };
  costSavings: {
    totalSaved: number;
    tokensAvoided: number;
    monthlySavingsProjection: number;
    savingsBreakdown: Record<string, number>;
  };
}

interface RealTimeMonitoring {
  systemHealth: {
    overallStatus: 'operational' | 'degraded' | 'down';
    uptime: number;
    memoryUsage: any;
    cpuUsage: number;
    activeConnections: number;
    lastHealthCheck: string;
  };
  endpointStatus: Record<string, {
    status: string;
    responseTime: number;
    successRate: number;
    lastCheck: string;
    models: string[];
  }>;
  performanceAlerts: Array<{
    id: string;
    type: string;
    severity: string;
    message: string;
    threshold: number;
    currentValue: number;
    timestamp: string;
  }>;
  liveMetrics: {
    requestsPerMinute: number;
    averageResponseTime: number;
    errorRate: number;
    cacheHitRate: number;
    costPerHour: number;
    timestamp: string;
  };
}

interface DashboardMetrics {
  overview: PerformanceOverview;
  gptO1Analytics: GPTO1Analytics;
  cachingEfficiency: CachingEfficiency;
  modelComparison: any;
  realTimeMonitoring: RealTimeMonitoring;
}

interface PerformanceMetricsDashboardProps {
  className?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

/**
 * Performance Metrics Dashboard for GPT-o1 Integration Enhancement
 * Comprehensive analytics dashboard with GPT-o1 analytics, caching efficiency, and real-time monitoring
 */
export const PerformanceMetricsDashboard: React.FC<PerformanceMetricsDashboardProps> = ({
  className = '',
  autoRefresh = true,
  refreshInterval = 30000 // 30 seconds
}) => {
  const [overview, setOverview] = useState<PerformanceOverview | null>(null);
  const [dashboardMetrics, setDashboardMetrics] = useState<DashboardMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('24h');
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const socketRef = useRef<Socket | null>(null);

  // Real-time dashboard WebSocket connection
  const realtimeDashboard = useRealtimeDashboard({
    dashboardType: 'performance',
    maxReconnectAttempts: 3,
    reconnectIntervals: [1000, 2000, 4000],
    dataHistoryLimit: 100
  });

  /**
   * Fetch comprehensive dashboard metrics
   */
  const fetchDashboardMetrics = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/performance/dashboard?timeRange=${timeRange}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch dashboard metrics: ${response.statusText}`);
      }

      const data = await response.json();
      if (data.success) {
        setDashboardMetrics(data.data);
        setOverview(data.data.overview); // Maintain backward compatibility
        setLastUpdated(new Date());
      } else {
        throw new Error(data.error || 'Failed to fetch dashboard metrics');
      }

    } catch (err) {
      console.error('Error fetching dashboard metrics:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, [timeRange]);

  /**
   * Fetch performance overview data (legacy support)
   */
  const fetchOverview = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/performance/overview?timeRange=${timeRange}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch performance overview: ${response.statusText}`);
      }

      const data = await response.json();
      if (data.success) {
        setOverview(data.data);
        setLastUpdated(new Date());
      } else {
        throw new Error(data.error || 'Failed to fetch performance overview');
      }

    } catch (err) {
      console.error('Error fetching performance overview:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, [timeRange]);

  /**
   * Initialize WebSocket connection for real-time updates
   */
  const initializeWebSocket = useCallback(async () => {
    try {
      const { io } = await import('socket.io-client');
      const socketUrl = import.meta.env.VITE_SOCKET_URL || 'http://localhost:3001';

      socketRef.current = io(socketUrl, {
        transports: ['websocket', 'polling'],
        timeout: 10000,
        retries: 3
      });

      const socket = socketRef.current;

      socket.on('connect', () => {
        console.log('Connected to performance dashboard WebSocket');
        socket.emit('join-performance-dashboard');
      });

      socket.on('disconnect', () => {
        console.log('Disconnected from performance dashboard WebSocket');
      });

      // Listen for real-time updates
      socket.on('gpt-o1-analytics-update', (data: any) => {
        console.log('GPT-o1 analytics update received:', data);
        if (dashboardMetrics) {
          setDashboardMetrics(prev => prev ? {
            ...prev,
            gptO1Analytics: { ...prev.gptO1Analytics, ...data }
          } : prev);
        }
      });

      socket.on('caching-efficiency-update', (data: any) => {
        console.log('Caching efficiency update received:', data);
        if (dashboardMetrics) {
          setDashboardMetrics(prev => prev ? {
            ...prev,
            cachingEfficiency: { ...prev.cachingEfficiency, ...data }
          } : prev);
        }
      });

      socket.on('model-performance-update', (data: any) => {
        console.log('Model performance update received:', data);
        // Update model performance in real-time
      });

      socket.on('performance-alert', (alert: any) => {
        console.log('Performance alert received:', alert);
        // Handle real-time alerts
      });

      socket.on('system-health-update', (health: any) => {
        console.log('System health update received:', health);
        if (dashboardMetrics) {
          setDashboardMetrics(prev => prev ? {
            ...prev,
            realTimeMonitoring: { ...prev.realTimeMonitoring, systemHealth: health }
          } : prev);
        }
      });

    } catch (error) {
      console.error('Failed to initialize WebSocket:', error);
    }
  }, [dashboardMetrics]);

  /**
   * Export metrics data
   */
  const handleExport = async (format: 'CSV' | 'JSON') => {
    try {
      const response = await fetch(`/api/performance/export?format=${format}&timeRange=${timeRange}`);
      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `performance-metrics-${timeRange}-${new Date().toISOString().split('T')[0]}.${format.toLowerCase()}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

    } catch (err) {
      console.error('Export failed:', err);
      setError(err instanceof Error ? err.message : 'Export failed');
    }
  };

  /**
   * Acknowledge an alert
   */
  const handleAcknowledgeAlert = async (alertId: string) => {
    try {
      const response = await fetch(`/api/performance/alerts/${alertId}/acknowledge`, {
        method: 'POST'
      });

      if (response.ok) {
        // Refresh data to update alert status
        await fetchOverview();
      }

    } catch (err) {
      console.error('Failed to acknowledge alert:', err);
    }
  };

  // Initial data fetch and WebSocket setup
  useEffect(() => {
    fetchDashboardMetrics();
    initializeWebSocket();

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, [fetchDashboardMetrics, initializeWebSocket]);

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchDashboardMetrics, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchDashboardMetrics]);

  // Model colors for charts
  const modelColors = {
    'GPT4O': 'hsl(var(--status-info))',      // Blue
    'GPTO1': 'hsl(var(--status-success))',      // Green  
    'GPT4O_MINI': 'hsl(var(--status-warning))'  // Amber
  };

  const getSeverityColor = (severity: PerformanceAlert['severity']) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-status-error';
      case 'HIGH': return 'bg-status-warning';
      case 'MEDIUM': return 'bg-status-warning';
      case 'LOW': return 'bg-status-info';
      default: return 'bg-gray-500';
    }
  };

  const getSeverityTextColor = (severity: PerformanceAlert['severity']) => {
    switch (severity) {
      case 'CRITICAL': return 'text-red-700';
      case 'HIGH': return 'text-orange-700';
      case 'MEDIUM': return 'text-yellow-700';
      case 'LOW': return 'text-aone-sage-dark';
      default: return 'text-aone-charcoal';
    }
  };

  if (loading && !overview) {
    return (
      <div className={`space-y-6 ${className}`} data-testid="performance-dashboard-loading">
        <div className="aone-flex-center py-12">
          <div className="flex items-center gap-3">
            <RefreshCw className="w-6 h-6 animate-spin text-status-info" />
            <span className="text-aone-lg font-aone-medium">Loading performance metrics...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`space-y-6 ${className}`} data-testid="performance-dashboard-error">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <AlertTriangle className="w-12 h-12 text-status-error mx-auto mb-aone-md" />
              <h3 className="text-aone-lg font-aone-medium text-foreground mb-2">Error Loading Dashboard</h3>
              <p className="text-aone-charcoal mb-aone-md">{error}</p>
              <Button onClick={fetchOverview}>
                <RefreshCw className="w-4 h-4 mr-2" />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!overview) {
    return null;
  }

  return (
    <div className={`space-y-6 ${className}`} data-testid="performance-dashboard">
      {/* Header */}
      <div className="aone-flex-between">
        <div>
          <div className="flex items-center gap-aone-md mb-2">
            <h1 className="text-aone-3xl font-aone-bold text-foreground">
              Performance Metrics Dashboard
            </h1>
            <DashboardStatusBadge
              status={realtimeDashboard.connectionStatus}
              lastUpdate={realtimeDashboard.lastUpdate}
            />
          </div>
          <p className="text-aone-charcoal">
            Comprehensive AI model performance analytics and real-time monitoring
          </p>
        </div>

        <div className="flex items-center gap-aone-md">
          {/* Connection Status Indicator */}
          <ConnectionStatusIndicator
            status={realtimeDashboard.connectionStatus}
            reconnectAttempts={realtimeDashboard.reconnectAttempts}
            maxReconnectAttempts={3}
            lastUpdate={realtimeDashboard.lastUpdate}
            onReconnect={realtimeDashboard.reconnect}
            className="hidden md:flex"
          />

          {/* Time Range Selector */}
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-aone-sage/20 rounded-aone-md aone-focus-ring aone-focus-ring"
            title="Select time range for metrics"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>

          {/* Export Buttons */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport('CSV')}
          >
            <Download className="w-4 h-4 mr-2" />
            CSV
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport('JSON')}
          >
            <Download className="w-4 h-4 mr-2" />
            JSON
          </Button>

          {/* Refresh Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={fetchOverview}
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Last Updated */}
      {lastUpdated && (
        <div className="text-aone-sm text-aone-soft-gray">
          Last updated: {lastUpdated.toLocaleString()}
        </div>
      )}

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-aone-lg">
        <Card>
          <CardContent className="p-aone-lg">
            <div className="aone-flex-between">
              <div>
                <p className="text-aone-sm font-aone-medium text-aone-charcoal">Total Requests</p>
                <p className="text-aone-2xl font-aone-bold text-foreground">
                  {overview?.summary?.totalRequests?.toLocaleString() || '0'}
                </p>
              </div>
              <div className="aone-spacing-sm bg-blue-100 rounded-aone-lg">
                <Activity className="w-6 h-6 text-status-info" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-aone-lg">
            <div className="aone-flex-between">
              <div>
                <p className="text-aone-sm font-aone-medium text-aone-charcoal">Total Cost</p>
                <p className="text-aone-2xl font-aone-bold text-foreground">
                  ${overview?.summary?.totalCost?.toFixed(2) || '0.00'}
                </p>
              </div>
              <div className="aone-spacing-sm bg-green-100 rounded-aone-lg">
                <DollarSign className="w-6 h-6 text-status-success" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-aone-lg">
            <div className="aone-flex-between">
              <div>
                <p className="text-aone-sm font-aone-medium text-aone-charcoal">Most Efficient</p>
                <p className="text-aone-2xl font-aone-bold text-foreground">
                  {overview?.summary?.mostEfficientModel || 'N/A'}
                </p>
              </div>
              <div className="aone-spacing-sm bg-purple-100 rounded-aone-lg">
                <Target className="w-6 h-6 text-preset-accent" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-aone-lg">
            <div className="aone-flex-between">
              <div>
                <p className="text-aone-sm font-aone-medium text-aone-charcoal">Active Alerts</p>
                <p className="text-aone-2xl font-aone-bold text-foreground">
                  {overview?.summary?.alertCount || 0}
                </p>
                {(overview?.summary?.criticalAlerts || 0) > 0 && (
                  <Badge variant="destructive" className="mt-1">
                    {overview?.summary?.criticalAlerts || 0} Critical
                  </Badge>
                )}
              </div>
              <div className="aone-spacing-sm bg-red-100 rounded-aone-lg">
                <AlertTriangle className="w-6 h-6 text-status-error" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Dashboard Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="gpt-o1" className="flex items-center gap-2">
            <Brain className="w-4 h-4" />
            GPT-o1 Analytics
          </TabsTrigger>
          <TabsTrigger value="caching" className="flex items-center gap-2">
            <Database className="w-4 h-4" />
            Caching Efficiency
          </TabsTrigger>
          <TabsTrigger value="models" className="flex items-center gap-2">
            <Cpu className="w-4 h-4" />
            Model Comparison
          </TabsTrigger>
          <TabsTrigger value="monitoring" className="flex items-center gap-2">
            <Activity className="w-4 h-4" />
            Real-time Monitoring
          </TabsTrigger>
          <TabsTrigger value="alerts" className="flex items-center gap-2">
            <AlertTriangle className="w-4 h-4" />
            Alerts ({overview?.summary?.alertCount || 0})
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-aone-lg">
            {/* Model Performance Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Model Performance Comparison
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    requests: {
                      label: "Requests",
                      color: "hsl(var(--chart-1))",
                    },
                    responseTime: {
                      label: "Response Time (ms)",
                      color: "hsl(var(--chart-2))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={overview?.modelComparison?.models || []}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="modelName" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Bar
                        dataKey="totalRequests"
                        fill={modelColors.GPT4O}
                        name="Total Requests"
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            {/* Usage Patterns Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  Usage Patterns (24h)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    requests: {
                      label: "Requests",
                      color: "hsl(var(--chart-3))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={overview?.usagePatterns || []}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Area
                        type="monotone"
                        dataKey="requests"
                        stroke="hsl(var(--status-info))"
                        fill="hsl(var(--status-info))"
                        fillOpacity={0.3}
                        name="Requests"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>

          {/* Cost Optimization Opportunities */}
          {(overview?.modelComparison?.costOptimizationOpportunities?.length || 0) > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5" />
                  Cost Optimization Opportunities
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {(overview?.modelComparison?.costOptimizationOpportunities || []).map((opportunity, index) => (
                    <div key={index} className="flex items-start gap-3 aone-spacing-sm bg-yellow-50 rounded-aone-lg">
                      <div className="p-1 bg-yellow-200 rounded">
                        <TrendingUp className="w-4 h-4 text-yellow-700" />
                      </div>
                      <p className="text-aone-sm text-yellow-800">{opportunity}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* GPT-o1 Analytics Tab */}
        <TabsContent value="gpt-o1" className="space-y-6">
          {dashboardMetrics?.gptO1Analytics ? (
            <>
              {/* Reasoning Chain Performance */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-aone-lg">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Brain className="w-5 h-5" />
                      Reasoning Chain Performance
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-aone-md">
                      <div>
                        <p className="text-aone-sm text-aone-charcoal">Avg Steps per Chain</p>
                        <p className="text-aone-2xl font-aone-bold">
                          {dashboardMetrics.gptO1Analytics.reasoningChainPerformance.averageStepsPerChain}
                        </p>
                      </div>
                      <div>
                        <p className="text-aone-sm text-aone-charcoal">Avg Step Time</p>
                        <p className="text-aone-2xl font-aone-bold">
                          {dashboardMetrics.gptO1Analytics.reasoningChainPerformance.averageStepCompletionTime}s
                        </p>
                      </div>
                      <div>
                        <p className="text-aone-sm text-aone-charcoal">Step Success Rate</p>
                        <p className="text-aone-2xl font-aone-bold">
                          {(dashboardMetrics.gptO1Analytics.reasoningChainPerformance.stepSuccessRate * 100).toFixed(1)}%
                        </p>
                      </div>
                      <div>
                        <p className="text-aone-sm text-aone-charcoal">Chain Completion</p>
                        <p className="text-aone-2xl font-aone-bold">
                          {(dashboardMetrics.gptO1Analytics.reasoningChainPerformance.chainCompletionRate * 100).toFixed(1)}%
                        </p>
                      </div>
                    </div>

                    {/* Complexity Distribution */}
                    <div>
                      <p className="text-aone-sm text-aone-charcoal mb-3">Complexity Distribution</p>
                      <div className="space-y-2">
                        {Object.entries(dashboardMetrics.gptO1Analytics.reasoningChainPerformance.complexityDistribution).map(([level, percentage]) => (
                          <div key={level} className="aone-flex-between">
                            <span className="text-aone-sm capitalize">{level}</span>
                            <div className="flex items-center gap-2">
                              <Progress value={percentage} className="w-20 h-2" />
                              <span className="text-aone-sm text-aone-soft-gray">{percentage}%</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Token Usage Optimization */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="w-5 h-5" />
                      Token Usage Optimization
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-aone-md">
                      <div>
                        <p className="text-aone-sm text-aone-charcoal">Avg Tokens/Analysis</p>
                        <p className="text-aone-2xl font-aone-bold">
                          {dashboardMetrics.gptO1Analytics.tokenUsageOptimization.averageTokensPerAnalysis.toLocaleString()}
                        </p>
                      </div>
                      <div>
                        <p className="text-aone-sm text-aone-charcoal">Efficiency Score</p>
                        <p className="text-aone-2xl font-aone-bold">
                          {(dashboardMetrics.gptO1Analytics.tokenUsageOptimization.tokenEfficiencyScore * 100).toFixed(1)}%
                        </p>
                      </div>
                      <div>
                        <p className="text-aone-sm text-aone-charcoal">Cost per Analysis</p>
                        <p className="text-aone-2xl font-aone-bold">
                          ${dashboardMetrics.gptO1Analytics.tokenUsageOptimization.costPerAnalysis.toFixed(3)}
                        </p>
                      </div>
                    </div>

                    {/* Optimization Recommendations */}
                    <div>
                      <p className="text-aone-sm text-aone-charcoal mb-3">Optimization Recommendations</p>
                      <div className="space-y-2">
                        {dashboardMetrics.gptO1Analytics.tokenUsageOptimization.optimizationRecommendations.map((rec, index) => (
                          <div key={index} className="flex items-start gap-2 aone-spacing-xs bg-blue-50 rounded">
                            <Target className="w-4 h-4 text-status-info mt-0.5" />
                            <span className="text-aone-sm text-blue-800">{rec}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Accuracy Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    Accuracy Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-aone-lg">
                    {/* Overall Accuracy */}
                    <div className="space-y-4">
                      <div>
                        <p className="text-aone-sm text-aone-charcoal mb-2">Overall Accuracy</p>
                        <div className="flex items-center gap-aone-md">
                          <Progress value={dashboardMetrics.gptO1Analytics.accuracyMetrics.overallAccuracy * 100} className="flex-1 h-3" />
                          <span className="text-aone-lg font-bold">
                            {(dashboardMetrics.gptO1Analytics.accuracyMetrics.overallAccuracy * 100).toFixed(1)}%
                          </span>
                        </div>
                      </div>

                      {/* Accuracy by Complexity */}
                      <div>
                        <p className="text-aone-sm text-aone-charcoal mb-3">Accuracy by Complexity</p>
                        <div className="space-y-2">
                          {Object.entries(dashboardMetrics.gptO1Analytics.accuracyMetrics.accuracyByComplexity).map(([level, accuracy]) => (
                            <div key={level} className="aone-flex-between">
                              <span className="text-aone-sm capitalize">{level}</span>
                              <div className="flex items-center gap-2">
                                <Progress value={accuracy * 100} className="w-20 h-2" />
                                <span className="text-aone-sm text-aone-soft-gray">{(accuracy * 100).toFixed(1)}%</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Confidence Distribution */}
                    <div>
                      <p className="text-aone-sm text-aone-charcoal mb-3">Confidence Distribution</p>
                      <ChartContainer
                        config={{
                          count: {
                            label: "Count",
                            color: "hsl(var(--chart-5))",
                          },
                        }}
                        className="h-[200px]"
                      >
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart data={dashboardMetrics.gptO1Analytics.accuracyMetrics.confidenceDistribution}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="range" />
                            <YAxis />
                            <ChartTooltip content={<ChartTooltipContent />} />
                            <Bar dataKey="count" fill="rgb(var(--preset-accent))" />
                          </BarChart>
                        </ResponsiveContainer>
                      </ChartContainer>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Recent Reasoning Chains */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="w-5 h-5" />
                    Recent Reasoning Chains
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {dashboardMetrics.gptO1Analytics.reasoningChainPerformance.recentChains.map((chain) => (
                      <div key={chain.id} className="aone-flex-between aone-spacing-md border rounded-aone-lg">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <Badge variant="outline">{chain.analysisType.replace('_', ' ')}</Badge>
                            <Badge variant={chain.complexity === 'high' ? 'destructive' : chain.complexity === 'medium' ? 'default' : 'secondary'}>
                              {chain.complexity}
                            </Badge>
                          </div>
                          <p className="text-aone-sm text-aone-charcoal">
                            {chain.steps} steps • {chain.completionTime}s • {(chain.accuracy * 100).toFixed(1)}% accuracy
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-aone-sm font-aone-medium">{chain.id}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-12">
                  <Brain className="w-12 h-12 text-gray-400 mx-auto mb-aone-md" />
                  <h3 className="text-aone-lg font-aone-medium text-foreground mb-2">GPT-o1 Analytics Loading</h3>
                  <p className="text-aone-charcoal">
                    GPT-o1 analytics data is being processed...
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Model Comparison Tab */}
        <TabsContent value="models" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-aone-lg">
            {(overview?.modelComparison?.models || []).map((model) => (
              <Card key={model.modelName}>
                <CardHeader>
                  <CardTitle className="aone-flex-between">
                    <span>{model.modelName}</span>
                    <Badge
                      variant={model.modelName === overview?.summary?.mostEfficientModel ? "default" : "secondary"}
                    >
                      {model.modelName === overview?.summary?.mostEfficientModel ? "Most Efficient" : "Active"}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-aone-md">
                    <div>
                      <p className="text-aone-sm text-aone-charcoal">Total Requests</p>
                      <p className="text-aone-lg font-aone-semibold">{model.totalRequests.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-aone-sm text-aone-charcoal">Success Rate</p>
                      <p className="text-aone-lg font-aone-semibold">
                        {((model.successfulRequests / model.totalRequests) * 100).toFixed(1)}%
                      </p>
                    </div>
                    <div>
                      <p className="text-aone-sm text-aone-charcoal">Avg Response Time</p>
                      <p className="text-aone-lg font-aone-semibold">{(model.averageResponseTime / 1000).toFixed(1)}s</p>
                    </div>
                    <div>
                      <p className="text-aone-sm text-aone-charcoal">Total Cost</p>
                      <p className="text-aone-lg font-aone-semibold">${model.totalCost.toFixed(2)}</p>
                    </div>
                  </div>

                  <div>
                    <p className="text-aone-sm text-aone-charcoal mb-2">Confidence Score</p>
                    <Progress value={model.averageConfidence * 100} className="h-2" />
                    <p className="text-xs text-aone-soft-gray mt-1">
                      {(model.averageConfidence * 100).toFixed(1)}%
                    </p>
                  </div>

                  <div className="text-xs text-aone-soft-gray">
                    Last used: {new Date(model.lastUsed).toLocaleString()}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Caching Efficiency Tab */}
        <TabsContent value="caching" className="space-y-6">
          {dashboardMetrics?.cachingEfficiency ? (
            <>
              {/* API Call Reduction Overview */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-aone-lg">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Database className="w-5 h-5" />
                      API Call Reduction
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="text-center">
                      <p className="text-aone-3xl font-aone-bold text-status-success">
                        {dashboardMetrics.cachingEfficiency.apiCallReduction.reductionPercentage}%
                      </p>
                      <p className="text-aone-sm text-aone-charcoal">Current Reduction</p>
                      <div className="mt-2">
                        <Progress
                          value={dashboardMetrics.cachingEfficiency.apiCallReduction.reductionPercentage}
                          className="h-3"
                        />
                      </div>
                      <p className="text-xs text-aone-soft-gray mt-1">
                        Target: {dashboardMetrics.cachingEfficiency.apiCallReduction.targetReduction}%
                      </p>
                    </div>

                    <div className="grid grid-cols-2 gap-aone-md text-center">
                      <div>
                        <p className="text-aone-lg font-aone-semibold">
                          {dashboardMetrics.cachingEfficiency.apiCallReduction.cacheHits.toLocaleString()}
                        </p>
                        <p className="text-xs text-aone-charcoal">Cache Hits</p>
                      </div>
                      <div>
                        <p className="text-aone-lg font-aone-semibold">
                          {dashboardMetrics.cachingEfficiency.apiCallReduction.totalRequests.toLocaleString()}
                        </p>
                        <p className="text-xs text-aone-charcoal">Total Requests</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="w-5 h-5" />
                      Semantic Similarity
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-aone-md">
                      <div>
                        <p className="text-aone-sm text-aone-charcoal">Hit Rate</p>
                        <p className="text-aone-xl font-bold">
                          {(dashboardMetrics.cachingEfficiency.semanticSimilarity.hitRate * 100).toFixed(1)}%
                        </p>
                      </div>
                      <div>
                        <p className="text-aone-sm text-aone-charcoal">Semantic Hits</p>
                        <p className="text-aone-xl font-bold">
                          {(dashboardMetrics.cachingEfficiency.semanticSimilarity.semanticHitRate * 100).toFixed(1)}%
                        </p>
                      </div>
                      <div>
                        <p className="text-aone-sm text-aone-charcoal">Avg Similarity</p>
                        <p className="text-aone-xl font-bold">
                          {(dashboardMetrics.cachingEfficiency.semanticSimilarity.averageSimilarityScore * 100).toFixed(1)}%
                        </p>
                      </div>
                      <div>
                        <p className="text-aone-sm text-aone-charcoal">Embedding Time</p>
                        <p className="text-aone-xl font-bold">
                          {dashboardMetrics.cachingEfficiency.semanticSimilarity.embeddingGenerationTime.toFixed(0)}ms
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <DollarSign className="w-5 h-5" />
                      Cost Savings
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <p className="text-aone-sm text-aone-charcoal">Total Saved</p>
                      <p className="text-aone-2xl font-aone-bold text-status-success">
                        ${dashboardMetrics.cachingEfficiency.costSavings.totalSaved.toFixed(2)}
                      </p>
                    </div>
                    <div>
                      <p className="text-aone-sm text-aone-charcoal">Monthly Projection</p>
                      <p className="text-aone-xl font-bold">
                        ${dashboardMetrics.cachingEfficiency.costSavings.monthlySavingsProjection.toFixed(2)}
                      </p>
                    </div>
                    <div>
                      <p className="text-aone-sm text-aone-charcoal">Tokens Avoided</p>
                      <p className="text-aone-lg font-aone-semibold">
                        {dashboardMetrics.cachingEfficiency.costSavings.tokensAvoided.toLocaleString()}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Similarity Distribution and Cache Warming */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-aone-lg">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="w-5 h-5" />
                      Similarity Distribution
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ChartContainer
                      config={{
                        count: {
                          label: "Count",
                          color: "hsl(var(--chart-6))",
                        },
                      }}
                      className="h-[250px]"
                    >
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={dashboardMetrics.cachingEfficiency.semanticSimilarity.similarityDistribution}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="range" />
                          <YAxis />
                          <ChartTooltip content={<ChartTooltipContent />} />
                          <Bar dataKey="count" fill="rgb(var(--preset-accent))" />
                        </BarChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="w-5 h-5" />
                      Cache Warming Status
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <p className="text-aone-sm text-aone-charcoal mb-2">Warmup Effectiveness</p>
                      <div className="flex items-center gap-aone-md">
                        <Progress value={dashboardMetrics.cachingEfficiency.cacheWarming.warmupEffectiveness * 100} className="flex-1 h-3" />
                        <span className="text-aone-lg font-bold">
                          {(dashboardMetrics.cachingEfficiency.cacheWarming.warmupEffectiveness * 100).toFixed(1)}%
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-aone-md">
                      <div>
                        <p className="text-aone-sm text-aone-charcoal">Pre-generated Embeddings</p>
                        <p className="text-aone-xl font-bold">
                          {dashboardMetrics.cachingEfficiency.cacheWarming.preGeneratedEmbeddings}
                        </p>
                      </div>
                      <div>
                        <p className="text-aone-sm text-aone-charcoal">Warmup Patterns</p>
                        <p className="text-aone-xl font-bold">
                          {dashboardMetrics.cachingEfficiency.cacheWarming.warmupPatterns.length}
                        </p>
                      </div>
                    </div>

                    <div>
                      <p className="text-aone-sm text-aone-charcoal mb-2">Warmup Patterns</p>
                      <div className="space-y-1">
                        {dashboardMetrics.cachingEfficiency.cacheWarming.warmupPatterns.map((pattern, index) => (
                          <Badge key={index} variant="outline" className="mr-2">
                            {pattern.replace('_', ' ')}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="text-xs text-aone-soft-gray">
                      <p>Last warmup: {new Date(dashboardMetrics.cachingEfficiency.cacheWarming.lastWarmupTime).toLocaleString()}</p>
                      <p>Next scheduled: {new Date(dashboardMetrics.cachingEfficiency.cacheWarming.nextScheduledWarmup).toLocaleString()}</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Cost Savings Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <PieChart className="w-5 h-5" />
                    Cost Savings by Model
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ChartContainer
                    config={{
                      savings: {
                        label: "Savings ($)",
                        color: "hsl(var(--chart-7))",
                      },
                    }}
                    className="h-[300px]"
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={Object.entries(dashboardMetrics.cachingEfficiency.costSavings.savingsBreakdown).map(([model, savings]) => ({
                            name: model,
                            value: savings,
                            fill: modelColors[model as keyof typeof modelColors] || 'rgb(var(--preset-accent))'
                          }))}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, value }) => `${name}: $${value.toFixed(2)}`}
                          outerRadius={100}
                          fill="rgb(var(--preset-accent))"
                          dataKey="value"
                        >
                          {Object.entries(dashboardMetrics.cachingEfficiency.costSavings.savingsBreakdown).map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={Object.values(modelColors)[index]} />
                          ))}
                        </Pie>
                        <ChartTooltip content={<ChartTooltipContent />} />
                      </PieChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-12">
                  <Database className="w-12 h-12 text-gray-400 mx-auto mb-aone-md" />
                  <h3 className="text-aone-lg font-aone-medium text-foreground mb-2">Caching Efficiency Loading</h3>
                  <p className="text-aone-charcoal">
                    Caching efficiency metrics are being processed...
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Real-time Monitoring Tab */}
        <TabsContent value="monitoring" className="space-y-6">
          {dashboardMetrics?.realTimeMonitoring ? (
            <>
              {/* System Health Overview */}
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-aone-lg">
                <Card>
                  <CardContent className="p-aone-lg">
                    <div className="aone-flex-between">
                      <div>
                        <p className="text-aone-sm font-aone-medium text-aone-charcoal">System Status</p>
                        <p className="text-aone-2xl font-aone-bold">
                          {dashboardMetrics.realTimeMonitoring.systemHealth.overallStatus === 'operational' ? (
                            <span className="text-status-success">Operational</span>
                          ) : dashboardMetrics.realTimeMonitoring.systemHealth.overallStatus === 'degraded' ? (
                            <span className="text-yellow-600">Degraded</span>
                          ) : (
                            <span className="text-status-error">Down</span>
                          )}
                        </p>
                      </div>
                      <div className="aone-spacing-sm bg-green-100 rounded-aone-lg">
                        {dashboardMetrics.realTimeMonitoring.systemHealth.overallStatus === 'operational' ? (
                          <CheckCircle className="w-6 h-6 text-status-success" />
                        ) : dashboardMetrics.realTimeMonitoring.systemHealth.overallStatus === 'degraded' ? (
                          <AlertCircle className="w-6 h-6 text-yellow-600" />
                        ) : (
                          <XCircle className="w-6 h-6 text-status-error" />
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-aone-lg">
                    <div className="aone-flex-between">
                      <div>
                        <p className="text-aone-sm font-aone-medium text-aone-charcoal">Uptime</p>
                        <p className="text-aone-2xl font-aone-bold">
                          {Math.floor(dashboardMetrics.realTimeMonitoring.systemHealth.uptime / 3600)}h
                        </p>
                      </div>
                      <div className="aone-spacing-sm bg-blue-100 rounded-aone-lg">
                        <Clock className="w-6 h-6 text-status-info" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-aone-lg">
                    <div className="aone-flex-between">
                      <div>
                        <p className="text-aone-sm font-aone-medium text-aone-charcoal">Active Connections</p>
                        <p className="text-aone-2xl font-aone-bold">
                          {dashboardMetrics.realTimeMonitoring.systemHealth.activeConnections}
                        </p>
                      </div>
                      <div className="aone-spacing-sm bg-purple-100 rounded-aone-lg">
                        <Network className="w-6 h-6 text-preset-accent" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-aone-lg">
                    <div className="aone-flex-between">
                      <div>
                        <p className="text-aone-sm font-aone-medium text-aone-charcoal">CPU Usage</p>
                        <p className="text-aone-2xl font-aone-bold">
                          {(dashboardMetrics.realTimeMonitoring.systemHealth.cpuUsage * 100).toFixed(1)}%
                        </p>
                      </div>
                      <div className="aone-spacing-sm bg-orange-100 rounded-aone-lg">
                        <Cpu className="w-6 h-6 text-orange-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Endpoint Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Network className="w-5 h-5" />
                    AI Endpoint Status
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(dashboardMetrics.realTimeMonitoring.endpointStatus).map(([endpoint, status]) => (
                      <div key={endpoint} className="aone-flex-between aone-spacing-md border rounded-aone-lg">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <Badge variant={status.status === 'operational' ? 'default' : 'destructive'}>
                              {status.status}
                            </Badge>
                            <span className="font-aone-medium">{endpoint}</span>
                          </div>
                          <div className="flex items-center gap-aone-md text-aone-sm text-aone-charcoal">
                            <span>Response: {status.responseTime}ms</span>
                            <span>Success: {(status.successRate * 100).toFixed(1)}%</span>
                            <span>Models: {status.models.join(', ')}</span>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-xs text-aone-soft-gray">
                            Last check: {new Date(status.lastCheck).toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Live Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="w-5 h-5" />
                    Live Performance Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-5 gap-aone-lg">
                    <div className="text-center">
                      <p className="text-aone-2xl font-aone-bold text-status-info">
                        {dashboardMetrics.realTimeMonitoring.liveMetrics.requestsPerMinute}
                      </p>
                      <p className="text-aone-sm text-aone-charcoal">Requests/min</p>
                    </div>
                    <div className="text-center">
                      <p className="text-aone-2xl font-aone-bold text-status-success">
                        {dashboardMetrics.realTimeMonitoring.liveMetrics.averageResponseTime.toFixed(1)}s
                      </p>
                      <p className="text-aone-sm text-aone-charcoal">Avg Response</p>
                    </div>
                    <div className="text-center">
                      <p className="text-aone-2xl font-aone-bold text-status-error">
                        {(dashboardMetrics.realTimeMonitoring.liveMetrics.errorRate * 100).toFixed(1)}%
                      </p>
                      <p className="text-aone-sm text-aone-charcoal">Error Rate</p>
                    </div>
                    <div className="text-center">
                      <p className="text-aone-2xl font-aone-bold text-preset-accent">
                        {(dashboardMetrics.realTimeMonitoring.liveMetrics.cacheHitRate * 100).toFixed(1)}%
                      </p>
                      <p className="text-aone-sm text-aone-charcoal">Cache Hit Rate</p>
                    </div>
                    <div className="text-center">
                      <p className="text-aone-2xl font-aone-bold text-orange-600">
                        ${dashboardMetrics.realTimeMonitoring.liveMetrics.costPerHour.toFixed(2)}
                      </p>
                      <p className="text-aone-sm text-aone-charcoal">Cost/hour</p>
                    </div>
                  </div>
                  <div className="mt-4 text-xs text-aone-soft-gray text-center">
                    Last updated: {new Date(dashboardMetrics.realTimeMonitoring.liveMetrics.timestamp).toLocaleString()}
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-12">
                  <Activity className="w-12 h-12 text-gray-400 mx-auto mb-aone-md" />
                  <h3 className="text-aone-lg font-aone-medium text-foreground mb-2">Real-time Monitoring Loading</h3>
                  <p className="text-aone-charcoal">
                    Real-time monitoring data is being processed...
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Cost Analysis Tab */}
        <TabsContent value="costs" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-aone-lg">
            {/* Cost Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="w-5 h-5" />
                  Cost Overview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-aone-md">
                  <div>
                    <p className="text-aone-sm text-aone-charcoal">Daily Cost</p>
                    <p className="text-aone-2xl font-aone-bold">${overview?.costAnalysis?.dailyCost?.toFixed(2) || '0.00'}</p>
                  </div>
                  <div>
                    <p className="text-aone-sm text-aone-charcoal">Weekly Cost</p>
                    <p className="text-aone-2xl font-aone-bold">${overview?.costAnalysis?.weeklyCost?.toFixed(2) || '0.00'}</p>
                  </div>
                  <div>
                    <p className="text-aone-sm text-aone-charcoal">Monthly Cost</p>
                    <p className="text-aone-2xl font-aone-bold">${overview?.costAnalysis?.monthlyCost?.toFixed(2) || '0.00'}</p>
                  </div>
                  <div>
                    <p className="text-aone-sm text-aone-charcoal">Projected Monthly</p>
                    <p className="text-aone-2xl font-aone-bold">${overview?.costAnalysis?.projectedMonthlyCost?.toFixed(2) || '0.00'}</p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-aone-sm text-aone-charcoal">Trend:</span>
                  <Badge
                    variant={
                      overview?.costAnalysis?.costTrend === 'INCREASING' ? 'destructive' :
                      overview?.costAnalysis?.costTrend === 'DECREASING' ? 'default' : 'secondary'
                    }
                  >
                    {overview?.costAnalysis?.costTrend || 'STABLE'}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Cost by Model */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Cost by Model
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    cost: {
                      label: "Cost ($)",
                      color: "hsl(var(--chart-4))",
                    },
                  }}
                  className="h-[250px]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={Object.entries(overview?.costAnalysis?.costByModel || {}).map(([model, cost]) => ({
                          name: model,
                          value: cost,
                          fill: modelColors[model as keyof typeof modelColors] || 'rgb(var(--preset-accent))'
                        }))}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, value }) => `${name}: $${value.toFixed(2)}`}
                        outerRadius={80}
                        fill="rgb(var(--preset-accent))"
                        dataKey="value"
                      >
                        {Object.entries(overview?.costAnalysis?.costByModel || {}).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={Object.values(modelColors)[index]} />
                        ))}
                      </Pie>
                      <ChartTooltip content={<ChartTooltipContent />} />
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>

          {/* Cost Optimization Suggestions */}
          {(overview?.costAnalysis?.optimizationSuggestions?.length || 0) > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  Cost Optimization Suggestions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {(overview?.costAnalysis?.optimizationSuggestions || []).map((suggestion, index) => (
                    <div key={index} className="flex items-start gap-3 aone-spacing-sm bg-green-50 rounded-aone-lg">
                      <div className="p-1 bg-green-200 rounded">
                        <DollarSign className="w-4 h-4 text-green-700" />
                      </div>
                      <p className="text-aone-sm text-green-800">{suggestion}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Alerts Tab */}
        <TabsContent value="alerts" className="space-y-6">
          {(overview?.activeAlerts?.length || 0) > 0 ? (
            <div className="space-y-4">
              {(overview?.activeAlerts || []).map((alert) => (
                <Card key={alert.id} className={`border-l-4 ${
                  alert.severity === 'CRITICAL' ? 'border-l-red-500' :
                  alert.severity === 'HIGH' ? 'border-l-orange-500' :
                  alert.severity === 'MEDIUM' ? 'border-l-yellow-500' :
                  'border-l-blue-500'
                }`}>
                  <CardContent className="p-aone-lg">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <Badge className={getSeverityColor(alert.severity)}>
                            {alert.severity}
                          </Badge>
                          <span className="text-aone-sm text-aone-soft-gray">
                            {alert.type.replace('_', ' ')}
                          </span>
                          <span className="text-aone-sm text-aone-soft-gray">
                            {new Date(alert.timestamp).toLocaleString()}
                          </span>
                        </div>

                        <p className={`font-aone-medium ${getSeverityTextColor(alert.severity)}`}>
                          {alert.message}
                        </p>

                        <div className="mt-2 text-aone-sm text-aone-charcoal">
                          Current: {alert.currentValue} | Threshold: {alert.threshold}
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        {alert.acknowledged ? (
                          <Badge variant="outline">Acknowledged</Badge>
                        ) : (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleAcknowledgeAlert(alert.id)}
                          >
                            Acknowledge
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-12">
                  <AlertTriangle className="w-12 h-12 text-status-success mx-auto mb-aone-md" />
                  <h3 className="text-aone-lg font-aone-medium text-foreground mb-2">No Active Alerts</h3>
                  <p className="text-aone-charcoal">
                    All systems are operating within normal parameters.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};
