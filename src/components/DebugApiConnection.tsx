/**
 * Debug API Connection Component
 * 
 * Simple component to test API connectivity and debug network issues
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export const DebugApiConnection: React.FC = () => {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (test: string, success: boolean, data: any) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      data,
      timestamp: new Date().toISOString()
    }]);
  };

  const testApiConnection = async () => {
    setIsLoading(true);
    setTestResults([]);

    // Test 1: Environment variables
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';
    const socketUrl = import.meta.env.VITE_SOCKET_URL || 'http://localhost:3001';
    
    addResult('Environment Variables', true, {
      VITE_API_URL: apiUrl,
      VITE_SOCKET_URL: socketUrl,
      VITE_NODE_ENV: import.meta.env.VITE_NODE_ENV,
      VITE_DEBUG: import.meta.env.VITE_DEBUG
    });

    // Test 2: Health endpoint
    try {
      const healthResponse = await fetch(`${apiUrl.replace('/api', '')}/api/health`);
      const healthData = await healthResponse.json();
      addResult('Health Endpoint', healthResponse.ok, {
        status: healthResponse.status,
        data: healthData
      });
    } catch (error) {
      addResult('Health Endpoint', false, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 3: Reports templates endpoint
    try {
      const templatesResponse = await fetch(`${apiUrl}/reports/templates`);
      const templatesData = await templatesResponse.json();
      addResult('Reports Templates', templatesResponse.ok, {
        status: templatesResponse.status,
        data: templatesData
      });
    } catch (error) {
      addResult('Reports Templates', false, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 4: Analysis upload endpoint (OPTIONS request)
    try {
      const optionsResponse = await fetch(`${apiUrl}/analysis/upload`, {
        method: 'OPTIONS',
        headers: {
          'Origin': window.location.origin,
          'Access-Control-Request-Method': 'POST',
          'Access-Control-Request-Headers': 'Content-Type'
        }
      });
      addResult('Analysis Upload OPTIONS', optionsResponse.ok, {
        status: optionsResponse.status,
        headers: Object.fromEntries(optionsResponse.headers.entries())
      });
    } catch (error) {
      addResult('Analysis Upload OPTIONS', false, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 5: Simple FormData test
    try {
      const formData = new FormData();
      formData.append('test', 'value');

      addResult('FormData Creation', true, {
        formDataSupported: typeof FormData !== 'undefined',
        fetchSupported: typeof fetch !== 'undefined'
      });
    } catch (error) {
      addResult('FormData Creation', false, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 6: File upload test with small test file
    try {
      // Create a small test file
      const testFileContent = 'Test PDF content for upload test';
      const testFile = new File([testFileContent], 'test.pdf', { type: 'application/pdf' });

      const formData = new FormData();
      formData.append('file', testFile);
      formData.append('useGPT4o', 'true');
      formData.append('useReasoning', 'true');
      formData.append('focusOnMaterials', 'false');
      formData.append('focusOnHardware', 'false');
      formData.append('enableMultiView', 'true');

      const uploadResponse = await fetch(`${apiUrl}/analysis/upload`, {
        method: 'POST',
        body: formData,
      });

      const uploadData = await uploadResponse.json().catch(() => null);

      addResult('File Upload Test', uploadResponse.ok, {
        status: uploadResponse.status,
        statusText: uploadResponse.statusText,
        data: uploadData,
        headers: Object.fromEntries(uploadResponse.headers.entries())
      });
    } catch (error) {
      addResult('File Upload Test', false, {
        error: error instanceof Error ? error.message : 'Unknown error',
        errorType: error instanceof TypeError ? 'TypeError' : 'Other'
      });
    }

    setIsLoading(false);
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>API Connection Debug Tool</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={testApiConnection} 
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? 'Testing...' : 'Test API Connection'}
        </Button>

        {testResults.length > 0 && (
          <div className="space-y-2">
            <h3 className="text-aone-lg font-aone-semibold">Test Results:</h3>
            {testResults.map((result, index) => (
              <div 
                key={index}
                className={`aone-spacing-sm rounded border ${
                  result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                }`}
              >
                <div className="aone-flex-between">
                  <span className="font-aone-medium">{result.test}</span>
                  <span className={`px-2 py-1 rounded text-aone-sm ${
                    result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {result.success ? 'PASS' : 'FAIL'}
                  </span>
                </div>
                <pre className="mt-2 text-xs bg-muted aone-spacing-xs rounded overflow-auto">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DebugApiConnection;
