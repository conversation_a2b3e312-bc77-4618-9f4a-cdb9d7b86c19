import React, { Suspense, useState, useRef, useCallback } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Box, Text, Grid, Environment } from '@react-three/drei';
import * as THREE from 'three';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  RotateCcw, 
  ZoomIn, 
  ZoomOut, 
  Move3D, 
  Eye, 
  Settings,
  Maximize,
  Download,
  Info
} from 'lucide-react';
import { ReconstructionResult, Cabinet3DModel, Point3D } from '@/services/aiAnalysisService';

interface CabinetReconstructionViewerProps {
  reconstruction: ReconstructionResult;
  className?: string;
}

interface Cabinet3DProps {
  cabinet: Cabinet3DModel;
  isSelected: boolean;
  onSelect: () => void;
}

/**
 * Individual 3D Cabinet Component
 */
const Cabinet3D: React.FC<Cabinet3DProps> = ({ cabinet, isSelected, onSelect }) => {
  const meshRef = useRef<any>();
  
  // Convert dimensions from mm to meters for Three.js
  const width = cabinet.dimensions.width / 1000;
  const height = cabinet.dimensions.height / 1000;
  const depth = cabinet.dimensions.depth / 1000;
  
  const position: [number, number, number] = [
    cabinet.dimensions.position.x / 1000,
    cabinet.dimensions.position.y / 1000 + height / 2, // Adjust for center positioning
    cabinet.dimensions.position.z / 1000
  ];

  // Color based on cabinet type
  const getColor = () => {
    switch (cabinet.type) {
      case 'BASE': return isSelected ? 'hsl(var(--status-info))' : 'rgb(var(--aone-soft-gray))';
      case 'WALL': return isSelected ? 'hsl(var(--status-success))' : 'rgb(var(--aone-soft-gray))';
      case 'TALL': return isSelected ? 'hsl(var(--status-warning))' : 'hsl(var(--muted))';
      case 'ISLAND': return isSelected ? 'hsl(var(--status-error))' : 'hsl(var(--muted))';
      case 'PANTRY': return isSelected ? 'rgb(var(--preset-accent))' : 'hsl(var(--muted))';
      default: return 'rgb(var(--aone-soft-gray))';
    }
  };

  // Handle click with proper event handling to prevent page refresh
  const handleClick = (event: any) => {
    try {
      // Prevent any default browser behavior
      if (event && event.stopPropagation) {
        event.stopPropagation();
      }
      if (event && event.preventDefault) {
        event.preventDefault();
      }

      console.log('Cabinet clicked:', cabinet.id, 'Event type:', event?.type);

      // Call onSelect directly without setTimeout to avoid timing issues
      onSelect();
    } catch (error) {
      console.error('Error handling cabinet click:', error);
      // Fallback: still try to call onSelect
      onSelect();
    }
  };

  return (
    <Box
      ref={meshRef}
      position={position}
      args={[width, height, depth]}
      onClick={handleClick}
    >
      <meshStandardMaterial
        color={getColor()}
        transparent
        opacity={isSelected ? 0.9 : 0.7}
        wireframe={false}
      />
      {isSelected && (
        <lineSegments>
          <edgesGeometry args={[new THREE.BoxGeometry(width, height, depth)]} />
          <lineBasicMaterial color="#ffffff" linewidth={2} />
        </lineSegments>
      )}
    </Box>
  );
};

/**
 * 3D Scene Component
 */
const Scene3D: React.FC<{
  reconstruction: ReconstructionResult;
  selectedCabinet: string | null;
  onCabinetSelect: (cabinetId: string) => void;
}> = ({ reconstruction, selectedCabinet, onCabinetSelect }) => {
  return (
    <>
      {/* Lighting */}
      <ambientLight intensity={0.6} />
      <directionalLight position={[10, 10, 5]} intensity={1} />
      <pointLight position={[-10, -10, -5]} intensity={0.5} />

      {/* Environment */}
      <Environment preset="apartment" />

      {/* Grid floor */}
      <Grid 
        args={[reconstruction.roomDimensions.width / 1000, reconstruction.roomDimensions.depth / 1000]} 
        cellSize={0.5} 
        cellThickness={0.5} 
        cellColor="rgb(var(--aone-soft-gray))" 
        sectionSize={2} 
        sectionThickness={1} 
        sectionColor="rgb(var(--aone-charcoal))"
        fadeDistance={30}
        fadeStrength={1}
        followCamera={false}
        infiniteGrid={false}
        position={[0, 0, 0]}
      />

      {/* Cabinets */}
      {reconstruction.cabinets.map((cabinet) => (
        <Cabinet3D
          key={cabinet.id}
          cabinet={cabinet}
          isSelected={selectedCabinet === cabinet.id}
          onSelect={() => onCabinetSelect(cabinet.id)}
        />
      ))}

      {/* Room boundaries (wireframe) */}
      <lineSegments>
        <bufferGeometry>
          <bufferAttribute
            attach="attributes-position"
            count={24}
            array={new Float32Array([
              // Room outline at floor level
              0, 0, 0,
              reconstruction.roomDimensions.width / 1000, 0, 0,
              reconstruction.roomDimensions.width / 1000, 0, 0,
              reconstruction.roomDimensions.width / 1000, 0, reconstruction.roomDimensions.depth / 1000,
              reconstruction.roomDimensions.width / 1000, 0, reconstruction.roomDimensions.depth / 1000,
              0, 0, reconstruction.roomDimensions.depth / 1000,
              0, 0, reconstruction.roomDimensions.depth / 1000,
              0, 0, 0,
              // Vertical lines
              0, 0, 0,
              0, reconstruction.roomDimensions.height / 1000, 0,
              reconstruction.roomDimensions.width / 1000, 0, 0,
              reconstruction.roomDimensions.width / 1000, reconstruction.roomDimensions.height / 1000, 0,
              reconstruction.roomDimensions.width / 1000, 0, reconstruction.roomDimensions.depth / 1000,
              reconstruction.roomDimensions.width / 1000, reconstruction.roomDimensions.height / 1000, reconstruction.roomDimensions.depth / 1000,
              0, 0, reconstruction.roomDimensions.depth / 1000,
              0, reconstruction.roomDimensions.height / 1000, reconstruction.roomDimensions.depth / 1000,
              // Room outline at ceiling level
              0, reconstruction.roomDimensions.height / 1000, 0,
              reconstruction.roomDimensions.width / 1000, reconstruction.roomDimensions.height / 1000, 0,
              reconstruction.roomDimensions.width / 1000, reconstruction.roomDimensions.height / 1000, 0,
              reconstruction.roomDimensions.width / 1000, reconstruction.roomDimensions.height / 1000, reconstruction.roomDimensions.depth / 1000,
              reconstruction.roomDimensions.width / 1000, reconstruction.roomDimensions.height / 1000, reconstruction.roomDimensions.depth / 1000,
              0, reconstruction.roomDimensions.height / 1000, reconstruction.roomDimensions.depth / 1000,
              0, reconstruction.roomDimensions.height / 1000, reconstruction.roomDimensions.depth / 1000,
              0, reconstruction.roomDimensions.height / 1000, 0,
            ])}
            itemSize={3}
          />
        </bufferGeometry>
        <lineBasicMaterial color="rgb(var(--aone-soft-gray))" opacity={0.3} transparent />
      </lineSegments>

      {/* Camera controls */}
      <OrbitControls 
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        maxPolarAngle={Math.PI / 2}
        minDistance={2}
        maxDistance={20}
      />
    </>
  );
};

/**
 * Main 3D Cabinet Reconstruction Viewer Component
 */
const CabinetReconstructionViewer: React.FC<CabinetReconstructionViewerProps> = ({
  reconstruction,
  className = ''
}) => {
  const [selectedCabinet, setSelectedCabinet] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'3d' | 'wireframe' | 'metrics'>('3d');

  const selectedCabinetData = selectedCabinet
    ? reconstruction.cabinets.find(c => c.id === selectedCabinet)
    : null;

  const formatDimension = (mm: number) => `${(mm / 1000).toFixed(2)}m`;

  // Enhanced cabinet selection handler with debugging
  const handleCabinetSelect = useCallback((cabinetId: string) => {
    console.log('Selecting cabinet:', cabinetId);
    console.log('Current selected cabinet:', selectedCabinet);
    setSelectedCabinet(cabinetId);
    console.log('Cabinet selection updated');
  }, [selectedCabinet]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Move3D className="w-5 h-5" />
            3D Cabinet Reconstruction
            <Badge variant="outline" className="ml-auto">
              {reconstruction.cabinets.length} Cabinets
            </Badge>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Main 3D Viewer */}
      <Card className="h-[600px]">
        <CardContent className="p-0 h-full">
          <div
            className="h-full w-full"
            onContextMenu={(e) => e.preventDefault()}
            style={{ touchAction: 'none' }}
          >
            <Suspense fallback={
              <div className="aone-flex-center h-full">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  <p className="text-aone-sm text-aone-charcoal">Loading 3D model...</p>
                </div>
              </div>
            }>
              <Canvas
                camera={{
                  position: [5, 5, 5],
                  fov: 60,
                  near: 0.1,
                  far: 1000
                }}
                style={{ height: '100%', width: '100%' }}
                onCreated={(state) => {
                  console.log('3D Canvas created successfully');
                  // Prevent default browser behavior on canvas
                  state.gl.domElement.addEventListener('contextmenu', (e) => e.preventDefault());
                  // Add additional event listeners to prevent page refresh
                  state.gl.domElement.addEventListener('click', (e) => {
                    console.log('Canvas click event:', e);
                  });
                }}
                onError={(error) => {
                  console.error('3D Canvas error:', error);
                }}
              >
                <Scene3D
                  reconstruction={reconstruction}
                  selectedCabinet={selectedCabinet}
                  onCabinetSelect={handleCabinetSelect}
                />
              </Canvas>
            </Suspense>
          </div>
        </CardContent>
      </Card>

      {/* Controls and Information */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-aone-lg">
        {/* Cabinet Details */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="w-4 h-4" />
              {selectedCabinetData ? `Cabinet: ${selectedCabinetData.id}` : 'Select a Cabinet'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {selectedCabinetData ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-aone-md">
                  <div>
                    <p className="text-aone-sm font-aone-medium text-aone-charcoal">Type</p>
                    <Badge variant="outline">{selectedCabinetData.type}</Badge>
                  </div>
                  <div>
                    <p className="text-aone-sm font-aone-medium text-aone-charcoal">Confidence</p>
                    <Badge variant={selectedCabinetData.confidence > 0.8 ? 'default' : 'secondary'}>
                      {(selectedCabinetData.confidence * 100).toFixed(1)}%
                    </Badge>
                  </div>
                </div>
                
                <div>
                  <p className="text-aone-sm font-aone-medium text-aone-charcoal mb-2">Dimensions</p>
                  <div className="grid grid-cols-3 gap-2 text-aone-sm">
                    <div className="bg-muted aone-spacing-xs rounded">
                      <p className="font-aone-medium">Width</p>
                      <p>{formatDimension(selectedCabinetData.dimensions.width)}</p>
                    </div>
                    <div className="bg-muted aone-spacing-xs rounded">
                      <p className="font-aone-medium">Height</p>
                      <p>{formatDimension(selectedCabinetData.dimensions.height)}</p>
                    </div>
                    <div className="bg-muted aone-spacing-xs rounded">
                      <p className="font-aone-medium">Depth</p>
                      <p>{formatDimension(selectedCabinetData.dimensions.depth)}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <p className="text-aone-sm font-aone-medium text-aone-charcoal mb-2">Position</p>
                  <div className="grid grid-cols-3 gap-2 text-aone-sm">
                    <div className="bg-muted aone-spacing-xs rounded">
                      <p className="font-aone-medium">X</p>
                      <p>{formatDimension(selectedCabinetData.dimensions.position.x)}</p>
                    </div>
                    <div className="bg-muted aone-spacing-xs rounded">
                      <p className="font-aone-medium">Y</p>
                      <p>{formatDimension(selectedCabinetData.dimensions.position.y)}</p>
                    </div>
                    <div className="bg-muted aone-spacing-xs rounded">
                      <p className="font-aone-medium">Z</p>
                      <p>{formatDimension(selectedCabinetData.dimensions.position.z)}</p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-aone-soft-gray">Click on a cabinet in the 3D view to see its details</p>
            )}
          </CardContent>
        </Card>

        {/* Reconstruction Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="text-aone-sm">Reconstruction Metrics</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <p className="text-xs font-aone-medium text-aone-charcoal">Overall Confidence</p>
              <Badge variant={reconstruction.confidence.overall > 0.8 ? 'default' : 'secondary'}>
                {(reconstruction.confidence.overall * 100).toFixed(1)}%
              </Badge>
            </div>
            
            <div>
              <p className="text-xs font-aone-medium text-aone-charcoal">Spatial Accuracy</p>
              <Badge variant="outline">
                {(reconstruction.reconstructionMetrics.spatialAccuracy * 100).toFixed(1)}%
              </Badge>
            </div>
            
            <div>
              <p className="text-xs font-aone-medium text-aone-charcoal">Total Volume</p>
              <p className="text-aone-sm">{reconstruction.reconstructionMetrics.totalVolume.toFixed(2)} m³</p>
            </div>
            
            <div>
              <p className="text-xs font-aone-medium text-aone-charcoal">Processing Time</p>
              <p className="text-aone-sm">{(reconstruction.reconstructionMetrics.reconstructionTime / 1000).toFixed(1)}s</p>
            </div>

            <div>
              <p className="text-xs font-aone-medium text-aone-charcoal">Room Dimensions</p>
              <div className="text-xs space-y-1">
                <p>W: {formatDimension(reconstruction.roomDimensions.width)}</p>
                <p>H: {formatDimension(reconstruction.roomDimensions.height)}</p>
                <p>D: {formatDimension(reconstruction.roomDimensions.depth)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CabinetReconstructionViewer;
