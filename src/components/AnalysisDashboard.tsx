import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Upload,
  BarChart3,
  FileText,
  Settings,
  Brain,
  Zap,
  Activity,
  Calculator,
  Users,
  Eye,
  Sparkles
} from 'lucide-react';
import AnalysisUpload from './AnalysisUpload';
import AnalysisResults from './AnalysisResults';
import EnhancedAnalysisUpload from './EnhancedAnalysisUpload';
import EnhancedAnalysisResults from './EnhancedAnalysisResults';
import EnhancedWebSocketStatus from './EnhancedWebSocketStatus';
import { PerformanceMetricsDashboard } from './PerformanceMetricsDashboard';
import CollaborationDashboard from './collaboration/CollaborationDashboard';
import { QuotationSection } from './QuotationSection';
import { AnalysisResults as AnalysisResultsType } from '@/services/aiAnalysisService';
import { Quote } from '@/services/quotationService';
import VisualExperienceSystem from './visualization/VisualExperienceSystem';

const AnalysisDashboard: React.FC = () => {
  const [currentResults, setCurrentResults] = useState<AnalysisResultsType | null>(null);
  const [analysisHistory, setAnalysisHistory] = useState<AnalysisResultsType[]>([]);
  const [activeTab, setActiveTab] = useState<string>('upload');
  const [useEnhancedUI, setUseEnhancedUI] = useState<boolean>(true); // Feature flag for enhanced UI
  const [currentQuote, setCurrentQuote] = useState<Quote | null>(null);
  const [showQuotation, setShowQuotation] = useState<boolean>(false);
  const [collaborationMode, setCollaborationMode] = useState<'view' | 'edit' | 'comment' | 'annotate'>('view');

  const handleAnalysisComplete = (results: AnalysisResultsType) => {
    setCurrentResults(results);
    setAnalysisHistory(prev => [results, ...prev]);
    setActiveTab('results');
  };

  const handleAnalysisError = (error: string) => {
    console.error('Analysis error:', error);
    // Could show a toast notification here
  };

  const handleGenerateQuote = () => {
    if (currentResults) {
      console.log('Generating quote for analysis:', currentResults.id);
      setShowQuotation(true);
      setActiveTab('quotation');
    }
  };

  const handleQuoteGenerated = (quote: Quote) => {
    setCurrentQuote(quote);
    console.log('Quote generated successfully:', quote.id);
  };

  const handleExportResults = () => {
    if (currentResults) {
      // Export results as JSON
      const dataStr = JSON.stringify(currentResults, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
      
      const exportFileDefaultName = `kitchen-analysis-${currentResults.id}.json`;
      
      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();
    }
  };

  const handleSelectHistoryItem = (results: AnalysisResultsType) => {
    setCurrentResults(results);
    setActiveTab('results');
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('en-NZ', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="aone-container py-8 max-w-6xl">
      {/* Enhanced Header */}
      <div className="mb-8">
        <div className="aone-flex-between mb-aone-md">
          <div>
            <h1 className="text-aone-3xl font-aone-bold text-foreground mb-2">
              Cabinet Insight Pro
            </h1>
            <p className="text-muted-foreground">
              AI-powered kitchen cabinet analysis with enhanced user experience
            </p>
          </div>
          <div className="flex items-center gap-aone-md">
            {/* Enhanced UI Toggle */}
            <div className="flex items-center gap-2">
              <label className="aone-label-enterprise">
                Enhanced UI
              </label>
              <button
                type="button"
                onClick={() => setUseEnhancedUI(!useEnhancedUI)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors aone-focus-ring aone-focus-ring focus:ring-offset-2 ${
                  useEnhancedUI ? 'bg-blue-600' : 'bg-gray-200'
                }`}
                role="switch"
                aria-checked={useEnhancedUI}
                aria-label="Toggle enhanced UI"
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    useEnhancedUI ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
        </div>

        {/* Real-time Status (Enhanced UI only) */}
        {useEnhancedUI && (
          <div className="mb-aone-lg">
            <EnhancedWebSocketStatus showDetailedMetrics={false} />
          </div>
        )}
      </div>

      {/* Enhanced AI Capabilities Showcase */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-aone-md mb-8">
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="aone-spacing-xs bg-blue-100 rounded-aone-lg">
                <Brain className="w-6 h-6 text-aone-sage" />
              </div>
              <div>
                <h3 className="font-aone-semibold">GPT-4o + GPT-o1</h3>
                <p className="text-aone-sm text-muted-foreground">Dual AI model architecture</p>
                <div className="text-xs text-aone-sage font-aone-medium mt-1">88.2% Accuracy</div>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="aone-spacing-xs bg-green-100 rounded-aone-lg">
                <Zap className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h3 className="font-aone-semibold">3D Reconstruction</h3>
                <p className="text-aone-sm text-muted-foreground">Interactive spatial analysis</p>
                <div className="text-xs text-green-600 font-aone-medium mt-1">Real-time 3D</div>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="border-purple-200 bg-purple-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="aone-spacing-xs bg-purple-100 rounded-aone-lg">
                <BarChart3 className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <h3 className="font-aone-semibold">Smart Analytics</h3>
                <p className="text-aone-sm text-muted-foreground">Advanced performance metrics</p>
                <div className="text-xs text-purple-600 font-aone-medium mt-1">91.7% Success Rate</div>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="aone-spacing-xs bg-orange-100 rounded-aone-lg">
                <Activity className="w-6 h-6 text-orange-600" />
              </div>
              <div>
                <h3 className="font-aone-semibold">Enterprise Ready</h3>
                <p className="text-aone-sm text-muted-foreground">Production-grade platform</p>
                <div className="text-xs text-orange-600 font-aone-medium mt-1">1000+ Users</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="upload" className="flex items-center gap-2">
            <Upload className="w-4 h-4" />
            <span className="hidden sm:inline">Upload & Analyze</span>
            <span className="sm:hidden">Upload</span>
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center gap-2" disabled={!currentResults}>
            <BarChart3 className="w-4 h-4" />
            <span className="hidden sm:inline">AI Analysis Results</span>
            <span className="sm:hidden">Results</span>
            {currentResults && (
              <div className="ml-1 w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            )}
          </TabsTrigger>
          <TabsTrigger value="collaboration" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            <span className="hidden sm:inline">Team Collaboration</span>
            <span className="sm:hidden">Team</span>
          </TabsTrigger>
          <TabsTrigger value="quotation" className="flex items-center gap-2" disabled={!currentResults}>
            <Calculator className="w-4 h-4" />
            <span className="hidden sm:inline">NZD Quotation</span>
            <span className="sm:hidden">Quote</span>
            {currentQuote && (
              <div className="ml-1 w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            )}
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            <span className="hidden sm:inline">Analysis History</span>
            <span className="sm:hidden">History</span>
            <span className="ml-1 text-xs bg-gray-200 px-1.5 py-0.5 rounded-full">
              {analysisHistory.length}
            </span>
          </TabsTrigger>
          <TabsTrigger value="visual" className="flex items-center gap-2" disabled={!currentResults}>
            <Eye className="w-4 h-4" />
            <span className="hidden sm:inline">Visual Experience</span>
            <span className="sm:hidden">Visual</span>
            {currentResults && (
              <Sparkles className="w-3 h-3 ml-1 text-aone-sage animate-pulse" />
            )}
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <Activity className="w-4 h-4" />
            <span className="hidden sm:inline">Performance Metrics</span>
            <span className="sm:hidden">Metrics</span>
          </TabsTrigger>
        </TabsList>

        {/* Upload Tab */}
        <TabsContent value="upload" className="mt-6">
          {useEnhancedUI ? (
            <EnhancedAnalysisUpload
              onAnalysisComplete={handleAnalysisComplete}
              onAnalysisError={handleAnalysisError}
            />
          ) : (
            <AnalysisUpload
              onAnalysisComplete={handleAnalysisComplete}
              onAnalysisError={handleAnalysisError}
            />
          )}
        </TabsContent>

        {/* Results Tab */}
        <TabsContent value="results" className="mt-6">
          {currentResults ? (
            useEnhancedUI ? (
              <EnhancedAnalysisResults
                results={currentResults}
                onGenerateQuote={handleGenerateQuote}
                onExportResults={handleExportResults}
              />
            ) : (
              <AnalysisResults
                results={currentResults}
                onGenerateQuote={handleGenerateQuote}
                onExportResults={handleExportResults}
              />
            )
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-12">
                  <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-aone-md" />
                  <h3 className="text-aone-lg font-aone-medium text-foreground mb-2">No Analysis Results</h3>
                  <p className="text-muted-foreground mb-aone-md">
                    Upload a kitchen design to see detailed analysis results here.
                  </p>
                  <Button onClick={() => setActiveTab('upload')}>
                    Start Analysis
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Collaboration Tab */}
        <TabsContent value="collaboration" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-aone-lg h-[800px]">
            {/* Main Content Area */}
            <div className="lg:col-span-3 relative" data-project-canvas>
              {currentResults ? (
                <div className="h-full">
                  {useEnhancedUI ? (
                    <EnhancedAnalysisResults
                      results={currentResults}
                      onGenerateQuote={handleGenerateQuote}
                      onExportResults={handleExportResults}
                    />
                  ) : (
                    <AnalysisResults
                      results={currentResults}
                      onGenerateQuote={handleGenerateQuote}
                      onExportResults={handleExportResults}
                    />
                  )}
                </div>
              ) : (
                <Card className="h-full">
                  <CardContent className="pt-6 h-full aone-flex-center">
                    <div className="text-center">
                      <Users className="w-16 h-16 text-gray-400 mx-auto mb-aone-md" />
                      <h3 className="text-aone-xl font-aone-medium text-foreground mb-2">Start Collaborating</h3>
                      <p className="text-muted-foreground mb-aone-md">
                        Upload and analyze a kitchen design to begin team collaboration.
                      </p>
                      <Button onClick={() => setActiveTab('upload')}>
                        Upload Design
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Collaboration Sidebar */}
            <div className="lg:col-span-1">
              <CollaborationDashboard
                projectId={currentResults?.projectId || 'demo-project'}
                analysisId={currentResults?.id}
                mode={collaborationMode}
                onModeChange={setCollaborationMode}
                className="h-full"
              />
            </div>
          </div>
        </TabsContent>

        {/* Quotation Tab */}
        <TabsContent value="quotation" className="mt-6">
          {currentResults ? (
            <QuotationSection
              analysisData={currentResults}
              analysisId={currentResults.id}
              projectId={currentResults.projectId}
              onQuoteGenerated={handleQuoteGenerated}
            />
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-12">
                  <Calculator className="w-12 h-12 text-gray-400 mx-auto mb-aone-md" />
                  <h3 className="text-aone-lg font-aone-medium text-foreground mb-2">No Analysis Available</h3>
                  <p className="text-muted-foreground mb-aone-md">
                    Complete an analysis first to generate detailed NZD quotations.
                  </p>
                  <Button onClick={() => setActiveTab('upload')}>
                    Start Analysis
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history" className="mt-6">
          {analysisHistory.length > 0 ? (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Analysis History</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analysisHistory.map((analysis) => (
                      <div
                        key={analysis.id}
                        className="aone-flex-between aone-spacing-md border rounded-aone-lg hover:bg-muted cursor-pointer"
                        onClick={() => handleSelectHistoryItem(analysis)}
                      >
                        <div>
                          <h4 className="font-aone-medium">Analysis {analysis.id.split('_')[1]}</h4>
                          <p className="text-aone-sm text-muted-foreground">
                            {analysis.measurements.totalCabinets} cabinets • {analysis.measurements.totalLinearMeters}m linear
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatDate(analysis.timestamp)}
                          </p>
                        </div>
                        <div className="text-right">
                          <div className="text-aone-sm font-aone-medium text-green-600">
                            {Math.round(analysis.confidence.overall * 100)}% confidence
                          </div>
                          <div className="text-xs text-gray-500">
                            {analysis.processingTime.toFixed(1)}s processing
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-12">
                  <FileText className="w-12 h-12 text-gray-400 mx-auto mb-aone-md" />
                  <h3 className="text-aone-lg font-aone-medium text-foreground mb-2">No Analysis History</h3>
                  <p className="text-muted-foreground mb-aone-md">
                    Your completed analyses will appear here for easy access and comparison.
                  </p>
                  <Button onClick={() => setActiveTab('upload')}>
                    Start First Analysis
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Visual Experience Tab */}
        <TabsContent value="visual" className="mt-6">
          {currentResults ? (
            <VisualExperienceSystem
              reconstruction={currentResults.reconstruction}
              analysisId={currentResults.id}
              chainId={currentResults.reasoningChainId}
              defaultMode="integrated"
              data-testid="visual-experience-system"
            />
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-12">
                  <Eye className="w-12 h-12 text-gray-400 mx-auto mb-aone-md" />
                  <h3 className="text-aone-lg font-aone-medium text-foreground mb-2">Visual Experience System</h3>
                  <p className="text-muted-foreground mb-aone-md">
                    Complete an analysis to access cinematic 3D transitions, AI particle visualization, and interactive reasoning trees.
                  </p>
                  <Button onClick={() => setActiveTab('upload')}>
                    Start Analysis
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="mt-6">
          <PerformanceMetricsDashboard
            autoRefresh={true}
            refreshInterval={30000}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AnalysisDashboard;
