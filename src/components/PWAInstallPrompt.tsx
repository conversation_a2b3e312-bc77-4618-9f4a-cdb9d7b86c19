import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Download,
  Smartphone,
  X,
  Wifi,
  WifiOff,
  RefreshCw,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { usePWA, requestNotificationPermission } from '@/utils/pwaUtils';

interface PWAInstallPromptProps {
  className?: string;
  showInHeader?: boolean;
  autoShow?: boolean;
}

const PWAInstallPrompt: React.FC<PWAInstallPromptProps> = ({
  className = '',
  showInHeader = false,
  autoShow = true
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [installResult, setInstallResult] = useState<'success' | 'failed' | null>(null);
  const [pwaError, setPwaError] = useState<string | null>(null);

  // Safely get PWA state with error handling
  let capabilities, updateInfo, isOnline, install, applyUpdate;
  try {
    const pwaState = usePWA();
    capabilities = pwaState.capabilities;
    updateInfo = pwaState.updateInfo;
    isOnline = pwaState.isOnline;
    install = pwaState.install;
    applyUpdate = pwaState.applyUpdate;
  } catch (error) {
    console.error('[PWA] Error getting PWA state:', error);
    setPwaError('PWA features unavailable');
    // Return null to hide the component if PWA fails
    return null;
  }

  useEffect(() => {
    // Show install prompt if app is installable and not already installed
    if (autoShow && capabilities.isInstallable && !capabilities.isInstalled) {
      // Delay showing the prompt to avoid interrupting user flow
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [capabilities.isInstallable, capabilities.isInstalled, autoShow]);

  const handleInstall = async () => {
    setIsInstalling(true);
    setInstallResult(null);
    
    try {
      const success = await install();
      setInstallResult(success ? 'success' : 'failed');
      
      if (success) {
        setIsVisible(false);
      }
    } catch (error) {
      console.error('PWA installation failed:', error);
      setInstallResult('failed');
    } finally {
      setIsInstalling(false);
    }
  };

  const handleUpdate = async () => {
    setIsUpdating(true);
    
    try {
      await applyUpdate();
      // Page will reload automatically after update
    } catch (error) {
      console.error('PWA update failed:', error);
      setIsUpdating(false);
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    // Remember user dismissed the prompt (could store in localStorage)
    try {
      localStorage.setItem('pwa-install-dismissed', Date.now().toString());
    } catch (error) {
      console.warn('[PWA] Failed to save dismiss state:', error);
    }
  };

  const handleNotificationPermission = async () => {
    try {
      const permission = await requestNotificationPermission();
      console.log('[PWA] Notification permission result:', permission);
    } catch (error) {
      console.error('[PWA] Failed to request notification permission:', error);
    }
  };

  // Header version - compact install button
  if (showInHeader) {
    if (capabilities.isInstalled) {
      return (
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="flex items-center gap-1">
            <Smartphone className="w-3 h-3" />
            Installed
          </Badge>
          {updateInfo.isUpdateReady && (
            <Button
              size="sm"
              variant="outline"
              onClick={handleUpdate}
              disabled={isUpdating}
              className="flex items-center gap-1"
            >
              <RefreshCw className={`w-3 h-3 ${isUpdating ? 'animate-spin' : ''}`} />
              Update
            </Button>
          )}
        </div>
      );
    }

    if (capabilities.isInstallable) {
      return (
        <Button
          size="sm"
          variant="outline"
          onClick={handleInstall}
          disabled={isInstalling}
          className="flex items-center gap-1"
        >
          <Download className="w-3 h-3" />
          {isInstalling ? 'Installing...' : 'Install App'}
        </Button>
      );
    }

    return null;
  }

  // Full install prompt
  if (!isVisible || capabilities.isInstalled) {
    return null;
  }

  return (
    <div className={`fixed bottom-4 right-4 z-50 max-w-sm ${className}`}>
      <Card className="shadow-lg border-2 border-blue-200 dark:border-blue-800">
        <CardContent className="aone-spacing-md">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-teal-600 rounded-aone-lg aone-flex-center">
                <Smartphone className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="font-aone-semibold text-aone-sm">Install Cabinet Insight Pro</h3>
                <p className="text-xs text-muted-foreground dark:text-gray-400">
                  Get the full app experience
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="h-6 w-6 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          <div className="space-y-3">
            {/* Features */}
            <div className="text-xs space-y-1">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-3 h-3 text-green-500" />
                <span>Works offline</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-3 h-3 text-green-500" />
                <span>Faster loading</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-3 h-3 text-green-500" />
                <span>Push notifications</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-3 h-3 text-green-500" />
                <span>Home screen access</span>
              </div>
            </div>

            {/* Connection Status */}
            <div className="flex items-center gap-2 text-xs">
              {isOnline ? (
                <>
                  <Wifi className="w-3 h-3 text-green-500" />
                  <span className="text-green-600 dark:text-green-400">Online</span>
                </>
              ) : (
                <>
                  <WifiOff className="w-3 h-3 text-red-500" />
                  <span className="text-red-600 dark:text-red-400">Offline</span>
                </>
              )}
            </div>

            {/* Install Result */}
            {installResult && (
              <Alert className={installResult === 'success' ? 'border-green-200' : 'border-red-200'}>
                <AlertDescription className="text-xs flex items-center gap-2">
                  {installResult === 'success' ? (
                    <>
                      <CheckCircle className="w-3 h-3 text-green-500" />
                      App installed successfully!
                    </>
                  ) : (
                    <>
                      <AlertCircle className="w-3 h-3 text-red-500" />
                      Installation failed. Please try again.
                    </>
                  )}
                </AlertDescription>
              </Alert>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={handleInstall}
                disabled={isInstalling || !isOnline}
                className="flex-1 text-xs"
              >
                {isInstalling ? (
                  <>
                    <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                    Installing...
                  </>
                ) : (
                  <>
                    <Download className="w-3 h-3 mr-1" />
                    Install
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDismiss}
                className="text-xs"
              >
                Later
              </Button>
            </div>

            {!isOnline && (
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Connect to internet to install the app
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PWAInstallPrompt;
