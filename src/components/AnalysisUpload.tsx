import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Upload, 
  FileText, 
  Image, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Settings
} from 'lucide-react';
import { aiAnalysisService, AnalysisConfig, AnalysisProgress, AnalysisResults } from '@/services/aiAnalysisService';

interface AnalysisUploadProps {
  onAnalysisComplete?: (results: AnalysisResults) => void;
  onAnalysisError?: (error: string) => void;
}

const AnalysisUpload: React.FC<AnalysisUploadProps> = ({
  onAnalysisComplete,
  onAnalysisError,
}) => {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [progress, setProgress] = useState<AnalysisProgress | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [config, setConfig] = useState<AnalysisConfig>({
    useGPT4o: true,
    useReasoning: true,
    focusOnMaterials: false,
    focusOnHardware: false,
    enableMultiView: true,
  });

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      setUploadedFile(file);
      setError(null);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/png': ['.png'],
      'image/jpeg': ['.jpg', '.jpeg'],
    },
    maxFiles: 1,
    maxSize: 50 * 1024 * 1024, // 50MB
  });

  const handleAnalyze = async () => {
    if (!uploadedFile) return;

    setIsAnalyzing(true);
    setError(null);
    setProgress(null);

    try {
      const results = await aiAnalysisService.analyzeKitchenDesign(
        uploadedFile,
        config,
        (progressUpdate) => {
          setProgress(progressUpdate);
        }
      );

      onAnalysisComplete?.(results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Analysis failed';
      setError(errorMessage);
      onAnalysisError?.(errorMessage);
    } finally {
      setIsAnalyzing(false);
      setProgress(null);
    }
  };

  const getFileIcon = (file: File) => {
    if (file.type === 'application/pdf') {
      return <FileText className="w-8 h-8 text-red-500" />;
    }
    return <Image className="w-8 h-8 text-blue-500" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getProgressMessage = () => {
    if (!progress) return '';
    
    const stepMessages = {
      validation: 'Validating file format and size...',
      preprocessing: 'Converting file and extracting images...',
      analysis: 'Analyzing design with AI vision...',
      cabinet_detection: 'Detecting and classifying cabinets...',
      hardware_analysis: 'Analyzing hardware components...',
      compilation: 'Compiling and validating results...',
      completed: 'Analysis completed successfully!',
    };

    return stepMessages[progress.step as keyof typeof stepMessages] || progress.message;
  };

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            Upload Kitchen Design
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!uploadedFile ? (
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-aone-lg p-aone-xl text-center cursor-pointer transition-colors ${
                isDragActive
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-border hover:border-gray-400'
              }`}
            >
              <input {...getInputProps()} />
              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-aone-md" />
              <p className="text-aone-lg font-aone-medium text-foreground mb-2">
                {isDragActive ? 'Drop your file here' : 'Drag & drop your kitchen design'}
              </p>
              <p className="text-aone-sm text-gray-500 mb-aone-md">
                or click to browse files
              </p>
              <div className="flex justify-center gap-2">
                <Badge variant="secondary">PDF</Badge>
                <Badge variant="secondary">PNG</Badge>
                <Badge variant="secondary">JPEG</Badge>
              </div>
              <p className="text-xs text-gray-400 mt-2">
                Maximum file size: 50MB
              </p>
            </div>
          ) : (
            <div className="aone-flex-between aone-spacing-md bg-muted rounded-aone-lg">
              <div className="flex items-center gap-3">
                {getFileIcon(uploadedFile)}
                <div>
                  <p className="font-aone-medium text-foreground">{uploadedFile.name}</p>
                  <p className="text-aone-sm text-gray-500">{formatFileSize(uploadedFile.size)}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setUploadedFile(null)}
                  disabled={isAnalyzing}
                >
                  Remove
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Analysis Configuration */}
      {uploadedFile && !isAnalyzing && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Analysis Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-aone-md">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={config.focusOnMaterials}
                  onChange={(e) => setConfig(prev => ({ ...prev, focusOnMaterials: e.target.checked }))}
                  className="rounded"
                />
                <span className="text-aone-sm">Focus on Materials</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={config.focusOnHardware}
                  onChange={(e) => setConfig(prev => ({ ...prev, focusOnHardware: e.target.checked }))}
                  className="rounded"
                />
                <span className="text-aone-sm">Focus on Hardware</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={config.enableMultiView}
                  onChange={(e) => setConfig(prev => ({ ...prev, enableMultiView: e.target.checked }))}
                  className="rounded"
                />
                <span className="text-aone-sm">Multi-View Analysis</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={config.useReasoning}
                  onChange={(e) => setConfig(prev => ({ ...prev, useReasoning: e.target.checked }))}
                  className="rounded"
                />
                <span className="text-aone-sm">AI Reasoning & Validation</span>
              </label>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Analysis Progress */}
      {isAnalyzing && progress && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="aone-flex-between">
                <h3 className="font-aone-medium">Analyzing Kitchen Design</h3>
                <span className="text-aone-sm text-gray-500">{progress.progress}%</span>
              </div>
              <Progress value={progress.progress} className="w-full" />
              <p className="text-aone-sm text-muted-foreground">{getProgressMessage()}</p>
              <div className="flex items-center gap-2 text-aone-sm text-gray-500">
                <Loader2 className="w-4 h-4 animate-spin" />
                Processing step: {progress.step.replace('_', ' ')}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Action Button */}
      {uploadedFile && !isAnalyzing && (
        <div className="flex justify-center">
          <Button
            onClick={handleAnalyze}
            size="lg"
            className="px-8"
            disabled={!uploadedFile}
          >
            Start AI Analysis
          </Button>
        </div>
      )}
    </div>
  );
};

export default AnalysisUpload;
