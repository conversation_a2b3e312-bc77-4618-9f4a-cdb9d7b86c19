import React, { useState, useEffect, useCallback, useRef } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  Clock, 
  Users, 
  Server, 
  AlertTriangle, 
  CheckCircle,
  TrendingUp,
  Zap,
  Eye,
  Settings
} from 'lucide-react';

interface AnalysisMetrics {
  queueLength: number;
  processingCount: number;
  connectedClients: number;
  activeAnalyses: number;
  timestamp: string;
}

interface PerformanceMetrics {
  cpuUsage: number;
  memoryUsage: number;
  activeConnections: number;
  averageResponseTime: number;
  errorRate: number;
  serverUptime: number;
  timestamp: string;
}

interface AnalysisProgress {
  analysisId: string;
  step: string;
  progress: number;
  message: string;
  timestamp: string;
  confidence?: number;
  estimatedTimeRemaining?: number;
}

interface EnhancedAnalysisMonitorProps {
  isAdminMode?: boolean;
  showPerformanceMetrics?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

/**
 * Enhanced Analysis Monitor with advanced patterns from archived A.One Kitchen projects
 * Provides real-time monitoring of analysis queue, performance metrics, and system health
 */
export const EnhancedAnalysisMonitor: React.FC<EnhancedAnalysisMonitorProps> = ({
  isAdminMode = false,
  showPerformanceMetrics = false,
  autoRefresh = true,
  refreshInterval = 5000
}) => {
  const [metrics, setMetrics] = useState<AnalysisMetrics | null>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null);
  const [activeAnalyses, setActiveAnalyses] = useState<Map<string, AnalysisProgress>>(new Map());
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('connecting');
  const [historicalData, setHistoricalData] = useState<AnalysisMetrics[]>([]);
  
  const socketRef = useRef<any>(null);
  const metricsHistoryRef = useRef<AnalysisMetrics[]>([]);

  // Initialize WebSocket connection with enhanced error handling
  useEffect(() => {
    const initializeSocket = async () => {
      try {
        const { io } = await import('socket.io-client');
        const socketUrl = import.meta.env.VITE_SOCKET_URL || 'http://localhost:3001';
        
        socketRef.current = io(socketUrl, {
          transports: ['websocket', 'polling'],
          timeout: 10000,
          retries: 3
        });

        setupSocketListeners();
        
      } catch (error) {
        console.error('Failed to initialize socket:', error);
        setConnectionStatus('disconnected');
      }
    };

    initializeSocket();

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, []);

  // Setup socket event listeners with enhanced monitoring
  const setupSocketListeners = useCallback(() => {
    const socket = socketRef.current;
    if (!socket) return;

    socket.on('connect', () => {
      setConnectionStatus('connected');
      console.log('Enhanced monitor connected to server');
      
      // Join admin room if in admin mode
      if (isAdminMode) {
        socket.emit('join-admin');
      }
    });

    socket.on('disconnect', () => {
      setConnectionStatus('disconnected');
      console.log('Enhanced monitor disconnected from server');
    });

    socket.on('connect_error', (error: any) => {
      setConnectionStatus('disconnected');
      console.warn('Enhanced monitor connection error:', error);
    });

    // Listen for queue status updates
    socket.on('queue-status', (status: AnalysisMetrics) => {
      setMetrics(status);
      updateHistoricalData(status);
    });

    // Listen for admin-specific queue status (enhanced metrics)
    socket.on('admin-queue-status', (adminStatus: any) => {
      setMetrics(adminStatus);
      updateHistoricalData(adminStatus);
    });

    // Listen for performance metrics (admin only)
    socket.on('performance-metrics', (perfMetrics: PerformanceMetrics) => {
      setPerformanceMetrics(perfMetrics);
    });

    // Listen for analysis progress updates
    socket.on('analysis-progress', (progress: AnalysisProgress) => {
      setActiveAnalyses(prev => {
        const updated = new Map(prev);
        updated.set(progress.analysisId, progress);
        return updated;
      });
    });

    // Listen for analysis completion
    socket.on('analysis-complete', (result: any) => {
      setActiveAnalyses(prev => {
        const updated = new Map(prev);
        updated.delete(result.analysisId);
        return updated;
      });
    });

  }, [isAdminMode]);

  // Update historical data for trend analysis
  const updateHistoricalData = useCallback((newMetrics: AnalysisMetrics) => {
    metricsHistoryRef.current = [...metricsHistoryRef.current, newMetrics].slice(-50); // Keep last 50 data points
    setHistoricalData([...metricsHistoryRef.current]);
  }, []);

  // Calculate trend indicators
  const calculateTrends = useCallback(() => {
    if (historicalData.length < 2) return null;
    
    const recent = historicalData.slice(-10);
    const avgQueueLength = recent.reduce((sum, m) => sum + m.queueLength, 0) / recent.length;
    const avgProcessingCount = recent.reduce((sum, m) => sum + m.processingCount, 0) / recent.length;
    
    return {
      avgQueueLength: Math.round(avgQueueLength * 10) / 10,
      avgProcessingCount: Math.round(avgProcessingCount * 10) / 10,
      trend: recent.length > 5 ? (recent[recent.length - 1].queueLength > recent[0].queueLength ? 'up' : 'down') : 'stable'
    };
  }, [historicalData]);

  // Get system health status
  const getSystemHealth = useCallback(() => {
    if (!metrics || !performanceMetrics) return 'unknown';
    
    const issues = [];
    
    if (performanceMetrics.cpuUsage > 80) issues.push('High CPU usage');
    if (performanceMetrics.memoryUsage > 85) issues.push('High memory usage');
    if (performanceMetrics.errorRate > 5) issues.push('High error rate');
    if (metrics.queueLength > 10) issues.push('Large queue backlog');
    
    if (issues.length === 0) return 'healthy';
    if (issues.length <= 2) return 'warning';
    return 'critical';
  }, [metrics, performanceMetrics]);

  // Format uptime display
  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const trends = calculateTrends();
  const systemHealth = getSystemHealth();

  return (
    <div className="space-y-6">
      {/* Connection Status Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="aone-flex-between">
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              System Monitor
              {isAdminMode && <Badge variant="secondary">Admin</Badge>}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge 
                variant={connectionStatus === 'connected' ? 'default' : 'destructive'}
                className="flex items-center gap-1"
              >
                <div className={`w-2 h-2 rounded-full ${
                  connectionStatus === 'connected' ? 'bg-green-500' : 'bg-red-500'
                }`} />
                {connectionStatus}
              </Badge>
              {systemHealth !== 'unknown' && (
                <Badge 
                  variant={systemHealth === 'healthy' ? 'default' : systemHealth === 'warning' ? 'secondary' : 'destructive'}
                  className="flex items-center gap-1"
                >
                  {systemHealth === 'healthy' ? <CheckCircle className="w-3 h-3" /> : <AlertTriangle className="w-3 h-3" />}
                  {systemHealth}
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Main Metrics Tabs */}
      <Tabs defaultValue="queue" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="queue" className="flex items-center gap-2">
            <Server className="w-4 h-4" />
            Queue Status
          </TabsTrigger>
          <TabsTrigger value="active" className="flex items-center gap-2">
            <Zap className="w-4 h-4" />
            Active Analyses
          </TabsTrigger>
          {(isAdminMode || showPerformanceMetrics) && (
            <TabsTrigger value="performance" className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              Performance
            </TabsTrigger>
          )}
        </TabsList>

        {/* Queue Status Tab */}
        <TabsContent value="queue" className="space-y-4">
          {metrics && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-aone-md">
              <Card>
                <CardContent className="aone-spacing-md">
                  <div className="text-center">
                    <div className="text-aone-2xl font-aone-bold text-aone-sage">{metrics.queueLength}</div>
                    <div className="text-aone-sm text-gray-500">Queue Length</div>
                    {trends && (
                      <div className="text-xs text-gray-400 mt-1">
                        Avg: {trends.avgQueueLength}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="aone-spacing-md">
                  <div className="text-center">
                    <div className="text-aone-2xl font-aone-bold text-green-600">{metrics.processingCount}</div>
                    <div className="text-aone-sm text-gray-500">Processing</div>
                    {trends && (
                      <div className="text-xs text-gray-400 mt-1">
                        Avg: {trends.avgProcessingCount}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="aone-spacing-md">
                  <div className="text-center">
                    <div className="text-aone-2xl font-aone-bold text-purple-600">{metrics.connectedClients}</div>
                    <div className="text-aone-sm text-gray-500">Connected Clients</div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="aone-spacing-md">
                  <div className="text-center">
                    <div className="text-aone-2xl font-aone-bold text-orange-600">{metrics.activeAnalyses}</div>
                    <div className="text-aone-sm text-gray-500">Active Analyses</div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        {/* Active Analyses Tab */}
        <TabsContent value="active" className="space-y-4">
          {activeAnalyses.size > 0 ? (
            <div className="space-y-3">
              {Array.from(activeAnalyses.values()).map((analysis) => (
                <Card key={analysis.analysisId}>
                  <CardContent className="aone-spacing-md">
                    <div className="aone-flex-between mb-2">
                      <div className="font-aone-medium">Analysis {analysis.analysisId.slice(-8)}</div>
                      <Badge variant="outline">{analysis.step}</Badge>
                    </div>
                    <Progress value={analysis.progress} className="mb-2" />
                    <div className="aone-flex-between text-aone-sm text-gray-500">
                      <span>{analysis.message}</span>
                      {analysis.confidence && (
                        <span>Confidence: {Math.round(analysis.confidence * 100)}%</span>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-aone-xl text-center">
                <Eye className="w-12 h-12 text-gray-400 mx-auto mb-aone-md" />
                <p className="text-gray-500">No active analyses</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Performance Tab (Admin/Enhanced mode) */}
        {(isAdminMode || showPerformanceMetrics) && (
          <TabsContent value="performance" className="space-y-4">
            {performanceMetrics && (
              <div className="grid grid-cols-2 md:grid-cols-3 gap-aone-md">
                <Card>
                  <CardContent className="aone-spacing-md">
                    <div className="text-center">
                      <div className="text-aone-2xl font-aone-bold text-red-600">{performanceMetrics.cpuUsage.toFixed(1)}%</div>
                      <div className="text-aone-sm text-gray-500">CPU Usage</div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="aone-spacing-md">
                    <div className="text-center">
                      <div className="text-aone-2xl font-aone-bold text-yellow-600">{performanceMetrics.memoryUsage.toFixed(1)}%</div>
                      <div className="text-aone-sm text-gray-500">Memory Usage</div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="aone-spacing-md">
                    <div className="text-center">
                      <div className="text-aone-2xl font-aone-bold text-aone-sage">{formatUptime(performanceMetrics.serverUptime)}</div>
                      <div className="text-aone-sm text-gray-500">Uptime</div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="aone-spacing-md">
                    <div className="text-center">
                      <div className="text-aone-2xl font-aone-bold text-green-600">{performanceMetrics.averageResponseTime.toFixed(0)}ms</div>
                      <div className="text-aone-sm text-gray-500">Avg Response</div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="aone-spacing-md">
                    <div className="text-center">
                      <div className="text-aone-2xl font-aone-bold text-purple-600">{performanceMetrics.errorRate.toFixed(1)}%</div>
                      <div className="text-aone-sm text-gray-500">Error Rate</div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="aone-spacing-md">
                    <div className="text-center">
                      <div className="text-aone-2xl font-aone-bold text-indigo-600">{performanceMetrics.activeConnections}</div>
                      <div className="text-aone-sm text-gray-500">Active Connections</div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
};

export default EnhancedAnalysisMonitor;
