import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  BarChart3, 
  Download, 
  Star, 
  TrendingUp, 
  CheckCircle, 
  X, 
  ArrowRight,
  Award,
  DollarSign,
  Clock,
  Shield,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { Quote } from '@/services/quotationService';
import { 
  quoteComparisonService, 
  ComparisonData, 
  ComparisonFeature, 
  ValueProposition,
  ComparisonExportOptions 
} from '@/services/quoteComparisonService';

interface QuoteComparisonToolProps {
  quote: Quote;
  onExport?: (format: string) => void;
}

export const QuoteComparisonTool: React.FC<QuoteComparisonToolProps> = ({
  quote,
  onExport
}) => {
  const [comparisonData, setComparisonData] = useState<ComparisonData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [exporting, setExporting] = useState(false);
  const [selectedTier, setSelectedTier] = useState<'basic' | 'premium' | 'luxury'>('premium');

  useEffect(() => {
    generateComparison();
  }, [quote.id]);

  const generateComparison = async () => {
    try {
      setLoading(true);
      setError(null);

      // Generate comparison data from the quote
      const data = quoteComparisonService.createComparisonData(quote);
      setComparisonData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate comparison');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (format: 'pdf' | 'excel' | 'csv') => {
    if (!comparisonData) return;

    try {
      setExporting(true);
      
      const options: ComparisonExportOptions = {
        format,
        include_features: true,
        include_pricing: true,
        include_recommendations: true,
        template: 'comparison'
      };

      const result = await quoteComparisonService.exportComparison(quote.id, options);
      
      // Trigger download
      const link = document.createElement('a');
      link.href = result.download_url;
      link.download = result.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      if (onExport) {
        onExport(format);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to export comparison');
    } finally {
      setExporting(false);
    }
  };

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'basic': return <DollarSign className="h-4 w-4" />;
      case 'premium': return <Star className="h-4 w-4" />;
      case 'luxury': return <Award className="h-4 w-4" />;
      default: return <BarChart3 className="h-4 w-4" />;
    }
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'basic': return 'border-blue-200 bg-blue-50';
      case 'premium': return 'border-purple-200 bg-purple-50';
      case 'luxury': return 'border-amber-200 bg-amber-50';
      default: return 'border-border bg-muted';
    }
  };

  const getFeatureValue = (feature: ComparisonFeature, tier: 'basic' | 'premium' | 'luxury') => {
    const value = feature[tier];
    
    if (typeof value === 'boolean') {
      return value ? <CheckCircle className="h-4 w-4 text-green-500" /> : <X className="h-4 w-4 text-gray-400" />;
    }
    
    return <span className="text-aone-sm">{value}</span>;
  };

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-muted text-foreground';
    }
  };

  const renderFeatureComparison = () => {
    if (!comparisonData) return null;

    const categories = ['materials', 'hardware', 'installation', 'warranty', 'design', 'timeline'];
    
    return (
      <div className="space-y-6">
        {categories.map(category => {
          const categoryFeatures = comparisonData.features.filter(f => f.category === category);
          if (categoryFeatures.length === 0) return null;

          return (
            <Card key={category}>
              <CardHeader>
                <CardTitle className="capitalize text-aone-lg">{category}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2 pr-4">Feature</th>
                        <th className="text-center py-2 px-aone-md min-w-[120px]">
                          <div className="aone-flex-center">
                            {getTierIcon('basic')}
                            <span className="ml-1">Basic</span>
                          </div>
                        </th>
                        <th className="text-center py-2 px-aone-md min-w-[120px]">
                          <div className="aone-flex-center">
                            {getTierIcon('premium')}
                            <span className="ml-1">Premium</span>
                            <Badge variant="secondary" className="ml-1 text-xs">Recommended</Badge>
                          </div>
                        </th>
                        <th className="text-center py-2 px-aone-md min-w-[120px]">
                          <div className="aone-flex-center">
                            {getTierIcon('luxury')}
                            <span className="ml-1">Luxury</span>
                          </div>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {categoryFeatures.map((feature, index) => (
                        <tr key={index} className="border-b hover:bg-muted">
                          <td className="py-aone-sm pr-4">
                            <div className="flex items-center">
                              <span className="font-aone-medium">{feature.feature}</span>
                              <Badge 
                                variant="outline" 
                                className={`ml-2 text-xs ${getImportanceColor(feature.importance)}`}
                              >
                                {feature.importance}
                              </Badge>
                            </div>
                            {feature.description && (
                              <p className="text-xs text-gray-500 mt-1">{feature.description}</p>
                            )}
                          </td>
                          <td className="py-aone-sm px-aone-md text-center">
                            {getFeatureValue(feature, 'basic')}
                          </td>
                          <td className="py-aone-sm px-aone-md text-center bg-purple-25">
                            {getFeatureValue(feature, 'premium')}
                          </td>
                          <td className="py-aone-sm px-aone-md text-center">
                            {getFeatureValue(feature, 'luxury')}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    );
  };

  const renderValuePropositions = () => {
    if (!comparisonData) return null;

    return (
      <div className="grid gap-aone-md md:grid-cols-3">
        {comparisonData.value_propositions.map((proposition, index) => (
          <Card 
            key={index} 
            className={`relative ${getTierColor(proposition.tier)} ${proposition.recommended ? 'ring-2 ring-purple-500' : ''}`}
          >
            {proposition.recommended && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-purple-500 text-white">
                  <Star className="h-3 w-3 mr-1" />
                  Recommended
                </Badge>
              </div>
            )}
            
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-aone-lg">
                {getTierIcon(proposition.tier)}
                <span className="ml-2 capitalize">{proposition.tier}</span>
                {proposition.best_value && (
                  <Badge variant="secondary" className="ml-2 text-xs">Best Value</Badge>
                )}
              </CardTitle>
              <CardDescription className="font-aone-medium text-base">
                {proposition.title}
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              <p className="text-aone-sm text-muted-foreground mb-aone-md">{proposition.description}</p>
              
              {proposition.upgrade_cost && (
                <div className="flex items-center text-aone-sm text-gray-500 mb-2">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  <span>Upgrade cost: {proposition.upgrade_cost}</span>
                </div>
              )}
              
              {proposition.savings && (
                <div className="flex items-center text-aone-sm text-green-600 mb-2">
                  <DollarSign className="h-4 w-4 mr-1" />
                  <span>Savings: {proposition.savings}</span>
                </div>
              )}
              
              <Button 
                className="w-full mt-4" 
                variant={proposition.recommended ? 'default' : 'outline'}
                onClick={() => setSelectedTier(proposition.tier)}
              >
                {selectedTier === proposition.tier ? 'Selected' : 'Select This Option'}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const renderSummary = () => {
    if (!comparisonData) return null;

    const { summary, recommendations } = comparisonData;

    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Investment Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-aone-md">
              <div className="text-center aone-spacing-md bg-blue-50 rounded-aone-lg">
                <div className="text-aone-2xl font-aone-bold text-aone-sage-dark">
                  {summary.price_range.currency} ${summary.price_range.min.toLocaleString()}
                </div>
                <div className="text-aone-sm text-aone-sage">Starting Price</div>
              </div>
              <div className="text-center aone-spacing-md bg-purple-50 rounded-aone-lg">
                <div className="text-aone-2xl font-aone-bold text-purple-700">
                  {summary.price_range.currency} ${summary.price_range.max.toLocaleString()}
                </div>
                <div className="text-aone-sm text-purple-600">Premium Option</div>
              </div>
              <div className="text-center aone-spacing-md bg-green-50 rounded-aone-lg">
                <div className="text-aone-2xl font-aone-bold text-green-700">
                  {summary.savings_potential.split(' ')[2]}
                </div>
                <div className="text-aone-sm text-green-600">Potential Savings</div>
              </div>
              <div className="text-center aone-spacing-md bg-orange-50 rounded-aone-lg">
                <div className="text-aone-2xl font-aone-bold text-orange-700">
                  {quote.summary.estimatedTimeframe}
                </div>
                <div className="text-aone-sm text-orange-600">Timeline</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Award className="h-5 w-5 mr-2" />
              Our Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-aone-md md:grid-cols-3">
              <div className="aone-spacing-md border rounded-aone-lg">
                <div className="flex items-center mb-2">
                  <Star className="h-4 w-4 text-yellow-500 mr-2" />
                  <span className="font-aone-medium">Best Value</span>
                </div>
                <div className="text-aone-lg font-bold capitalize">{recommendations.best_value}</div>
                <p className="text-aone-sm text-muted-foreground">Optimal balance of features and price</p>
              </div>
              <div className="aone-spacing-md border rounded-aone-lg">
                <div className="flex items-center mb-2">
                  <TrendingUp className="h-4 w-4 text-blue-500 mr-2" />
                  <span className="font-aone-medium">Most Popular</span>
                </div>
                <div className="text-aone-lg font-bold capitalize">{recommendations.most_popular}</div>
                <p className="text-aone-sm text-muted-foreground">Chosen by 70% of our customers</p>
              </div>
              <div className="aone-spacing-md border rounded-aone-lg">
                <div className="flex items-center mb-2">
                  <Award className="h-4 w-4 text-purple-500 mr-2" />
                  <span className="font-aone-medium">Premium Choice</span>
                </div>
                <div className="text-aone-lg font-bold capitalize">{recommendations.premium_choice}</div>
                <p className="text-aone-sm text-muted-foreground">Ultimate quality and features</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ArrowRight className="h-5 w-5 mr-2" />
              Upgrade Benefits
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2">
              {summary.upgrade_benefits.map((benefit, index) => (
                <div key={index} className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span className="text-aone-sm">{benefit}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="aone-flex-center py-8">
          <Loader2 className="h-8 w-8 animate-spin mr-2" />
          <span>Generating comparison...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {error}
          <Button variant="outline" size="sm" className="ml-2" onClick={generateComparison}>
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className="w-full" data-testid="quote-comparison-tool">
      <CardHeader>
        <div className="aone-flex-between">
          <div>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Quote Comparison Tool
            </CardTitle>
            <CardDescription>
              Compare features, pricing, and value across all quote tiers
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('pdf')}
              disabled={exporting}
              data-testid="export-comparison-pdf"
            >
              {exporting ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Download className="h-4 w-4 mr-2" />
              )}
              Export PDF
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('excel')}
              disabled={exporting}
              data-testid="export-comparison-excel"
            >
              Export Excel
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="comparison" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="comparison">Feature Comparison</TabsTrigger>
            <TabsTrigger value="value">Value Propositions</TabsTrigger>
            <TabsTrigger value="summary">Summary & Recommendations</TabsTrigger>
          </TabsList>

          <TabsContent value="comparison" className="space-y-6 mt-6">
            {renderFeatureComparison()}
          </TabsContent>

          <TabsContent value="value" className="space-y-6 mt-6">
            {renderValuePropositions()}
          </TabsContent>

          <TabsContent value="summary" className="space-y-6 mt-6">
            {renderSummary()}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
