import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Copy, 
  Eye, 
  Settings, 
  Users, 
  FileText,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { quoteTemplateService, QuoteTemplate, CustomerSegment } from '@/services/quoteTemplateService';
import { QuoteTemplateForm } from './QuoteTemplateForm';
import { QuoteTemplatePreview } from './QuoteTemplatePreview';

interface QuoteTemplateManagerProps {
  onTemplateSelected?: (template: QuoteTemplate) => void;
  selectedSegmentId?: number;
}

export const QuoteTemplateManager: React.FC<QuoteTemplateManagerProps> = ({
  onTemplateSelected,
  selectedSegmentId
}) => {
  const [templates, setTemplates] = useState<QuoteTemplate[]>([]);
  const [segments, setSegments] = useState<CustomerSegment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<QuoteTemplate | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [formMode, setFormMode] = useState<'create' | 'edit' | 'duplicate'>('create');
  const [includeInactive, setIncludeInactive] = useState(false);

  useEffect(() => {
    loadData();
  }, [selectedSegmentId, includeInactive]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [templatesData, segmentsData] = await Promise.all([
        quoteTemplateService.getTemplates(includeInactive, selectedSegmentId),
        quoteTemplateService.getCustomerSegments()
      ]);

      setTemplates(templatesData);
      setSegments(segmentsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load template data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTemplate = () => {
    setSelectedTemplate(null);
    setFormMode('create');
    setShowForm(true);
  };

  const handleEditTemplate = (template: QuoteTemplate) => {
    setSelectedTemplate(template);
    setFormMode('edit');
    setShowForm(true);
  };

  const handleDuplicateTemplate = (template: QuoteTemplate) => {
    setSelectedTemplate(template);
    setFormMode('duplicate');
    setShowForm(true);
  };

  const handleDeleteTemplate = async (template: QuoteTemplate) => {
    if (!confirm(`Are you sure you want to delete template "${template.template_name}"?`)) {
      return;
    }

    try {
      await quoteTemplateService.deleteTemplate(template.id);
      await loadData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete template');
    }
  };

  const handlePreviewTemplate = (template: QuoteTemplate) => {
    setSelectedTemplate(template);
    setShowPreview(true);
  };

  const handleFormSubmit = async () => {
    setShowForm(false);
    await loadData();
  };

  const getSegmentName = (segmentId?: number) => {
    if (!segmentId) return 'No Segment';
    const segment = segments.find(s => s.id === segmentId);
    return segment ? segment.segment_name : 'Unknown Segment';
  };

  const getSegmentBadgeColor = (targetMarket: string) => {
    switch (targetMarket) {
      case 'residential': return 'bg-blue-100 text-blue-800';
      case 'commercial': return 'bg-green-100 text-green-800';
      case 'luxury': return 'bg-purple-100 text-purple-800';
      case 'budget': return 'bg-orange-100 text-orange-800';
      default: return 'bg-muted text-foreground';
    }
  };

  const filteredTemplates = templates.filter(template => {
    if (selectedSegmentId) {
      return template.customer_segment_id === selectedSegmentId;
    }
    return true;
  });

  if (loading) {
    return (
      <Card>
        <CardContent className="aone-flex-center py-8">
          <Loader2 className="h-8 w-8 animate-spin mr-2" />
          <span>Loading templates...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <div className="aone-flex-between">
            <div>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Quote Template Manager
              </CardTitle>
              <CardDescription>
                Create and manage customizable quote templates for different customer segments
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIncludeInactive(!includeInactive)}
              >
                {includeInactive ? 'Hide Inactive' : 'Show Inactive'}
              </Button>
              <Button onClick={handleCreateTemplate} data-testid="create-template-btn">
                <Plus className="h-4 w-4 mr-2" />
                Create Template
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue="templates" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="templates">Templates</TabsTrigger>
              <TabsTrigger value="segments">Customer Segments</TabsTrigger>
            </TabsList>

            <TabsContent value="templates" className="space-y-4">
              {filteredTemplates.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 mx-auto text-gray-400 mb-aone-md" />
                  <h3 className="text-aone-lg font-aone-medium text-foreground mb-2">No templates found</h3>
                  <p className="text-gray-500 mb-aone-md">
                    {selectedSegmentId 
                      ? 'No templates found for the selected customer segment.'
                      : 'Get started by creating your first quote template.'
                    }
                  </p>
                  <Button onClick={handleCreateTemplate}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Template
                  </Button>
                </div>
              ) : (
                <div className="grid gap-aone-md md:grid-cols-2 lg:grid-cols-3">
                  {filteredTemplates.map((template) => (
                    <Card 
                      key={template.id} 
                      className={`relative ${!template.is_active ? 'opacity-60' : ''}`}
                      data-testid={`template-card-${template.id}`}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <CardTitle className="text-base flex items-center">
                              {template.template_name}
                              {template.is_default && (
                                <Badge variant="secondary" className="ml-2 text-xs">
                                  Default
                                </Badge>
                              )}
                              {!template.is_active && (
                                <Badge variant="outline" className="ml-2 text-xs">
                                  Inactive
                                </Badge>
                              )}
                            </CardTitle>
                            <CardDescription className="text-aone-sm">
                              {template.description || 'No description'}
                            </CardDescription>
                          </div>
                        </div>
                        
                        {template.customer_segment && (
                          <Badge 
                            className={`w-fit text-xs ${getSegmentBadgeColor(template.customer_segment.target_market)}`}
                          >
                            <Users className="h-3 w-3 mr-1" />
                            {template.customer_segment.segment_name}
                          </Badge>
                        )}
                      </CardHeader>

                      <CardContent className="pt-0">
                        <div className="aone-flex-between">
                          <div className="text-xs text-gray-500">
                            Code: {template.template_code}
                          </div>
                          <div className="text-xs text-gray-500">
                            v{template.version}
                          </div>
                        </div>

                        <div className="flex items-center space-x-1 mt-3">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePreviewTemplate(template)}
                            data-testid={`preview-template-${template.id}`}
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditTemplate(template)}
                            data-testid={`edit-template-${template.id}`}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDuplicateTemplate(template)}
                            data-testid={`duplicate-template-${template.id}`}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteTemplate(template)}
                            className="text-red-600 hover:text-red-700"
                            data-testid={`delete-template-${template.id}`}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                          {onTemplateSelected && (
                            <Button
                              size="sm"
                              onClick={() => onTemplateSelected(template)}
                              data-testid={`select-template-${template.id}`}
                            >
                              Select
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="segments" className="space-y-4">
              <div className="grid gap-aone-md md:grid-cols-2 lg:grid-cols-3">
                {segments.map((segment) => (
                  <Card key={segment.id} data-testid={`segment-card-${segment.id}`}>
                    <CardHeader>
                      <CardTitle className="text-base flex items-center">
                        <Users className="h-4 w-4 mr-2" />
                        {segment.segment_name}
                      </CardTitle>
                      <CardDescription>{segment.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between text-aone-sm">
                          <span className="text-gray-500">Market:</span>
                          <Badge className={getSegmentBadgeColor(segment.target_market)}>
                            {segment.target_market}
                          </Badge>
                        </div>
                        <div className="flex justify-between text-aone-sm">
                          <span className="text-gray-500">Pricing Tier:</span>
                          <span className="font-aone-medium">{segment.pricing_tier}</span>
                        </div>
                        <div className="flex justify-between text-aone-sm">
                          <span className="text-gray-500">Default Markup:</span>
                          <span className="font-aone-medium">{segment.default_markup_percentage}%</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Template Form Modal */}
      {showForm && (
        <QuoteTemplateForm
          template={selectedTemplate}
          segments={segments}
          mode={formMode}
          onSubmit={handleFormSubmit}
          onCancel={() => setShowForm(false)}
        />
      )}

      {/* Template Preview Modal */}
      {showPreview && selectedTemplate && (
        <QuoteTemplatePreview
          template={selectedTemplate}
          onClose={() => setShowPreview(false)}
        />
      )}
    </div>
  );
};
