
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, FileText, ArrowRight } from "lucide-react";

const UploadSection = () => {
  const [dragActive, setDragActive] = useState(false);
  const navigate = useNavigate();

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    // Redirect to analysis page for real file upload
    navigate('/analysis');
  };

  const handleStartAnalysis = () => {
    // Redirect to analysis page for real file upload
    navigate('/analysis');
  };

  return (
    <div className="py-16 bg-aone-cream/30 dark:bg-aone-cream/20">
      <div className="aone-container">
        <div className="text-center mb-12">
          <h2 className="aone-heading-enterprise text-aone-3xl font-aone-bold mb-aone-md">
            Upload Your Kitchen Drawings
          </h2>
          <p className="aone-body-enterprise text-aone-lg max-w-2xl mx-auto">
            Support for Winner Flex and Cabinet Vision PDF drawings, plus standard formats like PNG, JPG, DWG, and SKP files.
          </p>
        </div>

        <div className="max-w-2xl mx-auto">
          <Card className="aone-card-enterprise border-2 border-dashed border-aone-sage/30 hover:border-aone-sage/50 transition-colors">
            <CardContent className="p-aone-xl">
              <div
                className={`text-center ${dragActive ? 'bg-aone-sage/10' : ''} rounded-aone-lg p-aone-xl transition-colors cursor-pointer aone-micro-interaction`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
                onClick={handleStartAnalysis}
              >
                <Upload className="w-16 h-16 text-aone-sage/70 mx-auto mb-aone-md" />
                <h3 className="aone-heading-enterprise text-aone-xl font-aone-semibold mb-2">
                  Drop your drawings here
                </h3>
                <p className="aone-body-enterprise mb-aone-md">
                  or click to start real AI analysis
                </p>
                <Button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleStartAnalysis();
                  }}
                  className="aone-button-primary-enhanced"
                >
                  Start Analysis <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>

              <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-aone-md text-center">
                <div className="flex flex-col items-center aone-micro-interaction">
                  <FileText className="w-8 h-8 text-aone-sage/60 mb-2" />
                  <span className="text-aone-sm aone-body-enterprise">PDF</span>
                </div>
                <div className="flex flex-col items-center aone-micro-interaction">
                  <FileText className="w-8 h-8 text-aone-sage/60 mb-2" />
                  <span className="text-aone-sm aone-body-enterprise">DWG</span>
                </div>
                <div className="flex flex-col items-center aone-micro-interaction">
                  <FileText className="w-8 h-8 text-aone-sage/60 mb-2" />
                  <span className="text-aone-sm aone-body-enterprise">PNG/JPG</span>
                </div>
                <div className="flex flex-col items-center aone-micro-interaction">
                  <FileText className="w-8 h-8 text-aone-sage/60 mb-2" />
                  <span className="text-aone-sm aone-body-enterprise">SKP</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default UploadSection;
