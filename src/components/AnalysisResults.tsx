import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  BarChart3,
  Package,
  Ruler,
  Wrench,
  Palette,
  Download,
  Eye,
  CheckCircle,
  AlertTriangle,
  Clock,
  Sparkles,
  Tag,
  Award
} from 'lucide-react';
import { AnalysisResults as AnalysisResultsType, CabinetAnalysis, HardwareItem } from '@/services/aiAnalysisService';
import ReportGenerator from '@/components/ReportGenerator';

interface AnalysisResultsProps {
  results: AnalysisResultsType;
  onGenerateQuote?: () => void;
  onExportResults?: () => void;
}

const AnalysisResults: React.FC<AnalysisResultsProps> = ({
  results,
  onGenerateQuote,
  onExportResults,
}) => {
  const [selectedCabinet, setSelectedCabinet] = useState<CabinetAnalysis | null>(null);

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'text-status-success';
    if (confidence >= 0.7) return 'text-status-warning';
    return 'text-status-error';
  };

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 0.9) return <Badge className="confidence-high">High Confidence</Badge>;
    if (confidence >= 0.7) return <Badge className="confidence-medium">Medium Confidence</Badge>;
    return <Badge className="confidence-low">Low Confidence</Badge>;
  };

  const formatDimensions = (dimensions: { width: number; height: number; depth: number }) => {
    return `${dimensions.width}mm × ${dimensions.height}mm × ${dimensions.depth}mm`;
  };

  const formatProcessingTime = (seconds: number) => {
    if (seconds < 60) return `${seconds.toFixed(1)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
  };

  return (
    <div className="space-y-6">
      {/* Header with Summary */}
      <Card>
        <CardHeader>
          <div className="aone-flex-between">
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Analysis Results
            </CardTitle>
            <div className="flex items-center gap-2">
              {getConfidenceBadge(results.confidence.overall)}
              <Badge variant="outline" className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                {formatProcessingTime(results.processingTime)}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-aone-md">
            <div className="cabinet-metric-primary">
              <div className="text-aone-2xl font-aone-bold text-aone-sage">{results.measurements.totalCabinets}</div>
              <div className="text-aone-sm text-muted-foreground">Total Cabinets</div>
            </div>
            <div className="cabinet-metric-secondary">
              <div className="text-aone-2xl font-aone-bold text-status-success">{results.measurements.totalLinearMeters}m</div>
              <div className="text-aone-sm text-muted-foreground">Linear Meters</div>
            </div>
            <div className="cabinet-metric-accent">
              <div className="text-aone-2xl font-aone-bold text-status-info">{results.hardware.length}</div>
              <div className="text-aone-sm text-muted-foreground">Hardware Types</div>
            </div>
            <div className="cabinet-metric text-center">
              <div className={`text-aone-2xl font-aone-bold ${getConfidenceColor(results.confidence.overall)}`}>
                {Math.round(results.confidence.overall * 100)}%
              </div>
              <div className="text-aone-sm text-muted-foreground">Confidence</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Results Tabs */}
      <Tabs defaultValue="cabinets" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="cabinets" className="flex items-center gap-2">
            <Package className="w-4 h-4" />
            Cabinets
          </TabsTrigger>
          <TabsTrigger value="style" className="flex items-center gap-2">
            <Sparkles className="w-4 h-4" />
            Style Analysis
          </TabsTrigger>
          <TabsTrigger value="hardware" className="flex items-center gap-2">
            <Wrench className="w-4 h-4" />
            Hardware
          </TabsTrigger>
          <TabsTrigger value="measurements" className="flex items-center gap-2">
            <Ruler className="w-4 h-4" />
            Measurements
          </TabsTrigger>
          <TabsTrigger value="materials" className="flex items-center gap-2">
            <Palette className="w-4 h-4" />
            Materials
          </TabsTrigger>
          <TabsTrigger value="reports" className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Reports
          </TabsTrigger>
        </TabsList>

        {/* Cabinets Tab */}
        <TabsContent value="cabinets" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cabinet Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-aone-md">
                {Object.entries(results.measurements.cabinetsByType).map(([type, count]) => (
                  <div key={type} className="aone-flex-between aone-spacing-sm bg-muted/50 rounded-aone-lg">
                    <span className="font-aone-medium text-foreground">{type.replace('_', ' ')} Cabinets</span>
                    <Badge variant="secondary">{count}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Individual Cabinets</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3">
                {results.cabinets.map((cabinet) => (
                  <div
                    key={cabinet.id}
                    className={`aone-card-interactive aone-spacing-md aone-micro-interaction ${
                      selectedCabinet?.id === cabinet.id ? 'border-aone-sage bg-aone-sage/5 ring-2 ring-aone-sage/20' : 'hover:border-aone-sage/30'
                    }`}
                    onClick={() => setSelectedCabinet(cabinet)}
                  >
                    <div className="aone-flex-between">
                      <div>
                        <h4 className="font-aone-medium">{cabinet.type} Cabinet</h4>
                        <p className="text-aone-sm text-gray-500">{formatDimensions(cabinet.dimensions)}</p>
                        {cabinet.style && cabinet.style.primary !== 'UNKNOWN' && (
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="secondary" className="text-xs">
                              {cabinet.style.primary.replace('_', ' ')}
                            </Badge>
                            {cabinet.style.confidence && (
                              <span className={`text-xs ${getConfidenceColor(cabinet.style.confidence)}`}>
                                {Math.round(cabinet.style.confidence * 100)}%
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                      <div className="text-right">
                        <div className={`text-aone-sm font-aone-medium ${getConfidenceColor(cabinet.confidence)}`}>
                          {Math.round(cabinet.confidence * 100)}%
                        </div>
                        {cabinet.materialFinish && cabinet.materialFinish.type !== 'UNKNOWN' ? (
                          <div className="space-y-1">
                            <Badge variant="outline" size="sm">
                              {cabinet.materialFinish.type} - {cabinet.materialFinish.color}
                            </Badge>
                            {cabinet.materialFinish.brand && (
                              <div className="text-xs text-gray-500">
                                {cabinet.materialFinish.brand}
                              </div>
                            )}
                          </div>
                        ) : (
                          <Badge variant="outline" size="sm">
                            {cabinet.materials.join(', ')}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Style Analysis Tab - Priority 1 Enhancement */}
        <TabsContent value="style" className="space-y-4">
          {results.styleAnalysis && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Award className="w-5 h-5" />
                    Dominant Style Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="aone-card-enterprise aone-spacing-md bg-gradient-to-r from-aone-sage/10 to-aone-sage/5">
                      <div>
                        <h3 className="text-aone-lg font-aone-semibold text-aone-charcoal">
                          {results.styleAnalysis.dominantStyle.primary.replace('_', ' ')}
                        </h3>
                        {results.styleAnalysis.dominantStyle.secondary && (
                          <p className="text-aone-sm text-aone-charcoal/70">
                            with {results.styleAnalysis.dominantStyle.secondary} influences
                          </p>
                        )}
                        {results.styleAnalysis.designEra && (
                          <p className="text-xs text-muted-foreground mt-1">
                            Design Era: {results.styleAnalysis.designEra}
                          </p>
                        )}
                      </div>
                      <div className="text-right">
                        <div className={`text-aone-lg font-bold ${getConfidenceColor(results.styleAnalysis.dominantStyle.confidence)}`}>
                          {Math.round(results.styleAnalysis.dominantStyle.confidence * 100)}%
                        </div>
                        <div className="text-xs text-gray-500">Confidence</div>
                      </div>
                    </div>

                    {results.styleAnalysis.dominantStyle.characteristics && results.styleAnalysis.dominantStyle.characteristics.length > 0 && (
                      <div>
                        <h4 className="font-aone-medium mb-2">Key Characteristics</h4>
                        <div className="flex flex-wrap gap-2">
                          {results.styleAnalysis.dominantStyle.characteristics.map((characteristic, index) => (
                            <Badge key={index} variant="secondary" className="bg-aone-sage/10 text-aone-sage border-aone-sage/20">
                              {characteristic}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="font-aone-medium">Style Consistency</span>
                        <span className="font-aone-medium">{Math.round(results.styleAnalysis.styleConsistency * 100)}%</span>
                      </div>
                      <Progress value={results.styleAnalysis.styleConsistency * 100} className="h-2" />
                      <p className="text-xs text-gray-500 mt-1">
                        {results.styleAnalysis.styleConsistency >= 0.8
                          ? 'Highly consistent style throughout'
                          : results.styleAnalysis.styleConsistency >= 0.6
                          ? 'Moderately consistent with some variations'
                          : 'Mixed styles detected - consider style unification'
                        }
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {results.styleAnalysis.mixedStyles && results.styleAnalysis.mixedStyles.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Tag className="w-5 h-5" />
                      Mixed Styles Detected
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-3">
                      {results.styleAnalysis.mixedStyles.map((style, index) => (
                        <div key={index} className="aone-flex-between aone-spacing-sm border rounded-aone-lg">
                          <div>
                            <h4 className="font-aone-medium">{style.primary.replace('_', ' ')}</h4>
                            {style.characteristics && style.characteristics.length > 0 && (
                              <p className="text-aone-sm text-gray-500">
                                {style.characteristics.slice(0, 2).join(', ')}
                              </p>
                            )}
                          </div>
                          <div className={`text-aone-sm font-aone-medium ${getConfidenceColor(style.confidence)}`}>
                            {Math.round(style.confidence * 100)}%
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {results.styleAnalysis.recommendations && results.styleAnalysis.recommendations.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="w-5 h-5" />
                      Style Recommendations
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {results.styleAnalysis.recommendations.map((recommendation, index) => (
                        <div key={index} className="aone-spacing-sm bg-status-success/10 border border-status-success/20 rounded-aone-lg">
                          <p className="text-aone-sm text-status-success">{recommendation}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          )}

          {!results.styleAnalysis && (
            <Card>
              <CardContent className="text-center py-8">
                <Sparkles className="w-12 h-12 text-gray-400 mx-auto mb-aone-md" />
                <p className="text-gray-500">Style analysis not available for this analysis.</p>
                <p className="text-aone-sm text-gray-400 mt-2">
                  Enhanced style analysis is available with the latest analysis engine.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Hardware Tab */}
        <TabsContent value="hardware" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Hardware Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-aone-md">
                {results.hardware.map((item, index) => (
                  <div key={index} className="aone-spacing-md border rounded-aone-lg space-y-3">
                    <div className="aone-flex-between">
                      <div>
                        <h4 className="font-aone-medium">{item.type.replace('_', ' ')}</h4>
                        <p className="text-aone-sm text-gray-500">
                          Quantity: {item.quantity}
                          {item.specifications && Object.keys(item.specifications).length > 0 && (
                            <span className="ml-2">
                              • {Object.entries(item.specifications).map(([key, value]) =>
                                `${key}: ${value}`
                              ).join(', ')}
                            </span>
                          )}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className={`text-aone-sm font-aone-medium ${getConfidenceColor(item.confidence)}`}>
                          {Math.round(item.confidence * 100)}%
                        </div>
                        {item.estimatedCost && (
                          <div className="text-aone-sm text-gray-500">
                            ${item.estimatedCost.toFixed(2)}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Enhanced Hardware Information */}
                    <div className="grid grid-cols-2 gap-aone-md pt-2 border-t">
                      <div>
                        {item.brand && (
                          <div className="mb-2">
                            <span className="text-xs font-aone-medium text-muted-foreground">Brand:</span>
                            <Badge variant="outline" className="ml-2">{item.brand}</Badge>
                            {item.model && (
                              <span className="text-xs text-gray-500 ml-2">Model: {item.model}</span>
                            )}
                          </div>
                        )}
                        {item.finish && (
                          <div className="mb-2">
                            <span className="text-xs font-aone-medium text-muted-foreground">Finish:</span>
                            <span className="text-xs text-foreground ml-2">{item.finish}</span>
                          </div>
                        )}
                        {item.style && item.style !== 'UNKNOWN' && (
                          <div>
                            <span className="text-xs font-aone-medium text-muted-foreground">Style:</span>
                            <Badge variant="secondary" className="ml-2 text-xs">
                              {item.style.replace('_', ' ')}
                            </Badge>
                          </div>
                        )}
                      </div>

                      <div>
                        {item.compatibility && (
                          <div className="space-y-2">
                            <div>
                              <span className="text-xs font-aone-medium text-muted-foreground">Installation:</span>
                              <Badge
                                variant={item.compatibility.installationComplexity === 'LOW' ? 'default' :
                                        item.compatibility.installationComplexity === 'MEDIUM' ? 'secondary' : 'destructive'}
                                className="ml-2 text-xs"
                              >
                                {item.compatibility.installationComplexity}
                              </Badge>
                            </div>
                            {item.compatibility.cabinetStyles && item.compatibility.cabinetStyles.length > 0 && (
                              <div>
                                <span className="text-xs font-aone-medium text-muted-foreground">Compatible Styles:</span>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {item.compatibility.cabinetStyles.slice(0, 3).map((style, styleIndex) => (
                                    <Badge key={styleIndex} variant="outline" className="text-xs">
                                      {style}
                                    </Badge>
                                  ))}
                                  {item.compatibility.cabinetStyles.length > 3 && (
                                    <span className="text-xs text-gray-500">
                                      +{item.compatibility.cabinetStyles.length - 3} more
                                    </span>
                                  )}
                                </div>
                              </div>
                            )}
                            {item.compatibility.notes && (
                              <p className="text-xs text-gray-500 italic">
                                {item.compatibility.notes}
                              </p>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Measurements Tab */}
        <TabsContent value="measurements" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Measurement Accuracy</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-2">
                    <span>Cabinet Count Accuracy</span>
                    <span className="font-aone-medium">{Math.round(results.confidence.cabinetCount * 100)}%</span>
                  </div>
                  <Progress value={results.confidence.cabinetCount * 100} />
                </div>
                <div>
                  <div className="flex justify-between mb-2">
                    <span>Cabinet Type Classification</span>
                    <span className="font-aone-medium">{Math.round(results.confidence.cabinetTypes * 100)}%</span>
                  </div>
                  <Progress value={results.confidence.cabinetTypes * 100} />
                </div>
                <div>
                  <div className="flex justify-between mb-2">
                    <span>Hardware Detection</span>
                    <span className="font-aone-medium">{Math.round(results.confidence.hardware * 100)}%</span>
                  </div>
                  <Progress value={results.confidence.hardware * 100} />
                </div>
                <div>
                  <div className="flex justify-between mb-2">
                    <span>Measurement Precision</span>
                    <span className="font-aone-medium">{Math.round(results.confidence.measurements * 100)}%</span>
                  </div>
                  <Progress value={results.confidence.measurements * 100} />
                </div>
                {results.confidence.styleClassification && (
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>Style Classification</span>
                      <span className="font-aone-medium">{Math.round(results.confidence.styleClassification * 100)}%</span>
                    </div>
                    <Progress value={results.confidence.styleClassification * 100} />
                  </div>
                )}
                {results.confidence.materialIdentification && (
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>Material Identification</span>
                      <span className="font-aone-medium">{Math.round(results.confidence.materialIdentification * 100)}%</span>
                    </div>
                    <Progress value={results.confidence.materialIdentification * 100} />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Key Measurements</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-aone-md">
                <div className="cabinet-metric-secondary">
                  <div className="text-aone-sm text-muted-foreground">Total Linear Meters</div>
                  <div className="text-aone-xl font-bold text-status-success">{results.measurements.totalLinearMeters}m</div>
                </div>
                <div className="cabinet-metric-primary">
                  <div className="text-aone-sm text-muted-foreground">Total Cabinets</div>
                  <div className="text-aone-xl font-bold text-aone-sage">{results.measurements.totalCabinets}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Materials Tab */}
        <TabsContent value="materials" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Material Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-aone-md">
                <div>
                  <h4 className="font-aone-medium mb-2">Finishes</h4>
                  <div className="flex flex-wrap gap-2">
                    {results.materials.finishes.map((finish, index) => (
                      <Badge key={index} variant="secondary">{finish}</Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="font-aone-medium mb-2">Colors</h4>
                  <div className="flex flex-wrap gap-2">
                    {results.materials.colors.map((color, index) => (
                      <Badge key={index} variant="outline">{color}</Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="font-aone-medium mb-2">Textures</h4>
                  <div className="flex flex-wrap gap-2">
                    {results.materials.textures.map((texture, index) => (
                      <Badge key={index} variant="secondary">{texture}</Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Reports Tab */}
        <TabsContent value="reports" className="space-y-4">
          <ReportGenerator
            analysisResults={results}
            onReportGenerated={(reportId) => {
              console.log('Report generated:', reportId);
            }}
          />
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="flex justify-center gap-aone-md">
        <Button onClick={onGenerateQuote} className="flex items-center gap-2">
          <BarChart3 className="w-4 h-4" />
          Generate Quote
        </Button>
        <Button variant="outline" onClick={onExportResults} className="flex items-center gap-2">
          <Download className="w-4 h-4" />
          Export Results
        </Button>
      </div>

      {/* Errors Display */}
      {results.errors && results.errors.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-status-warning">
              <AlertTriangle className="w-5 h-5" />
              Analysis Warnings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {results.errors.map((error, index) => (
                <div key={index} className="aone-spacing-sm bg-status-warning/10 border border-status-warning/20 rounded-aone-lg">
                  <p className="text-aone-sm text-status-warning">{error}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AnalysisResults;
