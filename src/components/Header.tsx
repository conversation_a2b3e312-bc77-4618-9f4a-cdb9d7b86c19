
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Upload, Users, BarChart3, Setting<PERSON> } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import PWAInstallPrompt from "./PWAInstallPrompt";
import AOneBanner from "./AOneBanner";
import { useIsMobile } from "@/hooks/use-mobile";
import { ThemeToggle } from "./ui/theme-toggle";

const Header = () => {
  const location = useLocation();
  const isMobile = useIsMobile();

  const isActive = (path: string) => location.pathname === path;

  return (
    <>
      <AOneBanner message="Revolutionize your kitchen design workflow with Blackveil Design Mind's AI-powered insights. Start your free analysis today!" />
      <header className="aone-header sticky top-0 z-50 animate-slide-in-elegant">
        <div className="aone-container lg:px-8 py-6">
          <div className="aone-flex-between">
            <Link to="/" className="group flex items-center space-x-4 aone-micro-interaction">
              <div className="w-12 h-12 bg-gradient-to-br from-aone-sage to-aone-sage-light rounded-aone-xl aone-flex-center shadow-lg group-hover:shadow-xl transition-shadow duration-200 animate-glow-sage">
                <BarChart3 className="w-7 h-7 text-white" />
              </div>
              <div className="flex flex-col">
                <div className="aone-logo text-2xl lg:text-3xl">BLACKVEIL DESIGN MIND</div>
                <span className="aone-logo-accent">AI KITCHEN ANALYSIS</span>
              </div>
            </Link>
          
          <nav className="hidden lg:flex items-center space-x-2">
            <Link
              to="/"
              className={`aone-nav-link ${isActive('/') ? 'aone-nav-link-active aone-nav-enterprise-active' : 'aone-nav-enterprise'} relative`}
            >
              Home
              {isActive('/') && <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-aone-sage rounded-full animate-slide-in-elegant"></div>}
            </Link>
            <Link
              to="/analysis"
              className={`aone-nav-link ${isActive('/analysis') ? 'aone-nav-link-active aone-nav-enterprise-active' : 'aone-nav-enterprise'} relative`}
            >
              AI Analysis
              {isActive('/analysis') && <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-aone-sage rounded-full animate-slide-in-elegant"></div>}
            </Link>
            <Link
              to="/features"
              className={`aone-nav-link ${isActive('/features') ? 'aone-nav-link-active aone-nav-enterprise-active' : 'aone-nav-enterprise'} relative`}
            >
              Features
              {isActive('/features') && <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-aone-sage rounded-full animate-slide-in-elegant"></div>}
            </Link>
            <Link
              to="/projects"
              className={`aone-nav-link ${isActive('/projects') ? 'aone-nav-link-active aone-nav-enterprise-active' : 'aone-nav-enterprise'} relative`}
            >
              Collaboration
              {isActive('/projects') && <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-aone-sage rounded-full animate-slide-in-elegant"></div>}
            </Link>
            <Link
              to="/performance"
              className={`aone-nav-link ${isActive('/performance') ? 'aone-nav-link-active aone-nav-enterprise-active' : 'aone-nav-enterprise'} relative`}
            >
              Performance
              {isActive('/performance') && <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-aone-sage rounded-full animate-slide-in-elegant"></div>}
            </Link>
          </nav>

          <div className="flex items-center space-x-3 lg:space-x-4">
            {/* Theme Toggle - Advanced Theme System with Presets & Accessibility */}
            <ThemeToggle
              variant="ghost"
              size="icon"
              showPresets={true}
              showAccessibility={true}
            />

            {/* PWA Install Button in Header */}
            <PWAInstallPrompt showInHeader={true} autoShow={false} />

            <Button
              variant="outline"
              size="sm"
              className={`aone-button-secondary text-aone-sm px-aone-md py-2 ${isMobile ? "hidden" : ""}`}
            >
              <Users className="w-4 h-4 mr-2" />
              Design Partners
            </Button>
            <Link to="/analysis" className="group">
              <Button
                size="sm"
                className="aone-button-primary text-aone-sm px-6 py-2 touch-manipulation group-aone-hover-lift transition-transform duration-200"
              >
                <Upload className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-200" />
                {isMobile ? "Analyze" : "Start Analysis"}
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </header>
    </>
  );
};

export default Header;
