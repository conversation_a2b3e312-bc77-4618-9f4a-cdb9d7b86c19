import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Wifi, 
  WifiOff, 
  Activity, 
  CheckCircle, 
  AlertTriangle, 
  Clock,
  Zap,
  Server,
  Eye,
  RefreshCw
} from 'lucide-react';

interface AnalysisProgress {
  analysisId: string;
  progress: number;
  step: string;
  message: string;
  confidence?: number;
}

interface ConnectionMetrics {
  connectedClients: number;
  queueLength: number;
  processingCount: number;
  activeAnalyses: number;
  lastUpdate: number;
}

interface EnhancedWebSocketStatusProps {
  className?: string;
  showDetailedMetrics?: boolean;
}

const EnhancedWebSocketStatus: React.FC<EnhancedWebSocketStatusProps> = ({
  className = '',
  showDetailedMetrics = false
}) => {
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');
  const [metrics, setMetrics] = useState<ConnectionMetrics | null>(null);
  const [activeAnalyses, setActiveAnalyses] = useState<Map<string, AnalysisProgress>>(new Map());
  const [lastHeartbeat, setLastHeartbeat] = useState<number>(Date.now());
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const socketRef = useRef<any>(null);

  // Initialize WebSocket connection with enhanced error handling
  useEffect(() => {
    const initializeSocket = async () => {
      try {
        setConnectionStatus('connecting');
        const { io } = await import('socket.io-client');
        const socketUrl = import.meta.env.VITE_SOCKET_URL || 'http://localhost:3001';
        
        socketRef.current = io(socketUrl, {
          transports: ['websocket', 'polling'],
          timeout: 10000,
          retries: 3,
          reconnection: true,
          reconnectionDelay: 1000,
          reconnectionAttempts: 5
        });

        setupSocketListeners();
        
      } catch (error) {
        console.error('Failed to initialize socket:', error);
        setConnectionStatus('disconnected');
      }
    };

    initializeSocket();

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, []);

  // Setup socket event listeners with enhanced monitoring
  const setupSocketListeners = useCallback(() => {
    const socket = socketRef.current;
    if (!socket) return;

    socket.on('connect', () => {
      setConnectionStatus('connected');
      setReconnectAttempts(0);
      setLastHeartbeat(Date.now());
      console.log('Enhanced WebSocket connected');
    });

    socket.on('disconnect', () => {
      setConnectionStatus('disconnected');
      console.log('Enhanced WebSocket disconnected');
    });

    socket.on('connect_error', (error: any) => {
      setConnectionStatus('disconnected');
      setReconnectAttempts(prev => prev + 1);
      console.warn('Enhanced WebSocket connection error:', error);
    });

    socket.on('reconnect', (attemptNumber: number) => {
      setConnectionStatus('connected');
      setReconnectAttempts(0);
      console.log(`Enhanced WebSocket reconnected after ${attemptNumber} attempts`);
    });

    // Listen for queue status updates
    socket.on('queue-status', (status: ConnectionMetrics) => {
      setMetrics(status);
      setLastHeartbeat(Date.now());
    });

    // Listen for analysis progress updates
    socket.on('analysis-progress', (progress: AnalysisProgress) => {
      setActiveAnalyses(prev => {
        const updated = new Map(prev);
        updated.set(progress.analysisId, progress);
        return updated;
      });
      setLastHeartbeat(Date.now());
    });

    // Listen for analysis completion
    socket.on('analysis-complete', (data: { analysisId: string }) => {
      setActiveAnalyses(prev => {
        const updated = new Map(prev);
        updated.delete(data.analysisId);
        return updated;
      });
    });

    // Heartbeat monitoring
    socket.on('heartbeat', () => {
      setLastHeartbeat(Date.now());
    });
  }, []);

  // Manual reconnection
  const handleReconnect = () => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current.connect();
    }
  };

  // Get connection status badge
  const getConnectionBadge = () => {
    switch (connectionStatus) {
      case 'connected':
        return (
          <Badge className="status-connected">
            <Wifi className="w-3 h-3 mr-1" />
            Connected
          </Badge>
        );
      case 'connecting':
        return (
          <Badge className="status-processing">
            <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
            Connecting...
          </Badge>
        );
      default:
        return (
          <Badge className="status-disconnected">
            <WifiOff className="w-3 h-3 mr-1" />
            Disconnected
          </Badge>
        );
    }
  };

  // Get system health indicator
  const getSystemHealth = () => {
    if (connectionStatus !== 'connected' || !metrics) return 'unknown';
    
    const timeSinceLastUpdate = Date.now() - lastHeartbeat;
    if (timeSinceLastUpdate > 30000) return 'warning'; // 30 seconds
    if (metrics.queueLength > 10) return 'warning';
    if (metrics.processingCount > 5) return 'warning';
    
    return 'healthy';
  };

  const systemHealth = getSystemHealth();

  // Format time since last update
  const formatTimeSince = (timestamp: number): string => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);
    if (seconds < 60) return `${seconds}s ago`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ago`;
  };

  return (
    <div className={`space-md ${className}`}>
      {/* Connection Status Header */}
      <Card className="cabinet-card">
        <CardHeader className="pb-3">
          <div className="aone-flex-between">
            <CardTitle className="flex items-center gap-2 text-aone-sm">
              <Activity className="w-4 h-4" />
              Real-time Status
            </CardTitle>
            <div className="flex items-center gap-2">
              {getConnectionBadge()}
              {connectionStatus === 'disconnected' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReconnect}
                  className="focus-cabinet"
                >
                  <RefreshCw className="w-3 h-3 mr-1" />
                  Retry
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        {connectionStatus === 'connected' && (
          <CardContent className="pt-0">
            <div className="aone-flex-between text-xs text-gray-500">
              <span>Last update: {formatTimeSince(lastHeartbeat)}</span>
              {systemHealth !== 'unknown' && (
                <div className="flex items-center gap-1">
                  {systemHealth === 'healthy' ? (
                    <CheckCircle className="w-3 h-3 text-green-500" />
                  ) : (
                    <AlertTriangle className="w-3 h-3 text-yellow-500" />
                  )}
                  <span className="capitalize">{systemHealth}</span>
                </div>
              )}
            </div>
          </CardContent>
        )}
      </Card>

      {/* Active Analyses */}
      {activeAnalyses.size > 0 && (
        <Card className="cabinet-card animate-slide-up">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-aone-sm">
              <Zap className="w-4 h-4" />
              Active Analyses ({activeAnalyses.size})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-sm">
              {Array.from(activeAnalyses.values()).map((analysis) => (
                <div key={analysis.analysisId} className="aone-spacing-sm rounded-aone-lg bg-muted dark:bg-gray-800">
                  <div className="aone-flex-between mb-2">
                    <div className="text-aone-sm font-aone-medium">
                      Analysis {analysis.analysisId.slice(-8)}
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {analysis.step.replace('_', ' ')}
                    </Badge>
                  </div>
                  <Progress value={analysis.progress} className="h-2 mb-2" />
                  <div className="aone-flex-between text-xs text-gray-500">
                    <span>{analysis.message}</span>
                    {analysis.confidence && (
                      <span>Confidence: {Math.round(analysis.confidence * 100)}%</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Detailed Metrics (Optional) */}
      {showDetailedMetrics && metrics && connectionStatus === 'connected' && (
        <Card className="cabinet-card animate-slide-up">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-aone-sm">
              <Server className="w-4 h-4" />
              System Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-aone-md">
              <div className="text-center aone-spacing-sm rounded-aone-lg bg-blue-50 dark:bg-blue-950">
                <div className="text-aone-lg font-bold text-aone-sage dark:text-blue-400">
                  {metrics.queueLength}
                </div>
                <div className="text-xs text-blue-800 dark:text-blue-200">
                  Queue Length
                </div>
              </div>
              
              <div className="text-center aone-spacing-sm rounded-aone-lg bg-green-50 dark:bg-green-950">
                <div className="text-aone-lg font-bold text-green-600 dark:text-green-400">
                  {metrics.processingCount}
                </div>
                <div className="text-xs text-green-800 dark:text-green-200">
                  Processing
                </div>
              </div>
              
              <div className="text-center aone-spacing-sm rounded-aone-lg bg-purple-50 dark:bg-purple-950">
                <div className="text-aone-lg font-bold text-purple-600 dark:text-purple-400">
                  {metrics.connectedClients}
                </div>
                <div className="text-xs text-purple-800 dark:text-purple-200">
                  Connected
                </div>
              </div>
              
              <div className="text-center aone-spacing-sm rounded-aone-lg bg-orange-50 dark:bg-orange-950">
                <div className="text-aone-lg font-bold text-orange-600 dark:text-orange-400">
                  {metrics.activeAnalyses}
                </div>
                <div className="text-xs text-orange-800 dark:text-orange-200">
                  Active
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Connection Issues */}
      {connectionStatus === 'disconnected' && reconnectAttempts > 0 && (
        <Card className="border-yellow-200 dark:border-yellow-800 animate-slide-up">
          <CardContent className="pt-4">
            <div className="flex items-center gap-2 text-yellow-600 dark:text-yellow-400">
              <AlertTriangle className="w-4 h-4" />
              <span className="text-aone-sm">
                Connection lost. Attempted {reconnectAttempts} reconnection{reconnectAttempts > 1 ? 's' : ''}.
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Active Analyses */}
      {activeAnalyses.size === 0 && connectionStatus === 'connected' && (
        <Card className="cabinet-card">
          <CardContent className="pt-4 pb-4">
            <div className="text-center text-gray-500">
              <Eye className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-aone-sm">No active analyses</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EnhancedWebSocketStatus;
