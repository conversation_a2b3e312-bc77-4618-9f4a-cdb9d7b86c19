import React, { useState, useCallback, useMemo } from 'react';
import React<PERSON><PERSON>, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  MiniMap,
  ReactFlowProvider,
  Position,
  MarkerType
} from 'reactflow';
import 'reactflow/dist/style.css';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Brain,
  Clock,
  CheckCircle,
  AlertTriangle,
  Info,
  Zap,
  Target,
  ArrowRight,
  Maximize2,
  Download,
  RotateCcw,
  Eye,
  EyeOff
} from 'lucide-react';

// Types for reasoning chain data
export interface ReasoningStep {
  id: string;
  type: 'observation' | 'analysis' | 'inference' | 'conclusion' | 'validation';
  title: string;
  content: string;
  confidence: number;
  timestamp: Date;
  dependencies: string[];
  evidence: string[];
  uncertainties?: string[];
  processingTime?: number;
}

export interface ReasoningChain {
  id: string;
  modelType: 'GPTO1' | 'GPT4O' | 'GPT4O_MINI';
  analysisType: string;
  steps: ReasoningStep[];
  overallConfidence: number;
  totalProcessingTime: number;
  complexityFactors: string[];
  expectedOutcomes: string[];
}

interface ReasoningChainVisualizationProps {
  reasoningChain: ReasoningChain;
  isVisible: boolean;
  onToggleVisibility: () => void;
  className?: string;
}

// Custom node component for reasoning steps
const ReasoningStepNode = ({ data }: { data: ReasoningStep }) => {
  const getStepIcon = (type: string) => {
    switch (type) {
      case 'observation': return <Eye className="w-4 h-4" />;
      case 'analysis': return <Brain className="w-4 h-4" />;
      case 'inference': return <Zap className="w-4 h-4" />;
      case 'conclusion': return <Target className="w-4 h-4" />;
      case 'validation': return <CheckCircle className="w-4 h-4" />;
      default: return <Info className="w-4 h-4" />;
    }
  };

  const getStepColor = (type: string) => {
    switch (type) {
      case 'observation': return 'bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800';
      case 'analysis': return 'bg-purple-50 border-purple-200 dark:bg-purple-950 dark:border-purple-800';
      case 'inference': return 'bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800';
      case 'conclusion': return 'bg-orange-50 border-orange-200 dark:bg-orange-950 dark:border-orange-800';
      case 'validation': return 'bg-emerald-50 border-emerald-200 dark:bg-emerald-950 dark:border-emerald-800';
      default: return 'bg-muted border-border dark:bg-gray-950 dark:border-gray-800';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-status-success dark:text-green-400';
    if (confidence >= 0.6) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-status-error dark:text-red-400';
  };

  return (
    <div
      className={`aone-spacing-md rounded-aone-lg border-2 min-w-[280px] max-w-[320px] ${getStepColor(data.type)}`}
      data-testid="reasoning-step"
      data-step-type={data.type}
    >
      <div className="flex items-center gap-2 mb-2">
        {getStepIcon(data.type)}
        <span className="font-aone-semibold text-aone-sm capitalize">{data.type}</span>
        <Badge variant="outline" className="ml-auto">
          <span className={getConfidenceColor(data.confidence)}>
            {Math.round(data.confidence * 100)}%
          </span>
        </Badge>
      </div>
      
      <h4 className="font-aone-medium text-aone-sm mb-2 line-clamp-2">{data.title}</h4>
      
      <p className="text-xs text-aone-charcoal dark:text-gray-400 mb-3 line-clamp-3">
        {data.content}
      </p>
      
      <div className="aone-flex-between text-xs">
        <div className="flex items-center gap-1 text-aone-soft-gray">
          <Clock className="w-3 h-3" />
          {data.processingTime ? `${data.processingTime}ms` : 'N/A'}
        </div>
        
        {data.uncertainties && data.uncertainties.length > 0 && (
          <div className="flex items-center gap-1 text-amber-600">
            <AlertTriangle className="w-3 h-3" />
            <span>{data.uncertainties.length}</span>
          </div>
        )}
      </div>
      
      <Progress 
        value={data.confidence * 100} 
        className="mt-2 h-1"
      />
    </div>
  );
};

// Node types for ReactFlow
const nodeTypes = {
  reasoningStep: ReasoningStepNode,
};

export default function ReasoningChainVisualization({
  reasoningChain,
  isVisible,
  onToggleVisibility,
  className = ''
}: ReasoningChainVisualizationProps) {
  const [viewMode, setViewMode] = useState<'flow' | 'timeline'>('flow');
  const [selectedStep, setSelectedStep] = useState<ReasoningStep | null>(null);

  // Convert reasoning steps to ReactFlow nodes and edges
  const { nodes, edges } = useMemo(() => {
    const flowNodes: Node[] = reasoningChain.steps.map((step, index) => ({
      id: step.id,
      type: 'reasoningStep',
      position: { 
        x: (index % 3) * 350, 
        y: Math.floor(index / 3) * 200 
      },
      data: step,
      sourcePosition: Position.Right,
      targetPosition: Position.Left,
    }));

    const flowEdges: Edge[] = [];
    reasoningChain.steps.forEach((step) => {
      step.dependencies.forEach((depId) => {
        if (reasoningChain.steps.find(s => s.id === depId)) {
          flowEdges.push({
            id: `${depId}-${step.id}`,
            source: depId,
            target: step.id,
            type: 'smoothstep',
            markerEnd: {
              type: MarkerType.ArrowClosed,
            },
            style: {
              strokeWidth: 2,
              stroke: 'rgb(var(--preset-accent))',
            },
          });
        }
      });
    });

    return { nodes: flowNodes, edges: flowEdges };
  }, [reasoningChain]);

  const [flowNodes, setNodes, onNodesChange] = useNodesState(nodes);
  const [flowEdges, setEdges, onEdgesChange] = useEdgesState(edges);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  if (!isVisible) {
    return (
      <div className={`mb-aone-md ${className}`}>
        <Button
          variant="outline"
          onClick={onToggleVisibility}
          className="flex items-center gap-2"
        >
          <Brain className="w-4 h-4" />
          Show Reasoning Chain
          <Badge variant="secondary">{reasoningChain.steps.length} steps</Badge>
        </Button>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`} data-testid="reasoning-chain-visualization">
      {/* Header Controls */}
      <div className="aone-flex-between">
        <div className="flex items-center gap-aone-md">
          <Button
            variant="outline"
            onClick={onToggleVisibility}
            className="flex items-center gap-2"
          >
            <EyeOff className="w-4 h-4" />
            Hide Reasoning Chain
          </Button>
          
          <Badge variant="outline" className="flex items-center gap-1">
            <Brain className="w-3 h-3" />
            {reasoningChain.modelType}
          </Badge>
          
          <Badge variant="secondary">
            {Math.round(reasoningChain.overallConfidence * 100)}% Confidence
          </Badge>
        </div>

        <div className="flex items-center gap-2">
          <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'flow' | 'timeline')}>
            <TabsList>
              <TabsTrigger value="flow">Flow View</TabsTrigger>
              <TabsTrigger value="timeline">Timeline</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      {/* Main Visualization */}
      <Card className="h-[600px]">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            GPT-o1 Reasoning Chain: {reasoningChain.analysisType}
          </CardTitle>
          <div className="flex items-center gap-aone-md text-aone-sm text-aone-charcoal dark:text-gray-400">
            <span>{reasoningChain.steps.length} reasoning steps</span>
            <span>{reasoningChain.totalProcessingTime}ms total</span>
            <span>{reasoningChain.complexityFactors.length} complexity factors</span>
          </div>
        </CardHeader>
        
        <CardContent className="h-[500px] p-0">
          {viewMode === 'flow' ? (
            <ReactFlowProvider>
              <ReactFlow
                nodes={flowNodes}
                edges={flowEdges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                nodeTypes={nodeTypes}
                fitView
                className="bg-muted dark:bg-gray-900"
              >
                <Controls />
                <MiniMap />
                <Background variant="dots" gap={12} size={1} />
              </ReactFlow>
            </ReactFlowProvider>
          ) : (
            <div className="h-full overflow-y-auto aone-spacing-md">
              <div className="space-y-4">
                {reasoningChain.steps.map((step, index) => (
                  <div key={step.id} className="flex items-start gap-aone-md">
                    <div className="flex flex-col items-center">
                      <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 aone-flex-center text-aone-sm font-aone-medium">
                        {index + 1}
                      </div>
                      {index < reasoningChain.steps.length - 1 && (
                        <div className="w-px h-16 bg-muted dark:bg-gray-700 mt-2" />
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <ReasoningStepNode data={step} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
