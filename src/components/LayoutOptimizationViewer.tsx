import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  TrendingUp,
  Maximize2,
  Users,
  Navigation,
  DollarSign,
  CheckCircle,
  AlertTriangle,
  Info,
  ArrowRight,
  Target,
  Zap,
  Shield,
  BarChart3,
  Layout,
  Move3D
} from 'lucide-react';

// Layout Optimization Interfaces
export interface LayoutOptimizationResult {
  workflowOptimization: {
    currentWorkflow: {
      efficiency: number;
      bottlenecks: string[];
      workTriangle: {
        perimeter: number;
        efficiency: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR';
        recommendations: string[];
      };
    };
    optimizedWorkflow: {
      suggestedChanges: LayoutChange[];
      expectedEfficiency: number;
      implementationComplexity: 'LOW' | 'MEDIUM' | 'HIGH';
      estimatedCost: number;
    };
    confidence: {
      workflowAnalysis: number;
      optimizationAccuracy: number;
      costEstimation: number;
      overall: number;
    };
  };
  spaceUtilization: {
    currentUtilization: {
      totalSpace: number;
      usedSpace: number;
      utilizationPercentage: number;
      wastedSpace: number;
      storageEfficiency: number;
    };
    optimizationOpportunities: {
      verticalSpace: SpaceOpportunity[];
      cornerUtilization: SpaceOpportunity[];
      cabinetConfiguration: SpaceOpportunity[];
      applianceLayout: SpaceOpportunity[];
    };
    recommendations: {
      priority: 'HIGH' | 'MEDIUM' | 'LOW';
      description: string;
      expectedGain: number;
      implementationCost: number;
      roi: number;
    }[];
  };
  ergonomicAssessment: {
    currentErgonomics: {
      reachability: number;
      heightOptimization: number;
      accessibilityScore: number;
      safetyRating: number;
    };
    improvements: {
      cabinetHeights: ErgonomicRecommendation[];
      handlePositions: ErgonomicRecommendation[];
      accessibilityFeatures: ErgonomicRecommendation[];
      safetyEnhancements: ErgonomicRecommendation[];
    };
    accessibilityCompliance: {
      adaCompliant: boolean;
      universalDesignScore: number;
      recommendations: string[];
    };
  };
  trafficFlowAnalysis: {
    currentFlow: {
      primaryPaths: FlowPath[];
      congestionPoints: CongestionPoint[];
      clearanceIssues: ClearanceIssue[];
      overallRating: number;
    };
    optimizedFlow: {
      suggestedLayout: LayoutChange[];
      improvedPaths: FlowPath[];
      resolvedIssues: string[];
      expectedImprovement: number;
    };
  };
  costBenefitAnalysis: {
    totalInvestment: {
      materialCosts: number;
      laborCosts: number;
      totalCost: number;
      timeframe: string;
    };
    expectedBenefits: {
      efficiencyGains: number;
      spaceUtilization: number;
      propertyValue: number;
      energySavings: number;
    };
    roi: {
      paybackPeriod: number;
      netPresentValue: number;
      returnPercentage: number;
      riskAssessment: 'LOW' | 'MEDIUM' | 'HIGH';
    };
  };
  processingMetrics: {
    analysisTime: number;
    confidenceScore: number;
    featuresAnalyzed: string[];
  };
}

export interface LayoutChange {
  id: string;
  type: 'CABINET_RELOCATION' | 'APPLIANCE_MOVE' | 'STORAGE_ADDITION' | 'WORKFLOW_OPTIMIZATION';
  description: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  estimatedCost: number;
  implementationTime: string;
  expectedBenefit: string;
  visualImpact: 'MAJOR' | 'MODERATE' | 'MINOR';
}

export interface SpaceOpportunity {
  location: string;
  currentState: string;
  opportunity: string;
  potentialGain: number;
  implementationCost: number;
  difficulty: 'EASY' | 'MODERATE' | 'DIFFICULT';
}

export interface ErgonomicRecommendation {
  area: string;
  currentIssue: string;
  recommendation: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  accessibilityImpact: boolean;
  estimatedCost: number;
}

export interface FlowPath {
  name: string;
  startPoint: string;
  endPoint: string;
  frequency: 'HIGH' | 'MEDIUM' | 'LOW';
  currentEfficiency: number;
  obstacles: string[];
}

export interface CongestionPoint {
  location: string;
  severity: 'HIGH' | 'MEDIUM' | 'LOW';
  causes: string[];
  suggestedSolution: string;
  implementationCost: number;
}

export interface ClearanceIssue {
  location: string;
  currentClearance: number;
  requiredClearance: number;
  impact: string;
  solution: string;
}

interface LayoutOptimizationViewerProps {
  result: LayoutOptimizationResult;
  onGenerateReport?: () => void;
  onImplementChanges?: (changes: LayoutChange[]) => void;
}

const LayoutOptimizationViewer: React.FC<LayoutOptimizationViewerProps> = ({
  result,
  onGenerateReport,
  onImplementChanges
}) => {
  const [selectedTab, setSelectedTab] = useState('overview');

  // Helper functions
  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 0.8) return <Badge className="bg-green-100 text-green-800">High Confidence</Badge>;
    if (confidence >= 0.6) return <Badge className="bg-yellow-100 text-yellow-800">Medium Confidence</Badge>;
    return <Badge className="bg-red-100 text-red-800">Low Confidence</Badge>;
  };

  const getPriorityBadge = (priority: 'HIGH' | 'MEDIUM' | 'LOW') => {
    const colors = {
      HIGH: 'bg-red-100 text-red-800',
      MEDIUM: 'bg-yellow-100 text-yellow-800',
      LOW: 'bg-green-100 text-green-800'
    };
    return <Badge className={colors[priority]}>{priority}</Badge>;
  };

  const getEfficiencyBadge = (efficiency: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR') => {
    const colors = {
      EXCELLENT: 'bg-green-100 text-green-800',
      GOOD: 'bg-blue-100 text-blue-800',
      FAIR: 'bg-yellow-100 text-yellow-800',
      POOR: 'bg-red-100 text-red-800'
    };
    return <Badge className={colors[efficiency]}>{efficiency}</Badge>;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${Math.round(value * 100)}%`;
  };

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header with Overall Metrics */}
      <Card className="border-2 border-blue-200 dark:border-blue-800">
        <CardHeader>
          <div className="aone-flex-between">
            <CardTitle className="flex items-center gap-2">
              <Layout className="w-6 h-6 text-aone-sage" />
              Smart Layout Optimization Analysis
            </CardTitle>
            <div className="flex items-center gap-2">
              {getConfidenceBadge(result.processingMetrics.confidenceScore)}
              <Badge variant="outline" className="flex items-center gap-1">
                <Zap className="w-3 h-3" />
                {result.processingMetrics.analysisTime}ms
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-aone-md">
            <div className="text-center">
              <div className="text-aone-3xl font-aone-bold text-aone-sage">
                {formatPercentage(result.workflowOptimization.currentWorkflow.efficiency)}
              </div>
              <div className="text-aone-sm text-muted-foreground">Current Efficiency</div>
            </div>
            <div className="text-center">
              <div className="text-aone-3xl font-aone-bold text-green-600">
                {formatPercentage(result.spaceUtilization.currentUtilization.utilizationPercentage / 100)}
              </div>
              <div className="text-aone-sm text-muted-foreground">Space Utilization</div>
            </div>
            <div className="text-center">
              <div className="text-aone-3xl font-aone-bold text-purple-600">
                {formatPercentage(result.ergonomicAssessment.currentErgonomics.accessibilityScore)}
              </div>
              <div className="text-aone-sm text-muted-foreground">Accessibility Score</div>
            </div>
            <div className="text-center">
              <div className="text-aone-3xl font-aone-bold text-orange-600">
                {formatCurrency(result.costBenefitAnalysis.totalInvestment.totalCost)}
              </div>
              <div className="text-aone-sm text-muted-foreground">Investment Required</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Analysis Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="workflow" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Workflow
          </TabsTrigger>
          <TabsTrigger value="space" className="flex items-center gap-2">
            <Maximize2 className="w-4 h-4" />
            Space
          </TabsTrigger>
          <TabsTrigger value="ergonomics" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            Ergonomics
          </TabsTrigger>
          <TabsTrigger value="roi" className="flex items-center gap-2">
            <DollarSign className="w-4 h-4" />
            ROI Analysis
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-aone-md">
            {/* Key Recommendations */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  Priority Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {result.workflowOptimization.optimizedWorkflow.suggestedChanges
                  .filter(change => change.priority === 'HIGH')
                  .slice(0, 3)
                  .map((change, index) => (
                    <div key={change.id} className="flex items-start gap-3 aone-spacing-sm bg-muted rounded-aone-lg">
                      <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full aone-flex-center text-aone-sm font-bold">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <div className="font-aone-medium">{change.description}</div>
                        <div className="text-aone-sm text-muted-foreground mt-1">
                          {formatCurrency(change.estimatedCost)} • {change.implementationTime}
                        </div>
                        <div className="text-aone-sm text-green-600 mt-1">{change.expectedBenefit}</div>
                      </div>
                    </div>
                  ))}
              </CardContent>
            </Card>

            {/* Performance Improvements */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  Expected Improvements
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-aone-sm mb-1">
                      <span>Workflow Efficiency</span>
                      <span>{formatPercentage(result.workflowOptimization.optimizedWorkflow.expectedEfficiency)}</span>
                    </div>
                    <Progress value={result.workflowOptimization.optimizedWorkflow.expectedEfficiency * 100} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-aone-sm mb-1">
                      <span>Traffic Flow</span>
                      <span>+{formatPercentage(result.trafficFlowAnalysis.optimizedFlow.expectedImprovement)}</span>
                    </div>
                    <Progress value={(result.trafficFlowAnalysis.currentFlow.overallRating + result.trafficFlowAnalysis.optimizedFlow.expectedImprovement) * 100} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-aone-sm mb-1">
                      <span>Accessibility Score</span>
                      <span>{formatPercentage(result.ergonomicAssessment.accessibilityCompliance.universalDesignScore)}</span>
                    </div>
                    <Progress value={result.ergonomicAssessment.accessibilityCompliance.universalDesignScore * 100} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Workflow Tab */}
        <TabsContent value="workflow" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-aone-md">
            <Card>
              <CardHeader>
                <CardTitle>Current Workflow Analysis</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="aone-flex-between">
                  <span>Work Triangle Efficiency</span>
                  {getEfficiencyBadge(result.workflowOptimization.currentWorkflow.workTriangle.efficiency)}
                </div>
                <div>
                  <span className="text-aone-sm text-muted-foreground">Perimeter: {result.workflowOptimization.currentWorkflow.workTriangle.perimeter} feet</span>
                </div>
                <div>
                  <h4 className="font-aone-medium mb-2">Current Bottlenecks:</h4>
                  <ul className="space-y-1">
                    {result.workflowOptimization.currentWorkflow.bottlenecks.map((bottleneck, index) => (
                      <li key={index} className="flex items-start gap-2 text-aone-sm">
                        <AlertTriangle className="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                        {bottleneck}
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Optimization Recommendations</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  {result.workflowOptimization.optimizedWorkflow.suggestedChanges.map((change) => (
                    <div key={change.id} className="aone-spacing-sm border rounded-aone-lg">
                      <div className="flex items-start justify-between mb-2">
                        <h5 className="font-aone-medium">{change.description}</h5>
                        {getPriorityBadge(change.priority)}
                      </div>
                      <div className="text-aone-sm text-muted-foreground space-y-1">
                        <div>Cost: {formatCurrency(change.estimatedCost)}</div>
                        <div>Timeline: {change.implementationTime}</div>
                        <div className="text-green-600">{change.expectedBenefit}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Space Tab */}
        <TabsContent value="space" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-aone-md">
            <Card>
              <CardHeader>
                <CardTitle>Current Space Utilization</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-aone-md text-center">
                  <div>
                    <div className="text-aone-2xl font-aone-bold text-aone-sage">
                      {result.spaceUtilization.currentUtilization.totalSpace.toFixed(1)} sq ft
                    </div>
                    <div className="text-aone-sm text-muted-foreground">Total Space</div>
                  </div>
                  <div>
                    <div className="text-aone-2xl font-aone-bold text-green-600">
                      {result.spaceUtilization.currentUtilization.usedSpace.toFixed(1)} sq ft
                    </div>
                    <div className="text-aone-sm text-muted-foreground">Used Space</div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between text-aone-sm mb-1">
                    <span>Storage Efficiency</span>
                    <span>{formatPercentage(result.spaceUtilization.currentUtilization.storageEfficiency)}</span>
                  </div>
                  <Progress value={result.spaceUtilization.currentUtilization.storageEfficiency * 100} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Optimization Opportunities</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {result.spaceUtilization.recommendations.slice(0, 3).map((rec, index) => (
                  <div key={index} className="aone-spacing-sm border rounded-aone-lg">
                    <div className="flex items-start justify-between mb-2">
                      <h5 className="font-aone-medium">{rec.description}</h5>
                      {getPriorityBadge(rec.priority)}
                    </div>
                    <div className="text-aone-sm text-muted-foreground space-y-1">
                      <div>Gain: +{rec.expectedGain.toFixed(1)} sq ft</div>
                      <div>Cost: {formatCurrency(rec.implementationCost)}</div>
                      <div className="text-green-600">ROI: {rec.roi.toFixed(1)}x</div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Ergonomics Tab */}
        <TabsContent value="ergonomics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-aone-md">
            <Card>
              <CardHeader>
                <CardTitle>Current Ergonomic Assessment</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  {[
                    { label: 'Reachability', value: result.ergonomicAssessment.currentErgonomics.reachability },
                    { label: 'Height Optimization', value: result.ergonomicAssessment.currentErgonomics.heightOptimization },
                    { label: 'Accessibility Score', value: result.ergonomicAssessment.currentErgonomics.accessibilityScore },
                    { label: 'Safety Rating', value: result.ergonomicAssessment.currentErgonomics.safetyRating }
                  ].map((metric) => (
                    <div key={metric.label}>
                      <div className="flex justify-between text-aone-sm mb-1">
                        <span>{metric.label}</span>
                        <span>{formatPercentage(metric.value)}</span>
                      </div>
                      <Progress value={metric.value * 100} className="h-2" />
                    </div>
                  ))}
                </div>
                <div className="mt-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Shield className="w-4 h-4" />
                    <span className="font-aone-medium">ADA Compliance</span>
                    {result.ergonomicAssessment.accessibilityCompliance.adaCompliant ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <AlertTriangle className="w-4 h-4 text-yellow-500" />
                    )}
                  </div>
                  <div className="text-aone-sm text-muted-foreground">
                    Universal Design Score: {formatPercentage(result.ergonomicAssessment.accessibilityCompliance.universalDesignScore)}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Improvement Recommendations</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {[
                  ...result.ergonomicAssessment.improvements.cabinetHeights,
                  ...result.ergonomicAssessment.improvements.handlePositions,
                  ...result.ergonomicAssessment.improvements.safetyEnhancements
                ].slice(0, 4).map((improvement, index) => (
                  <div key={index} className="aone-spacing-sm border rounded-aone-lg">
                    <div className="flex items-start justify-between mb-2">
                      <h5 className="font-aone-medium">{improvement.area}</h5>
                      {getPriorityBadge(improvement.priority)}
                    </div>
                    <div className="text-aone-sm text-muted-foreground space-y-1">
                      <div><strong>Issue:</strong> {improvement.currentIssue}</div>
                      <div><strong>Solution:</strong> {improvement.recommendation}</div>
                      <div>Cost: {formatCurrency(improvement.estimatedCost)}</div>
                      {improvement.accessibilityImpact && (
                        <div className="text-aone-sage flex items-center gap-1">
                          <Users className="w-3 h-3" />
                          Improves accessibility
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* ROI Analysis Tab */}
        <TabsContent value="roi" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-aone-md">
            <Card>
              <CardHeader>
                <CardTitle>Investment Breakdown</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Material Costs</span>
                    <span className="font-aone-medium">{formatCurrency(result.costBenefitAnalysis.totalInvestment.materialCosts)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Labor Costs</span>
                    <span className="font-aone-medium">{formatCurrency(result.costBenefitAnalysis.totalInvestment.laborCosts)}</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-bold">
                      <span>Total Investment</span>
                      <span>{formatCurrency(result.costBenefitAnalysis.totalInvestment.totalCost)}</span>
                    </div>
                  </div>
                  <div className="text-aone-sm text-muted-foreground">
                    Estimated timeframe: {result.costBenefitAnalysis.totalInvestment.timeframe}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Expected Returns</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Property Value Increase</span>
                    <span className="font-aone-medium text-green-600">
                      +{formatCurrency(result.costBenefitAnalysis.expectedBenefits.propertyValue)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Efficiency Gains</span>
                    <span className="font-aone-medium text-green-600">
                      +{formatCurrency(result.costBenefitAnalysis.expectedBenefits.efficiencyGains)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Annual Energy Savings</span>
                    <span className="font-aone-medium text-green-600">
                      +{formatCurrency(result.costBenefitAnalysis.expectedBenefits.energySavings)}
                    </span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-bold">
                      <span>ROI</span>
                      <span className="text-green-600">{result.costBenefitAnalysis.roi.returnPercentage}%</span>
                    </div>
                  </div>
                  <div className="text-aone-sm text-muted-foreground">
                    Payback period: {result.costBenefitAnalysis.roi.paybackPeriod} years
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="flex gap-aone-md pt-4">
        <Button onClick={onGenerateReport} className="flex items-center gap-2">
          <BarChart3 className="w-4 h-4" />
          Generate Report
        </Button>
        <Button 
          variant="outline" 
          onClick={() => onImplementChanges?.(result.workflowOptimization.optimizedWorkflow.suggestedChanges)}
          className="flex items-center gap-2"
        >
          <Move3D className="w-4 h-4" />
          View 3D Implementation
        </Button>
      </div>
    </div>
  );
};

export default LayoutOptimizationViewer;
