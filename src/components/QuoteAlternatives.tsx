import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Lightbulb, TrendingDown, Info } from 'lucide-react';
import { QuoteAlternative } from '@/services/quotationService';

interface QuoteAlternativesProps {
  alternatives: QuoteAlternative[];
}

export const QuoteAlternatives: React.FC<QuoteAlternativesProps> = ({ alternatives }) => {
  const getSavingsColor = (costDifference: string) => {
    const amount = parseFloat(costDifference.replace(/[^\d.-]/g, ''));
    return amount < 0 ? 'text-green-600' : 'text-red-600';
  };

  const getSavingsBadgeVariant = (costDifference: string) => {
    const amount = parseFloat(costDifference.replace(/[^\d.-]/g, ''));
    return amount < 0 ? 'default' : 'secondary';
  };

  return (
    <Card data-testid="quote-alternatives" className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Lightbulb className="h-5 w-5 mr-2 text-yellow-500" />
          Cost-Saving Alternatives
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {alternatives.map((alternative, index) => (
            <div
              key={index}
              className="aone-spacing-md border rounded-aone-lg bg-gradient-to-r from-blue-50 to-indigo-50"
              data-testid="alternative-item"
            >
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-aone-semibold text-aone-sm" data-testid="alternative-description">
                  {alternative.description}
                </h4>
                <Badge
                  variant={getSavingsBadgeVariant(alternative.costDifference)}
                  className={getSavingsColor(alternative.costDifference)}
                  data-testid="alternative-cost-diff"
                >
                  {alternative.costDifference}
                </Badge>
              </div>
              
              <div className="flex items-center text-aone-sm text-muted-foreground">
                <Info className="h-3 w-3 mr-1" />
                <span data-testid="alternative-impact">{alternative.impact}</span>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-4 aone-spacing-sm bg-yellow-50 border border-yellow-200 rounded-aone-lg">
          <div className="flex items-center text-aone-sm text-yellow-800">
            <TrendingDown className="h-4 w-4 mr-2" />
            <span className="font-aone-medium">
              These alternatives can help reduce costs while maintaining quality standards.
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
