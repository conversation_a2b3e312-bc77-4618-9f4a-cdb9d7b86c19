import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  FileText,
  Download,
  Share,
  Eye,
  Printer,
  Mail,
  Smartphone,
  ExternalLink,
  CheckCircle,
  Clock,
  User,
  Calendar,
  Building,
  Loader2
} from 'lucide-react';

interface ReportData {
  id: string;
  title: string;
  type: 'basic' | 'detailed' | 'professional';
  generatedAt: string;
  fileSize: string;
  pageCount: number;
  downloadUrl?: string;
  previewUrl?: string;
  status: 'generating' | 'ready' | 'error';
}

interface MobileReportViewerProps {
  reports: ReportData[];
  onGenerateReport?: (type: 'basic' | 'detailed' | 'professional') => void;
  onDownloadReport?: (reportId: string) => void;
  onShareReport?: (reportId: string) => void;
  onPreviewReport?: (reportId: string) => void;
  className?: string;
}

const MobileReportViewer: React.FC<MobileReportViewerProps> = ({
  reports,
  onGenerateReport,
  onDownloadReport,
  onShareReport,
  onPreviewReport,
  className = ''
}) => {
  const isMobile = useIsMobile();
  const [generatingType, setGeneratingType] = useState<string | null>(null);

  const handleGenerateReport = async (type: 'basic' | 'detailed' | 'professional') => {
    setGeneratingType(type);
    try {
      await onGenerateReport?.(type);
    } finally {
      setGeneratingType(null);
    }
  };

  const getReportTypeInfo = (type: string) => {
    switch (type) {
      case 'basic':
        return {
          name: 'Basic Report',
          description: 'Essential analysis summary',
          icon: FileText,
          color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
        };
      case 'detailed':
        return {
          name: 'Detailed Report',
          description: 'Comprehensive analysis with measurements',
          icon: FileText,
          color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
        };
      case 'professional':
        return {
          name: 'Professional Report',
          description: 'Executive presentation with branding',
          icon: FileText,
          color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
        };
      default:
        return {
          name: 'Report',
          description: 'Analysis report',
          icon: FileText,
          color: 'bg-muted text-foreground dark:bg-gray-900 dark:text-gray-200'
        };
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-NZ', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Mobile Header */}
      <Card className="border-2 border-blue-200 dark:border-blue-800">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Analysis Reports
            {isMobile && <Badge variant="secondary">Mobile View</Badge>}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-aone-sm text-muted-foreground dark:text-gray-400 mb-aone-md">
            Generate and manage professional analysis reports optimized for mobile viewing and sharing.
          </p>

          {/* Quick Generate Buttons */}
          <div className="grid grid-cols-1 gap-3">
            {(['basic', 'detailed', 'professional'] as const).map((type) => {
              const info = getReportTypeInfo(type);
              const Icon = info.icon;
              const isGenerating = generatingType === type;

              return (
                <Button
                  key={type}
                  variant="outline"
                  onClick={() => handleGenerateReport(type)}
                  disabled={isGenerating}
                  className="h-auto aone-spacing-md justify-start touch-manipulation"
                >
                  <div className="flex items-center gap-3 w-full">
                    <div className={`aone-spacing-xs rounded-aone-lg ${info.color}`}>
                      {isGenerating ? (
                        <Loader2 className="w-5 h-5 animate-spin" />
                      ) : (
                        <Icon className="w-5 h-5" />
                      )}
                    </div>
                    <div className="flex-1 text-left">
                      <div className="font-aone-medium">{info.name}</div>
                      <div className="text-aone-sm text-muted-foreground dark:text-gray-400">
                        {isGenerating ? 'Generating...' : info.description}
                      </div>
                    </div>
                  </div>
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Existing Reports */}
      {reports.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-aone-lg">Generated Reports</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {reports.map((report) => {
                const info = getReportTypeInfo(report.type);
                const Icon = info.icon;

                return (
                  <Card key={report.id} className="border">
                    <CardContent className="aone-spacing-md">
                      <div className="flex items-start gap-3">
                        <div className={`aone-spacing-xs rounded-aone-lg ${info.color} flex-shrink-0`}>
                          <Icon className="w-5 h-5" />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-aone-semibold truncate">{report.title}</h3>
                            <Badge 
                              variant={report.status === 'ready' ? 'default' : 'secondary'}
                              className="flex-shrink-0"
                            >
                              {report.status === 'ready' && <CheckCircle className="w-3 h-3 mr-1" />}
                              {report.status === 'generating' && <Loader2 className="w-3 h-3 mr-1 animate-spin" />}
                              {report.status}
                            </Badge>
                          </div>

                          <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground dark:text-gray-400 mb-3">
                            <div className="flex items-center gap-1">
                              <Calendar className="w-3 h-3" />
                              {formatDate(report.generatedAt)}
                            </div>
                            <div className="flex items-center gap-1">
                              <FileText className="w-3 h-3" />
                              {report.pageCount} pages, {report.fileSize}
                            </div>
                          </div>

                          {report.status === 'ready' && (
                            <div className="grid grid-cols-3 gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => onPreviewReport?.(report.id)}
                                className="text-xs touch-manipulation"
                              >
                                <Eye className="w-3 h-3 mr-1" />
                                Preview
                              </Button>
                              <Button
                                size="sm"
                                onClick={() => onDownloadReport?.(report.id)}
                                className="text-xs touch-manipulation"
                              >
                                <Download className="w-3 h-3 mr-1" />
                                Download
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => onShareReport?.(report.id)}
                                className="text-xs touch-manipulation"
                              >
                                <Share className="w-3 h-3 mr-1" />
                                Share
                              </Button>
                            </div>
                          )}

                          {report.status === 'generating' && (
                            <div className="space-y-2">
                              <Progress value={65} className="h-2" />
                              <p className="text-xs text-muted-foreground dark:text-gray-400">
                                Generating report... This may take a few moments.
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Mobile Sharing Options */}
      {isMobile && reports.some(r => r.status === 'ready') && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-aone-lg flex items-center gap-2">
              <Smartphone className="w-5 h-5" />
              Mobile Sharing
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="outline"
                className="h-16 aone-flex-col-center gap-2 touch-manipulation"
              >
                <Mail className="w-6 h-6" />
                <span className="text-xs">Email</span>
              </Button>
              <Button
                variant="outline"
                className="h-16 aone-flex-col-center gap-2 touch-manipulation"
              >
                <Share className="w-6 h-6" />
                <span className="text-xs">Share Link</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Report Templates Info */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-aone-lg">Report Templates</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-aone-sm">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <div className="font-aone-medium">Basic Report</div>
                <div className="text-muted-foreground dark:text-gray-400">
                  Essential analysis summary with cabinet count, dimensions, and confidence scores.
                </div>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <div className="font-aone-medium">Detailed Report</div>
                <div className="text-muted-foreground dark:text-gray-400">
                  Comprehensive analysis including hardware detection, materials, and measurements.
                </div>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <div className="font-aone-medium">Professional Report</div>
                <div className="text-muted-foreground dark:text-gray-400">
                  Executive presentation with custom branding, 3D visualizations, and recommendations.
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Empty State */}
      {reports.length === 0 && (
        <Card>
          <CardContent className="p-aone-xl text-center">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-aone-md" />
            <h3 className="text-aone-lg font-aone-semibold mb-2">No Reports Generated</h3>
            <p className="text-muted-foreground dark:text-gray-400 mb-aone-md">
              Generate your first analysis report using the options above.
            </p>
            <Button
              onClick={() => handleGenerateReport('basic')}
              disabled={generatingType === 'basic'}
              className="touch-manipulation"
            >
              {generatingType === 'basic' ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <FileText className="w-4 h-4 mr-2" />
                  Generate Basic Report
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default MobileReportViewer;
