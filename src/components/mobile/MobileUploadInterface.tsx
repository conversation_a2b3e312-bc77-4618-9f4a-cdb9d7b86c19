import React, { useState, useCallback, useRef } from 'react';
import { useDropzone } from 'react-dropzone';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { useIsMobile } from '@/hooks/use-mobile';
import { 
  Upload, 
  Camera, 
  FileText, 
  Image, 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  X,
  FileCheck,
  Smartphone,
  TouchIcon,
  Maximize
} from 'lucide-react';
import { aiAnalysisService, AnalysisConfig, AnalysisProgress, AnalysisResults } from '@/services/aiAnalysisService';

interface MobileUploadInterfaceProps {
  onAnalysisComplete?: (results: AnalysisResults) => void;
  onAnalysisError?: (error: string) => void;
  className?: string;
}

const MobileUploadInterface: React.FC<MobileUploadInterfaceProps> = ({
  onAnalysisComplete,
  onAnalysisError,
  className = ''
}) => {
  const isMobile = useIsMobile();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);
  
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [progress, setProgress] = useState<AnalysisProgress | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [uploadMethod, setUploadMethod] = useState<'drag' | 'camera' | 'file'>('drag');

  // Mobile-optimized analysis config
  const [config] = useState<AnalysisConfig>({
    useGPT4o: true,
    useReasoning: true,
    useGPTO1: false,
    modelSelection: 'AUTO',
    focusOnMaterials: false,
    focusOnHardware: false,
    enableMultiView: true,
    enable3DReconstruction: true,
    spatialResolution: 'MEDIUM', // Reduced for mobile performance
    includeHardwarePositioning: true,
    complexReasoningRequired: false,
  });

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      setUploadedFile(file);
      setError(null);
      setUploadMethod('drag');
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
    },
    maxSize: 50 * 1024 * 1024, // 50MB
    multiple: false,
  });

  const handleCameraCapture = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
      setError(null);
      setUploadMethod('camera');
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
      setError(null);
      setUploadMethod('file');
    }
  };

  const startAnalysis = async () => {
    if (!uploadedFile) return;

    setIsAnalyzing(true);
    setError(null);
    setProgress(null);

    try {
      const results = await aiAnalysisService.analyzeFile(
        uploadedFile,
        config,
        (progressUpdate) => {
          setProgress(progressUpdate);
        }
      );

      onAnalysisComplete?.(results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Analysis failed';
      setError(errorMessage);
      onAnalysisError?.(errorMessage);
    } finally {
      setIsAnalyzing(false);
      setProgress(null);
    }
  };

  const removeFile = () => {
    setUploadedFile(null);
    setError(null);
    setProgress(null);
    
    // Reset file inputs
    if (fileInputRef.current) fileInputRef.current.value = '';
    if (cameraInputRef.current) cameraInputRef.current.value = '';
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (file: File) => {
    if (file.type === 'application/pdf') {
      return <FileText className="w-6 h-6 text-red-500" />;
    }
    return <Image className="w-6 h-6 text-blue-500" />;
  };

  const getUploadMethodIcon = () => {
    switch (uploadMethod) {
      case 'camera':
        return <Camera className="w-4 h-4" />;
      case 'file':
        return <FileText className="w-4 h-4" />;
      default:
        return <TouchIcon className="w-4 h-4" />;
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Mobile-Optimized Upload Area */}
      <Card className="border-2 border-dashed border-border hover:border-blue-400 transition-colors">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-aone-lg">
            <Smartphone className="w-5 h-5" />
            Upload Kitchen Design
            {isMobile && <Badge variant="secondary">Mobile Optimized</Badge>}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!uploadedFile ? (
            <div className="space-y-4">
              {/* Touch-Friendly Upload Zone */}
              <div
                {...getRootProps()}
                className={`
                  min-h-[200px] p-aone-lg rounded-aone-lg border-2 border-dashed transition-all cursor-pointer
                  ${isDragActive 
                    ? 'border-blue-400 bg-blue-50 dark:bg-blue-950/20' 
                    : 'border-border hover:border-blue-300 hover:bg-muted dark:hover:bg-gray-800'
                  }
                  ${isMobile ? 'touch-manipulation' : ''}
                `}
                style={{ touchAction: 'manipulation' }}
              >
                <input {...getInputProps()} />
                <div className="text-center space-y-4">
                  <div className="flex justify-center">
                    <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full aone-flex-center">
                      <Upload className="w-8 h-8 text-aone-sage dark:text-blue-400" />
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-aone-lg font-aone-semibold text-foreground dark:text-gray-100 mb-2">
                      {isDragActive ? 'Drop your file here' : 'Tap to upload or drag & drop'}
                    </h3>
                    <p className="text-muted-foreground dark:text-gray-400 text-aone-sm">
                      Kitchen drawings, plans, or photos
                    </p>
                  </div>

                  <div className="flex justify-center gap-2 flex-wrap">
                    <Badge variant="secondary">PDF</Badge>
                    <Badge variant="secondary">PNG</Badge>
                    <Badge variant="secondary">JPEG</Badge>
                  </div>
                </div>
              </div>

              {/* Mobile-Specific Upload Options */}
              {isMobile && (
                <div className="grid grid-cols-2 gap-3">
                  {/* Camera Capture */}
                  <Button
                    variant="outline"
                    className="h-16 aone-flex-col-center gap-2 touch-manipulation"
                    onClick={() => cameraInputRef.current?.click()}
                  >
                    <Camera className="w-6 h-6" />
                    <span className="text-xs">Take Photo</span>
                  </Button>
                  
                  {/* File Browser */}
                  <Button
                    variant="outline"
                    className="h-16 aone-flex-col-center gap-2 touch-manipulation"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <FileText className="w-6 h-6" />
                    <span className="text-xs">Browse Files</span>
                  </Button>
                </div>
              )}

              {/* Hidden file inputs for mobile */}
              <input
                ref={cameraInputRef}
                type="file"
                accept="image/*"
                capture="environment"
                onChange={handleCameraCapture}
                className="hidden"
              />
              <input
                ref={fileInputRef}
                type="file"
                accept=".pdf,.png,.jpg,.jpeg"
                onChange={handleFileSelect}
                className="hidden"
              />

              <p className="text-xs text-gray-500 text-center">
                Maximum file size: 50MB
              </p>
            </div>
          ) : (
            /* File Preview */
            <div className="space-y-4">
              <div className="aone-flex-between aone-spacing-md bg-muted dark:bg-gray-800 rounded-aone-lg">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  {getFileIcon(uploadedFile)}
                  <div className="flex-1 min-w-0">
                    <p className="font-aone-medium text-foreground dark:text-gray-100 truncate">
                      {uploadedFile.name}
                    </p>
                    <div className="flex items-center gap-2 text-aone-sm text-gray-500">
                      <span>{formatFileSize(uploadedFile.size)}</span>
                      <Badge variant="outline" className="flex items-center gap-1">
                        {getUploadMethodIcon()}
                        {uploadMethod === 'camera' ? 'Camera' : uploadMethod === 'file' ? 'File' : 'Drag & Drop'}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={removeFile}
                    disabled={isAnalyzing}
                    className="touch-manipulation"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Analysis Button */}
              <Button
                onClick={startAnalysis}
                disabled={isAnalyzing}
                className="w-full h-12 text-aone-lg touch-manipulation"
                size="lg"
              >
                {isAnalyzing ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    Analyzing...
                  </>
                ) : (
                  <>
                    <FileCheck className="w-5 h-5 mr-2" />
                    Start AI Analysis
                  </>
                )}
              </Button>
            </div>
          )}

          {/* Progress Display */}
          {progress && (
            <div className="mt-4 space-y-3">
              <div className="aone-flex-between">
                <span className="text-aone-sm font-aone-medium">{progress.stage}</span>
                <span className="text-aone-sm text-gray-500">{Math.round(progress.progress)}%</span>
              </div>
              <Progress value={progress.progress} className="h-2" />
              {progress.message && (
                <p className="text-xs text-muted-foreground dark:text-gray-400">{progress.message}</p>
              )}
            </div>
          )}

          {/* Error Display */}
          {error && (
            <Alert className="mt-4 border-red-200 dark:border-red-800">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MobileUploadInterface;
