import React, { useState, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  BarChart3,
  Package,
  Ruler,
  Wrench,
  Palette,
  Download,
  Eye,
  CheckCircle,
  AlertTriangle,
  Clock,
  Sparkles,
  Tag,
  Award,
  Smartphone,
  Share,
  Maximize,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { AnalysisResults, CabinetAnalysis, HardwareItem } from '@/services/aiAnalysisService';
import Mobile3DViewer from './Mobile3DViewer';

interface MobileAnalysisViewerProps {
  results: AnalysisResults;
  onGenerateQuote?: () => void;
  onExportResults?: () => void;
  onShare?: () => void;
  className?: string;
}

const MobileAnalysisViewer: React.FC<MobileAnalysisViewerProps> = ({
  results,
  onGenerateQuote,
  onExportResults,
  onShare,
  className = ''
}) => {
  const isMobile = useIsMobile();
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['summary']));
  const [selectedTab, setSelectedTab] = useState('overview');

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 0.8) {
      return <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">High Confidence</Badge>;
    } else if (confidence >= 0.6) {
      return <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">Medium Confidence</Badge>;
    } else {
      return <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">Low Confidence</Badge>;
    }
  };

  const formatProcessingTime = (time: number) => {
    return `${(time / 1000).toFixed(1)}s`;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NZ', {
      style: 'currency',
      currency: 'NZD'
    }).format(amount);
  };

  // Mobile-optimized cabinet summary
  const cabinetSummary = useMemo(() => {
    const types = results.cabinets.reduce((acc, cabinet) => {
      acc[cabinet.type] = (acc[cabinet.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return Object.entries(types).map(([type, count]) => ({ type, count }));
  }, [results.cabinets]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Mobile Header with Key Metrics */}
      <Card className="border-2 border-blue-200 dark:border-blue-800">
        <CardContent className="aone-spacing-md">
          <div className="aone-flex-between mb-aone-md">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-teal-600 rounded-aone-lg aone-flex-center">
                <BarChart3 className="w-4 h-4 text-white" />
              </div>
              <div>
                <h2 className="font-bold text-aone-lg">Analysis Complete</h2>
                {isMobile && <Badge variant="secondary">Mobile View</Badge>}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {getConfidenceBadge(results.confidence.overall)}
            </div>
          </div>

          {/* Hero Cabinet Count */}
          <div className="text-center py-6 bg-gradient-to-br from-blue-50 to-teal-50 dark:from-blue-950 dark:to-teal-950 rounded-aone-lg mb-aone-md">
            <div className="text-4xl font-bold text-aone-sage dark:text-blue-400 mb-2">
              {results.measurements.totalCabinets}
            </div>
            <div className="text-aone-lg font-aone-semibold text-blue-800 dark:text-blue-200">
              Total Cabinets Detected
            </div>
          </div>

          {/* Quick Stats Grid */}
          <div className="grid grid-cols-2 gap-3 text-aone-sm">
            <div className="text-center aone-spacing-sm bg-muted dark:bg-gray-800 rounded-aone-lg">
              <div className="font-bold text-aone-lg">{results.measurements.totalWidth}mm</div>
              <div className="text-muted-foreground dark:text-gray-400">Total Width</div>
            </div>
            <div className="text-center aone-spacing-sm bg-muted dark:bg-gray-800 rounded-aone-lg">
              <div className="font-bold text-aone-lg">{results.hardware.length}</div>
              <div className="text-muted-foreground dark:text-gray-400">Hardware Items</div>
            </div>
            <div className="text-center aone-spacing-sm bg-muted dark:bg-gray-800 rounded-aone-lg">
              <div className="font-bold text-aone-lg">{formatProcessingTime(results.processingTime)}</div>
              <div className="text-muted-foreground dark:text-gray-400">Processing Time</div>
            </div>
            <div className="text-center aone-spacing-sm bg-muted dark:bg-gray-800 rounded-aone-lg">
              <div className="font-bold text-aone-lg">{Math.round(results.confidence.overall * 100)}%</div>
              <div className="text-muted-foreground dark:text-gray-400">Accuracy</div>
            </div>
          </div>

          {/* Mobile Action Buttons */}
          <div className="grid grid-cols-2 gap-3 mt-4">
            <Button
              onClick={onGenerateQuote}
              className="touch-manipulation"
              size="sm"
            >
              <Tag className="w-4 h-4 mr-2" />
              Get Quote
            </Button>
            <Button
              variant="outline"
              onClick={onShare}
              className="touch-manipulation"
              size="sm"
            >
              <Share className="w-4 h-4 mr-2" />
              Share
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Mobile-Optimized Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 h-auto">
          <TabsTrigger value="overview" className="text-xs aone-spacing-xs">
            <div className="flex flex-col items-center gap-1">
              <BarChart3 className="w-4 h-4" />
              <span>Overview</span>
            </div>
          </TabsTrigger>
          <TabsTrigger value="cabinets" className="text-xs aone-spacing-xs">
            <div className="flex flex-col items-center gap-1">
              <Package className="w-4 h-4" />
              <span>Cabinets</span>
            </div>
          </TabsTrigger>
          <TabsTrigger value="hardware" className="text-xs aone-spacing-xs">
            <div className="flex flex-col items-center gap-1">
              <Wrench className="w-4 h-4" />
              <span>Hardware</span>
            </div>
          </TabsTrigger>
          <TabsTrigger value="3d" className="text-xs aone-spacing-xs">
            <div className="flex flex-col items-center gap-1">
              <Eye className="w-4 h-4" />
              <span>3D View</span>
            </div>
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          {/* Cabinet Types Summary */}
          <Card>
            <CardHeader 
              className="pb-3 cursor-pointer"
              onClick={() => toggleSection('cabinet-types')}
            >
              <div className="aone-flex-between">
                <CardTitle className="text-aone-lg flex items-center gap-2">
                  <Package className="w-5 h-5" />
                  Cabinet Types
                </CardTitle>
                {expandedSections.has('cabinet-types') ? 
                  <ChevronUp className="w-4 h-4" /> : 
                  <ChevronDown className="w-4 h-4" />
                }
              </div>
            </CardHeader>
            {expandedSections.has('cabinet-types') && (
              <CardContent>
                <div className="space-y-3">
                  {cabinetSummary.map(({ type, count }) => (
                    <div key={type} className="aone-flex-between aone-spacing-sm bg-muted dark:bg-gray-800 rounded-aone-lg">
                      <span className="font-aone-medium">{type}</span>
                      <Badge variant="outline">{count}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            )}
          </Card>

          {/* Confidence Scores */}
          <Card>
            <CardHeader 
              className="pb-3 cursor-pointer"
              onClick={() => toggleSection('confidence')}
            >
              <div className="aone-flex-between">
                <CardTitle className="text-aone-lg flex items-center gap-2">
                  <Award className="w-5 h-5" />
                  Confidence Scores
                </CardTitle>
                {expandedSections.has('confidence') ? 
                  <ChevronUp className="w-4 h-4" /> : 
                  <ChevronDown className="w-4 h-4" />
                }
              </div>
            </CardHeader>
            {expandedSections.has('confidence') && (
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-aone-sm">Overall Accuracy</span>
                      <span className="font-aone-medium">{Math.round(results.confidence.overall * 100)}%</span>
                    </div>
                    <Progress value={results.confidence.overall * 100} />
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-aone-sm">Cabinet Count</span>
                      <span className="font-aone-medium">{Math.round(results.confidence.cabinetCount * 100)}%</span>
                    </div>
                    <Progress value={results.confidence.cabinetCount * 100} />
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-aone-sm">Hardware Detection</span>
                      <span className="font-aone-medium">{Math.round(results.confidence.hardwareDetection * 100)}%</span>
                    </div>
                    <Progress value={results.confidence.hardwareDetection * 100} />
                  </div>
                </div>
              </CardContent>
            )}
          </Card>
        </TabsContent>

        {/* Cabinets Tab */}
        <TabsContent value="cabinets" className="space-y-4">
          {results.cabinets.map((cabinet, index) => (
            <Card key={index}>
              <CardContent className="aone-spacing-md">
                <div className="aone-flex-between mb-3">
                  <h3 className="font-aone-semibold">{cabinet.type}</h3>
                  <Badge variant="outline">
                    {Math.round(cabinet.confidence * 100)}% confidence
                  </Badge>
                </div>
                <div className="grid grid-cols-2 gap-3 text-aone-sm">
                  <div>
                    <span className="text-muted-foreground dark:text-gray-400">Dimensions:</span>
                    <p className="font-aone-medium">{cabinet.dimensions.width}×{cabinet.dimensions.height}mm</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground dark:text-gray-400">Material:</span>
                    <p className="font-aone-medium">{cabinet.material || 'Not specified'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        {/* Hardware Tab */}
        <TabsContent value="hardware" className="space-y-4">
          {results.hardware.map((item, index) => (
            <Card key={index}>
              <CardContent className="aone-spacing-md">
                <div className="aone-flex-between mb-3">
                  <h3 className="font-aone-semibold">{item.type}</h3>
                  <Badge variant="outline">
                    {Math.round(item.confidence * 100)}% confidence
                  </Badge>
                </div>
                <div className="grid grid-cols-2 gap-3 text-aone-sm">
                  <div>
                    <span className="text-muted-foreground dark:text-gray-400">Brand:</span>
                    <p className="font-aone-medium">{item.brand || 'Unknown'}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground dark:text-gray-400">Quantity:</span>
                    <p className="font-aone-medium">{item.quantity}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        {/* 3D View Tab */}
        <TabsContent value="3d" className="space-y-4">
          {results.reconstruction3D ? (
            <Mobile3DViewer
              reconstruction={results.reconstruction3D}
              onCabinetSelect={(cabinetId) => {
                console.log('Selected cabinet:', cabinetId);
              }}
            />
          ) : (
            <Card>
              <CardContent className="p-aone-xl text-center">
                <Eye className="w-12 h-12 text-gray-400 mx-auto mb-aone-md" />
                <h3 className="text-aone-lg font-aone-semibold mb-2">3D Reconstruction Not Available</h3>
                <p className="text-muted-foreground dark:text-gray-400">
                  3D reconstruction was not generated for this analysis.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MobileAnalysisViewer;
