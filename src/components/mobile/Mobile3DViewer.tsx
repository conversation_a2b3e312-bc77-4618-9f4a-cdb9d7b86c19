import React, { Suspense, useState, useRef, useCallback, useEffect } from 'react';
import { <PERSON>vas, useFrame, useThree } from '@react-three/fiber';
import { OrbitControls, Box, Text, Grid, Environment } from '@react-three/drei';
import * as THREE from 'three';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  RotateCcw,
  ZoomIn,
  ZoomOut,
  Move3D,
  Eye,
  Maximize,
  Minimize,
  Smartphone,
  TouchIcon,
  Hand
} from 'lucide-react';
import { ReconstructionResult, Cabinet3DModel, Point3D } from '@/services/aiAnalysisService';

interface Mobile3DViewerProps {
  reconstruction: ReconstructionResult;
  className?: string;
  onCabinetSelect?: (cabinetId: string) => void;
}

interface Cabinet3DProps {
  cabinet: Cabinet3DModel;
  isSelected: boolean;
  onSelect: () => void;
}

// Mobile-optimized Cabinet 3D Component
const Cabinet3D: React.FC<Cabinet3DProps> = ({ cabinet, isSelected, onSelect }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);

  useFrame((state) => {
    if (meshRef.current && isSelected) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime) * 0.1;
    }
  });

  const position: [number, number, number] = [
    cabinet.position.x / 1000,
    cabinet.dimensions.height / 2000,
    cabinet.position.z / 1000
  ];

  const scale: [number, number, number] = [
    cabinet.dimensions.width / 1000,
    cabinet.dimensions.height / 1000,
    cabinet.dimensions.depth / 1000
  ];

  return (
    <Box
      ref={meshRef}
      position={position}
      scale={scale}
      onClick={onSelect}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
    >
      <meshStandardMaterial
        color={isSelected ? 'hsl(var(--status-info))' : hovered ? 'hsl(var(--status-info))' : 'rgb(var(--preset-accent))'}
        transparent
        opacity={isSelected ? 0.9 : 0.7}
        roughness={0.3}
        metalness={0.1}
      />
      
      {/* Cabinet label for mobile */}
      <Text
        position={[0, scale[1] / 2 + 0.2, 0]}
        fontSize={0.15}
        color={isSelected ? 'hsl(var(--status-info))' : 'rgb(var(--aone-charcoal))'}
        anchorX="center"
        anchorY="middle"
      >
        {cabinet.type}
      </Text>
    </Box>
  );
};

// Mobile-optimized Scene Component
const MobileScene3D: React.FC<{
  reconstruction: ReconstructionResult;
  selectedCabinet: string | null;
  onCabinetSelect: (id: string) => void;
}> = ({ reconstruction, selectedCabinet, onCabinetSelect }) => {
  const { camera, gl } = useThree();
  
  useEffect(() => {
    // Mobile-specific camera settings
    camera.fov = 70; // Wider field of view for mobile
    camera.updateProjectionMatrix();
    
    // Optimize renderer for mobile
    gl.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    gl.shadowMap.enabled = false; // Disable shadows for performance
    gl.antialias = false; // Disable antialiasing for performance
  }, [camera, gl]);

  return (
    <>
      {/* Optimized lighting for mobile */}
      <ambientLight intensity={0.6} />
      <directionalLight position={[10, 10, 5]} intensity={0.8} />
      <pointLight position={[-10, -10, -5]} intensity={0.3} />

      {/* Simplified grid for mobile */}
      <Grid 
        args={[reconstruction.roomDimensions.width / 1000, reconstruction.roomDimensions.depth / 1000]} 
        cellSize={1} 
        cellThickness={0.5} 
        cellColor="rgb(var(--aone-soft-gray))" 
        sectionSize={4} 
        sectionThickness={1} 
        sectionColor="rgb(var(--aone-soft-gray))"
        fadeDistance={20}
        fadeStrength={1}
        followCamera={false}
        infiniteGrid={false}
        position={[0, 0, 0]}
      />

      {/* Cabinets */}
      {reconstruction.cabinets.map((cabinet) => (
        <Cabinet3D
          key={cabinet.id}
          cabinet={cabinet}
          isSelected={selectedCabinet === cabinet.id}
          onSelect={() => onCabinetSelect(cabinet.id)}
        />
      ))}

      {/* Mobile-optimized controls */}
      <OrbitControls 
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        maxPolarAngle={Math.PI / 2}
        minDistance={3}
        maxDistance={15}
        zoomSpeed={0.8}
        rotateSpeed={0.6}
        panSpeed={0.8}
        enableDamping={true}
        dampingFactor={0.05}
        touches={{
          ONE: THREE.TOUCH.ROTATE,
          TWO: THREE.TOUCH.DOLLY_PAN
        }}
      />
    </>
  );
};

const Mobile3DViewer: React.FC<Mobile3DViewerProps> = ({
  reconstruction,
  className = '',
  onCabinetSelect
}) => {
  const isMobile = useIsMobile();
  const [selectedCabinet, setSelectedCabinet] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [touchInstructions, setTouchInstructions] = useState(true);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const handleCabinetSelect = useCallback((cabinetId: string) => {
    setSelectedCabinet(cabinetId);
    onCabinetSelect?.(cabinetId);
  }, [onCabinetSelect]);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const resetView = () => {
    // Reset camera position - would need to access OrbitControls ref
    setSelectedCabinet(null);
  };

  const dismissInstructions = () => {
    setTouchInstructions(false);
    localStorage.setItem('mobile-3d-instructions-dismissed', 'true');
  };

  useEffect(() => {
    // Check if instructions were previously dismissed
    const dismissed = localStorage.getItem('mobile-3d-instructions-dismissed');
    if (dismissed) {
      setTouchInstructions(false);
    }
  }, []);

  const selectedCabinetData = selectedCabinet
    ? reconstruction.cabinets.find(c => c.id === selectedCabinet)
    : null;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Mobile Touch Instructions */}
      {isMobile && touchInstructions && (
        <Card className="border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-950/20">
          <CardContent className="aone-spacing-md">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                <TouchIcon className="w-5 h-5 text-status-info" />
                <div>
                  <h4 className="font-aone-medium text-blue-900 dark:text-blue-100">Touch Controls</h4>
                  <div className="text-aone-sm text-aone-sage-dark dark:text-blue-300 space-y-1">
                    <div className="flex items-center gap-2">
                      <Hand className="w-3 h-3" />
                      <span>One finger: Rotate view</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <ZoomIn className="w-3 h-3" />
                      <span>Pinch: Zoom in/out</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Move3D className="w-3 h-3" />
                      <span>Two fingers: Pan view</span>
                    </div>
                  </div>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={dismissInstructions}
                className="text-status-info hover:text-blue-800"
              >
                Got it
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 3D Viewer Controls */}
      <div className="aone-flex-between">
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="flex items-center gap-1">
            <Smartphone className="w-3 h-3" />
            {isMobile ? 'Mobile Optimized' : 'Desktop'}
          </Badge>
          {selectedCabinetData && (
            <Badge variant="outline">
              {selectedCabinetData.type} Selected
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={resetView}
            className="touch-manipulation"
          >
            <RotateCcw className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={toggleFullscreen}
            className="touch-manipulation"
          >
            {isFullscreen ? <Minimize className="w-4 h-4" /> : <Maximize className="w-4 h-4" />}
          </Button>
        </div>
      </div>

      {/* 3D Canvas */}
      <Card className={isFullscreen ? 'fixed inset-0 z-50 rounded-none' : 'h-[400px] md:h-[500px]'}>
        <CardContent className="p-0 h-full">
          <div
            className="h-full w-full relative"
            onContextMenu={(e) => e.preventDefault()}
            style={{ touchAction: 'none' }}
          >
            <Suspense fallback={
              <div className="aone-flex-center h-full bg-muted dark:bg-gray-900">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  <p className="text-aone-sm text-aone-charcoal dark:text-gray-400">Loading 3D model...</p>
                  {isMobile && (
                    <p className="text-xs text-aone-soft-gray mt-1">Optimized for mobile</p>
                  )}
                </div>
              </div>
            }>
              <Canvas
                ref={canvasRef}
                camera={{
                  position: [5, 5, 5],
                  fov: isMobile ? 70 : 60,
                  near: 0.1,
                  far: 1000
                }}
                style={{ height: '100%', width: '100%' }}
                dpr={isMobile ? [1, 2] : [1, 2]} // Limit pixel ratio on mobile
                performance={{ min: 0.5 }} // Allow frame rate to drop for performance
                onCreated={(state) => {
                  console.log('Mobile 3D Canvas created successfully');
                  
                  // Mobile-specific optimizations
                  if (isMobile) {
                    state.gl.setPixelRatio(Math.min(window.devicePixelRatio, 2));
                    state.gl.shadowMap.enabled = false;
                    state.gl.antialias = false;
                  }
                  
                  // Prevent context menu and other browser behaviors
                  state.gl.domElement.addEventListener('contextmenu', (e) => e.preventDefault());
                  state.gl.domElement.addEventListener('touchstart', (e) => e.preventDefault());
                }}
                onError={(error) => {
                  console.error('Mobile 3D Canvas error:', error);
                }}
              >
                <MobileScene3D
                  reconstruction={reconstruction}
                  selectedCabinet={selectedCabinet}
                  onCabinetSelect={handleCabinetSelect}
                />
              </Canvas>
            </Suspense>

            {/* Fullscreen close button */}
            {isFullscreen && (
              <Button
                variant="outline"
                size="sm"
                onClick={toggleFullscreen}
                className="absolute top-4 right-4 z-10 touch-manipulation"
              >
                <Minimize className="w-4 h-4" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Selected Cabinet Info */}
      {selectedCabinetData && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-aone-lg">Cabinet Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-aone-md text-aone-sm">
              <div>
                <span className="font-aone-medium">Type:</span>
                <p className="text-aone-charcoal dark:text-gray-400">{selectedCabinetData.type}</p>
              </div>
              <div>
                <span className="font-aone-medium">Confidence:</span>
                <p className="text-aone-charcoal dark:text-gray-400">
                  {Math.round(selectedCabinetData.confidence * 100)}%
                </p>
              </div>
              <div>
                <span className="font-aone-medium">Dimensions:</span>
                <p className="text-aone-charcoal dark:text-gray-400">
                  {selectedCabinetData.dimensions.width}×{selectedCabinetData.dimensions.height}×{selectedCabinetData.dimensions.depth}mm
                </p>
              </div>
              <div>
                <span className="font-aone-medium">Position:</span>
                <p className="text-aone-charcoal dark:text-gray-400">
                  ({selectedCabinetData.position.x}, {selectedCabinetData.position.z})
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Mobile3DViewer;
