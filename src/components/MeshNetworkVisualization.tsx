import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Network, 
  Users, 
  Zap, 
  Activity, 
  Wifi, 
  WifiOff, 
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';

export interface MeshNetworkData {
  totalMeshNetworks: number;
  totalPeers: number;
  totalConnections: number;
  averageHealth: number;
  timestamp: string;
}

export interface MeshTopologyData {
  projectId: string;
  peerCount: number;
  connectionCount: number;
  health: number;
}

export interface MeshNetworkVisualizationProps {
  className?: string;
}

/**
 * Mesh Network Visualization Component
 * 
 * Displays real-time mesh network topology, health metrics, and connection status
 * Integrates with the performance monitoring dashboard
 */
export const MeshNetworkVisualization: React.FC<MeshNetworkVisualizationProps> = ({ 
  className = '' 
}) => {
  const [meshData, setMeshData] = useState<MeshNetworkData | null>(null);
  const [topologies, setTopologies] = useState<MeshTopologyData[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Mock data for demonstration - in real implementation, this would come from WebSocket
  useEffect(() => {
    const mockData: MeshNetworkData = {
      totalMeshNetworks: 3,
      totalPeers: 12,
      totalConnections: 18,
      averageHealth: 0.87,
      timestamp: new Date().toISOString()
    };

    const mockTopologies: MeshTopologyData[] = [
      { projectId: 'project-1', peerCount: 5, connectionCount: 8, health: 0.92 },
      { projectId: 'project-2', peerCount: 4, connectionCount: 6, health: 0.85 },
      { projectId: 'project-3', peerCount: 3, connectionCount: 4, health: 0.78 }
    ];

    setMeshData(mockData);
    setTopologies(mockTopologies);
    setIsConnected(true);
    setLastUpdated(new Date());

    // Simulate real-time updates
    const interval = setInterval(() => {
      setMeshData(prev => prev ? {
        ...prev,
        totalPeers: prev.totalPeers + Math.floor(Math.random() * 3) - 1,
        totalConnections: prev.totalConnections + Math.floor(Math.random() * 3) - 1,
        averageHealth: Math.max(0.5, Math.min(1, prev.averageHealth + (Math.random() - 0.5) * 0.1)),
        timestamp: new Date().toISOString()
      } : null);
      setLastUpdated(new Date());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Draw mesh topology visualization
  useEffect(() => {
    if (!canvasRef.current || !meshData) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw mesh network visualization
    drawMeshTopology(ctx, canvas.width, canvas.height, meshData);
  }, [meshData]);

  const drawMeshTopology = (ctx: CanvasRenderingContext2D, width: number, height: number, data: MeshNetworkData) => {
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) * 0.3;

    // Draw connections
    ctx.strokeStyle = 'hsl(var(--status-info))';
    ctx.lineWidth = 2;
    ctx.globalAlpha = 0.6;

    for (let i = 0; i < data.totalPeers; i++) {
      const angle1 = (i / data.totalPeers) * 2 * Math.PI;
      const x1 = centerX + Math.cos(angle1) * radius;
      const y1 = centerY + Math.sin(angle1) * radius;

      // Draw connections to nearby peers
      for (let j = i + 1; j < Math.min(i + 3, data.totalPeers); j++) {
        const angle2 = (j / data.totalPeers) * 2 * Math.PI;
        const x2 = centerX + Math.cos(angle2) * radius;
        const y2 = centerY + Math.sin(angle2) * radius;

        ctx.beginPath();
        ctx.moveTo(x1, y1);
        ctx.lineTo(x2, y2);
        ctx.stroke();
      }
    }

    // Draw peers
    ctx.globalAlpha = 1;
    for (let i = 0; i < data.totalPeers; i++) {
      const angle = (i / data.totalPeers) * 2 * Math.PI;
      const x = centerX + Math.cos(angle) * radius;
      const y = centerY + Math.sin(angle) * radius;

      // Peer circle
      ctx.fillStyle = data.averageHealth > 0.8 ? 'hsl(var(--status-success))' : data.averageHealth > 0.6 ? 'hsl(var(--status-warning))' : 'hsl(var(--status-error))';
      ctx.beginPath();
      ctx.arc(x, y, 8, 0, 2 * Math.PI);
      ctx.fill();

      // Peer border
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 2;
      ctx.stroke();
    }

    // Draw center hub
    ctx.fillStyle = 'rgb(var(--preset-accent))';
    ctx.beginPath();
    ctx.arc(centerX, centerY, 12, 0, 2 * Math.PI);
    ctx.fill();
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 3;
    ctx.stroke();
  };

  const getHealthColor = (health: number) => {
    if (health >= 0.8) return 'text-status-success';
    if (health >= 0.6) return 'text-yellow-600';
    return 'text-status-error';
  };

  const getHealthBadgeVariant = (health: number) => {
    if (health >= 0.8) return 'default';
    if (health >= 0.6) return 'secondary';
    return 'destructive';
  };

  const formatLatency = (ms: number) => `${ms.toFixed(0)}ms`;
  const formatHealth = (health: number) => `${(health * 100).toFixed(1)}%`;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="aone-flex-between">
            <div className="flex items-center">
              <Network className="h-5 w-5 mr-2" />
              Mesh Network Status
            </div>
            <div className="flex items-center space-x-2">
              {isConnected ? (
                <Wifi className="h-4 w-4 text-status-success" />
              ) : (
                <WifiOff className="h-4 w-4 text-status-error" />
              )}
              <Button variant="outline" size="sm">
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </CardTitle>
          <CardDescription>
            Real-time P2P mesh network monitoring and topology visualization
          </CardDescription>
        </CardHeader>
        <CardContent>
          {meshData ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-aone-md">
              <div className="text-center">
                <div className="text-aone-2xl font-aone-bold text-status-info">{meshData.totalMeshNetworks}</div>
                <div className="text-aone-sm text-aone-charcoal">Active Networks</div>
              </div>
              <div className="text-center">
                <div className="text-aone-2xl font-aone-bold text-status-success">{meshData.totalPeers}</div>
                <div className="text-aone-sm text-aone-charcoal">Connected Peers</div>
              </div>
              <div className="text-center">
                <div className="text-aone-2xl font-aone-bold text-preset-accent">{meshData.totalConnections}</div>
                <div className="text-aone-sm text-aone-charcoal">P2P Connections</div>
              </div>
              <div className="text-center">
                <div className={`text-aone-2xl font-aone-bold ${getHealthColor(meshData.averageHealth)}`}>
                  {formatHealth(meshData.averageHealth)}
                </div>
                <div className="text-aone-sm text-aone-charcoal">Network Health</div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-aone-soft-gray">
              No mesh network data available
            </div>
          )}
        </CardContent>
      </Card>

      {/* Detailed View */}
      <Tabs defaultValue="topology" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="topology">Topology</TabsTrigger>
          <TabsTrigger value="projects">Projects</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
        </TabsList>

        <TabsContent value="topology" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Network Topology</CardTitle>
              <CardDescription>
                Visual representation of mesh network connections
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center">
                <canvas
                  ref={canvasRef}
                  width={400}
                  height={300}
                  className="border rounded-aone-lg bg-muted"
                />
              </div>
              <div className="mt-4 flex justify-center space-x-6 text-aone-sm">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-status-success rounded-full mr-2"></div>
                  Healthy ({formatHealth(0.8)}+)
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-status-warning rounded-full mr-2"></div>
                  Warning ({formatHealth(0.6)}-{formatHealth(0.8)})
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-status-error rounded-full mr-2"></div>
                  Critical (&lt;{formatHealth(0.6)})
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="projects" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Project Mesh Networks</CardTitle>
              <CardDescription>
                Individual project mesh network status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {topologies.map((topology) => (
                  <div key={topology.projectId} className="aone-flex-between aone-spacing-sm border rounded-aone-lg">
                    <div className="flex items-center space-x-3">
                      <Users className="h-4 w-4 text-aone-soft-gray" />
                      <div>
                        <div className="font-aone-medium">{topology.projectId}</div>
                        <div className="text-aone-sm text-aone-charcoal">
                          {topology.peerCount} peers, {topology.connectionCount} connections
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={getHealthBadgeVariant(topology.health)}>
                        {formatHealth(topology.health)}
                      </Badge>
                      {topology.health >= 0.8 ? (
                        <CheckCircle className="h-4 w-4 text-status-success" />
                      ) : topology.health >= 0.6 ? (
                        <Clock className="h-4 w-4 text-status-warning" />
                      ) : (
                        <AlertTriangle className="h-4 w-4 text-status-error" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
              <CardDescription>
                Real-time mesh network performance data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-aone-md">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-aone-sm font-aone-medium">Average Latency</span>
                    <span className="text-aone-sm">{formatLatency(45)}</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-status-success h-2 rounded-full" style={{ width: '85%' }}></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-aone-sm font-aone-medium">Connection Quality</span>
                    <span className="text-aone-sm">{formatHealth(0.87)}</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-status-info h-2 rounded-full" style={{ width: '87%' }}></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-aone-sm font-aone-medium">Bandwidth Usage</span>
                    <span className="text-aone-sm">2.3 MB/s</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-preset-accent h-2 rounded-full" style={{ width: '65%' }}></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-aone-sm font-aone-medium">Topology Efficiency</span>
                    <span className="text-aone-sm">{formatHealth(0.92)}</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-indigo-600 h-2 rounded-full" style={{ width: '92%' }}></div>
                  </div>
                </div>
              </div>
              <div className="mt-4 text-xs text-aone-soft-gray">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
