import React, { useState, useMemo } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  BarChart3,
  Package,
  Ruler,
  Wrench,
  Palette,
  Download,
  Eye,
  CheckCircle,
  AlertTriangle,
  Clock,
  TrendingUp,
  Award,
  Target,
  Move3D,
  Layout,
  Brain
} from 'lucide-react';
import { AnalysisResults as AnalysisResultsType, CabinetAnalysis, HardwareItem } from '@/services/aiAnalysisService';
import CabinetReconstructionViewer from './CabinetReconstructionViewer';
import ReasoningChainVisualization from './ReasoningChainVisualization';
import { ReasoningChainParser } from '@/services/reasoningChainParser';
import LayoutOptimizationViewer from './LayoutOptimizationViewer';

interface EnhancedAnalysisResultsProps {
  results: AnalysisResultsType;
  onGenerateQuote?: () => void;
  onExportResults?: () => void;
}

const EnhancedAnalysisResults: React.FC<EnhancedAnalysisResultsProps> = ({
  results,
  onGenerateQuote,
  onExportResults,
}) => {
  const [selectedCabinet, setSelectedCabinet] = useState<CabinetAnalysis | null>(null);
  const [showReasoningChain, setShowReasoningChain] = useState(false);

  // Parse reasoning chain from analysis results
  const reasoningChain = useMemo(() => {
    if (!results.rawAnalysis || !results.model) return null;

    return ReasoningChainParser.parseReasoningChain(
      results.rawAnalysis,
      results.model as 'GPTO1' | 'GPT4O' | 'GPT4O_MINI',
      'Cabinet Analysis',
      results.processingTime || 0,
      ['spatial_analysis', 'cabinet_identification', 'measurement_extraction'],
      ['cabinet_count', 'cabinet_types', 'hardware_detection']
    );
  }, [results]);

  // Enhanced confidence badge with better visual hierarchy
  const getEnhancedConfidenceBadge = (confidence: number) => {
    if (confidence >= 0.8) {
      return (
        <Badge className="confidence-high">
          <Award className="w-3 h-3 mr-1" />
          High ({Math.round(confidence * 100)}%)
        </Badge>
      );
    } else if (confidence >= 0.6) {
      return (
        <Badge className="confidence-medium">
          <Target className="w-3 h-3 mr-1" />
          Medium ({Math.round(confidence * 100)}%)
        </Badge>
      );
    } else {
      return (
        <Badge className="confidence-low">
          <AlertTriangle className="w-3 h-3 mr-1" />
          Low ({Math.round(confidence * 100)}%)
        </Badge>
      );
    }
  };

  const formatProcessingTime = (time: number): string => {
    return `${(time / 1000).toFixed(1)}s`;
  };

  const formatDimensions = (dimensions: { width: number; height: number; depth: number }): string => {
    return `${dimensions.width}×${dimensions.height}×${dimensions.depth}cm`;
  };

  return (
    <div className="space-lg animate-fade-in">
      {/* Hero Cabinet Count Display */}
      <Card className="cabinet-card-hero border-2 border-blue-200 dark:border-blue-800">
        <CardContent className="pt-6">
          <div className="text-center space-md">
            <div className="cabinet-metric-primary inline-block px-8 py-6 rounded-2xl">
              <div className="text-6xl font-bold text-aone-sage dark:text-blue-400 mb-2">
                {results.measurements.totalCabinets}
              </div>
              <div className="text-aone-lg font-aone-semibold text-blue-800 dark:text-blue-200">
                Total Cabinets Detected
              </div>
            </div>
            <div className="aone-flex-center gap-aone-md mt-6">
              {getEnhancedConfidenceBadge(results.confidence.overall)}
              <Badge variant="outline" className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                {formatProcessingTime(results.processingTime)}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-aone-lg">
        <Card className="cabinet-card">
          <CardContent className="p-aone-lg">
            <div className="cabinet-metric-secondary">
              <div className="text-aone-3xl font-aone-bold text-green-600 dark:text-green-400 mb-2">
                {results.measurements.totalLinearMeters}m
              </div>
              <div className="text-aone-sm font-aone-medium text-green-800 dark:text-green-200">
                Linear Meters
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="cabinet-card">
          <CardContent className="p-aone-lg">
            <div className="cabinet-metric-accent">
              <div className="text-aone-3xl font-aone-bold text-purple-600 dark:text-purple-400 mb-2">
                {results.hardware.length}
              </div>
              <div className="text-aone-sm font-aone-medium text-purple-800 dark:text-purple-200">
                Hardware Types
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="cabinet-card">
          <CardContent className="p-aone-lg">
            <div className="cabinet-metric-primary">
              <div className="text-aone-3xl font-aone-bold text-aone-sage dark:text-blue-400 mb-2">
                {Object.keys(results.measurements.cabinetsByType).length}
              </div>
              <div className="text-aone-sm font-aone-medium text-blue-800 dark:text-blue-200">
                Cabinet Types
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* GPT-o1 Reasoning Chain Visualization */}
      {reasoningChain && (
        <ReasoningChainVisualization
          reasoningChain={reasoningChain}
          isVisible={showReasoningChain}
          onToggleVisibility={() => setShowReasoningChain(!showReasoningChain)}
          className="mb-aone-lg"
        />
      )}

      {/* Cabinet Breakdown Summary */}
      <Card className="cabinet-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            Cabinet Breakdown Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-aone-md">
            {Object.entries(results.measurements.cabinetsByType).map(([type, count]) => (
              <div key={type} className="text-center aone-spacing-md rounded-aone-lg bg-muted dark:bg-gray-800">
                <div className="text-aone-2xl font-aone-bold text-foreground dark:text-gray-100 mb-1">
                  {count}
                </div>
                <div className="text-aone-sm text-muted-foreground dark:text-gray-400 capitalize">
                  {type.replace('_', ' ')} Cabinets
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Results Tabs */}
      <Tabs defaultValue="cabinets" className="w-full">
        <TabsList className={`grid w-full ${
          results.reconstruction3D && results.layoutOptimization ? 'grid-cols-6' :
          results.reconstruction3D || results.layoutOptimization ? 'grid-cols-5' : 'grid-cols-4'
        }`}>
          <TabsTrigger value="cabinets" className="flex items-center gap-2 focus-cabinet">
            <Package className="w-4 h-4" />
            Cabinets
          </TabsTrigger>
          <TabsTrigger value="hardware" className="flex items-center gap-2 focus-cabinet">
            <Wrench className="w-4 h-4" />
            Hardware
          </TabsTrigger>
          <TabsTrigger value="measurements" className="flex items-center gap-2 focus-cabinet">
            <Ruler className="w-4 h-4" />
            Measurements
          </TabsTrigger>
          <TabsTrigger value="materials" className="flex items-center gap-2 focus-cabinet">
            <Palette className="w-4 h-4" />
            Materials
          </TabsTrigger>
          {results.layoutOptimization && (
            <TabsTrigger value="layout-optimization" className="flex items-center gap-2 focus-cabinet">
              <Layout className="w-4 h-4" />
              Layout Optimization
            </TabsTrigger>
          )}
          {results.reconstruction3D && (
            <TabsTrigger value="3d-reconstruction" className="flex items-center gap-2 focus-cabinet">
              <Move3D className="w-4 h-4" />
              3D Model
            </TabsTrigger>
          )}
        </TabsList>

        {/* Cabinets Tab */}
        <TabsContent value="cabinets" className="space-md animate-slide-up">
          <Card className="cabinet-card">
            <CardHeader>
              <CardTitle>Individual Cabinet Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3">
                {results.cabinets.map((cabinet) => (
                  <div
                    key={cabinet.id}
                    className={`aone-spacing-md border rounded-aone-lg cursor-pointer aone-micro-interaction focus-cabinet ${
                      selectedCabinet?.id === cabinet.id 
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-950 shadow-cabinet-md' 
                        : 'border-border dark:border-gray-700 hover:border-border dark:hover:border-gray-600 hover:shadow-cabinet-sm'
                    }`}
                    onClick={() => setSelectedCabinet(cabinet)}
                    role="button"
                    tabIndex={0}
                    aria-label={`Select ${cabinet.type} cabinet for detailed view`}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        setSelectedCabinet(cabinet);
                      }
                    }}
                  >
                    <div className="aone-flex-between">
                      <div>
                        <h4 className="font-aone-medium text-foreground dark:text-gray-100">
                          {cabinet.type} Cabinet
                        </h4>
                        <p className="text-aone-sm text-gray-500 dark:text-gray-400">
                          {formatDimensions(cabinet.dimensions)}
                        </p>
                      </div>
                      <div className="text-right space-xs">
                        {getEnhancedConfidenceBadge(cabinet.confidence)}
                        <div className="mt-1">
                          <Badge variant="outline" className="text-xs">
                            {cabinet.materials.join(', ')}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Hardware Tab */}
        <TabsContent value="hardware" className="space-md animate-slide-up">
          <Card className="cabinet-card">
            <CardHeader>
              <CardTitle>Hardware Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-aone-md">
                {results.hardware.map((item, index) => (
                  <div key={index} className="aone-flex-between aone-spacing-md border rounded-aone-lg hover:shadow-cabinet-sm transition-shadow">
                    <div>
                      <h4 className="font-aone-medium text-foreground dark:text-gray-100">
                        {item.type.replace('_', ' ')}
                      </h4>
                      <p className="text-aone-sm text-gray-500 dark:text-gray-400">
                        Quantity: {item.quantity}
                        {item.specifications && Object.keys(item.specifications).length > 0 && (
                          <span className="ml-2">
                            • {Object.entries(item.specifications).map(([key, value]) => 
                              `${key}: ${value}`
                            ).join(', ')}
                          </span>
                        )}
                      </p>
                    </div>
                    <div className="text-right space-xs">
                      {getEnhancedConfidenceBadge(item.confidence)}
                      {item.estimatedCost && (
                        <div className="text-aone-sm font-aone-medium text-foreground dark:text-gray-100">
                          ${item.estimatedCost.toFixed(2)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Measurements Tab */}
        <TabsContent value="measurements" className="space-md animate-slide-up">
          <Card className="cabinet-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Measurement Accuracy Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-md">
                {[
                  { label: 'Cabinet Count Accuracy', value: results.confidence.cabinetCount },
                  { label: 'Cabinet Type Classification', value: results.confidence.cabinetTypes },
                  { label: 'Hardware Detection', value: results.confidence.hardware },
                  { label: 'Measurement Precision', value: results.confidence.measurements }
                ].map((metric) => (
                  <div key={metric.label}>
                    <div className="flex justify-between mb-2">
                      <span className="text-aone-sm font-aone-medium">{metric.label}</span>
                      <span className="text-aone-sm font-bold">{Math.round(metric.value * 100)}%</span>
                    </div>
                    <Progress value={metric.value * 100} className="h-2" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Materials Tab */}
        <TabsContent value="materials" className="space-md animate-slide-up">
          <Card className="cabinet-card">
            <CardHeader>
              <CardTitle>Material Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-lg">
                <div>
                  <h4 className="font-aone-medium mb-3">Finishes</h4>
                  <div className="flex flex-wrap gap-2">
                    {results.materials.finishes.map((finish, index) => (
                      <Badge key={index} variant="secondary" className="px-3 py-1">
                        {finish}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="font-aone-medium mb-3">Colors</h4>
                  <div className="flex flex-wrap gap-2">
                    {results.materials.colors.map((color, index) => (
                      <Badge key={index} variant="outline" className="px-3 py-1">
                        {color}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="font-aone-medium mb-3">Textures</h4>
                  <div className="flex flex-wrap gap-2">
                    {results.materials.textures.map((texture, index) => (
                      <Badge key={index} variant="secondary" className="px-3 py-1">
                        {texture}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Layout Optimization Tab */}
        {results.layoutOptimization && (
          <TabsContent value="layout-optimization" className="space-md animate-slide-up">
            <LayoutOptimizationViewer
              result={results.layoutOptimization}
              onGenerateReport={() => {
                console.log('Generate layout optimization report');
                // TODO: Implement report generation
              }}
              onImplementChanges={(changes) => {
                console.log('Implement layout changes:', changes);
                // TODO: Integrate with 3D visualization
              }}
            />
          </TabsContent>
        )}

        {/* 3D Reconstruction Tab */}
        {results.reconstruction3D && (
          <TabsContent value="3d-reconstruction" className="space-md animate-slide-up">
            <CabinetReconstructionViewer
              reconstruction={results.reconstruction3D}
              className="w-full"
            />
          </TabsContent>
        )}
      </Tabs>

      {/* Enhanced Action Buttons */}
      <div className="space-y-4">
        {/* Primary Actions */}
        <div className="flex flex-wrap justify-center gap-aone-md">
          <Button onClick={onGenerateQuote} size="lg" className="flex items-center gap-2 focus-cabinet aone-button-primary">
            <BarChart3 className="w-5 h-5" />
            Generate NZD Quote
            <div className="ml-2 text-xs bg-blue-500 px-2 py-1 rounded-full">
              Multi-tier
            </div>
          </Button>
          <Button variant="outline" onClick={onExportResults} size="lg" className="flex items-center gap-2 focus-cabinet">
            <Download className="w-5 h-5" />
            Export Analysis
          </Button>
        </div>

        {/* Feature Highlights */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-aone-md text-center">
          <div className="aone-spacing-sm bg-blue-50 rounded-aone-lg border border-blue-200">
            <div className="text-aone-sm font-aone-medium text-blue-800">3D Reconstruction</div>
            <div className="text-xs text-aone-sage">Interactive spatial analysis</div>
          </div>
          <div className="aone-spacing-sm bg-green-50 rounded-aone-lg border border-green-200">
            <div className="text-aone-sm font-aone-medium text-green-800">Material Recognition</div>
            <div className="text-xs text-green-600">5 regional markets</div>
          </div>
          <div className="aone-spacing-sm bg-purple-50 rounded-aone-lg border border-purple-200">
            <div className="text-aone-sm font-aone-medium text-purple-800">Layout Optimization</div>
            <div className="text-xs text-purple-600">Workflow efficiency</div>
          </div>
        </div>
      </div>

      {/* Errors Display */}
      {results.errors && results.errors.length > 0 && (
        <Card className="border-yellow-200 dark:border-yellow-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-600 dark:text-yellow-400">
              <AlertTriangle className="w-5 h-5" />
              Analysis Warnings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-sm">
              {results.errors.map((error, index) => (
                <div key={index} className="aone-spacing-sm bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800 rounded-aone-lg">
                  <p className="text-aone-sm text-yellow-800 dark:text-yellow-200">{error}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EnhancedAnalysisResults;
