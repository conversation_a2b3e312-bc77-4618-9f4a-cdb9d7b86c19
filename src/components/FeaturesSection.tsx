
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { 
  Brain, 
  Users, 
  Shield, 
  Zap, 
  FileText, 
  BarChart3,
  Globe,
  Settings
} from "lucide-react";

const FeaturesSection = () => {
  return (
    <div className="py-16 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="aone-container">
        <div className="text-center mb-12">
          <h2 className="text-aone-3xl font-aone-bold text-foreground mb-aone-md">
            Enterprise-Grade Kitchen Design Platform
          </h2>
          <p className="text-aone-lg text-muted-foreground max-w-3xl mx-auto">
            Built on the T3 Stack with multi-tenant architecture, advanced AI models, and seamless workflow integration 
            for professional kitchen design teams across New Zealand and beyond.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-aone-lg max-w-6xl mx-auto">
          <Card className="aone-hover-lift transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-blue-100 rounded-aone-lg aone-flex-center mb-aone-md">
                <Brain className="w-6 h-6 text-aone-sage" />
              </div>
              <CardTitle>Dual AI Engine</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                GPT-4o for advanced analysis and o4-mini for validation, trained specifically on Winner Flex and Cabinet Vision drawings.
              </p>
            </CardContent>
          </Card>

          <Card className="aone-hover-lift transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-teal-100 rounded-aone-lg aone-flex-center mb-aone-md">
                <Users className="w-6 h-6 text-teal-600" />
              </div>
              <CardTitle>Multi-Tenant Architecture</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Complete data isolation, role-based access control, and organization-specific customizations for enterprise teams.
              </p>
            </CardContent>
          </Card>

          <Card className="aone-hover-lift transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-purple-100 rounded-aone-lg aone-flex-center mb-aone-md">
                <Shield className="w-6 h-6 text-purple-600" />
              </div>
              <CardTitle>Security & Compliance</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Enterprise-grade security with audit trails, data encryption, and compliance with industry standards.
              </p>
            </CardContent>
          </Card>

          <Card className="aone-hover-lift transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-green-100 rounded-aone-lg aone-flex-center mb-aone-md">
                <Zap className="w-6 h-6 text-green-600" />
              </div>
              <CardTitle>Real-Time Collaboration</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                WebSocket-powered live updates, project sharing, and team collaboration tools for seamless workflow integration.
              </p>
            </CardContent>
          </Card>

          <Card className="aone-hover-lift transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-orange-100 rounded-aone-lg aone-flex-center mb-aone-md">
                <FileText className="w-6 h-6 text-orange-600" />
              </div>
              <CardTitle>Professional Reporting</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Custom branded reports, professional quotations, and CAD-compatible exports for client presentations.
              </p>
            </CardContent>
          </Card>

          <Card className="aone-hover-lift transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-red-100 rounded-aone-lg aone-flex-center mb-aone-md">
                <BarChart3 className="w-6 h-6 text-red-600" />
              </div>
              <CardTitle>Advanced Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Business intelligence dashboards, usage metrics, and performance tracking for data-driven decisions.
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="mt-16 text-center">
          <div className="bg-white rounded-aone-xl p-aone-xl shadow-lg max-w-4xl mx-auto">
            <h3 className="text-aone-2xl font-aone-bold text-foreground mb-aone-md">
              Specialized for New Zealand Kitchen Professionals
            </h3>
            <div className="grid md:grid-cols-2 gap-8 mt-8">
              <div className="flex items-start space-x-3">
                <Globe className="w-6 h-6 text-aone-sage mt-1" />
                <div className="text-left">
                  <h4 className="font-aone-semibold text-foreground">Regional Pricing</h4>
                  <p className="text-muted-foreground">NZ-specific supplier integration and regional cost variations.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Settings className="w-6 h-6 text-teal-600 mt-1" />
                <div className="text-left">
                  <h4 className="font-aone-semibold text-foreground">Custom Workflows</h4>
                  <p className="text-muted-foreground">Tailored to local industry standards and business practices.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeaturesSection;
