import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';
import { AlertTriangle, CheckCircle, Clock, TrendingUp, TrendingDown, Activity, Zap } from 'lucide-react';
import { useRealtimeDashboard } from '../hooks/useRealtimeDashboard';
import { ConnectionStatusIndicator, DashboardStatusBadge } from './ConnectionStatusIndicator';
import { MeshNetworkVisualization } from './MeshNetworkVisualization';

interface DashboardData {
  realTimeStats: {
    successRate: number;
    totalTests: number;
    passedTests: number;
    timestamp: string;
  };
  alerts: Array<{
    id: string;
    alert_type: string;
    severity: string;
    message: string;
    current_value: number;
    threshold_value: number;
    timestamp: string;
    resolved: boolean;
  }>;
  trends: Array<{
    test_category: string;
    browser: string;
    total_tests: number;
    passed_tests: number;
    success_rate: number;
    avg_duration: number;
  }>;
  historicalData: Array<{
    hour: string;
    success_rate: number;
    total_tests: number;
  }>;
  thresholds: {
    successRate: number;
    successRateWarning: number;
    maxFailureSpike: number;
    maxDurationIncrease: number;
  };
}

const PerformanceMonitoringDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Real-time dashboard WebSocket connection
  const realtimeDashboard = useRealtimeDashboard({
    dashboardType: 'monitoring',
    maxReconnectAttempts: 3,
    reconnectIntervals: [1000, 2000, 4000],
    dataHistoryLimit: 100
  });

  useEffect(() => {
    fetchDashboardData();
    
    // Set up real-time updates every 30 seconds
    const interval = setInterval(fetchDashboardData, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/performance-monitoring/dashboard');
      if (!response.ok) {
        throw new Error('Failed to fetch dashboard data');
      }
      
      const result = await response.json();
      setDashboardData(result.data);
      setLastUpdated(new Date());
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const resolveAlert = async (alertId: string) => {
    try {
      const response = await fetch(`/api/performance-monitoring/alerts/${alertId}/resolve`, {
        method: 'PUT'
      });
      
      if (response.ok) {
        fetchDashboardData(); // Refresh data
      }
    } catch (err) {
      console.error('Failed to resolve alert:', err);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'default';
    }
  };

  const getSuccessRateStatus = (rate: number, threshold: number) => {
    if (rate >= threshold) return { icon: CheckCircle, color: 'text-status-success', status: 'Healthy' };
    if (rate >= threshold - 2) return { icon: AlertTriangle, color: 'text-yellow-600', status: 'Warning' };
    return { icon: AlertTriangle, color: 'text-status-error', status: 'Critical' };
  };

  if (loading) {
    return (
      <div className="aone-flex-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading performance data...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Failed to load performance monitoring data: {error}
        </AlertDescription>
      </Alert>
    );
  }

  if (!dashboardData) {
    return (
      <Alert>
        <AlertDescription>
          No performance monitoring data available.
        </AlertDescription>
      </Alert>
    );
  }

  const { realTimeStats, alerts, trends, historicalData, thresholds } = dashboardData;
  const successRateStatus = getSuccessRateStatus(realTimeStats?.successRate || 0, thresholds.successRate);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <div className="flex items-center gap-aone-md mb-2">
            <h1 className="text-aone-3xl font-aone-bold">Performance Monitoring</h1>
            <DashboardStatusBadge
              status={realtimeDashboard.connectionStatus}
              lastUpdate={realtimeDashboard.lastUpdate}
            />
          </div>
          <p className="text-aone-charcoal">
            Real-time test execution monitoring and 91.7% success rate tracking
          </p>
        </div>
        <div className="flex items-center gap-aone-md">
          <ConnectionStatusIndicator
            status={realtimeDashboard.connectionStatus}
            reconnectAttempts={realtimeDashboard.reconnectAttempts}
            maxReconnectAttempts={3}
            lastUpdate={realtimeDashboard.lastUpdate}
            onReconnect={realtimeDashboard.reconnect}
            className="hidden md:flex"
          />
          <div className="text-right">
            <p className="text-aone-sm text-aone-soft-gray">Last updated</p>
            <p className="text-aone-sm font-aone-medium">{lastUpdated.toLocaleTimeString()}</p>
          </div>
        </div>
      </div>

      {/* Real-time Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-aone-md">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-aone-sm font-aone-medium">Success Rate</CardTitle>
            <successRateStatus.icon className={`h-4 w-4 ${successRateStatus.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-aone-2xl font-aone-bold">
              {realTimeStats?.successRate?.toFixed(1) || 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              {successRateStatus.status} (Threshold: {thresholds.successRate}%)
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-aone-sm font-aone-medium">Total Tests</CardTitle>
            <Activity className="h-4 w-4 text-status-info" />
          </CardHeader>
          <CardContent>
            <div className="text-aone-2xl font-aone-bold">{realTimeStats?.totalTests || 0}</div>
            <p className="text-xs text-muted-foreground">
              Last hour
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-aone-sm font-aone-medium">Passed Tests</CardTitle>
            <CheckCircle className="h-4 w-4 text-status-success" />
          </CardHeader>
          <CardContent>
            <div className="text-aone-2xl font-aone-bold">{realTimeStats?.passedTests || 0}</div>
            <p className="text-xs text-muted-foreground">
              Successful executions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-aone-sm font-aone-medium">Active Alerts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-status-error" />
          </CardHeader>
          <CardContent>
            <div className="text-aone-2xl font-aone-bold">{alerts?.length || 0}</div>
            <p className="text-xs text-muted-foreground">
              Unresolved issues
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Alerts Section */}
      {alerts && alerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-status-error" />
              Active Performance Alerts
            </CardTitle>
            <CardDescription>
              Critical issues requiring immediate attention
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {alerts.map((alert) => (
                <div key={alert.id} className="aone-flex-between aone-spacing-sm border rounded-aone-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant={getSeverityColor(alert.severity)}>
                        {alert.severity.toUpperCase()}
                      </Badge>
                      <span className="text-aone-sm text-aone-soft-gray">
                        {new Date(alert.timestamp).toLocaleString()}
                      </span>
                    </div>
                    <p className="text-aone-sm">{alert.message}</p>
                    <p className="text-xs text-aone-soft-gray">
                      Current: {alert.current_value} | Threshold: {alert.threshold_value}
                    </p>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => resolveAlert(alert.id)}
                  >
                    Resolve
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Charts and Analytics */}
      <Tabs defaultValue="trends" className="space-y-4">
        <TabsList>
          <TabsTrigger value="trends">Test Trends</TabsTrigger>
          <TabsTrigger value="historical">Historical Data</TabsTrigger>
          <TabsTrigger value="categories">By Category</TabsTrigger>
          <TabsTrigger value="mesh">Mesh Network</TabsTrigger>
        </TabsList>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Success Rate Trends (Last 7 Days)</CardTitle>
              <CardDescription>
                Hourly success rate tracking with 91.7% threshold line
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={historicalData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="hour" 
                    tickFormatter={(value) => new Date(value).toLocaleDateString()}
                  />
                  <YAxis domain={[80, 100]} />
                  <Tooltip 
                    labelFormatter={(value) => new Date(value).toLocaleString()}
                    formatter={(value: number) => [`${value.toFixed(1)}%`, 'Success Rate']}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="success_rate" 
                    stroke="hsl(var(--status-info))" 
                    strokeWidth={2}
                    dot={{ r: 3 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey={() => thresholds.successRate} 
                    stroke="hsl(var(--status-error))" 
                    strokeDasharray="5 5"
                    strokeWidth={1}
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="historical" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Test Execution Volume</CardTitle>
              <CardDescription>
                Number of tests executed over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={historicalData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="hour" 
                    tickFormatter={(value) => new Date(value).toLocaleDateString()}
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={(value) => new Date(value).toLocaleString()}
                    formatter={(value: number) => [value, 'Tests']}
                  />
                  <Bar dataKey="total_tests" fill="hsl(var(--status-info))" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-aone-md">
            <Card>
              <CardHeader>
                <CardTitle>Success Rate by Category</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {trends?.map((trend, index) => (
                    <div key={index} className="aone-flex-between">
                      <div>
                        <p className="font-aone-medium">{trend.test_category}</p>
                        <p className="text-aone-sm text-aone-soft-gray">{trend.browser}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-aone-medium">{trend.success_rate.toFixed(1)}%</p>
                        <p className="text-aone-sm text-aone-soft-gray">
                          {trend.passed_tests}/{trend.total_tests} tests
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Average Duration by Category</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {trends?.map((trend, index) => (
                    <div key={index} className="aone-flex-between">
                      <div>
                        <p className="font-aone-medium">{trend.test_category}</p>
                        <p className="text-aone-sm text-aone-soft-gray">{trend.browser}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-aone-medium">{Math.round(trend.avg_duration)}ms</p>
                        <p className="text-aone-sm text-aone-soft-gray">average</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="mesh" className="space-y-4">
          <MeshNetworkVisualization />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PerformanceMonitoringDashboard;
