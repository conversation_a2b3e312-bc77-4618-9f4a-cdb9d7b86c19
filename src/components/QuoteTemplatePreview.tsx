import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Eye, 
  Download, 
  Settings, 
  Palette, 
  Layout, 
  FileText,
  Users,
  CheckCircle,
  X,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { QuoteTemplate, quoteTemplateService } from '@/services/quoteTemplateService';

interface QuoteTemplatePreviewProps {
  template: QuoteTemplate;
  onClose: () => void;
}

export const QuoteTemplatePreview: React.FC<QuoteTemplatePreviewProps> = ({
  template,
  onClose
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  useEffect(() => {
    generatePreview();
  }, [template.id]);

  const generatePreview = async () => {
    try {
      setLoading(true);
      setError(null);

      // Generate sample data for preview
      const sampleData = {
        cabinetCount: 15,
        materials: ['Oak Veneer', 'Soft-close hinges', 'Quartz countertop'],
        totalCost: 25000,
        customerName: 'Sample Customer',
        projectName: 'Kitchen Renovation'
      };

      const url = await quoteTemplateService.generateTemplatePreview(template.id, sampleData);
      setPreviewUrl(url);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate preview');
    } finally {
      setLoading(false);
    }
  };

  const getFormatBadgeColor = (format: string) => {
    switch (format) {
      case 'basic': return 'bg-blue-100 text-blue-800';
      case 'detailed': return 'bg-green-100 text-green-800';
      case 'professional': return 'bg-purple-100 text-purple-800';
      case 'commercial': return 'bg-orange-100 text-orange-800';
      default: return 'bg-muted text-foreground';
    }
  };

  const renderConfigurationOverview = () => {
    const config = template.template_config;
    const sections = template.sections_config;
    const styling = template.styling_config;

    return (
      <div className="space-y-6">
        {/* Template Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-4 w-4 mr-2" />
              Template Configuration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-aone-md">
              <div>
                <label className="text-aone-sm font-aone-medium text-gray-500">Format</label>
                <div className="mt-1">
                  <Badge className={getFormatBadgeColor(config.format)}>
                    {config.format}
                  </Badge>
                </div>
              </div>
              <div>
                <label className="text-aone-sm font-aone-medium text-gray-500">Features</label>
                <div className="mt-1 space-y-1">
                  {config.include_images && (
                    <Badge variant="outline" className="mr-1 mb-1">Images</Badge>
                  )}
                  {config.include_alternatives && (
                    <Badge variant="outline" className="mr-1 mb-1">Alternatives</Badge>
                  )}
                  {config.include_warranty && (
                    <Badge variant="outline" className="mr-1 mb-1">Warranty</Badge>
                  )}
                  {config.include_3d_renders && (
                    <Badge variant="outline" className="mr-1 mb-1">3D Renders</Badge>
                  )}
                  {config.include_compliance && (
                    <Badge variant="outline" className="mr-1 mb-1">Compliance</Badge>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sections Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Layout className="h-4 w-4 mr-2" />
              Sections Configuration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-aone-md">
              {Object.entries(sections).map(([sectionName, sectionConfig]: [string, any]) => (
                <div key={sectionName} className="aone-flex-between aone-spacing-xs border rounded">
                  <span className="font-aone-medium capitalize">{sectionName}</span>
                  {sectionConfig.enabled ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <X className="h-4 w-4 text-gray-400" />
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Styling Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Palette className="h-4 w-4 mr-2" />
              Styling Configuration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="text-aone-sm font-aone-medium text-gray-500">Color Scheme</label>
                <div className="mt-2 flex space-x-2">
                  <div className="flex items-center space-x-1">
                    <div 
                      className="w-4 h-4 rounded border"
                      style={{ backgroundColor: styling.colors.primary }}
                    />
                    <span className="text-xs">Primary</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div 
                      className="w-4 h-4 rounded border"
                      style={{ backgroundColor: styling.colors.secondary }}
                    />
                    <span className="text-xs">Secondary</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div 
                      className="w-4 h-4 rounded border"
                      style={{ backgroundColor: styling.colors.accent }}
                    />
                    <span className="text-xs">Accent</span>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-aone-md">
                <div>
                  <label className="text-aone-sm font-aone-medium text-gray-500">Typography</label>
                  <div className="mt-1 text-aone-sm">
                    <div>Heading: {styling.fonts.heading}</div>
                    <div>Body: {styling.fonts.body}</div>
                  </div>
                </div>
                <div>
                  <label className="text-aone-sm font-aone-medium text-gray-500">Layout</label>
                  <div className="mt-1 text-aone-sm">
                    <div>Margins: {styling.layout.margins}px</div>
                    <div>Spacing: {styling.layout.spacing}px</div>
                    <div>Columns: {styling.layout.columns}</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderPreviewContent = () => {
    if (loading) {
      return (
        <div className="aone-flex-center py-12">
          <Loader2 className="h-8 w-8 animate-spin mr-2" />
          <span>Generating preview...</span>
        </div>
      );
    }

    if (error) {
      return (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button 
              variant="outline" 
              size="sm" 
              className="ml-2"
              onClick={generatePreview}
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      );
    }

    if (previewUrl) {
      return (
        <div className="space-y-4">
          <div className="border rounded-aone-lg overflow-hidden">
            <iframe
              src={previewUrl}
              className="w-full h-96"
              title="Template Preview"
            />
          </div>
          <div className="flex justify-center">
            <Button variant="outline" onClick={() => window.open(previewUrl, '_blank')}>
              <Download className="h-4 w-4 mr-2" />
              Download Preview PDF
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className="text-center py-12 text-gray-500">
        <FileText className="h-12 w-12 mx-auto mb-aone-md text-gray-400" />
        <p>Preview not available</p>
        <Button variant="outline" className="mt-2" onClick={generatePreview}>
          Generate Preview
        </Button>
      </div>
    );
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Eye className="h-5 w-5 mr-2" />
            Template Preview: {template.template_name}
          </DialogTitle>
          <DialogDescription>
            Preview the template configuration and generated output
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Template Info */}
          <Card>
            <CardHeader>
              <div className="aone-flex-between">
                <div>
                  <CardTitle className="text-aone-lg">{template.template_name}</CardTitle>
                  <CardDescription>{template.description}</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">v{template.version}</Badge>
                  {template.is_default && (
                    <Badge variant="secondary">Default</Badge>
                  )}
                  {!template.is_active && (
                    <Badge variant="outline">Inactive</Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-aone-md text-aone-sm">
                <div>
                  <span className="text-gray-500">Template Code:</span>
                  <span className="ml-2 font-mono">{template.template_code}</span>
                </div>
                <div>
                  <span className="text-gray-500">Customer Segment:</span>
                  <span className="ml-2">
                    {template.customer_segment ? (
                      <Badge className="text-xs">
                        <Users className="h-3 w-3 mr-1" />
                        {template.customer_segment.segment_name}
                      </Badge>
                    ) : (
                      'No specific segment'
                    )}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500">Created:</span>
                  <span className="ml-2">{new Date(template.created_at).toLocaleDateString()}</span>
                </div>
                <div>
                  <span className="text-gray-500">Last Updated:</span>
                  <span className="ml-2">{new Date(template.updated_at).toLocaleDateString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Preview Tabs */}
          <Tabs defaultValue="preview" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="preview">Preview</TabsTrigger>
              <TabsTrigger value="configuration">Configuration</TabsTrigger>
            </TabsList>

            <TabsContent value="preview" className="space-y-4">
              {renderPreviewContent()}
            </TabsContent>

            <TabsContent value="configuration" className="space-y-4">
              {renderConfigurationOverview()}
            </TabsContent>
          </Tabs>
        </div>

        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
