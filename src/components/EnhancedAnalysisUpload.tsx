import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { LoadingState, AnalysisLoadingState, UploadLoadingState } from '@/components/ui/loading';
import { ErrorState, FormError, validateFileType, validateFileSize } from '@/components/ui/error-state';
import { 
  Upload, 
  FileText, 
  Image, 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  Settings,
  X,
  FileCheck,
  Zap,
  Brain,
  Eye,
  Move3D,
  Target
} from 'lucide-react';
import { aiAnalysisService, AnalysisConfig, AnalysisProgress, AnalysisResults } from '@/services/aiAnalysisService';

interface EnhancedAnalysisUploadProps {
  onAnalysisComplete?: (results: AnalysisResults) => void;
  onAnalysisError?: (error: string) => void;
}

const EnhancedAnalysisUpload: React.FC<EnhancedAnalysisUploadProps> = ({
  onAnalysisComplete,
  onAnalysisError,
}) => {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [progress, setProgress] = useState<AnalysisProgress | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [config, setConfig] = useState<AnalysisConfig>({
    useGPT4o: true,
    useReasoning: true,
    useGPTO1: false,
    modelSelection: 'AUTO',
    focusOnMaterials: false,
    focusOnHardware: false,
    enableMultiView: true,
    enable3DReconstruction: true,
    spatialResolution: 'HIGH',
    includeHardwarePositioning: true,
    complexReasoningRequired: false,
  });

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    const file = acceptedFiles[0];
    if (file) {
      // Validate file type and size
      const typeError = validateFileType(file, ['application/pdf', 'image/png', 'image/jpeg']);
      const sizeError = validateFileSize(file, 50);

      if (typeError) {
        setError(typeError);
        return;
      }

      if (sizeError) {
        setError(sizeError);
        return;
      }

      setUploadedFile(file);
      setError(null);
    } else if (rejectedFiles.length > 0) {
      setError('File type not supported or file too large. Please upload a PDF, PNG, or JPG file under 50MB.');
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/png': ['.png'],
      'image/jpeg': ['.jpg', '.jpeg'],
    },
    maxFiles: 1,
    maxSize: 50 * 1024 * 1024, // 50MB
  });

  const handleAnalyze = async () => {
    if (!uploadedFile) return;

    setIsAnalyzing(true);
    setError(null);
    setProgress(null);

    try {
      const results = await aiAnalysisService.analyzeKitchenDesign(
        uploadedFile,
        config,
        (progressUpdate) => {
          setProgress(progressUpdate);
        }
      );

      onAnalysisComplete?.(results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Analysis failed';
      setError(errorMessage);
      onAnalysisError?.(errorMessage);
    } finally {
      setIsAnalyzing(false);
      setProgress(null);
    }
  };

  const getFileIcon = (file: File) => {
    if (file.type === 'application/pdf') {
      return <FileText className="w-8 h-8 text-red-500" />;
    }
    return <Image className="w-8 h-8 text-blue-500" />;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getProgressMessage = (): string => {
    if (!progress) return '';
    
    const messages: Record<string, string> = {
      'uploading': 'Uploading your kitchen design...',
      'processing_pdf': 'Converting PDF to images...',
      'analyzing_gpt4o': 'AI analyzing cabinet layout with GPT-4o...',
      'reasoning_validation': 'Validating analysis with o4-mini reasoning...',
      'extracting_measurements': 'Extracting precise measurements...',
      'identifying_hardware': 'Identifying hardware components...',
      'finalizing': 'Finalizing analysis results...',
    };
    
    return messages[progress.step] || `Processing: ${progress.step.replace('_', ' ')}`;
  };

  const getProgressSteps = () => {
    const steps = [
      { key: 'uploading', label: 'Upload', icon: Upload },
      { key: 'processing_pdf', label: 'Process', icon: FileCheck },
      { key: 'analyzing_gpt4o', label: 'AI Analysis', icon: Brain },
      { key: 'reasoning_validation', label: 'Validation', icon: Zap },
      { key: 'finalizing', label: 'Complete', icon: CheckCircle },
    ];

    return steps.map((step, index) => {
      const isActive = progress?.step === step.key;
      const isCompleted = progress && steps.findIndex(s => s.key === progress.step) > index;
      const Icon = step.icon;

      return (
        <div key={step.key} className={`progress-step ${isActive ? 'progress-step-active' : ''}`}>
          <div className={`w-8 h-8 rounded-full aone-flex-center ${
            isCompleted ? 'bg-green-500 text-white' : 
            isActive ? 'bg-blue-500 text-white animate-pulse-subtle' : 
            'bg-gray-200 dark:bg-gray-700 text-gray-500'
          }`}>
            {isCompleted ? <CheckCircle className="w-4 h-4" /> : <Icon className="w-4 h-4" />}
          </div>
          <span className={`text-aone-sm font-aone-medium ${
            isActive ? 'text-aone-sage dark:text-blue-400' : 
            isCompleted ? 'text-green-600 dark:text-green-400' : 
            'text-gray-500'
          }`}>
            {step.label}
          </span>
        </div>
      );
    });
  };

  return (
    <div className="space-lg">
      {/* Enhanced Upload Area */}
      <Card className="cabinet-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            Upload Kitchen Design
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!uploadedFile ? (
            <div
              {...getRootProps()}
              className={`
                aone-card-elegant p-12 border-2 border-dashed cursor-pointer transition-all duration-300 aone-focus-ring
                ${isDragActive
                  ? 'border-aone-sage bg-aone-sage/5 scale-105'
                  : 'border-aone-sage/30 hover:border-aone-sage/60 hover:bg-aone-sage/5'
                }
              `}
              aria-label="Upload kitchen design file - drag and drop or click to browse"
            >
              <input {...getInputProps()} />
              <div className="text-center aone-fade-in">
                <div className={`
                  w-20 h-20 mx-auto mb-aone-lg rounded-2xl aone-flex-center transition-all duration-300
                  ${isDragActive ? 'bg-aone-sage/20 scale-110' : 'bg-aone-sage/10'}
                `}>
                  <Upload className={`w-10 h-10 transition-colors duration-300 ${
                    isDragActive ? 'text-aone-sage' : 'text-aone-sage/70'
                  }`} />
                </div>
                <h3 className="text-2xl font-aone-semibold text-aone-charcoal mb-3">
                  {isDragActive ? 'Drop your file here' : 'Upload Kitchen Design'}
                </h3>
                <p className="text-aone-charcoal/70 mb-aone-lg leading-relaxed">
                  Drag & drop your kitchen design file or click to browse
                </p>
                <div className="flex justify-center gap-3 mb-aone-lg">
                  <Badge className="bg-aone-sage/10 text-aone-sage border-aone-sage/20">PDF</Badge>
                  <Badge className="bg-aone-sage/10 text-aone-sage border-aone-sage/20">PNG</Badge>
                  <Badge className="bg-aone-sage/10 text-aone-sage border-aone-sage/20">JPG</Badge>
                </div>
                <p className="text-aone-sm text-aone-charcoal/50">
                  Maximum file size: 50MB
                </p>
              </div>
            </div>
          ) : (
            <div className="aone-slide-in">
              <div className="aone-success-state p-aone-lg rounded-aone-xl border-2 border-green-200">
                <div className="aone-flex-between">
                  <div className="flex items-center gap-aone-md">
                    <div className="w-12 h-12 bg-green-100 rounded-aone-xl aone-flex-center">
                      {getFileIcon(uploadedFile)}
                    </div>
                    <div>
                      <h4 className="font-aone-semibold text-aone-charcoal">{uploadedFile.name}</h4>
                      <p className="text-aone-sm text-aone-charcoal/60">{formatFileSize(uploadedFile.size)}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2 text-green-600">
                      <CheckCircle className="w-5 h-5" />
                      <span className="text-aone-sm font-aone-medium">Ready</span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setUploadedFile(null)}
                      disabled={isAnalyzing}
                      className="aone-button-ghost-enhanced aone-spacing-xs"
                      aria-label="Remove uploaded file"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Enhanced Analysis Configuration */}
      {uploadedFile && !isAnalyzing && (
        <Card className="cabinet-card animate-slide-up">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Analysis Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-md">
            {/* AI Model Selection */}
            <div className="mb-aone-lg aone-spacing-md bg-blue-50 dark:bg-blue-950 rounded-aone-lg border border-blue-200 dark:border-blue-800">
              <h4 className="text-aone-sm font-aone-semibold text-blue-900 dark:text-blue-100 mb-3 flex items-center gap-2">
                <Brain className="w-4 h-4" />
                AI Model Selection
              </h4>
              <div className="space-y-3">
                <div>
                  <label className="aone-label dark:text-gray-300 mb-2">
                    Analysis Model
                  </label>
                  <Select
                    value={config.modelSelection || 'AUTO'}
                    onValueChange={(value: 'AUTO' | 'GPT4O' | 'GPT4O_MINI' | 'GPTO1') =>
                      setConfig(prev => ({
                        ...prev,
                        modelSelection: value,
                        useGPTO1: value === 'GPTO1',
                        complexReasoningRequired: value === 'GPTO1'
                      }))
                    }
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select AI model" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="AUTO">
                        <div className="flex items-center gap-2">
                          <Target className="w-4 h-4" />
                          <div>
                            <div className="font-aone-medium">Auto-Select (Recommended)</div>
                            <div className="text-xs text-gray-500">Automatically chooses optimal model</div>
                          </div>
                        </div>
                      </SelectItem>
                      <SelectItem value="GPT4O">
                        <div className="flex items-center gap-2">
                          <Eye className="w-4 h-4" />
                          <div>
                            <div className="font-aone-medium">GPT-4o</div>
                            <div className="text-xs text-gray-500">Standard high-quality analysis</div>
                          </div>
                        </div>
                      </SelectItem>
                      <SelectItem value="GPT4O_MINI">
                        <div className="flex items-center gap-2">
                          <Zap className="w-4 h-4" />
                          <div>
                            <div className="font-aone-medium">GPT-4o Mini</div>
                            <div className="text-xs text-gray-500">Fast, efficient analysis</div>
                          </div>
                        </div>
                      </SelectItem>
                      <SelectItem value="GPTO1">
                        <div className="flex items-center gap-2">
                          <Brain className="w-4 h-4" />
                          <div>
                            <div className="font-aone-medium">GPT-o1 (Advanced Reasoning)</div>
                            <div className="text-xs text-gray-500">Complex multi-step analysis</div>
                          </div>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {config.modelSelection === 'GPTO1' && (
                  <div className="aone-spacing-sm bg-amber-50 dark:bg-amber-950 rounded-aone-lg border border-amber-200 dark:border-amber-800">
                    <div className="flex items-start gap-2">
                      <Brain className="w-4 h-4 text-amber-600 dark:text-amber-400 mt-0.5" />
                      <div className="text-aone-sm text-amber-800 dark:text-amber-200">
                        <div className="font-aone-medium">GPT-o1 Advanced Reasoning</div>
                        <div className="text-xs mt-1">
                          Optimal for complex spatial analysis, multi-step optimization, and advanced material compatibility analysis.
                          Processing time may be longer due to enhanced reasoning capabilities.
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Analysis Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-aone-md">
              {[
                { key: 'focusOnMaterials', label: 'Focus on Materials', icon: Zap },
                { key: 'focusOnHardware', label: 'Focus on Hardware', icon: Settings },
                { key: 'enableMultiView', label: 'Multi-View Analysis', icon: Eye },
                { key: 'useReasoning', label: 'AI Reasoning & Validation', icon: Brain },
                { key: 'enable3DReconstruction', label: '3D Cabinet Reconstruction', icon: Move3D },
                { key: 'includeHardwarePositioning', label: '3D Hardware Positioning', icon: Target },
              ].map((option) => {
                const Icon = option.icon;
                return (
                  <label key={option.key} className="flex items-center space-x-3 aone-spacing-sm rounded-aone-lg border hover:bg-muted dark:hover:bg-gray-800 cursor-pointer focus-cabinet">
                    <input
                      type="checkbox"
                      checked={config[option.key as keyof AnalysisConfig] as boolean}
                      onChange={(e) => setConfig(prev => ({ ...prev, [option.key]: e.target.checked }))}
                      className="rounded aone-focus-ring"
                    />
                    <Icon className="w-4 h-4 text-gray-500" />
                    <span className="text-aone-sm font-aone-medium">{option.label}</span>
                  </label>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Enhanced Analysis Progress */}
      {isAnalyzing && (
        <Card className="aone-card-elegant aone-slide-in">
          <CardContent className="pt-8">
            <div className="space-y-8">
              <AnalysisLoadingState stage={getProgressMessage()} />

              {progress && (
                <div className="space-y-4">
                  <div className="aone-flex-between">
                    <span className="text-aone-sm font-aone-semibold text-aone-charcoal">Overall Progress</span>
                    <span className="text-aone-sm font-aone-medium text-aone-sage">{progress.progress}%</span>
                  </div>
                  <div className="w-full bg-aone-sage/10 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-aone-sage to-green-600 h-3 rounded-full transition-all duration-500 ease-out"
                      style={{ width: `${progress.progress}%` }}
                    />
                  </div>
                </div>
              )}

              <div className="grid grid-cols-5 gap-3">
                {getProgressSteps().map((step, index) => (
                  <div key={index} className="flex flex-col items-center space-y-2">
                    {step}
                  </div>
                ))}
              </div>

              <div className="text-center aone-spacing-md bg-aone-sage/5 rounded-aone-xl border border-aone-sage/10">
                <div className="aone-flex-center gap-3 text-aone-charcoal/70">
                  <Brain className="w-5 h-5 text-aone-sage animate-pulse" />
                  <span className="font-aone-medium">
                    Processing with Azure OpenAI {
                      config.modelSelection === 'GPTO1' ? 'GPT-o1 (Advanced Reasoning)' :
                      config.modelSelection === 'GPT4O' ? 'GPT-4o' :
                      config.modelSelection === 'GPT4O_MINI' ? 'GPT-4o Mini' :
                      'Auto-Selected Model'
                    }
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Enhanced Error Display */}
      {error && (
        <div className="aone-slide-in">
          <ErrorState
            type="upload"
            message={error}
            onRetry={() => setError(null)}
            size="sm"
            className="py-6"
          />
        </div>
      )}

      {/* Enhanced Action Button */}
      {uploadedFile && !isAnalyzing && (
        <div className="flex justify-center aone-slide-in">
          <Button
            onClick={handleAnalyze}
            size="lg"
            className="aone-button-primary-enhanced text-aone-lg px-12 py-4 shadow-lg hover:shadow-xl"
            disabled={!uploadedFile}
          >
            <Brain className="w-6 h-6 mr-3" />
            Start AI Analysis
          </Button>
        </div>
      )}
    </div>
  );
};

export default EnhancedAnalysisUpload;
