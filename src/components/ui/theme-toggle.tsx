import React from 'react';
import { <PERSON>, Sun, Monitor, Briefcase, <PERSON><PERSON>, Minimize, Building, Settings, Zap, Eye, Focus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from '@/components/ui/dropdown-menu';
import { useTheme, useThemePresets, useThemeAccessibility } from '@/hooks/useTheme';
import { ThemePreset } from '@/types/theme';
import { cn } from '@/lib/utils';

interface ThemeToggleProps {
  className?: string;
  variant?: 'default' | 'outline' | 'ghost' | 'icon';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  showLabel?: boolean;
  showPresets?: boolean;
  showAccessibility?: boolean;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className,
  variant = 'ghost',
  size = 'icon',
  showLabel = false,
  showPresets = true,
  showAccessibility = true,
}) => {
  const { theme, setTheme, resolvedTheme, isTransitioning } = useTheme();
  const { preset, setPreset, getPresetConfig } = useThemePresets();
  const { accessibility, toggleHighContrast, toggleFocusEnhancement } = useThemeAccessibility();

  const getThemeIcon = () => {
    if (isTransitioning) {
      return <div className="animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full" />;
    }

    switch (theme) {
      case 'light':
        return <Sun className="h-4 w-4" />;
      case 'dark':
        return <Moon className="h-4 w-4" />;
      case 'system':
        return <Monitor className="h-4 w-4" />;
      default:
        return <Sun className="h-4 w-4" />;
    }
  };

  const getThemeLabel = () => {
    switch (theme) {
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark';
      case 'system':
        return 'System';
      default:
        return 'Light';
    }
  };

  const getPresetIcon = (presetId: ThemePreset) => {
    switch (presetId) {
      case 'professional':
        return <Briefcase className="h-4 w-4" />;
      case 'creative':
        return <Palette className="h-4 w-4" />;
      case 'minimal':
        return <Minimize className="h-4 w-4" />;
      case 'enterprise':
        return <Building className="h-4 w-4" />;
      default:
        return <Briefcase className="h-4 w-4" />;
    }
  };

  const getPresetLabel = (presetId: ThemePreset) => {
    const config = getPresetConfig(presetId);
    return config.name;
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={cn(
            'aone-nav-link micro-interaction focus-ring',
            'hover:bg-aone-sage/10 hover:text-aone-sage',
            'dark:hover:bg-aone-sage/20',
            isTransitioning && 'pointer-events-none opacity-70',
            className
          )}
          disabled={isTransitioning}
          aria-label={`Current theme: ${getThemeLabel()}. Click to change theme.`}
          data-testid="theme-toggle"
        >
          {getThemeIcon()}
          {showLabel && (
            <span className="ml-2 text-aone-sm font-aone-medium">
              {getThemeLabel()}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="aone-card border-aone-sage/20 min-w-[220px]"
      >
        <DropdownMenuLabel className="text-xs font-aone-semibold text-muted-foreground">
          Theme Mode
        </DropdownMenuLabel>
        <DropdownMenuItem
          onClick={() => setTheme('light')}
          className={cn(
            'cursor-pointer micro-interaction',
            'hover:bg-aone-sage/10 hover:text-aone-sage',
            theme === 'light' && 'bg-aone-sage/10 text-aone-sage font-aone-medium'
          )}
        >
          <Sun className="mr-2 h-4 w-4" />
          <span>Light</span>
          {theme === 'light' && (
            <div className="ml-auto w-2 h-2 bg-aone-sage rounded-full" />
          )}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme('dark')}
          className={cn(
            'cursor-pointer micro-interaction',
            'hover:bg-aone-sage/10 hover:text-aone-sage',
            theme === 'dark' && 'bg-aone-sage/10 text-aone-sage font-aone-medium'
          )}
        >
          <Moon className="mr-2 h-4 w-4" />
          <span>Dark</span>
          {theme === 'dark' && (
            <div className="ml-auto w-2 h-2 bg-aone-sage rounded-full" />
          )}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme('system')}
          className={cn(
            'cursor-pointer micro-interaction',
            'hover:bg-aone-sage/10 hover:text-aone-sage',
            theme === 'system' && 'bg-aone-sage/10 text-aone-sage font-aone-medium'
          )}
        >
          <Monitor className="mr-2 h-4 w-4" />
          <span>System</span>
          {theme === 'system' && (
            <div className="ml-auto w-2 h-2 bg-aone-sage rounded-full" />
          )}
        </DropdownMenuItem>

        {showPresets && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuLabel className="text-xs font-aone-semibold text-muted-foreground">
              Theme Presets
            </DropdownMenuLabel>
            {(['professional', 'creative', 'minimal', 'enterprise'] as ThemePreset[]).map((presetId) => {
              const config = getPresetConfig(presetId);
              return (
                <DropdownMenuItem
                  key={presetId}
                  onClick={() => setPreset(presetId)}
                  className={cn(
                    'cursor-pointer micro-interaction',
                    'hover:bg-aone-sage/10 hover:text-aone-sage',
                    preset === presetId && 'bg-aone-sage/10 text-aone-sage font-aone-medium'
                  )}
                >
                  {getPresetIcon(presetId)}
                  <div className="ml-2 flex-1">
                    <div className="text-aone-sm font-aone-medium">{config.name}</div>
                    <div className="text-xs text-muted-foreground">{config.description}</div>
                  </div>
                  {preset === presetId && (
                    <div className="ml-auto w-2 h-2 bg-aone-sage rounded-full" />
                  )}
                </DropdownMenuItem>
              );
            })}
          </>
        )}

        {showAccessibility && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuLabel className="text-xs font-aone-semibold text-muted-foreground">
              Accessibility
            </DropdownMenuLabel>
            <DropdownMenuItem
              onClick={toggleHighContrast}
              className={cn(
                'cursor-pointer micro-interaction',
                'hover:bg-aone-sage/10 hover:text-aone-sage',
                accessibility.highContrast && 'bg-aone-sage/10 text-aone-sage font-aone-medium'
              )}
            >
              <Eye className="mr-2 h-4 w-4" />
              <span>High Contrast</span>
              {accessibility.highContrast && (
                <div className="ml-auto w-2 h-2 bg-aone-sage rounded-full" />
              )}
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={toggleFocusEnhancement}
              className={cn(
                'cursor-pointer micro-interaction',
                'hover:bg-aone-sage/10 hover:text-aone-sage',
                accessibility.focusEnhancement && 'bg-aone-sage/10 text-aone-sage font-aone-medium'
              )}
            >
              <Focus className="mr-2 h-4 w-4" />
              <span>Focus Enhancement</span>
              {accessibility.focusEnhancement && (
                <div className="ml-auto w-2 h-2 bg-aone-sage rounded-full" />
              )}
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

// Simple toggle button variant (just toggles between light/dark)
export const SimpleThemeToggle: React.FC<{
  className?: string;
  size?: 'default' | 'sm' | 'lg' | 'icon';
}> = ({ className, size = 'icon' }) => {
  const { toggleTheme, resolvedTheme, isTransitioning } = useTheme();

  return (
    <Button
      variant="ghost"
      size={size}
      onClick={toggleTheme}
      disabled={isTransitioning}
      className={cn(
        'aone-nav-link micro-interaction focus-ring',
        'hover:bg-aone-sage/10 hover:text-aone-sage',
        'dark:hover:bg-aone-sage/20',
        isTransitioning && 'pointer-events-none opacity-70',
        className
      )}
      aria-label={`Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} mode`}
    >
      {isTransitioning ? (
        <div className="animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full" />
      ) : resolvedTheme === 'dark' ? (
        <Sun className="h-4 w-4" />
      ) : (
        <Moon className="h-4 w-4" />
      )}
    </Button>
  );
};

// Theme status indicator (read-only)
export const ThemeIndicator: React.FC<{
  className?: string;
  showLabel?: boolean;
}> = ({ className, showLabel = true }) => {
  const { theme, resolvedTheme } = useTheme();

  const getStatusColor = () => {
    switch (resolvedTheme) {
      case 'dark':
        return 'bg-blue-500';
      case 'light':
        return 'bg-yellow-500';
      default:
        return 'bg-aone-sage';
    }
  };

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <div className={cn('w-2 h-2 rounded-full', getStatusColor())} />
      {showLabel && (
        <span className="text-aone-sm text-muted-foreground">
          {theme === 'system' ? `System (${resolvedTheme})` : theme}
        </span>
      )}
    </div>
  );
};

export default ThemeToggle;
