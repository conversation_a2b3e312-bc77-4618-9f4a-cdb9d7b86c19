import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-aone-flex-center gap-2 whitespace-nowrap text-aone-sm font-aone-medium aone-focus-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-aone-sage hover:bg-aone-sage/90 text-white rounded-aone-xl shadow-md aone-hover-lift aone-micro-interaction",
        destructive:
          "bg-red-500 text-white hover:bg-red-600 rounded-aone-xl shadow-md aone-hover-lift aone-micro-interaction",
        outline:
          "border-2 border-aone-sage/30 aone-glass hover:border-aone-sage hover:bg-white text-aone-sage rounded-aone-xl shadow-md aone-hover-lift aone-micro-interaction",
        secondary:
          "bg-aone-sage/10 text-aone-sage hover:bg-aone-sage/20 rounded-aone-xl shadow-sm hover:shadow-md aone-micro-interaction",
        ghost: "hover:bg-aone-sage/10 text-aone-sage rounded-aone-xl aone-micro-interaction",
        link: "text-aone-sage underline-offset-4 hover:underline aone-micro-interaction",
        // Phase 1 Visual Experience System - Enhanced variants
        sage: "aone-button-primary-enhanced",
        sageOutline: "aone-button-secondary-enhanced",
        sageGhost: "aone-button-ghost-enhanced",
        enterprise: "aone-glass bg-gradient-to-r from-aone-sage to-green-600 hover:from-aone-sage/90 hover:to-green-600/90 text-white rounded-aone-xl shadow-lg hover:shadow-xl aone-micro-interaction border border-aone-sage/20",
        enterpriseOutline: "aone-input-enterprise text-aone-sage hover:bg-aone-sage/5 rounded-aone-xl aone-micro-interaction",
      },
      size: {
        default: "h-10 px-aone-md py-2",
        sm: "h-9 px-3 py-2",
        lg: "h-12 px-8 py-aone-sm text-aone-lg",
        xl: "h-14 px-12 py-4 text-aone-xl",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
