import React from 'react';
import { Loader2, <PERSON>, <PERSON><PERSON>, <PERSON>rk<PERSON> } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'sage' | 'minimal' | 'enterprise';
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'default',
  className
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  const variantClasses = {
    default: 'text-aone-sage aone-loading-spinner',
    sage: 'text-aone-sage aone-loading-spinner',
    minimal: 'text-gray-400 aone-loading-spinner',
    enterprise: 'text-aone-sage aone-loading-spinner-enterprise'
  };

  return (
    <Loader2
      className={cn(
        sizeClasses[size],
        variantClasses[variant],
        className
      )}
    />
  );
};

interface LoadingSkeletonProps {
  width?: string;
  height?: string;
  className?: string;
  rounded?: boolean;
  variant?: 'default' | 'elegant' | 'enterprise';
}

export const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({
  width = 'w-full',
  height = 'h-4',
  className,
  rounded = true,
  variant = 'default'
}) => {
  const variantClasses = {
    default: 'aone-loading-skeleton',
    elegant: 'aone-loading-skeleton-elegant',
    enterprise: 'aone-loading-skeleton-elegant'
  };

  return (
    <div
      className={cn(
        variantClasses[variant],
        width,
        height,
        rounded && 'rounded-aone-lg',
        className
      )}
    />
  );
};

interface LoadingCardProps {
  title?: boolean;
  lines?: number;
  avatar?: boolean;
  className?: string;
}

export const LoadingCard: React.FC<LoadingCardProps> = ({
  title = true,
  lines = 3,
  avatar = false,
  className
}) => {
  return (
    <div className={cn('aone-card p-aone-lg space-y-4', className)}>
      <div className="flex items-start space-x-4">
        {avatar && (
          <LoadingSkeleton width="w-12" height="h-12" className="rounded-full" />
        )}
        <div className="flex-1 space-y-3">
          {title && (
            <LoadingSkeleton width="w-3/4" height="h-5" />
          )}
          {Array.from({ length: lines }).map((_, index) => (
            <LoadingSkeleton 
              key={index}
              width={index === lines - 1 ? 'w-1/2' : 'w-full'}
              height="h-4"
            />
          ))}
        </div>
      </div>
    </div>
  );
};

interface LoadingStateProps {
  message?: string;
  submessage?: string;
  icon?: 'default' | 'brain' | 'zap';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  message = 'Loading...',
  submessage,
  icon = 'default',
  size = 'md',
  className
}) => {
  const IconComponent = {
    default: LoadingSpinner,
    brain: Brain,
    zap: Zap
  }[icon];

  const sizeClasses = {
    sm: 'py-4',
    md: 'py-8',
    lg: 'py-12'
  };

  const iconSizes = {
    sm: 'sm',
    md: 'md',
    lg: 'lg'
  } as const;

  return (
    <div className={cn(
      'aone-flex-col-center text-center space-y-4',
      sizeClasses[size],
      className
    )}>
      {icon === 'default' ? (
        <IconComponent size={iconSizes[size]} />
      ) : (
        <IconComponent className={cn(
          'aone-loading-spinner text-aone-sage',
          size === 'sm' && 'w-6 h-6',
          size === 'md' && 'w-8 h-8',
          size === 'lg' && 'w-12 h-12'
        )} />
      )}
      
      <div className="space-y-2">
        <p className="text-aone-charcoal font-aone-medium">{message}</p>
        {submessage && (
          <p className="text-aone-charcoal/60 text-aone-sm">{submessage}</p>
        )}
      </div>
    </div>
  );
};

interface LoadingDotsProps {
  className?: string;
}

export const LoadingDots: React.FC<LoadingDotsProps> = ({ className }) => {
  return (
    <span className={cn('aone-loading-dots', className)}>
      Loading
    </span>
  );
};

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  children?: React.ReactNode;
  className?: string;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isVisible,
  message = 'Processing...',
  children,
  className
}) => {
  if (!isVisible) return <>{children}</>;

  return (
    <div className={cn('relative', className)}>
      {children}
      <div className="absolute inset-0 bg-white/80 backdrop-blur-sm aone-flex-center z-50 rounded-aone-lg">
        <LoadingState message={message} size="md" />
      </div>
    </div>
  );
};

// Analysis-specific loading states
export const AnalysisLoadingState: React.FC<{ stage?: string }> = ({ 
  stage = 'Analyzing kitchen design...' 
}) => {
  return (
    <LoadingState
      message="AI Analysis in Progress"
      submessage={stage}
      icon="brain"
      size="lg"
      className="aone-fade-in"
    />
  );
};

export const UploadLoadingState: React.FC<{ progress?: number }> = ({
  progress
}) => {
  return (
    <div className="space-y-4">
      <LoadingState
        message="Uploading file..."
        submessage={progress ? `${progress}% complete` : 'Preparing upload...'}
        icon="zap"
        size="md"
      />
      {progress && (
        <div className="w-full bg-aone-sage/10 rounded-full h-2">
          <div
            className="bg-aone-sage h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        </div>
      )}
    </div>
  );
};

// Phase 1 Visual Experience System - Enterprise Loading Components
export const EnterpriseLoadingState: React.FC<{
  message?: string;
  submessage?: string;
  progress?: number;
  variant?: 'analysis' | 'upload' | 'processing' | 'generating';
}> = ({
  message = 'Processing...',
  submessage,
  progress,
  variant = 'processing'
}) => {
  const variantConfig = {
    analysis: {
      icon: Brain,
      primaryColor: 'text-aone-sage',
      bgColor: 'bg-aone-sage/5',
      borderColor: 'border-aone-sage/20'
    },
    upload: {
      icon: Zap,
      primaryColor: 'text-aone-sage',
      bgColor: 'bg-blue-50/80',
      borderColor: 'border-blue-200'
    },
    processing: {
      icon: Sparkles,
      primaryColor: 'text-aone-sage',
      bgColor: 'bg-aone-sage/5',
      borderColor: 'border-aone-sage/20'
    },
    generating: {
      icon: Brain,
      primaryColor: 'text-purple-600',
      bgColor: 'bg-purple-50/80',
      borderColor: 'border-purple-200'
    }
  };

  const config = variantConfig[variant];
  const IconComponent = config.icon;

  return (
    <div className={cn(
      'aone-glass rounded-2xl p-aone-xl text-center space-y-6',
      config.bgColor,
      config.borderColor,
      'border',
      'animate-fade-in-elegant'
    )}>
      <div className="flex justify-center">
        <div className={cn(
          'w-16 h-16 rounded-2xl aone-flex-center',
          'bg-gradient-to-br from-white/80 to-white/40',
          'dark:from-aone-warm-white/60 dark:to-aone-warm-white/30',
          'shadow-lg backdrop-blur-sm',
          'animate-float-gentle'
        )}>
          <IconComponent className={cn('w-8 h-8', config.primaryColor)} />
        </div>
      </div>

      <div className="space-y-2">
        <h3 className={cn('aone-heading-enterprise text-aone-xl', config.primaryColor)}>
          {message}
        </h3>
        {submessage && (
          <p className="aone-body-enterprise text-aone-sm">
            {submessage}
          </p>
        )}
      </div>

      {progress !== undefined && (
        <div className="space-y-2">
          <div className="w-full bg-white/50 dark:bg-aone-warm-white/30 rounded-full h-2 overflow-hidden">
            <div
              className={cn(
                'h-2 rounded-full transition-all duration-500 ease-out',
                'bg-gradient-to-r from-aone-sage to-green-600'
              )}
              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
            />
          </div>
          <p className="text-xs text-aone-charcoal/60 dark:text-aone-charcoal/70">
            {Math.round(progress)}% complete
          </p>
        </div>
      )}

      <LoadingSpinner variant="enterprise" size="sm" />
    </div>
  );
};
