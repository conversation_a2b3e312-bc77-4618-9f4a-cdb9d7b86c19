import React from 'react';
import { Al<PERSON><PERSON>riangle, Refresh<PERSON><PERSON>, Home, FileX, Wifi, Brain } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface ErrorStateProps {
  title?: string;
  message?: string;
  type?: 'general' | 'network' | 'upload' | 'analysis' | 'validation';
  onRetry?: () => void;
  onGoHome?: () => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const ErrorState: React.FC<ErrorStateProps> = ({
  title,
  message,
  type = 'general',
  onRetry,
  onGoHome,
  className,
  size = 'md'
}) => {
  const getErrorConfig = () => {
    switch (type) {
      case 'network':
        return {
          icon: Wifi,
          defaultTitle: 'Connection Error',
          defaultMessage: 'Unable to connect to the server. Please check your internet connection and try again.',
          iconColor: 'text-orange-500'
        };
      case 'upload':
        return {
          icon: FileX,
          defaultTitle: 'Upload Failed',
          defaultMessage: 'There was an issue uploading your file. Please ensure it\'s a valid PDF and try again.',
          iconColor: 'text-red-500'
        };
      case 'analysis':
        return {
          icon: Brain,
          defaultTitle: 'Analysis Error',
          defaultMessage: 'Unable to analyze the kitchen design. The AI service may be temporarily unavailable.',
          iconColor: 'text-purple-500'
        };
      case 'validation':
        return {
          icon: AlertTriangle,
          defaultTitle: 'Validation Error',
          defaultMessage: 'Please check your input and try again.',
          iconColor: 'text-yellow-500'
        };
      default:
        return {
          icon: AlertTriangle,
          defaultTitle: 'Something went wrong',
          defaultMessage: 'An unexpected error occurred. Please try again or contact support if the problem persists.',
          iconColor: 'text-red-500'
        };
    }
  };

  const config = getErrorConfig();
  const IconComponent = config.icon;

  const sizeClasses = {
    sm: 'py-4',
    md: 'py-8',
    lg: 'py-12'
  };

  const iconSizes = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  };

  return (
    <div className={cn(
      'aone-flex-col-center text-center space-y-6',
      sizeClasses[size],
      className
    )}>
      <div className={cn(
        'p-aone-lg rounded-2xl aone-glass border border-border/50 shadow-lg',
        'animate-fade-in-elegant'
      )}>
        <IconComponent className={cn(iconSizes[size], config.iconColor)} />
      </div>

      <div className="space-y-4 max-w-md">
        <h3 className="aone-heading-enterprise text-aone-xl">
          {title || config.defaultTitle}
        </h3>
        <p className="aone-body-enterprise">
          {message || config.defaultMessage}
        </p>
      </div>

      <div className="flex flex-col sm:flex-row gap-aone-md">
        {onRetry && (
          <Button
            onClick={onRetry}
            variant="enterprise"
            className="aone-micro-interaction"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        )}
        {onGoHome && (
          <Button
            onClick={onGoHome}
            variant="enterpriseOutline"
            className="aone-micro-interaction"
          >
            <Home className="w-4 h-4 mr-2" />
            Go Home
          </Button>
        )}
      </div>
    </div>
  );
};

interface FormErrorProps {
  message: string;
  className?: string;
}

export const FormError: React.FC<FormErrorProps> = ({ message, className }) => {
  return (
    <div className={cn('aone-error-enterprise aone-spacing-md rounded-aone-xl animate-slide-in-elegant', className)}>
      <div className="flex items-start space-x-3">
        <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
        <p className="aone-body-enterprise text-aone-sm">{message}</p>
      </div>
    </div>
  );
};

interface FormSuccessProps {
  message: string;
  className?: string;
}

export const FormSuccess: React.FC<FormSuccessProps> = ({ message, className }) => {
  return (
    <div className={cn('aone-success-enterprise aone-spacing-md rounded-aone-xl animate-slide-in-elegant', className)}>
      <div className="flex items-start space-x-3">
        <div className="w-5 h-5 bg-green-500 rounded-full aone-flex-center mt-0.5 flex-shrink-0 shadow-sm">
          <div className="w-2 h-2 bg-white rounded-full" />
        </div>
        <p className="aone-body-enterprise text-aone-sm">{message}</p>
      </div>
    </div>
  );
};

interface InlineErrorProps {
  message: string;
  className?: string;
}

export const InlineError: React.FC<InlineErrorProps> = ({ message, className }) => {
  return (
    <p className={cn('aone-error-text text-xs mt-1 aone-fade-in', className)}>
      {message}
    </p>
  );
};

interface NetworkErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

interface NetworkErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

export class NetworkErrorBoundary extends React.Component<
  NetworkErrorBoundaryProps,
  NetworkErrorBoundaryState
> {
  constructor(props: NetworkErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): NetworkErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Network Error Boundary caught an error:', error, errorInfo);
  }

  retry = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error!} retry={this.retry} />;
      }

      return (
        <ErrorState
          type="network"
          onRetry={this.retry}
          size="lg"
          className="min-h-[400px]"
        />
      );
    }

    return this.props.children;
  }
}

// Validation helpers
export const validateRequired = (value: string, fieldName: string): string | null => {
  if (!value || value.trim() === '') {
    return `${fieldName} is required`;
  }
  return null;
};

export const validateEmail = (email: string): string | null => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return 'Please enter a valid email address';
  }
  return null;
};

export const validateFileType = (file: File, allowedTypes: string[]): string | null => {
  if (!allowedTypes.includes(file.type)) {
    return `File type not supported. Please upload: ${allowedTypes.join(', ')}`;
  }
  return null;
};

export const validateFileSize = (file: File, maxSizeMB: number): string | null => {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  if (file.size > maxSizeBytes) {
    return `File size must be less than ${maxSizeMB}MB`;
  }
  return null;
};
