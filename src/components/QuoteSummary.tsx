import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Calculator, 
  Ruler, 
  Clock, 
  BarChart3,
  Package
} from 'lucide-react';

interface QuoteSummaryProps {
  summary: {
    cabinetCount: number;
    linearFeet: number;
    complexity: string;
    estimatedTimeframe: string;
  };
}

export const QuoteSummary: React.FC<QuoteSummaryProps> = ({ summary }) => {
  const getComplexityColor = (complexity: string) => {
    switch (complexity.toLowerCase()) {
      case 'simple':
        return 'bg-green-100 text-green-800';
      case 'moderate':
        return 'bg-yellow-100 text-yellow-800';
      case 'complex':
        return 'bg-orange-100 text-orange-800';
      case 'very_complex':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-muted text-foreground';
    }
  };

  const formatComplexity = (complexity: string) => {
    return complexity.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <Card className="w-full mb-aone-lg">
      <CardHeader>
        <CardTitle className="flex items-center">
          <BarChart3 className="h-5 w-5 mr-2" />
          Project Summary
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-aone-md">
          {/* Cabinet Count */}
          <div className="flex items-center space-x-3 aone-spacing-sm bg-blue-50 rounded-aone-lg">
            <div className="aone-spacing-xs bg-blue-100 rounded-full">
              <Package className="h-4 w-4 text-aone-sage" />
            </div>
            <div>
              <p className="text-aone-sm font-aone-medium text-blue-900">Total Cabinets</p>
              <p className="text-aone-lg font-bold text-aone-sage-dark" data-testid="cabinet-count">
                {summary.cabinetCount}
              </p>
            </div>
          </div>

          {/* Linear Feet */}
          <div className="flex items-center space-x-3 aone-spacing-sm bg-green-50 rounded-aone-lg">
            <div className="aone-spacing-xs bg-green-100 rounded-full">
              <Ruler className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <p className="text-aone-sm font-aone-medium text-green-900">Linear Feet</p>
              <p className="text-aone-lg font-bold text-green-700" data-testid="linear-feet">
                {summary.linearFeet}
              </p>
            </div>
          </div>

          {/* Complexity */}
          <div className="flex items-center space-x-3 aone-spacing-sm bg-purple-50 rounded-aone-lg">
            <div className="aone-spacing-xs bg-purple-100 rounded-full">
              <Calculator className="h-4 w-4 text-purple-600" />
            </div>
            <div>
              <p className="text-aone-sm font-aone-medium text-purple-900">Complexity</p>
              <Badge 
                className={getComplexityColor(summary.complexity)}
                data-testid="complexity-badge"
              >
                {formatComplexity(summary.complexity)}
              </Badge>
            </div>
          </div>

          {/* Timeframe */}
          <div className="flex items-center space-x-3 aone-spacing-sm bg-orange-50 rounded-aone-lg">
            <div className="aone-spacing-xs bg-orange-100 rounded-full">
              <Clock className="h-4 w-4 text-orange-600" />
            </div>
            <div>
              <p className="text-aone-sm font-aone-medium text-orange-900">Timeframe</p>
              <p className="text-aone-lg font-bold text-orange-700" data-testid="timeframe">
                {summary.estimatedTimeframe}
              </p>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="mt-4 aone-spacing-sm bg-muted rounded-aone-lg">
          <p className="text-aone-sm text-muted-foreground">
            <strong>Note:</strong> Timeframe estimates include material procurement, manufacturing, 
            and installation. Actual timelines may vary based on material availability and site conditions.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
