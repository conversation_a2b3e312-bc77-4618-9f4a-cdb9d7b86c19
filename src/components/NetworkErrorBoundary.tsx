import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON>riangle, Refresh<PERSON>w, Wifi, ExternalLink } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  isRetrying: boolean;
}

export class NetworkErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      isRetrying: false
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      isRetrying: false
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Network Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Log to external service if available
    this.logErrorToService(error, errorInfo);
  }

  private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    // In a real application, you would send this to your error tracking service
    console.error('Error logged:', {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    });
  };

  private isNetworkError = (error: Error): boolean => {
    const networkErrorPatterns = [
      'NetworkError',
      'fetch',
      'CORS',
      'ERR_NETWORK',
      'ERR_INTERNET_DISCONNECTED',
      'ERR_CONNECTION_REFUSED',
      'Failed to fetch'
    ];

    return networkErrorPatterns.some(pattern => 
      error.message.toLowerCase().includes(pattern.toLowerCase())
    );
  };

  private handleRetry = async () => {
    if (this.retryCount >= this.maxRetries) {
      return;
    }

    this.setState({ isRetrying: true });
    this.retryCount++;

    try {
      // Test network connectivity
      await fetch('/api/health', {
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      });

      // If successful, reset the error boundary
      this.setState({
        hasError: false,
        error: undefined,
        errorInfo: undefined,
        isRetrying: false
      });
      this.retryCount = 0;

    } catch (error) {
      console.warn(`Retry attempt ${this.retryCount} failed:`, error);
      this.setState({ isRetrying: false });
    }
  };

  private handleRefresh = () => {
    window.location.reload();
  };

  private renderNetworkErrorUI = () => {
    const { error } = this.state;
    const isNetworkIssue = error && this.isNetworkError(error);

    return (
      <div className="min-h-screen aone-flex-center bg-muted px-aone-md">
        <div className="max-w-md w-full bg-white rounded-aone-lg shadow-lg p-aone-lg">
          <div className="aone-flex-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-aone-md">
            {isNetworkIssue ? (
              <Wifi className="w-6 h-6 text-red-600" />
            ) : (
              <AlertTriangle className="w-6 h-6 text-red-600" />
            )}
          </div>

          <h1 className="text-aone-xl font-aone-semibold text-foreground text-center mb-2">
            {isNetworkIssue ? 'Connection Problem' : 'Something went wrong'}
          </h1>

          <p className="text-muted-foreground text-center mb-aone-lg">
            {isNetworkIssue ? (
              <>
                Unable to connect to the Cabinet Insight Pro server. 
                Please check your internet connection and try again.
              </>
            ) : (
              'An unexpected error occurred. Please try refreshing the page.'
            )}
          </p>

          {error && (
            <div className="bg-muted rounded-aone-md aone-spacing-sm mb-aone-md">
              <p className="text-aone-sm text-foreground font-aone-medium mb-1">Error Details:</p>
              <p className="text-xs text-muted-foreground break-words">{error.message}</p>
            </div>
          )}

          <div className="space-y-3">
            {isNetworkIssue && this.retryCount < this.maxRetries && (
              <button
                onClick={this.handleRetry}
                disabled={this.state.isRetrying}
                className="w-full aone-flex-center px-aone-md py-2 bg-blue-600 text-white rounded-aone-md aone-micro-interaction hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {this.state.isRetrying ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Retrying...
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Try Again ({this.maxRetries - this.retryCount} attempts left)
                  </>
                )}
              </button>
            )}

            <button
              onClick={this.handleRefresh}
              className="w-full aone-flex-center px-aone-md py-2 bg-gray-600 text-white rounded-aone-md aone-micro-interaction hover:bg-gray-700 transition-colors"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh Page
            </button>

            <div className="text-center">
              <a
                href="/debug"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-aone-sm text-aone-sage aone-micro-interaction hover:text-blue-800"
              >
                <ExternalLink className="w-4 h-4 mr-1" />
                Open Network Diagnostics
              </a>
            </div>
          </div>

          {isNetworkIssue && (
            <div className="mt-6 aone-spacing-sm bg-blue-50 rounded-aone-md">
              <h3 className="text-aone-sm font-aone-medium text-blue-800 mb-2">Troubleshooting Tips:</h3>
              <ul className="text-xs text-aone-sage-dark space-y-1">
                <li>• Check your internet connection</li>
                <li>• Ensure the backend server is running on port 3001</li>
                <li>• Try disabling browser extensions</li>
                <li>• Clear browser cache and cookies</li>
                <li>• Contact support if the problem persists</li>
              </ul>
            </div>
          )}
        </div>
      </div>
    );
  };

  render() {
    if (this.state.hasError) {
      return this.props.fallback || this.renderNetworkErrorUI();
    }

    return this.props.children;
  }
}

export default NetworkErrorBoundary;
