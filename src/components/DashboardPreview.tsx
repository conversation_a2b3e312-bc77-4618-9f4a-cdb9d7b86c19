import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { 
  Calculator, 
  Ruler, 
  Package, 
  DollarSign, 
  Download, 
  Share2,
  TrendingUp,
  CheckCircle
} from "lucide-react";

const DashboardPreview = () => {
  return (
    <div className="py-16 bg-aone-warm-white dark:bg-aone-warm-white">
      <div className="aone-container">
        <div className="text-center mb-12">
          <h2 className="aone-heading-enterprise text-aone-3xl font-aone-bold mb-aone-md">
            Comprehensive Analysis Dashboard
          </h2>
          <p className="aone-body-enterprise text-aone-lg max-w-2xl mx-auto">
            Get instant insights with detailed measurements, cost breakdowns, and professional reporting tools.
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          {/* Analysis Summary Cards */}
          <div className="grid md:grid-cols-4 gap-aone-lg mb-8">
            <Card className="aone-card-enterprise border-l-4 border-l-aone-sage">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-aone-sm font-aone-medium aone-subheading-enterprise">
                  <Package className="w-4 h-4 mr-2 text-aone-sage" />
                  Total Cabinets
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-aone-2xl font-aone-bold aone-heading-enterprise">24</div>
                <p className="text-aone-sm aone-body-enterprise">Units detected</p>
              </CardContent>
            </Card>

            <Card className="aone-card-enterprise border-l-4 border-l-aone-sage/70">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-aone-sm font-aone-medium aone-subheading-enterprise">
                  <Ruler className="w-4 h-4 mr-2 text-aone-sage/70" />
                  Linear Metres
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-aone-2xl font-aone-bold aone-heading-enterprise">12.9</div>
                <p className="text-aone-sm aone-body-enterprise">Total measurement</p>
              </CardContent>
            </Card>

            <Card className="aone-card-enterprise border-l-4 border-l-aone-sage/50">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-aone-sm font-aone-medium aone-subheading-enterprise">
                  <Calculator className="w-4 h-4 mr-2 text-aone-sage/50" />
                  Hardware Items
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-aone-2xl font-aone-bold aone-heading-enterprise">186</div>
                <p className="text-aone-sm aone-body-enterprise">Components identified</p>
              </CardContent>
            </Card>

            <Card className="aone-card-enterprise border-l-4 border-l-aone-sage/80">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-aone-sm font-aone-medium aone-subheading-enterprise">
                  <DollarSign className="w-4 h-4 mr-2 text-aone-sage/80" />
                  Estimated Cost
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-aone-2xl font-aone-bold aone-heading-enterprise">$18,750</div>
                <p className="text-aone-sm aone-body-enterprise">NZD (ex. installation)</p>
              </CardContent>
            </Card>
          </div>

          {/* Main Analysis Results */}
          <div className="grid lg:grid-cols-3 gap-aone-lg">
            {/* Cabinet Analysis */}
            <Card className="aone-card-enterprise lg:col-span-2">
              <CardHeader>
                <div className="aone-flex-between">
                  <CardTitle className="aone-heading-enterprise">Cabinet Analysis Results</CardTitle>
                  <Badge variant="secondary" className="bg-aone-sage/10 text-aone-sage border-aone-sage/20">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    98% Confidence
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="aone-flex-between aone-spacing-sm bg-aone-cream/20 rounded-aone-lg aone-micro-interaction">
                    <div>
                      <div className="font-aone-semibold aone-subheading-enterprise">Base Cabinets</div>
                      <div className="text-aone-sm aone-body-enterprise">18 units • 760mm height</div>
                    </div>
                    <div className="text-right">
                      <div className="font-aone-semibold aone-heading-enterprise">$12,450</div>
                      <div className="text-aone-sm aone-body-enterprise">8.7m linear</div>
                    </div>
                  </div>

                  <div className="aone-flex-between aone-spacing-sm bg-aone-cream/20 rounded-aone-lg aone-micro-interaction">
                    <div>
                      <div className="font-aone-semibold aone-subheading-enterprise">Wall Cabinets</div>
                      <div className="text-aone-sm aone-body-enterprise">6 units • 760mm height</div>
                    </div>
                    <div className="text-right">
                      <div className="font-aone-semibold aone-heading-enterprise">$4,200</div>
                      <div className="text-aone-sm aone-body-enterprise">4.2m linear</div>
                    </div>
                  </div>

                  <div className="aone-flex-between aone-spacing-sm bg-aone-cream/20 rounded-aone-lg aone-micro-interaction">
                    <div>
                      <div className="font-aone-semibold aone-subheading-enterprise">Hardware & Accessories</div>
                      <div className="text-aone-sm aone-body-enterprise">Hinges, handles, slides</div>
                    </div>
                    <div className="text-right">
                      <div className="font-aone-semibold aone-heading-enterprise">$2,100</div>
                      <div className="text-aone-sm aone-body-enterprise">186 components</div>
                    </div>
                  </div>
                </div>

                <div className="mt-6 pt-4 border-t border-aone-sage/20">
                  <div className="flex gap-3">
                    <Button className="aone-button-primary-enhanced flex-1">
                      <Download className="w-4 h-4 mr-2" />
                      Download Report
                    </Button>
                    <Button variant="outline" className="aone-button-secondary-enhanced flex-1">
                      <Share2 className="w-4 h-4 mr-2" />
                      Share Quote
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Analysis Progress */}
            <Card className="aone-card-enterprise">
              <CardHeader>
                <CardTitle className="aone-heading-enterprise">Analysis Progress</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <div className="flex justify-between text-aone-sm mb-2">
                    <span className="aone-body-enterprise">Drawing Processing</span>
                    <span className="text-aone-sage font-aone-medium">Complete</span>
                  </div>
                  <Progress value={100} className="h-2" />
                </div>

                <div>
                  <div className="flex justify-between text-aone-sm mb-2">
                    <span className="aone-body-enterprise">Cabinet Detection</span>
                    <span className="text-aone-sage font-aone-medium">Complete</span>
                  </div>
                  <Progress value={100} className="h-2" />
                </div>

                <div>
                  <div className="flex justify-between text-aone-sm mb-2">
                    <span className="aone-body-enterprise">Measurement Extraction</span>
                    <span className="text-aone-sage font-aone-medium">Complete</span>
                  </div>
                  <Progress value={100} className="h-2" />
                </div>

                <div>
                  <div className="flex justify-between text-aone-sm mb-2">
                    <span className="aone-body-enterprise">Cost Calculation</span>
                    <span className="text-aone-sage font-aone-medium">Complete</span>
                  </div>
                  <Progress value={100} className="h-2" />
                </div>

                <div className="pt-4 border-t border-aone-sage/20">
                  <div className="flex items-center text-aone-sm aone-body-enterprise mb-2">
                    <TrendingUp className="w-4 h-4 mr-2 text-aone-sage" />
                    Analysis Quality Score
                  </div>
                  <div className="text-aone-2xl font-aone-bold text-aone-sage">98%</div>
                  <p className="text-aone-sm aone-body-enterprise">Excellent confidence level</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPreview;
