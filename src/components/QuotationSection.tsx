import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Download, Calculator, TrendingUp, AlertCircle, CheckCircle, Wifi, WifiOff } from 'lucide-react';
import { quotationService, Quote, QuoteTier } from '@/services/quotationService';
import { QuoteTierCard } from './QuoteTierCard';
import { QuoteAlternatives } from './QuoteAlternatives';
import { QuoteSummary } from './QuoteSummary';
import { QuoteComparisonTool } from './QuoteComparisonTool';
import { QuoteTemplateManager } from './QuoteTemplateManager';
import { io, Socket } from 'socket.io-client';

interface QuotationSectionProps {
  analysisData: any;
  analysisId: string;
  projectId?: string;
  onQuoteGenerated?: (quote: Quote) => void;
}

export const QuotationSection: React.FC<QuotationSectionProps> = ({
  analysisData,
  analysisId,
  projectId,
  onQuoteGenerated
}) => {
  const [quote, setQuote] = useState<Quote | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isServiceAvailable, setIsServiceAvailable] = useState<boolean | null>(null);
  const [selectedTier, setSelectedTier] = useState<'basic' | 'premium' | 'luxury'>('premium');
  const [generationProgress, setGenerationProgress] = useState<string>('');
  const [isWebSocketConnected, setIsWebSocketConnected] = useState(false);
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    checkServiceAvailability();
    setupWebSocketConnection();

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, []);

  const setupWebSocketConnection = () => {
    try {
      const socket = io('http://localhost:3001', {
        transports: ['websocket'],
        timeout: 5000
      });

      socketRef.current = socket;

      socket.on('connect', () => {
        setIsWebSocketConnected(true);
        console.log('QuotationSection WebSocket connected');
      });

      socket.on('disconnect', () => {
        setIsWebSocketConnected(false);
        console.log('QuotationSection WebSocket disconnected');
      });

      // Listen for quote generation events
      socket.on('quote:generated', (data: { quoteId: string; analysisId: string; summary: any; tiers: any[] }) => {
        if (data.analysisId === analysisId) {
          setGenerationProgress('Quote generated successfully!');
          console.log('Real-time quote generation update:', data);
        }
      });

      socket.on('quote:updated', (data: { quoteId: string; customizations?: any; notes?: string }) => {
        if (quote && data.quoteId === quote.id) {
          console.log('Real-time quote update:', data);
        }
      });

      socket.on('quote:pdf_generated', (data: { quoteId: string; filename: string; template: string }) => {
        if (quote && data.quoteId === quote.id) {
          setGenerationProgress(`PDF generated: ${data.filename}`);
          console.log('Real-time PDF generation update:', data);
        }
      });

      // Join project room if projectId is available
      if (projectId) {
        socket.emit('join-project', projectId);
      }

    } catch (error) {
      console.error('Failed to setup WebSocket connection:', error);
      setIsWebSocketConnected(false);
    }
  };

  useEffect(() => {
    if (analysisData && isServiceAvailable) {
      const recommendedTier = quotationService.getTierRecommendation(analysisData);
      setSelectedTier(recommendedTier);
    }
  }, [analysisData, isServiceAvailable]);

  const checkServiceAvailability = async () => {
    try {
      const available = await quotationService.isAvailable();
      setIsServiceAvailable(available);
    } catch (error) {
      setIsServiceAvailable(false);
    }
  };

  const generateQuote = async () => {
    if (!analysisData || !isServiceAvailable) return;

    setIsGenerating(true);
    setError(null);
    setGenerationProgress('Initializing quote generation...');

    try {
      // Show progress updates
      setGenerationProgress('Analyzing cabinet data...');
      await new Promise(resolve => setTimeout(resolve, 500)); // Brief delay for UX

      setGenerationProgress('Calculating material costs...');
      await new Promise(resolve => setTimeout(resolve, 300));

      setGenerationProgress('Processing hardware pricing...');
      await new Promise(resolve => setTimeout(resolve, 300));

      setGenerationProgress('Generating multi-tier quotes...');

      const newQuote = await quotationService.generateQuote({
        analysisId,
        projectId,
        regionCode: 'NZ_NORTH', // Default to North Island
        analysisData
      });

      setGenerationProgress('Quote generated successfully!');
      setQuote(newQuote);
      onQuoteGenerated?.(newQuote);

      // Clear progress after a brief delay
      setTimeout(() => setGenerationProgress(''), 2000);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate quote');
      setGenerationProgress('');
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadPDF = async (template: 'basic' | 'detailed' | 'professional' = 'detailed') => {
    if (!quote) return;

    setIsDownloading(true);
    try {
      const blob = await quotationService.downloadQuotePDF(quote.id, {
        template,
        includeBranding: true,
        includeAlternatives: true
      });

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `quote_${quote.id}_${template}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to download PDF');
    } finally {
      setIsDownloading(false);
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'text-green-600';
    if (confidence >= 0.8) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getConfidenceBadgeVariant = (confidence: number) => {
    if (confidence >= 0.9) return 'default';
    if (confidence >= 0.8) return 'secondary';
    return 'destructive';
  };

  if (isServiceAvailable === null) {
    return (
      <Card data-testid="quotation-section">
        <CardContent className="aone-flex-center p-aone-lg">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Checking quotation service availability...</span>
        </CardContent>
      </Card>
    );
  }

  if (!isServiceAvailable) {
    return (
      <Card data-testid="quotation-section">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calculator className="h-5 w-5 mr-2" />
            Quotation System
          </CardTitle>
          <CardDescription>
            Generate detailed cost estimates for your kitchen cabinet project
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Quotation service is temporarily unavailable. The pricing database may be offline.
              Please try again later or contact support for assistance.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card data-testid="quotation-section" className="w-full">
      <CardHeader>
        <CardTitle className="aone-flex-between">
          <div className="flex items-center">
            <Calculator className="h-5 w-5 mr-2" />
            Quotation System
            <div className="ml-3 flex items-center">
              {isWebSocketConnected ? (
                <Wifi className="h-4 w-4 text-green-500" title="Real-time updates connected" />
              ) : (
                <WifiOff className="h-4 w-4 text-gray-400" title="Real-time updates disconnected" />
              )}
            </div>
          </div>
          {quote && (
            <Badge
              variant={getConfidenceBadgeVariant(quote.confidence)}
              className={getConfidenceColor(quote.confidence)}
              data-testid="overall-confidence"
            >
              {(quote.confidence * 100).toFixed(1)}% Confidence
            </Badge>
          )}
        </CardTitle>
        <CardDescription>
          Generate detailed cost estimates for your kitchen cabinet project in NZD
          {isWebSocketConnected && (
            <span className="ml-2 text-green-600 text-xs">• Real-time updates enabled</span>
          )}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {error && (
          <Alert variant="destructive" data-testid="error-message">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {!quote ? (
          <div className="text-center space-y-4">
            <p className="text-muted-foreground">
              Ready to generate a comprehensive quote based on your kitchen analysis
            </p>

            {/* Progress indicator */}
            {isGenerating && generationProgress && (
              <div className="bg-blue-50 border border-blue-200 rounded-aone-lg aone-spacing-sm mb-aone-md">
                <div className="aone-flex-center text-aone-sage-dark">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  <span className="text-aone-sm font-aone-medium">{generationProgress}</span>
                </div>
              </div>
            )}

            <Button
              onClick={generateQuote}
              disabled={isGenerating || !analysisData}
              size="lg"
              data-testid="generate-quote-btn"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Generating Quote...
                </>
              ) : (
                <>
                  <Calculator className="h-4 w-4 mr-2" />
                  Generate Quote
                </>
              )}
            </Button>
          </div>
        ) : (
          <div data-testid="quote-results" data-quote-id={quote.id}>
            {/* Quote Summary */}
            <QuoteSummary summary={quote.summary} />

            {/* Quote Tiers */}
            <Tabs value={selectedTier} onValueChange={(value) => setSelectedTier(value as any)} className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic" data-testid="tier-tab-basic">
                  Essential
                  <Badge variant="outline" className="ml-2">
                    {quote.tiers.find(t => t.tier === 'basic')?.total}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger value="premium" data-testid="tier-tab-premium">
                  Professional
                  <Badge variant="outline" className="ml-2">
                    {quote.tiers.find(t => t.tier === 'premium')?.total}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger value="luxury" data-testid="tier-tab-luxury">
                  Designer
                  <Badge variant="outline" className="ml-2">
                    {quote.tiers.find(t => t.tier === 'luxury')?.total}
                  </Badge>
                </TabsTrigger>
              </TabsList>

              {quote.tiers.map((tier) => (
                <TabsContent key={tier.tier} value={tier.tier}>
                  <QuoteTierCard tier={tier} />
                </TabsContent>
              ))}
            </Tabs>

            {/* Quote Comparison Tool */}
            <div className="mt-6">
              <QuoteComparisonTool quote={quote} onExport={(format) => console.log(`Exported as ${format}`)} />
            </div>

            {/* Quote Alternatives */}
            {quote.alternatives.length > 0 && (
              <QuoteAlternatives alternatives={quote.alternatives} />
            )}

            {/* Actions */}
            <div className="flex flex-wrap gap-3 pt-4 border-t">
              <Button
                onClick={() => downloadPDF('detailed')}
                disabled={isDownloading}
                data-testid="download-quote-pdf"
              >
                {isDownloading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                Download PDF
              </Button>

              <Button
                variant="outline"
                onClick={() => downloadPDF('professional')}
                disabled={isDownloading}
              >
                Professional Report
              </Button>

              <Button
                variant="outline"
                onClick={() => downloadPDF('basic')}
                disabled={isDownloading}
              >
                Basic Quote
              </Button>

              <Button
                variant="ghost"
                onClick={generateQuote}
                disabled={isGenerating}
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                Regenerate
              </Button>
            </div>

            {/* Quote Validity */}
            <div className="text-aone-sm text-muted-foreground pt-2 border-t">
              <div className="aone-flex-between">
                <span>Quote ID: {quote.id}</span>
                <span>Valid until: {new Date(quote.validUntil).toLocaleDateString('en-NZ')}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
