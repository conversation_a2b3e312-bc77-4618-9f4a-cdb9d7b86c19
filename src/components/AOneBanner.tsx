import { X } from "lucide-react";
import { useState } from "react";

interface AOneBannerProps {
  message?: string;
  dismissible?: boolean;
  className?: string;
}

const AOneBanner = ({
  message = "Revolutionize your kitchen design workflow with Blackveil Design Mind's AI-powered insights. Contact our design team today!",
  dismissible = true,
  className = ""
}: AOneBannerProps) => {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  return (
    <div className={`aone-banner relative ${className} shadow-sm`}>
      <div className="aone-container lg:px-8 aone-flex-center">
        <p className="text-center flex-1 font-aone-medium">
          {message}
        </p>
        {dismissible && (
          <button
            type="button"
            onClick={() => setIsVisible(false)}
            className="ml-4 aone-spacing-xs hover:bg-white/20 rounded-full aone-micro-interaction hover:scale-110"
            aria-label="Dismiss banner"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  );
};

export default AOneBanner;
