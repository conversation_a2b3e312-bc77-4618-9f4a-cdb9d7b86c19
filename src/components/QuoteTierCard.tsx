import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CheckCircle, Package, Wrench, Users } from 'lucide-react';
import { QuoteTier } from '@/services/quotationService';

interface QuoteTierCardProps {
  tier: QuoteTier;
}

export const QuoteTierCard: React.FC<QuoteTierCardProps> = ({ tier }) => {
  const getTierIcon = () => {
    switch (tier.tier) {
      case 'basic':
        return <Package className="h-5 w-5" />;
      case 'premium':
        return <Wrench className="h-5 w-5" />;
      case 'luxury':
        return <CheckCircle className="h-5 w-5" />;
      default:
        return <Package className="h-5 w-5" />;
    }
  };

  const getTierColor = () => {
    switch (tier.tier) {
      case 'basic':
        return 'border-blue-200 bg-blue-50';
      case 'premium':
        return 'border-purple-200 bg-purple-50';
      case 'luxury':
        return 'border-gold-200 bg-amber-50';
      default:
        return 'border-border bg-muted';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'text-green-600';
    if (confidence >= 0.8) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <Card className={`w-full ${getTierColor()}`} data-testid={`quote-tier-${tier.tier}`}>
      <CardHeader>
        <CardTitle className="aone-flex-between">
          <div className="flex items-center">
            {getTierIcon()}
            <span className="ml-2">{tier.name}</span>
          </div>
          <Badge 
            variant="secondary" 
            className={getConfidenceColor(tier.confidence)}
            data-testid={`tier-confidence-${tier.tier}`}
          >
            {(tier.confidence * 100).toFixed(1)}% Confidence
          </Badge>
        </CardTitle>
        <CardDescription>{tier.description}</CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Materials Section */}
        <div>
          <h4 className="font-aone-semibold flex items-center mb-3">
            <Package className="h-4 w-4 mr-2" />
            Materials
            <Badge variant="outline" className="ml-auto" data-testid={`materials-cost-${tier.tier}`}>
              {tier.materials.cost}
            </Badge>
          </h4>
          <div className="space-y-2">
            {tier.materials.items.map((item, index) => (
              <div key={index} className="flex justify-between items-center text-aone-sm">
                <div className="flex-1">
                  <span className="font-aone-medium">{item.description}</span>
                  <span className="text-muted-foreground ml-2">
                    {item.quantity} × {item.unitPrice}
                  </span>
                </div>
                <span className="font-aone-medium" data-testid={`material-item-price-${index}`}>
                  {item.totalPrice}
                </span>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Hardware Section */}
        <div>
          <h4 className="font-aone-semibold flex items-center mb-3">
            <Wrench className="h-4 w-4 mr-2" />
            Hardware
            <Badge variant="outline" className="ml-auto" data-testid={`hardware-cost-${tier.tier}`}>
              {tier.hardware.cost}
            </Badge>
          </h4>
          <div className="space-y-2">
            {tier.hardware.items.map((item, index) => (
              <div key={index} className="flex justify-between items-center text-aone-sm">
                <div className="flex-1">
                  <span className="font-aone-medium">{item.description}</span>
                  <span className="text-muted-foreground ml-2">
                    {item.quantity} × {item.unitPrice}
                  </span>
                </div>
                <span className="font-aone-medium" data-testid={`hardware-item-price-${index}`}>
                  {item.totalPrice}
                </span>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Labor Section */}
        <div>
          <h4 className="font-aone-semibold flex items-center mb-3">
            <Users className="h-4 w-4 mr-2" />
            Installation
            <Badge variant="outline" className="ml-auto" data-testid={`labor-cost-${tier.tier}`}>
              {tier.labor.cost}
            </Badge>
          </h4>
          <div className="space-y-2">
            {tier.labor.items.map((item, index) => (
              <div key={index} className="flex justify-between items-center text-aone-sm">
                <div className="flex-1">
                  <span className="font-aone-medium">{item.description}</span>
                  <span className="text-muted-foreground ml-2">
                    {item.hours}h × {item.hourlyRate}
                  </span>
                </div>
                <span className="font-aone-medium" data-testid={`labor-item-price-${index}`}>
                  {item.totalPrice}
                </span>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Totals Section */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="font-aone-medium">Subtotal</span>
            <span className="font-aone-medium" data-testid={`subtotal-${tier.tier}`}>
              {tier.subtotal}
            </span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="font-aone-medium">GST (15%)</span>
            <span className="font-aone-medium" data-testid={`taxes-${tier.tier}`}>
              {tier.taxes}
            </span>
          </div>
          
          <Separator />
          
          <div className="flex justify-between items-center text-aone-lg font-bold">
            <span>Total</span>
            <span className="text-primary" data-testid={`tier-total`}>
              {tier.total}
            </span>
          </div>
        </div>

        {/* Tier Benefits */}
        <div className="mt-6 aone-spacing-md bg-white/50 rounded-aone-lg">
          <h5 className="font-aone-semibold mb-2">What's Included:</h5>
          <ul className="text-aone-sm space-y-1">
            {tier.tier === 'basic' && (
              <>
                <li className="flex items-center">
                  <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                  Standard materials and finishes
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                  Basic hardware and fittings
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                  Professional installation
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                  12-month warranty
                </li>
              </>
            )}
            
            {tier.tier === 'premium' && (
              <>
                <li className="flex items-center">
                  <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                  Premium materials and finishes
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                  Soft-close hardware
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                  Expert installation
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                  18-month warranty
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                  Design consultation
                </li>
              </>
            )}
            
            {tier.tier === 'luxury' && (
              <>
                <li className="flex items-center">
                  <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                  Designer materials and finishes
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                  Premium soft-close hardware
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                  Master craftsman installation
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                  24-month warranty
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                  Full design service
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                  Custom modifications
                </li>
              </>
            )}
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};
