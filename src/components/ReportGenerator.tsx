/**
 * Report Generator Component
 * 
 * Priority 2 Enhanced Analysis Engine Feature 3
 * Frontend interface for generating comprehensive PDF reports
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import {
  FileText,
  Download,
  Settings,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2,
  Eye,
  Calendar,
  Trash2
} from 'lucide-react';
import { AnalysisResults } from '@/services/aiAnalysisService';

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  sections: Array<{
    id: string;
    title: string;
    type: string;
    required: boolean;
  }>;
}

interface ReportGeneratorProps {
  analysisResults: AnalysisResults;
  onReportGenerated?: (reportId: string) => void;
}

interface ReportGenerationStatus {
  status: 'idle' | 'generating' | 'completed' | 'error';
  progress: number;
  message: string;
  reportId?: string;
  downloadUrl?: string;
  error?: string;
}

export const ReportGenerator: React.FC<ReportGeneratorProps> = ({
  analysisResults,
  onReportGenerated
}) => {
  const [templates, setTemplates] = useState<ReportTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [customization, setCustomization] = useState({
    companyName: '',
    logo: '',
    brandColors: {
      primary: 'hsl(var(--status-info))',
      secondary: 'rgb(var(--aone-soft-gray))',
      accent: 'rgb(var(--preset-accent))'
    }
  });
  const [options, setOptions] = useState({
    includeRawData: false,
    includeCharts: true,
    include3DVisualization: true,
    quality: 'standard' as 'draft' | 'standard' | 'high'
  });
  const [generationStatus, setGenerationStatus] = useState<ReportGenerationStatus>({
    status: 'idle',
    progress: 0,
    message: ''
  });
  const [reportHistory, setReportHistory] = useState<any[]>([]);

  useEffect(() => {
    loadTemplates();
    loadReportHistory();
  }, []);

  const loadTemplates = async () => {
    try {
      const response = await fetch('/api/reports/templates');
      const data = await response.json();
      
      if (data.success) {
        setTemplates(data.data.templates);
        if (data.data.templates.length > 0) {
          setSelectedTemplate(data.data.templates[0].id);
        }
      }
    } catch (error) {
      console.error('Failed to load templates:', error);
    }
  };

  const loadReportHistory = async () => {
    try {
      const response = await fetch('/api/reports/history');
      const data = await response.json();
      
      if (data.success) {
        setReportHistory(data.data.reports);
      }
    } catch (error) {
      console.error('Failed to load report history:', error);
    }
  };

  const generateReport = async () => {
    if (!selectedTemplate) {
      return;
    }

    setGenerationStatus({
      status: 'generating',
      progress: 0,
      message: 'Initializing report generation...'
    });

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setGenerationStatus(prev => ({
          ...prev,
          progress: Math.min(prev.progress + 10, 90),
          message: prev.progress < 30 ? 'Analyzing data...' :
                   prev.progress < 60 ? 'Generating content...' :
                   prev.progress < 90 ? 'Creating PDF...' : 'Finalizing...'
        }));
      }, 500);

      const response = await fetch('/api/reports/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          analysisId: analysisResults.id,
          templateId: selectedTemplate,
          customization,
          options
        })
      });

      clearInterval(progressInterval);

      const data = await response.json();

      if (data.success) {
        setGenerationStatus({
          status: 'completed',
          progress: 100,
          message: 'Report generated successfully!',
          reportId: data.data.reportId,
          downloadUrl: data.data.downloadUrl
        });

        if (onReportGenerated) {
          onReportGenerated(data.data.reportId);
        }

        // Reload history
        loadReportHistory();
      } else {
        throw new Error(data.error || 'Report generation failed');
      }

    } catch (error) {
      setGenerationStatus({
        status: 'error',
        progress: 0,
        message: 'Report generation failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  const downloadReport = (downloadUrl: string, reportId: string) => {
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `cabinet-analysis-${reportId}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const deleteReport = async (reportId: string) => {
    try {
      const response = await fetch(`/api/reports/${reportId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        loadReportHistory();
      }
    } catch (error) {
      console.error('Failed to delete report:', error);
    }
  };

  const selectedTemplateData = templates.find(t => t.id === selectedTemplate);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Enhanced Report Generator
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="generate" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="generate">Generate Report</TabsTrigger>
              <TabsTrigger value="customize">Customize</TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
            </TabsList>

            <TabsContent value="generate" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="template">Report Template</Label>
                  <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a template" />
                    </SelectTrigger>
                    <SelectContent>
                      {templates.map(template => (
                        <SelectItem key={template.id} value={template.id}>
                          {template.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {selectedTemplateData && (
                    <p className="text-aone-sm text-aone-charcoal mt-1">
                      {selectedTemplateData.description}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Report Options</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeCharts"
                        checked={options.includeCharts}
                        onCheckedChange={(checked) => 
                          setOptions(prev => ({ ...prev, includeCharts: checked as boolean }))
                        }
                      />
                      <Label htmlFor="includeCharts">Include interactive charts</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include3D"
                        checked={options.include3DVisualization}
                        onCheckedChange={(checked) => 
                          setOptions(prev => ({ ...prev, include3DVisualization: checked as boolean }))
                        }
                      />
                      <Label htmlFor="include3D">Include 3D visualizations</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeRaw"
                        checked={options.includeRawData}
                        onCheckedChange={(checked) => 
                          setOptions(prev => ({ ...prev, includeRawData: checked as boolean }))
                        }
                      />
                      <Label htmlFor="includeRaw">Include raw analysis data</Label>
                    </div>
                  </div>
                </div>

                <div>
                  <Label htmlFor="quality">Report Quality</Label>
                  <Select 
                    value={options.quality} 
                    onValueChange={(value: 'draft' | 'standard' | 'high') => 
                      setOptions(prev => ({ ...prev, quality: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft (Fast)</SelectItem>
                      <SelectItem value="standard">Standard</SelectItem>
                      <SelectItem value="high">High Quality (Slow)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {generationStatus.status === 'generating' && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span className="text-aone-sm">{generationStatus.message}</span>
                    </div>
                    <Progress value={generationStatus.progress} className="w-full" />
                  </div>
                )}

                {generationStatus.status === 'completed' && (
                  <div className="bg-green-50 border border-green-200 rounded-aone-lg aone-spacing-md">
                    <div className="flex items-center gap-2 text-green-800">
                      <CheckCircle className="w-5 h-5" />
                      <span className="font-aone-medium">{generationStatus.message}</span>
                    </div>
                    <Button
                      onClick={() => downloadReport(generationStatus.downloadUrl!, generationStatus.reportId!)}
                      className="mt-2"
                      size="sm"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Download Report
                    </Button>
                  </div>
                )}

                {generationStatus.status === 'error' && (
                  <div className="bg-red-50 border border-red-200 rounded-aone-lg aone-spacing-md">
                    <div className="flex items-center gap-2 text-red-800">
                      <AlertCircle className="w-5 h-5" />
                      <span className="font-aone-medium">{generationStatus.message}</span>
                    </div>
                    {generationStatus.error && (
                      <p className="text-aone-sm text-status-error mt-1">{generationStatus.error}</p>
                    )}
                  </div>
                )}

                <Button
                  onClick={generateReport}
                  disabled={!selectedTemplate || generationStatus.status === 'generating'}
                  className="w-full"
                >
                  {generationStatus.status === 'generating' ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Generating Report...
                    </>
                  ) : (
                    <>
                      <FileText className="w-4 h-4 mr-2" />
                      Generate Report
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="customize" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="companyName">Company Name</Label>
                  <Input
                    id="companyName"
                    value={customization.companyName}
                    onChange={(e) => setCustomization(prev => ({ 
                      ...prev, 
                      companyName: e.target.value 
                    }))}
                    placeholder="Your Company Name"
                  />
                </div>

                <div>
                  <Label htmlFor="primaryColor">Primary Brand Color</Label>
                  <Input
                    id="primaryColor"
                    type="color"
                    value={customization.brandColors.primary}
                    onChange={(e) => setCustomization(prev => ({
                      ...prev,
                      brandColors: { ...prev.brandColors, primary: e.target.value }
                    }))}
                  />
                </div>

                <div>
                  <Label htmlFor="secondaryColor">Secondary Brand Color</Label>
                  <Input
                    id="secondaryColor"
                    type="color"
                    value={customization.brandColors.secondary}
                    onChange={(e) => setCustomization(prev => ({
                      ...prev,
                      brandColors: { ...prev.brandColors, secondary: e.target.value }
                    }))}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <div className="space-y-2">
                {reportHistory.length === 0 ? (
                  <p className="text-aone-soft-gray text-center py-8">No reports generated yet</p>
                ) : (
                  reportHistory.map(report => (
                    <Card key={report.reportId}>
                      <CardContent className="aone-spacing-md">
                        <div className="aone-flex-between">
                          <div>
                            <h4 className="font-aone-medium">Report {report.reportId}</h4>
                            <p className="text-aone-sm text-aone-charcoal">
                              Generated {new Date(report.metadata.generatedAt).toLocaleDateString()}
                            </p>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline">{report.pageCount} pages</Badge>
                              <Badge variant="outline">{Math.round(report.fileSize / 1024)} KB</Badge>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => downloadReport(`/api/reports/download/${report.reportId}`, report.reportId)}
                            >
                              <Download className="w-4 h-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => deleteReport(report.reportId)}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReportGenerator;
