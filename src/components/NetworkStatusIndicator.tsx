import React, { useState, useEffect } from 'react';
import { Alert<PERSON>ircle, CheckCircle, Wifi, WifiOff, RefreshCw } from 'lucide-react';

interface NetworkStatus {
  isOnline: boolean;
  backendReachable: boolean;
  lastChecked: Date;
  latency?: number;
  error?: string;
}

interface NetworkStatusIndicatorProps {
  className?: string;
  showDetails?: boolean;
}

export const NetworkStatusIndicator: React.FC<NetworkStatusIndicatorProps> = ({
  className = '',
  showDetails = false
}) => {
  const [status, setStatus] = useState<NetworkStatus>({
    isOnline: navigator.onLine,
    backendReachable: false,
    lastChecked: new Date()
  });
  const [isChecking, setIsChecking] = useState(false);

  const checkBackendConnectivity = async (): Promise<void> => {
    setIsChecking(true);
    const startTime = Date.now();
    
    try {
      const response = await fetch('/api/health', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });
      
      const latency = Date.now() - startTime;
      
      if (response.ok) {
        setStatus(prev => ({
          ...prev,
          backendReachable: true,
          lastChecked: new Date(),
          latency,
          error: undefined
        }));
      } else {
        throw new Error(`Backend returned ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.warn('Backend connectivity check failed:', error);
      setStatus(prev => ({
        ...prev,
        backendReachable: false,
        lastChecked: new Date(),
        latency: undefined,
        error: error instanceof Error ? error.message : 'Unknown error'
      }));
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    // Initial check
    checkBackendConnectivity();

    // Set up periodic checks
    const interval = setInterval(checkBackendConnectivity, 30000); // Check every 30 seconds

    // Listen for online/offline events
    const handleOnline = () => {
      setStatus(prev => ({ ...prev, isOnline: true }));
      checkBackendConnectivity();
    };

    const handleOffline = () => {
      setStatus(prev => ({ ...prev, isOnline: false, backendReachable: false }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      clearInterval(interval);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const getStatusIcon = () => {
    if (isChecking) {
      return <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />;
    }
    
    if (!status.isOnline) {
      return <WifiOff className="w-4 h-4 text-red-500" />;
    }
    
    if (status.backendReachable) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
    
    return <AlertCircle className="w-4 h-4 text-yellow-500" />;
  };

  const getStatusText = () => {
    if (!status.isOnline) {
      return 'Offline';
    }
    
    if (status.backendReachable) {
      return 'Connected';
    }
    
    return 'Backend Unreachable';
  };

  const getStatusColor = () => {
    if (!status.isOnline || !status.backendReachable) {
      return 'text-red-600';
    }
    return 'text-green-600';
  };

  const handleManualCheck = () => {
    if (!isChecking) {
      checkBackendConnectivity();
    }
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <button
        onClick={handleManualCheck}
        disabled={isChecking}
        className="flex items-center space-x-2 aone-spacing-xs rounded-aone-md aone-micro-interaction hover:bg-muted transition-colors"
        title="Click to check connection"
      >
        {getStatusIcon()}
        <span className={`text-aone-sm font-aone-medium ${getStatusColor()}`}>
          {getStatusText()}
        </span>
      </button>

      {showDetails && (
        <div className="text-xs text-gray-500">
          {status.latency && (
            <span className="mr-2">{status.latency}ms</span>
          )}
          <span>
            Last checked: {status.lastChecked.toLocaleTimeString()}
          </span>
        </div>
      )}

      {status.error && showDetails && (
        <div className="text-xs text-red-500 max-w-xs truncate" title={status.error}>
          Error: {status.error}
        </div>
      )}
    </div>
  );
};

// Hook for accessing network status in other components
export const useNetworkStatus = () => {
  const [status, setStatus] = useState<NetworkStatus>({
    isOnline: navigator.onLine,
    backendReachable: false,
    lastChecked: new Date()
  });

  useEffect(() => {
    const checkStatus = async () => {
      try {
        const response = await fetch('/api/health', {
          method: 'GET',
          signal: AbortSignal.timeout(3000)
        });
        
        setStatus(prev => ({
          ...prev,
          backendReachable: response.ok,
          lastChecked: new Date()
        }));
      } catch {
        setStatus(prev => ({
          ...prev,
          backendReachable: false,
          lastChecked: new Date()
        }));
      }
    };

    checkStatus();
    const interval = setInterval(checkStatus, 15000);

    const handleOnline = () => setStatus(prev => ({ ...prev, isOnline: true }));
    const handleOffline = () => setStatus(prev => ({ ...prev, isOnline: false, backendReachable: false }));

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      clearInterval(interval);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return status;
};

export default NetworkStatusIndicator;
