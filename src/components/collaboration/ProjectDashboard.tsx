import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useCollaboration } from '../../hooks/useCollaboration';
import UserPresence from './UserPresence';

export interface Project {
  id: string;
  name: string;
  description?: string;
  organizationId?: string;
  ownerId: string;
  ownerName: string;
  ownerEmail: string;
  visibility: 'public' | 'private' | 'organization';
  status: 'active' | 'archived' | 'deleted';
  tags?: string[];
  folderPath?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ProjectDashboardProps {
  onProjectSelect?: (project: Project) => void;
  onCreateProject?: () => void;
}

export const ProjectDashboard: React.FC<ProjectDashboardProps> = ({ 
  onProjectSelect, 
  onCreateProject 
}) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'archived'>('active');
  
  const { user } = useAuth();
  const { getProjects, createProject } = useCollaboration();

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    setIsLoading(true);
    try {
      const fetchedProjects = await getProjects();
      setProjects(fetchedProjects);
    } catch (error) {
      console.error('Failed to load projects:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || project.status === filterStatus;
    
    return matchesSearch && matchesStatus;
  });

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(new Date(date));
  };

  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'public': return '🌐';
      case 'private': return '🔒';
      case 'organization': return '🏢';
      default: return '📁';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'archived': return 'bg-muted text-foreground';
      default: return 'bg-muted text-foreground';
    }
  };

  if (isLoading) {
    return (
      <div className="aone-flex-center p-aone-xl">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="aone-flex-between">
        <div>
          <h1 className="text-aone-2xl font-aone-bold text-foreground">Projects</h1>
          <p className="text-muted-foreground">Manage your cabinet analysis projects</p>
        </div>
        
        <button
          onClick={onCreateProject}
          className="px-aone-md py-2 bg-blue-600 text-white rounded-aone-lg aone-micro-interaction hover:bg-blue-700 transition-colors"
        >
          New Project
        </button>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search projects..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 border border-border rounded-aone-lg aone-focus-ring aone-focus-ring"
          />
        </div>
        
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value as any)}
          className="px-3 py-2 border border-border rounded-aone-lg aone-focus-ring aone-focus-ring"
        >
          <option value="all">All Projects</option>
          <option value="active">Active</option>
          <option value="archived">Archived</option>
        </select>
      </div>

      {/* Projects Grid */}
      {filteredProjects.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-aone-md">📁</div>
          <h3 className="text-aone-lg font-aone-medium text-foreground mb-2">
            {searchTerm ? 'No projects found' : 'No projects yet'}
          </h3>
          <p className="text-muted-foreground mb-aone-md">
            {searchTerm 
              ? 'Try adjusting your search terms or filters'
              : 'Create your first project to get started with collaborative analysis'
            }
          </p>
          {!searchTerm && (
            <button
              onClick={onCreateProject}
              className="px-aone-md py-2 bg-blue-600 text-white rounded-aone-lg aone-micro-interaction hover:bg-blue-700 transition-colors"
            >
              Create Project
            </button>
          )}
        </div>
      ) : (
        <div className="aone-grid-responsive">
          {filteredProjects.map((project) => (
            <div
              key={project.id}
              className="bg-white rounded-aone-lg border border-border p-aone-lg hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => onProjectSelect?.(project)}
            >
              <div className="flex items-start justify-between mb-aone-md">
                <div className="flex items-center space-x-2">
                  <span className="text-2xl">{getVisibilityIcon(project.visibility)}</span>
                  <div>
                    <h3 className="font-aone-medium text-foreground truncate">{project.name}</h3>
                    <p className="text-aone-sm text-muted-foreground">by {project.ownerName}</p>
                  </div>
                </div>
                
                <span className={`px-2 py-1 rounded-full text-xs font-aone-medium ${getStatusColor(project.status)}`}>
                  {project.status}
                </span>
              </div>
              
              {project.description && (
                <p className="text-muted-foreground text-aone-sm mb-aone-md line-clamp-2">
                  {project.description}
                </p>
              )}
              
              {project.tags && project.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-aone-md">
                  {project.tags.slice(0, 3).map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                  {project.tags.length > 3 && (
                    <span className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-full">
                      +{project.tags.length - 3}
                    </span>
                  )}
                </div>
              )}
              
              <div className="aone-flex-between text-aone-sm text-gray-500">
                <span>Updated {formatDate(project.updatedAt)}</span>
                <UserPresence projectId={project.id} className="scale-75" />
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProjectDashboard;
