import React, { useState, useRef, useEffect } from 'react';
import { useCollaboration } from '../../hooks/useCollaboration';
import { useAuth } from '../../hooks/useAuth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Users, 
  MessageCircle, 
  History, 
  Settings, 
  Share2, 
  Eye, 
  Edit3, 
  GitBranch,
  Activity,
  Bell,
  Search,
  Filter,
  MoreHorizontal
} from 'lucide-react';

// Import collaboration components
import EnhancedUserPresence from './EnhancedUserPresence';
import LiveCursorTracker from './LiveCursorTracker';
import AdvancedAnnotationSystem, { Annotation } from './AdvancedAnnotationSystem';
import VersionControlSystem, { ProjectVersion } from './VersionControlSystem';
import { CommentThread } from './CommentThread';
import EnhancedCursorTracker from './EnhancedCursorTracker';
import VoiceCommentsSystem from './VoiceCommentsSystem';

interface CollaborationDashboardProps {
  projectId: string;
  analysisId?: string;
  mode: 'view' | 'edit' | 'comment' | 'annotate';
  onModeChange: (mode: 'view' | 'edit' | 'comment' | 'annotate') => void;
  className?: string;
}

export const CollaborationDashboard: React.FC<CollaborationDashboardProps> = ({
  projectId,
  analysisId,
  mode,
  onModeChange,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('presence');
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [showResolved, setShowResolved] = useState(false);
  const [notificationCount, setNotificationCount] = useState(3);
  const [isCollapsed, setIsCollapsed] = useState(false);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const { user } = useAuth();
  const { socket, isConnected, currentProject } = useCollaboration();

  // Load annotations
  useEffect(() => {
    loadAnnotations();
  }, [projectId, analysisId]);

  const loadAnnotations = async () => {
    try {
      // Mock annotations data
      const mockAnnotations: Annotation[] = [
        {
          id: 'ann1',
          projectId,
          analysisId,
          authorId: 'user1',
          authorName: 'John Doe',
          content: 'The upper cabinets seem to be missing proper measurements',
          type: 'issue',
          status: 'open',
          priority: 'high',
          position: { x: 25, y: 30 },
          anchor: { elementType: 'cabinet', elementId: 'cabinet-1' },
          attachments: [],
          mentions: [],
          replies: [],
          createdAt: new Date('2024-01-15T10:00:00Z'),
          updatedAt: new Date('2024-01-15T10:00:00Z')
        },
        {
          id: 'ann2',
          projectId,
          analysisId,
          authorId: 'user2',
          authorName: 'Jane Smith',
          content: 'Great 3D reconstruction! The depth estimation looks accurate.',
          type: 'comment',
          status: 'open',
          priority: 'low',
          position: { x: 60, y: 45 },
          anchor: { elementType: 'general' },
          attachments: [],
          mentions: [],
          replies: [],
          createdAt: new Date('2024-01-16T14:30:00Z'),
          updatedAt: new Date('2024-01-16T14:30:00Z')
        }
      ];
      
      setAnnotations(mockAnnotations);
    } catch (error) {
      console.error('Failed to load annotations:', error);
    }
  };

  // Handle annotation operations
  const handleAnnotationCreate = (annotation: Omit<Annotation, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newAnnotation: Annotation = {
      ...annotation,
      id: `ann-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    setAnnotations(prev => [...prev, newAnnotation]);
  };

  const handleAnnotationUpdate = (id: string, updates: Partial<Annotation>) => {
    setAnnotations(prev => prev.map(ann => 
      ann.id === id ? { ...ann, ...updates, updatedAt: new Date() } : ann
    ));
  };

  const handleAnnotationDelete = (id: string) => {
    setAnnotations(prev => prev.filter(ann => ann.id !== id));
  };

  // Handle version control operations
  const handleVersionRestore = (versionId: string) => {
    console.log('Restoring version:', versionId);
    // Implementation would restore project to specific version
  };

  const handleVersionCompare = (versionId1: string, versionId2: string) => {
    console.log('Comparing versions:', versionId1, versionId2);
    // Implementation would show version comparison
  };

  const handleBranchCreate = (name: string, description?: string) => {
    console.log('Creating branch:', name, description);
    // Implementation would create new branch
  };

  const handleBranchSwitch = (branchId: string) => {
    console.log('Switching to branch:', branchId);
    // Implementation would switch to different branch
  };

  // Get mode icon and color
  const getModeConfig = (currentMode: string) => {
    switch (currentMode) {
      case 'view':
        return { icon: Eye, color: 'text-aone-sage', bg: 'bg-blue-50' };
      case 'edit':
        return { icon: Edit3, color: 'text-green-600', bg: 'bg-green-50' };
      case 'comment':
        return { icon: MessageCircle, color: 'text-purple-600', bg: 'bg-purple-50' };
      case 'annotate':
        return { icon: Edit3, color: 'text-orange-600', bg: 'bg-orange-50' };
      default:
        return { icon: Eye, color: 'text-muted-foreground', bg: 'bg-muted' };
    }
  };

  const modeConfig = getModeConfig(mode);
  const ModeIcon = modeConfig.icon;

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="aone-flex-between aone-spacing-md border-b bg-white">
        <div className="flex items-center gap-3">
          <h2 className="text-aone-lg font-aone-semibold">Collaboration</h2>
          <Badge variant={isConnected ? 'default' : 'destructive'}>
            {isConnected ? 'Connected' : 'Disconnected'}
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Mode selector */}
          <div className="flex items-center gap-1 p-1 bg-muted rounded-aone-lg">
            {['view', 'edit', 'comment', 'annotate'].map((m) => {
              const config = getModeConfig(m);
              const Icon = config.icon;
              return (
                <Button
                  key={m}
                  size="sm"
                  variant={mode === m ? 'default' : 'ghost'}
                  onClick={() => onModeChange(m as any)}
                  className={mode === m ? config.bg : ''}
                >
                  <Icon className="w-4 h-4" />
                </Button>
              );
            })}
          </div>
          
          {/* Notifications */}
          <Button size="sm" variant="ghost" className="relative">
            <Bell className="w-4 h-4" />
            {notificationCount > 0 && (
              <Badge className="absolute -top-1 -right-1 w-5 h-5 text-xs p-0 aone-flex-center">
                {notificationCount}
              </Badge>
            )}
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setIsCollapsed(!isCollapsed)}
          >
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {!isCollapsed && (
        <>
          {/* User Presence */}
          <div className="aone-spacing-md border-b bg-muted">
            <EnhancedUserPresence
              projectId={projectId}
              maxVisible={6}
              showActivity={true}
              showPermissions={true}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 overflow-hidden">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-4 mx-4 mt-4">
                <TabsTrigger value="presence" className="text-xs">
                  <Users className="w-3 h-3 mr-1" />
                  Team
                </TabsTrigger>
                <TabsTrigger value="comments" className="text-xs">
                  <MessageCircle className="w-3 h-3 mr-1" />
                  Comments
                </TabsTrigger>
                <TabsTrigger value="annotations" className="text-xs">
                  <Edit3 className="w-3 h-3 mr-1" />
                  Notes
                </TabsTrigger>
                <TabsTrigger value="history" className="text-xs">
                  <History className="w-3 h-3 mr-1" />
                  History
                </TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-y-auto">
                <TabsContent value="presence" className="aone-spacing-md space-y-4">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-aone-sm">Project Activity</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center gap-3 text-aone-sm">
                        <Activity className="w-4 h-4 text-green-500" />
                        <span>John Doe is viewing 3D reconstruction</span>
                        <span className="text-xs text-gray-500">2m ago</span>
                      </div>
                      <div className="flex items-center gap-3 text-aone-sm">
                        <MessageCircle className="w-4 h-4 text-blue-500" />
                        <span>Jane Smith added a comment</span>
                        <span className="text-xs text-gray-500">5m ago</span>
                      </div>
                      <div className="flex items-center gap-3 text-aone-sm">
                        <GitBranch className="w-4 h-4 text-purple-500" />
                        <span>Version 1.2.0 created</span>
                        <span className="text-xs text-gray-500">1h ago</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-aone-sm">Project Settings</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <Button size="sm" variant="outline" className="w-full justify-start">
                        <Share2 className="w-4 h-4 mr-2" />
                        Share Project
                      </Button>
                      <Button size="sm" variant="outline" className="w-full justify-start">
                        <Settings className="w-4 h-4 mr-2" />
                        Project Settings
                      </Button>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="comments" className="aone-spacing-md space-y-4">
                  {/* Voice Comments System */}
                  <VoiceCommentsSystem
                    projectId={projectId}
                    analysisId={analysisId}
                    enabled={true}
                    maxRecordingDuration={60000}
                    className="mb-aone-md"
                  />

                  {/* Text Comments */}
                  {analysisId ? (
                    <CommentThread
                      analysisId={analysisId}
                      projectId={projectId}
                    />
                  ) : (
                    <div className="text-center text-gray-500 py-8">
                      <MessageCircle className="w-12 h-12 mx-auto mb-aone-md text-gray-300" />
                      <p>Select an analysis to view comments</p>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="annotations" className="aone-spacing-md space-y-4">
                  <div className="aone-flex-between">
                    <h3 className="font-aone-medium">Annotations</h3>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setShowResolved(!showResolved)}
                      >
                        {showResolved ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                        {showResolved ? 'Hide' : 'Show'} Resolved
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    {annotations
                      .filter(ann => showResolved || ann.status !== 'resolved')
                      .map((annotation) => (
                        <Card key={annotation.id} className="aone-spacing-sm">
                          <div className="flex items-start gap-3">
                            <div className={`w-2 h-2 rounded-full mt-2 ${
                              annotation.priority === 'critical' ? 'bg-red-500' :
                              annotation.priority === 'high' ? 'bg-orange-500' :
                              annotation.priority === 'medium' ? 'bg-yellow-500' :
                              'bg-green-500'
                            }`} />
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="text-aone-sm font-aone-medium">{annotation.authorName}</span>
                                <Badge variant="outline" className="text-xs">
                                  {annotation.type}
                                </Badge>
                                <Badge variant={annotation.status === 'resolved' ? 'default' : 'secondary'} className="text-xs">
                                  {annotation.status}
                                </Badge>
                              </div>
                              <p className="text-aone-sm text-foreground">{annotation.content}</p>
                              <p className="text-xs text-gray-500 mt-1">
                                {annotation.createdAt.toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                        </Card>
                      ))}
                  </div>
                </TabsContent>

                <TabsContent value="history" className="aone-spacing-md">
                  <VersionControlSystem
                    projectId={projectId}
                    onVersionRestore={handleVersionRestore}
                    onVersionCompare={handleVersionCompare}
                    onBranchCreate={handleBranchCreate}
                    onBranchSwitch={handleBranchSwitch}
                  />
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </>
      )}

      {/* Enhanced WebRTC Cursor Tracker - overlays on the main content */}
      <EnhancedCursorTracker
        projectId={projectId}
        enabled={isConnected}
        showTrails={true}
        maxTrailLength={10}
        className="fixed inset-0 pointer-events-none z-40"
      />

      {/* Annotation System - overlays on the main content */}
      <AdvancedAnnotationSystem
        projectId={projectId}
        analysisId={analysisId}
        containerRef={containerRef}
        annotations={annotations}
        onAnnotationCreate={handleAnnotationCreate}
        onAnnotationUpdate={handleAnnotationUpdate}
        onAnnotationDelete={handleAnnotationDelete}
        mode={mode === 'annotate' ? 'annotate' : 'view'}
        showResolved={showResolved}
        className="fixed inset-0 pointer-events-none z-30"
      />
    </div>
  );
};

export default CollaborationDashboard;
