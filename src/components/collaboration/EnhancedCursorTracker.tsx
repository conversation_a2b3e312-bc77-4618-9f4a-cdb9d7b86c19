import React, { useEffect, useRef, useState, useCallback } from 'react';
import { webrtcService, CursorData } from '@/services/webrtcService';
import { useCollaboration } from '@/hooks/useCollaboration';

interface CursorPosition {
  userId: string;
  x: number;
  y: number;
  timestamp: number;
  elementId?: string;
  userName?: string;
  userColor?: string;
}

interface EnhancedCursorTrackerProps {
  projectId: string;
  enabled?: boolean;
  showTrails?: boolean;
  maxTrailLength?: number;
  className?: string;
}

/**
 * Enhanced Cursor Tracker with WebRTC Ultra-Low Latency
 * 
 * Implements Phase 1 of Priority 3 Feature 2 Advanced Collaboration Tools
 * Provides <100ms cursor tracking via WebRTC P2P connections
 * Falls back to WebSocket when P2P is unavailable
 */
const EnhancedCursorTracker: React.FC<EnhancedCursorTrackerProps> = ({
  projectId,
  enabled = true,
  showTrails = true,
  maxTrailLength = 10,
  className = ''
}) => {
  const [cursors, setCursors] = useState<Map<string, CursorPosition>>(new Map());
  const [cursorTrails, setCursorTrails] = useState<Map<string, CursorPosition[]>>(new Map());
  const [isWebRTCConnected, setIsWebRTCConnected] = useState(false);
  const [averageLatency, setAverageLatency] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const lastSentPosition = useRef<{ x: number; y: number; timestamp: number } | null>(null);
  const throttleTimeout = useRef<NodeJS.Timeout | null>(null);
  
  const { socket, isConnected } = useCollaboration();

  // User colors for cursor identification
  const userColors = [
    'hsl(var(--status-info))', 'hsl(var(--status-error))', 'hsl(var(--status-success))', 'hsl(var(--status-warning))',
    'rgb(var(--preset-accent))', 'rgb(var(--preset-accent))', 'rgb(var(--preset-accent))', 'rgb(var(--preset-accent))'
  ];

  /**
   * Get user color based on userId
   */
  const getUserColor = useCallback((userId: string): string => {
    const hash = userId.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    return userColors[Math.abs(hash) % userColors.length];
  }, []);

  /**
   * Initialize WebRTC cursor tracking
   */
  useEffect(() => {
    if (!enabled || !socket || !isConnected) return;

    const initializeWebRTC = async () => {
      try {
        await webrtcService.initialize(socket, socket.id || 'anonymous');
        setIsWebRTCConnected(true);
        console.log('WebRTC cursor tracking initialized');
      } catch (error) {
        console.warn('WebRTC initialization failed, using WebSocket fallback:', error);
        setIsWebRTCConnected(false);
      }
    };

    initializeWebRTC();

    return () => {
      webrtcService.destroy();
      setIsWebRTCConnected(false);
    };
  }, [enabled, socket, isConnected]);

  /**
   * Setup cursor update listeners
   */
  useEffect(() => {
    if (!enabled) return;

    // WebRTC cursor updates (ultra-low latency)
    const unsubscribeWebRTC = webrtcService.onCursorUpdate((data: CursorData) => {
      updateCursorPosition(data);
    });

    // WebSocket cursor updates (fallback)
    const handleWebSocketCursor = (data: CursorData) => {
      updateCursorPosition(data);
    };

    if (socket) {
      socket.on('cursor-update', handleWebSocketCursor);
    }

    return () => {
      unsubscribeWebRTC();
      if (socket) {
        socket.off('cursor-update', handleWebSocketCursor);
      }
    };
  }, [enabled, socket]);

  /**
   * Setup latency monitoring
   */
  useEffect(() => {
    if (!enabled) return;

    const updateLatency = () => {
      const latency = webrtcService.getAverageLatency();
      setAverageLatency(latency);
    };

    const interval = setInterval(updateLatency, 1000);
    return () => clearInterval(interval);
  }, [enabled]);

  /**
   * Update cursor position with interpolation and trails
   */
  const updateCursorPosition = useCallback((data: CursorData) => {
    const { userId, x, y, timestamp, elementId } = data;
    
    if (userId === socket?.id) return; // Don't show own cursor

    const newPosition: CursorPosition = {
      userId,
      x,
      y,
      timestamp,
      elementId,
      userName: `User ${userId.slice(-4)}`,
      userColor: getUserColor(userId)
    };

    // Update cursor position
    setCursors(prev => new Map(prev.set(userId, newPosition)));

    // Update cursor trails
    if (showTrails) {
      setCursorTrails(prev => {
        const newTrails = new Map(prev);
        const userTrail = newTrails.get(userId) || [];
        const updatedTrail = [...userTrail, newPosition].slice(-maxTrailLength);
        newTrails.set(userId, updatedTrail);
        return newTrails;
      });
    }

    // Clean up old cursors (inactive for more than 5 seconds)
    const now = Date.now();
    setCursors(prev => {
      const filtered = new Map();
      prev.forEach((cursor, id) => {
        if (now - cursor.timestamp < 5000) {
          filtered.set(id, cursor);
        }
      });
      return filtered;
    });
  }, [socket?.id, getUserColor, showTrails, maxTrailLength]);

  /**
   * Handle mouse movement with throttling for optimal performance
   */
  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!enabled || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    const now = Date.now();

    // Throttle cursor updates to maintain <100ms target
    if (throttleTimeout.current) {
      clearTimeout(throttleTimeout.current);
    }

    throttleTimeout.current = setTimeout(() => {
      // Only send if position changed significantly or enough time passed
      const lastPos = lastSentPosition.current;
      const shouldSend = !lastPos || 
        Math.abs(x - lastPos.x) > 2 || 
        Math.abs(y - lastPos.y) > 2 || 
        now - lastPos.timestamp > 100;

      if (shouldSend) {
        webrtcService.sendCursorPosition(x, y);
        lastSentPosition.current = { x, y, timestamp: now };
      }
    }, 16); // ~60fps throttling
  }, [enabled]);

  /**
   * Setup mouse event listeners
   */
  useEffect(() => {
    if (!enabled || !containerRef.current) return;

    const container = containerRef.current;
    container.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      container.removeEventListener('mousemove', handleMouseMove);
      if (throttleTimeout.current) {
        clearTimeout(throttleTimeout.current);
      }
    };
  }, [enabled, handleMouseMove]);

  /**
   * Render cursor with smooth animation
   */
  const renderCursor = (cursor: CursorPosition) => {
    const trail = cursorTrails.get(cursor.userId) || [];
    
    return (
      <div key={cursor.userId}>
        {/* Cursor trail */}
        {showTrails && trail.map((trailPoint, index) => (
          <div
            key={`${cursor.userId}-trail-${index}`}
            className="absolute pointer-events-none transition-all duration-100 ease-out"
            style={{
              left: trailPoint.x,
              top: trailPoint.y,
              transform: 'translate(-50%, -50%)',
              opacity: (index + 1) / trail.length * 0.3,
              zIndex: 1000 + index
            }}
          >
            <div
              className="w-2 h-2 rounded-full"
              style={{ backgroundColor: cursor.userColor }}
            />
          </div>
        ))}
        
        {/* Main cursor */}
        <div
          className="absolute pointer-events-none transition-all duration-75 ease-out"
          style={{
            left: cursor.x,
            top: cursor.y,
            transform: 'translate(-2px, -2px)',
            zIndex: 1010
          }}
        >
          {/* Cursor pointer */}
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            className="drop-shadow-lg"
          >
            <path
              d="M0 0L0 16L5 11L8 16L12 14L9 9L16 9L0 0Z"
              fill={cursor.userColor}
              stroke="white"
              strokeWidth="1"
            />
          </svg>
          
          {/* User label */}
          <div
            className="absolute top-5 left-2 px-2 py-1 text-xs text-white rounded shadow-lg whitespace-nowrap"
            style={{ backgroundColor: cursor.userColor }}
          >
            {cursor.userName}
          </div>
        </div>
      </div>
    );
  };

  if (!enabled) return null;

  return (
    <>
      {/* Cursor tracking container */}
      <div
        ref={containerRef}
        className={`absolute inset-0 pointer-events-none ${className}`}
        style={{ zIndex: 1000 }}
      >
        {Array.from(cursors.values()).map(renderCursor)}
      </div>

      {/* Connection status indicator */}
      <div className="fixed bottom-4 right-4 z-50">
        <div className="bg-white dark:bg-gray-800 rounded-aone-lg shadow-lg aone-spacing-sm text-aone-sm">
          <div className="flex items-center space-x-2">
            <div
              className={`w-2 h-2 rounded-full ${
                isWebRTCConnected ? 'bg-status-success' : 'bg-status-warning'
              }`}
            />
            <span className="text-aone-charcoal dark:text-gray-300">
              {isWebRTCConnected ? 'WebRTC P2P' : 'WebSocket'}
            </span>
          </div>
          
          {averageLatency > 0 && (
            <div className="text-xs text-aone-soft-gray mt-1">
              Latency: {Math.round(averageLatency)}ms
            </div>
          )}
          
          <div className="text-xs text-aone-soft-gray">
            Active cursors: {cursors.size}
          </div>
        </div>
      </div>
    </>
  );
};

export default EnhancedCursorTracker;
