import React, { useState, useRef, useCallback, useEffect } from 'react';
import { useCollaboration } from '../../hooks/useCollaboration';
import { useAuth } from '../../hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  MessageCircle, 
  Pin, 
  Edit3, 
  Trash2, 
  Check, 
  X, 
  Reply, 
  MoreHorizontal,
  Flag,
  Eye,
  EyeOff
} from 'lucide-react';

export interface Annotation {
  id: string;
  projectId: string;
  analysisId?: string;
  authorId: string;
  authorName: string;
  avatarUrl?: string;
  content: string;
  type: 'comment' | 'highlight' | 'measurement' | 'issue';
  status: 'open' | 'in_progress' | 'resolved';
  priority: 'low' | 'medium' | 'high' | 'critical';
  position: {
    x: number;
    y: number;
    z?: number; // For 3D annotations
  };
  anchor: {
    elementId?: string;
    elementType?: 'cabinet' | 'hardware' | 'measurement' | 'general';
    coordinates?: number[];
  };
  attachments?: string[];
  mentions?: string[];
  replies?: Annotation[];
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
  resolvedBy?: string;
}

interface AdvancedAnnotationSystemProps {
  projectId: string;
  analysisId?: string;
  containerRef: React.RefObject<HTMLElement>;
  annotations: Annotation[];
  onAnnotationCreate: (annotation: Omit<Annotation, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onAnnotationUpdate: (id: string, updates: Partial<Annotation>) => void;
  onAnnotationDelete: (id: string) => void;
  mode: 'view' | 'annotate';
  showResolved?: boolean;
  className?: string;
}

export const AdvancedAnnotationSystem: React.FC<AdvancedAnnotationSystemProps> = ({
  projectId,
  analysisId,
  containerRef,
  annotations,
  onAnnotationCreate,
  onAnnotationUpdate,
  onAnnotationDelete,
  mode,
  showResolved = false,
  className = ''
}) => {
  const [isCreating, setIsCreating] = useState(false);
  const [newAnnotation, setNewAnnotation] = useState<{
    position: { x: number; y: number };
    content: string;
    type: Annotation['type'];
    priority: Annotation['priority'];
  } | null>(null);
  const [selectedAnnotation, setSelectedAnnotation] = useState<string | null>(null);
  const [editingAnnotation, setEditingAnnotation] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');
  
  const { user } = useAuth();
  const { socket } = useCollaboration();

  // Handle click to create annotation
  const handleContainerClick = useCallback((event: React.MouseEvent) => {
    if (mode !== 'annotate' || isCreating) return;

    const container = containerRef.current;
    if (!container) return;

    const rect = container.getBoundingClientRect();
    const position = {
      x: ((event.clientX - rect.left) / rect.width) * 100,
      y: ((event.clientY - rect.top) / rect.height) * 100
    };

    setNewAnnotation({
      position,
      content: '',
      type: 'comment',
      priority: 'medium'
    });
    setIsCreating(true);
  }, [mode, isCreating, containerRef]);

  // Create new annotation
  const handleCreateAnnotation = useCallback(async () => {
    if (!newAnnotation || !user || !newAnnotation.content.trim()) return;

    const annotation: Omit<Annotation, 'id' | 'createdAt' | 'updatedAt'> = {
      projectId,
      analysisId,
      authorId: user.id,
      authorName: `${user.firstName} ${user.lastName}`,
      avatarUrl: user.avatarUrl,
      content: newAnnotation.content.trim(),
      type: newAnnotation.type,
      status: 'open',
      priority: newAnnotation.priority,
      position: newAnnotation.position,
      anchor: {
        elementType: 'general'
      },
      attachments: [],
      mentions: extractMentions(newAnnotation.content),
      replies: []
    };

    onAnnotationCreate(annotation);
    
    // Send real-time update
    if (socket) {
      socket.emit('new-annotation', annotation);
    }

    setNewAnnotation(null);
    setIsCreating(false);
  }, [newAnnotation, user, projectId, analysisId, onAnnotationCreate, socket]);

  // Cancel annotation creation
  const handleCancelCreate = useCallback(() => {
    setNewAnnotation(null);
    setIsCreating(false);
  }, []);

  // Start editing annotation
  const handleStartEdit = useCallback((annotation: Annotation) => {
    setEditingAnnotation(annotation.id);
    setEditContent(annotation.content);
  }, []);

  // Save annotation edit
  const handleSaveEdit = useCallback(async (annotationId: string) => {
    if (!editContent.trim()) return;

    const updates: Partial<Annotation> = {
      content: editContent.trim(),
      mentions: extractMentions(editContent),
      updatedAt: new Date()
    };

    onAnnotationUpdate(annotationId, updates);
    
    // Send real-time update
    if (socket) {
      socket.emit('annotation-updated', { id: annotationId, updates });
    }

    setEditingAnnotation(null);
    setEditContent('');
  }, [editContent, onAnnotationUpdate, socket]);

  // Cancel edit
  const handleCancelEdit = useCallback(() => {
    setEditingAnnotation(null);
    setEditContent('');
  }, []);

  // Resolve annotation
  const handleResolveAnnotation = useCallback((annotationId: string) => {
    const updates: Partial<Annotation> = {
      status: 'resolved',
      resolvedAt: new Date(),
      resolvedBy: user?.id
    };

    onAnnotationUpdate(annotationId, updates);
    
    if (socket) {
      socket.emit('annotation-resolved', { id: annotationId, resolvedBy: user?.id });
    }
  }, [user, onAnnotationUpdate, socket]);

  // Delete annotation
  const handleDeleteAnnotation = useCallback((annotationId: string) => {
    onAnnotationDelete(annotationId);
    
    if (socket) {
      socket.emit('annotation-deleted', { id: annotationId });
    }
  }, [onAnnotationDelete, socket]);

  // Extract mentions from content
  const extractMentions = (content: string): string[] => {
    const mentionRegex = /@(\w+)/g;
    const mentions: string[] = [];
    let match;
    
    while ((match = mentionRegex.exec(content)) !== null) {
      mentions.push(match[1]);
    }
    
    return mentions;
  };

  // Get annotation icon
  const getAnnotationIcon = (type: Annotation['type']) => {
    switch (type) {
      case 'comment': return <MessageCircle className="w-4 h-4" />;
      case 'highlight': return <Pin className="w-4 h-4" />;
      case 'measurement': return <Edit3 className="w-4 h-4" />;
      case 'issue': return <Flag className="w-4 h-4" />;
      default: return <MessageCircle className="w-4 h-4" />;
    }
  };

  // Get priority color
  const getPriorityColor = (priority: Annotation['priority']) => {
    switch (priority) {
      case 'low': return 'bg-green-500';
      case 'medium': return 'bg-yellow-500';
      case 'high': return 'bg-orange-500';
      case 'critical': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  // Get status color
  const getStatusColor = (status: Annotation['status']) => {
    switch (status) {
      case 'open': return 'bg-blue-500';
      case 'in_progress': return 'bg-yellow-500';
      case 'resolved': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  // Filter annotations
  const visibleAnnotations = annotations.filter(annotation => 
    showResolved || annotation.status !== 'resolved'
  );

  // Setup container click handler
  useEffect(() => {
    const container = containerRef.current;
    if (!container || mode !== 'annotate') return;

    container.addEventListener('click', handleContainerClick as any);
    
    return () => {
      container.removeEventListener('click', handleContainerClick as any);
    };
  }, [containerRef, mode, handleContainerClick]);

  return (
    <div className={`absolute inset-0 pointer-events-none ${className}`}>
      {/* Existing annotations */}
      {visibleAnnotations.map((annotation) => (
        <div
          key={annotation.id}
          className="absolute pointer-events-auto"
          style={{
            left: `${annotation.position.x}%`,
            top: `${annotation.position.y}%`,
            transform: 'translate(-50%, -50%)'
          }}
        >
          <Popover 
            open={selectedAnnotation === annotation.id}
            onOpenChange={(open) => setSelectedAnnotation(open ? annotation.id : null)}
          >
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className={`w-8 h-8 p-0 rounded-full border-2 ${getStatusColor(annotation.status)} border-white shadow-lg hover:scale-110 transition-transform`}
              >
                {getAnnotationIcon(annotation.type)}
              </Button>
            </PopoverTrigger>
            
            <PopoverContent className="w-80 p-0" side="top" align="start">
              <div className="aone-spacing-md space-y-3">
                {/* Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-2">
                    <Avatar className="w-6 h-6">
                      <AvatarImage src={annotation.avatarUrl} alt={annotation.authorName} />
                      <AvatarFallback className="text-xs">
                        {annotation.authorName.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-aone-sm font-aone-medium">{annotation.authorName}</p>
                      <p className="text-xs text-gray-500">
                        {annotation.createdAt.toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <Badge variant="outline" className="text-xs">
                      {annotation.type}
                    </Badge>
                    <div className={`w-2 h-2 rounded-full ${getPriorityColor(annotation.priority)}`} />
                  </div>
                </div>

                {/* Content */}
                <div>
                  {editingAnnotation === annotation.id ? (
                    <div className="space-y-2">
                      <Textarea
                        value={editContent}
                        onChange={(e) => setEditContent(e.target.value)}
                        placeholder="Edit annotation..."
                        className="min-h-[60px]"
                      />
                      <div className="flex justify-end space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleCancelEdit}
                        >
                          <X className="w-3 h-3" />
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleSaveEdit(annotation.id)}
                        >
                          <Check className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <p className="text-aone-sm">{annotation.content}</p>
                  )}
                </div>

                {/* Status and actions */}
                <div className="aone-flex-between pt-2 border-t">
                  <Badge variant="secondary" className="text-xs">
                    {annotation.status}
                  </Badge>
                  
                  <div className="flex items-center space-x-1">
                    {annotation.status !== 'resolved' && (
                      <>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleStartEdit(annotation)}
                        >
                          <Edit3 className="w-3 h-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleResolveAnnotation(annotation.id)}
                        >
                          <Check className="w-3 h-3" />
                        </Button>
                      </>
                    )}
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleDeleteAnnotation(annotation.id)}
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      ))}

      {/* New annotation creation */}
      {newAnnotation && (
        <div
          className="absolute pointer-events-auto"
          style={{
            left: `${newAnnotation.position.x}%`,
            top: `${newAnnotation.position.y}%`,
            transform: 'translate(-50%, -50%)'
          }}
        >
          <div className="bg-white rounded-aone-lg shadow-lg border aone-spacing-md w-80">
            <div className="space-y-3">
              <div className="aone-flex-between">
                <h4 className="font-aone-medium">New Annotation</h4>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleCancelCreate}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
              
              <Textarea
                value={newAnnotation.content}
                onChange={(e) => setNewAnnotation(prev => prev ? { ...prev, content: e.target.value } : null)}
                placeholder="Add your annotation..."
                className="min-h-[80px]"
                autoFocus
              />
              
              <div className="aone-flex-between">
                <div className="flex items-center space-x-2">
                  <select
                    value={newAnnotation.type}
                    onChange={(e) => setNewAnnotation(prev => prev ? { ...prev, type: e.target.value as Annotation['type'] } : null)}
                    className="text-aone-sm border rounded px-2 py-1"
                  >
                    <option value="comment">Comment</option>
                    <option value="highlight">Highlight</option>
                    <option value="measurement">Measurement</option>
                    <option value="issue">Issue</option>
                  </select>
                  
                  <select
                    value={newAnnotation.priority}
                    onChange={(e) => setNewAnnotation(prev => prev ? { ...prev, priority: e.target.value as Annotation['priority'] } : null)}
                    className="text-aone-sm border rounded px-2 py-1"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="critical">Critical</option>
                  </select>
                </div>
                
                <Button
                  size="sm"
                  onClick={handleCreateAnnotation}
                  disabled={!newAnnotation.content.trim()}
                >
                  Create
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedAnnotationSystem;
