import React, { useEffect, useState, useRef, useCallback } from 'react';
import { useCollaboration } from '../../hooks/useCollaboration';
import { useAuth } from '../../hooks/useAuth';

interface CursorPosition {
  x: number;
  y: number;
  timestamp: number;
}

interface UserCursor {
  userId: string;
  userName: string;
  avatarUrl?: string;
  position: CursorPosition;
  isActive: boolean;
  color: string;
  lastSeen: number;
}

interface LiveCursorTrackerProps {
  projectId: string;
  containerRef: React.RefObject<HTMLElement>;
  enabled?: boolean;
  showTrails?: boolean;
  className?: string;
}

export const LiveCursorTracker: React.FC<LiveCursorTrackerProps> = ({
  projectId,
  containerRef,
  enabled = true,
  showTrails = true,
  className = ''
}) => {
  const [cursors, setCursors] = useState<Map<string, UserCursor>>(new Map());
  const [isTracking, setIsTracking] = useState(false);
  const { socket } = useCollaboration();
  const { user } = useAuth();
  const lastPositionRef = useRef<CursorPosition | null>(null);
  const throttleRef = useRef<NodeJS.Timeout | null>(null);

  // User color assignment
  const getUserColor = useCallback((userId: string): string => {
    const colors = [
      'hsl(var(--status-info))', 'hsl(var(--status-error))', 'hsl(var(--status-success))', 'hsl(var(--status-warning))', 
      'rgb(var(--preset-accent))', 'rgb(var(--preset-accent))', 'rgb(var(--preset-accent))', 'rgb(var(--preset-accent))'
    ];
    const hash = userId.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    return colors[Math.abs(hash) % colors.length];
  }, []);

  // Handle mouse movement tracking
  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!enabled || !socket || !user || !containerRef.current) return;

    const container = containerRef.current;
    const rect = container.getBoundingClientRect();
    
    const position: CursorPosition = {
      x: ((event.clientX - rect.left) / rect.width) * 100, // Percentage-based positioning
      y: ((event.clientY - rect.top) / rect.height) * 100,
      timestamp: Date.now()
    };

    // Throttle cursor updates to prevent spam
    if (throttleRef.current) {
      clearTimeout(throttleRef.current);
    }

    throttleRef.current = setTimeout(() => {
      // Only send if position changed significantly
      if (!lastPositionRef.current || 
          Math.abs(lastPositionRef.current.x - position.x) > 0.5 ||
          Math.abs(lastPositionRef.current.y - position.y) > 0.5) {
        
        socket.emit('cursor-update', {
          projectId,
          userId: user.id,
          position,
          viewData: {
            viewport: {
              width: rect.width,
              height: rect.height
            }
          }
        });

        lastPositionRef.current = position;
      }
    }, 50); // 20fps update rate
  }, [enabled, socket, user, projectId, containerRef]);

  // Handle cursor visibility
  const handleMouseEnter = useCallback(() => {
    setIsTracking(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsTracking(false);
    if (socket && user) {
      socket.emit('cursor-leave', {
        projectId,
        userId: user.id
      });
    }
  }, [socket, user, projectId]);

  // Setup event listeners
  useEffect(() => {
    if (!enabled || !containerRef.current) return;

    const container = containerRef.current;
    
    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mouseenter', handleMouseEnter);
    container.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('mouseenter', handleMouseEnter);
      container.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [enabled, containerRef, handleMouseMove, handleMouseEnter, handleMouseLeave]);

  // Handle incoming cursor updates
  useEffect(() => {
    if (!socket) return;

    const handleCursorMoved = (data: {
      userId: string;
      user: any;
      position: CursorPosition;
      viewData?: any;
      timestamp: string;
    }) => {
      if (data.userId === user?.id) return; // Ignore own cursor

      setCursors(prev => {
        const newCursors = new Map(prev);
        newCursors.set(data.userId, {
          userId: data.userId,
          userName: `${data.user.firstName} ${data.user.lastName}`,
          avatarUrl: data.user.avatarUrl,
          position: data.position,
          isActive: true,
          color: getUserColor(data.userId),
          lastSeen: Date.now()
        });
        return newCursors;
      });
    };

    const handleCursorLeft = (data: { userId: string }) => {
      setCursors(prev => {
        const newCursors = new Map(prev);
        newCursors.delete(data.userId);
        return newCursors;
      });
    };

    socket.on('cursor-moved', handleCursorMoved);
    socket.on('cursor-left', handleCursorLeft);

    return () => {
      socket.off('cursor-moved', handleCursorMoved);
      socket.off('cursor-left', handleCursorLeft);
    };
  }, [socket, user, getUserColor]);

  // Cleanup inactive cursors
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      setCursors(prev => {
        const newCursors = new Map(prev);
        for (const [userId, cursor] of newCursors) {
          if (now - cursor.lastSeen > 5000) { // 5 seconds timeout
            newCursors.delete(userId);
          }
        }
        return newCursors;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  if (!enabled || !containerRef.current) return null;

  return (
    <div className={`absolute inset-0 pointer-events-none z-50 ${className}`}>
      {Array.from(cursors.values()).map((cursor) => (
        <CursorIndicator
          key={cursor.userId}
          cursor={cursor}
          showTrails={showTrails}
        />
      ))}
    </div>
  );
};

interface CursorIndicatorProps {
  cursor: UserCursor;
  showTrails: boolean;
}

const CursorIndicator: React.FC<CursorIndicatorProps> = ({ cursor, showTrails }) => {
  const [trail, setTrail] = useState<CursorPosition[]>([]);

  useEffect(() => {
    if (showTrails) {
      setTrail(prev => {
        const newTrail = [...prev, cursor.position].slice(-5); // Keep last 5 positions
        return newTrail;
      });
    }
  }, [cursor.position, showTrails]);

  return (
    <>
      {/* Cursor trail */}
      {showTrails && trail.map((pos, index) => (
        <div
          key={index}
          className="absolute w-2 h-2 rounded-full transition-opacity duration-300"
          style={{
            left: `${pos.x}%`,
            top: `${pos.y}%`,
            backgroundColor: cursor.color,
            opacity: (index + 1) / trail.length * 0.3,
            transform: 'translate(-50%, -50%)'
          }}
        />
      ))}
      
      {/* Main cursor */}
      <div
        className="absolute transition-all duration-100 ease-out"
        style={{
          left: `${cursor.position.x}%`,
          top: `${cursor.position.y}%`,
          transform: 'translate(-50%, -50%)'
        }}
      >
        {/* Cursor pointer */}
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          className="drop-shadow-lg"
        >
          <path
            d="M5.65376 12.3673H5.46026L5.31717 12.4976L0.500002 16.8829L0.500002 1.19841L11.7841 12.3673H5.65376Z"
            fill={cursor.color}
            stroke="white"
            strokeWidth="1"
          />
        </svg>
        
        {/* User label */}
        <div
          className="absolute top-6 left-2 px-2 py-1 rounded text-xs font-aone-medium text-white shadow-lg whitespace-nowrap"
          style={{ backgroundColor: cursor.color }}
        >
          {cursor.userName}
        </div>
      </div>
    </>
  );
};

export default LiveCursorTracker;
