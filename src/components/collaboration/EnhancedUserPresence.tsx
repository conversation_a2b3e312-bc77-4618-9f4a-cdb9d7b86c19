import React, { useState, useEffect } from 'react';
import { useCollaboration } from '../../hooks/useCollaboration';
import { useAuth } from '../../hooks/useAuth';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  Eye, 
  Edit3, 
  MessageCircle, 
  Settings, 
  Crown, 
  Shield, 
  Users,
  Clock,
  Activity,
  Wifi,
  WifiOff
} from 'lucide-react';

interface EnhancedUserPresence {
  userId: string;
  userName: string;
  avatarUrl?: string;
  email: string;
  role: 'admin' | 'designer' | 'collaborator' | 'viewer';
  status: 'online' | 'away' | 'offline';
  activity: 'viewing' | 'editing' | 'commenting' | 'idle';
  currentView?: {
    section: string;
    details: string;
  };
  lastActivity: Date;
  joinedAt: Date;
  permissions: string[];
}

interface EnhancedUserPresenceProps {
  projectId: string;
  maxVisible?: number;
  showActivity?: boolean;
  showPermissions?: boolean;
  className?: string;
}

export const EnhancedUserPresence: React.FC<EnhancedUserPresenceProps> = ({
  projectId,
  maxVisible = 5,
  showActivity = true,
  showPermissions = false,
  className = ''
}) => {
  const [presenceData, setPresenceData] = useState<EnhancedUserPresence[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { socket, getProjectPresence } = useCollaboration();
  const { user } = useAuth();

  // Load initial presence data
  useEffect(() => {
    loadPresenceData();
  }, [projectId]);

  const loadPresenceData = async () => {
    try {
      setIsLoading(true);
      const presence = await getProjectPresence(projectId);
      
      // Transform to enhanced presence data
      const enhancedPresence: EnhancedUserPresence[] = presence.map(p => ({
        userId: p.userId,
        userName: p.userName,
        avatarUrl: p.avatarUrl,
        email: '', // Would be fetched from user service
        role: 'collaborator', // Would be fetched from permissions
        status: p.status,
        activity: 'viewing', // Default activity
        currentView: p.currentView,
        lastActivity: p.lastActivity,
        joinedAt: new Date(), // Would be tracked
        permissions: ['view', 'comment'] // Would be fetched from permissions
      }));
      
      setPresenceData(enhancedPresence);
    } catch (error) {
      console.error('Failed to load presence data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle real-time presence updates
  useEffect(() => {
    if (!socket) return;

    const handleUserJoined = (data: any) => {
      if (data.projectId === projectId) {
        setPresenceData(prev => {
          const existing = prev.find(p => p.userId === data.userId);
          if (existing) return prev;
          
          return [...prev, {
            userId: data.userId,
            userName: data.user?.firstName + ' ' + data.user?.lastName || 'Unknown User',
            avatarUrl: data.user?.avatarUrl,
            email: data.user?.email || '',
            role: data.user?.role || 'viewer',
            status: 'online',
            activity: 'viewing',
            lastActivity: new Date(),
            joinedAt: new Date(),
            permissions: ['view']
          }];
        });
      }
    };

    const handleUserLeft = (data: any) => {
      if (data.projectId === projectId) {
        setPresenceData(prev => prev.filter(p => p.userId !== data.userId));
      }
    };

    const handlePresenceUpdate = (data: any) => {
      setPresenceData(prev => prev.map(p => 
        p.userId === data.userId 
          ? { ...p, ...data, lastActivity: new Date() }
          : p
      ));
    };

    const handleActivityUpdate = (data: {
      userId: string;
      activity: 'viewing' | 'editing' | 'commenting' | 'idle';
      currentView?: any;
    }) => {
      setPresenceData(prev => prev.map(p => 
        p.userId === data.userId 
          ? { 
              ...p, 
              activity: data.activity,
              currentView: data.currentView,
              lastActivity: new Date()
            }
          : p
      ));
    };

    socket.on('user-joined', handleUserJoined);
    socket.on('user-left', handleUserLeft);
    socket.on('presence-updated', handlePresenceUpdate);
    socket.on('activity-updated', handleActivityUpdate);

    return () => {
      socket.off('user-joined', handleUserJoined);
      socket.off('user-left', handleUserLeft);
      socket.off('presence-updated', handlePresenceUpdate);
      socket.off('activity-updated', handleActivityUpdate);
    };
  }, [socket, projectId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'away': return 'bg-yellow-500';
      case 'offline': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  const getActivityIcon = (activity: string) => {
    switch (activity) {
      case 'viewing': return <Eye className="w-3 h-3" />;
      case 'editing': return <Edit3 className="w-3 h-3" />;
      case 'commenting': return <MessageCircle className="w-3 h-3" />;
      case 'idle': return <Clock className="w-3 h-3" />;
      default: return <Activity className="w-3 h-3" />;
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return <Crown className="w-3 h-3 text-yellow-500" />;
      case 'designer': return <Shield className="w-3 h-3 text-blue-500" />;
      case 'collaborator': return <Users className="w-3 h-3 text-green-500" />;
      case 'viewer': return <Eye className="w-3 h-3 text-gray-500" />;
      default: return <Users className="w-3 h-3 text-gray-500" />;
    }
  };

  const formatLastActivity = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    return `${Math.floor(hours / 24)}d ago`;
  };

  const visibleUsers = presenceData.slice(0, maxVisible);
  const hiddenCount = Math.max(0, presenceData.length - maxVisible);

  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="animate-pulse flex space-x-1">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="w-8 h-8 bg-gray-200 rounded-full" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className={`flex items-center space-x-2 ${className}`}>
        {/* Connection status indicator */}
        <div className="flex items-center space-x-1">
          {socket?.connected ? (
            <Wifi className="w-4 h-4 text-green-500" />
          ) : (
            <WifiOff className="w-4 h-4 text-red-500" />
          )}
          <span className="text-xs text-gray-500">
            {presenceData.length} online
          </span>
        </div>

        {/* User avatars */}
        <div className="flex -space-x-2">
          {visibleUsers.map((presence) => (
            <Popover key={presence.userId}>
              <PopoverTrigger asChild>
                <div className="relative cursor-pointer">
                  <Avatar className="w-8 h-8 border-2 border-white hover:scale-110 transition-transform">
                    <AvatarImage src={presence.avatarUrl} alt={presence.userName} />
                    <AvatarFallback className="text-xs">
                      {presence.userName.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  
                  {/* Status indicator */}
                  <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(presence.status)}`} />
                  
                  {/* Activity indicator */}
                  {showActivity && (
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-white rounded-full border border-border aone-flex-center">
                      {getActivityIcon(presence.activity)}
                    </div>
                  )}
                </div>
              </PopoverTrigger>
              
              <PopoverContent className="w-80 aone-spacing-md" side="bottom" align="start">
                <div className="space-y-3">
                  {/* User header */}
                  <div className="flex items-center space-x-3">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src={presence.avatarUrl} alt={presence.userName} />
                      <AvatarFallback>
                        {presence.userName.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-aone-medium">{presence.userName}</h4>
                        {showPermissions && getRoleIcon(presence.role)}
                      </div>
                      <p className="text-aone-sm text-gray-500">{presence.email}</p>
                      
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant={presence.status === 'online' ? 'default' : 'secondary'} className="text-xs">
                          {presence.status}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {presence.activity}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Current activity */}
                  {presence.currentView && (
                    <div className="aone-spacing-xs bg-muted rounded-aone-lg">
                      <p className="text-xs font-aone-medium text-foreground">Currently viewing:</p>
                      <p className="text-aone-sm">{presence.currentView.section}</p>
                      {presence.currentView.details && (
                        <p className="text-xs text-gray-500">{presence.currentView.details}</p>
                      )}
                    </div>
                  )}

                  {/* Activity timeline */}
                  <div className="text-xs text-gray-500">
                    <p>Last activity: {formatLastActivity(presence.lastActivity)}</p>
                    <p>Joined: {formatLastActivity(presence.joinedAt)}</p>
                  </div>

                  {/* Permissions */}
                  {showPermissions && presence.permissions.length > 0 && (
                    <div>
                      <p className="text-xs font-aone-medium text-foreground mb-1">Permissions:</p>
                      <div className="flex flex-wrap gap-1">
                        {presence.permissions.map((permission) => (
                          <Badge key={permission} variant="outline" className="text-xs">
                            {permission}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </PopoverContent>
            </Popover>
          ))}

          {/* Show more indicator */}
          {hiddenCount > 0 && (
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="w-8 h-8 bg-muted border-2 border-white rounded-full aone-flex-center text-xs font-aone-medium text-muted-foreground cursor-pointer hover:bg-gray-200 transition-colors">
                  +{hiddenCount}
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{hiddenCount} more user{hiddenCount > 1 ? 's' : ''} online</p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
};

export default EnhancedUserPresence;
