import React, { useState, useEffect } from 'react';
import { useCollaboration } from '../../hooks/useCollaboration';

export interface UserPresence {
  userId: string;
  userName: string;
  avatarUrl?: string;
  status: 'online' | 'away' | 'offline';
  currentView?: any;
  lastActivity: Date;
}

interface UserPresenceProps {
  projectId: string;
  className?: string;
}

export const UserPresence: React.FC<UserPresenceProps> = ({ projectId, className = '' }) => {
  const [onlineUsers, setOnlineUsers] = useState<UserPresence[]>([]);
  const { getProjectPresence, socket } = useCollaboration();

  useEffect(() => {
    loadPresence();
    
    if (socket) {
      socket.on('user-joined', handleUserJoined);
      socket.on('user-left', handleUserLeft);
      socket.on('presence-updated', handlePresenceUpdate);
      
      return () => {
        socket.off('user-joined', handleUserJoined);
        socket.off('user-left', handleUserLeft);
        socket.off('presence-updated', handlePresenceUpdate);
      };
    }
  }, [projectId, socket]);

  const loadPresence = async () => {
    try {
      const presence = await getProjectPresence(projectId);
      setOnlineUsers(presence);
    } catch (error) {
      console.error('Failed to load presence:', error);
    }
  };

  const handleUserJoined = (data: { userId: string; user: any; projectId: string }) => {
    if (data.projectId === projectId) {
      setOnlineUsers(prev => {
        const existing = prev.find(u => u.userId === data.userId);
        if (existing) return prev;
        
        return [...prev, {
          userId: data.userId,
          userName: `${data.user.firstName} ${data.user.lastName}`,
          avatarUrl: data.user.avatarUrl,
          status: 'online',
          lastActivity: new Date()
        }];
      });
    }
  };

  const handleUserLeft = (data: { userId: string; projectId: string }) => {
    if (data.projectId === projectId) {
      setOnlineUsers(prev => prev.filter(u => u.userId !== data.userId));
    }
  };

  const handlePresenceUpdate = (presence: UserPresence) => {
    setOnlineUsers(prev => {
      const index = prev.findIndex(u => u.userId === presence.userId);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = presence;
        return updated;
      }
      return [...prev, presence];
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-400';
      case 'away': return 'bg-yellow-400';
      case 'offline': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  if (onlineUsers.length === 0) {
    return null;
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <span className="text-aone-sm text-muted-foreground font-aone-medium">
        Online ({onlineUsers.length}):
      </span>
      
      <div className="flex -space-x-2">
        {onlineUsers.slice(0, 5).map((user) => (
          <div
            key={user.userId}
            className="relative group"
            title={`${user.userName} (${user.status})`}
          >
            <div className="w-8 h-8 rounded-full bg-blue-500 aone-flex-center text-white text-xs font-aone-medium border-2 border-white">
              {user.avatarUrl ? (
                <img
                  src={user.avatarUrl}
                  alt={user.userName}
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                getInitials(user.userName)
              )}
            </div>
            
            <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(user.status)}`}></div>
            
            {/* Tooltip */}
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
              {user.userName}
              <div className="text-xs text-gray-300 capitalize">{user.status}</div>
            </div>
          </div>
        ))}
        
        {onlineUsers.length > 5 && (
          <div className="w-8 h-8 rounded-full bg-gray-500 aone-flex-center text-white text-xs font-aone-medium border-2 border-white">
            +{onlineUsers.length - 5}
          </div>
        )}
      </div>
    </div>
  );
};

export default UserPresence;
