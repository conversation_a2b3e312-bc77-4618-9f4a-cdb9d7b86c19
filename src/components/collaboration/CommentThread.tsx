import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useCollaboration } from '../../hooks/useCollaboration';

export interface Comment {
  id: string;
  projectId?: string;
  analysisId?: string;
  parentCommentId?: string;
  content: string;
  authorId: string;
  authorName: string;
  avatarUrl?: string;
  status: 'open' | 'in_progress' | 'resolved';
  positionData?: any;
  attachments?: string[];
  mentions?: string[];
  createdAt: Date;
  updatedAt: Date;
  replies?: Comment[];
}

interface CommentThreadProps {
  analysisId: string;
  projectId?: string;
  onCommentAdded?: (comment: Comment) => void;
}

export const CommentThread: React.FC<CommentThreadProps> = ({ 
  analysisId, 
  projectId, 
  onCommentAdded 
}) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { user } = useAuth();
  const { createComment, getComments, updateCommentStatus } = useCollaboration();

  useEffect(() => {
    loadComments();
  }, [analysisId]);

  const loadComments = async () => {
    setIsLoading(true);
    try {
      const fetchedComments = await getComments(analysisId);
      setComments(organizeComments(fetchedComments));
    } catch (error) {
      console.error('Failed to load comments:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const organizeComments = (flatComments: Comment[]): Comment[] => {
    const commentMap = new Map<string, Comment>();
    const rootComments: Comment[] = [];

    // First pass: create comment map
    flatComments.forEach(comment => {
      commentMap.set(comment.id, { ...comment, replies: [] });
    });

    // Second pass: organize into threads
    flatComments.forEach(comment => {
      const commentWithReplies = commentMap.get(comment.id)!;
      
      if (comment.parentCommentId) {
        const parent = commentMap.get(comment.parentCommentId);
        if (parent) {
          parent.replies!.push(commentWithReplies);
        }
      } else {
        rootComments.push(commentWithReplies);
      }
    });

    return rootComments.sort((a, b) => 
      new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );
  };

  const handleSubmitComment = async (e: React.FormEvent, parentId?: string) => {
    e.preventDefault();
    if (!newComment.trim() || !user) return;

    setIsSubmitting(true);
    try {
      const comment = await createComment({
        projectId,
        analysisId,
        parentCommentId: parentId,
        content: newComment.trim(),
        authorId: user.id
      });

      setNewComment('');
      await loadComments(); // Reload to get updated thread
      onCommentAdded?.(comment);
    } catch (error) {
      console.error('Failed to create comment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleStatusChange = async (commentId: string, status: string) => {
    try {
      await updateCommentStatus(commentId, status);
      await loadComments(); // Reload to get updated status
    } catch (error) {
      console.error('Failed to update comment status:', error);
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-blue-100 text-blue-800';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      default: return 'bg-muted text-foreground';
    }
  };

  const renderComment = (comment: Comment, isReply = false) => (
    <div key={comment.id} className={`${isReply ? 'ml-8 mt-3' : 'mb-aone-md'}`}>
      <div className="bg-white rounded-aone-lg border border-border aone-spacing-md">
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-500 rounded-full aone-flex-center text-white text-aone-sm font-aone-medium">
              {comment.authorName.split(' ').map(n => n[0]).join('')}
            </div>
            <div>
              <p className="font-aone-medium text-foreground">{comment.authorName}</p>
              <p className="text-xs text-gray-500">{formatDate(comment.createdAt)}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 rounded-full text-xs font-aone-medium ${getStatusColor(comment.status)}`}>
              {comment.status.replace('_', ' ')}
            </span>
            
            {user && (user.id === comment.authorId || user.role === 'admin') && (
              <select
                value={comment.status}
                onChange={(e) => handleStatusChange(comment.id, e.target.value)}
                className="text-xs border border-border rounded px-2 py-1"
              >
                <option value="open">Open</option>
                <option value="in_progress">In Progress</option>
                <option value="resolved">Resolved</option>
              </select>
            )}
          </div>
        </div>
        
        <div className="text-foreground mb-3">
          {comment.content}
        </div>
        
        {comment.mentions && comment.mentions.length > 0 && (
          <div className="text-xs text-aone-sage mb-2">
            Mentioned: {comment.mentions.join(', ')}
          </div>
        )}
        
        {!isReply && (
          <button
            onClick={() => {/* Handle reply */}}
            className="text-aone-sm aone-button-ghost text-aone-sage hover:text-aone-sage-dark"
          >
            Reply
          </button>
        )}
      </div>
      
      {comment.replies && comment.replies.length > 0 && (
        <div className="mt-2">
          {comment.replies.map(reply => renderComment(reply, true))}
        </div>
      )}
    </div>
  );

  if (isLoading) {
    return (
      <div className="aone-flex-center p-aone-xl">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="aone-flex-between">
        <h3 className="text-aone-lg font-aone-medium text-foreground">
          Comments ({comments.length})
        </h3>
      </div>
      
      {/* New Comment Form */}
      {user && (
        <form onSubmit={handleSubmitComment} className="mb-aone-lg">
          <div className="border border-border rounded-aone-lg">
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Add a comment..."
              rows={3}
              className="w-full px-3 py-2 border-0 rounded-aone-lg resize-none aone-focus-ring aone-focus-ring"
            />
            <div className="flex justify-between items-center px-3 py-2 bg-muted rounded-b-lg">
              <div className="text-xs text-gray-500">
                Use @username to mention someone
              </div>
              <button
                type="submit"
                disabled={!newComment.trim() || isSubmitting}
                className="px-aone-md py-1 bg-blue-600 text-white rounded text-aone-sm aone-micro-interaction hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Posting...' : 'Post Comment'}
              </button>
            </div>
          </div>
        </form>
      )}
      
      {/* Comments List */}
      <div className="space-y-4">
        {comments.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No comments yet. Be the first to comment!
          </div>
        ) : (
          comments.map(comment => renderComment(comment))
        )}
      </div>
    </div>
  );
};

export default CommentThread;
