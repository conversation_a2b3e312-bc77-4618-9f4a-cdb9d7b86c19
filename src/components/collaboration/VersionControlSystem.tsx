import React, { useState, useEffect, useCallback } from 'react';
import { useCollaboration } from '../../hooks/useCollaboration';
import { useAuth } from '../../hooks/useAuth';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import {
  History,
  GitBranch,
  GitMerge,
  Camera,
  Download,
  Eye,
  RotateCcw,
  Copy,
  Trash2,
  Clock,
  User,
  FileText,
  Image as ImageIcon,
  GitCompareArrows
} from 'lucide-react';
import html2canvas from 'html2canvas';

export interface ProjectVersion {
  id: string;
  projectId: string;
  versionNumber: string;
  title: string;
  description?: string;
  authorId: string;
  authorName: string;
  avatarUrl?: string;
  thumbnail?: string;
  changes: VersionChange[];
  metadata: {
    analysisCount: number;
    commentCount: number;
    annotationCount: number;
    fileSize: number;
  };
  parentVersionId?: string;
  branchName?: string;
  tags: string[];
  isAutoSave: boolean;
  createdAt: Date;
}

export interface VersionChange {
  type: 'added' | 'modified' | 'deleted';
  category: 'analysis' | 'comment' | 'annotation' | 'settings' | 'file';
  description: string;
  details?: any;
}

export interface VersionBranch {
  id: string;
  name: string;
  description?: string;
  baseVersionId: string;
  headVersionId: string;
  authorId: string;
  authorName: string;
  isActive: boolean;
  createdAt: Date;
}

interface VersionControlSystemProps {
  projectId: string;
  currentVersionId?: string;
  onVersionRestore: (versionId: string) => void;
  onVersionCompare: (versionId1: string, versionId2: string) => void;
  onBranchCreate: (name: string, description?: string) => void;
  onBranchSwitch: (branchId: string) => void;
  className?: string;
}

export const VersionControlSystem: React.FC<VersionControlSystemProps> = ({
  projectId,
  currentVersionId,
  onVersionRestore,
  onVersionCompare,
  onBranchCreate,
  onBranchSwitch,
  className = ''
}) => {
  const [versions, setVersions] = useState<ProjectVersion[]>([]);
  const [branches, setBranches] = useState<VersionBranch[]>([]);
  const [selectedVersions, setSelectedVersions] = useState<string[]>([]);
  const [isCreatingSnapshot, setIsCreatingSnapshot] = useState(false);
  const [isCreatingBranch, setIsCreatingBranch] = useState(false);
  const [newBranchName, setNewBranchName] = useState('');
  const [newBranchDescription, setNewBranchDescription] = useState('');
  const [activeTab, setActiveTab] = useState('history');
  
  const { user } = useAuth();
  const { socket } = useCollaboration();

  // Load version history
  useEffect(() => {
    loadVersionHistory();
    loadBranches();
  }, [projectId]);

  const loadVersionHistory = async () => {
    try {
      // This would be an API call to fetch version history
      const mockVersions: ProjectVersion[] = [
        {
          id: 'v1',
          projectId,
          versionNumber: '1.0.0',
          title: 'Initial Analysis',
          description: 'First cabinet analysis with basic measurements',
          authorId: 'user1',
          authorName: 'John Doe',
          thumbnail: '/api/thumbnails/v1.jpg',
          changes: [
            { type: 'added', category: 'analysis', description: 'Added initial cabinet analysis' },
            { type: 'added', category: 'file', description: 'Uploaded kitchen layout PDF' }
          ],
          metadata: {
            analysisCount: 1,
            commentCount: 0,
            annotationCount: 0,
            fileSize: 2048000
          },
          branchName: 'main',
          tags: ['initial', 'baseline'],
          isAutoSave: false,
          createdAt: new Date('2024-01-15T10:00:00Z')
        },
        {
          id: 'v2',
          projectId,
          versionNumber: '1.1.0',
          title: 'Added 3D Reconstruction',
          description: 'Enhanced analysis with 3D cabinet models',
          authorId: 'user1',
          authorName: 'John Doe',
          thumbnail: '/api/thumbnails/v2.jpg',
          changes: [
            { type: 'modified', category: 'analysis', description: 'Updated analysis with 3D reconstruction' },
            { type: 'added', category: 'comment', description: 'Added 3 team comments' }
          ],
          metadata: {
            analysisCount: 1,
            commentCount: 3,
            annotationCount: 0,
            fileSize: 3072000
          },
          parentVersionId: 'v1',
          branchName: 'main',
          tags: ['3d', 'enhanced'],
          isAutoSave: false,
          createdAt: new Date('2024-01-16T14:30:00Z')
        }
      ];
      
      setVersions(mockVersions);
    } catch (error) {
      console.error('Failed to load version history:', error);
    }
  };

  const loadBranches = async () => {
    try {
      const mockBranches: VersionBranch[] = [
        {
          id: 'main',
          name: 'main',
          description: 'Main development branch',
          baseVersionId: 'v1',
          headVersionId: 'v2',
          authorId: 'user1',
          authorName: 'John Doe',
          isActive: true,
          createdAt: new Date('2024-01-15T10:00:00Z')
        }
      ];
      
      setBranches(mockBranches);
    } catch (error) {
      console.error('Failed to load branches:', error);
    }
  };

  // Create version snapshot
  const createSnapshot = useCallback(async (title: string, description?: string) => {
    setIsCreatingSnapshot(true);
    
    try {
      // Capture thumbnail
      const thumbnail = await captureProjectThumbnail();
      
      // Detect changes (this would be more sophisticated in real implementation)
      const changes: VersionChange[] = [
        { type: 'modified', category: 'analysis', description: 'Updated analysis results' }
      ];
      
      const newVersion: ProjectVersion = {
        id: `v${Date.now()}`,
        projectId,
        versionNumber: generateVersionNumber(),
        title,
        description,
        authorId: user!.id,
        authorName: `${user!.firstName} ${user!.lastName}`,
        avatarUrl: user!.avatarUrl,
        thumbnail,
        changes,
        metadata: {
          analysisCount: 1,
          commentCount: 0,
          annotationCount: 0,
          fileSize: 2048000
        },
        parentVersionId: currentVersionId,
        branchName: 'main',
        tags: [],
        isAutoSave: false,
        createdAt: new Date()
      };
      
      setVersions(prev => [newVersion, ...prev]);
      
      // Send to server
      if (socket) {
        socket.emit('version-created', newVersion);
      }
      
    } catch (error) {
      console.error('Failed to create snapshot:', error);
    } finally {
      setIsCreatingSnapshot(false);
    }
  }, [projectId, currentVersionId, user, socket]);

  // Capture project thumbnail
  const captureProjectThumbnail = async (): Promise<string> => {
    try {
      const element = document.querySelector('[data-project-canvas]') as HTMLElement;
      if (!element) return '';
      
      const canvas = await html2canvas(element, {
        width: 300,
        height: 200,
        scale: 0.5
      });
      
      return canvas.toDataURL('image/jpeg', 0.8);
    } catch (error) {
      console.error('Failed to capture thumbnail:', error);
      return '';
    }
  };

  // Generate version number
  const generateVersionNumber = (): string => {
    const lastVersion = versions[0];
    if (!lastVersion) return '1.0.0';
    
    const [major, minor, patch] = lastVersion.versionNumber.split('.').map(Number);
    return `${major}.${minor}.${patch + 1}`;
  };

  // Handle version selection for comparison
  const handleVersionSelect = (versionId: string) => {
    setSelectedVersions(prev => {
      if (prev.includes(versionId)) {
        return prev.filter(id => id !== versionId);
      } else if (prev.length < 2) {
        return [...prev, versionId];
      } else {
        return [prev[1], versionId];
      }
    });
  };

  // Create new branch
  const handleCreateBranch = () => {
    if (!newBranchName.trim()) return;
    
    onBranchCreate(newBranchName.trim(), newBranchDescription.trim() || undefined);
    setNewBranchName('');
    setNewBranchDescription('');
    setIsCreatingBranch(false);
  };

  // Format file size
  const formatFileSize = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  // Get change type color
  const getChangeTypeColor = (type: VersionChange['type']) => {
    switch (type) {
      case 'added': return 'text-green-600 bg-green-50';
      case 'modified': return 'text-aone-sage bg-blue-50';
      case 'deleted': return 'text-red-600 bg-red-50';
      default: return 'text-muted-foreground bg-muted';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="aone-flex-between">
        <h3 className="text-aone-lg font-aone-semibold flex items-center gap-2">
          <History className="w-5 h-5" />
          Version Control
        </h3>
        
        <div className="flex items-center gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button size="sm" variant="outline">
                <Camera className="w-4 h-4 mr-2" />
                Create Snapshot
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Version Snapshot</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <label className="text-aone-sm font-aone-medium">Title</label>
                  <input
                    type="text"
                    className="w-full mt-1 px-3 py-2 border rounded-aone-md"
                    placeholder="Version title..."
                  />
                </div>
                <div>
                  <label className="text-aone-sm font-aone-medium">Description (optional)</label>
                  <textarea
                    className="w-full mt-1 px-3 py-2 border rounded-aone-md"
                    rows={3}
                    placeholder="Describe the changes..."
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline">Cancel</Button>
                  <Button onClick={() => createSnapshot('Manual Snapshot')}>
                    Create Snapshot
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
          
          {selectedVersions.length === 2 && (
            <Button
              size="sm"
              onClick={() => onVersionCompare(selectedVersions[0], selectedVersions[1])}
            >
              <GitCompareArrows className="w-4 h-4 mr-2" />
              Compare
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="branches">Branches</TabsTrigger>
        </TabsList>

        <TabsContent value="history" className="space-y-4">
          {versions.map((version) => (
            <Card 
              key={version.id} 
              className={`cursor-pointer transition-colors ${
                selectedVersions.includes(version.id) ? 'ring-2 ring-blue-500' : ''
              } ${version.id === currentVersionId ? 'bg-blue-50' : ''}`}
              onClick={() => handleVersionSelect(version.id)}
            >
              <CardContent className="aone-spacing-md">
                <div className="flex items-start gap-aone-md">
                  {/* Thumbnail */}
                  <div className="w-16 h-12 bg-muted rounded border aone-flex-center">
                    {version.thumbnail ? (
                      <img 
                        src={version.thumbnail} 
                        alt="Version thumbnail"
                        className="w-full h-full object-cover rounded"
                      />
                    ) : (
                      <ImageIcon className="w-6 h-6 text-gray-400" />
                    )}
                  </div>

                  {/* Version info */}
                  <div className="flex-1">
                    <div className="aone-flex-between mb-2">
                      <div className="flex items-center gap-2">
                        <h4 className="font-aone-medium">{version.title}</h4>
                        <Badge variant="outline">{version.versionNumber}</Badge>
                        {version.id === currentVersionId && (
                          <Badge>Current</Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation();
                            onVersionRestore(version.id);
                          }}
                        >
                          <RotateCcw className="w-4 h-4" />
                        </Button>
                        <Button size="sm" variant="ghost">
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center gap-aone-md text-aone-sm text-muted-foreground mb-2">
                      <div className="flex items-center gap-1">
                        <Avatar className="w-4 h-4">
                          <AvatarImage src={version.avatarUrl} />
                          <AvatarFallback className="text-xs">
                            {version.authorName.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <span>{version.authorName}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        <span>{version.createdAt.toLocaleDateString()}</span>
                      </div>
                      <span>{formatFileSize(version.metadata.fileSize)}</span>
                    </div>

                    {version.description && (
                      <p className="text-aone-sm text-muted-foreground mb-2">{version.description}</p>
                    )}

                    {/* Changes */}
                    <div className="flex flex-wrap gap-1">
                      {version.changes.slice(0, 3).map((change, index) => (
                        <Badge 
                          key={index}
                          variant="outline" 
                          className={`text-xs ${getChangeTypeColor(change.type)}`}
                        >
                          {change.type} {change.category}
                        </Badge>
                      ))}
                      {version.changes.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{version.changes.length - 3} more
                        </Badge>
                      )}
                    </div>

                    {/* Tags */}
                    {version.tags.length > 0 && (
                      <div className="flex gap-1 mt-2">
                        {version.tags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="branches" className="space-y-4">
          <div className="flex justify-end">
            <Dialog open={isCreatingBranch} onOpenChange={setIsCreatingBranch}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <GitBranch className="w-4 h-4 mr-2" />
                  New Branch
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Branch</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="text-aone-sm font-aone-medium">Branch Name</label>
                    <input
                      type="text"
                      value={newBranchName}
                      onChange={(e) => setNewBranchName(e.target.value)}
                      className="w-full mt-1 px-3 py-2 border rounded-aone-md"
                      placeholder="feature/new-analysis"
                    />
                  </div>
                  <div>
                    <label className="text-aone-sm font-aone-medium">Description (optional)</label>
                    <textarea
                      value={newBranchDescription}
                      onChange={(e) => setNewBranchDescription(e.target.value)}
                      className="w-full mt-1 px-3 py-2 border rounded-aone-md"
                      rows={3}
                      placeholder="Describe the purpose of this branch..."
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setIsCreatingBranch(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreateBranch}>
                      Create Branch
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {branches.map((branch) => (
            <Card key={branch.id} className={branch.isActive ? 'bg-blue-50' : ''}>
              <CardContent className="aone-spacing-md">
                <div className="aone-flex-between">
                  <div className="flex items-center gap-3">
                    <GitBranch className="w-5 h-5" />
                    <div>
                      <div className="flex items-center gap-2">
                        <h4 className="font-aone-medium">{branch.name}</h4>
                        {branch.isActive && <Badge>Active</Badge>}
                      </div>
                      {branch.description && (
                        <p className="text-aone-sm text-muted-foreground">{branch.description}</p>
                      )}
                      <div className="flex items-center gap-aone-md text-xs text-gray-500 mt-1">
                        <span>by {branch.authorName}</span>
                        <span>{branch.createdAt.toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {!branch.isActive && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onBranchSwitch(branch.id)}
                      >
                        Switch
                      </Button>
                    )}
                    <Button size="sm" variant="ghost">
                      <GitMerge className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default VersionControlSystem;
