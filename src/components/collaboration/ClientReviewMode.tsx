import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Eye, 
  MessageCircle, 
  ThumbsUp, 
  ThumbsDown, 
  Download, 
  Share2, 
  CheckCircle,
  Clock,
  User,
  FileText,
  Image as ImageIcon
} from 'lucide-react';
import { AnalysisResults } from '@/services/aiAnalysisService';

interface ClientReviewModeProps {
  analysisResults: AnalysisResults;
  projectId: string;
  clientInfo?: {
    name: string;
    email: string;
    company?: string;
  };
  onFeedbackSubmit: (feedback: ClientFeedback) => void;
  onApprovalChange: (approved: boolean, comments?: string) => void;
  className?: string;
}

interface ClientFeedback {
  id: string;
  type: 'comment' | 'approval' | 'revision_request';
  content: string;
  section?: string;
  priority: 'low' | 'medium' | 'high';
  timestamp: Date;
}

export const ClientReviewMode: React.FC<ClientReviewModeProps> = ({
  analysisResults,
  projectId,
  clientInfo,
  onFeedbackSubmit,
  onApprovalChange,
  className = ''
}) => {
  const [feedback, setFeedback] = useState<ClientFeedback[]>([]);
  const [newComment, setNewComment] = useState('');
  const [selectedSection, setSelectedSection] = useState<string>('overall');
  const [approvalStatus, setApprovalStatus] = useState<'pending' | 'approved' | 'rejected'>('pending');
  const [approvalComments, setApprovalComments] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Simplified sections for client review
  const reviewSections = [
    { id: 'overall', name: 'Overall Design', icon: Eye },
    { id: 'measurements', name: 'Measurements', icon: FileText },
    { id: 'materials', name: 'Materials & Finishes', icon: ImageIcon },
    { id: 'layout', name: 'Layout & Flow', icon: Eye }
  ];

  const handleSubmitFeedback = async () => {
    if (!newComment.trim()) return;

    setIsSubmitting(true);
    
    const feedbackItem: ClientFeedback = {
      id: `feedback-${Date.now()}`,
      type: 'comment',
      content: newComment.trim(),
      section: selectedSection,
      priority: 'medium',
      timestamp: new Date()
    };

    setFeedback(prev => [...prev, feedbackItem]);
    onFeedbackSubmit(feedbackItem);
    setNewComment('');
    setIsSubmitting(false);
  };

  const handleApproval = (approved: boolean) => {
    const status = approved ? 'approved' : 'rejected';
    setApprovalStatus(status);
    onApprovalChange(approved, approvalComments);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NZ', {
      style: 'currency',
      currency: 'NZD'
    }).format(amount);
  };

  const getApprovalStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  return (
    <div className={`max-w-4xl mx-auto space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="aone-flex-between">
            <div>
              <CardTitle className="text-2xl">Kitchen Design Review</CardTitle>
              <p className="text-muted-foreground mt-1">
                Please review the analysis and provide your feedback
              </p>
            </div>
            
            <div className="flex items-center gap-3">
              <Badge className={getApprovalStatusColor(approvalStatus)}>
                {approvalStatus === 'pending' && <Clock className="w-3 h-3 mr-1" />}
                {approvalStatus === 'approved' && <CheckCircle className="w-3 h-3 mr-1" />}
                {approvalStatus === 'rejected' && <ThumbsDown className="w-3 h-3 mr-1" />}
                {approvalStatus.charAt(0).toUpperCase() + approvalStatus.slice(1)}
              </Badge>
              
              {clientInfo && (
                <div className="flex items-center gap-2">
                  <Avatar className="w-8 h-8">
                    <AvatarFallback>
                      {clientInfo.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-aone-sm">
                    <p className="font-aone-medium">{clientInfo.name}</p>
                    {clientInfo.company && (
                      <p className="text-gray-500">{clientInfo.company}</p>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Analysis Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Design Analysis Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-aone-lg">
            <div className="text-center">
              <div className="text-aone-3xl font-aone-bold text-aone-sage">
                {analysisResults.measurements.totalCabinets}
              </div>
              <p className="text-muted-foreground">Total Cabinets</p>
            </div>
            
            <div className="text-center">
              <div className="text-aone-3xl font-aone-bold text-green-600">
                {analysisResults.measurements.totalLinearMeters}m
              </div>
              <p className="text-muted-foreground">Linear Meters</p>
            </div>
            
            <div className="text-center">
              <div className="text-aone-3xl font-aone-bold text-purple-600">
                {Math.round(analysisResults.confidence.overall * 100)}%
              </div>
              <p className="text-muted-foreground">Analysis Confidence</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Analysis Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-aone-lg">
        {/* Cabinet Analysis */}
        <Card>
          <CardHeader>
            <CardTitle>Cabinet Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analysisResults.cabinets.slice(0, 3).map((cabinet, index) => (
                <div key={index} className="aone-flex-between aone-spacing-sm bg-muted rounded-aone-lg">
                  <div>
                    <p className="font-aone-medium">{cabinet.type}</p>
                    <p className="text-aone-sm text-muted-foreground">
                      {cabinet.dimensions.width}W × {cabinet.dimensions.height}H × {cabinet.dimensions.depth}D cm
                    </p>
                  </div>
                  <Badge variant="outline">{cabinet.material}</Badge>
                </div>
              ))}
              
              {analysisResults.cabinets.length > 3 && (
                <p className="text-aone-sm text-gray-500 text-center">
                  +{analysisResults.cabinets.length - 3} more cabinets
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Hardware & Materials */}
        <Card>
          <CardHeader>
            <CardTitle>Hardware & Materials</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analysisResults.hardware.slice(0, 4).map((item, index) => (
                <div key={index} className="aone-flex-between">
                  <div>
                    <p className="font-aone-medium">{item.type}</p>
                    <p className="text-aone-sm text-muted-foreground">{item.brand} - {item.model}</p>
                  </div>
                  <Badge variant="secondary">Qty: {item.quantity}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 3D Visualization (if available) */}
      {analysisResults.reconstruction3D && (
        <Card>
          <CardHeader>
            <CardTitle>3D Visualization</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="aspect-video bg-muted rounded-aone-lg aone-flex-center">
              <div className="text-center">
                <ImageIcon className="w-16 h-16 text-gray-400 mx-auto mb-aone-md" />
                <p className="text-muted-foreground">3D visualization available</p>
                <p className="text-aone-sm text-gray-500">
                  {analysisResults.reconstruction3D.cabinets.length} cabinets reconstructed
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Feedback Section */}
      <Card>
        <CardHeader>
          <CardTitle>Your Feedback</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Section Selector */}
          <div>
            <label className="aone-label-enterprise mb-2 block">
              Select Section to Comment On:
            </label>
            <div className="flex flex-wrap gap-2">
              {reviewSections.map((section) => {
                const Icon = section.icon;
                return (
                  <Button
                    key={section.id}
                    variant={selectedSection === section.id ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedSection(section.id)}
                  >
                    <Icon className="w-4 h-4 mr-2" />
                    {section.name}
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Comment Input */}
          <div>
            <label className="aone-label-enterprise mb-2 block">
              Add Your Comments:
            </label>
            <Textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Share your thoughts about this design..."
              className="min-h-[100px]"
            />
          </div>

          <Button 
            onClick={handleSubmitFeedback}
            disabled={!newComment.trim() || isSubmitting}
            className="w-full"
          >
            <MessageCircle className="w-4 h-4 mr-2" />
            {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
          </Button>
        </CardContent>
      </Card>

      {/* Previous Feedback */}
      {feedback.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Previous Feedback</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {feedback.map((item) => (
                <div key={item.id} className="aone-spacing-sm bg-muted rounded-aone-lg">
                  <div className="aone-flex-between mb-2">
                    <Badge variant="outline">{item.section}</Badge>
                    <span className="text-xs text-gray-500">
                      {item.timestamp.toLocaleDateString()}
                    </span>
                  </div>
                  <p className="text-aone-sm">{item.content}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Approval Section */}
      <Card>
        <CardHeader>
          <CardTitle>Final Approval</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="aone-label-enterprise mb-2 block">
              Additional Comments (Optional):
            </label>
            <Textarea
              value={approvalComments}
              onChange={(e) => setApprovalComments(e.target.value)}
              placeholder="Any final comments or requirements..."
              className="min-h-[80px]"
            />
          </div>

          <div className="flex gap-aone-md">
            <Button
              onClick={() => handleApproval(true)}
              className="flex-1 bg-green-600 hover:bg-green-700"
              disabled={approvalStatus === 'approved'}
            >
              <ThumbsUp className="w-4 h-4 mr-2" />
              Approve Design
            </Button>
            
            <Button
              onClick={() => handleApproval(false)}
              variant="outline"
              className="flex-1 border-red-300 text-red-600 hover:bg-red-50"
              disabled={approvalStatus === 'rejected'}
            >
              <ThumbsDown className="w-4 h-4 mr-2" />
              Request Revisions
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-center gap-aone-md">
        <Button variant="outline">
          <Download className="w-4 h-4 mr-2" />
          Download Report
        </Button>
        
        <Button variant="outline">
          <Share2 className="w-4 h-4 mr-2" />
          Share with Team
        </Button>
      </div>
    </div>
  );
};

export default ClientReviewMode;
