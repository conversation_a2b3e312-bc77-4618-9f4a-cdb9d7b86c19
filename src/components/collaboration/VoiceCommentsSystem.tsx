import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Mic, 
  MicOff, 
  Play, 
  Pause, 
  Square, 
  Volume2, 
  VolumeX,
  Clock,
  Wifi,
  WifiOff
} from 'lucide-react';
import { webrtcService, VoiceCommentData } from '@/services/webrtcService';
import { useCollaboration } from '@/hooks/useCollaboration';

interface VoiceComment {
  id: string;
  userId: string;
  userName: string;
  audioBlob: Blob;
  duration: number;
  timestamp: number;
  isPlaying: boolean;
  latency?: number;
}

interface VoiceCommentsSystemProps {
  projectId: string;
  analysisId?: string;
  enabled?: boolean;
  maxRecordingDuration?: number;
  className?: string;
}

/**
 * Voice Comments System with WebRTC Low-Latency Audio
 * 
 * Implements Phase 1 of Priority 3 Feature 2 Advanced Collaboration Tools
 * Provides <200ms voice transmission via WebRTC audio streaming
 * Integrates with existing comment system and authentication
 */
const VoiceCommentsSystem: React.FC<VoiceCommentsSystemProps> = ({
  projectId,
  analysisId,
  enabled = true,
  maxRecordingDuration = 60000, // 60 seconds
  className = ''
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [voiceComments, setVoiceComments] = useState<VoiceComment[]>([]);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [isWebRTCEnabled, setIsWebRTCEnabled] = useState(false);
  const [audioPermission, setAudioPermission] = useState<'granted' | 'denied' | 'prompt'>('prompt');
  const [averageLatency, setAverageLatency] = useState(0);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  
  const { socket, isConnected } = useCollaboration();

  /**
   * Initialize audio permissions and WebRTC
   */
  useEffect(() => {
    if (!enabled) return;

    const initializeAudio = async () => {
      try {
        // Check audio permissions
        const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
        setAudioPermission(permission.state);
        
        permission.addEventListener('change', () => {
          setAudioPermission(permission.state);
        });

        // Initialize WebRTC if permissions granted
        if (permission.state === 'granted' && socket) {
          await webrtcService.initialize(socket, socket.id || 'anonymous');
          setIsWebRTCEnabled(true);
        }
      } catch (error) {
        console.warn('Failed to initialize voice comments:', error);
        setAudioPermission('denied');
      }
    };

    initializeAudio();
  }, [enabled, socket]);

  /**
   * Setup voice comment listeners
   */
  useEffect(() => {
    if (!enabled) return;

    // WebRTC voice comment updates
    const unsubscribeWebRTC = webrtcService.onVoiceComment((data: VoiceCommentData) => {
      handleIncomingVoiceComment(data);
    });

    // WebSocket voice comment updates (fallback)
    const handleWebSocketVoice = (data: any) => {
      handleIncomingVoiceComment(data);
    };

    if (socket) {
      socket.on('voice-comment', handleWebSocketVoice);
    }

    return () => {
      unsubscribeWebRTC();
      if (socket) {
        socket.off('voice-comment', handleWebSocketVoice);
      }
    };
  }, [enabled, socket]);

  /**
   * Monitor latency
   */
  useEffect(() => {
    if (!enabled) return;

    const updateLatency = () => {
      const latency = webrtcService.getAverageLatency();
      setAverageLatency(latency);
    };

    const interval = setInterval(updateLatency, 1000);
    return () => clearInterval(interval);
  }, [enabled]);

  /**
   * Handle incoming voice comment
   */
  const handleIncomingVoiceComment = useCallback((data: VoiceCommentData) => {
    const { userId, commentId, audioBlob, duration, timestamp } = data;
    
    if (userId === socket?.id) return; // Don't show own comments

    const voiceComment: VoiceComment = {
      id: commentId,
      userId,
      userName: `User ${userId.slice(-4)}`,
      audioBlob,
      duration,
      timestamp,
      isPlaying: false,
      latency: Date.now() - timestamp
    };

    setVoiceComments(prev => [voiceComment, ...prev].slice(0, 20)); // Keep last 20 comments
  }, [socket?.id]);

  /**
   * Request microphone permission
   */
  const requestMicrophonePermission = async (): Promise<boolean> => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop()); // Stop immediately after permission
      setAudioPermission('granted');
      return true;
    } catch (error) {
      console.error('Microphone permission denied:', error);
      setAudioPermission('denied');
      return false;
    }
  };

  /**
   * Start voice recording
   */
  const startRecording = async () => {
    if (audioPermission !== 'granted') {
      const granted = await requestMicrophonePermission();
      if (!granted) return;
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100
        }
      });

      // Initialize audio context for better quality
      audioContextRef.current = new AudioContext();
      
      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      audioChunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm;codecs=opus' });
        handleRecordingComplete(audioBlob);
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorderRef.current.start(100); // Collect data every 100ms
      setIsRecording(true);
      setRecordingDuration(0);

      // Start recording timer
      recordingTimerRef.current = setInterval(() => {
        setRecordingDuration(prev => {
          const newDuration = prev + 100;
          if (newDuration >= maxRecordingDuration) {
            stopRecording();
          }
          return newDuration;
        });
      }, 100);

    } catch (error) {
      console.error('Failed to start recording:', error);
      setAudioPermission('denied');
    }
  };

  /**
   * Stop voice recording
   */
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }
    }
  };

  /**
   * Handle recording completion
   */
  const handleRecordingComplete = async (audioBlob: Blob) => {
    if (audioBlob.size === 0) return;

    const commentId = `voice-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // Send via WebRTC for low latency
      await webrtcService.sendVoiceComment(audioBlob, commentId);
      
      // Add to local comments for immediate feedback
      const localComment: VoiceComment = {
        id: commentId,
        userId: socket?.id || 'local',
        userName: 'You',
        audioBlob,
        duration: recordingDuration,
        timestamp: Date.now(),
        isPlaying: false
      };
      
      setVoiceComments(prev => [localComment, ...prev]);
      
    } catch (error) {
      console.error('Failed to send voice comment:', error);
    }
    
    setRecordingDuration(0);
  };

  /**
   * Play voice comment
   */
  const playVoiceComment = async (comment: VoiceComment) => {
    try {
      const audio = new Audio();
      audio.src = URL.createObjectURL(comment.audioBlob);
      
      // Update playing state
      setVoiceComments(prev => 
        prev.map(c => 
          c.id === comment.id 
            ? { ...c, isPlaying: true }
            : { ...c, isPlaying: false }
        )
      );

      audio.onended = () => {
        setVoiceComments(prev => 
          prev.map(c => 
            c.id === comment.id 
              ? { ...c, isPlaying: false }
              : c
          )
        );
        URL.revokeObjectURL(audio.src);
      };

      await audio.play();
    } catch (error) {
      console.error('Failed to play voice comment:', error);
    }
  };

  /**
   * Stop playing voice comment
   */
  const stopPlaying = (comment: VoiceComment) => {
    setVoiceComments(prev => 
      prev.map(c => 
        c.id === comment.id 
          ? { ...c, isPlaying: false }
          : c
      )
    );
  };

  /**
   * Format duration
   */
  const formatDuration = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    return `${Math.floor(seconds / 60)}:${(seconds % 60).toString().padStart(2, '0')}`;
  };

  if (!enabled) return null;

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <CardTitle className="aone-flex-between">
          <div className="flex items-center space-x-2">
            <Volume2 className="h-5 w-5" />
            <span>Voice Comments</span>
          </div>
          
          <div className="flex items-center space-x-2">
            {isWebRTCEnabled ? (
              <Badge variant="outline" className="text-green-600">
                <Wifi className="h-3 w-3 mr-1" />
                WebRTC
              </Badge>
            ) : (
              <Badge variant="outline" className="text-yellow-600">
                <WifiOff className="h-3 w-3 mr-1" />
                WebSocket
              </Badge>
            )}
            
            {averageLatency > 0 && (
              <Badge variant="outline">
                {Math.round(averageLatency)}ms
              </Badge>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Recording Controls */}
        <div className="flex items-center space-x-4">
          {audioPermission === 'denied' ? (
            <Alert>
              <AlertDescription>
                Microphone access is required for voice comments. Please enable microphone permissions.
              </AlertDescription>
            </Alert>
          ) : (
            <>
              <Button
                variant={isRecording ? "destructive" : "default"}
                size="sm"
                onClick={isRecording ? stopRecording : startRecording}
                disabled={!isConnected}
                className="flex items-center space-x-2"
              >
                {isRecording ? (
                  <>
                    <Square className="h-4 w-4" />
                    <span>Stop</span>
                  </>
                ) : (
                  <>
                    <Mic className="h-4 w-4" />
                    <span>Record</span>
                  </>
                )}
              </Button>
              
              {isRecording && (
                <div className="flex items-center space-x-2 text-aone-sm text-red-600">
                  <div className="w-2 h-2 bg-red-600 rounded-full animate-pulse" />
                  <span>{formatDuration(recordingDuration)}</span>
                  <span className="text-gray-500">
                    / {formatDuration(maxRecordingDuration)}
                  </span>
                </div>
              )}
            </>
          )}
        </div>

        {/* Voice Comments List */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {voiceComments.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              No voice comments yet. Start a conversation!
            </div>
          ) : (
            voiceComments.map((comment) => (
              <div
                key={comment.id}
                className="flex items-center space-x-3 aone-spacing-sm bg-muted dark:bg-gray-800 rounded-aone-lg"
              >
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => 
                    comment.isPlaying 
                      ? stopPlaying(comment)
                      : playVoiceComment(comment)
                  }
                  className="flex-shrink-0"
                >
                  {comment.isPlaying ? (
                    <Pause className="h-4 w-4" />
                  ) : (
                    <Play className="h-4 w-4" />
                  )}
                </Button>
                
                <div className="flex-1 min-w-0">
                  <div className="aone-flex-between">
                    <span className="text-aone-sm font-aone-medium text-foreground dark:text-gray-100">
                      {comment.userName}
                    </span>
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <Clock className="h-3 w-3" />
                      <span>{formatDuration(comment.duration)}</span>
                      {comment.latency && (
                        <Badge variant="outline" className="text-xs">
                          {Math.round(comment.latency)}ms
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <div className="text-xs text-gray-500 mt-1">
                    {new Date(comment.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default VoiceCommentsSystem;
