
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Upload, Zap, Target, TrendingUp, ArrowR<PERSON>, Sparkles } from "lucide-react";
import { <PERSON> } from "react-router-dom";

const HeroSection = () => {
  return (
    <div className="aone-hero-section relative overflow-hidden bg-gradient-to-br from-aone-warm-white via-aone-cream/30 to-aone-sage/5 py-24 lg:py-32">
      {/* Phase 1 Visual Experience System - Enhanced Background Elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 aone-hero-pattern"></div>
      </div>

      {/* Sophisticated Glass Orbs */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-aone-sage/15 via-aone-sage/8 to-transparent rounded-full blur-3xl animate-float-gentle"></div>
      <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-aone-sage/10 via-aone-sage/5 to-transparent rounded-full blur-3xl animate-float-gentle animate-delay-1-5"></div>

      {/* Additional Cinematic Elements */}
      <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-br from-green-400/5 to-aone-sage/5 rounded-full blur-2xl animate-pulse-elegant"></div>
      <div className="absolute bottom-1/3 right-1/3 w-48 h-48 bg-gradient-to-tl from-aone-sage/8 to-green-500/5 rounded-full blur-2xl animate-pulse-elegant animate-delay-3"></div>

      <div className="relative aone-container lg:px-8">
        <div className="text-center max-w-5xl mx-auto">
          {/* Phase 1 Visual Experience System - Enhanced Typography Hierarchy */}
          <div className="mb-8 animate-fade-in-elegant">
            <div className="inline-flex items-center px-6 py-aone-sm rounded-full aone-glass border border-aone-sage/20 text-aone-sage font-aone-medium text-aone-sm mb-8 aone-micro-interaction">
              <Sparkles className="w-4 h-4 mr-2 animate-pulse-elegant" />
              Enterprise-Grade AI Kitchen Analysis
            </div>

            <h1 className="aone-heading-enterprise text-6xl lg:text-7xl mb-8 leading-[1.1] animate-slide-in-elegant">
              Transform Kitchen Drawings into
              <span className="block mt-2 bg-gradient-to-r from-aone-sage via-green-600 to-aone-sage bg-clip-text text-transparent font-light">
                AI-Powered Insights
              </span>
            </h1>
          </div>

          <p className="text-aone-xl lg:text-2xl text-aone-charcoal/70 mb-12 leading-relaxed max-w-4xl mx-auto font-light">
            Revolutionize your kitchen design workflow with Blackveil Design Mind's cutting-edge AI that analyzes
            <span className="font-aone-medium text-aone-sage"> Winner Flex and Cabinet Vision</span> drawings,
            delivering precise measurements, accurate quotations, and optimized design solutions in seconds.
          </p>

          {/* Enhanced Call-to-Action Section */}
          <div className="flex flex-col sm:flex-row gap-aone-lg justify-center mb-16">
            <Link to="/analysis" className="group">
              <Button
                size="lg"
                className="bg-aone-sage hover:bg-aone-sage/90 text-white font-aone-semibold text-aone-lg px-10 py-6 rounded-aone-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-0"
              >
                <Upload className="w-6 h-6 mr-3 group-hover:scale-110 transition-transform duration-200" />
                Start Free Analysis
                <ArrowRight className="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform duration-200" />
              </Button>
            </Link>
            <Button
              variant="outline"
              size="lg"
              className="bg-white/80 backdrop-blur-sm hover:bg-white text-aone-sage border-2 border-aone-sage/30 hover:border-aone-sage font-aone-semibold text-aone-lg px-10 py-6 rounded-aone-xl shadow-md aone-hover-lift transition-all duration-300"
            >
              Watch Demo
            </Button>
          </div>

          {/* Phase 1 Visual Experience System - Enhanced Feature Cards */}
          <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
            <div className="aone-card-enterprise group animate-fade-in-elegant animate-delay-1">
              <div className="absolute inset-0 bg-gradient-to-br from-aone-sage/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative p-aone-xl lg:p-10">
                <div className="aone-feature-icon mx-auto mb-8">
                  <Zap className="w-8 h-8 text-aone-sage" />
                </div>
                <h3 className="aone-heading-enterprise text-2xl mb-aone-md group-hover:text-aone-sage transition-colors duration-300">AI-Powered Analysis</h3>
                <p className="aone-body-enterprise text-aone-lg">Advanced GPT-4o engine trained specifically on kitchen design drawings for unmatched accuracy and precision.</p>
              </div>
            </div>

            <div className="aone-card-enterprise group animate-fade-in-elegant animate-delay-2">
              <div className="absolute inset-0 bg-gradient-to-br from-aone-sage/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative p-aone-xl lg:p-10">
                <div className="aone-feature-icon mx-auto mb-8">
                  <Target className="w-8 h-8 text-aone-sage" />
                </div>
                <h3 className="aone-heading-enterprise text-2xl mb-aone-md group-hover:text-aone-sage transition-colors duration-300">Precise Measurements</h3>
                <p className="aone-body-enterprise text-aone-lg">Extract exact dimensions, cabinet specifications, and hardware requirements with confidence and reliability.</p>
              </div>
            </div>

            <div className="aone-card-enterprise group animate-fade-in-elegant animate-delay-3">
              <div className="absolute inset-0 bg-gradient-to-br from-aone-sage/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative p-aone-xl lg:p-10">
                <div className="aone-feature-icon mx-auto mb-8">
                  <TrendingUp className="w-8 h-8 text-aone-sage" />
                </div>
                <h3 className="aone-heading-enterprise text-2xl mb-aone-md group-hover:text-aone-sage transition-colors duration-300">Instant Quotations</h3>
                <p className="aone-body-enterprise text-aone-lg">Generate professional quotes with real-time pricing and detailed cost breakdowns instantly.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
