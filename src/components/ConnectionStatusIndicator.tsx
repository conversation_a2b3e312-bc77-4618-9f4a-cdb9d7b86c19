import React from 'react';
import { ConnectionStatus } from '../hooks/useRealtimeDashboard';

interface ConnectionStatusIndicatorProps {
  status: ConnectionStatus;
  reconnectAttempts?: number;
  maxReconnectAttempts?: number;
  lastUpdate?: string | null;
  onReconnect?: () => void;
  className?: string;
}

export const ConnectionStatusIndicator: React.FC<ConnectionStatusIndicatorProps> = ({
  status,
  reconnectAttempts = 0,
  maxReconnectAttempts = 3,
  lastUpdate,
  onReconnect,
  className = ''
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'connected':
        return {
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          borderColor: 'border-green-200',
          icon: '●',
          text: 'Connected',
          description: 'Real-time updates active'
        };
      case 'reconnecting':
        return {
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
          borderColor: 'border-yellow-200',
          icon: '◐',
          text: 'Reconnecting',
          description: `Attempt ${reconnectAttempts}/${maxReconnectAttempts}`
        };
      case 'disconnected':
        return {
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          borderColor: 'border-red-200',
          icon: '○',
          text: 'Disconnected',
          description: 'Real-time updates unavailable'
        };
      default:
        return {
          color: 'text-muted-foreground',
          bgColor: 'bg-muted',
          borderColor: 'border-border',
          icon: '?',
          text: 'Unknown',
          description: 'Connection status unknown'
        };
    }
  };

  const config = getStatusConfig();
  
  const formatLastUpdate = (timestamp: string | null) => {
    if (!timestamp) return 'Never';
    
    const now = new Date();
    const updateTime = new Date(timestamp);
    const diffMs = now.getTime() - updateTime.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    
    if (diffSeconds < 60) {
      return `${diffSeconds}s ago`;
    } else if (diffSeconds < 3600) {
      return `${Math.floor(diffSeconds / 60)}m ago`;
    } else {
      return `${Math.floor(diffSeconds / 3600)}h ago`;
    }
  };

  return (
    <div className={`inline-flex items-center space-x-2 px-3 py-2 rounded-aone-lg border ${config.bgColor} ${config.borderColor} ${className}`}>
      {/* Status Icon with Animation */}
      <span 
        className={`text-aone-lg ${config.color} ${status === 'reconnecting' ? 'animate-spin' : ''}`}
        style={{ 
          animation: status === 'reconnecting' ? 'pulse 1.5s ease-in-out infinite' : undefined 
        }}
      >
        {config.icon}
      </span>
      
      {/* Status Text */}
      <div className="flex flex-col">
        <span className={`text-aone-sm font-aone-medium ${config.color}`}>
          {config.text}
        </span>
        <span className="text-xs text-gray-500">
          {config.description}
        </span>
      </div>
      
      {/* Last Update Time */}
      {status === 'connected' && lastUpdate && (
        <div className="text-xs text-gray-400 border-l border-border pl-2">
          Updated {formatLastUpdate(lastUpdate)}
        </div>
      )}
      
      {/* Reconnect Button */}
      {status === 'disconnected' && onReconnect && (
        <button
          onClick={onReconnect}
          className="text-xs px-2 py-1 bg-blue-500 text-white rounded aone-micro-interaction hover:bg-blue-600 transition-colors"
          title="Manually reconnect to real-time updates"
        >
          Reconnect
        </button>
      )}
      
      {/* Reconnection Progress */}
      {status === 'reconnecting' && (
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-yellow-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
          <div className="w-2 h-2 bg-yellow-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
          <div className="w-2 h-2 bg-yellow-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
        </div>
      )}
    </div>
  );
};

// Compact version for smaller spaces
export const CompactConnectionStatus: React.FC<{
  status: ConnectionStatus;
  className?: string;
}> = ({ status, className = '' }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'connected':
        return { color: 'text-green-500', icon: '●', title: 'Connected - Real-time updates active' };
      case 'reconnecting':
        return { color: 'text-yellow-500', icon: '◐', title: 'Reconnecting to real-time updates' };
      case 'disconnected':
        return { color: 'text-red-500', icon: '○', title: 'Disconnected - Real-time updates unavailable' };
      default:
        return { color: 'text-gray-500', icon: '?', title: 'Connection status unknown' };
    }
  };

  const config = getStatusConfig();

  return (
    <span 
      className={`inline-flex items-center ${config.color} ${className}`}
      title={config.title}
    >
      <span 
        className={`text-aone-sm ${status === 'reconnecting' ? 'animate-pulse' : ''}`}
      >
        {config.icon}
      </span>
    </span>
  );
};

// Status badge for dashboard headers
export const DashboardStatusBadge: React.FC<{
  status: ConnectionStatus;
  lastUpdate?: string | null;
  className?: string;
}> = ({ status, lastUpdate, className = '' }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'connected':
        return {
          bgColor: 'bg-green-500',
          text: 'LIVE',
          pulse: false
        };
      case 'reconnecting':
        return {
          bgColor: 'bg-yellow-500',
          text: 'CONNECTING',
          pulse: true
        };
      case 'disconnected':
        return {
          bgColor: 'bg-red-500',
          text: 'OFFLINE',
          pulse: false
        };
      default:
        return {
          bgColor: 'bg-gray-500',
          text: 'UNKNOWN',
          pulse: false
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className={`inline-flex items-center space-x-2 ${className}`}>
      <div 
        className={`px-2 py-1 rounded text-xs font-bold text-white ${config.bgColor} ${
          config.pulse ? 'animate-pulse' : ''
        }`}
      >
        {config.text}
      </div>
      {status === 'connected' && lastUpdate && (
        <span className="text-xs text-gray-500">
          {new Date(lastUpdate).toLocaleTimeString()}
        </span>
      )}
    </div>
  );
};

export default ConnectionStatusIndicator;
