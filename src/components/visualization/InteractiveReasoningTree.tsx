import React, { useState, useEffect, useCallback, useMemo } from 'react';
import React<PERSON><PERSON>, {
  Node,
  Edge,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  ConnectionMode,
  Panel,
  MiniMap,
  NodeTypes,
  EdgeTypes
} from 'reactflow';
import 'reactflow/dist/style.css';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  Target, 
  Lightbulb, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Zap,
  Eye,
  EyeOff,
  Play,
  Pause,
  SkipForward,
  RotateCcw,
  Maximize2,
  Settings
} from 'lucide-react';
import { io, Socket } from 'socket.io-client';

// Types for reasoning data
interface ReasoningStep {
  id: string;
  type: 'observation' | 'analysis' | 'inference' | 'validation' | 'conclusion';
  description: string;
  confidence: number;
  dependencies: string[];
  gptO1Reasoning?: {
    internalThoughts: string[];
    reasoningPath: string[];
    confidenceFactors: string[];
    alternativesConsidered: string[];
    complexityScore: number;
  };
  visualizationData?: {
    position: { x: number; y: number };
    status: 'pending' | 'processing' | 'completed' | 'failed';
    processingTime?: number;
    complexity: 'low' | 'medium' | 'high';
    parentSteps: string[];
    childSteps: string[];
  };
  timestamp: Date;
}

interface ReasoningChain {
  id: string;
  analysisId: string;
  goal: string;
  status: 'in_progress' | 'completed' | 'failed';
  currentStep: number;
  steps: ReasoningStep[];
  metadata: {
    startTime: Date;
    endTime?: Date;
    totalSteps: number;
    completedSteps: number;
  };
}

interface InteractiveReasoningTreeProps {
  analysisId: string;
  chainId?: string;
  onStepClick?: (step: ReasoningStep) => void;
  onStepHover?: (step: ReasoningStep | null) => void;
  className?: string;
  height?: number;
  enablePlayback?: boolean;
  enableMiniMap?: boolean;
}

// Custom Node Components
const ReasoningStepNode: React.FC<{ data: any }> = ({ data }) => {
  const { step, isSelected, isHighlighted, onSelect } = data;
  
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'observation': return <Eye className="w-4 h-4" />;
      case 'analysis': return <Brain className="w-4 h-4" />;
      case 'inference': return <Lightbulb className="w-4 h-4" />;
      case 'validation': return <CheckCircle className="w-4 h-4" />;
      case 'conclusion': return <Target className="w-4 h-4" />;
      default: return <Brain className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 border-green-300 text-green-800';
      case 'processing': return 'bg-blue-100 border-blue-300 text-blue-800 animate-pulse';
      case 'failed': return 'bg-red-100 border-red-300 text-red-800';
      default: return 'bg-muted border-border text-muted-foreground';
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div
      className={`
        relative aone-spacing-sm rounded-aone-lg border-2 cursor-pointer aone-micro-interaction min-w-[200px] max-w-[250px]
        ${isSelected ? 'ring-2 ring-aone-sage ring-offset-2' : ''}
        ${isHighlighted ? 'shadow-lg scale-105' : 'shadow-md'}
        ${getStatusColor(step.visualizationData?.status || 'pending')}
        aone-hover-lift aone-hover-lift
      `}
      onClick={() => onSelect(step)}
    >
      {/* Header */}
      <div className="aone-flex-between mb-2">
        <div className="flex items-center gap-2">
          {getTypeIcon(step.type)}
          <Badge variant="outline" className="text-xs capitalize">
            {step.type}
          </Badge>
        </div>
        
        {step.gptO1Reasoning && (
          <Badge className="bg-status-info/10 text-status-info border-status-info/20">
            <Zap className="w-3 h-3 mr-1" />
            GPT-o1
          </Badge>
        )}
      </div>

      {/* Description */}
      <p className="text-aone-sm font-aone-medium mb-2 line-clamp-2">
        {step.description}
      </p>

      {/* Confidence Bar */}
      <div className="mb-2">
        <div className="flex justify-between items-center mb-1">
          <span className="text-xs text-muted-foreground">Confidence</span>
          <span className="text-xs font-aone-medium">{Math.round(step.confidence * 100)}%</span>
        </div>
        <Progress value={step.confidence * 100} className="h-1" />
      </div>

      {/* Complexity Indicator */}
      {step.visualizationData?.complexity && (
        <div className="flex items-center gap-2 mb-2">
          <div className={`w-2 h-2 rounded-full ${getComplexityColor(step.visualizationData.complexity)}`} />
          <span className="text-xs text-muted-foreground capitalize">
            {step.visualizationData.complexity} complexity
          </span>
        </div>
      )}

      {/* Processing Time */}
      {step.visualizationData?.processingTime && (
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <Clock className="w-3 h-3" />
          {step.visualizationData.processingTime}ms
        </div>
      )}

      {/* Dependencies indicator */}
      {step.dependencies.length > 0 && (
        <div className="absolute -top-1 -left-1 w-3 h-3 bg-aone-sage rounded-full aone-flex-center">
          <span className="text-xs text-white font-bold">{step.dependencies.length}</span>
        </div>
      )}
    </div>
  );
};

// Custom Edge Component
const ReasoningEdge: React.FC<{ data: any }> = ({ data }) => {
  return (
    <div className="react-flow__edge-path">
      <path
        d={data.path}
        stroke="#6B7A4F"
        strokeWidth={2}
        fill="none"
        markerEnd="url(#arrowhead)"
        className="animate-pulse"
      />
    </div>
  );
};

const nodeTypes: NodeTypes = {
  reasoningStep: ReasoningStepNode,
};

const edgeTypes: EdgeTypes = {
  reasoning: ReasoningEdge,
};

/**
 * Interactive Reasoning Tree Component
 */
const InteractiveReasoningTree: React.FC<InteractiveReasoningTreeProps> = ({
  analysisId,
  chainId,
  onStepClick,
  onStepHover,
  className = '',
  height = 600,
  enablePlayback = true,
  enableMiniMap = true
}) => {
  const [chains, setChains] = useState<ReasoningChain[]>([]);
  const [selectedChain, setSelectedChain] = useState<ReasoningChain | null>(null);
  const [selectedStep, setSelectedStep] = useState<ReasoningStep | null>(null);
  const [highlightedStep, setHighlightedStep] = useState<ReasoningStep | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const [currentPlaybackStep, setCurrentPlaybackStep] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  // Load reasoning chains
  useEffect(() => {
    const loadReasoningChains = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/reasoning/chains/analysis/${analysisId}`);
        const data = await response.json();

        if (data.success) {
          setChains(data.data.chains);
          
          // Select specific chain or first available
          const targetChain = chainId 
            ? data.data.chains.find((c: ReasoningChain) => c.id === chainId)
            : data.data.chains[0];
            
          if (targetChain) {
            setSelectedChain(targetChain);
          }
        } else {
          setError(data.error || 'Failed to load reasoning chains');
        }
      } catch (err) {
        setError('Failed to connect to reasoning service');
        console.error('Error loading reasoning chains:', err);
      } finally {
        setLoading(false);
      }
    };

    loadReasoningChains();
  }, [analysisId, chainId]);

  // Convert reasoning chain to ReactFlow nodes and edges
  const { flowNodes, flowEdges } = useMemo(() => {
    if (!selectedChain) return { flowNodes: [], flowEdges: [] };

    const steps = selectedChain.steps;
    const flowNodes: Node[] = [];
    const flowEdges: Edge[] = [];

    // Create nodes for each step
    steps.forEach((step, index) => {
      const position = step.visualizationData?.position || {
        x: (index % 4) * 300,
        y: Math.floor(index / 4) * 200
      };

      flowNodes.push({
        id: step.id,
        type: 'reasoningStep',
        position,
        data: {
          step,
          isSelected: selectedStep?.id === step.id,
          isHighlighted: highlightedStep?.id === step.id,
          onSelect: handleStepSelect
        },
        draggable: true
      });
    });

    // Create edges based on dependencies
    steps.forEach(step => {
      step.dependencies.forEach(depId => {
        flowEdges.push({
          id: `${depId}-${step.id}`,
          source: depId,
          target: step.id,
          type: 'reasoning',
          animated: step.visualizationData?.status === 'processing',
          style: { stroke: '#6B7A4F', strokeWidth: 2 }
        });
      });
    });

    return { flowNodes, flowEdges };
  }, [selectedChain, selectedStep, highlightedStep]);

  // Update ReactFlow nodes and edges
  useEffect(() => {
    setNodes(flowNodes);
    setEdges(flowEdges);
  }, [flowNodes, flowEdges, setNodes, setEdges]);

  // Handle step selection
  const handleStepSelect = useCallback((step: ReasoningStep) => {
    setSelectedStep(step);
    if (onStepClick) {
      onStepClick(step);
    }
  }, [onStepClick]);

  // Handle step hover
  const handleStepHover = useCallback((step: ReasoningStep | null) => {
    setHighlightedStep(step);
    if (onStepHover) {
      onStepHover(step);
    }
  }, [onStepHover]);

  // Playback controls
  const startPlayback = useCallback(() => {
    setIsPlaying(true);
    setCurrentPlaybackStep(0);
  }, []);

  const stopPlayback = useCallback(() => {
    setIsPlaying(false);
  }, []);

  const resetPlayback = useCallback(() => {
    setIsPlaying(false);
    setCurrentPlaybackStep(0);
    setSelectedStep(null);
  }, []);

  // Playback animation
  useEffect(() => {
    if (!isPlaying || !selectedChain) return;

    const interval = setInterval(() => {
      setCurrentPlaybackStep(prev => {
        const nextStep = prev + 1;
        if (nextStep >= selectedChain.steps.length) {
          setIsPlaying(false);
          return prev;
        }
        
        // Highlight current step
        const step = selectedChain.steps[nextStep];
        setSelectedStep(step);
        
        return nextStep;
      });
    }, 2000 / playbackSpeed);

    return () => clearInterval(interval);
  }, [isPlaying, selectedChain, playbackSpeed]);

  // Auto-layout for better visualization
  const autoLayout = useCallback(() => {
    if (!selectedChain) return;

    // Implement hierarchical layout algorithm
    const steps = selectedChain.steps;
    const levels: { [key: number]: ReasoningStep[] } = {};
    
    // Calculate levels based on dependencies
    const calculateLevel = (step: ReasoningStep, visited = new Set()): number => {
      if (visited.has(step.id)) return 0; // Prevent cycles
      visited.add(step.id);
      
      if (step.dependencies.length === 0) return 0;
      
      const depLevels = step.dependencies.map(depId => {
        const depStep = steps.find(s => s.id === depId);
        return depStep ? calculateLevel(depStep, visited) : 0;
      });
      
      return Math.max(...depLevels) + 1;
    };

    steps.forEach(step => {
      const level = calculateLevel(step);
      if (!levels[level]) levels[level] = [];
      levels[level].push(step);
    });

    // Position nodes based on levels
    const updatedNodes = flowNodes.map(node => {
      const step = steps.find(s => s.id === node.id);
      if (!step) return node;

      const level = calculateLevel(step);
      const levelSteps = levels[level];
      const indexInLevel = levelSteps.findIndex(s => s.id === step.id);

      return {
        ...node,
        position: {
          x: indexInLevel * 300 - (levelSteps.length - 1) * 150,
          y: level * 200
        }
      };
    });

    setNodes(updatedNodes);
  }, [selectedChain, flowNodes, setNodes]);

  if (loading) {
    return (
      <Card className={`h-[${height}px] ${className}`}>
        <CardContent className="aone-flex-center h-full">
          <div className="text-center">
            <div className="aone-loading-spinner w-8 h-8 mx-auto mb-2"></div>
            <p className="text-aone-sm text-aone-sage">Loading reasoning chains...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`h-[${height}px] ${className}`}>
        <CardContent className="aone-flex-center h-full">
          <div className="text-center">
            <AlertCircle className="w-8 h-8 text-red-500 mx-auto mb-2" />
            <p className="text-aone-sm text-red-600">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Chain Selection */}
      {chains.length > 1 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="w-5 h-5" />
              Reasoning Chains
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2 flex-wrap">
              {chains.map(chain => (
                <Button
                  key={chain.id}
                  variant={selectedChain?.id === chain.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedChain(chain)}
                  className="flex items-center gap-2"
                >
                  <Badge variant={chain.status === 'completed' ? 'default' : 'secondary'}>
                    {chain.status}
                  </Badge>
                  Chain {chain.id.split('_').pop()}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Visualization */}
      <Card className={`h-[${height}px]`}>
        <CardHeader>
          <div className="aone-flex-between">
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              {selectedChain?.goal || 'Interactive Reasoning Tree'}
            </CardTitle>
            
            {/* Controls */}
            {enablePlayback && selectedChain && (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={isPlaying ? stopPlayback : startPlayback}
                >
                  {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                </Button>
                <Button variant="outline" size="sm" onClick={resetPlayback}>
                  <RotateCcw className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={autoLayout}>
                  <Settings className="w-4 h-4" />
                  Auto Layout
                </Button>
              </div>
            )}
          </div>
          
          {selectedChain && (
            <div className="flex items-center gap-aone-md">
              <Progress 
                value={(selectedChain.metadata.completedSteps / selectedChain.metadata.totalSteps) * 100} 
                className="flex-1" 
              />
              <span className="text-aone-sm text-muted-foreground">
                {selectedChain.metadata.completedSteps} of {selectedChain.metadata.totalSteps} steps
              </span>
            </div>
          )}
        </CardHeader>
        
        <CardContent className="p-0 h-[calc(100%-120px)]">
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            nodeTypes={nodeTypes}
            edgeTypes={edgeTypes}
            connectionMode={ConnectionMode.Loose}
            fitView
            attributionPosition="bottom-left"
            className="bg-gradient-to-br from-aone-warm-white to-aone-sage/5"
          >
            <Background color="#6B7A4F" gap={20} size={1} />
            <Controls />
            {enableMiniMap && (
              <MiniMap 
                nodeColor="#6B7A4F"
                maskColor="rgba(107, 122, 79, 0.1)"
                position="top-right"
              />
            )}
            
            {/* Custom Panel for Step Details */}
            {selectedStep && (
              <Panel position="bottom-right" className="bg-white aone-spacing-md rounded-aone-lg shadow-lg max-w-sm">
                <h4 className="font-aone-medium mb-2 flex items-center gap-2">
                  {selectedStep.type === 'observation' && <Eye className="w-4 h-4" />}
                  {selectedStep.type === 'analysis' && <Brain className="w-4 h-4" />}
                  {selectedStep.type === 'inference' && <Lightbulb className="w-4 h-4" />}
                  {selectedStep.type === 'validation' && <CheckCircle className="w-4 h-4" />}
                  {selectedStep.type === 'conclusion' && <Target className="w-4 h-4" />}
                  Step Details
                </h4>
                <p className="text-aone-sm text-foreground mb-2">{selectedStep.description}</p>
                <div className="text-xs text-gray-500">
                  Confidence: {Math.round(selectedStep.confidence * 100)}%
                </div>
                {selectedStep.gptO1Reasoning && (
                  <Badge className="mt-2 bg-blue-100 text-blue-800">
                    <Zap className="w-3 h-3 mr-1" />
                    Enhanced with GPT-o1
                  </Badge>
                )}
              </Panel>
            )}
          </ReactFlow>
        </CardContent>
      </Card>
    </div>
  );
};

export default InteractiveReasoningTree;
