import React, { useState, useEffect, useCallback } from 'react';
import { Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import * as THREE from 'three';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { 
  Eye, 
  Sparkles, 
  Brain, 
  Settings, 
  Play, 
  Pause,
  Camera,
  Zap,
  TreePine,
  Layers,
  Monitor,
  Maximize2
} from 'lucide-react';
import { ReconstructionResult } from '@/services/aiAnalysisService';
import CinematicCabinetViewer from './CinematicCabinetViewer';
import AIProcessVisualization, { useAIProcessVisualization } from './AIProcessVisualization';
import InteractiveReasoningTree from './InteractiveReasoningTree';

interface VisualExperienceSystemProps {
  reconstruction?: ReconstructionResult;
  analysisId?: string;
  chainId?: string;
  className?: string;
  defaultMode?: 'cinematic' | 'particles' | 'reasoning' | 'integrated';
}

interface SystemSettings {
  cinematicMode: {
    enabled: boolean;
    preset: 'professional' | 'dramatic' | 'subtle';
    autoTransitions: boolean;
  };
  particleSystem: {
    enabled: boolean;
    intensity: 'low' | 'medium' | 'high';
    showDataFlow: boolean;
    showConfidenceIndicators: boolean;
  };
  reasoningTree: {
    enabled: boolean;
    enablePlayback: boolean;
    enableMiniMap: boolean;
    autoLayout: boolean;
  };
  integration: {
    syncAnimations: boolean;
    crossSystemInteractions: boolean;
    performanceMode: 'quality' | 'balanced' | 'performance';
  };
}

/**
 * Visual Experience System - Integrates all Phase 1 Foundation Enhancements
 */
const VisualExperienceSystem: React.FC<VisualExperienceSystemProps> = ({
  reconstruction,
  analysisId,
  chainId,
  className = '',
  defaultMode = 'integrated'
}) => {
  const [activeMode, setActiveMode] = useState(defaultMode);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [settings, setSettings] = useState<SystemSettings>({
    cinematicMode: {
      enabled: true,
      preset: 'professional',
      autoTransitions: true
    },
    particleSystem: {
      enabled: true,
      intensity: 'medium',
      showDataFlow: true,
      showConfidenceIndicators: true
    },
    reasoningTree: {
      enabled: true,
      enablePlayback: true,
      enableMiniMap: true,
      autoLayout: true
    },
    integration: {
      syncAnimations: true,
      crossSystemInteractions: true,
      performanceMode: 'balanced'
    }
  });

  // AI Process Visualization controls
  const aiVisualization = useAIProcessVisualization();

  // Update settings handler
  const updateSettings = useCallback((section: keyof SystemSettings, updates: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: { ...prev[section], ...updates }
    }));
  }, []);

  // Performance monitoring
  const [performanceMetrics, setPerformanceMetrics] = useState({
    fps: 60,
    particleCount: 0,
    memoryUsage: 0,
    renderTime: 0
  });

  // Monitor performance
  useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();
    
    const updateMetrics = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        setPerformanceMetrics(prev => ({
          ...prev,
          fps: Math.round(frameCount * 1000 / (currentTime - lastTime))
        }));
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(updateMetrics);
    };
    
    const animationId = requestAnimationFrame(updateMetrics);
    return () => cancelAnimationFrame(animationId);
  }, []);

  // Auto-adjust performance based on metrics
  useEffect(() => {
    if (settings.integration.performanceMode === 'balanced') {
      if (performanceMetrics.fps < 30) {
        // Reduce quality for better performance
        updateSettings('particleSystem', { intensity: 'low' });
        updateSettings('cinematicMode', { preset: 'subtle' });
      } else if (performanceMetrics.fps > 55) {
        // Increase quality if performance allows
        updateSettings('particleSystem', { intensity: 'medium' });
      }
    }
  }, [performanceMetrics.fps, settings.integration.performanceMode, updateSettings]);

  const renderCinematicView = () => (
    <div className="h-full">
      {reconstruction ? (
        <CinematicCabinetViewer
          reconstruction={reconstruction}
          enableCinematicMode={settings.cinematicMode.enabled}
          transitionPreset={settings.cinematicMode.preset}
          className="h-full"
        />
      ) : (
        <Card className="h-full aone-flex-center">
          <CardContent>
            <Camera className="w-12 h-12 text-aone-sage mx-auto mb-aone-md" />
            <p className="text-center text-muted-foreground">No 3D reconstruction data available</p>
          </CardContent>
        </Card>
      )}
    </div>
  );

  const renderParticleView = () => (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="w-5 h-5" />
          AI Process Visualization
          <Badge className="bg-aone-sage text-white">
            Live Particles
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="h-[calc(100%-80px)]">
        <div className="h-full bg-gradient-to-br from-aone-warm-white to-aone-sage/5 rounded-aone-lg relative">
          <Suspense fallback={
            <div className="aone-flex-center h-full">
              <div className="text-center">
                <div className="aone-loading-spinner w-8 h-8 mx-auto mb-2"></div>
                <p className="text-aone-sm text-aone-sage">Initializing particle system...</p>
              </div>
            </div>
          }>
            <Canvas
              camera={{ position: [0, 0, 10], fov: 60 }}
              style={{ height: '100%', width: '100%' }}
            >
              <AIProcessVisualization
                enabled={settings.particleSystem.enabled}
                intensity={settings.particleSystem.intensity}
                showDataFlow={settings.particleSystem.showDataFlow}
                showConfidenceIndicators={settings.particleSystem.showConfidenceIndicators}
              />
              <ambientLight intensity={0.5} />
              <pointLight position={[10, 10, 10]} />
            </Canvas>
          </Suspense>
          
          {/* Particle Controls Overlay */}
          <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-aone-lg aone-spacing-sm space-y-2">
            <div className="flex items-center gap-2">
              <Sparkles className="w-4 h-4" />
              <span className="text-aone-sm font-aone-medium">Particles: {performanceMetrics.particleCount}</span>
            </div>
            <div className="flex items-center gap-2">
              <Monitor className="w-4 h-4" />
              <span className="text-aone-sm">FPS: {performanceMetrics.fps}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const renderReasoningView = () => (
    <div className="h-full">
      {analysisId ? (
        <InteractiveReasoningTree
          analysisId={analysisId}
          chainId={chainId}
          enablePlayback={settings.reasoningTree.enablePlayback}
          enableMiniMap={settings.reasoningTree.enableMiniMap}
          height={600}
          className="h-full"
        />
      ) : (
        <Card className="h-full aone-flex-center">
          <CardContent>
            <Brain className="w-12 h-12 text-aone-sage mx-auto mb-aone-md" />
            <p className="text-center text-muted-foreground">No analysis data available for reasoning visualization</p>
          </CardContent>
        </Card>
      )}
    </div>
  );

  const renderIntegratedView = () => (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-aone-lg h-full">
      {/* Cinematic 3D View */}
      <div className="h-full">
        {renderCinematicView()}
      </div>
      
      {/* Split view: Particles + Reasoning */}
      <div className="space-y-6 h-full">
        <div className="h-1/2">
          {renderParticleView()}
        </div>
        <div className="h-1/2">
          {renderReasoningView()}
        </div>
      </div>
    </div>
  );

  const renderSettingsPanel = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="w-5 h-5" />
          Visual Experience Settings
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="cinematic" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="cinematic">Cinematic</TabsTrigger>
            <TabsTrigger value="particles">Particles</TabsTrigger>
            <TabsTrigger value="reasoning">Reasoning</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>
          
          <TabsContent value="cinematic" className="space-y-4">
            <div className="aone-flex-between">
              <label className="text-aone-sm font-aone-medium">Enable Cinematic Mode</label>
              <Switch
                checked={settings.cinematicMode.enabled}
                onCheckedChange={(checked) => updateSettings('cinematicMode', { enabled: checked })}
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-aone-sm font-aone-medium">Transition Preset</label>
              <div className="flex gap-2">
                {['professional', 'dramatic', 'subtle'].map(preset => (
                  <Button
                    key={preset}
                    variant={settings.cinematicMode.preset === preset ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => updateSettings('cinematicMode', { preset })}
                    className="capitalize"
                  >
                    {preset}
                  </Button>
                ))}
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="particles" className="space-y-4">
            <div className="aone-flex-between">
              <label className="text-aone-sm font-aone-medium">Enable Particle System</label>
              <Switch
                checked={settings.particleSystem.enabled}
                onCheckedChange={(checked) => updateSettings('particleSystem', { enabled: checked })}
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-aone-sm font-aone-medium">Intensity</label>
              <div className="flex gap-2">
                {['low', 'medium', 'high'].map(intensity => (
                  <Button
                    key={intensity}
                    variant={settings.particleSystem.intensity === intensity ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => updateSettings('particleSystem', { intensity })}
                    className="capitalize"
                  >
                    {intensity}
                  </Button>
                ))}
              </div>
            </div>
            
            <div className="aone-flex-between">
              <label className="text-aone-sm font-aone-medium">Show Data Flow</label>
              <Switch
                checked={settings.particleSystem.showDataFlow}
                onCheckedChange={(checked) => updateSettings('particleSystem', { showDataFlow: checked })}
              />
            </div>
            
            <div className="aone-flex-between">
              <label className="text-aone-sm font-aone-medium">Show Confidence Indicators</label>
              <Switch
                checked={settings.particleSystem.showConfidenceIndicators}
                onCheckedChange={(checked) => updateSettings('particleSystem', { showConfidenceIndicators: checked })}
              />
            </div>
          </TabsContent>
          
          <TabsContent value="reasoning" className="space-y-4">
            <div className="aone-flex-between">
              <label className="text-aone-sm font-aone-medium">Enable Reasoning Tree</label>
              <Switch
                checked={settings.reasoningTree.enabled}
                onCheckedChange={(checked) => updateSettings('reasoningTree', { enabled: checked })}
              />
            </div>
            
            <div className="aone-flex-between">
              <label className="text-aone-sm font-aone-medium">Enable Playback</label>
              <Switch
                checked={settings.reasoningTree.enablePlayback}
                onCheckedChange={(checked) => updateSettings('reasoningTree', { enablePlayback: checked })}
              />
            </div>
            
            <div className="aone-flex-between">
              <label className="text-aone-sm font-aone-medium">Show Mini Map</label>
              <Switch
                checked={settings.reasoningTree.enableMiniMap}
                onCheckedChange={(checked) => updateSettings('reasoningTree', { enableMiniMap: checked })}
              />
            </div>
          </TabsContent>
          
          <TabsContent value="performance" className="space-y-4">
            <div className="grid grid-cols-2 gap-aone-md">
              <div className="text-center aone-spacing-sm bg-muted rounded-aone-lg">
                <div className="text-aone-2xl font-aone-bold text-aone-sage">{performanceMetrics.fps}</div>
                <div className="text-aone-sm text-muted-foreground">FPS</div>
              </div>
              <div className="text-center aone-spacing-sm bg-muted rounded-aone-lg">
                <div className="text-aone-2xl font-aone-bold text-aone-sage">{performanceMetrics.particleCount}</div>
                <div className="text-aone-sm text-muted-foreground">Particles</div>
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-aone-sm font-aone-medium">Performance Mode</label>
              <div className="flex gap-2">
                {['quality', 'balanced', 'performance'].map(mode => (
                  <Button
                    key={mode}
                    variant={settings.integration.performanceMode === mode ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => updateSettings('integration', { performanceMode: mode })}
                    className="capitalize"
                  >
                    {mode}
                  </Button>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Mode Selection */}
      <Card>
        <CardHeader>
          <div className="aone-flex-between">
            <CardTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5" />
              Visual Experience System
              <Badge className="bg-aone-sage text-white">
                Phase 1 Foundation
              </Badge>
            </CardTitle>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsFullscreen(!isFullscreen)}
              >
                <Maximize2 className="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          {/* Mode Selection */}
          <div className="flex gap-2 flex-wrap">
            {[
              { id: 'cinematic', label: 'Cinematic 3D', icon: Camera },
              { id: 'particles', label: 'AI Particles', icon: Sparkles },
              { id: 'reasoning', label: 'Reasoning Tree', icon: TreePine },
              { id: 'integrated', label: 'Integrated View', icon: Layers }
            ].map(({ id, label, icon: Icon }) => (
              <Button
                key={id}
                variant={activeMode === id ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveMode(id as any)}
                className="flex items-center gap-2"
              >
                <Icon className="w-4 h-4" />
                {label}
              </Button>
            ))}
          </div>
        </CardHeader>
      </Card>

      {/* Main Visualization Area */}
      <div className={`${isFullscreen ? 'fixed inset-0 z-50 bg-white p-aone-lg' : 'h-[700px]'}`}>
        {activeMode === 'cinematic' && renderCinematicView()}
        {activeMode === 'particles' && renderParticleView()}
        {activeMode === 'reasoning' && renderReasoningView()}
        {activeMode === 'integrated' && renderIntegratedView()}
      </div>

      {/* Settings Panel */}
      {!isFullscreen && renderSettingsPanel()}
    </div>
  );
};

export default VisualExperienceSystem;
