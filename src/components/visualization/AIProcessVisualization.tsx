import React, { useRef, useEffect, useState, useCallback } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import * as THREE from 'three';
import { ParticleSystem, ParticleEmitter, ParticleBehavior } from '@/services/visualization/ParticleSystem';
import { io, Socket } from 'socket.io-client';

export interface AIProcessState {
  modelName: 'gpt-4o' | 'gpt-o1' | 'o4-mini';
  status: 'idle' | 'processing' | 'completed' | 'error';
  confidence: number;
  processingTime: number;
  requestId: string;
}

export interface AIProcessVisualizationProps {
  enabled?: boolean;
  intensity?: 'low' | 'medium' | 'high';
  showDataFlow?: boolean;
  showConfidenceIndicators?: boolean;
  position?: THREE.Vector3;
  className?: string;
}

/**
 * AI Process Visualization Component
 * Visualizes Azure OpenAI API calls with particle systems
 */
const AIProcessVisualization: React.FC<AIProcessVisualizationProps> = ({
  enabled = true,
  intensity = 'medium',
  showDataFlow = true,
  showConfidenceIndicators = true,
  position = new THREE.Vector3(0, 5, 0)
}) => {
  const { scene } = useThree();
  const particleSystemRef = useRef<ParticleSystem | null>(null);
  const socketRef = useRef<Socket | null>(null);
  const [aiProcesses, setAiProcesses] = useState<Map<string, AIProcessState>>(new Map());
  const [isConnected, setIsConnected] = useState(false);

  // Particle system configuration based on intensity
  const getParticleConfig = useCallback((modelName: string, status: string, confidence: number) => {
    const baseConfigs = {
      'gpt-4o': {
        color: new THREE.Color('#6B7A4F'), // A.ONE sage green
        size: 0.15,
        speed: 2.0,
        emissionRate: 50
      },
      'gpt-o1': {
        color: new THREE.Color('#4A90E2'), // Blue for reasoning
        size: 0.2,
        speed: 1.5,
        emissionRate: 30
      },
      'o4-mini': {
        color: new THREE.Color('#F5A623'), // Orange for mini
        size: 0.1,
        speed: 3.0,
        emissionRate: 80
      }
    };

    const intensityMultipliers = {
      low: 0.5,
      medium: 1.0,
      high: 1.8
    };

    const statusMultipliers = {
      idle: 0.1,
      processing: 1.0,
      completed: 0.3,
      error: 0.8
    };

    const baseConfig = baseConfigs[modelName as keyof typeof baseConfigs] || baseConfigs['gpt-4o'];
    const intensityMult = intensityMultipliers[intensity];
    const statusMult = statusMultipliers[status as keyof typeof statusMultipliers] || 1.0;
    const confidenceMult = 0.5 + confidence * 0.5; // Scale with confidence

    return {
      count: 1000,
      size: baseConfig.size * intensityMult,
      color: baseConfig.color,
      opacity: 0.8 * confidenceMult,
      speed: baseConfig.speed * intensityMult * statusMult,
      lifetime: 3.0,
      emissionRate: baseConfig.emissionRate * intensityMult * statusMult,
      spread: 1.0,
      gravity: new THREE.Vector3(0, -0.1, 0),
      turbulence: 0.5
    };
  }, [intensity]);

  // Initialize particle system
  useEffect(() => {
    if (!enabled) return;

    const maxParticles = intensity === 'high' ? 15000 : intensity === 'medium' ? 10000 : 5000;
    particleSystemRef.current = new ParticleSystem(maxParticles);
    
    // Add particle system to scene
    scene.add(particleSystemRef.current.getPoints());
    particleSystemRef.current.start();

    return () => {
      if (particleSystemRef.current) {
        scene.remove(particleSystemRef.current.getPoints());
        particleSystemRef.current.dispose();
      }
    };
  }, [enabled, intensity, scene]);

  // Setup WebSocket connection for real-time AI process updates
  useEffect(() => {
    if (!enabled) return;

    const socket = io(window.location.origin, {
      transports: ['websocket', 'polling']
    });

    socketRef.current = socket;

    socket.on('connect', () => {
      console.log('AI Process Visualization connected to WebSocket');
      setIsConnected(true);
    });

    socket.on('disconnect', () => {
      console.log('AI Process Visualization disconnected from WebSocket');
      setIsConnected(false);
    });

    // Listen for AI analysis events
    socket.on('analysis-started', (data: { requestId: string; modelName: string }) => {
      console.log('AI Analysis started:', data);
      setAiProcesses(prev => new Map(prev.set(data.requestId, {
        modelName: data.modelName as AIProcessState['modelName'],
        status: 'processing',
        confidence: 0.5,
        processingTime: 0,
        requestId: data.requestId
      })));
    });

    socket.on('analysis-progress', (data: { requestId: string; confidence: number; processingTime: number }) => {
      setAiProcesses(prev => {
        const updated = new Map(prev);
        const existing = updated.get(data.requestId);
        if (existing) {
          updated.set(data.requestId, {
            ...existing,
            confidence: data.confidence,
            processingTime: data.processingTime
          });
        }
        return updated;
      });
    });

    socket.on('analysis-completed', (data: { requestId: string; confidence: number; processingTime: number }) => {
      console.log('AI Analysis completed:', data);
      setAiProcesses(prev => {
        const updated = new Map(prev);
        const existing = updated.get(data.requestId);
        if (existing) {
          updated.set(data.requestId, {
            ...existing,
            status: 'completed',
            confidence: data.confidence,
            processingTime: data.processingTime
          });
        }
        return updated;
      });

      // Remove completed process after delay
      setTimeout(() => {
        setAiProcesses(prev => {
          const updated = new Map(prev);
          updated.delete(data.requestId);
          return updated;
        });
      }, 5000);
    });

    socket.on('analysis-error', (data: { requestId: string; error: string }) => {
      console.log('AI Analysis error:', data);
      setAiProcesses(prev => {
        const updated = new Map(prev);
        const existing = updated.get(data.requestId);
        if (existing) {
          updated.set(data.requestId, {
            ...existing,
            status: 'error',
            confidence: 0.1
          });
        }
        return updated;
      });

      // Remove error process after delay
      setTimeout(() => {
        setAiProcesses(prev => {
          const updated = new Map(prev);
          updated.delete(data.requestId);
          return updated;
        });
      }, 3000);
    });

    return () => {
      socket.disconnect();
    };
  }, [enabled]);

  // Update particle emitters based on AI processes
  useEffect(() => {
    if (!particleSystemRef.current || !enabled) return;

    // Clear existing emitters
    const existingEmitters = Array.from(aiProcesses.keys());
    
    aiProcesses.forEach((process, requestId) => {
      const config = getParticleConfig(process.modelName, process.status, process.confidence);
      
      // Create behaviors based on model and status
      const behaviors: ParticleBehavior[] = [];

      if (showDataFlow) {
        // Data flow behavior - particles flow toward analysis target
        behaviors.push({
          type: 'flow',
          strength: 2.0,
          target: new THREE.Vector3(0, 0, 0) // Flow toward center
        });
      }

      if (showConfidenceIndicators) {
        // Confidence-based behavior
        if (process.confidence > 0.8) {
          behaviors.push({
            type: 'attraction',
            strength: 1.5,
            target: position.clone()
          });
        } else if (process.confidence < 0.3) {
          behaviors.push({
            type: 'repulsion',
            strength: 1.0,
            target: position.clone(),
            radius: 2.0
          });
        }
      }

      // Model-specific behaviors
      switch (process.modelName) {
        case 'gpt-o1':
          // Reasoning model - spiral pattern
          behaviors.push({
            type: 'spiral',
            strength: 1.0,
            target: position.clone(),
            frequency: 0.5
          });
          break;
        case 'gpt-4o':
          // Main model - orbit pattern
          behaviors.push({
            type: 'orbit',
            strength: 0.8,
            target: position.clone(),
            frequency: 1.0
          });
          break;
        case 'o4-mini':
          // Mini model - burst pattern
          if (process.status === 'processing') {
            behaviors.push({
              type: 'burst',
              strength: 2.0,
              target: position.clone()
            });
          }
          break;
      }

      // Create or update emitter
      const emitterPosition = position.clone().add(
        new THREE.Vector3(
          (Math.random() - 0.5) * 2,
          (Math.random() - 0.5) * 2,
          (Math.random() - 0.5) * 2
        )
      );

      const emitter: ParticleEmitter = {
        id: requestId,
        position: emitterPosition,
        direction: new THREE.Vector3(0, 1, 0),
        config,
        behaviors,
        active: process.status === 'processing' || process.status === 'error'
      };

      particleSystemRef.current.addEmitter(emitter);
    });

    // Remove emitters for completed/removed processes
    const currentEmitters = particleSystemRef.current.getEmitter;
    // Note: We'll need to add a method to get all emitter IDs
    
  }, [aiProcesses, enabled, showDataFlow, showConfidenceIndicators, position, getParticleConfig]);

  // Update particle system each frame
  useFrame(() => {
    if (particleSystemRef.current && enabled) {
      particleSystemRef.current.update();
    }
  });

  // Debug information (only in development)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('AI Process Visualization State:', {
        enabled,
        intensity,
        isConnected,
        activeProcesses: aiProcesses.size,
        particleCount: particleSystemRef.current?.getParticleCount() || 0
      });
    }
  }, [enabled, intensity, isConnected, aiProcesses.size]);

  return null; // This component only manages particles in the scene
};

export default AIProcessVisualization;

/**
 * Hook for controlling AI Process Visualization
 */
export const useAIProcessVisualization = () => {
  const [enabled, setEnabled] = useState(true);
  const [intensity, setIntensity] = useState<'low' | 'medium' | 'high'>('medium');
  const [showDataFlow, setShowDataFlow] = useState(true);
  const [showConfidenceIndicators, setShowConfidenceIndicators] = useState(true);

  const toggleEnabled = useCallback(() => setEnabled(prev => !prev), []);
  const setIntensityLevel = useCallback((level: 'low' | 'medium' | 'high') => setIntensity(level), []);
  const toggleDataFlow = useCallback(() => setShowDataFlow(prev => !prev), []);
  const toggleConfidenceIndicators = useCallback(() => setShowConfidenceIndicators(prev => !prev), []);

  return {
    enabled,
    intensity,
    showDataFlow,
    showConfidenceIndicators,
    toggleEnabled,
    setIntensityLevel,
    toggleDataFlow,
    toggleConfidenceIndicators
  };
};
