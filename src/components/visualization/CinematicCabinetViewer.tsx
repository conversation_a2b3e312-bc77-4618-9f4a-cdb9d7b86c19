import React, { Suspense, useState, useRef, useCallback, useEffect } from 'react';
import { <PERSON>vas, useFrame, useThree } from '@react-three/fiber';
import { OrbitControls, Box, Text, Grid, Environment } from '@react-three/drei';
import { useSpring, animated } from '@react-spring/three';
import { useGesture } from '@use-gesture/react';
import * as THREE from 'three';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  RotateCcw, 
  ZoomIn, 
  ZoomOut, 
  Move3D, 
  Eye, 
  Settings,
  Maximize,
  Download,
  Info,
  Play,
  Pause,
  SkipBack,
  Camera,
  Layers
} from 'lucide-react';
import { ReconstructionResult, Cabinet3DModel, Point3D } from '@/services/aiAnalysisService';
import { cinematicCameraController } from '@/services/visualization/CinematicCameraController';
import { sceneTransitionManager } from '@/services/visualization/SceneTransitionManager';

interface CinematicCabinetViewerProps {
  reconstruction: ReconstructionResult;
  className?: string;
  enableCinematicMode?: boolean;
  transitionPreset?: 'professional' | 'dramatic' | 'subtle';
}

interface AnimatedCabinet3DProps {
  cabinet: Cabinet3DModel;
  isSelected: boolean;
  onSelect: () => void;
  animationDelay?: number;
}

/**
 * Animated 3D Cabinet Component with cinematic transitions
 */
const AnimatedCabinet3D: React.FC<AnimatedCabinet3DProps> = ({ 
  cabinet, 
  isSelected, 
  onSelect,
  animationDelay = 0 
}) => {
  const meshRef = useRef<any>();
  const [hovered, setHovered] = useState(false);
  
  // Convert dimensions from mm to meters for Three.js
  const width = cabinet.dimensions.width / 1000;
  const height = cabinet.dimensions.height / 1000;
  const depth = cabinet.dimensions.depth / 1000;
  
  const position: [number, number, number] = [
    cabinet.dimensions.position.x / 1000,
    cabinet.dimensions.position.y / 1000 + height / 2,
    cabinet.dimensions.position.z / 1000
  ];

  // Spring animation for cabinet appearance
  const { scale, opacity } = useSpring({
    scale: isSelected ? [1.05, 1.05, 1.05] : [1, 1, 1],
    opacity: isSelected ? 0.9 : 0.7,
    config: { tension: 300, friction: 30 },
    delay: animationDelay
  });

  // Color based on cabinet type with A.ONE sage green theme
  const getColor = () => {
    const colors = {
      'upper': '#6B7A4F', // A.ONE sage green
      'lower': '#8B9A6F', // Lighter sage
      'pantry': '#4A5A3F', // Darker sage
      'island': '#7A8A5F', // Medium sage
      'default': '#6B7A4F'
    };
    return colors[cabinet.type as keyof typeof colors] || colors.default;
  };

  // Gesture handling for enhanced interactions
  const bind = useGesture({
    onHover: ({ hovering }) => setHovered(hovering!),
    onClick: (state) => {
      state.event.stopPropagation();
      onSelect();
    }
  });

  return (
    <animated.group {...bind()} scale={scale}>
      <Box
        ref={meshRef}
        position={position}
        args={[width, height, depth]}
      >
        <animated.meshStandardMaterial
          color={getColor()}
          transparent
          opacity={opacity}
          roughness={0.3}
          metalness={0.1}
          emissive={hovered ? '#2A3A1F' : '#000000'}
          emissiveIntensity={hovered ? 0.1 : 0}
        />
        
        {/* Selection outline */}
        {isSelected && (
          <lineSegments>
            <edgesGeometry args={[new THREE.BoxGeometry(width, height, depth)]} />
            <lineBasicMaterial color="#ffffff" linewidth={2} />
          </lineSegments>
        )}
      </Box>
      
      {/* Cabinet label with fade-in animation */}
      <Text
        position={[position[0], position[1] + height / 2 + 0.2, position[2]]}
        fontSize={0.12}
        color={isSelected ? '#ffffff' : '#374151'}
        anchorX="center"
        anchorY="middle"
        material-transparent
        material-opacity={isSelected ? 1 : 0.8}
      >
        {cabinet.type}
      </Text>
    </animated.group>
  );
};

/**
 * Enhanced 3D Scene with cinematic camera controls
 */
const CinematicScene3D: React.FC<{
  reconstruction: ReconstructionResult;
  selectedCabinet: string | null;
  onCabinetSelect: (cabinetId: string) => void;
  enableCinematicMode: boolean;
  transitionPreset: 'professional' | 'dramatic' | 'subtle';
}> = ({ reconstruction, selectedCabinet, onCabinetSelect, enableCinematicMode, transitionPreset }) => {
  const { camera, gl } = useThree();
  const controlsRef = useRef<any>();

  useEffect(() => {
    if (enableCinematicMode) {
      // Initialize cinematic camera controller
      cinematicCameraController.setCamera(camera);
      if (controlsRef.current) {
        cinematicCameraController.setControls(controlsRef.current);
      }
    }
  }, [camera, enableCinematicMode]);

  // Auto-transition to 3D view on mount if cinematic mode is enabled
  useEffect(() => {
    if (enableCinematicMode) {
      const timer = setTimeout(() => {
        sceneTransitionManager.transitionTo3D({ preset: transitionPreset });
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [enableCinematicMode, transitionPreset]);

  return (
    <>
      {/* Enhanced lighting for cinematic effect */}
      <ambientLight intensity={0.4} />
      <directionalLight 
        position={[10, 10, 5]} 
        intensity={1.2} 
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      <pointLight position={[-10, -10, -5]} intensity={0.6} color="#6B7A4F" />
      <spotLight 
        position={[0, 15, 0]} 
        intensity={0.8} 
        angle={Math.PI / 6}
        penumbra={0.5}
        color="#ffffff"
      />

      {/* Environment for reflections */}
      <Environment preset="apartment" />

      {/* Enhanced grid with A.ONE styling */}
      <Grid 
        args={[reconstruction.roomDimensions.width / 1000, reconstruction.roomDimensions.depth / 1000]} 
        cellSize={0.5} 
        cellThickness={0.5} 
        cellColor="#6B7A4F" 
        sectionSize={2} 
        sectionThickness={1} 
        sectionColor="#4A5A3F"
        fadeDistance={30}
        fadeStrength={1}
        followCamera={false}
        infiniteGrid={false}
        position={[0, 0, 0]}
      />

      {/* Animated cabinets with staggered appearance */}
      {reconstruction.cabinets.map((cabinet, index) => (
        <AnimatedCabinet3D
          key={cabinet.id}
          cabinet={cabinet}
          isSelected={selectedCabinet === cabinet.id}
          onSelect={() => onCabinetSelect(cabinet.id)}
          animationDelay={index * 100} // Staggered animation
        />
      ))}

      {/* Enhanced camera controls */}
      <OrbitControls 
        ref={controlsRef}
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        maxPolarAngle={Math.PI / 2}
        minDistance={2}
        maxDistance={20}
        enableDamping={true}
        dampingFactor={0.05}
        zoomSpeed={0.8}
        rotateSpeed={0.6}
        panSpeed={0.8}
      />
    </>
  );
};

/**
 * Main Cinematic Cabinet Viewer Component
 */
const CinematicCabinetViewer: React.FC<CinematicCabinetViewerProps> = ({
  reconstruction,
  className = '',
  enableCinematicMode = true,
  transitionPreset = 'professional'
}) => {
  const [selectedCabinet, setSelectedCabinet] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'2d' | '3d'>('2d');
  const [isTransitioning, setIsTransitioning] = useState(false);

  const selectedCabinetData = selectedCabinet
    ? reconstruction.cabinets.find(c => c.id === selectedCabinet)
    : null;

  // Enhanced cabinet selection with cinematic focus
  const handleCabinetSelect = useCallback(async (cabinetId: string) => {
    setSelectedCabinet(cabinetId);
    
    if (enableCinematicMode) {
      const cabinet = reconstruction.cabinets.find(c => c.id === cabinetId);
      if (cabinet) {
        const position = new THREE.Vector3(
          cabinet.dimensions.position.x / 1000,
          cabinet.dimensions.position.y / 1000,
          cabinet.dimensions.position.z / 1000
        );
        
        const boundingBox = new THREE.Box3().setFromCenterAndSize(
          position,
          new THREE.Vector3(
            cabinet.dimensions.width / 1000,
            cabinet.dimensions.height / 1000,
            cabinet.dimensions.depth / 1000
          )
        );

        setIsTransitioning(true);
        await sceneTransitionManager.focusOnObject(
          cabinetId,
          position,
          boundingBox,
          { 
            preset: transitionPreset,
            onComplete: () => setIsTransitioning(false)
          }
        );
      }
    }
  }, [selectedCabinet, enableCinematicMode, transitionPreset, reconstruction.cabinets]);

  // View mode transitions
  const handleViewModeChange = async (mode: '2d' | '3d') => {
    if (mode === viewMode || !enableCinematicMode) {
      setViewMode(mode);
      return;
    }

    setIsTransitioning(true);
    
    try {
      if (mode === '3d') {
        await sceneTransitionManager.transitionTo3D({ 
          preset: transitionPreset,
          onComplete: () => {
            setViewMode('3d');
            setIsTransitioning(false);
          }
        });
      } else {
        await sceneTransitionManager.transitionTo2D({ 
          preset: transitionPreset,
          onComplete: () => {
            setViewMode('2d');
            setIsTransitioning(false);
          }
        });
      }
    } catch (error) {
      console.error('View transition failed:', error);
      setIsTransitioning(false);
    }
  };

  // Return to overview
  const handleReturnToOverview = async () => {
    if (!enableCinematicMode) return;

    setIsTransitioning(true);
    setSelectedCabinet(null);
    
    await sceneTransitionManager.returnToOverview({
      preset: transitionPreset,
      onComplete: () => setIsTransitioning(false)
    });
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Enhanced Header with Cinematic Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="aone-flex-between">
            <div className="flex items-center gap-2">
              <Move3D className="w-5 h-5" />
              Cinematic 3D Cabinet Reconstruction
              <Badge variant="outline" className="ml-2">
                {reconstruction.cabinets.length} Cabinets
              </Badge>
              {enableCinematicMode && (
                <Badge className="bg-aone-sage text-white">
                  <Camera className="w-3 h-3 mr-1" />
                  Cinematic Mode
                </Badge>
              )}
            </div>
            
            {/* Cinematic Controls */}
            {enableCinematicMode && (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewModeChange('2d')}
                  disabled={isTransitioning || viewMode === '2d'}
                >
                  <Layers className="w-4 h-4 mr-1" />
                  2D View
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewModeChange('3d')}
                  disabled={isTransitioning || viewMode === '3d'}
                >
                  <Move3D className="w-4 h-4 mr-1" />
                  3D View
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReturnToOverview}
                  disabled={isTransitioning}
                >
                  <Eye className="w-4 h-4 mr-1" />
                  Overview
                </Button>
                {isTransitioning && (
                  <Badge variant="secondary" className="animate-pulse">
                    <Play className="w-3 h-3 mr-1" />
                    Transitioning...
                  </Badge>
                )}
              </div>
            )}
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Main 3D Viewer */}
      <Card className="h-[600px]">
        <CardContent className="p-0 h-full">
          <div
            className="h-full w-full relative"
            onContextMenu={(e) => e.preventDefault()}
            style={{ touchAction: 'none' }}
          >
            <Suspense fallback={
              <div className="aone-flex-center h-full bg-gradient-to-br from-aone-warm-white to-aone-sage/5">
                <div className="text-center">
                  <div className="aone-loading-spinner w-8 h-8 mx-auto mb-2"></div>
                  <p className="text-aone-sm text-aone-sage">Loading cinematic 3D model...</p>
                  <p className="text-xs text-gray-500 mt-1">Preparing enhanced visualization</p>
                </div>
              </div>
            }>
              <Canvas
                camera={{
                  position: [0, 0, 8],
                  fov: 60,
                  near: 0.1,
                  far: 1000
                }}
                style={{ height: '100%', width: '100%' }}
                shadows={enableCinematicMode}
                onCreated={(state) => {
                  console.log('Cinematic 3D Canvas created successfully');
                  state.gl.shadowMap.enabled = enableCinematicMode;
                  state.gl.shadowMap.type = THREE.PCFSoftShadowMap;
                  state.gl.toneMapping = THREE.ACESFilmicToneMapping;
                  state.gl.toneMappingExposure = 1.2;
                }}
              >
                <CinematicScene3D
                  reconstruction={reconstruction}
                  selectedCabinet={selectedCabinet}
                  onCabinetSelect={handleCabinetSelect}
                  enableCinematicMode={enableCinematicMode}
                  transitionPreset={transitionPreset}
                />
              </Canvas>
            </Suspense>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CinematicCabinetViewer;
export { AnimatedCabinet3D, CinematicScene3D };
