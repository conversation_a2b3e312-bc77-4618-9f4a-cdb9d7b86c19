import React, { useState } from 'react';
import { useAuth } from '../../hooks/useAuth';

interface RegisterFormProps {
  onSuccess?: () => void;
  onSwitchToLogin?: () => void;
}

export const RegisterForm: React.FC<RegisterFormProps> = ({ onSuccess, onSwitchToLogin }) => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    role: 'viewer' as 'admin' | 'designer' | 'collaborator' | 'viewer'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { register } = useAuth();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Validation
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      setIsLoading(false);
      return;
    }

    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long');
      setIsLoading(false);
      return;
    }

    try {
      await register({
        email: formData.email,
        password: formData.password,
        firstName: formData.firstName,
        lastName: formData.lastName,
        role: formData.role
      });
      onSuccess?.();
    } catch (err: any) {
      setError(err.message || 'Registration failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto aone-card-interactive p-aone-lg">
      <h2 className="text-aone-2xl font-aone-bold text-foreground mb-aone-lg text-center">
        Create Your Account
      </h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="aone-status-error px-aone-md py-aone-sm rounded-aone-md">
            {error}
          </div>
        )}
        
        <div className="grid grid-cols-2 gap-aone-md">
          <div>
            <label htmlFor="firstName" className="aone-label mb-1">
              First Name
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              required
              className="aone-input w-full shadow-sm aone-focus-ring aone-focus-ring focus:border-blue-500"
              placeholder="First name"
            />
          </div>
          
          <div>
            <label htmlFor="lastName" className="aone-label mb-1">
              Last Name
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              required
              className="aone-input w-full shadow-sm aone-focus-ring aone-focus-ring focus:border-blue-500"
              placeholder="Last name"
            />
          </div>
        </div>
        
        <div>
          <label htmlFor="email" className="aone-label mb-1">
            Email Address
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            required
            className="aone-input w-full shadow-sm aone-focus-ring aone-focus-ring focus:border-blue-500"
            placeholder="Enter your email"
          />
        </div>
        
        <div>
          <label htmlFor="role" className="aone-label mb-1">
            Role
          </label>
          <select
            id="role"
            name="role"
            value={formData.role}
            onChange={handleChange}
            className="aone-input w-full shadow-sm aone-focus-ring aone-focus-ring focus:border-blue-500"
          >
            <option value="viewer">Viewer</option>
            <option value="collaborator">Collaborator</option>
            <option value="designer">Designer</option>
            <option value="admin">Admin</option>
          </select>
        </div>
        
        <div>
          <label htmlFor="password" className="aone-label mb-1">
            Password
          </label>
          <input
            type="password"
            id="password"
            name="password"
            value={formData.password}
            onChange={handleChange}
            required
            className="aone-input w-full shadow-sm aone-focus-ring aone-focus-ring focus:border-blue-500"
            placeholder="Enter your password"
          />
        </div>
        
        <div>
          <label htmlFor="confirmPassword" className="aone-label mb-1">
            Confirm Password
          </label>
          <input
            type="password"
            id="confirmPassword"
            name="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleChange}
            required
            className="aone-input w-full shadow-sm aone-focus-ring aone-focus-ring focus:border-blue-500"
            placeholder="Confirm your password"
          />
        </div>
        
        <button
          type="submit"
          disabled={isLoading}
          className="w-full bg-blue-600 text-white py-2 px-aone-md rounded-aone-md aone-micro-interaction hover:bg-blue-700 aone-focus-ring aone-focus-ring focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isLoading ? 'Creating Account...' : 'Create Account'}
        </button>
      </form>
      
      <div className="mt-6 text-center">
        <p className="text-aone-sm text-muted-foreground">
          Already have an account?{' '}
          <button
            onClick={onSwitchToLogin}
            className="aone-button-ghost text-aone-sage aone-micro-interaction hover:text-aone-sage-dark font-aone-medium"
          >
            Sign in
          </button>
        </p>
      </div>
    </div>
  );
};

export default RegisterForm;
