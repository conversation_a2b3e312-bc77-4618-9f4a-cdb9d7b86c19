import React, { useState } from 'react';
import { useAuth } from '../../hooks/useAuth';

interface LoginFormProps {
  onSuccess?: () => void;
  onSwitchToRegister?: () => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({ onSuccess, onSwitchToRegister }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      await login(email, password);
      onSuccess?.();
    } catch (err: any) {
      setError(err.message || 'Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="aone-card-enterprise max-w-md mx-auto p-aone-lg">
      <h2 className="text-aone-2xl font-aone-bold text-foreground mb-aone-lg text-center">
        Sign In to Blackveil Design Mind
      </h2>

      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="aone-status-error">
            {error}
          </div>
        )}

        <div>
          <label htmlFor="email" className="aone-label-enterprise mb-1">
            Email Address
          </label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className="aone-input-enterprise w-full"
            placeholder="Enter your email"
          />
        </div>
        
        <div>
          <label htmlFor="password" className="aone-label-enterprise mb-1">
            Password
          </label>
          <input
            type="password"
            id="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            className="aone-input-enterprise w-full"
            placeholder="Enter your password"
          />
        </div>

        <button
          type="submit"
          disabled={isLoading}
          className="aone-button-primary w-full aone-micro-interaction disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Signing In...' : 'Sign In'}
        </button>
      </form>

      <div className="mt-6 text-center">
        <p className="text-aone-sm text-muted-foreground">
          Don't have an account?{' '}
          <button
            type="button"
            onClick={onSwitchToRegister}
            className="aone-button-ghost text-aone-sage hover:text-aone-sage-dark font-aone-medium aone-micro-interaction"
          >
            Sign up
          </button>
        </p>
      </div>
    </div>
  );
};

export default LoginForm;
