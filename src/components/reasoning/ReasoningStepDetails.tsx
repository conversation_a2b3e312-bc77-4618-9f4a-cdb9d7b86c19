import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Brain, 
  Clock, 
  Target, 
  Lightbulb, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  TrendingUp,
  GitBranch,
  Zap
} from 'lucide-react';

interface ReasoningStep {
  id: string;
  type: 'observation' | 'analysis' | 'inference' | 'validation' | 'conclusion';
  description: string;
  confidence: number;
  dependencies: string[];
  gptO1Reasoning?: {
    internalThoughts: string[];
    reasoningPath: string[];
    confidenceFactors: string[];
    alternativesConsidered: string[];
    complexityScore: number;
  };
  visualizationData?: {
    position: { x: number; y: number };
    status: 'pending' | 'processing' | 'completed' | 'failed';
    processingTime?: number;
    complexity: 'low' | 'medium' | 'high';
    parentSteps: string[];
    childSteps: string[];
  };
  timestamp: Date;
}

interface ReasoningStepDetailsProps {
  step: ReasoningStep | null;
  chainId?: string;
  onClose?: () => void;
}

const ReasoningStepDetails: React.FC<ReasoningStepDetailsProps> = ({
  step,
  chainId,
  onClose
}) => {
  const [loading, setLoading] = useState(false);
  const [fullStepData, setFullStepData] = useState<any>(null);

  useEffect(() => {
    if (step && chainId) {
      loadFullStepDetails();
    }
  }, [step, chainId]);

  const loadFullStepDetails = async () => {
    if (!step || !chainId) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/reasoning/steps/${step.id}/details?chainId=${chainId}`);
      const data = await response.json();

      if (data.success) {
        setFullStepData(data.data);
      }
    } catch (err) {
      console.error('Error loading step details:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'processing':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'failed':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'observation':
        return <Target className="w-4 h-4" />;
      case 'analysis':
        return <Brain className="w-4 h-4" />;
      case 'inference':
        return <Lightbulb className="w-4 h-4" />;
      case 'validation':
        return <CheckCircle className="w-4 h-4" />;
      case 'conclusion':
        return <TrendingUp className="w-4 h-4" />;
      default:
        return <GitBranch className="w-4 h-4" />;
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-muted text-foreground border-border';
    }
  };

  if (!step) {
    return (
      <Card className="w-full">
        <CardContent className="aone-flex-center p-aone-xl text-gray-500">
          <Brain className="w-8 h-8 mr-2" />
          <span>Select a reasoning step to view details</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="aone-flex-between">
          <CardTitle className="flex items-center gap-2">
            {getTypeIcon(step.type)}
            Reasoning Step Details
          </CardTitle>
          {onClose && (
            <Button variant="outline" size="sm" onClick={onClose}>
              Close
            </Button>
          )}
        </div>
        <div className="flex items-center gap-2">
          {getStatusIcon(step.visualizationData?.status || 'pending')}
          <Badge variant="outline" className="capitalize">
            {step.type}
          </Badge>
          {step.visualizationData?.complexity && (
            <Badge className={getComplexityColor(step.visualizationData.complexity)}>
              {step.visualizationData.complexity} complexity
            </Badge>
          )}
          {step.gptO1Reasoning && (
            <Badge className="bg-blue-100 text-blue-800 border-blue-200">
              <Zap className="w-3 h-3 mr-1" />
              GPT-o1 Enhanced
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="reasoning" disabled={!step.gptO1Reasoning}>
              GPT-o1 Reasoning
            </TabsTrigger>
            <TabsTrigger value="dependencies">Dependencies</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div>
              <h4 className="font-aone-medium mb-2">Description</h4>
              <p className="text-aone-sm text-foreground bg-muted aone-spacing-sm rounded-aone-md">
                {step.description}
              </p>
            </div>

            <div className="grid grid-cols-2 gap-aone-md">
              <div>
                <h4 className="font-aone-medium mb-2">Confidence Score</h4>
                <div className="flex items-center gap-2">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full transition-all"
                      style={{ width: `${step.confidence * 100}%` }}
                    />
                  </div>
                  <span className="text-aone-sm font-aone-medium">
                    {(step.confidence * 100).toFixed(1)}%
                  </span>
                </div>
              </div>

              <div>
                <h4 className="font-aone-medium mb-2">Processing Time</h4>
                <p className="text-aone-sm text-muted-foreground">
                  {step.visualizationData?.processingTime 
                    ? `${step.visualizationData.processingTime}ms`
                    : 'Not available'
                  }
                </p>
              </div>
            </div>

            <div>
              <h4 className="font-aone-medium mb-2">Timestamp</h4>
              <p className="text-aone-sm text-muted-foreground">
                {new Date(step.timestamp).toLocaleString()}
              </p>
            </div>
          </TabsContent>

          <TabsContent value="reasoning" className="space-y-4">
            {step.gptO1Reasoning ? (
              <>
                <div>
                  <h4 className="font-aone-medium mb-2 flex items-center gap-2">
                    <Brain className="w-4 h-4" />
                    Internal Thoughts
                  </h4>
                  <ScrollArea className="h-32 w-full border rounded-aone-md aone-spacing-sm">
                    <div className="space-y-2">
                      {step.gptO1Reasoning.internalThoughts.map((thought, index) => (
                        <div key={index} className="text-aone-sm text-foreground bg-blue-50 aone-spacing-xs rounded">
                          <span className="font-aone-medium text-aone-sage-dark">#{index + 1}:</span> {thought}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>

                <div>
                  <h4 className="font-aone-medium mb-2 flex items-center gap-2">
                    <GitBranch className="w-4 h-4" />
                    Reasoning Path
                  </h4>
                  <ScrollArea className="h-32 w-full border rounded-aone-md aone-spacing-sm">
                    <div className="space-y-2">
                      {step.gptO1Reasoning.reasoningPath.map((pathStep, index) => (
                        <div key={index} className="text-aone-sm text-foreground flex items-start gap-2">
                          <span className="font-aone-medium text-green-600 min-w-[20px]">
                            {index + 1}.
                          </span>
                          <span>{pathStep}</span>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>

                <div className="grid grid-cols-2 gap-aone-md">
                  <div>
                    <h4 className="font-aone-medium mb-2">Confidence Factors</h4>
                    <ScrollArea className="h-24 w-full border rounded-aone-md aone-spacing-xs">
                      <div className="space-y-1">
                        {step.gptO1Reasoning.confidenceFactors.map((factor, index) => (
                          <div key={index} className="text-xs text-muted-foreground bg-green-50 p-1 rounded">
                            {factor}
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>

                  <div>
                    <h4 className="font-aone-medium mb-2">Alternatives Considered</h4>
                    <ScrollArea className="h-24 w-full border rounded-aone-md aone-spacing-xs">
                      <div className="space-y-1">
                        {step.gptO1Reasoning.alternativesConsidered.map((alt, index) => (
                          <div key={index} className="text-xs text-muted-foreground bg-yellow-50 p-1 rounded">
                            {alt}
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                </div>

                <div>
                  <h4 className="font-aone-medium mb-2">Complexity Score</h4>
                  <div className="flex items-center gap-2">
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-purple-500 h-2 rounded-full transition-all"
                        style={{ width: `${step.gptO1Reasoning.complexityScore * 100}%` }}
                      />
                    </div>
                    <span className="text-aone-sm font-aone-medium">
                      {(step.gptO1Reasoning.complexityScore * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center text-gray-500 py-8">
                <Brain className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>GPT-o1 reasoning data not available for this step</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="dependencies" className="space-y-4">
            <div>
              <h4 className="font-aone-medium mb-2">Parent Steps</h4>
              {step.dependencies.length > 0 ? (
                <div className="space-y-2">
                  {step.dependencies.map(depId => (
                    <div key={depId} className="text-aone-sm bg-muted aone-spacing-xs rounded border-l-4 border-blue-500">
                      {depId}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-aone-sm text-gray-500">No dependencies</p>
              )}
            </div>

            <Separator />

            <div>
              <h4 className="font-aone-medium mb-2">Child Steps</h4>
              {step.visualizationData?.childSteps?.length ? (
                <div className="space-y-2">
                  {step.visualizationData.childSteps.map(childId => (
                    <div key={childId} className="text-aone-sm bg-muted aone-spacing-xs rounded border-l-4 border-green-500">
                      {childId}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-aone-sm text-gray-500">No child steps</p>
              )}
            </div>
          </TabsContent>

          <TabsContent value="performance" className="space-y-4">
            <div className="grid grid-cols-2 gap-aone-md">
              <div>
                <h4 className="font-aone-medium mb-2">Processing Time</h4>
                <p className="text-aone-2xl font-aone-bold text-aone-sage">
                  {step.visualizationData?.processingTime 
                    ? `${step.visualizationData.processingTime}ms`
                    : 'N/A'
                  }
                </p>
              </div>

              <div>
                <h4 className="font-aone-medium mb-2">Status</h4>
                <div className="flex items-center gap-2">
                  {getStatusIcon(step.visualizationData?.status || 'pending')}
                  <span className="capitalize font-aone-medium">
                    {step.visualizationData?.status || 'pending'}
                  </span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-aone-medium mb-2">Position in Chain</h4>
              <p className="text-aone-sm text-muted-foreground">
                X: {step.visualizationData?.position?.x || 0}, 
                Y: {step.visualizationData?.position?.y || 0}
              </p>
            </div>

            {step.gptO1Reasoning && (
              <div>
                <h4 className="font-aone-medium mb-2">GPT-o1 Metrics</h4>
                <div className="grid grid-cols-2 gap-aone-md text-aone-sm">
                  <div>
                    <span className="text-muted-foreground">Internal Thoughts:</span>
                    <span className="ml-2 font-aone-medium">
                      {step.gptO1Reasoning.internalThoughts.length}
                    </span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Reasoning Steps:</span>
                    <span className="ml-2 font-aone-medium">
                      {step.gptO1Reasoning.reasoningPath.length}
                    </span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Confidence Factors:</span>
                    <span className="ml-2 font-aone-medium">
                      {step.gptO1Reasoning.confidenceFactors.length}
                    </span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Alternatives:</span>
                    <span className="ml-2 font-aone-medium">
                      {step.gptO1Reasoning.alternativesConsidered.length}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ReasoningStepDetails;
