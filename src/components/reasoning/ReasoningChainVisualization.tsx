import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Brain, Clock, Target, Lightbulb, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { io, Socket } from 'socket.io-client';

interface ReasoningStep {
  id: string;
  type: 'observation' | 'analysis' | 'inference' | 'validation' | 'conclusion';
  description: string;
  confidence: number;
  dependencies: string[];
  gptO1Reasoning?: {
    internalThoughts: string[];
    reasoningPath: string[];
    confidenceFactors: string[];
    alternativesConsidered: string[];
    complexityScore: number;
  };
  visualizationData?: {
    position: { x: number; y: number };
    status: 'pending' | 'processing' | 'completed' | 'failed';
    processingTime?: number;
    complexity: 'low' | 'medium' | 'high';
    parentSteps: string[];
    childSteps: string[];
  };
  timestamp: Date;
}

interface ReasoningChain {
  id: string;
  analysisId: string;
  goal: string;
  status: 'in_progress' | 'completed' | 'failed';
  currentStep: number;
  steps: ReasoningStep[];
  metadata: {
    startTime: Date;
    endTime?: Date;
    totalSteps: number;
    completedSteps: number;
  };
}

interface ReasoningChainVisualizationProps {
  analysisId: string;
  chainId?: string;
  onStepClick?: (step: ReasoningStep) => void;
}

const ReasoningChainVisualization: React.FC<ReasoningChainVisualizationProps> = ({
  analysisId,
  chainId,
  onStepClick
}) => {
  const [chains, setChains] = useState<ReasoningChain[]>([]);
  const [selectedChain, setSelectedChain] = useState<ReasoningChain | null>(null);
  const [selectedStep, setSelectedStep] = useState<ReasoningStep | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const socketRef = useRef<Socket | null>(null);
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    // Initialize WebSocket connection
    socketRef.current = io('http://localhost:3001');

    // Listen for reasoning updates
    socketRef.current.on('reasoning-step-update', (updateData) => {
      handleReasoningStepUpdate(updateData);
    });

    socketRef.current.on('reasoning-chain-initialized', (chainData) => {
      handleReasoningChainInitialized(chainData);
    });

    socketRef.current.on('reasoning-chain-completed', (chainData) => {
      handleReasoningChainCompleted(chainData);
    });

    // Load initial data
    loadReasoningChains();

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, [analysisId]);

  useEffect(() => {
    if (chainId && chains.length > 0) {
      const chain = chains.find(c => c.id === chainId);
      if (chain) {
        setSelectedChain(chain);
        initializeVisualization(chainId);
      }
    } else if (chains.length > 0) {
      setSelectedChain(chains[0]);
      initializeVisualization(chains[0].id);
    }
  }, [chainId, chains]);

  const loadReasoningChains = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/reasoning/chains/analysis/${analysisId}`);
      const data = await response.json();

      if (data.success) {
        setChains(data.data.chains);
      } else {
        setError(data.error || 'Failed to load reasoning chains');
      }
    } catch (err) {
      setError('Failed to connect to reasoning service');
      console.error('Error loading reasoning chains:', err);
    } finally {
      setLoading(false);
    }
  };

  const initializeVisualization = async (chainId: string) => {
    try {
      await fetch(`/api/reasoning/chains/${chainId}/initialize-visualization`, {
        method: 'POST'
      });
    } catch (err) {
      console.error('Error initializing visualization:', err);
    }
  };

  const handleReasoningStepUpdate = (updateData: any) => {
    setChains(prevChains => 
      prevChains.map(chain => {
        if (chain.id === updateData.chainId) {
          return {
            ...chain,
            steps: chain.steps.map(step => 
              step.id === updateData.stepId 
                ? { ...step, ...updateData.step }
                : step
            )
          };
        }
        return chain;
      })
    );

    // Update selected chain if it matches
    if (selectedChain?.id === updateData.chainId) {
      setSelectedChain(prevChain => ({
        ...prevChain!,
        steps: prevChain!.steps.map(step => 
          step.id === updateData.stepId 
            ? { ...step, ...updateData.step }
            : step
        )
      }));
    }
  };

  const handleReasoningChainInitialized = (chainData: any) => {
    if (chainData.analysisId === analysisId) {
      loadReasoningChains(); // Reload to get the new chain
    }
  };

  const handleReasoningChainCompleted = (chainData: any) => {
    setChains(prevChains => 
      prevChains.map(chain => 
        chain.id === chainData.chainId 
          ? { ...chain, status: 'completed' as const }
          : chain
      )
    );
  };

  const getStepIcon = (step: ReasoningStep) => {
    const status = step.visualizationData?.status || 'pending';
    
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'processing':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-muted text-foreground';
    }
  };

  const calculateProgress = (chain: ReasoningChain) => {
    const completedSteps = chain.steps.filter(step => 
      step.visualizationData?.status === 'completed'
    ).length;
    return (completedSteps / chain.steps.length) * 100;
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardContent className="aone-flex-center p-aone-xl">
          <Loader2 className="w-8 h-8 animate-spin mr-2" />
          <span>Loading reasoning chains...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full">
        <CardContent className="p-aone-xl">
          <div className="text-center text-red-600">
            <AlertCircle className="w-8 h-8 mx-auto mb-2" />
            <p>{error}</p>
            <Button onClick={loadReasoningChains} className="mt-4">
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Chain Selection */}
      {chains.length > 1 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="w-5 h-5" />
              Reasoning Chains
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2 flex-wrap">
              {chains.map(chain => (
                <Button
                  key={chain.id}
                  variant={selectedChain?.id === chain.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedChain(chain)}
                  className="flex items-center gap-2"
                >
                  <Badge variant={chain.status === 'completed' ? 'default' : 'secondary'}>
                    {chain.status}
                  </Badge>
                  Chain {chain.id.split('_').pop()}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Visualization */}
      {selectedChain && (
        <Card>
          <CardHeader>
            <CardTitle className="aone-flex-between">
              <div className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                {selectedChain.goal}
              </div>
              <Badge variant={selectedChain.status === 'completed' ? 'default' : 'secondary'}>
                {selectedChain.status}
              </Badge>
            </CardTitle>
            <Progress value={calculateProgress(selectedChain)} className="w-full" />
            <p className="text-aone-sm text-muted-foreground">
              {selectedChain.steps.filter(s => s.visualizationData?.status === 'completed').length} of {selectedChain.steps.length} steps completed
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-aone-md">
              {selectedChain.steps.map(step => (
                <TooltipProvider key={step.id}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Card 
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedStep?.id === step.id ? 'ring-2 ring-blue-500' : ''
                        }`}
                        onClick={() => {
                          setSelectedStep(step);
                          onStepClick?.(step);
                        }}
                      >
                        <CardContent className="aone-spacing-md">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-center gap-2">
                              {getStepIcon(step)}
                              <Badge variant="outline" className="text-xs">
                                {step.type}
                              </Badge>
                            </div>
                            {step.visualizationData?.complexity && (
                              <Badge className={`text-xs ${getComplexityColor(step.visualizationData.complexity)}`}>
                                {step.visualizationData.complexity}
                              </Badge>
                            )}
                          </div>
                          <p className="text-aone-sm font-aone-medium mb-2 line-clamp-2">
                            {step.description}
                          </p>
                          <div className="aone-flex-between text-xs text-gray-500">
                            <span>Confidence: {(step.confidence * 100).toFixed(1)}%</span>
                            {step.visualizationData?.processingTime && (
                              <span>{step.visualizationData.processingTime}ms</span>
                            )}
                          </div>
                          {step.gptO1Reasoning && (
                            <div className="mt-2 flex items-center gap-1 text-xs text-aone-sage">
                              <Lightbulb className="w-3 h-3" />
                              GPT-o1 Enhanced
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="max-w-xs">
                        <p className="font-aone-medium">{step.description}</p>
                        {step.gptO1Reasoning && (
                          <div className="mt-2 text-xs">
                            <p>Complexity Score: {(step.gptO1Reasoning.complexityScore * 100).toFixed(1)}%</p>
                            <p>Internal Thoughts: {step.gptO1Reasoning.internalThoughts.length}</p>
                            <p>Reasoning Steps: {step.gptO1Reasoning.reasoningPath.length}</p>
                          </div>
                        )}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ReasoningChainVisualization;
