import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertCircle, Settings, Palette, Layout } from 'lucide-react';
import { quoteTemplateService, QuoteTemplate, CustomerSegment, CreateTemplateRequest, UpdateTemplateRequest } from '@/services/quoteTemplateService';

const templateFormSchema = z.object({
  template_code: z.string().min(3, 'Template code must be at least 3 characters').max(50),
  template_name: z.string().min(3, 'Template name must be at least 3 characters').max(100),
  description: z.string().max(500).optional(),
  customer_segment_id: z.number().optional(),
  is_default: z.boolean().default(false),
  template_config: z.object({
    format: z.enum(['basic', 'detailed', 'professional', 'commercial']),
    include_images: z.boolean().default(false),
    include_alternatives: z.boolean().default(true),
    include_warranty: z.boolean().default(false),
    include_3d_renders: z.boolean().default(false),
    include_compliance: z.boolean().default(false),
  }),
  sections_config: z.object({
    header: z.object({
      enabled: z.boolean().default(true),
      show_logo: z.boolean().default(true),
      show_certifications: z.boolean().default(false),
    }),
    pricing: z.object({
      enabled: z.boolean().default(true),
      show_breakdown: z.boolean().default(true),
      show_alternatives: z.boolean().default(false),
      show_labor_breakdown: z.boolean().default(false),
    }),
    materials: z.object({
      enabled: z.boolean().default(true),
    }),
    terms: z.object({
      enabled: z.boolean().default(true),
      simplified: z.boolean().default(false),
      detailed: z.boolean().default(true),
      commercial: z.boolean().default(false),
    }),
    delivery: z.object({
      enabled: z.boolean().default(true),
    }),
  }),
  styling_config: z.object({
    colors: z.object({
      primary: z.string().default('hsl(var(--status-info))'),
      secondary: z.string().default('rgb(var(--aone-soft-gray))'),
      accent: z.string().default('hsl(var(--status-warning))'),
    }),
    fonts: z.object({
      heading: z.string().default('Inter'),
      body: z.string().default('Inter'),
    }),
    layout: z.object({
      margins: z.number().default(20),
      spacing: z.number().default(10),
      columns: z.number().default(1),
    }),
  }),
});

type TemplateFormData = z.infer<typeof templateFormSchema>;

interface QuoteTemplateFormProps {
  template?: QuoteTemplate | null;
  segments: CustomerSegment[];
  mode: 'create' | 'edit' | 'duplicate';
  onSubmit: () => void;
  onCancel: () => void;
}

export const QuoteTemplateForm: React.FC<QuoteTemplateFormProps> = ({
  template,
  segments,
  mode,
  onSubmit,
  onCancel
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<TemplateFormData>({
    resolver: zodResolver(templateFormSchema),
    defaultValues: {
      template_code: mode === 'duplicate' ? `${template?.template_code}_copy` : template?.template_code || '',
      template_name: mode === 'duplicate' ? `${template?.template_name} (Copy)` : template?.template_name || '',
      description: template?.description || '',
      customer_segment_id: template?.customer_segment_id,
      is_default: mode === 'duplicate' ? false : template?.is_default || false,
      template_config: template?.template_config || quoteTemplateService.getDefaultTemplateConfig('basic'),
      sections_config: template?.sections_config || quoteTemplateService.getDefaultSectionsConfig(),
      styling_config: template?.styling_config || quoteTemplateService.getDefaultStylingConfig(),
    },
  });

  const handleSubmit = async (data: TemplateFormData) => {
    try {
      setLoading(true);
      setError(null);

      if (mode === 'create' || mode === 'duplicate') {
        const createData: CreateTemplateRequest = {
          template_code: data.template_code,
          template_name: data.template_name,
          description: data.description,
          customer_segment_id: data.customer_segment_id,
          is_default: data.is_default,
          template_config: data.template_config,
          sections_config: data.sections_config,
          styling_config: data.styling_config,
        };
        await quoteTemplateService.createTemplate(createData);
      } else if (mode === 'edit' && template) {
        const updateData: UpdateTemplateRequest = {
          template_name: data.template_name,
          description: data.description,
          customer_segment_id: data.customer_segment_id,
          is_default: data.is_default,
          template_config: data.template_config,
          sections_config: data.sections_config,
          styling_config: data.styling_config,
        };
        await quoteTemplateService.updateTemplate(template.id, updateData);
      }

      onSubmit();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save template');
    } finally {
      setLoading(false);
    }
  };

  const getTitle = () => {
    switch (mode) {
      case 'create': return 'Create Quote Template';
      case 'edit': return 'Edit Quote Template';
      case 'duplicate': return 'Duplicate Quote Template';
      default: return 'Quote Template';
    }
  };

  const formatOptions = [
    { value: 'basic', label: 'Basic', description: 'Simple layout with essential information' },
    { value: 'detailed', label: 'Detailed', description: 'Comprehensive layout with full breakdown' },
    { value: 'professional', label: 'Professional', description: 'Premium layout with advanced features' },
    { value: 'commercial', label: 'Commercial', description: 'Business-focused layout with compliance info' },
  ];

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
          <DialogDescription>
            {mode === 'create' && 'Create a new quote template for your customer segments.'}
            {mode === 'edit' && 'Modify the existing quote template configuration.'}
            {mode === 'duplicate' && 'Create a copy of the existing template with modifications.'}
          </DialogDescription>
        </DialogHeader>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="basic">Basic Info</TabsTrigger>
                <TabsTrigger value="config">Configuration</TabsTrigger>
                <TabsTrigger value="sections">Sections</TabsTrigger>
                <TabsTrigger value="styling">Styling</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <div className="grid grid-cols-2 gap-aone-md">
                  <FormField
                    control={form.control}
                    name="template_code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Template Code</FormLabel>
                        <FormControl>
                          <Input 
                            {...field} 
                            placeholder="e.g., LUXURY_RESIDENTIAL"
                            disabled={mode === 'edit'}
                          />
                        </FormControl>
                        <FormDescription>
                          Unique identifier for the template (cannot be changed after creation)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="template_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Template Name</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="e.g., Luxury Residential Quote" />
                        </FormControl>
                        <FormDescription>
                          Display name for the template
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea 
                          {...field} 
                          placeholder="Describe the purpose and target use case for this template..."
                          rows={3}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-aone-md">
                  <FormField
                    control={form.control}
                    name="customer_segment_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Customer Segment</FormLabel>
                        <Select 
                          onValueChange={(value) => field.onChange(value ? parseInt(value) : undefined)}
                          value={field.value?.toString()}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a customer segment" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="">No specific segment</SelectItem>
                            {segments.map((segment) => (
                              <SelectItem key={segment.id} value={segment.id.toString()}>
                                {segment.segment_name}
                                <Badge className="ml-2 text-xs">
                                  {segment.target_market}
                                </Badge>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="is_default"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-aone-lg border aone-spacing-md">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Default Template</FormLabel>
                          <FormDescription>
                            Use this as the default template for the selected customer segment
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>

              <TabsContent value="config" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Settings className="h-4 w-4 mr-2" />
                      Template Configuration
                    </CardTitle>
                    <CardDescription>
                      Configure the overall format and features of the template
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="template_config.format"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Template Format</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select template format" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {formatOptions.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  <div>
                                    <div className="font-aone-medium">{option.label}</div>
                                    <div className="text-aone-sm text-aone-soft-gray">{option.description}</div>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-aone-md">
                      <FormField
                        control={form.control}
                        name="template_config.include_images"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-aone-lg border aone-spacing-sm">
                            <div className="space-y-0.5">
                              <FormLabel>Include Images</FormLabel>
                              <FormDescription className="text-xs">
                                Add product and material images
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="template_config.include_alternatives"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-aone-lg border aone-spacing-sm">
                            <div className="space-y-0.5">
                              <FormLabel>Include Alternatives</FormLabel>
                              <FormDescription className="text-xs">
                                Show cost-saving alternatives
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="template_config.include_warranty"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-aone-lg border aone-spacing-sm">
                            <div className="space-y-0.5">
                              <FormLabel>Include Warranty</FormLabel>
                              <FormDescription className="text-xs">
                                Add warranty information
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="template_config.include_3d_renders"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-aone-lg border aone-spacing-sm">
                            <div className="space-y-0.5">
                              <FormLabel>Include 3D Renders</FormLabel>
                              <FormDescription className="text-xs">
                                Add 3D visualization images
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="sections" className="space-y-4">
                {/* Sections configuration will be added in the next part */}
                <div className="text-center py-8 text-aone-soft-gray">
                  Sections configuration panel will be implemented here
                </div>
              </TabsContent>

              <TabsContent value="styling" className="space-y-4">
                {/* Styling configuration will be added in the next part */}
                <div className="text-center py-8 text-aone-soft-gray">
                  Styling configuration panel will be implemented here
                </div>
              </TabsContent>
            </Tabs>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                {mode === 'create' ? 'Create Template' : 'Save Changes'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
