
import Header from "@/components/Header";
import HeroSection from "@/components/HeroSection";
import UploadSection from "@/components/UploadSection";
import DashboardPreview from "@/components/DashboardPreview";
import FeaturesSection from "@/components/FeaturesSection";
import FeaturesShowcase from "@/components/FeaturesShowcase";

const Index = () => {
  return (
    <div className="min-h-screen bg-aone-warm-white dark:bg-aone-warm-white">
      <Header />
      <HeroSection />
      <UploadSection />
      <DashboardPreview />

      {/* Enhanced Features Showcase */}
      <section className="py-16 bg-aone-cream/20 dark:bg-aone-cream/10">
        <div className="aone-container">
          <FeaturesShowcase />
        </div>
      </section>

      <FeaturesSection />
      
      {/* Enhanced Footer */}
      <footer className="bg-aone-charcoal text-white py-16">
        <div className="aone-container lg:px-8">
          <div className="grid md:grid-cols-4 gap-8 lg:gap-12">
            <div>
              <div className="flex items-center space-x-3 mb-aone-lg">
                <div className="w-10 h-10 bg-gradient-to-br from-aone-sage to-aone-sage-light rounded-aone-xl aone-flex-center shadow-lg">
                  <span className="text-white font-bold text-aone-lg">B</span>
                </div>
                <h3 className="text-2xl font-light tracking-wide">Blackveil Design Mind</h3>
              </div>
              <p className="text-gray-300 leading-relaxed">
                Revolutionizing kitchen design analysis with cutting-edge AI technology and enterprise-grade precision.
              </p>
            </div>
            
            <div>
              <h4 className="font-aone-semibold mb-aone-md">Product</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="aone-micro-interaction hover:text-white transition-colors">Features</a></li>
                <li><a href="#" className="aone-micro-interaction hover:text-white transition-colors">Pricing</a></li>
                <li><a href="#" className="aone-micro-interaction hover:text-white transition-colors">API</a></li>
                <li><a href="#" className="aone-micro-interaction hover:text-white transition-colors">Integrations</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-aone-semibold mb-aone-md">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="aone-micro-interaction hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="aone-micro-interaction hover:text-white transition-colors">Blog</a></li>
                <li><a href="#" className="aone-micro-interaction hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="aone-micro-interaction hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-aone-semibold mb-aone-md">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="aone-micro-interaction hover:text-white transition-colors">Documentation</a></li>
                <li><a href="#" className="aone-micro-interaction hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="aone-micro-interaction hover:text-white transition-colors">Status</a></li>
                <li><a href="#" className="aone-micro-interaction hover:text-white transition-colors">Security</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-700 mt-12 pt-8 text-center text-gray-300">
            <p>&copy; 2024 Blackveil Design Mind. All rights reserved. Built for New Zealand kitchen professionals.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
