import React from 'react';
import Header from '@/components/Header';
import FeaturesShowcase from '@/components/FeaturesShowcase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Brain,
  Zap,
  Target,
  Award,
  TrendingUp,
  Shield,
  Globe,
  Users,
  BarChart3,
  Smartphone,
  ArrowRight,
  CheckCircle
} from 'lucide-react';
import { Link } from 'react-router-dom';

const Features: React.FC = () => {
  const competitiveAdvantages = [
    {
      title: "Superior AI Integration",
      description: "Dual GPT-4o + GPT-o1 architecture with 88.2% accuracy",
      icon: <Brain className="w-6 h-6 text-aone-sage" />,
      comparison: "vs. Cabinet Vision Pro's basic automation"
    },
    {
      title: "Real-time 3D Reconstruction",
      description: "Interactive Three.js visualization with spatial analysis",
      icon: <Zap className="w-6 h-6 text-green-600" />,
      comparison: "vs. Winner <PERSON><PERSON>'s static 2D layouts"
    },
    {
      title: "Production-Grade Performance",
      description: "91.7% test success rate with enterprise scalability",
      icon: <Target className="w-6 h-6 text-purple-600" />,
      comparison: "vs. competitors' limited reliability"
    },
    {
      title: "Advanced Collaboration",
      description: "Multi-user real-time editing with role-based permissions",
      icon: <Users className="w-6 h-6 text-orange-600" />,
      comparison: "vs. desktop-only solutions"
    }
  ];

  const technicalSpecs = [
    { label: "AI Models", value: "GPT-4o + GPT-o1 + o4-mini" },
    { label: "Analysis Accuracy", value: "88.2% average confidence" },
    { label: "Test Success Rate", value: "91.7% (62+ comprehensive tests)" },
    { label: "Processing Speed", value: "< 60 seconds per analysis" },
    { label: "3D Reconstruction", value: "Real-time Three.js rendering" },
    { label: "Supported Formats", value: "PDF, JPG, PNG (up to 50MB)" },
    { label: "Hardware Database", value: "Blum, Hettich, Grass, Salice, Hafele" },
    { label: "Regional Pricing", value: "5 US markets + NZD pricing" },
    { label: "Concurrent Users", value: "1000+ with horizontal scaling" },
    { label: "API Call Reduction", value: "60-80% through intelligent caching" },
    { label: "Mobile Support", value: "PWA with offline capabilities" },
    { label: "Accessibility", value: "WCAG 2.1 AA compliant" }
  ];

  return (
    <div className="min-h-screen bg-muted">
      <Header />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-blue-700 to-teal-600 text-white py-16">
        <div className="aone-container">
          <div className="max-w-4xl mx-auto text-center">
            <div className="aone-flex-center gap-2 mb-aone-lg">
              <Brain className="w-10 h-10" />
              <h1 className="text-4xl md:text-5xl font-bold">
                World-Class AI Kitchen Analysis
              </h1>
            </div>
            <p className="text-aone-xl md:text-2xl text-blue-100 mb-8">
              Industry-leading cabinet analysis platform with advanced AI integration, 
              3D reconstruction, and comprehensive material recognition capabilities.
            </p>
            <div className="aone-flex-center gap-8 mb-8">
              <div className="text-center">
                <div className="text-aone-3xl font-aone-bold">88.2%</div>
                <div className="text-blue-200">AI Accuracy</div>
              </div>
              <div className="text-center">
                <div className="text-aone-3xl font-aone-bold">91.7%</div>
                <div className="text-blue-200">Test Success</div>
              </div>
              <div className="text-center">
                <div className="text-aone-3xl font-aone-bold">1000+</div>
                <div className="text-blue-200">Users Supported</div>
              </div>
            </div>
            <div className="aone-flex-center gap-aone-md">
              <Link to="/analysis">
                <Button size="lg" className="bg-white text-aone-sage hover:bg-muted">
                  <Zap className="w-5 h-5 mr-2" />
                  Start Analysis
                </Button>
              </Link>
              <Link to="/performance">
                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-aone-sage">
                  <BarChart3 className="w-5 h-5 mr-2" />
                  View Performance
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Competitive Advantages */}
      <section className="py-16">
        <div className="aone-container">
          <div className="text-center mb-12">
            <h2 className="text-aone-3xl font-aone-bold text-foreground mb-aone-md">
              Why Cabinet Insight Pro Leads the Industry
            </h2>
            <p className="text-aone-xl text-muted-foreground max-w-3xl mx-auto">
              Our advanced AI platform surpasses traditional kitchen design software 
              with cutting-edge technology and enterprise-grade reliability.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            {competitiveAdvantages.map((advantage, index) => (
              <Card key={index} className="border-2 aone-hover-lift transition-shadow">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="aone-spacing-xs bg-muted rounded-aone-lg">
                      {advantage.icon}
                    </div>
                    <div>
                      <CardTitle className="text-aone-lg">{advantage.title}</CardTitle>
                      <CardDescription className="text-aone-sm text-green-600 font-aone-medium">
                        {advantage.comparison}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{advantage.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Technical Specifications */}
      <section className="py-16 bg-white">
        <div className="aone-container">
          <div className="text-center mb-12">
            <h2 className="text-aone-3xl font-aone-bold text-foreground mb-aone-md">
              Technical Specifications
            </h2>
            <p className="text-aone-xl text-muted-foreground">
              Production-grade platform built for enterprise performance and reliability
            </p>
          </div>
          
          <div className="max-w-4xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-6 h-6 text-green-600" />
                  Platform Specifications
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-aone-lg">
                  {technicalSpecs.map((spec, index) => (
                    <div key={index} className="aone-flex-between aone-spacing-sm border rounded-aone-lg">
                      <span className="font-aone-medium text-foreground">{spec.label}</span>
                      <span className="text-foreground font-aone-semibold">{spec.value}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Features Showcase */}
      <section className="py-16 bg-muted">
        <div className="aone-container">
          <FeaturesShowcase />
        </div>
      </section>

      {/* Call to Action */}
      <section className="aone-section bg-gradient-to-r from-aone-sage to-aone-sage-light text-white">
        <div className="aone-container text-center">
          <h2 className="text-aone-3xl font-aone-bold mb-aone-md">
            Ready to Transform Your Kitchen Analysis Workflow?
          </h2>
          <p className="text-aone-xl text-aone-cream mb-8 max-w-2xl mx-auto">
            Join the future of kitchen design with our AI-powered analysis platform.
            Experience the difference that world-class technology makes.
          </p>
          <div className="aone-flex-center gap-aone-md">
            <Link to="/analysis">
              <Button size="lg" className="aone-button-secondary bg-white text-aone-sage hover:bg-aone-cream aone-micro-interaction">
                <Zap className="w-5 h-5 mr-2" />
                Start Free Analysis
              </Button>
            </Link>
            <Link to="/projects">
              <Button variant="outline" size="lg" className="aone-button-ghost border-white text-white hover:bg-white hover:text-aone-sage aone-micro-interaction">
                <Users className="w-5 h-5 mr-2" />
                Explore Collaboration
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Features;
