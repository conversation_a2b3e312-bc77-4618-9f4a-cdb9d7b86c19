import { useLocation } from "react-router-dom";
import { useEffect } from "react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen aone-flex-center bg-muted">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-aone-md">404</h1>
        <p className="text-aone-xl text-muted-foreground mb-aone-md">Oops! Page not found</p>
        <a href="/" className="text-blue-500 aone-micro-interaction hover:text-aone-sage-dark underline">
          Return to Home
        </a>
      </div>
    </div>
  );
};

export default NotFound;
