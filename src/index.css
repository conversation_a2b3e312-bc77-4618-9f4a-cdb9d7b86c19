/* =============================================================================
   BLACKVEIL DESIGN MIND - A.ONE INSPIRED DESIGN SYSTEM
   Comprehensive Theme Foundation with Centralized CSS Custom Properties
   ============================================================================= */

/* CSS Validation Test Class */
.css-validation-test {
  --validation-loaded: 'true';
}

/* =============================================================================
   CORE A.ONE COLOR PALETTE
   Primary sage green (#6B7A4F) with systematic light/dark variants
   ============================================================================= */

:root {
  /* A.ONE Primary Color System - RGB values for better Tailwind integration */
  --aone-sage: 107 122 79;           /* Primary sage green (#6B7A4F) */
  --aone-sage-light: 139 154 116;    /* Lighter sage variant (#8B9A74) */
  --aone-sage-dark: 75 90 52;        /* Darker sage variant (#4B5A34) */
  
  /* A.ONE Neutral Color System */
  --aone-cream: 248 245 240;         /* Light cream (#F8F5F0) */
  --aone-warm-white: 252 251 249;    /* Warm white (#FCFBF9) */
  --aone-charcoal: 46 46 46;         /* Dark charcoal (#2E2E2E) */
  --aone-soft-gray: 156 163 175;     /* Soft gray (#9CA3AF) */
  
  /* Design Token System - Spacing Scale */
  --spacing-xs: 0.25rem;    /* 4px */
  --spacing-sm: 0.5rem;     /* 8px */
  --spacing-md: 1rem;       /* 16px */
  --spacing-lg: 1.5rem;     /* 24px */
  --spacing-xl: 2rem;       /* 32px */
  --spacing-2xl: 3rem;      /* 48px */
  --spacing-3xl: 4rem;      /* 64px */

  /* Design Token System - Border Radius Scale */
  --radius-sm: 0.25rem;     /* 4px */
  --radius-md: 0.5rem;      /* 8px */
  --radius-lg: 0.75rem;     /* 12px */
  --radius-xl: 1rem;        /* 16px */

  /* Design Token System - Typography Scale */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */

  /* Design Token System - Line Height Scale */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Design Token System - Font Weight Scale */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Design Token System - Shadow Scale */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Typography Scale */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */

  /* Animation Timing */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;
  
  /* Easing Functions */
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);

  /* Chart and Visualization Colors */
  --chart-primary: var(--aone-sage);
  --chart-secondary: var(--preset-accent);
  --chart-success: var(--status-success);
  --chart-warning: var(--status-warning);
  --chart-error: var(--status-error);
  --chart-info: var(--status-info);
  
  /* Collaboration User Colors */
  --collab-user-1: var(--status-info);
  --collab-user-2: var(--status-success);
  --collab-user-3: var(--status-warning);
  --collab-user-4: var(--status-error);
  --collab-user-5: var(--preset-accent);
  --collab-user-6: 139 69 19; /* Brown */
  --collab-user-7: 168 85 247; /* Purple */
  --collab-user-8: 236 72 153; /* Pink */
}

/* =============================================================================
   DARK MODE COLOR VARIANTS
   Enhanced contrast ratios for accessibility compliance
   ============================================================================= */

.dark {
  /* A.ONE Dark Mode Color System */
  --aone-sage: 139 154 116;          /* Enhanced sage for dark mode */
  --aone-sage-light: 155 170 132;    /* Lighter sage for dark mode */
  --aone-sage-dark: 107 122 79;      /* Darker sage for dark mode */
  
  /* A.ONE Dark Mode Neutrals */
  --aone-cream: 30 30 30;            /* Dark cream background */
  --aone-warm-white: 24 24 27;       /* Dark warm background */
  --aone-charcoal: 212 212 216;      /* Light charcoal for text */
  --aone-soft-gray: 113 113 122;     /* Muted gray for dark mode */
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* =============================================================================
   SHADCN/UI BASE THEME INTEGRATION
   Seamless integration with existing component library
   ============================================================================= */

@layer base {
  :root {
    /* Base Shadcn Colors - Light Mode */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: var(--aone-sage);
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: var(--aone-sage-light);
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: var(--aone-sage);
    --radius: 0.5rem;
  }

  .dark {
    /* Base Shadcn Colors - Dark Mode */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: var(--aone-sage);
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: var(--aone-sage-light);
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: var(--aone-sage);
  }
}

/* =============================================================================
   SEMANTIC STATUS COLORS
   Consistent status indication across the application
   ============================================================================= */

@layer base {
  :root {
    /* Status Colors - Light Mode */
    --status-success: 142 76% 36%;
    --status-success-foreground: 355 7% 97%;
    --status-warning: 38 92% 50%;
    --status-warning-foreground: 48 96% 89%;
    --status-error: 0 84% 60%;
    --status-error-foreground: 210 40% 98%;
    --status-info: 221 83% 53%;
    --status-info-foreground: 210 40% 98%;
    
    /* Confidence Score Colors */
    --confidence-high: 142 76% 36%;
    --confidence-medium: 38 92% 50%;
    --confidence-low: 0 84% 60%;
  }

  .dark {
    /* Status Colors - Dark Mode */
    --status-success: 142 71% 45%;
    --status-success-foreground: 137 72% 94%;
    --status-warning: 38 92% 50%;
    --status-warning-foreground: 48 96% 89%;
    --status-error: 0 72% 51%;
    --status-error-foreground: 210 40% 98%;
    --status-info: 221 83% 53%;
    --status-info-foreground: 210 40% 98%;
    
    /* Confidence Score Colors - Dark Mode */
    --confidence-high: 142 71% 45%;
    --confidence-medium: 38 92% 50%;
    --confidence-low: 0 72% 51%;
  }
}

/* =============================================================================
   ADVANCED THEME TRANSITION SYSTEM
   Configurable transitions with animation presets and accessibility support
   ============================================================================= */

* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: var(--ease-in-out);
  transition-duration: var(--transition-fast);
}

.theme-transitioning * {
  transition-duration: var(--theme-transition-duration, var(--transition-normal));
  transition-timing-function: var(--theme-transition-easing, var(--ease-in-out));
}

/* Reduced motion support */
.reduced-motion *,
.reduced-motion .theme-transitioning * {
  transition-duration: 0ms !important;
  animation-duration: 0ms !important;
  animation-iteration-count: 1 !important;
}

/* Animation preset specific transitions */
.theme-transitioning.animation-slide-transform * {
  transition-property: transform, opacity, background-color, border-color, color;
}

.theme-transitioning.animation-zoom-effect * {
  transition-property: transform, opacity, background-color, box-shadow;
}

/* =============================================================================
   UTILITY CLASSES
   Reusable classes to eliminate style duplication
   ============================================================================= */

@layer utilities {
  /* A.ONE Color Utilities */
  .text-aone-sage { color: rgb(var(--aone-sage)); }
  .bg-aone-sage { background-color: rgb(var(--aone-sage)); }
  .border-aone-sage { border-color: rgb(var(--aone-sage)); }
  
  .text-aone-cream { color: rgb(var(--aone-cream)); }
  .bg-aone-cream { background-color: rgb(var(--aone-cream)); }
  
  .text-aone-charcoal { color: rgb(var(--aone-charcoal)); }
  .bg-aone-charcoal { background-color: rgb(var(--aone-charcoal)); }
  
  /* Glass Effect Utilities */
  .glass-effect {
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .dark .glass-effect {
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  /* Focus Ring Utilities */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-aone-sage;
  }
  
  /* Micro-interaction Utilities */
  .micro-interaction {
    @apply transition-all duration-150 ease-in-out;
  }

  .micro-interaction:hover {
    transform: translateY(-1px);
  }
}

/* =============================================================================
   A.ONE COMPONENT STYLES
   Enterprise-grade component styling with consistent design patterns
   ============================================================================= */

@layer components {
  /* =============================================================================
     A.ONE COMPONENT LIBRARY - ENTERPRISE DESIGN SYSTEM
     Comprehensive utility classes for consistent design patterns
     ============================================================================= */

  /* A.ONE Banner */
  .aone-banner {
    @apply bg-aone-sage text-white text-center py-3 px-4 text-sm font-medium;
  }

  /* A.ONE Header */
  .aone-header {
    @apply bg-background/95 backdrop-blur-md shadow-sm border-b border-border;
  }

  /* A.ONE Logo */
  .aone-logo {
    @apply text-2xl font-light tracking-wider text-foreground;
  }

  .aone-logo-accent {
    @apply text-xs font-normal tracking-widest text-muted-foreground uppercase;
  }

  /* A.ONE Navigation */
  .aone-nav-link {
    @apply text-muted-foreground hover:text-foreground font-medium transition-all duration-200 tracking-wide hover:scale-105;
  }

  .aone-nav-link-active {
    @apply aone-nav-link text-aone-sage font-semibold;
  }

  .aone-nav-enterprise {
    @apply aone-nav-link bg-background/80 backdrop-blur-sm rounded-md px-3 py-2 border border-border/50;
  }

  /* =============================================================================
     A.ONE BUTTON SYSTEM - COMPREHENSIVE BUTTON PATTERNS
     ============================================================================= */

  .aone-button-primary {
    @apply bg-aone-sage hover:bg-aone-sage-dark text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg focus:ring-2 focus:ring-aone-sage/50 focus:outline-none;
  }

  .aone-button-secondary {
    @apply bg-transparent hover:bg-aone-sage/10 text-aone-sage border border-aone-sage font-medium px-6 py-3 rounded-lg transition-all duration-200 hover:shadow-md focus:ring-2 focus:ring-aone-sage/50 focus:outline-none;
  }

  .aone-button-ghost {
    @apply bg-transparent hover:bg-aone-sage/5 text-muted-foreground hover:text-aone-sage font-medium px-4 py-2 rounded-md transition-all duration-200 focus:ring-2 focus:ring-aone-sage/30 focus:outline-none;
  }

  .aone-button-enterprise {
    @apply aone-button-primary bg-gradient-to-r from-aone-sage to-aone-sage-light hover:from-aone-sage-dark hover:to-aone-sage shadow-lg hover:shadow-xl;
  }

  /* =============================================================================
     A.ONE CARD SYSTEM - ENTERPRISE CARD PATTERNS
     ============================================================================= */

  .aone-card {
    @apply bg-card border border-border rounded-lg shadow-sm transition-all duration-200;
  }

  .aone-card-interactive {
    @apply aone-card hover:shadow-md hover:border-aone-sage/30 cursor-pointer transform hover:scale-[1.02];
  }

  .aone-card-enterprise {
    @apply aone-card bg-gradient-to-br from-card to-aone-cream/20 shadow-lg hover:shadow-xl border-aone-sage/20;
  }

  .aone-card-glass {
    @apply backdrop-blur-md bg-card/80 border border-border/50 rounded-lg shadow-lg;
  }

  /* =============================================================================
     A.ONE FORM SYSTEM - COMPREHENSIVE FORM PATTERNS
     ============================================================================= */

  .aone-input {
    @apply bg-background border border-input rounded-md px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-aone-sage focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .aone-input-enterprise {
    @apply aone-input bg-gradient-to-r from-background to-aone-cream/10 border-aone-sage/30 focus-visible:border-aone-sage;
  }

  .aone-label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-foreground;
  }

  .aone-label-enterprise {
    @apply aone-label text-aone-charcoal font-semibold tracking-wide;
  }

  /* =============================================================================
     A.ONE LAYOUT SYSTEM - RESPONSIVE LAYOUT PATTERNS
     ============================================================================= */

  .aone-container {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  .aone-section {
    @apply py-16 lg:py-24;
  }

  .aone-grid-responsive {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  /* =============================================================================
     A.ONE MICRO-INTERACTION SYSTEM - SOPHISTICATED ANIMATIONS
     ============================================================================= */

  .aone-micro-interaction {
    @apply transition-all duration-200 ease-in-out;
  }

  .aone-hover-lift {
    @apply aone-micro-interaction hover:transform hover:scale-105 hover:shadow-lg;
  }

  .aone-hover-glow {
    @apply aone-micro-interaction hover:shadow-lg hover:shadow-aone-sage/25;
  }

  .aone-focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-aone-sage/50 focus:ring-offset-2 focus:ring-offset-background;
  }

  .aone-pulse-sage {
    @apply animate-pulse bg-aone-sage/10;
  }

  /* =============================================================================
     A.ONE STATUS SYSTEM - SEMANTIC COLOR PATTERNS
     ============================================================================= */

  .aone-status-success {
    @apply bg-status-success/10 text-status-success border border-status-success/20 rounded-md px-3 py-2;
  }

  .aone-status-warning {
    @apply bg-status-warning/10 text-status-warning border border-status-warning/20 rounded-md px-3 py-2;
  }

  .aone-status-error {
    @apply bg-status-error/10 text-status-error border border-status-error/20 rounded-md px-3 py-2;
  }

  .aone-status-info {
    @apply bg-status-info/10 text-status-info border border-status-info/20 rounded-md px-3 py-2;
  }

  /* =============================================================================
     A.ONE COMPREHENSIVE UTILITY SYSTEM - HIGH ADOPTION CLASSES
     ============================================================================= */

  /* Typography Utilities */
  .aone-heading-primary {
    @apply text-aone-3xl font-aone-bold text-foreground leading-tight tracking-tight;
  }

  .aone-heading-secondary {
    @apply text-aone-2xl font-aone-semibold text-foreground leading-tight;
  }

  .aone-heading-tertiary {
    @apply text-aone-xl font-aone-medium text-foreground;
  }

  .aone-body-primary {
    @apply text-aone-base font-aone-normal text-foreground leading-relaxed;
  }

  .aone-body-secondary {
    @apply text-aone-sm font-aone-normal text-muted-foreground leading-normal;
  }

  .aone-caption {
    @apply text-aone-xs font-aone-medium text-muted-foreground uppercase tracking-wide;
  }

  /* Spacing Utilities */
  .aone-spacing-xs { @apply p-aone-xs; }
  .aone-spacing-sm { @apply p-aone-sm; }
  .aone-spacing-md { @apply p-aone-md; }
  .aone-spacing-lg { @apply p-aone-lg; }
  .aone-spacing-xl { @apply p-aone-xl; }

  .aone-margin-xs { @apply m-aone-xs; }
  .aone-margin-sm { @apply m-aone-sm; }
  .aone-margin-md { @apply m-aone-md; }
  .aone-margin-lg { @apply m-aone-lg; }
  .aone-margin-xl { @apply m-aone-xl; }

  /* Flexbox Utilities */
  .aone-flex-center {
    @apply flex items-center justify-center;
  }

  .aone-flex-between {
    @apply flex items-center justify-between;
  }

  .aone-flex-start {
    @apply flex items-center justify-start;
  }

  .aone-flex-end {
    @apply flex items-center justify-end;
  }

  .aone-flex-col-center {
    @apply flex flex-col items-center justify-center;
  }

  /* Border Utilities */
  .aone-border-light {
    @apply border border-border/30;
  }

  .aone-border-medium {
    @apply border border-border;
  }

  .aone-border-strong {
    @apply border-2 border-aone-sage/30;
  }

  .aone-border-accent {
    @apply border-2 border-aone-sage;
  }

  /* A.ONE Hero Section */
  .aone-hero-section {
    @apply bg-gradient-to-br from-background via-aone-cream/30 to-aone-sage/5 py-24 lg:py-32 relative overflow-hidden;
  }

  /* A.ONE Text Styles */
  .aone-text-primary {
    @apply text-foreground font-semibold;
  }

  .aone-text-secondary {
    @apply text-muted-foreground;
  }

  .aone-text-accent {
    @apply text-aone-sage font-semibold;
  }

  /* A.ONE Hero Components */
  .aone-hero-badge {
    @apply inline-flex items-center px-4 py-2 rounded-full bg-aone-sage/10 border border-aone-sage/20 text-aone-sage font-medium text-sm backdrop-blur-sm;
  }

  .aone-hero-title {
    @apply text-6xl lg:text-7xl font-extralight text-foreground leading-[1.1] tracking-tight;
  }

  .aone-hero-subtitle {
    @apply text-xl lg:text-2xl text-muted-foreground leading-relaxed font-light;
  }

  /* A.ONE Feature Cards */
  .aone-feature-card {
    @apply group relative bg-card/60 backdrop-blur-sm rounded-2xl p-8 lg:p-10 shadow-lg hover:shadow-2xl transition-all duration-500 border border-border hover:border-aone-sage/20 hover:-translate-y-2;
  }

  .aone-feature-icon {
    @apply w-16 h-16 bg-gradient-to-br from-aone-sage/20 to-aone-sage/10 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300;
  }

  /* A.ONE Form Elements */
  .aone-input {
    @apply w-full px-4 py-3 border border-input rounded-xl bg-background/80 backdrop-blur-sm text-foreground placeholder:text-muted-foreground transition-all duration-200;
  }

  .aone-input:focus {
    @apply border-aone-sage ring-2 ring-aone-sage/20 ring-offset-2 outline-none;
  }

  .aone-input:hover {
    @apply border-aone-sage/40;
  }

  /* A.ONE Loading States */
  .aone-loading-skeleton {
    @apply bg-gradient-to-r from-aone-sage/10 via-aone-sage/5 to-aone-sage/10 bg-[length:200%_100%] rounded-lg;
    animation: shimmer 2s ease-in-out infinite;
  }

  .aone-loading-spinner {
    @apply text-aone-sage;
    animation: spin 1s linear infinite;
  }


}

/* =============================================================================
   ANIMATIONS AND KEYFRAMES
   Smooth, professional animations for enhanced user experience
   ============================================================================= */

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* =============================================================================
   RESPONSIVE UTILITIES
   Mobile-first responsive design utilities
   ============================================================================= */

@layer utilities {
  /* Animation Utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  .animate-pulse-gentle {
    animation: pulse 2s infinite;
  }

  /* Mobile-specific utilities */
  .mobile-safe-area {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }

  .mobile-viewport {
    min-height: 100vh;
    min-height: -webkit-fill-available;
  }

  /* Touch-friendly utilities */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Accessibility utilities */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Focus utilities */
  .focus-visible-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-aone-sage focus-visible:ring-offset-2;
  }
}

/* =============================================================================
   THEME PRESET STYLES
   Preset-specific styling variations
   ============================================================================= */

/* Professional Preset - Enhanced for business environments */
.preset-professional {
  --preset-accent: var(--aone-accent, 100 116 139);
  --preset-accent-light: var(--aone-accent-light, 148 163 184);
  --preset-accent-dark: var(--aone-accent-dark, 71 85 105);
}

/* Creative Preset - Warm and inspiring */
.preset-creative {
  --preset-accent: var(--aone-accent, 251 146 60);
  --preset-accent-light: var(--aone-accent-light, 253 186 116);
  --preset-accent-dark: var(--aone-accent-dark, 234 88 12);
}

/* Minimal Preset - High contrast and clean */
.preset-minimal {
  --preset-accent: var(--aone-accent, 0 0 0);
  --preset-accent-light: var(--aone-accent-light, 64 64 64);
  --preset-accent-dark: var(--aone-accent-dark, 0 0 0);
}

.preset-minimal.dark {
  --preset-accent: var(--aone-accent, 255 255 255);
  --preset-accent-light: var(--aone-accent-light, 229 229 229);
  --preset-accent-dark: var(--aone-accent-dark, 255 255 255);
}

/* Enterprise Preset - Corporate and professional */
.preset-enterprise {
  --preset-accent: var(--aone-accent, 37 99 235);
  --preset-accent-light: var(--aone-accent-light, 96 165 250);
  --preset-accent-dark: var(--aone-accent-dark, 29 78 216);
}

/* =============================================================================
   ACCESSIBILITY ENHANCEMENTS
   High contrast, focus enhancement, and reduced motion support
   ============================================================================= */

/* High Contrast Mode */
.high-contrast {
  --contrast-boost: 1.3;
}

.high-contrast .aone-card {
  border-width: 2px;
  border-color: rgb(var(--aone-sage));
}

.high-contrast .aone-button-primary {
  border: 2px solid rgb(var(--aone-sage-dark));
  font-weight: 700;
}

.high-contrast .aone-button-secondary {
  border-width: 3px;
  font-weight: 700;
}

.high-contrast .aone-nav-link {
  font-weight: 600;
}

.high-contrast .aone-nav-link:hover {
  background-color: rgb(var(--aone-sage) / 0.2);
  border: 1px solid rgb(var(--aone-sage));
}

/* Focus Enhancement Mode */
.focus-enhanced *:focus,
.focus-enhanced *:focus-visible {
  outline: 3px solid rgb(var(--aone-sage)) !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 6px rgb(var(--aone-sage) / 0.2) !important;
}

.focus-enhanced .aone-button-primary:focus,
.focus-enhanced .aone-button-secondary:focus {
  transform: scale(1.05);
  box-shadow: 0 0 0 4px rgb(var(--aone-sage) / 0.3), 0 8px 16px rgb(var(--aone-sage) / 0.2) !important;
}

.focus-enhanced .aone-nav-link:focus {
  background-color: rgb(var(--aone-sage) / 0.15);
  border-radius: 6px;
}

/* Keyboard navigation indicators */
.focus-enhanced [tabindex]:focus::before {
  content: "⌨️";
  position: absolute;
  top: -8px;
  right: -8px;
  background: rgb(var(--aone-sage));
  color: white;
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 4px;
  z-index: 1000;
}

/* =============================================================================
   PRESET-SPECIFIC COMPONENT VARIATIONS
   Component styling that adapts to active preset
   ============================================================================= */

/* Professional preset components */
.preset-professional .aone-feature-card {
  border-color: rgb(var(--preset-accent) / 0.2);
}

.preset-professional .aone-feature-card:hover {
  border-color: rgb(var(--preset-accent) / 0.4);
  box-shadow: 0 10px 25px rgb(var(--preset-accent) / 0.1);
}

/* Creative preset components */
.preset-creative .aone-hero-badge {
  background: linear-gradient(135deg, rgb(var(--aone-sage) / 0.1), rgb(var(--preset-accent) / 0.1));
  border-color: rgb(var(--preset-accent) / 0.3);
}

.preset-creative .aone-button-primary {
  background: linear-gradient(135deg, rgb(var(--aone-sage)), rgb(var(--preset-accent)));
}

.preset-creative .aone-button-primary:hover {
  background: linear-gradient(135deg, rgb(var(--aone-sage-dark)), rgb(var(--preset-accent-dark)));
}

/* Minimal preset components */
.preset-minimal .aone-card {
  border-color: rgb(var(--preset-accent) / 0.8);
  background-color: rgb(var(--aone-warm-white));
}

.preset-minimal .aone-button-primary {
  border: 2px solid rgb(var(--preset-accent));
  background-color: rgb(var(--preset-accent));
}

.preset-minimal .aone-button-secondary {
  border: 2px solid rgb(var(--preset-accent));
  color: rgb(var(--preset-accent));
}

/* Enterprise preset components */
.preset-enterprise .aone-header {
  background: linear-gradient(90deg, rgb(var(--aone-cream)), rgb(var(--preset-accent) / 0.05));
}

.preset-enterprise .aone-feature-icon {
  background: linear-gradient(135deg, rgb(var(--aone-sage) / 0.2), rgb(var(--preset-accent) / 0.2));
}

.preset-enterprise .aone-status-info {
  background-color: rgb(var(--preset-accent) / 0.1);
  border-color: rgb(var(--preset-accent) / 0.3);
  color: rgb(var(--preset-accent-dark));
}
