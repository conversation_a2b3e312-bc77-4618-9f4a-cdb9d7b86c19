/**
 * PWA Utilities for Cabinet Insight Pro
 * Handles Progressive Web App installation, updates, and offline capabilities
 */

import React from 'react';

export interface PWAInstallPrompt {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

export interface PWACapabilities {
  isInstallable: boolean;
  isInstalled: boolean;
  isOfflineCapable: boolean;
  hasServiceWorker: boolean;
  supportsNotifications: boolean;
  supportsPushNotifications: boolean;
}

export interface PWAUpdateInfo {
  hasUpdate: boolean;
  isUpdateReady: boolean;
  newVersion?: string;
  currentVersion?: string;
}

class PWAManager {
  private installPrompt: PWAInstallPrompt | null = null;
  private serviceWorkerRegistration: ServiceWorkerRegistration | null = null;
  private updateAvailable = false;
  private isInitialized = false;
  private initializationError: Error | null = null;

  constructor() {
    // Only initialize in browser environment
    if (typeof window !== 'undefined') {
      this.initializePWA().catch((error) => {
        this.initializationError = error;
        console.error('[PWA] Initialization failed:', error);
      });
    }
  }

  /**
   * Initialize PWA functionality
   */
  private async initializePWA(): Promise<void> {
    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined' || typeof navigator === 'undefined') {
        console.warn('[PWA] Not in browser environment, skipping initialization');
        return;
      }

      // Register service worker
      await this.registerServiceWorker();

      // Listen for install prompt
      this.setupInstallPromptListener();

      // Setup update detection
      this.setupUpdateDetection();

      // Setup notification permissions
      this.setupNotifications();

      this.isInitialized = true;
      console.log('[PWA] PWA Manager initialized successfully');
    } catch (error) {
      this.initializationError = error as Error;
      console.error('[PWA] Failed to initialize PWA:', error);
      throw error;
    }
  }

  /**
   * Register service worker
   */
  private async registerServiceWorker(): Promise<void> {
    if (!('serviceWorker' in navigator)) {
      console.warn('[PWA] Service Worker not supported');
      return;
    }

    // Skip service worker registration in development mode to avoid conflicts
    const isDevelopment = import.meta.env.VITE_NODE_ENV === 'development' || import.meta.env.DEV;
    if (isDevelopment) {
      console.log('[PWA] Skipping service worker registration in development mode');
      return;
    }

    try {
      this.serviceWorkerRegistration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
        updateViaCache: 'none'
      });

      console.log('[PWA] Service Worker registered successfully');

      // Listen for service worker updates
      this.serviceWorkerRegistration.addEventListener('updatefound', () => {
        console.log('[PWA] Service Worker update found');
        this.updateAvailable = true;
        this.handleServiceWorkerUpdate();
      });

    } catch (error) {
      console.error('[PWA] Service Worker registration failed:', error);
      // Don't throw error to prevent PWA initialization failure
    }
  }

  /**
   * Setup install prompt listener
   */
  private setupInstallPromptListener(): void {
    window.addEventListener('beforeinstallprompt', (event) => {
      event.preventDefault();
      this.installPrompt = event as any;
      console.log('[PWA] Install prompt available');
      
      // Dispatch custom event for UI components
      window.dispatchEvent(new CustomEvent('pwa-install-available'));
    });

    // Listen for app installation
    window.addEventListener('appinstalled', () => {
      console.log('[PWA] App installed successfully');
      this.installPrompt = null;
      
      // Dispatch custom event
      window.dispatchEvent(new CustomEvent('pwa-installed'));
    });
  }

  /**
   * Setup update detection
   */
  private setupUpdateDetection(): void {
    if (!this.serviceWorkerRegistration) return;

    // Check for updates periodically
    setInterval(() => {
      this.serviceWorkerRegistration?.update();
    }, 60000); // Check every minute

    // Listen for controlling service worker changes
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      console.log('[PWA] Service Worker controller changed - reloading');
      window.location.reload();
    });
  }

  /**
   * Setup notification permissions
   */
  private setupNotifications(): void {
    if (!('Notification' in window)) {
      console.warn('[PWA] Notifications not supported');
      return;
    }

    // Only log current permission status, don't request automatically
    console.log('[PWA] Notification permission status:', Notification.permission);
  }

  /**
   * Request notification permission (must be called from user interaction)
   */
  public async requestNotificationPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      console.warn('[PWA] Notifications not supported');
      return 'denied';
    }

    try {
      const permission = await Notification.requestPermission();
      console.log('[PWA] Notification permission:', permission);
      return permission;
    } catch (error) {
      console.error('[PWA] Failed to request notification permission:', error);
      return 'denied';
    }
  }

  /**
   * Handle service worker updates
   */
  private handleServiceWorkerUpdate(): void {
    if (!this.serviceWorkerRegistration) return;

    const newWorker = this.serviceWorkerRegistration.installing;
    if (!newWorker) return;

    newWorker.addEventListener('statechange', () => {
      if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
        console.log('[PWA] New service worker installed and ready');
        
        // Dispatch update ready event
        window.dispatchEvent(new CustomEvent('pwa-update-ready', {
          detail: { newWorker }
        }));
      }
    });
  }

  /**
   * Check if PWA can be installed
   */
  public canInstall(): boolean {
    if (this.initializationError || typeof window === 'undefined') {
      return false;
    }
    return this.installPrompt !== null;
  }

  /**
   * Trigger PWA installation
   */
  public async install(): Promise<boolean> {
    if (this.initializationError || typeof window === 'undefined') {
      console.warn('[PWA] PWA not properly initialized');
      return false;
    }

    if (!this.installPrompt) {
      console.warn('[PWA] Install prompt not available');
      return false;
    }

    try {
      await this.installPrompt.prompt();
      const choiceResult = await this.installPrompt.userChoice;

      if (choiceResult.outcome === 'accepted') {
        console.log('[PWA] User accepted installation');
        this.installPrompt = null;
        return true;
      } else {
        console.log('[PWA] User dismissed installation');
        return false;
      }
    } catch (error) {
      console.error('[PWA] Installation failed:', error);
      return false;
    }
  }

  /**
   * Check if app is installed
   */
  public isInstalled(): boolean {
    if (typeof window === 'undefined') {
      return false;
    }

    try {
      return window.matchMedia('(display-mode: standalone)').matches ||
             window.matchMedia('(display-mode: fullscreen)').matches ||
             (window.navigator as any).standalone === true;
    } catch (error) {
      console.warn('[PWA] Failed to check installation status:', error);
      return false;
    }
  }

  /**
   * Get PWA capabilities
   */
  public getCapabilities(): PWACapabilities {
    if (typeof window === 'undefined' || typeof navigator === 'undefined') {
      return {
        isInstallable: false,
        isInstalled: false,
        isOfflineCapable: false,
        hasServiceWorker: false,
        supportsNotifications: false,
        supportsPushNotifications: false
      };
    }

    try {
      return {
        isInstallable: this.canInstall(),
        isInstalled: this.isInstalled(),
        isOfflineCapable: 'serviceWorker' in navigator,
        hasServiceWorker: this.serviceWorkerRegistration !== null,
        supportsNotifications: 'Notification' in window,
        supportsPushNotifications: 'PushManager' in window
      };
    } catch (error) {
      console.warn('[PWA] Failed to get capabilities:', error);
      return {
        isInstallable: false,
        isInstalled: false,
        isOfflineCapable: false,
        hasServiceWorker: false,
        supportsNotifications: false,
        supportsPushNotifications: false
      };
    }
  }

  /**
   * Get update information
   */
  public getUpdateInfo(): PWAUpdateInfo {
    const version = import.meta.env.VITE_APP_VERSION || '0.1.0';
    return {
      hasUpdate: this.updateAvailable,
      isUpdateReady: this.serviceWorkerRegistration?.waiting !== null,
      newVersion: version,
      currentVersion: version
    };
  }

  /**
   * Apply pending update
   */
  public async applyUpdate(): Promise<void> {
    if (!this.serviceWorkerRegistration?.waiting) {
      console.warn('[PWA] No pending update available');
      return;
    }

    // Tell the waiting service worker to skip waiting
    this.serviceWorkerRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
  }

  /**
   * Check online status
   */
  public isOnline(): boolean {
    return navigator.onLine;
  }

  /**
   * Show notification
   */
  public async showNotification(title: string, options?: NotificationOptions): Promise<void> {
    if (!('Notification' in window)) {
      console.warn('[PWA] Notifications not supported');
      return;
    }

    if (Notification.permission !== 'granted') {
      console.warn('[PWA] Notification permission not granted');
      return;
    }

    try {
      if (this.serviceWorkerRegistration) {
        await this.serviceWorkerRegistration.showNotification(title, {
          icon: '/icons/icon-192x192.png',
          badge: '/icons/badge-72x72.png',
          vibrate: [100, 50, 100],
          ...options
        });
      } else {
        new Notification(title, options);
      }
    } catch (error) {
      console.error('[PWA] Failed to show notification:', error);
    }
  }

  /**
   * Get service worker registration
   */
  public getServiceWorkerRegistration(): ServiceWorkerRegistration | null {
    return this.serviceWorkerRegistration;
  }
}

// Create singleton instance
export const pwaManager = new PWAManager();

// Utility functions
export const isPWAInstalled = () => pwaManager.isInstalled();
export const canInstallPWA = () => pwaManager.canInstall();
export const installPWA = () => pwaManager.install();
export const getPWACapabilities = () => pwaManager.getCapabilities();
export const getPWAUpdateInfo = () => pwaManager.getUpdateInfo();
export const applyPWAUpdate = () => pwaManager.applyUpdate();
export const isOnline = () => pwaManager.isOnline();
export const requestNotificationPermission = () => pwaManager.requestNotificationPermission();
export const showPWANotification = (title: string, options?: NotificationOptions) =>
  pwaManager.showNotification(title, options);

// React hook for PWA state
export const usePWA = () => {
  const [capabilities, setCapabilities] = React.useState<PWACapabilities>(getPWACapabilities());
  const [updateInfo, setUpdateInfo] = React.useState<PWAUpdateInfo>(getPWAUpdateInfo());
  const [isOnlineState, setIsOnlineState] = React.useState<boolean>(isOnline());

  React.useEffect(() => {
    const updateCapabilities = () => setCapabilities(getPWACapabilities());
    const updateUpdateInfo = () => setUpdateInfo(getPWAUpdateInfo());
    const updateOnlineStatus = () => setIsOnlineState(isOnline());

    // Listen for PWA events
    window.addEventListener('pwa-install-available', updateCapabilities);
    window.addEventListener('pwa-installed', updateCapabilities);
    window.addEventListener('pwa-update-ready', updateUpdateInfo);
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    return () => {
      window.removeEventListener('pwa-install-available', updateCapabilities);
      window.removeEventListener('pwa-installed', updateCapabilities);
      window.removeEventListener('pwa-update-ready', updateUpdateInfo);
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, []);

  return {
    capabilities,
    updateInfo,
    isOnline: isOnlineState,
    install: installPWA,
    applyUpdate: applyPWAUpdate,
    showNotification: showPWANotification
  };
};

export default pwaManager;
