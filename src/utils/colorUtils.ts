// =============================================================================
// BLACKVEIL DESIGN MIND - COLOR UTILITY FUNCTIONS
// Advanced color calculations, contrast ratios, and accessibility compliance
// =============================================================================

import { CustomColor } from '@/types/theme';
import { ACCESSIBILITY_RATIOS, HIGH_CONTRAST_ADJUSTMENTS } from '@/config/themePresets';

// =============================================================================
// COLOR CONVERSION UTILITIES
// =============================================================================

/**
 * Convert HSL to RGB values
 */
export const hslToRgb = (h: number, s: number, l: number): [number, number, number] => {
  h = h / 360;
  s = s / 100;
  l = l / 100;

  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs((h * 6) % 2 - 1));
  const m = l - c / 2;

  let r = 0, g = 0, b = 0;

  if (0 <= h && h < 1/6) {
    r = c; g = x; b = 0;
  } else if (1/6 <= h && h < 2/6) {
    r = x; g = c; b = 0;
  } else if (2/6 <= h && h < 3/6) {
    r = 0; g = c; b = x;
  } else if (3/6 <= h && h < 4/6) {
    r = 0; g = x; b = c;
  } else if (4/6 <= h && h < 5/6) {
    r = x; g = 0; b = c;
  } else if (5/6 <= h && h < 1) {
    r = c; g = 0; b = x;
  }

  return [
    Math.round((r + m) * 255),
    Math.round((g + m) * 255),
    Math.round((b + m) * 255)
  ];
};

/**
 * Convert RGB to HSL values
 */
export const rgbToHsl = (r: number, g: number, b: number): [number, number, number] => {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const diff = max - min;
  const sum = max + min;
  const l = sum / 2;

  let h = 0, s = 0;

  if (diff !== 0) {
    s = l > 0.5 ? diff / (2 - sum) : diff / sum;

    switch (max) {
      case r:
        h = ((g - b) / diff) + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / diff + 2;
        break;
      case b:
        h = (r - g) / diff + 4;
        break;
    }
    h /= 6;
  }

  return [Math.round(h * 360), Math.round(s * 100), Math.round(l * 100)];
};

/**
 * Convert RGB string to RGB values
 */
export const parseRgbString = (rgbString: string): [number, number, number] => {
  const values = rgbString.split(' ').map(v => parseInt(v.trim(), 10));
  return [values[0] || 0, values[1] || 0, values[2] || 0];
};

/**
 * Convert RGB values to RGB string
 */
export const rgbToString = (r: number, g: number, b: number): string => {
  return `${r} ${g} ${b}`;
};

// =============================================================================
// COLOR GENERATION UTILITIES
// =============================================================================

/**
 * Generate complementary colors from a custom color
 */
export const generateComplementaryColors = (customColor: CustomColor): {
  sage: string;
  sageLight: string;
  sageDark: string;
} => {
  const { hue, saturation, lightness } = customColor;
  
  // Base color
  const [r, g, b] = hslToRgb(hue, saturation, lightness);
  
  // Light variant (increase lightness by 15%)
  const lightLightness = Math.min(lightness + 15, 90);
  const [rLight, gLight, bLight] = hslToRgb(hue, saturation, lightLightness);
  
  // Dark variant (decrease lightness by 15%)
  const darkLightness = Math.max(lightness - 15, 10);
  const [rDark, gDark, bDark] = hslToRgb(hue, saturation, darkLightness);

  return {
    sage: rgbToString(r, g, b),
    sageLight: rgbToString(rLight, gLight, bLight),
    sageDark: rgbToString(rDark, gDark, bDark),
  };
};

/**
 * Calculate relative luminance for contrast ratio calculations
 */
export const getRelativeLuminance = (r: number, g: number, b: number): number => {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
};

/**
 * Calculate contrast ratio between two colors
 */
export const getContrastRatio = (color1: string, color2: string): number => {
  const [r1, g1, b1] = parseRgbString(color1);
  const [r2, g2, b2] = parseRgbString(color2);
  
  const lum1 = getRelativeLuminance(r1, g1, b1);
  const lum2 = getRelativeLuminance(r2, g2, b2);
  
  const lighter = Math.max(lum1, lum2);
  const darker = Math.min(lum1, lum2);
  
  return (lighter + 0.05) / (darker + 0.05);
};

/**
 * Check if color combination meets WCAG accessibility standards
 */
export const meetsAccessibilityStandards = (
  foreground: string,
  background: string,
  level: 'AA' | 'AAA' = 'AA',
  size: 'normal' | 'large' = 'normal'
): boolean => {
  const ratio = getContrastRatio(foreground, background);
  
  if (level === 'AAA') {
    return size === 'large' ? ratio >= ACCESSIBILITY_RATIOS.AAA_LARGE : ratio >= ACCESSIBILITY_RATIOS.AAA_NORMAL;
  }
  
  return size === 'large' ? ratio >= ACCESSIBILITY_RATIOS.AA_LARGE : ratio >= ACCESSIBILITY_RATIOS.AA_NORMAL;
};

// =============================================================================
// HIGH CONTRAST UTILITIES
// =============================================================================

/**
 * Apply high contrast adjustments to colors
 */
export const applyHighContrastAdjustments = (
  rgbString: string,
  theme: 'light' | 'dark',
  type: 'background' | 'text' | 'border'
): string => {
  const [r, g, b] = parseRgbString(rgbString);
  const [h, s, l] = rgbToHsl(r, g, b);
  
  const adjustments = HIGH_CONTRAST_ADJUSTMENTS[theme];
  let newLightness = l;
  
  switch (type) {
    case 'background':
      newLightness = l * adjustments.backgroundBoost;
      break;
    case 'text':
      newLightness = l * adjustments.textBoost;
      break;
    case 'border':
      newLightness = l * adjustments.borderBoost;
      break;
  }
  
  // Clamp lightness to valid range
  newLightness = Math.max(0, Math.min(100, newLightness));
  
  const [newR, newG, newB] = hslToRgb(h, s, newLightness);
  return rgbToString(newR, newG, newB);
};

// =============================================================================
// COLOR VALIDATION UTILITIES
// =============================================================================

/**
 * Validate custom color is within allowed range
 */
export const validateCustomColor = (color: CustomColor): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  if (color.hue < 70 || color.hue > 120) {
    errors.push('Hue must be between 70 and 120 degrees');
  }
  
  if (color.saturation < 20 || color.saturation > 40) {
    errors.push('Saturation must be between 20% and 40%');
  }
  
  if (color.lightness < 35 || color.lightness > 55) {
    errors.push('Lightness must be between 35% and 55%');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Ensure color meets minimum contrast requirements
 */
export const ensureMinimumContrast = (
  foreground: string,
  background: string,
  targetRatio: number = ACCESSIBILITY_RATIOS.AA_NORMAL
): string => {
  const currentRatio = getContrastRatio(foreground, background);
  
  if (currentRatio >= targetRatio) {
    return foreground;
  }
  
  // Adjust foreground color to meet contrast requirement
  const [r, g, b] = parseRgbString(foreground);
  const [h, s, l] = rgbToHsl(r, g, b);
  
  // Determine if we need to make it lighter or darker
  const [bgR, bgG, bgB] = parseRgbString(background);
  const bgLuminance = getRelativeLuminance(bgR, bgG, bgB);
  
  let newLightness = l;
  const step = bgLuminance > 0.5 ? -5 : 5; // Darker for light backgrounds, lighter for dark
  
  // Iteratively adjust lightness until we meet the target ratio
  for (let i = 0; i < 20; i++) { // Max 20 iterations to prevent infinite loop
    newLightness += step;
    newLightness = Math.max(0, Math.min(100, newLightness));
    
    const [newR, newG, newB] = hslToRgb(h, s, newLightness);
    const testColor = rgbToString(newR, newG, newB);
    
    if (getContrastRatio(testColor, background) >= targetRatio) {
      return testColor;
    }
    
    // Break if we've reached the limits
    if (newLightness <= 0 || newLightness >= 100) {
      break;
    }
  }
  
  return foreground; // Return original if we couldn't improve it
};

// =============================================================================
// CSS CUSTOM PROPERTY UTILITIES
// =============================================================================

/**
 * Apply colors to CSS custom properties
 */
export const applyCSSCustomProperties = (colors: Record<string, string>): void => {
  const root = document.documentElement;
  
  Object.entries(colors).forEach(([property, value]) => {
    root.style.setProperty(`--${property}`, value);
  });
};

/**
 * Get current CSS custom property value
 */
export const getCSSCustomProperty = (property: string): string => {
  return getComputedStyle(document.documentElement)
    .getPropertyValue(`--${property}`)
    .trim();
};
