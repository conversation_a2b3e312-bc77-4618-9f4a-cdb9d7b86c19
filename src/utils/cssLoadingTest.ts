/**
 * CSS Loading Detection Test Utilities
 * 
 * Provides testing functions to validate the CSS loading detection mechanism
 * and ensure the race condition has been resolved.
 */

export interface CSSLoadingTestResult {
  success: boolean;
  duration: number;
  attempts: number;
  cssPropertiesFound: {
    testValidation: boolean;
    sageColor: boolean;
    backgroundColor: boolean;
    charcoalColor: boolean;
  };
  stylesheetCount: number;
  documentReady: boolean;
  timestamp: string;
  error?: string;
}

/**
 * Test CSS loading detection mechanism
 * Returns detailed results about CSS property availability
 */
export const testCSSLoadingDetection = (): Promise<CSSLoadingTestResult> => {
  return new Promise((resolve) => {
    const startTime = performance.now();
    let attemptCount = 0;

    const runTest = () => {
      attemptCount++;
      
      // Create test element
      const testElement = document.createElement('div');
      testElement.className = 'test-css-properties';
      testElement.style.position = 'absolute';
      testElement.style.top = '-9999px';
      testElement.style.left = '-9999px';
      document.body.appendChild(testElement);

      const computedStyle = getComputedStyle(testElement);
      
      // Check all critical CSS properties
      const testValidation = computedStyle.getPropertyValue('--test-validation').trim();
      const sageColor = computedStyle.getPropertyValue('--aone-sage').trim();
      const backgroundColor = computedStyle.getPropertyValue('--background').trim();
      const charcoalColor = computedStyle.getPropertyValue('--aone-charcoal').trim();
      
      document.body.removeChild(testElement);

      const duration = performance.now() - startTime;
      
      const result: CSSLoadingTestResult = {
        success: testValidation.includes('loaded') && !!sageColor && !!backgroundColor,
        duration,
        attempts: attemptCount,
        cssPropertiesFound: {
          testValidation: testValidation.includes('loaded'),
          sageColor: !!sageColor,
          backgroundColor: !!backgroundColor,
          charcoalColor: !!charcoalColor
        },
        stylesheetCount: document.styleSheets.length,
        documentReady: document.readyState === 'complete',
        timestamp: new Date().toISOString()
      };

      resolve(result);
    };

    runTest();
  });
};

/**
 * Test theme switching functionality after CSS loading
 * Verifies that theme changes work correctly once CSS is loaded
 */
export const testThemeSwitching = async (): Promise<{
  lightThemeTest: boolean;
  darkThemeTest: boolean;
  systemThemeTest: boolean;
  error?: string;
}> => {
  try {
    const root = document.documentElement;
    const originalClasses = Array.from(root.classList);

    // Test light theme
    root.classList.remove('dark', 'light');
    root.classList.add('light');
    
    const lightTestElement = document.createElement('div');
    lightTestElement.style.background = 'hsl(var(--aone-sage))';
    lightTestElement.style.position = 'absolute';
    lightTestElement.style.top = '-9999px';
    document.body.appendChild(lightTestElement);
    
    const lightBg = getComputedStyle(lightTestElement).backgroundColor;
    document.body.removeChild(lightTestElement);
    
    // Test dark theme
    root.classList.remove('light');
    root.classList.add('dark');
    
    const darkTestElement = document.createElement('div');
    darkTestElement.style.background = 'hsl(var(--aone-sage))';
    darkTestElement.style.position = 'absolute';
    darkTestElement.style.top = '-9999px';
    document.body.appendChild(darkTestElement);
    
    const darkBg = getComputedStyle(darkTestElement).backgroundColor;
    document.body.removeChild(darkTestElement);
    
    // Restore original classes
    root.className = '';
    originalClasses.forEach(cls => root.classList.add(cls));

    return {
      lightThemeTest: lightBg !== 'rgba(0, 0, 0, 0)' && lightBg !== 'transparent',
      darkThemeTest: darkBg !== 'rgba(0, 0, 0, 0)' && darkBg !== 'transparent',
      systemThemeTest: lightBg !== darkBg // Should be different colors
    };
  } catch (error) {
    return {
      lightThemeTest: false,
      darkThemeTest: false,
      systemThemeTest: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Comprehensive CSS loading and theme test
 * Runs all tests and provides a complete report
 */
export const runComprehensiveCSSTest = async (): Promise<{
  cssLoadingTest: CSSLoadingTestResult;
  themeSwitchingTest: Awaited<ReturnType<typeof testThemeSwitching>>;
  overallSuccess: boolean;
  recommendations: string[];
}> => {
  console.log('🧪 Running comprehensive CSS loading test...');
  
  const cssLoadingTest = await testCSSLoadingDetection();
  const themeSwitchingTest = await testThemeSwitching();
  
  const overallSuccess = cssLoadingTest.success && 
                        themeSwitchingTest.lightThemeTest && 
                        themeSwitchingTest.darkThemeTest;
  
  const recommendations: string[] = [];
  
  if (!cssLoadingTest.success) {
    recommendations.push('CSS loading detection failed - check CSS file loading and parsing');
  }
  
  if (!cssLoadingTest.cssPropertiesFound.testValidation) {
    recommendations.push('CSS validation property not found - check index.css for .test-css-properties class');
  }
  
  if (!cssLoadingTest.cssPropertiesFound.sageColor) {
    recommendations.push('A.ONE sage color not found - check CSS custom property definitions');
  }
  
  if (!themeSwitchingTest.lightThemeTest || !themeSwitchingTest.darkThemeTest) {
    recommendations.push('Theme switching test failed - check theme CSS rules');
  }
  
  if (cssLoadingTest.duration > 1000) {
    recommendations.push('CSS loading took longer than 1 second - consider optimizing CSS structure');
  }
  
  if (overallSuccess) {
    recommendations.push('✅ All tests passed - CSS loading detection is working correctly');
  }

  console.log('🧪 CSS Test Results:', {
    cssLoadingTest,
    themeSwitchingTest,
    overallSuccess,
    recommendations
  });

  return {
    cssLoadingTest,
    themeSwitchingTest,
    overallSuccess,
    recommendations
  };
};

/**
 * Monitor CSS loading performance over time
 * Useful for detecting performance regressions
 */
export const monitorCSSLoadingPerformance = (samples = 5): Promise<{
  averageDuration: number;
  minDuration: number;
  maxDuration: number;
  successRate: number;
  samples: CSSLoadingTestResult[];
}> => {
  return new Promise(async (resolve) => {
    const results: CSSLoadingTestResult[] = [];
    
    for (let i = 0; i < samples; i++) {
      // Small delay between samples
      if (i > 0) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      const result = await testCSSLoadingDetection();
      results.push(result);
    }
    
    const durations = results.map(r => r.duration);
    const successCount = results.filter(r => r.success).length;
    
    resolve({
      averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      successRate: (successCount / samples) * 100,
      samples: results
    });
  });
};

// Export for browser console testing
if (typeof window !== 'undefined') {
  (window as any).cssLoadingTest = {
    testCSSLoadingDetection,
    testThemeSwitching,
    runComprehensiveCSSTest,
    monitorCSSLoadingPerformance
  };
}
