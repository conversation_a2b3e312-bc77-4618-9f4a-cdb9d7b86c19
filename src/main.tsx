import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import React from 'react'

// Force CSS cache busting for development
if (import.meta.env.DEV) {
  const timestamp = Date.now();
  console.log(`🔄 Development mode: CSS cache bust timestamp ${timestamp}`);
}

// Simple theme initialization
const initializeTheme = () => {
  const savedTheme = localStorage.getItem('blackveil-design-mind-theme') || 'system';
  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  const resolvedTheme = savedTheme === 'system' ? (systemPrefersDark ? 'dark' : 'light') : savedTheme;

  // Apply theme class to document root
  document.documentElement.classList.remove('light', 'dark');
  document.documentElement.classList.add(resolvedTheme);
  document.documentElement.style.colorScheme = resolvedTheme;

  console.log(`🎨 Theme initialized: ${resolvedTheme}`);
};

// Initialize theme immediately
initializeTheme();

// Error Boundary Component
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('React Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
          <h1 style={{ color: '#dc2626' }}>Something went wrong</h1>
          <p>The application encountered an error:</p>
          <pre style={{ background: '#f3f4f6', padding: '10px', borderRadius: '4px' }}>
            {this.state.error?.message}
          </pre>
          <button
            onClick={() => window.location.reload()}
            style={{
              background: '#6B7A4F',
              color: 'white',
              padding: '10px 20px',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Reload Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Simple CSS validation
const validateCSS = () => {
  const testElement = document.createElement('div');
  testElement.className = 'css-validation-test';
  testElement.style.position = 'absolute';
  testElement.style.top = '-9999px';
  document.body.appendChild(testElement);

  const computedStyle = getComputedStyle(testElement);
  const validationLoaded = computedStyle.getPropertyValue('--validation-loaded').trim();

  document.body.removeChild(testElement);

  const isValid = validationLoaded.includes('true');
  console.log(`🎨 CSS validation: ${isValid ? 'SUCCESS' : 'FAILED'}`);

  return isValid;
};

// Simple initialization when DOM is ready
const initializeApp = () => {
  console.log('🚀 Initializing Blackveil Design Mind...');

  // Validate CSS is loaded
  setTimeout(() => {
    const isValid = validateCSS();
    if (isValid) {
      console.log('✅ Application initialized successfully');
    } else {
      console.warn('⚠️ CSS validation failed, but continuing...');
    }
  }, 100);
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', initializeApp);

const root = document.getElementById("root");
if (root) {
  console.log('Root element found, mounting React app...');
  try {
    createRoot(root).render(
      <ErrorBoundary>
        <App />
      </ErrorBoundary>
    );
    console.log('React app mounted successfully');
  } catch (error) {
    console.error('Error mounting React app:', error);
    // Fallback rendering
    createRoot(root).render(
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
        <h1 style={{ color: '#dc2626' }}>Failed to load application</h1>
        <p>Error: {error instanceof Error ? error.message : 'Unknown error'}</p>
        <button onClick={() => window.location.reload()}>Reload Page</button>
      </div>
    );
  }
} else {
  console.error('Root element not found!');
}
