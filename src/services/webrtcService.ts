import Peer, { DataConnection, MediaConnection } from 'peerjs';
import { Socket } from 'socket.io-client';

export interface WebRTCConfig {
  iceServers: RTCIceServer[];
  enableAudio: boolean;
  enableVideo: boolean;
  enableDataChannel: boolean;
  maxRetries: number;
  connectionTimeout: number;
}

export interface CursorData {
  userId: string;
  x: number;
  y: number;
  timestamp: number;
  elementId?: string;
}

export interface VoiceCommentData {
  userId: string;
  commentId: string;
  audioBlob: Blob;
  duration: number;
  timestamp: number;
}

export interface WebRTCConnectionState {
  peerId: string;
  isConnected: boolean;
  connectionType: 'p2p' | 'websocket';
  latency: number;
  lastActivity: number;
}

/**
 * WebRTC Service for Ultra-Low Latency Real-time Communication
 * 
 * Implements Phase 1 of Priority 3 Feature 2 Advanced Collaboration Tools
 * Provides <100ms cursor tracking and <200ms voice transmission
 * Integrates with existing SocketManager for signaling server functionality
 */
export class WebRTCService {
  private peer: Peer | null = null;
  private socket: Socket | null = null;
  private connections: Map<string, DataConnection> = new Map();
  private audioConnections: Map<string, MediaConnection> = new Map();
  private localStream: MediaStream | null = null;
  private config: WebRTCConfig;
  private isInitialized = false;
  private connectionStates: Map<string, WebRTCConnectionState> = new Map();
  private cursorCallbacks: Set<(data: CursorData) => void> = new Set();
  private voiceCallbacks: Set<(data: VoiceCommentData) => void> = new Set();
  private connectionCallbacks: Set<(state: WebRTCConnectionState) => void> = new Set();

  constructor(config: Partial<WebRTCConfig> = {}) {
    this.config = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
      ],
      enableAudio: true,
      enableVideo: false,
      enableDataChannel: true,
      maxRetries: 3,
      connectionTimeout: 10000,
      ...config
    };
  }

  /**
   * Initialize WebRTC service with Socket.IO for signaling
   */
  async initialize(socket: Socket, userId: string): Promise<void> {
    if (this.isInitialized) {
      console.warn('WebRTC service already initialized');
      return;
    }

    this.socket = socket;

    try {
      // Initialize PeerJS with custom configuration
      this.peer = new Peer(userId, {
        config: {
          iceServers: this.config.iceServers
        },
        debug: process.env.NODE_ENV === 'development' ? 2 : 0
      });

      await this.setupPeerEventHandlers();
      await this.setupSocketSignaling();
      
      if (this.config.enableAudio) {
        await this.initializeAudioStream();
      }

      this.isInitialized = true;
      console.log('WebRTC service initialized successfully', { userId });
    } catch (error) {
      console.error('Failed to initialize WebRTC service:', error);
      throw error;
    }
  }

  /**
   * Setup PeerJS event handlers
   */
  private async setupPeerEventHandlers(): Promise<void> {
    if (!this.peer) return;

    return new Promise((resolve, reject) => {
      this.peer!.on('open', (id) => {
        console.log('PeerJS connection opened:', id);
        resolve();
      });

      this.peer!.on('error', (error) => {
        console.error('PeerJS error:', error);
        if (!this.isInitialized) {
          reject(error);
        }
      });

      this.peer!.on('connection', (conn) => {
        this.handleIncomingDataConnection(conn);
      });

      this.peer!.on('call', (call) => {
        this.handleIncomingAudioCall(call);
      });

      // Timeout fallback
      setTimeout(() => {
        if (!this.isInitialized) {
          reject(new Error('PeerJS initialization timeout'));
        }
      }, this.config.connectionTimeout);
    });
  }

  /**
   * Setup Socket.IO signaling for WebRTC
   */
  private setupSocketSignaling(): void {
    if (!this.socket) return;

    // Handle WebRTC signaling messages
    this.socket.on('webrtc-signal', (data: {
      from: string;
      to: string;
      signal: any;
      type: 'offer' | 'answer' | 'ice-candidate';
    }) => {
      this.handleSignalingMessage(data);
    });

    // Handle peer join/leave events
    this.socket.on('peer-joined', (data: { userId: string; projectId: string }) => {
      this.connectToPeer(data.userId);
    });

    this.socket.on('peer-left', (data: { userId: string }) => {
      this.disconnectFromPeer(data.userId);
    });
  }

  /**
   * Initialize audio stream for voice comments
   */
  private async initializeAudioStream(): Promise<void> {
    try {
      this.localStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100
        },
        video: false
      });
      console.log('Audio stream initialized for voice comments');
    } catch (error) {
      console.warn('Failed to initialize audio stream:', error);
      this.config.enableAudio = false;
    }
  }

  /**
   * Connect to a peer for P2P communication
   */
  async connectToPeer(peerId: string): Promise<void> {
    if (!this.peer || !this.isInitialized) {
      console.warn('WebRTC service not initialized');
      return;
    }

    if (this.connections.has(peerId)) {
      console.log('Already connected to peer:', peerId);
      return;
    }

    try {
      // Establish data connection for cursor tracking
      if (this.config.enableDataChannel) {
        const dataConn = this.peer.connect(peerId, {
          reliable: true,
          serialization: 'json'
        });
        
        dataConn.on('open', () => {
          this.connections.set(peerId, dataConn);
          this.updateConnectionState(peerId, {
            peerId,
            isConnected: true,
            connectionType: 'p2p',
            latency: 0,
            lastActivity: Date.now()
          });
          console.log('Data connection established with peer:', peerId);
        });

        dataConn.on('data', (data) => {
          this.handleDataChannelMessage(peerId, data);
        });

        dataConn.on('error', (error) => {
          console.error('Data connection error with peer:', peerId, error);
          this.fallbackToWebSocket(peerId);
        });
      }

      // Establish audio connection for voice comments
      if (this.config.enableAudio && this.localStream) {
        const audioCall = this.peer.call(peerId, this.localStream);
        this.audioConnections.set(peerId, audioCall);

        audioCall.on('stream', (remoteStream) => {
          this.handleRemoteAudioStream(peerId, remoteStream);
        });

        audioCall.on('error', (error) => {
          console.error('Audio connection error with peer:', peerId, error);
        });
      }

    } catch (error) {
      console.error('Failed to connect to peer:', peerId, error);
      this.fallbackToWebSocket(peerId);
    }
  }

  /**
   * Handle incoming data connection
   */
  private handleIncomingDataConnection(conn: DataConnection): void {
    const peerId = conn.peer;
    
    conn.on('open', () => {
      this.connections.set(peerId, conn);
      this.updateConnectionState(peerId, {
        peerId,
        isConnected: true,
        connectionType: 'p2p',
        latency: 0,
        lastActivity: Date.now()
      });
      console.log('Incoming data connection established with peer:', peerId);
    });

    conn.on('data', (data) => {
      this.handleDataChannelMessage(peerId, data);
    });

    conn.on('error', (error) => {
      console.error('Incoming data connection error:', error);
      this.fallbackToWebSocket(peerId);
    });
  }

  /**
   * Handle incoming audio call
   */
  private handleIncomingAudioCall(call: MediaConnection): void {
    if (!this.localStream) {
      call.close();
      return;
    }

    call.answer(this.localStream);
    this.audioConnections.set(call.peer, call);

    call.on('stream', (remoteStream) => {
      this.handleRemoteAudioStream(call.peer, remoteStream);
    });

    call.on('error', (error) => {
      console.error('Incoming audio call error:', error);
    });
  }

  /**
   * Send cursor position with ultra-low latency
   */
  sendCursorPosition(x: number, y: number, elementId?: string): void {
    if (!this.isInitialized) return;

    const cursorData: CursorData = {
      userId: this.peer?.id || '',
      x,
      y,
      timestamp: Date.now(),
      elementId
    };

    // Try P2P first for lowest latency
    let sentViaP2P = false;
    this.connections.forEach((conn, peerId) => {
      if (conn.open) {
        try {
          conn.send({
            type: 'cursor',
            data: cursorData
          });
          sentViaP2P = true;
        } catch (error) {
          console.warn('Failed to send cursor via P2P to:', peerId, error);
        }
      }
    });

    // Fallback to WebSocket if no P2P connections
    if (!sentViaP2P && this.socket) {
      this.socket.emit('cursor-update', cursorData);
    }
  }

  /**
   * Send voice comment with low latency
   */
  async sendVoiceComment(audioBlob: Blob, commentId: string): Promise<void> {
    if (!this.isInitialized || !this.config.enableAudio) return;

    const voiceData: VoiceCommentData = {
      userId: this.peer?.id || '',
      commentId,
      audioBlob,
      duration: 0, // Will be calculated
      timestamp: Date.now()
    };

    // For voice comments, we'll use WebSocket for reliability
    // P2P audio streaming is handled separately via MediaConnection
    if (this.socket) {
      // Convert blob to base64 for transmission
      const arrayBuffer = await audioBlob.arrayBuffer();
      const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
      
      this.socket.emit('voice-comment', {
        ...voiceData,
        audioData: base64Audio
      });
    }
  }

  /**
   * Handle data channel messages
   */
  private handleDataChannelMessage(peerId: string, message: any): void {
    try {
      if (message.type === 'cursor') {
        const cursorData = message.data as CursorData;
        this.cursorCallbacks.forEach(callback => callback(cursorData));
        
        // Update latency measurement
        const latency = Date.now() - cursorData.timestamp;
        this.updateConnectionLatency(peerId, latency);
      }
    } catch (error) {
      console.error('Error handling data channel message:', error);
    }
  }

  /**
   * Handle remote audio stream
   */
  private handleRemoteAudioStream(peerId: string, stream: MediaStream): void {
    // Create audio element for playback
    const audio = new Audio();
    audio.srcObject = stream;
    audio.autoplay = true;
    audio.volume = 0.8;
    
    console.log('Received remote audio stream from peer:', peerId);
  }

  /**
   * Fallback to WebSocket when P2P fails
   */
  private fallbackToWebSocket(peerId: string): void {
    this.updateConnectionState(peerId, {
      peerId,
      isConnected: true,
      connectionType: 'websocket',
      latency: 50, // Estimated WebSocket latency
      lastActivity: Date.now()
    });
    
    console.log('Falling back to WebSocket for peer:', peerId);
  }

  /**
   * Update connection state and notify listeners
   */
  private updateConnectionState(peerId: string, state: WebRTCConnectionState): void {
    this.connectionStates.set(peerId, state);
    this.connectionCallbacks.forEach(callback => callback(state));
  }

  /**
   * Update connection latency
   */
  private updateConnectionLatency(peerId: string, latency: number): void {
    const state = this.connectionStates.get(peerId);
    if (state) {
      state.latency = latency;
      state.lastActivity = Date.now();
      this.connectionStates.set(peerId, state);
    }
  }

  /**
   * Handle signaling messages
   */
  private handleSignalingMessage(data: any): void {
    // Implementation for custom signaling if needed
    // PeerJS handles most signaling automatically
    console.log('Received signaling message:', data);
  }

  /**
   * Disconnect from a peer
   */
  disconnectFromPeer(peerId: string): void {
    const dataConn = this.connections.get(peerId);
    if (dataConn) {
      dataConn.close();
      this.connections.delete(peerId);
    }

    const audioConn = this.audioConnections.get(peerId);
    if (audioConn) {
      audioConn.close();
      this.audioConnections.delete(peerId);
    }

    this.connectionStates.delete(peerId);
    console.log('Disconnected from peer:', peerId);
  }

  /**
   * Register cursor position callback
   */
  onCursorUpdate(callback: (data: CursorData) => void): () => void {
    this.cursorCallbacks.add(callback);
    return () => this.cursorCallbacks.delete(callback);
  }

  /**
   * Register voice comment callback
   */
  onVoiceComment(callback: (data: VoiceCommentData) => void): () => void {
    this.voiceCallbacks.add(callback);
    return () => this.voiceCallbacks.delete(callback);
  }

  /**
   * Register connection state callback
   */
  onConnectionStateChange(callback: (state: WebRTCConnectionState) => void): () => void {
    this.connectionCallbacks.add(callback);
    return () => this.connectionCallbacks.delete(callback);
  }

  /**
   * Get current connection states
   */
  getConnectionStates(): Map<string, WebRTCConnectionState> {
    return new Map(this.connectionStates);
  }

  /**
   * Get average latency across all connections
   */
  getAverageLatency(): number {
    const latencies = Array.from(this.connectionStates.values()).map(state => state.latency);
    return latencies.length > 0 ? latencies.reduce((a, b) => a + b, 0) / latencies.length : 0;
  }

  /**
   * Cleanup and destroy service
   */
  destroy(): void {
    // Close all connections
    this.connections.forEach((conn) => conn.close());
    this.audioConnections.forEach((conn) => conn.close());
    
    // Stop local stream
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
    }

    // Destroy peer
    if (this.peer) {
      this.peer.destroy();
    }

    // Clear all data
    this.connections.clear();
    this.audioConnections.clear();
    this.connectionStates.clear();
    this.cursorCallbacks.clear();
    this.voiceCallbacks.clear();
    this.connectionCallbacks.clear();

    this.isInitialized = false;
    console.log('WebRTC service destroyed');
  }
}

// Export singleton instance
export const webrtcService = new WebRTCService();
