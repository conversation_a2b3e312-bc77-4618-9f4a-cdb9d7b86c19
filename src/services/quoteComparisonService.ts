import { API_BASE_URL } from '../lib/config';
import { Quote, QuoteTier } from './quotationService';

export interface ComparisonFeature {
  feature: string;
  basic: string | boolean | number;
  premium: string | boolean | number;
  luxury: string | boolean | number;
  category: 'materials' | 'hardware' | 'installation' | 'warranty' | 'design' | 'timeline';
  importance: 'low' | 'medium' | 'high';
  description?: string;
}

export interface ValueProposition {
  tier: 'basic' | 'premium' | 'luxury';
  title: string;
  description: string;
  savings?: string;
  upgrade_cost?: string;
  recommended?: boolean;
  best_value?: boolean;
}

export interface ComparisonData {
  quote_id: string;
  tiers: QuoteTier[];
  features: ComparisonFeature[];
  value_propositions: ValueProposition[];
  recommendations: {
    best_value: string;
    most_popular: string;
    premium_choice: string;
  };
  summary: {
    price_range: {
      min: number;
      max: number;
      currency: string;
    };
    savings_potential: string;
    upgrade_benefits: string[];
  };
}

export interface ComparisonExportOptions {
  format: 'pdf' | 'excel' | 'csv';
  include_features: boolean;
  include_pricing: boolean;
  include_recommendations: boolean;
  template?: string;
}

class QuoteComparisonService {
  private baseUrl = `${API_BASE_URL}/api/quotation`;

  private async getAuthHeaders(): Promise<HeadersInit> {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
    };
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
  }

  // Generate comparison data from quote
  async generateComparison(quoteId: string): Promise<ComparisonData> {
    const response = await fetch(`${this.baseUrl}/${quoteId}/comparison`, {
      headers: await this.getAuthHeaders(),
    });

    const result = await this.handleResponse<{ success: boolean; data: ComparisonData }>(response);
    return result.data;
  }

  // Export comparison data
  async exportComparison(
    quoteId: string, 
    options: ComparisonExportOptions
  ): Promise<{ download_url: string; filename: string }> {
    const response = await fetch(`${this.baseUrl}/${quoteId}/comparison/export`, {
      method: 'POST',
      headers: await this.getAuthHeaders(),
      body: JSON.stringify(options),
    });

    const result = await this.handleResponse<{ 
      success: boolean; 
      data: { download_url: string; filename: string } 
    }>(response);
    return result.data;
  }

  // Calculate feature differences between tiers
  calculateFeatureDifferences(quote: Quote): ComparisonFeature[] {
    const features: ComparisonFeature[] = [];

    // Material quality comparison
    features.push({
      feature: 'Cabinet Material',
      basic: 'MDF with laminate finish',
      premium: 'Plywood with wood veneer',
      luxury: 'Solid hardwood construction',
      category: 'materials',
      importance: 'high',
      description: 'Core cabinet box construction material'
    });

    features.push({
      feature: 'Door Style',
      basic: 'Flat panel (Shaker style)',
      premium: 'Raised panel with decorative edge',
      luxury: 'Custom millwork with hand-carved details',
      category: 'materials',
      importance: 'medium',
      description: 'Cabinet door design and construction'
    });

    // Hardware comparison
    features.push({
      feature: 'Hinges',
      basic: 'Standard European hinges',
      premium: 'Soft-close European hinges',
      luxury: 'Premium soft-close with lifetime warranty',
      category: 'hardware',
      importance: 'high',
      description: 'Cabinet door hinge quality and features'
    });

    features.push({
      feature: 'Drawer Slides',
      basic: 'Side-mount ball bearing slides',
      premium: 'Under-mount soft-close slides',
      luxury: 'Premium full-extension soft-close',
      category: 'hardware',
      importance: 'high',
      description: 'Drawer slide mechanism and quality'
    });

    features.push({
      feature: 'Handles & Knobs',
      basic: 'Standard brushed nickel',
      premium: 'Designer collection (multiple finishes)',
      luxury: 'Custom hardware or premium designer brands',
      category: 'hardware',
      importance: 'medium',
      description: 'Cabinet hardware style and quality'
    });

    // Installation and service
    features.push({
      feature: 'Installation',
      basic: 'Standard installation',
      premium: 'Professional installation with cleanup',
      luxury: 'White-glove installation with project manager',
      category: 'installation',
      importance: 'high',
      description: 'Installation service level and support'
    });

    features.push({
      feature: 'Project Timeline',
      basic: '6-8 weeks',
      premium: '4-6 weeks',
      luxury: '3-4 weeks (priority scheduling)',
      category: 'timeline',
      importance: 'medium',
      description: 'Expected project completion timeframe'
    });

    // Warranty and support
    features.push({
      feature: 'Warranty Period',
      basic: '1 year limited warranty',
      premium: '3 year comprehensive warranty',
      luxury: '5 year comprehensive warranty',
      category: 'warranty',
      importance: 'high',
      description: 'Warranty coverage period and scope'
    });

    features.push({
      feature: 'Design Support',
      basic: 'Basic layout assistance',
      premium: '3D design renderings',
      luxury: 'Full design service with revisions',
      category: 'design',
      importance: 'medium',
      description: 'Design assistance and visualization services'
    });

    // Add pricing-based features
    const basicTier = quote.tiers.find(t => t.tier === 'basic');
    const premiumTier = quote.tiers.find(t => t.tier === 'premium');
    const luxuryTier = quote.tiers.find(t => t.tier === 'luxury');

    if (basicTier && premiumTier && luxuryTier) {
      features.push({
        feature: 'Total Investment',
        basic: basicTier.total,
        premium: premiumTier.total,
        luxury: luxuryTier.total,
        category: 'materials',
        importance: 'high',
        description: 'Total project cost including materials, labor, and installation'
      });

      features.push({
        feature: 'Cost per Linear Foot',
        basic: this.calculateCostPerLinearFoot(basicTier.total, quote.summary.linearFeet),
        premium: this.calculateCostPerLinearFoot(premiumTier.total, quote.summary.linearFeet),
        luxury: this.calculateCostPerLinearFoot(luxuryTier.total, quote.summary.linearFeet),
        category: 'materials',
        importance: 'medium',
        description: 'Cost efficiency based on linear footage'
      });
    }

    return features;
  }

  // Generate value propositions for each tier
  generateValuePropositions(quote: Quote): ValueProposition[] {
    const propositions: ValueProposition[] = [];
    const basicTier = quote.tiers.find(t => t.tier === 'basic');
    const premiumTier = quote.tiers.find(t => t.tier === 'premium');
    const luxuryTier = quote.tiers.find(t => t.tier === 'luxury');

    if (basicTier) {
      propositions.push({
        tier: 'basic',
        title: 'Budget-Friendly Choice',
        description: 'Quality cabinets at an affordable price point with essential features and reliable construction.',
        best_value: false,
        recommended: false
      });
    }

    if (premiumTier && basicTier) {
      const upgradeValue = this.calculateUpgradeValue(basicTier.total, premiumTier.total);
      propositions.push({
        tier: 'premium',
        title: 'Best Value Package',
        description: 'Perfect balance of quality, features, and price. Includes premium hardware and enhanced warranty.',
        upgrade_cost: upgradeValue,
        best_value: true,
        recommended: true
      });
    }

    if (luxuryTier && premiumTier) {
      const upgradeValue = this.calculateUpgradeValue(premiumTier.total, luxuryTier.total);
      propositions.push({
        tier: 'luxury',
        title: 'Premium Experience',
        description: 'Top-tier materials, custom features, and white-glove service for the ultimate kitchen transformation.',
        upgrade_cost: upgradeValue,
        best_value: false,
        recommended: false
      });
    }

    return propositions;
  }

  // Helper methods
  private calculateCostPerLinearFoot(total: string, linearFeet: number): string {
    const totalNum = parseFloat(total.replace(/[^\d.-]/g, ''));
    const costPerFoot = totalNum / linearFeet;
    return `NZD $${costPerFoot.toFixed(0)}/ft`;
  }

  private calculateUpgradeValue(lowerTier: string, higherTier: string): string {
    const lowerNum = parseFloat(lowerTier.replace(/[^\d.-]/g, ''));
    const higherNum = parseFloat(higherTier.replace(/[^\d.-]/g, ''));
    const difference = higherNum - lowerNum;
    return `+NZD $${difference.toLocaleString()}`;
  }

  // Generate recommendations based on quote analysis
  generateRecommendations(quote: Quote): ComparisonData['recommendations'] {
    return {
      best_value: 'premium',
      most_popular: 'premium',
      premium_choice: 'luxury'
    };
  }

  // Generate summary statistics
  generateSummary(quote: Quote): ComparisonData['summary'] {
    const basicTier = quote.tiers.find(t => t.tier === 'basic');
    const luxuryTier = quote.tiers.find(t => t.tier === 'luxury');

    if (!basicTier || !luxuryTier) {
      return {
        price_range: { min: 0, max: 0, currency: 'NZD' },
        savings_potential: 'N/A',
        upgrade_benefits: []
      };
    }

    const minPrice = parseFloat(basicTier.total.replace(/[^\d.-]/g, ''));
    const maxPrice = parseFloat(luxuryTier.total.replace(/[^\d.-]/g, ''));
    const savingsPotential = ((maxPrice - minPrice) / maxPrice * 100).toFixed(0);

    return {
      price_range: {
        min: minPrice,
        max: maxPrice,
        currency: 'NZD'
      },
      savings_potential: `Up to ${savingsPotential}% savings with basic tier`,
      upgrade_benefits: [
        'Enhanced durability and longevity',
        'Premium hardware and finishes',
        'Extended warranty coverage',
        'Professional design support',
        'Priority installation scheduling'
      ]
    };
  }

  // Create complete comparison data
  createComparisonData(quote: Quote): ComparisonData {
    return {
      quote_id: quote.id,
      tiers: quote.tiers,
      features: this.calculateFeatureDifferences(quote),
      value_propositions: this.generateValuePropositions(quote),
      recommendations: this.generateRecommendations(quote),
      summary: this.generateSummary(quote)
    };
  }
}

export const quoteComparisonService = new QuoteComparisonService();
