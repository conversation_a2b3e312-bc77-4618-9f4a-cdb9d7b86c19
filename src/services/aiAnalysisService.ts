/**
 * AI Analysis Service
 * 
 * Provides GPT-4o powered kitchen design analysis capabilities
 * Adapted from the Python implementation in the archived projects
 */

export interface AnalysisConfig {
  useGPT4o: boolean;
  useReasoning: boolean;
  useGPTO1?: boolean; // New GPT-o1 model option
  modelSelection?: 'AUTO' | 'GPT4O' | 'GPT4O_MINI' | 'GPTO1'; // Explicit model selection
  focusOnMaterials: boolean;
  focusOnHardware: boolean;
  enableMultiView: boolean;
  enable3DReconstruction?: boolean;
  spatialResolution?: 'LOW' | 'MEDIUM' | 'HIGH';
  includeHardwarePositioning?: boolean;
  complexReasoningRequired?: boolean; // Flag for GPT-o1 use cases
  promptId?: string;
  promptVersion?: string;
}

export interface CabinetStyle {
  primary: 'SHAKER' | 'MODERN' | 'TRADITIONAL' | 'CONTEMPORARY' | 'TRANSITIONAL' | 'RUSTIC' | 'INDUSTRIAL' | 'UNKNOWN';
  secondary?: string;
  confidence: number;
  characteristics: string[];
}

export interface MaterialFinish {
  type: 'WOOD' | 'LAMINATE' | 'PAINTED' | 'STAINED' | 'THERMOFOIL' | 'METAL' | 'GLASS' | 'UNKNOWN';
  color: string;
  texture: 'SMOOTH' | 'TEXTURED' | 'GRAIN_VISIBLE' | 'MATTE' | 'GLOSS' | 'SATIN' | 'UNKNOWN';
  brand?: string;
  confidence: number;
}

export interface CabinetAnalysis {
  id: string;
  type: 'BASE' | 'WALL' | 'TALL' | 'ISLAND' | 'PANTRY';
  style: CabinetStyle;
  dimensions: {
    width: number;
    height: number;
    depth: number;
  };
  position: {
    x: number;
    y: number;
  };
  materials: string[];
  materialFinish: MaterialFinish;
  hardware: HardwareItem[];
  confidence: number;
}

export interface HardwareItem {
  type: 'HINGE' | 'HANDLE' | 'DRAWER_SLIDE' | 'SHELF' | 'SOFT_CLOSE' | 'KNOB' | 'PULL';
  quantity: number;
  specifications: Record<string, any>;
  brand?: string;
  model?: string;
  finish: string;
  style: 'MODERN' | 'TRADITIONAL' | 'CONTEMPORARY' | 'RUSTIC' | 'INDUSTRIAL' | 'UNKNOWN';
  compatibility: {
    cabinetStyles: string[];
    installationComplexity: 'LOW' | 'MEDIUM' | 'HIGH';
    notes?: string;
  };
  estimatedCost?: number;
  confidence: number;
}

export interface Point3D {
  x: number;
  y: number;
  z: number;
}

export interface Cabinet3DModel {
  id: string;
  type: 'BASE' | 'WALL' | 'TALL' | 'ISLAND' | 'PANTRY';
  dimensions: {
    width: number;
    height: number;
    depth: number;
    position: Point3D;
    rotation: { x: number; y: number; z: number };
  };
  vertices: Point3D[];
  faces: number[][];
  materials: {
    door: string;
    frame: string;
    hardware: string;
  };
  confidence: number;
}

export interface SpatialRelationship {
  cabinetId1: string;
  cabinetId2: string;
  relationship: 'ADJACENT' | 'ABOVE' | 'BELOW' | 'CORNER' | 'OPPOSITE';
  distance: number;
  confidence: number;
}

export interface ReconstructionResult {
  cabinets: Cabinet3DModel[];
  spatialRelationships: SpatialRelationship[];
  roomDimensions: {
    width: number;
    height: number;
    depth: number;
  };
  reconstructionMetrics: {
    totalVolume: number;
    cabinetDensity: number;
    spatialAccuracy: number;
    reconstructionTime: number;
  };
  confidence: {
    overall: number;
    spatialMapping: number;
    dimensionAccuracy: number;
    cabinetPositioning: number;
  };
}

export interface AnalysisResults {
  id: string;
  status: 'PROCESSING' | 'COMPLETED' | 'FAILED';
  progress: number;
  cabinets: CabinetAnalysis[];
  hardware: HardwareItem[];
  measurements: {
    totalLinearMeters: number;
    totalCabinets: number;
    cabinetsByType: Record<string, number>;
  };
  materials: {
    finishes: string[];
    colors: string[];
    textures: string[];
  };
  styleAnalysis: {
    dominantStyle: CabinetStyle;
    styleConsistency: number;
    mixedStyles?: CabinetStyle[];
    designEra?: string;
    recommendations?: string[];
  };
  reconstruction3D?: ReconstructionResult;
  layoutOptimization?: {
    workflowOptimization: any;
    spaceUtilization: any;
    ergonomicAssessment: any;
    trafficFlowAnalysis: any;
    costBenefitAnalysis: any;
    processingMetrics: {
      analysisTime: number;
      confidenceScore: number;
      featuresAnalyzed: string[];
    };
  };
  confidence: {
    overall: number;
    cabinetCount: number;
    cabinetTypes: number;
    hardware: number;
    measurements: number;
    styleClassification: number;
    materialIdentification: number;
    spatialReconstruction?: number;
  };
  processingTime: number;
  timestamp: string;
  errors?: string[];
  rawAnalysis?: string; // Raw analysis content for reasoning chain parsing
  model?: string; // Model used for analysis (GPT-4o, o4-mini, GPT-o1)
}

export interface AnalysisProgress {
  step: string;
  progress: number;
  message: string;
  timestamp: string;
}

export class AIAnalysisService {
  private baseUrl: string;
  private socket: any = null;

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';
    console.log('AIAnalysisService initialized with baseUrl:', this.baseUrl);
    console.log('Environment variables:', {
      VITE_API_URL: import.meta.env.VITE_API_URL,
      VITE_SOCKET_URL: import.meta.env.VITE_SOCKET_URL,
      VITE_NODE_ENV: import.meta.env.VITE_NODE_ENV
    });
    this.initializeSocket();
  }

  /**
   * Initialize Socket.IO connection for real-time updates
   */
  private initializeSocket() {
    if (typeof window !== 'undefined') {
      // Dynamically import socket.io-client to avoid SSR issues
      import('socket.io-client').then(({ io }) => {
        const socketUrl = import.meta.env.VITE_SOCKET_URL || 'http://localhost:3001';
        this.socket = io(socketUrl, {
          transports: ['websocket', 'polling']
        });

        this.socket.on('connect', () => {
          console.log('Connected to analysis server');
        });

        this.socket.on('disconnect', () => {
          console.log('Disconnected from analysis server');
        });

        this.socket.on('connect_error', (error: any) => {
          console.warn('Socket connection error:', error);
        });
      }).catch(error => {
        console.warn('Failed to load socket.io-client:', error);
      });
    }
  }

  /**
   * Analyze a kitchen design file using the backend API
   */
  async analyzeKitchenDesign(
    file: File,
    config: AnalysisConfig = {
      useGPT4o: true,
      useReasoning: true,
      focusOnMaterials: false,
      focusOnHardware: false,
      enableMultiView: true,
    },
    onProgress?: (progress: AnalysisProgress) => void
  ): Promise<AnalysisResults> {
    try {
      // Step 1: Upload file and start analysis
      onProgress?.({
        step: 'upload',
        progress: 5,
        message: 'Uploading file to server...',
        timestamp: new Date().toISOString(),
      });

      const analysisId = await this.uploadFileForAnalysis(file, config);

      // Step 2: Set up real-time progress monitoring
      if (this.socket && onProgress) {
        this.setupProgressMonitoring(analysisId, onProgress);
      }

      // Step 3: Poll for completion or wait for socket updates
      const results = await this.waitForAnalysisCompletion(analysisId, onProgress);

      return results;

    } catch (error) {
      console.error('Analysis failed:', error);
      throw new Error(`Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Upload file to backend for analysis with retry logic
   */
  private async uploadFileForAnalysis(file: File, config: AnalysisConfig): Promise<string> {
    const maxRetries = 3;
    const retryDelay = 1000; // 1 second

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.performUpload(file, config, attempt);
      } catch (error) {
        console.warn(`Upload attempt ${attempt} failed:`, error);

        // Don't retry on certain errors
        if (error instanceof Error) {
          if (error.message.includes('413') || // File too large
              error.message.includes('400') || // Bad request
              error.message.includes('CORS')) { // CORS error
            throw error;
          }
        }

        // If this was the last attempt, throw the error
        if (attempt === maxRetries) {
          throw error;
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        console.log(`Retrying upload (attempt ${attempt + 1}/${maxRetries})...`);
      }
    }

    throw new Error('Upload failed after maximum retries');
  }

  /**
   * Perform the actual file upload
   */
  private async performUpload(file: File, config: AnalysisConfig, attempt: number): Promise<string> {
    console.log(`Starting file upload for analysis (attempt ${attempt}):`, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      config,
      baseUrl: this.baseUrl,
      attempt
    });

    const formData = new FormData();
    formData.append('file', file);
    formData.append('useGPT4o', config.useGPT4o.toString());
    formData.append('useReasoning', config.useReasoning.toString());
    formData.append('focusOnMaterials', config.focusOnMaterials.toString());
    formData.append('focusOnHardware', config.focusOnHardware.toString());
    formData.append('enableMultiView', config.enableMultiView.toString());

    // Add 3D reconstruction parameters
    if (config.enable3DReconstruction !== undefined) {
      formData.append('enable3DReconstruction', config.enable3DReconstruction.toString());
    }
    if (config.spatialResolution) {
      formData.append('spatialResolution', config.spatialResolution);
    }
    if (config.includeHardwarePositioning !== undefined) {
      formData.append('includeHardwarePositioning', config.includeHardwarePositioning.toString());
    }

    if (config.promptId) {
      formData.append('promptId', config.promptId);
    }
    if (config.promptVersion) {
      formData.append('promptVersion', config.promptVersion.toString());
    }

    const uploadUrl = `${this.baseUrl}/analysis/upload`;
    console.log('Making fetch request to:', uploadUrl);

    try {
      const response = await fetch(uploadUrl, {
        method: 'POST',
        body: formData,
      });

      console.log('Upload response received:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        headers: Object.fromEntries(response.headers.entries())
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: { message: 'Upload failed' } }));
        console.error('Upload failed with error data:', errorData);
        throw new Error(errorData.error?.message || `Upload failed with status ${response.status}`);
      }

      const data = await response.json();
      console.log('Upload successful, received data:', data);
      return data.data.analysisId;

    } catch (error) {
      console.error('Network error during file upload:', error);

      // Enhanced error handling with specific guidance
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error(`Network connection failed. Please ensure:
1. Backend server is running on http://localhost:3001
2. Frontend is accessing the correct port (currently: ${window.location.origin})
3. No firewall or proxy blocking the connection
4. Try refreshing the page and attempting again

Technical details: ${error.message}`);
      }

      // Handle specific HTTP errors
      if (error instanceof Error) {
        if (error.message.includes('CORS')) {
          throw new Error(`CORS policy error. The backend server may not be configured to accept requests from ${window.location.origin}. Please contact support.`);
        }

        if (error.message.includes('timeout')) {
          throw new Error('Request timeout. The server may be overloaded. Please try again in a few moments.');
        }

        if (error.message.includes('413')) {
          throw new Error('File too large. Please ensure your file is under 50MB and try again.');
        }
      }

      throw error;
    }
  }

  /**
   * Set up real-time progress monitoring via WebSocket
   */
  private setupProgressMonitoring(analysisId: string, onProgress: (progress: AnalysisProgress) => void): void {
    if (!this.socket) return;

    // Join analysis room for updates
    this.socket.emit('join-analysis', analysisId);

    // Listen for progress updates
    this.socket.on('analysis-progress', (progress: AnalysisProgress) => {
      if (progress.analysisId === analysisId) {
        onProgress(progress);
      }
    });

    // Listen for analysis updates
    this.socket.on('analysis-update', (update: any) => {
      if (update.analysisId === analysisId) {
        onProgress({
          step: update.currentStep,
          progress: update.progress,
          message: update.message,
          timestamp: update.timestamp,
        });
      }
    });
  }

  /**
   * Wait for analysis completion
   */
  private async waitForAnalysisCompletion(
    analysisId: string,
    onProgress?: (progress: AnalysisProgress) => void
  ): Promise<AnalysisResults> {
    const maxWaitTime = 300000; // 5 minutes
    const pollInterval = 2000; // 2 seconds
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const status = await this.getAnalysisStatus(analysisId);

        if (status.status === 'COMPLETED') {
          const results = await this.getAnalysisResults(analysisId);

          // Clean up socket listeners
          if (this.socket) {
            this.socket.emit('leave-analysis', analysisId);
            this.socket.off('analysis-progress');
            this.socket.off('analysis-update');
          }

          return results;
        }

        if (status.status === 'FAILED') {
          throw new Error(status.error || 'Analysis failed on server');
        }

        // Update progress if no socket connection
        if (!this.socket && onProgress) {
          onProgress({
            step: status.currentStep,
            progress: status.progress,
            message: `Analysis ${status.status.toLowerCase()}...`,
            timestamp: new Date().toISOString(),
          });
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, pollInterval));

      } catch (error) {
        if (error instanceof Error && error.message.includes('Analysis failed')) {
          throw error;
        }
        // Continue polling on network errors
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      }
    }

    throw new Error('Analysis timeout - please try again');
  }

  /**
   * Get analysis status from backend
   */
  private async getAnalysisStatus(analysisId: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/analysis/${analysisId}/status`);

    if (!response.ok) {
      throw new Error(`Failed to get analysis status: ${response.status}`);
    }

    const data = await response.json();
    return data.data;
  }

  /**
   * Get analysis results from backend
   */
  private async getAnalysisResults(analysisId: string): Promise<AnalysisResults> {
    const response = await fetch(`${this.baseUrl}/analysis/${analysisId}/results`);

    if (!response.ok) {
      throw new Error(`Failed to get analysis results: ${response.status}`);
    }

    const data = await response.json();
    return this.transformBackendResults(data.data.results);
  }

  /**
   * Transform backend results to frontend format
   */
  private transformBackendResults(backendResults: any): AnalysisResults {
    // Transform the backend results to match the frontend interface
    const results = backendResults.results || backendResults;

    return {
      id: results.id || backendResults.analysisId,
      status: 'COMPLETED',
      progress: 100,
      cabinets: this.transformCabinets(results.cabinets),
      hardware: this.transformHardware(results.hardware),
      measurements: this.transformMeasurements(results.measurements),
      materials: this.transformMaterials(results.materials),
      styleAnalysis: this.transformStyleAnalysis(results.styleAnalysis || results.style_analysis),
      reconstruction3D: results.reconstruction3D || results.reconstruction_3d,
      layoutOptimization: results.layoutOptimization || results.layout_optimization,
      confidence: this.transformConfidence(results),
      processingTime: results.processing_time_ms / 1000 || 0,
      timestamp: results.timestamp || new Date().toISOString(),
      errors: results.errors || [],
      rawAnalysis: results.rawAnalysis || results.raw_analysis || results.content,
      model: results.model || results.modelUsed || results.model_used
    };
  }

  /**
   * Transform cabinet data with enhanced style and material analysis
   */
  private transformCabinets(cabinets: any): CabinetAnalysis[] {
    if (!cabinets || !cabinets.details) return [];

    return cabinets.details.map((cabinet: any, index: number) => ({
      id: cabinet.id || `cabinet_${index + 1}`,
      type: cabinet.type?.toUpperCase() || 'BASE',
      style: this.transformCabinetStyle(cabinet.style),
      dimensions: {
        width: cabinet.width || 600,
        height: cabinet.height || 720,
        depth: cabinet.depth || 560
      },
      position: cabinet.position || { x: 0, y: 0 },
      materials: cabinet.materials || ['Unknown'],
      materialFinish: this.transformMaterialFinish(cabinet.materialFinish || cabinet.finish),
      hardware: this.transformCabinetHardware(cabinet.hardware),
      confidence: cabinet.confidence || 0.8
    }));
  }

  /**
   * Transform cabinet style data
   */
  private transformCabinetStyle(style: any): CabinetStyle {
    if (!style) {
      return {
        primary: 'UNKNOWN',
        confidence: 0.3,
        characteristics: []
      };
    }

    return {
      primary: style.primary?.toUpperCase() || 'UNKNOWN',
      secondary: style.secondary,
      confidence: style.confidence || 0.7,
      characteristics: style.characteristics || []
    };
  }

  /**
   * Transform material finish data
   */
  private transformMaterialFinish(finish: any): MaterialFinish {
    if (!finish) {
      return {
        type: 'UNKNOWN',
        color: 'Unknown',
        texture: 'UNKNOWN',
        confidence: 0.3
      };
    }

    return {
      type: finish.type?.toUpperCase() || 'UNKNOWN',
      color: finish.color || 'Unknown',
      texture: finish.texture?.toUpperCase() || 'UNKNOWN',
      brand: finish.brand,
      confidence: finish.confidence || 0.7
    };
  }

  /**
   * Transform cabinet hardware data
   */
  private transformCabinetHardware(hardware: any[]): HardwareItem[] {
    if (!hardware || !Array.isArray(hardware)) return [];

    return hardware.map((item: any) => ({
      type: item.type?.toUpperCase() || 'HANDLE',
      quantity: item.quantity || 1,
      specifications: item.specifications || {},
      brand: item.brand,
      model: item.model,
      finish: item.finish || 'Unknown',
      style: item.style?.toUpperCase() || 'UNKNOWN',
      compatibility: {
        cabinetStyles: item.compatibility?.cabinetStyles || [],
        installationComplexity: item.compatibility?.installationComplexity || 'MEDIUM',
        notes: item.compatibility?.notes
      },
      estimatedCost: item.estimatedCost,
      confidence: item.confidence || 0.7
    }));
  }

  /**
   * Transform hardware data
   */
  private transformHardware(hardware: any): HardwareItem[] {
    if (!hardware) return [];

    const items: HardwareItem[] = [];

    if (hardware.hinges) {
      items.push({
        type: 'HINGE',
        quantity: hardware.hinges.count || hardware.hinges,
        specifications: { type: hardware.hinges.type || 'standard' },
        brand: hardware.hinges.brand,
        model: hardware.hinges.model,
        finish: hardware.hinges.finish || 'Unknown',
        style: hardware.hinges.style?.toUpperCase() || 'UNKNOWN',
        compatibility: {
          cabinetStyles: hardware.hinges.compatibility?.cabinetStyles || [],
          installationComplexity: hardware.hinges.compatibility?.installationComplexity || 'MEDIUM'
        },
        estimatedCost: hardware.hinges.estimatedCost,
        confidence: hardware.hinges.confidence || 0.8
      });
    }

    if (hardware.handles) {
      items.push({
        type: 'HANDLE',
        quantity: hardware.handles.count || hardware.handles,
        specifications: {
          style: hardware.handles.style || 'bar',
          length: hardware.handles.length || 128
        },
        brand: hardware.handles.brand,
        model: hardware.handles.model,
        finish: hardware.handles.finish || 'Unknown',
        style: hardware.handles.style?.toUpperCase() || 'UNKNOWN',
        compatibility: {
          cabinetStyles: hardware.handles.compatibility?.cabinetStyles || [],
          installationComplexity: hardware.handles.compatibility?.installationComplexity || 'LOW'
        },
        estimatedCost: hardware.handles.estimatedCost,
        confidence: hardware.handles.confidence || 0.8
      });
    }

    if (hardware.drawer_slides) {
      items.push({
        type: 'DRAWER_SLIDE',
        quantity: hardware.drawer_slides.count || hardware.drawer_slides,
        specifications: { type: hardware.drawer_slides.type || 'standard' },
        brand: hardware.drawer_slides.brand,
        model: hardware.drawer_slides.model,
        finish: hardware.drawer_slides.finish || 'Unknown',
        style: hardware.drawer_slides.style?.toUpperCase() || 'UNKNOWN',
        compatibility: {
          cabinetStyles: hardware.drawer_slides.compatibility?.cabinetStyles || [],
          installationComplexity: hardware.drawer_slides.compatibility?.installationComplexity || 'HIGH'
        },
        estimatedCost: hardware.drawer_slides.estimatedCost,
        confidence: hardware.drawer_slides.confidence || 0.8
      });
    }

    return items;
  }

  /**
   * Transform measurements data
   */
  private transformMeasurements(measurements: any): any {
    if (!measurements) {
      return {
        totalLinearMeters: 0,
        totalCabinets: 0,
        cabinetsByType: {}
      };
    }

    return {
      totalLinearMeters: measurements.linear_meters || measurements.totalLinearMeters || 0,
      totalCabinets: measurements.total_cabinets || measurements.totalCabinets || 0,
      cabinetsByType: measurements.cabinets_by_type || measurements.cabinetsByType || {}
    };
  }

  /**
   * Transform materials data
   */
  private transformMaterials(materials: any): any {
    if (!materials) {
      return {
        finishes: ['Unknown'],
        colors: ['Unknown'],
        textures: ['Unknown']
      };
    }

    return {
      finishes: materials.cabinet_finish || materials.finishes || ['Unknown'],
      colors: materials.cabinet_color || materials.colors || ['Unknown'],
      textures: materials.textures || ['Unknown']
    };
  }

  /**
   * Transform style analysis data
   */
  private transformStyleAnalysis(styleAnalysis: any): any {
    if (!styleAnalysis) {
      return {
        dominantStyle: {
          primary: 'UNKNOWN',
          confidence: 0.3,
          characteristics: []
        },
        styleConsistency: 0.5,
        designEra: 'Unknown'
      };
    }

    return {
      dominantStyle: this.transformCabinetStyle(styleAnalysis.dominantStyle),
      styleConsistency: styleAnalysis.styleConsistency || 0.7,
      mixedStyles: styleAnalysis.mixedStyles?.map((style: any) => this.transformCabinetStyle(style)),
      designEra: styleAnalysis.designEra,
      recommendations: styleAnalysis.recommendations || []
    };
  }

  /**
   * Transform confidence data with enhanced style and material confidence
   */
  private transformConfidence(results: any): any {
    const confidence = results.confidence || results.overall_confidence || 0.8;

    if (typeof confidence === 'number') {
      return {
        overall: confidence,
        cabinetCount: confidence,
        cabinetTypes: confidence,
        hardware: confidence,
        measurements: confidence,
        styleClassification: confidence * 0.9, // Slightly lower for new feature
        materialIdentification: confidence * 0.9
      };
    }

    return {
      overall: confidence.overall || 0.8,
      cabinetCount: confidence.cabinet_count || confidence.cabinetCount || 0.8,
      cabinetTypes: confidence.cabinet_types || confidence.cabinetTypes || 0.8,
      hardware: confidence.hardware || 0.8,
      measurements: confidence.measurements || 0.8,
      styleClassification: confidence.styleClassification || confidence.style_classification || 0.7,
      materialIdentification: confidence.materialIdentification || confidence.material_identification || 0.7
    };
  }

  /**
   * Get socket connection status
   */
  getSocketStatus(): { connected: boolean; url?: string } {
    if (!this.socket) {
      return { connected: false };
    }

    return {
      connected: this.socket.connected,
      url: this.socket.io.uri
    };
  }

  /**
   * Disconnect socket
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }
}

export const aiAnalysisService = new AIAnalysisService();
