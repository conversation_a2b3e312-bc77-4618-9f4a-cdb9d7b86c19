import { API_BASE_URL } from '../lib/config';

export interface CustomerSegment {
  id: number;
  segment_code: string;
  segment_name: string;
  description: string;
  target_market: string;
  pricing_tier: string;
  default_markup_percentage: number;
  created_at: string;
  updated_at: string;
}

export interface QuoteTemplate {
  id: number;
  template_code: string;
  template_name: string;
  description: string;
  version: number;
  parent_template_id?: number;
  customer_segment_id?: number;
  is_default: boolean;
  is_active: boolean;
  template_config: any;
  sections_config: any;
  styling_config: any;
  created_by?: string;
  created_at: string;
  updated_at: string;
  customer_segment?: CustomerSegment;
  parent_template?: QuoteTemplate;
}

export interface CreateTemplateRequest {
  template_code: string;
  template_name: string;
  description?: string;
  parent_template_id?: number;
  customer_segment_id?: number;
  is_default?: boolean;
  template_config: any;
  sections_config: any;
  styling_config?: any;
}

export interface UpdateTemplateRequest {
  template_name?: string;
  description?: string;
  customer_segment_id?: number;
  is_default?: boolean;
  is_active?: boolean;
  template_config?: any;
  sections_config?: any;
  styling_config?: any;
}

export interface TemplateUsageStats {
  total_usage: number;
  successful_usage: number;
  avg_generation_time: number;
  last_used: string;
}

class QuoteTemplateService {
  private baseUrl = `${API_BASE_URL}/api/quotation/templates`;

  private async getAuthHeaders(): Promise<HeadersInit> {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
    };
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
  }

  // Customer Segments
  async getCustomerSegments(): Promise<CustomerSegment[]> {
    const response = await fetch(`${this.baseUrl}/segments/list`, {
      headers: await this.getAuthHeaders(),
    });

    const result = await this.handleResponse<{ success: boolean; data: { segments: CustomerSegment[] } }>(response);
    return result.data.segments;
  }

  // Quote Templates
  async getTemplates(includeInactive: boolean = false, segmentId?: number): Promise<QuoteTemplate[]> {
    const params = new URLSearchParams();
    if (includeInactive) params.append('include_inactive', 'true');
    if (segmentId) params.append('segment_id', segmentId.toString());

    const response = await fetch(`${this.baseUrl}?${params.toString()}`, {
      headers: await this.getAuthHeaders(),
    });

    const result = await this.handleResponse<{ success: boolean; data: { templates: QuoteTemplate[] } }>(response);
    return result.data.templates;
  }

  async getTemplateById(id: number): Promise<{ template: QuoteTemplate; usage_stats: TemplateUsageStats }> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      headers: await this.getAuthHeaders(),
    });

    const result = await this.handleResponse<{ 
      success: boolean; 
      data: { template: QuoteTemplate; usage_stats: TemplateUsageStats } 
    }>(response);
    return result.data;
  }

  async createTemplate(templateData: CreateTemplateRequest): Promise<QuoteTemplate> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: await this.getAuthHeaders(),
      body: JSON.stringify(templateData),
    });

    const result = await this.handleResponse<{ success: boolean; data: QuoteTemplate }>(response);
    return result.data;
  }

  async updateTemplate(id: number, updateData: UpdateTemplateRequest): Promise<QuoteTemplate> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      headers: await this.getAuthHeaders(),
      body: JSON.stringify(updateData),
    });

    const result = await this.handleResponse<{ success: boolean; data: QuoteTemplate }>(response);
    return result.data;
  }

  async deleteTemplate(id: number): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE',
      headers: await this.getAuthHeaders(),
    });

    await this.handleResponse<{ success: boolean }>(response);
  }

  // Template Validation
  validateTemplateConfig(config: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.format) {
      errors.push('Template format is required');
    }

    if (config.format && !['basic', 'detailed', 'professional', 'commercial'].includes(config.format)) {
      errors.push('Invalid template format');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  validateSectionsConfig(config: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const requiredSections = ['header', 'pricing'];

    for (const section of requiredSections) {
      if (!config[section] || typeof config[section].enabled !== 'boolean') {
        errors.push(`Section '${section}' configuration is required`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Template Preview
  async generateTemplatePreview(templateId: number, sampleData?: any): Promise<string> {
    const response = await fetch(`${this.baseUrl}/${templateId}/preview`, {
      method: 'POST',
      headers: await this.getAuthHeaders(),
      body: JSON.stringify({ sample_data: sampleData }),
    });

    const result = await this.handleResponse<{ success: boolean; data: { preview_url: string } }>(response);
    return result.data.preview_url;
  }

  // Default Templates
  getDefaultTemplateConfig(format: string): any {
    const configs = {
      basic: {
        format: 'basic',
        include_images: false,
        include_alternatives: true,
        include_warranty: false,
        include_3d_renders: false
      },
      detailed: {
        format: 'detailed',
        include_images: true,
        include_alternatives: true,
        include_warranty: true,
        include_3d_renders: false
      },
      professional: {
        format: 'professional',
        include_images: true,
        include_alternatives: true,
        include_warranty: true,
        include_3d_renders: true,
        include_compliance: false
      },
      commercial: {
        format: 'commercial',
        include_images: true,
        include_alternatives: false,
        include_warranty: true,
        include_compliance: true,
        include_3d_renders: false
      }
    };

    return configs[format as keyof typeof configs] || configs.basic;
  }

  getDefaultSectionsConfig(): any {
    return {
      header: {
        enabled: true,
        customizable: true,
        show_logo: true,
        show_certifications: false
      },
      branding: {
        enabled: true,
        customizable: true
      },
      pricing: {
        enabled: true,
        customizable: false,
        show_breakdown: true,
        show_alternatives: false,
        show_labor_breakdown: false
      },
      materials: {
        enabled: true,
        customizable: true
      },
      terms: {
        enabled: true,
        customizable: true,
        simplified: false,
        detailed: true,
        commercial: false,
        comprehensive: false
      },
      delivery: {
        enabled: true,
        customizable: true
      }
    };
  }

  getDefaultStylingConfig(): any {
    return {
      colors: {
        primary: '#2563eb',
        secondary: '#64748b',
        accent: '#f59e0b',
        background: '#ffffff',
        text: '#1f2937'
      },
      fonts: {
        heading: 'Inter',
        body: 'Inter',
        accent: 'Inter'
      },
      layout: {
        margins: 20,
        spacing: 10,
        columns: 1,
        page_size: 'A4'
      }
    };
  }
}

export const quoteTemplateService = new QuoteTemplateService();
