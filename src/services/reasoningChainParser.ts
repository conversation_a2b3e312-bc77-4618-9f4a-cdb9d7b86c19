import { ReasoningStep, Reasoning<PERSON>hain } from '@/components/ReasoningChainVisualization';

/**
 * Service for parsing GPT-o1 reasoning chains from response content
 */
export class ReasoningChainParser {
  /**
   * Parse GPT-o1 response content to extract reasoning steps
   */
  static parseReasoningChain(
    content: string,
    modelType: 'GPTO1' | 'GPT4O' | 'GPT4O_MINI',
    analysisType: string,
    totalProcessingTime: number,
    complexityFactors: string[] = [],
    expectedOutcomes: string[] = []
  ): ReasoningChain {
    const steps = this.extractReasoningSteps(content);
    const overallConfidence = this.calculateOverallConfidence(steps);

    return {
      id: `reasoning-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      modelType,
      analysisType,
      steps,
      overallConfidence,
      totalProcessingTime,
      complexityFactors,
      expectedOutcomes
    };
  }

  /**
   * Extract individual reasoning steps from content
   */
  private static extractReasoningSteps(content: string): ReasoningStep[] {
    const steps: ReasoningStep[] = [];
    
    // Split content into logical sections
    const sections = this.splitIntoSections(content);
    
    sections.forEach((section, index) => {
      const step = this.parseSection(section, index);
      if (step) {
        steps.push(step);
      }
    });

    // If no structured steps found, create a basic analysis flow
    if (steps.length === 0) {
      return this.createBasicReasoningFlow(content);
    }

    // Add dependencies based on logical flow
    this.addDependencies(steps);

    return steps;
  }

  /**
   * Split content into logical sections for analysis
   */
  private static splitIntoSections(content: string): string[] {
    // Look for common reasoning patterns in GPT-o1 responses
    const patterns = [
      /(?:^|\n)(?:First|Initially|To begin|Starting with)[,:]?\s*(.+?)(?=\n(?:Next|Then|Subsequently|Following|After|Finally|In conclusion)|$)/gims,
      /(?:^|\n)(?:Next|Then|Subsequently|Following)[,:]?\s*(.+?)(?=\n(?:Next|Then|Subsequently|Following|After|Finally|In conclusion)|$)/gims,
      /(?:^|\n)(?:After|Once|When)[,:]?\s*(.+?)(?=\n(?:Next|Then|Subsequently|Following|After|Finally|In conclusion)|$)/gims,
      /(?:^|\n)(?:Finally|In conclusion|To conclude)[,:]?\s*(.+?)(?=\n|$)/gims,
      /(?:^|\n)(?:I observe|I notice|I can see|Looking at)[,:]?\s*(.+?)(?=\n(?:Next|Then|Subsequently|Following|After|Finally|In conclusion)|$)/gims,
      /(?:^|\n)(?:This suggests|This indicates|This means|Therefore)[,:]?\s*(.+?)(?=\n(?:Next|Then|Subsequently|Following|After|Finally|In conclusion)|$)/gims
    ];

    const sections: string[] = [];
    let remainingContent = content;

    // Try to extract structured sections
    patterns.forEach(pattern => {
      const matches = remainingContent.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const cleanMatch = match.replace(/^[\n\r]+/, '').trim();
          if (cleanMatch.length > 20) {
            sections.push(cleanMatch);
            remainingContent = remainingContent.replace(match, '');
          }
        });
      }
    });

    // If no patterns found, split by paragraphs
    if (sections.length === 0) {
      const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 50);
      return paragraphs.slice(0, 8); // Limit to 8 steps max
    }

    return sections.slice(0, 8); // Limit to 8 steps max
  }

  /**
   * Parse a content section into a reasoning step
   */
  private static parseSection(section: string, index: number): ReasoningStep | null {
    if (section.trim().length < 20) return null;

    const stepType = this.determineStepType(section, index);
    const confidence = this.estimateConfidence(section);
    const uncertainties = this.extractUncertainties(section);
    const evidence = this.extractEvidence(section);

    return {
      id: `step-${index + 1}`,
      type: stepType,
      title: this.generateStepTitle(section, stepType),
      content: section.trim(),
      confidence,
      timestamp: new Date(),
      dependencies: [], // Will be added later
      evidence,
      uncertainties,
      processingTime: Math.floor(Math.random() * 2000) + 500 // Simulated processing time
    };
  }

  /**
   * Determine the type of reasoning step based on content
   */
  private static determineStepType(section: string, index: number): ReasoningStep['type'] {
    const lowerSection = section.toLowerCase();

    if (lowerSection.includes('observe') || lowerSection.includes('see') || lowerSection.includes('notice')) {
      return 'observation';
    }
    if (lowerSection.includes('analyze') || lowerSection.includes('examine') || lowerSection.includes('consider')) {
      return 'analysis';
    }
    if (lowerSection.includes('therefore') || lowerSection.includes('suggests') || lowerSection.includes('indicates')) {
      return 'inference';
    }
    if (lowerSection.includes('conclude') || lowerSection.includes('final') || lowerSection.includes('summary')) {
      return 'conclusion';
    }
    if (lowerSection.includes('verify') || lowerSection.includes('confirm') || lowerSection.includes('validate')) {
      return 'validation';
    }

    // Default based on position
    if (index === 0) return 'observation';
    if (index === 1) return 'analysis';
    return 'inference';
  }

  /**
   * Generate a concise title for the reasoning step
   */
  private static generateStepTitle(section: string, stepType: string): string {
    const sentences = section.split(/[.!?]+/).filter(s => s.trim().length > 10);
    const firstSentence = sentences[0]?.trim() || section.substring(0, 100);
    
    // Extract key phrases based on step type
    const keyPhrases = this.extractKeyPhrases(firstSentence, stepType);
    
    if (keyPhrases.length > 0) {
      return keyPhrases[0].substring(0, 60) + (keyPhrases[0].length > 60 ? '...' : '');
    }

    return firstSentence.substring(0, 60) + (firstSentence.length > 60 ? '...' : '');
  }

  /**
   * Extract key phrases based on step type
   */
  private static extractKeyPhrases(text: string, stepType: string): string[] {
    const phrases: string[] = [];
    
    // Common patterns for different step types
    const patterns = {
      observation: [/I (?:observe|notice|see|identify) (.+?)(?:[,.]|$)/gi],
      analysis: [/(?:analyzing|examining|considering) (.+?)(?:[,.]|$)/gi],
      inference: [/(?:this suggests|this indicates|therefore) (.+?)(?:[,.]|$)/gi],
      conclusion: [/(?:in conclusion|finally|to summarize) (.+?)(?:[,.]|$)/gi],
      validation: [/(?:confirming|verifying|validating) (.+?)(?:[,.]|$)/gi]
    };

    const stepPatterns = patterns[stepType as keyof typeof patterns] || [];
    
    stepPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        phrases.push(...matches.map(m => m.replace(pattern, '$1').trim()));
      }
    });

    return phrases;
  }

  /**
   * Estimate confidence based on language patterns
   */
  private static estimateConfidence(section: string): number {
    const lowerSection = section.toLowerCase();
    
    // High confidence indicators
    const highConfidenceWords = ['clearly', 'definitely', 'certainly', 'obviously', 'undoubtedly'];
    const mediumConfidenceWords = ['likely', 'probably', 'appears', 'seems', 'suggests'];
    const lowConfidenceWords = ['possibly', 'might', 'could', 'perhaps', 'maybe', 'uncertain'];

    let confidence = 0.7; // Base confidence

    highConfidenceWords.forEach(word => {
      if (lowerSection.includes(word)) confidence += 0.1;
    });

    mediumConfidenceWords.forEach(word => {
      if (lowerSection.includes(word)) confidence += 0.05;
    });

    lowConfidenceWords.forEach(word => {
      if (lowerSection.includes(word)) confidence -= 0.15;
    });

    return Math.max(0.1, Math.min(0.95, confidence));
  }

  /**
   * Extract uncertainty indicators from content
   */
  private static extractUncertainties(section: string): string[] {
    const uncertainties: string[] = [];
    const uncertaintyPatterns = [
      /(?:uncertain|unclear|ambiguous|difficult to determine) (?:about|whether|if) (.+?)(?:[,.]|$)/gi,
      /(?:might|could|possibly|perhaps) (.+?)(?:[,.]|$)/gi,
      /(?:need more|require additional|would benefit from) (.+?)(?:[,.]|$)/gi
    ];

    uncertaintyPatterns.forEach(pattern => {
      const matches = section.match(pattern);
      if (matches) {
        uncertainties.push(...matches.map(m => m.trim()));
      }
    });

    return uncertainties.slice(0, 3); // Limit to 3 uncertainties
  }

  /**
   * Extract evidence from content
   */
  private static extractEvidence(section: string): string[] {
    const evidence: string[] = [];
    const evidencePatterns = [
      /(?:based on|evidence of|shown by|indicated by) (.+?)(?:[,.]|$)/gi,
      /(?:the presence of|visible|apparent) (.+?)(?:[,.]|$)/gi,
      /(?:measurements show|dimensions indicate) (.+?)(?:[,.]|$)/gi
    ];

    evidencePatterns.forEach(pattern => {
      const matches = section.match(pattern);
      if (matches) {
        evidence.push(...matches.map(m => m.trim()));
      }
    });

    return evidence.slice(0, 3); // Limit to 3 evidence items
  }

  /**
   * Create basic reasoning flow when no structure is detected
   */
  private static createBasicReasoningFlow(content: string): ReasoningStep[] {
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 30);
    const steps: ReasoningStep[] = [];

    paragraphs.slice(0, 4).forEach((paragraph, index) => {
      const stepTypes: ReasoningStep['type'][] = ['observation', 'analysis', 'inference', 'conclusion'];
      
      steps.push({
        id: `step-${index + 1}`,
        type: stepTypes[index] || 'analysis',
        title: paragraph.substring(0, 50) + '...',
        content: paragraph.trim(),
        confidence: 0.7 + (Math.random() * 0.2),
        timestamp: new Date(),
        dependencies: index > 0 ? [`step-${index}`] : [],
        evidence: [],
        processingTime: Math.floor(Math.random() * 1500) + 300
      });
    });

    return steps;
  }

  /**
   * Add logical dependencies between steps
   */
  private static addDependencies(steps: ReasoningStep[]): void {
    steps.forEach((step, index) => {
      if (index > 0) {
        // Each step depends on the previous one by default
        step.dependencies.push(steps[index - 1].id);
        
        // Analysis steps might depend on multiple observations
        if (step.type === 'analysis' && index > 1) {
          const observations = steps.slice(0, index).filter(s => s.type === 'observation');
          if (observations.length > 0) {
            step.dependencies.push(...observations.map(o => o.id));
          }
        }
        
        // Conclusions depend on inferences
        if (step.type === 'conclusion') {
          const inferences = steps.slice(0, index).filter(s => s.type === 'inference');
          if (inferences.length > 0) {
            step.dependencies.push(...inferences.map(i => i.id));
          }
        }
      }
    });
  }

  /**
   * Calculate overall confidence from individual steps
   */
  private static calculateOverallConfidence(steps: ReasoningStep[]): number {
    if (steps.length === 0) return 0.5;
    
    const totalConfidence = steps.reduce((sum, step) => sum + step.confidence, 0);
    return totalConfidence / steps.length;
  }
}
