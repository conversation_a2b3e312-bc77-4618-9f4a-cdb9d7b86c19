import * as THREE from 'three';

export interface ParticleConfig {
  count: number;
  size: number;
  color: THREE.Color;
  opacity: number;
  speed: number;
  lifetime: number;
  emissionRate: number;
  spread: number;
  gravity: THREE.Vector3;
  turbulence: number;
}

export interface ParticleBehavior {
  type: 'flow' | 'attraction' | 'repulsion' | 'orbit' | 'spiral' | 'burst';
  strength: number;
  target?: THREE.Vector3;
  radius?: number;
  frequency?: number;
}

export interface ParticleEmitter {
  id: string;
  position: THREE.Vector3;
  direction: THREE.Vector3;
  config: ParticleConfig;
  behaviors: ParticleBehavior[];
  active: boolean;
}

export class ParticleSystem {
  private geometry: THREE.BufferGeometry;
  private material: THREE.PointsMaterial;
  private points: THREE.Points;
  private particles: Array<{
    position: THREE.Vector3;
    velocity: THREE.Vector3;
    life: number;
    maxLife: number;
    size: number;
    color: THREE.Color;
    opacity: number;
  }> = [];
  
  private emitters: Map<string, ParticleEmitter> = new Map();
  private maxParticles: number;
  private positions: Float32Array;
  private colors: Float32Array;
  private sizes: Float32Array;
  private opacities: Float32Array;
  
  private clock = new THREE.Clock();
  private isActive = false;

  constructor(maxParticles: number = 10000) {
    this.maxParticles = maxParticles;
    this.initializeBuffers();
    this.createGeometry();
    this.createMaterial();
    this.createPoints();
  }

  private initializeBuffers(): void {
    this.positions = new Float32Array(this.maxParticles * 3);
    this.colors = new Float32Array(this.maxParticles * 3);
    this.sizes = new Float32Array(this.maxParticles);
    this.opacities = new Float32Array(this.maxParticles);
  }

  private createGeometry(): void {
    this.geometry = new THREE.BufferGeometry();
    this.geometry.setAttribute('position', new THREE.BufferAttribute(this.positions, 3));
    this.geometry.setAttribute('color', new THREE.BufferAttribute(this.colors, 3));
    this.geometry.setAttribute('size', new THREE.BufferAttribute(this.sizes, 1));
    this.geometry.setAttribute('opacity', new THREE.BufferAttribute(this.opacities, 1));
  }

  private createMaterial(): void {
    this.material = new THREE.PointsMaterial({
      size: 0.1,
      vertexColors: true,
      transparent: true,
      opacity: 0.8,
      sizeAttenuation: true,
      blending: THREE.AdditiveBlending,
      depthWrite: false
    });

    // Custom shader for enhanced particle rendering
    this.material.onBeforeCompile = (shader) => {
      shader.vertexShader = shader.vertexShader.replace(
        'attribute float size;',
        `
        attribute float size;
        attribute float opacity;
        varying float vOpacity;
        `
      );

      shader.vertexShader = shader.vertexShader.replace(
        'gl_PointSize = size;',
        `
        gl_PointSize = size;
        vOpacity = opacity;
        `
      );

      shader.fragmentShader = shader.fragmentShader.replace(
        'varying vec3 vColor;',
        `
        varying vec3 vColor;
        varying float vOpacity;
        `
      );

      shader.fragmentShader = shader.fragmentShader.replace(
        'gl_FragColor = vec4( diffuse * vColor, opacity );',
        `
        float distanceToCenter = length(gl_PointCoord - vec2(0.5));
        float alpha = 1.0 - smoothstep(0.0, 0.5, distanceToCenter);
        gl_FragColor = vec4( diffuse * vColor, alpha * vOpacity * opacity );
        `
      );
    };
  }

  private createPoints(): void {
    this.points = new THREE.Points(this.geometry, this.material);
    this.points.frustumCulled = false;
  }

  /**
   * Add a particle emitter
   */
  addEmitter(emitter: ParticleEmitter): void {
    this.emitters.set(emitter.id, emitter);
  }

  /**
   * Remove a particle emitter
   */
  removeEmitter(id: string): void {
    this.emitters.delete(id);
  }

  /**
   * Get emitter by ID
   */
  getEmitter(id: string): ParticleEmitter | undefined {
    return this.emitters.get(id);
  }

  /**
   * Update emitter configuration
   */
  updateEmitter(id: string, updates: Partial<ParticleEmitter>): void {
    const emitter = this.emitters.get(id);
    if (emitter) {
      Object.assign(emitter, updates);
    }
  }

  /**
   * Start particle system
   */
  start(): void {
    this.isActive = true;
    this.clock.start();
  }

  /**
   * Stop particle system
   */
  stop(): void {
    this.isActive = false;
  }

  /**
   * Update particle system
   */
  update(): void {
    if (!this.isActive) return;

    const deltaTime = this.clock.getDelta();
    
    // Emit new particles
    this.emitParticles(deltaTime);
    
    // Update existing particles
    this.updateParticles(deltaTime);
    
    // Update GPU buffers
    this.updateBuffers();
  }

  private emitParticles(deltaTime: number): void {
    this.emitters.forEach(emitter => {
      if (!emitter.active) return;

      const particlesToEmit = Math.floor(emitter.config.emissionRate * deltaTime);
      
      for (let i = 0; i < particlesToEmit && this.particles.length < this.maxParticles; i++) {
        this.createParticle(emitter);
      }
    });
  }

  private createParticle(emitter: ParticleEmitter): void {
    const config = emitter.config;
    
    // Random position within spread
    const spreadRadius = config.spread;
    const randomOffset = new THREE.Vector3(
      (Math.random() - 0.5) * spreadRadius,
      (Math.random() - 0.5) * spreadRadius,
      (Math.random() - 0.5) * spreadRadius
    );
    
    const position = emitter.position.clone().add(randomOffset);
    
    // Random velocity based on direction and speed
    const velocity = emitter.direction.clone()
      .multiplyScalar(config.speed)
      .add(new THREE.Vector3(
        (Math.random() - 0.5) * config.speed * 0.5,
        (Math.random() - 0.5) * config.speed * 0.5,
        (Math.random() - 0.5) * config.speed * 0.5
      ));

    const particle = {
      position: position,
      velocity: velocity,
      life: config.lifetime,
      maxLife: config.lifetime,
      size: config.size * (0.8 + Math.random() * 0.4), // Size variation
      color: config.color.clone(),
      opacity: config.opacity
    };

    this.particles.push(particle);
  }

  private updateParticles(deltaTime: number): void {
    for (let i = this.particles.length - 1; i >= 0; i--) {
      const particle = this.particles[i];
      
      // Update life
      particle.life -= deltaTime;
      
      // Remove dead particles
      if (particle.life <= 0) {
        this.particles.splice(i, 1);
        continue;
      }
      
      // Apply behaviors from all emitters
      this.emitters.forEach(emitter => {
        if (emitter.active) {
          this.applyBehaviors(particle, emitter.behaviors, deltaTime);
        }
      });
      
      // Update position
      particle.position.add(particle.velocity.clone().multiplyScalar(deltaTime));
      
      // Update opacity based on life
      const lifeRatio = particle.life / particle.maxLife;
      particle.opacity = Math.sin(lifeRatio * Math.PI) * 0.8; // Fade in and out
    }
  }

  private applyBehaviors(particle: any, behaviors: ParticleBehavior[], deltaTime: number): void {
    behaviors.forEach(behavior => {
      switch (behavior.type) {
        case 'flow':
          this.applyFlowBehavior(particle, behavior, deltaTime);
          break;
        case 'attraction':
          this.applyAttractionBehavior(particle, behavior, deltaTime);
          break;
        case 'repulsion':
          this.applyRepulsionBehavior(particle, behavior, deltaTime);
          break;
        case 'orbit':
          this.applyOrbitBehavior(particle, behavior, deltaTime);
          break;
        case 'spiral':
          this.applySpiralBehavior(particle, behavior, deltaTime);
          break;
        case 'burst':
          this.applyBurstBehavior(particle, behavior, deltaTime);
          break;
      }
    });
  }

  private applyFlowBehavior(particle: any, behavior: ParticleBehavior, deltaTime: number): void {
    if (behavior.target) {
      const direction = behavior.target.clone().sub(particle.position).normalize();
      particle.velocity.add(direction.multiplyScalar(behavior.strength * deltaTime));
    }
  }

  private applyAttractionBehavior(particle: any, behavior: ParticleBehavior, deltaTime: number): void {
    if (behavior.target) {
      const distance = particle.position.distanceTo(behavior.target);
      if (distance > 0.1) {
        const force = behavior.target.clone().sub(particle.position).normalize();
        force.multiplyScalar(behavior.strength / (distance * distance) * deltaTime);
        particle.velocity.add(force);
      }
    }
  }

  private applyRepulsionBehavior(particle: any, behavior: ParticleBehavior, deltaTime: number): void {
    if (behavior.target) {
      const distance = particle.position.distanceTo(behavior.target);
      if (distance < (behavior.radius || 2)) {
        const force = particle.position.clone().sub(behavior.target).normalize();
        force.multiplyScalar(behavior.strength / (distance * distance) * deltaTime);
        particle.velocity.add(force);
      }
    }
  }

  private applyOrbitBehavior(particle: any, behavior: ParticleBehavior, deltaTime: number): void {
    if (behavior.target) {
      const radius = particle.position.distanceTo(behavior.target);
      const angle = Math.atan2(
        particle.position.z - behavior.target.z,
        particle.position.x - behavior.target.x
      );
      
      const newAngle = angle + (behavior.frequency || 1) * deltaTime;
      const newPosition = new THREE.Vector3(
        behavior.target.x + Math.cos(newAngle) * radius,
        particle.position.y,
        behavior.target.z + Math.sin(newAngle) * radius
      );
      
      particle.velocity.add(newPosition.sub(particle.position).multiplyScalar(behavior.strength));
    }
  }

  private applySpiralBehavior(particle: any, behavior: ParticleBehavior, deltaTime: number): void {
    if (behavior.target) {
      const toTarget = behavior.target.clone().sub(particle.position);
      const distance = toTarget.length();
      
      // Spiral inward
      const spiralForce = toTarget.normalize().multiplyScalar(behavior.strength * deltaTime);
      
      // Add rotational component
      const perpendicular = new THREE.Vector3(-toTarget.z, 0, toTarget.x).normalize();
      spiralForce.add(perpendicular.multiplyScalar(behavior.strength * 0.5 * deltaTime));
      
      particle.velocity.add(spiralForce);
    }
  }

  private applyBurstBehavior(particle: any, behavior: ParticleBehavior, deltaTime: number): void {
    if (behavior.target) {
      const fromTarget = particle.position.clone().sub(behavior.target).normalize();
      fromTarget.multiplyScalar(behavior.strength * deltaTime);
      particle.velocity.add(fromTarget);
    }
  }

  private updateBuffers(): void {
    const positionArray = this.geometry.attributes.position.array as Float32Array;
    const colorArray = this.geometry.attributes.color.array as Float32Array;
    const sizeArray = this.geometry.attributes.size.array as Float32Array;
    const opacityArray = this.geometry.attributes.opacity.array as Float32Array;

    // Clear arrays
    positionArray.fill(0);
    colorArray.fill(0);
    sizeArray.fill(0);
    opacityArray.fill(0);

    // Update with current particles
    for (let i = 0; i < this.particles.length && i < this.maxParticles; i++) {
      const particle = this.particles[i];
      const i3 = i * 3;

      // Position
      positionArray[i3] = particle.position.x;
      positionArray[i3 + 1] = particle.position.y;
      positionArray[i3 + 2] = particle.position.z;

      // Color
      colorArray[i3] = particle.color.r;
      colorArray[i3 + 1] = particle.color.g;
      colorArray[i3 + 2] = particle.color.b;

      // Size
      sizeArray[i] = particle.size;

      // Opacity
      opacityArray[i] = particle.opacity;
    }

    // Mark attributes as needing update
    this.geometry.attributes.position.needsUpdate = true;
    this.geometry.attributes.color.needsUpdate = true;
    this.geometry.attributes.size.needsUpdate = true;
    this.geometry.attributes.opacity.needsUpdate = true;

    // Update draw range
    this.geometry.setDrawRange(0, Math.min(this.particles.length, this.maxParticles));
  }

  /**
   * Get the Three.js Points object for adding to scene
   */
  getPoints(): THREE.Points {
    return this.points;
  }

  /**
   * Get current particle count
   */
  getParticleCount(): number {
    return this.particles.length;
  }

  /**
   * Clear all particles
   */
  clear(): void {
    this.particles = [];
    this.updateBuffers();
  }

  /**
   * Dispose of resources
   */
  dispose(): void {
    this.stop();
    this.clear();
    this.geometry.dispose();
    this.material.dispose();
    this.emitters.clear();
  }
}
