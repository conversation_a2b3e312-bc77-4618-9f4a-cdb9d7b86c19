import * as THREE from 'three';
import { cinematicCameraController, CameraTransition, TransitionPreset } from './CinematicCameraController';

export interface SceneTransitionConfig {
  type: '2d-to-3d' | '3d-to-2d' | 'focus-object' | 'overview' | 'custom';
  preset: 'professional' | 'dramatic' | 'subtle';
  duration?: number;
  onStart?: () => void;
  onComplete?: () => void;
  onProgress?: (progress: number) => void;
}

export interface ViewState {
  mode: '2d' | '3d';
  cameraPosition: THREE.Vector3;
  cameraTarget: THREE.Vector3;
  fov: number;
  zoom: number;
}

export interface TransitionContext {
  fromState: ViewState;
  toState: ViewState;
  selectedObject?: {
    id: string;
    position: THREE.Vector3;
    boundingBox: THREE.Box3;
  };
  sceneObjects: Array<{
    id: string;
    position: THREE.Vector3;
    visible: boolean;
  }>;
}

export class SceneTransitionManager {
  private currentViewState: ViewState;
  private transitionHistory: ViewState[] = [];
  private maxHistorySize = 10;
  private isTransitioning = false;

  constructor() {
    this.currentViewState = {
      mode: '2d',
      cameraPosition: new THREE.Vector3(0, 0, 8),
      cameraTarget: new THREE.Vector3(0, 0, 0),
      fov: 60,
      zoom: 1
    };
  }

  /**
   * Get current view state
   */
  getCurrentViewState(): ViewState {
    return { ...this.currentViewState };
  }

  /**
   * Set current view state
   */
  setCurrentViewState(state: ViewState): void {
    this.addToHistory(this.currentViewState);
    this.currentViewState = { ...state };
  }

  /**
   * Add state to history
   */
  private addToHistory(state: ViewState): void {
    this.transitionHistory.push({ ...state });
    if (this.transitionHistory.length > this.maxHistorySize) {
      this.transitionHistory.shift();
    }
  }

  /**
   * Go back to previous view state
   */
  async goBack(config?: Partial<SceneTransitionConfig>): Promise<void> {
    if (this.transitionHistory.length === 0) {
      console.warn('No previous view state to return to');
      return;
    }

    const previousState = this.transitionHistory.pop()!;
    const transitionConfig: SceneTransitionConfig = {
      type: 'custom',
      preset: 'professional',
      duration: 800,
      ...config
    };

    await this.executeTransition(previousState, transitionConfig);
  }

  /**
   * Transition from 2D to 3D view
   */
  async transitionTo3D(config: Partial<SceneTransitionConfig> = {}): Promise<void> {
    if (this.currentViewState.mode === '3d') {
      console.warn('Already in 3D mode');
      return;
    }

    const transitionConfig: SceneTransitionConfig = {
      type: '2d-to-3d',
      preset: 'professional',
      ...config
    };

    const targetState: ViewState = {
      mode: '3d',
      cameraPosition: new THREE.Vector3(5, 5, 5),
      cameraTarget: new THREE.Vector3(0, 0, 0),
      fov: 60,
      zoom: 1
    };

    await this.executeTransition(targetState, transitionConfig);
  }

  /**
   * Transition from 3D to 2D view
   */
  async transitionTo2D(config: Partial<SceneTransitionConfig> = {}): Promise<void> {
    if (this.currentViewState.mode === '2d') {
      console.warn('Already in 2D mode');
      return;
    }

    const transitionConfig: SceneTransitionConfig = {
      type: '3d-to-2d',
      preset: 'professional',
      ...config
    };

    const targetState: ViewState = {
      mode: '2d',
      cameraPosition: new THREE.Vector3(0, 0, 8),
      cameraTarget: new THREE.Vector3(0, 0, 0),
      fov: 60,
      zoom: 1
    };

    await this.executeTransition(targetState, transitionConfig);
  }

  /**
   * Focus on a specific object
   */
  async focusOnObject(
    objectId: string,
    objectPosition: THREE.Vector3,
    boundingBox: THREE.Box3,
    config: Partial<SceneTransitionConfig> = {}
  ): Promise<void> {
    const transitionConfig: SceneTransitionConfig = {
      type: 'focus-object',
      preset: 'professional',
      ...config
    };

    // Calculate optimal camera position based on object bounding box
    const size = boundingBox.getSize(new THREE.Vector3());
    const maxDim = Math.max(size.x, size.y, size.z);
    const distance = maxDim * 2.5; // Adjust multiplier for desired framing

    const targetState: ViewState = {
      mode: this.currentViewState.mode,
      cameraPosition: objectPosition.clone().add(new THREE.Vector3(distance, distance, distance)),
      cameraTarget: objectPosition.clone(),
      fov: 45, // Tighter FOV for focus
      zoom: 1.2
    };

    await this.executeTransition(targetState, transitionConfig);
  }

  /**
   * Return to overview
   */
  async returnToOverview(config: Partial<SceneTransitionConfig> = {}): Promise<void> {
    const transitionConfig: SceneTransitionConfig = {
      type: 'overview',
      preset: 'professional',
      ...config
    };

    const targetState: ViewState = {
      mode: this.currentViewState.mode,
      cameraPosition: this.currentViewState.mode === '3d' 
        ? new THREE.Vector3(8, 8, 8)
        : new THREE.Vector3(0, 0, 10),
      cameraTarget: new THREE.Vector3(0, 0, 0),
      fov: 60,
      zoom: 1
    };

    await this.executeTransition(targetState, transitionConfig);
  }

  /**
   * Execute transition to target state
   */
  private async executeTransition(
    targetState: ViewState,
    config: SceneTransitionConfig
  ): Promise<void> {
    if (this.isTransitioning) {
      console.warn('Transition already in progress');
      return;
    }

    this.isTransitioning = true;

    try {
      // Get appropriate transition from preset
      const preset = cinematicCameraController.getPreset(config.preset);
      if (!preset) {
        throw new Error(`Preset '${config.preset}' not found`);
      }

      let transition: CameraTransition;

      switch (config.type) {
        case '2d-to-3d':
          transition = preset.transitions.enter3D;
          break;
        case '3d-to-2d':
          transition = preset.transitions.enter2D;
          break;
        case 'focus-object':
          transition = preset.transitions.focusObject;
          break;
        case 'overview':
          transition = preset.transitions.overview;
          break;
        case 'custom':
          transition = cinematicCameraController.createTransition(
            'Custom Transition',
            {
              position: this.currentViewState.cameraPosition,
              target: this.currentViewState.cameraTarget,
              fov: this.currentViewState.fov
            },
            {
              position: targetState.cameraPosition,
              target: targetState.cameraTarget,
              fov: targetState.fov
            },
            config.duration || 1000
          );
          break;
        default:
          throw new Error(`Unknown transition type: ${config.type}`);
      }

      // Override transition endpoints with actual target state
      transition.to.position = targetState.cameraPosition;
      transition.to.target = targetState.cameraTarget;
      transition.to.fov = targetState.fov;

      // Call onStart callback
      if (config.onStart) {
        config.onStart();
      }

      // Execute the transition
      await cinematicCameraController.executeTransition(transition, () => {
        this.setCurrentViewState(targetState);
        this.isTransitioning = false;
        
        if (config.onComplete) {
          config.onComplete();
        }
      });

    } catch (error) {
      this.isTransitioning = false;
      console.error('Transition failed:', error);
      throw error;
    }
  }

  /**
   * Create smooth transition between any two view states
   */
  async createCustomTransition(
    fromState: ViewState,
    toState: ViewState,
    duration: number = 1000,
    easing: CameraTransition['easing'] = 'ease-in-out',
    onComplete?: () => void
  ): Promise<void> {
    const transition = cinematicCameraController.createTransition(
      'Custom State Transition',
      {
        position: fromState.cameraPosition,
        target: fromState.cameraTarget,
        fov: fromState.fov
      },
      {
        position: toState.cameraPosition,
        target: toState.cameraTarget,
        fov: toState.fov
      },
      duration,
      easing
    );

    await cinematicCameraController.executeTransition(transition, () => {
      this.setCurrentViewState(toState);
      if (onComplete) {
        onComplete();
      }
    });
  }

  /**
   * Stop any current transition
   */
  stopTransition(): void {
    cinematicCameraController.stopTransition();
    this.isTransitioning = false;
  }

  /**
   * Check if currently transitioning
   */
  isCurrentlyTransitioning(): boolean {
    return this.isTransitioning;
  }

  /**
   * Get transition history
   */
  getTransitionHistory(): ViewState[] {
    return [...this.transitionHistory];
  }

  /**
   * Clear transition history
   */
  clearHistory(): void {
    this.transitionHistory = [];
  }

  /**
   * Calculate optimal camera position for viewing multiple objects
   */
  calculateOptimalViewForObjects(objects: Array<{ position: THREE.Vector3; boundingBox: THREE.Box3 }>): ViewState {
    if (objects.length === 0) {
      return this.currentViewState;
    }

    // Calculate bounding box for all objects
    const overallBoundingBox = new THREE.Box3();
    objects.forEach(obj => {
      overallBoundingBox.expandByPoint(obj.position);
      overallBoundingBox.union(obj.boundingBox);
    });

    const center = overallBoundingBox.getCenter(new THREE.Vector3());
    const size = overallBoundingBox.getSize(new THREE.Vector3());
    const maxDim = Math.max(size.x, size.y, size.z);
    const distance = maxDim * 2;

    return {
      mode: '3d',
      cameraPosition: center.clone().add(new THREE.Vector3(distance, distance, distance)),
      cameraTarget: center,
      fov: 60,
      zoom: 1
    };
  }

  /**
   * Dispose of resources
   */
  dispose(): void {
    this.stopTransition();
    this.transitionHistory = [];
  }
}

// Singleton instance
export const sceneTransitionManager = new SceneTransitionManager();
