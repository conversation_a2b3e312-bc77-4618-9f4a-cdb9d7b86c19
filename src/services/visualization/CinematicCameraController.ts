import * as THREE from 'three';
import { Camera } from '@react-three/fiber';

export interface CameraTransition {
  id: string;
  name: string;
  duration: number;
  easing: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'cubic-bezier';
  from: {
    position: THREE.Vector3;
    target: THREE.Vector3;
    fov?: number;
  };
  to: {
    position: THREE.Vector3;
    target: THREE.Vector3;
    fov?: number;
  };
}

export interface CameraPath {
  id: string;
  name: string;
  keyframes: Array<{
    time: number; // 0-1
    position: THREE.Vector3;
    target: THREE.Vector3;
    fov?: number;
  }>;
  duration: number;
  loop: boolean;
}

export interface TransitionPreset {
  id: string;
  name: string;
  description: string;
  transitions: {
    enter2D: CameraTransition;
    enter3D: CameraTransition;
    focusObject: CameraTransition;
    overview: CameraTransition;
  };
}

export class CinematicCameraController {
  private camera: THREE.Camera | null = null;
  private controls: any = null;
  private isTransitioning = false;
  private currentTransition: CameraTransition | null = null;
  private animationId: number | null = null;
  private onTransitionComplete?: () => void;

  // Easing functions
  private easingFunctions = {
    linear: (t: number) => t,
    'ease-in': (t: number) => t * t,
    'ease-out': (t: number) => t * (2 - t),
    'ease-in-out': (t: number) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
    'cubic-bezier': (t: number) => t * t * (3 - 2 * t), // Smooth cubic
  };

  // Predefined transition presets
  private presets: TransitionPreset[] = [
    {
      id: 'professional',
      name: 'Professional',
      description: 'Smooth, business-appropriate transitions',
      transitions: {
        enter2D: {
          id: 'prof-2d',
          name: 'Professional 2D Entry',
          duration: 1200,
          easing: 'ease-in-out',
          from: { position: new THREE.Vector3(0, 10, 10), target: new THREE.Vector3(0, 0, 0) },
          to: { position: new THREE.Vector3(0, 0, 8), target: new THREE.Vector3(0, 0, 0) }
        },
        enter3D: {
          id: 'prof-3d',
          name: 'Professional 3D Entry',
          duration: 1500,
          easing: 'ease-in-out',
          from: { position: new THREE.Vector3(0, 0, 8), target: new THREE.Vector3(0, 0, 0) },
          to: { position: new THREE.Vector3(5, 5, 5), target: new THREE.Vector3(0, 0, 0) }
        },
        focusObject: {
          id: 'prof-focus',
          name: 'Professional Focus',
          duration: 800,
          easing: 'ease-out',
          from: { position: new THREE.Vector3(5, 5, 5), target: new THREE.Vector3(0, 0, 0) },
          to: { position: new THREE.Vector3(2, 2, 2), target: new THREE.Vector3(0, 0, 0) }
        },
        overview: {
          id: 'prof-overview',
          name: 'Professional Overview',
          duration: 1000,
          easing: 'ease-in-out',
          from: { position: new THREE.Vector3(2, 2, 2), target: new THREE.Vector3(0, 0, 0) },
          to: { position: new THREE.Vector3(8, 8, 8), target: new THREE.Vector3(0, 0, 0) }
        }
      }
    },
    {
      id: 'dramatic',
      name: 'Dramatic',
      description: 'Cinematic, attention-grabbing transitions',
      transitions: {
        enter2D: {
          id: 'drama-2d',
          name: 'Dramatic 2D Entry',
          duration: 2000,
          easing: 'cubic-bezier',
          from: { position: new THREE.Vector3(-15, 15, 15), target: new THREE.Vector3(0, 0, 0) },
          to: { position: new THREE.Vector3(0, 0, 8), target: new THREE.Vector3(0, 0, 0) }
        },
        enter3D: {
          id: 'drama-3d',
          name: 'Dramatic 3D Entry',
          duration: 2500,
          easing: 'ease-in-out',
          from: { position: new THREE.Vector3(0, 0, 8), target: new THREE.Vector3(0, 0, 0) },
          to: { position: new THREE.Vector3(10, 10, 10), target: new THREE.Vector3(0, 0, 0) }
        },
        focusObject: {
          id: 'drama-focus',
          name: 'Dramatic Focus',
          duration: 1200,
          easing: 'ease-in',
          from: { position: new THREE.Vector3(10, 10, 10), target: new THREE.Vector3(0, 0, 0) },
          to: { position: new THREE.Vector3(1.5, 1.5, 1.5), target: new THREE.Vector3(0, 0, 0) }
        },
        overview: {
          id: 'drama-overview',
          name: 'Dramatic Overview',
          duration: 1800,
          easing: 'ease-out',
          from: { position: new THREE.Vector3(1.5, 1.5, 1.5), target: new THREE.Vector3(0, 0, 0) },
          to: { position: new THREE.Vector3(12, 12, 12), target: new THREE.Vector3(0, 0, 0) }
        }
      }
    },
    {
      id: 'subtle',
      name: 'Subtle',
      description: 'Gentle, minimal transitions',
      transitions: {
        enter2D: {
          id: 'subtle-2d',
          name: 'Subtle 2D Entry',
          duration: 800,
          easing: 'ease-out',
          from: { position: new THREE.Vector3(0, 2, 6), target: new THREE.Vector3(0, 0, 0) },
          to: { position: new THREE.Vector3(0, 0, 5), target: new THREE.Vector3(0, 0, 0) }
        },
        enter3D: {
          id: 'subtle-3d',
          name: 'Subtle 3D Entry',
          duration: 1000,
          easing: 'ease-in-out',
          from: { position: new THREE.Vector3(0, 0, 5), target: new THREE.Vector3(0, 0, 0) },
          to: { position: new THREE.Vector3(3, 3, 3), target: new THREE.Vector3(0, 0, 0) }
        },
        focusObject: {
          id: 'subtle-focus',
          name: 'Subtle Focus',
          duration: 600,
          easing: 'ease-in-out',
          from: { position: new THREE.Vector3(3, 3, 3), target: new THREE.Vector3(0, 0, 0) },
          to: { position: new THREE.Vector3(2, 2, 2), target: new THREE.Vector3(0, 0, 0) }
        },
        overview: {
          id: 'subtle-overview',
          name: 'Subtle Overview',
          duration: 800,
          easing: 'ease-in-out',
          from: { position: new THREE.Vector3(2, 2, 2), target: new THREE.Vector3(0, 0, 0) },
          to: { position: new THREE.Vector3(6, 6, 6), target: new THREE.Vector3(0, 0, 0) }
        }
      }
    }
  ];

  constructor() {
    this.bindMethods();
  }

  private bindMethods(): void {
    this.setCamera = this.setCamera.bind(this);
    this.setControls = this.setControls.bind(this);
    this.executeTransition = this.executeTransition.bind(this);
    this.stopTransition = this.stopTransition.bind(this);
  }

  /**
   * Set the camera reference
   */
  setCamera(camera: THREE.Camera): void {
    this.camera = camera;
  }

  /**
   * Set the controls reference (OrbitControls, etc.)
   */
  setControls(controls: any): void {
    this.controls = controls;
  }

  /**
   * Get available transition presets
   */
  getPresets(): TransitionPreset[] {
    return [...this.presets];
  }

  /**
   * Get a specific preset by ID
   */
  getPreset(id: string): TransitionPreset | null {
    return this.presets.find(preset => preset.id === id) || null;
  }

  /**
   * Execute a camera transition
   */
  async executeTransition(
    transition: CameraTransition,
    onComplete?: () => void
  ): Promise<void> {
    if (!this.camera) {
      throw new Error('Camera not set. Call setCamera() first.');
    }

    if (this.isTransitioning) {
      this.stopTransition();
    }

    this.isTransitioning = true;
    this.currentTransition = transition;
    this.onTransitionComplete = onComplete;

    return new Promise((resolve) => {
      const startTime = performance.now();
      const startPosition = this.camera!.position.clone();
      const startTarget = this.controls?.target?.clone() || new THREE.Vector3(0, 0, 0);
      const startFov = (this.camera as THREE.PerspectiveCamera).fov || 60;

      const animate = (currentTime: number) => {
        if (!this.isTransitioning || !this.currentTransition) {
          resolve();
          return;
        }

        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / transition.duration, 1);
        const easedProgress = this.easingFunctions[transition.easing](progress);

        // Interpolate position
        const currentPosition = startPosition.clone().lerp(transition.to.position, easedProgress);
        this.camera!.position.copy(currentPosition);

        // Interpolate target (if controls available)
        if (this.controls && this.controls.target) {
          const currentTarget = startTarget.clone().lerp(transition.to.target, easedProgress);
          this.controls.target.copy(currentTarget);
          this.controls.update();
        }

        // Interpolate FOV (if specified and camera is perspective)
        if (transition.to.fov && this.camera instanceof THREE.PerspectiveCamera) {
          const targetFov = transition.to.fov;
          const currentFov = startFov + (targetFov - startFov) * easedProgress;
          this.camera.fov = currentFov;
          this.camera.updateProjectionMatrix();
        }

        if (progress >= 1) {
          this.isTransitioning = false;
          this.currentTransition = null;
          if (this.onTransitionComplete) {
            this.onTransitionComplete();
          }
          resolve();
        } else {
          this.animationId = requestAnimationFrame(animate);
        }
      };

      this.animationId = requestAnimationFrame(animate);
    });
  }

  /**
   * Stop current transition
   */
  stopTransition(): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    this.isTransitioning = false;
    this.currentTransition = null;
  }

  /**
   * Check if currently transitioning
   */
  isCurrentlyTransitioning(): boolean {
    return this.isTransitioning;
  }

  /**
   * Create a custom transition
   */
  createTransition(
    name: string,
    from: { position: THREE.Vector3; target: THREE.Vector3; fov?: number },
    to: { position: THREE.Vector3; target: THREE.Vector3; fov?: number },
    duration: number = 1000,
    easing: CameraTransition['easing'] = 'ease-in-out'
  ): CameraTransition {
    return {
      id: `custom_${Date.now()}`,
      name,
      duration,
      easing,
      from,
      to
    };
  }

  /**
   * Cleanup resources
   */
  dispose(): void {
    this.stopTransition();
    this.camera = null;
    this.controls = null;
    this.onTransitionComplete = undefined;
  }
}

// Singleton instance
export const cinematicCameraController = new CinematicCameraController();
