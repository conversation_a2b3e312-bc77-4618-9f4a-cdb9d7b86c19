import * as Y from 'yjs';
import { WebsocketProvider } from 'y-websocket';
import { Socket } from 'socket.io-client';

export interface SyncOperation {
  id: string;
  type: 'insert' | 'delete' | 'update' | 'move';
  path: string[];
  data: any;
  timestamp: number;
  userId: string;
  version: number;
}

export interface SyncState {
  version: number;
  operations: SyncOperation[];
  lastSyncTime: number;
}

export interface ConflictResolution {
  operation: SyncOperation;
  resolution: 'accept' | 'reject' | 'merge';
  mergedData?: any;
}

export class CollaborationSyncService {
  private ydoc: Y.Doc;
  private provider: WebsocketProvider | null = null;
  private socket: Socket | null = null;
  private projectId: string | null = null;
  private userId: string | null = null;
  private localState: Map<string, any> = new Map();
  private pendingOperations: SyncOperation[] = [];
  private operationQueue: SyncOperation[] = [];
  private isConnected = false;
  private syncCallbacks: Map<string, (data: any) => void> = new Map();

  constructor() {
    this.ydoc = new Y.Doc();
    this.setupYjsHandlers();
  }

  /**
   * Initialize synchronization for a project
   */
  public async initializeSync(
    projectId: string, 
    userId: string, 
    socket: Socket,
    wsUrl?: string
  ): Promise<void> {
    this.projectId = projectId;
    this.userId = userId;
    this.socket = socket;

    // Setup WebSocket provider for Yjs
    if (wsUrl) {
      this.provider = new WebsocketProvider(
        wsUrl,
        `project-${projectId}`,
        this.ydoc
      );

      this.provider.on('status', (event: any) => {
        this.isConnected = event.status === 'connected';
        this.handleConnectionChange(this.isConnected);
      });
    }

    // Setup Socket.IO handlers for custom operations
    this.setupSocketHandlers();

    // Initialize shared data structures
    this.initializeSharedStructures();
  }

  /**
   * Setup Yjs event handlers
   */
  private setupYjsHandlers(): void {
    this.ydoc.on('update', (update: Uint8Array, origin: any) => {
      if (origin !== this) {
        // Handle remote updates
        this.handleRemoteUpdate(update);
      }
    });
  }

  /**
   * Setup Socket.IO event handlers
   */
  private setupSocketHandlers(): void {
    if (!this.socket) return;

    this.socket.on('sync-operation', (operation: SyncOperation) => {
      this.handleRemoteOperation(operation);
    });

    this.socket.on('sync-conflict', (conflict: ConflictResolution) => {
      this.handleConflictResolution(conflict);
    });

    this.socket.on('sync-state-request', () => {
      this.sendCurrentState();
    });

    this.socket.on('sync-state-update', (state: SyncState) => {
      this.handleStateUpdate(state);
    });
  }

  /**
   * Initialize shared data structures
   */
  private initializeSharedStructures(): void {
    // Project metadata
    const projectMeta = this.ydoc.getMap('projectMeta');
    
    // Analysis results
    const analysisResults = this.ydoc.getMap('analysisResults');
    
    // Comments and annotations
    const comments = this.ydoc.getArray('comments');
    const annotations = this.ydoc.getArray('annotations');
    
    // User cursors and presence
    const userCursors = this.ydoc.getMap('userCursors');
    const userPresence = this.ydoc.getMap('userPresence');

    // 3D scene state
    const sceneState = this.ydoc.getMap('sceneState');
  }

  /**
   * Apply local operation and sync with others
   */
  public async applyOperation(operation: Omit<SyncOperation, 'id' | 'timestamp' | 'userId' | 'version'>): Promise<void> {
    const fullOperation: SyncOperation = {
      ...operation,
      id: this.generateOperationId(),
      timestamp: Date.now(),
      userId: this.userId!,
      version: this.getNextVersion()
    };

    // Apply operation locally with optimistic update
    const success = this.applyLocalOperation(fullOperation);
    
    if (success) {
      // Add to pending operations
      this.pendingOperations.push(fullOperation);
      
      // Send to server
      await this.sendOperation(fullOperation);
    }
  }

  /**
   * Apply operation locally
   */
  private applyLocalOperation(operation: SyncOperation): boolean {
    try {
      const target = this.getTargetFromPath(operation.path);
      
      switch (operation.type) {
        case 'insert':
          this.handleInsertOperation(target, operation);
          break;
        case 'delete':
          this.handleDeleteOperation(target, operation);
          break;
        case 'update':
          this.handleUpdateOperation(target, operation);
          break;
        case 'move':
          this.handleMoveOperation(target, operation);
          break;
      }

      // Trigger local callbacks
      this.triggerSyncCallbacks(operation.path[0], target);
      return true;
    } catch (error) {
      console.error('Failed to apply local operation:', error);
      return false;
    }
  }

  /**
   * Handle remote operation
   */
  private handleRemoteOperation(operation: SyncOperation): void {
    // Check for conflicts
    const conflict = this.detectConflict(operation);
    
    if (conflict) {
      this.resolveConflict(operation, conflict);
    } else {
      // Apply operation directly
      this.applyRemoteOperation(operation);
    }
  }

  /**
   * Apply remote operation
   */
  private applyRemoteOperation(operation: SyncOperation): void {
    try {
      const target = this.getTargetFromPath(operation.path);
      
      switch (operation.type) {
        case 'insert':
          this.handleInsertOperation(target, operation);
          break;
        case 'delete':
          this.handleDeleteOperation(target, operation);
          break;
        case 'update':
          this.handleUpdateOperation(target, operation);
          break;
        case 'move':
          this.handleMoveOperation(target, operation);
          break;
      }

      // Remove from pending if it was our operation
      this.pendingOperations = this.pendingOperations.filter(
        op => op.id !== operation.id
      );

      // Trigger callbacks
      this.triggerSyncCallbacks(operation.path[0], target);
    } catch (error) {
      console.error('Failed to apply remote operation:', error);
    }
  }

  /**
   * Detect conflicts between operations
   */
  private detectConflict(remoteOp: SyncOperation): SyncOperation | null {
    return this.pendingOperations.find(localOp => 
      this.operationsConflict(localOp, remoteOp)
    ) || null;
  }

  /**
   * Check if two operations conflict
   */
  private operationsConflict(op1: SyncOperation, op2: SyncOperation): boolean {
    // Same path and overlapping time window
    if (this.pathsOverlap(op1.path, op2.path)) {
      const timeDiff = Math.abs(op1.timestamp - op2.timestamp);
      return timeDiff < 1000; // 1 second window
    }
    return false;
  }

  /**
   * Resolve conflict between operations
   */
  private resolveConflict(remoteOp: SyncOperation, localOp: SyncOperation): void {
    // Simple conflict resolution: last writer wins with user priority
    const resolution: ConflictResolution = {
      operation: remoteOp,
      resolution: 'accept' // Could be more sophisticated
    };

    if (remoteOp.timestamp > localOp.timestamp) {
      // Accept remote operation, rollback local
      this.rollbackOperation(localOp);
      this.applyRemoteOperation(remoteOp);
      resolution.resolution = 'accept';
    } else {
      // Keep local operation, reject remote
      resolution.resolution = 'reject';
    }

    // Notify about conflict resolution
    this.handleConflictResolution(resolution);
  }

  /**
   * Rollback a local operation
   */
  private rollbackOperation(operation: SyncOperation): void {
    // Create inverse operation
    const inverseOp: SyncOperation = {
      ...operation,
      id: this.generateOperationId(),
      type: this.getInverseOperationType(operation.type),
      timestamp: Date.now()
    };

    this.applyLocalOperation(inverseOp);
  }

  /**
   * Get inverse operation type
   */
  private getInverseOperationType(type: SyncOperation['type']): SyncOperation['type'] {
    switch (type) {
      case 'insert': return 'delete';
      case 'delete': return 'insert';
      case 'update': return 'update';
      case 'move': return 'move';
    }
  }

  /**
   * Handle specific operation types
   */
  private handleInsertOperation(target: any, operation: SyncOperation): void {
    if (target instanceof Y.Array) {
      target.insert(operation.data.index || 0, [operation.data.value]);
    } else if (target instanceof Y.Map) {
      target.set(operation.data.key, operation.data.value);
    }
  }

  private handleDeleteOperation(target: any, operation: SyncOperation): void {
    if (target instanceof Y.Array) {
      target.delete(operation.data.index, operation.data.length || 1);
    } else if (target instanceof Y.Map) {
      target.delete(operation.data.key);
    }
  }

  private handleUpdateOperation(target: any, operation: SyncOperation): void {
    if (target instanceof Y.Map) {
      target.set(operation.data.key, operation.data.value);
    }
  }

  private handleMoveOperation(target: any, operation: SyncOperation): void {
    if (target instanceof Y.Array) {
      const item = target.get(operation.data.fromIndex);
      target.delete(operation.data.fromIndex, 1);
      target.insert(operation.data.toIndex, [item]);
    }
  }

  /**
   * Utility methods
   */
  private getTargetFromPath(path: string[]): any {
    let target: any = this.ydoc;
    
    for (const segment of path) {
      if (target instanceof Y.Doc) {
        target = target.getMap(segment) || target.getArray(segment);
      } else if (target instanceof Y.Map) {
        target = target.get(segment);
      } else if (target instanceof Y.Array) {
        target = target.get(parseInt(segment));
      }
    }
    
    return target;
  }

  private pathsOverlap(path1: string[], path2: string[]): boolean {
    const minLength = Math.min(path1.length, path2.length);
    for (let i = 0; i < minLength; i++) {
      if (path1[i] !== path2[i]) return false;
    }
    return true;
  }

  private generateOperationId(): string {
    return `${this.userId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private getNextVersion(): number {
    return Date.now();
  }

  private async sendOperation(operation: SyncOperation): Promise<void> {
    if (this.socket) {
      this.socket.emit('sync-operation', operation);
    }
  }

  private sendCurrentState(): void {
    if (this.socket) {
      const state: SyncState = {
        version: this.getNextVersion(),
        operations: this.pendingOperations,
        lastSyncTime: Date.now()
      };
      this.socket.emit('sync-state', state);
    }
  }

  private handleRemoteUpdate(update: Uint8Array): void {
    // Handle Yjs updates from other clients
    console.log('Received remote Yjs update');
  }

  private handleConnectionChange(connected: boolean): void {
    this.isConnected = connected;
    if (connected) {
      // Resync when reconnected
      this.sendCurrentState();
    }
  }

  private handleConflictResolution(resolution: ConflictResolution): void {
    console.log('Conflict resolved:', resolution);
  }

  private handleStateUpdate(state: SyncState): void {
    // Apply state updates from server
    console.log('State update received:', state);
  }

  private triggerSyncCallbacks(dataType: string, data: any): void {
    const callback = this.syncCallbacks.get(dataType);
    if (callback) {
      callback(data);
    }
  }

  /**
   * Public API methods
   */
  public onSync(dataType: string, callback: (data: any) => void): void {
    this.syncCallbacks.set(dataType, callback);
  }

  public getSharedData(path: string[]): any {
    return this.getTargetFromPath(path);
  }

  public isOnline(): boolean {
    return this.isConnected;
  }

  public disconnect(): void {
    if (this.provider) {
      this.provider.disconnect();
    }
    this.syncCallbacks.clear();
    this.pendingOperations = [];
  }
}

export const collaborationSyncService = new CollaborationSyncService();
