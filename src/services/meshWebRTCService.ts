import { Socket } from 'socket.io-client';
import SimplePeer from 'simple-peer';

export interface MeshPeerConnection {
  id: string;
  userId: string;
  peer: SimplePeer.Instance;
  connectionState: 'connecting' | 'connected' | 'disconnected' | 'failed';
  connectionQuality: number;
  latency: number;
  bandwidth: number;
  lastActivity: number;
  dataChannel?: RTCDataChannel;
}

export interface MeshNetworkConfig {
  iceServers: RTCIceServer[];
  maxPeers: number;
  connectionTimeout: number;
  qualityCheckInterval: number;
  enableDataChannels: boolean;
  enableAudio: boolean;
  enableVideo: boolean;
}

export interface MeshTopologyInfo {
  projectId: string;
  totalPeers: number;
  connections: Array<{
    peerId: string;
    connectedTo: string[];
  }>;
  lastUpdated: number;
}

export interface CursorData {
  userId: string;
  x: number;
  y: number;
  timestamp: number;
  elementId?: string;
}

export interface MeshDataMessage {
  type: 'cursor' | 'voice-comment' | 'annotation' | 'scene-update' | 'quote-update';
  data: any;
  timestamp: number;
  fromUserId: string;
}

/**
 * Advanced P2P Mesh WebRTC Service
 * 
 * Extends the existing WebRTC infrastructure to support full mesh topology
 * for 3-10 concurrent users in Cabinet Insight Pro collaboration
 */
export class MeshWebRTCService {
  private socket: Socket | null = null;
  private projectId: string | null = null;
  private userId: string | null = null;
  private meshConnections: Map<string, MeshPeerConnection> = new Map();
  private config: MeshNetworkConfig;
  private isInitialized = false;
  private qualityMonitorTimer: NodeJS.Timeout | null = null;
  private meshTopology: MeshTopologyInfo | null = null;

  // Event handlers
  private onPeerJoinedHandler?: (peerId: string, userId: string) => void;
  private onPeerLeftHandler?: (peerId: string, userId: string) => void;
  private onDataReceivedHandler?: (data: MeshDataMessage, fromPeerId: string) => void;
  private onTopologyUpdatedHandler?: (topology: MeshTopologyInfo) => void;
  private onConnectionQualityChangedHandler?: (peerId: string, quality: number) => void;

  constructor(config?: Partial<MeshNetworkConfig>) {
    this.config = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
      ],
      maxPeers: 6,
      connectionTimeout: 10000,
      qualityCheckInterval: 5000,
      enableDataChannels: true,
      enableAudio: false,
      enableVideo: false,
      ...config
    };
  }

  /**
   * Initialize mesh WebRTC service
   */
  async initialize(socket: Socket, userId: string, projectId: string): Promise<void> {
    if (this.isInitialized) {
      console.warn('MeshWebRTCService already initialized');
      return;
    }

    this.socket = socket;
    this.userId = userId;
    this.projectId = projectId;

    try {
      this.setupSocketHandlers();
      this.startQualityMonitoring();

      // Join mesh network
      await this.joinMeshNetwork();

      this.isInitialized = true;
      console.log('MeshWebRTCService initialized successfully', { userId, projectId });

    } catch (error) {
      console.error('Failed to initialize MeshWebRTCService:', error);
      throw error;
    }
  }

  /**
   * Setup Socket.IO event handlers for mesh networking
   */
  private setupSocketHandlers(): void {
    if (!this.socket) return;

    // Handle successful mesh join
    this.socket.on('mesh-joined', (data: {
      peerId: string;
      topology: MeshTopologyInfo;
    }) => {
      this.meshTopology = data.topology;
      console.log('Successfully joined mesh network', data);
      this.onTopologyUpdatedHandler?.(data.topology);
    });

    // Handle peer discovery response
    this.socket.on('mesh-peers-discovered', (data: {
      peers: Array<{
        id: string;
        userId: string;
        connectionState: string;
        connectionQuality: number;
        capabilities: any;
      }>;
      topology: MeshTopologyInfo;
    }) => {
      this.handlePeerDiscovery(data);
    });

    // Handle new peer availability
    this.socket.on('mesh-new-peer-available', (data: {
      peer: any;
      shouldConnect: boolean;
    }) => {
      if (data.shouldConnect) {
        this.connectToPeer(data.peer.id, data.peer.userId, true);
      }
    });

    // Handle peer joined mesh
    this.socket.on('mesh-peer-joined', (data: {
      peer: any;
    }) => {
      console.log('New peer joined mesh:', data.peer);
      this.onPeerJoinedHandler?.(data.peer.id, data.peer.userId);
    });

    // Handle peer left mesh
    this.socket.on('mesh-peer-left', (data: {
      peerId: string;
      userId: string;
    }) => {
      this.handlePeerLeft(data.peerId, data.userId);
    });

    // Handle topology optimization suggestions
    this.socket.on('mesh-optimization-connect', (data: {
      targetPeer: any;
      reason: string;
    }) => {
      console.log('Mesh optimization: connect to peer', data);
      this.connectToPeer(data.targetPeer.id, data.targetPeer.userId, true);
    });

    this.socket.on('mesh-optimization-disconnect', (data: {
      targetPeerId: string;
      reason: string;
    }) => {
      console.log('Mesh optimization: disconnect from peer', data);
      this.disconnectFromPeer(data.targetPeerId);
    });

    // Handle rebalancing suggestions
    this.socket.on('mesh-rebalance-suggestions', (data: {
      suggestedPeers: any[];
      reason: string;
    }) => {
      console.log('Mesh rebalancing suggestions received', data);
      this.handleRebalancingSuggestions(data.suggestedPeers);
    });

    // Handle topology updates
    this.socket.on('mesh-topology-optimized', (data: {
      oldMetrics: any;
      newMetrics: any;
      optimizations: number;
    }) => {
      console.log('Mesh topology optimized', data);
    });

    // Handle mesh shutdown
    this.socket.on('mesh-shutdown', (data: {
      reason: string;
    }) => {
      console.log('Mesh network shutting down:', data.reason);
      this.cleanup();
    });
  }

  /**
   * Join mesh network for the project
   */
  private async joinMeshNetwork(): Promise<void> {
    if (!this.socket || !this.userId || !this.projectId) {
      throw new Error('Socket, userId, or projectId not available');
    }

    const capabilities = {
      supportsWebRTC: true,
      supportsDataChannels: this.config.enableDataChannels,
      supportsAudio: this.config.enableAudio,
      supportsVideo: this.config.enableVideo
    };

    this.socket.emit('mesh-join-project', {
      projectId: this.projectId,
      userId: this.userId,
      capabilities
    });

    // Request peer discovery
    setTimeout(() => {
      this.socket?.emit('mesh-discover-peers', {
        projectId: this.projectId
      });
    }, 1000);
  }

  /**
   * Handle peer discovery response
   */
  private handlePeerDiscovery(data: {
    peers: Array<{
      id: string;
      userId: string;
      connectionState: string;
      connectionQuality: number;
      capabilities: any;
    }>;
    topology: MeshTopologyInfo;
  }): void {
    this.meshTopology = data.topology;
    this.onTopologyUpdatedHandler?.(data.topology);

    // Connect to discovered peers
    for (const peer of data.peers) {
      if (!this.meshConnections.has(peer.id) && peer.connectionState === 'connected') {
        this.connectToPeer(peer.id, peer.userId, true);
      }
    }
  }

  /**
   * Connect to a specific peer
   */
  private async connectToPeer(peerId: string, userId: string, initiator: boolean): Promise<void> {
    if (this.meshConnections.has(peerId)) {
      console.log('Already connected to peer:', peerId);
      return;
    }

    if (this.meshConnections.size >= this.config.maxPeers) {
      console.log('Maximum peer connections reached');
      return;
    }

    try {
      console.log(`Connecting to peer: ${peerId} (initiator: ${initiator})`);

      const peer = new SimplePeer({
        initiator,
        config: {
          iceServers: this.config.iceServers
        },
        channelConfig: {
          ordered: true
        }
      });

      const connection: MeshPeerConnection = {
        id: peerId,
        userId,
        peer,
        connectionState: 'connecting',
        connectionQuality: 1.0,
        latency: 0,
        bandwidth: 0,
        lastActivity: Date.now()
      };

      this.meshConnections.set(peerId, connection);
      this.setupPeerEventHandlers(connection);

    } catch (error) {
      console.error('Failed to connect to peer:', peerId, error);
    }
  }

  /**
   * Setup event handlers for a peer connection
   */
  private setupPeerEventHandlers(connection: MeshPeerConnection): void {
    const { peer, id: peerId } = connection;

    peer.on('signal', (signal) => {
      // Send signaling data through Socket.IO
      this.socket?.emit('webrtc-signal', {
        from: this.userId,
        to: connection.userId,
        signal,
        type: signal.type || 'signal'
      });
    });

    peer.on('connect', () => {
      console.log('Peer connected:', peerId);
      connection.connectionState = 'connected';
      connection.lastActivity = Date.now();

      this.onPeerJoinedHandler?.(peerId, connection.userId);
      this.startLatencyMonitoring(connection);
    });

    peer.on('data', (data) => {
      try {
        const message: MeshDataMessage = JSON.parse(data.toString());
        message.timestamp = Date.now(); // Update with receive timestamp

        connection.lastActivity = Date.now();
        this.onDataReceivedHandler?.(message, peerId);

        // Relay to other peers if needed (mesh forwarding)
        this.relayDataToMesh(message, peerId);

      } catch (error) {
        console.error('Failed to parse peer data:', error);
      }
    });

    peer.on('error', (error) => {
      console.error('Peer connection error:', peerId, error);
      connection.connectionState = 'failed';
      this.handleConnectionError(connection, error);
    });

    peer.on('close', () => {
      console.log('Peer connection closed:', peerId);
      connection.connectionState = 'disconnected';
      this.meshConnections.delete(peerId);
      this.onPeerLeftHandler?.(peerId, connection.userId);
    });
  }

  /**
   * Handle peer leaving the mesh
   */
  private handlePeerLeft(peerId: string, userId: string): void {
    const connection = this.meshConnections.get(peerId);
    if (connection) {
      connection.peer.destroy();
      this.meshConnections.delete(peerId);
    }

    this.onPeerLeftHandler?.(peerId, userId);
    console.log('Peer left mesh:', peerId);
  }

  /**
   * Disconnect from a specific peer
   */
  private disconnectFromPeer(peerId: string): void {
    const connection = this.meshConnections.get(peerId);
    if (connection) {
      connection.peer.destroy();
      this.meshConnections.delete(peerId);
      console.log('Disconnected from peer:', peerId);
    }
  }

  /**
   * Handle rebalancing suggestions
   */
  private handleRebalancingSuggestions(suggestedPeers: any[]): void {
    for (const peer of suggestedPeers) {
      if (!this.meshConnections.has(peer.id) &&
          this.meshConnections.size < this.config.maxPeers) {
        this.connectToPeer(peer.id, peer.userId, true);
      }
    }
  }

  /**
   * Start latency monitoring for a connection
   */
  private startLatencyMonitoring(connection: MeshPeerConnection): void {
    const pingInterval = setInterval(() => {
      if (connection.connectionState !== 'connected') {
        clearInterval(pingInterval);
        return;
      }

      const pingStart = Date.now();
      const pingMessage: MeshDataMessage = {
        type: 'cursor', // Use cursor type for ping
        data: { ping: true, timestamp: pingStart },
        timestamp: pingStart,
        fromUserId: this.userId || ''
      };

      try {
        connection.peer.send(JSON.stringify(pingMessage));

        // Calculate latency when pong is received
        const originalOnData = connection.peer.ondata;
        connection.peer.ondata = (data) => {
          try {
            const message = JSON.parse(data.toString());
            if (message.data?.ping) {
              connection.latency = Date.now() - pingStart;
              this.updateConnectionQuality(connection);
            }
          } catch (e) {
            // Ignore parsing errors for ping messages
          }

          // Call original handler
          if (originalOnData) {
            originalOnData.call(connection.peer, data);
          }
        };

      } catch (error) {
        console.warn('Failed to send ping to peer:', connection.id, error);
      }
    }, 5000);
  }

  /**
   * Update connection quality metrics
   */
  private updateConnectionQuality(connection: MeshPeerConnection): void {
    // Calculate quality based on latency and activity
    const latencyScore = Math.max(0, 1 - (connection.latency / 500)); // 500ms max
    const activityScore = Math.max(0, 1 - ((Date.now() - connection.lastActivity) / 30000)); // 30s max

    connection.connectionQuality = (latencyScore * 0.7 + activityScore * 0.3);

    // Report to server
    if (this.socket && this.projectId) {
      this.socket.emit('mesh-connection-quality', {
        projectId: this.projectId,
        peerId: this.userId,
        targetPeerId: connection.id,
        quality: connection.connectionQuality,
        latency: connection.latency,
        bandwidth: connection.bandwidth
      });
    }

    this.onConnectionQualityChangedHandler?.(connection.id, connection.connectionQuality);
  }

  /**
   * Handle connection errors
   */
  private handleConnectionError(connection: MeshPeerConnection, error: any): void {
    console.error('Connection error with peer:', connection.id, error);

    // Attempt reconnection for important peers
    if (connection.connectionQuality > 0.7) {
      setTimeout(() => {
        if (!this.meshConnections.has(connection.id)) {
          this.connectToPeer(connection.id, connection.userId, true);
        }
      }, 5000);
    }
  }

  /**
   * Relay data to other mesh peers (selective flooding)
   */
  private relayDataToMesh(message: MeshDataMessage, excludePeerId: string): void {
    // Only relay certain message types to prevent loops
    const relayableTypes = ['scene-update', 'quote-update'];
    if (!relayableTypes.includes(message.type)) {
      return;
    }

    // Add relay information to prevent infinite loops
    const relayMessage = {
      ...message,
      relayed: true,
      relayedBy: this.userId,
      originalSender: message.fromUserId
    };

    for (const [peerId, connection] of this.meshConnections) {
      if (peerId !== excludePeerId &&
          connection.connectionState === 'connected' &&
          connection.connectionQuality > 0.5) {
        try {
          connection.peer.send(JSON.stringify(relayMessage));
        } catch (error) {
          console.warn('Failed to relay message to peer:', peerId, error);
        }
      }
    }
  }

  /**
   * Start quality monitoring
   */
  private startQualityMonitoring(): void {
    this.qualityMonitorTimer = setInterval(() => {
      for (const connection of this.meshConnections.values()) {
        if (connection.connectionState === 'connected') {
          this.updateConnectionQuality(connection);
        }
      }
    }, this.config.qualityCheckInterval);
  }

  /**
   * Public API Methods
   */

  /**
   * Send cursor position to all mesh peers
   */
  sendCursorPosition(x: number, y: number, elementId?: string): void {
    if (!this.isInitialized || !this.userId) return;

    const cursorData: CursorData = {
      userId: this.userId,
      x,
      y,
      timestamp: Date.now(),
      elementId
    };

    const message: MeshDataMessage = {
      type: 'cursor',
      data: cursorData,
      timestamp: Date.now(),
      fromUserId: this.userId
    };

    this.broadcastToMesh(message);
  }

  /**
   * Send voice comment to mesh peers
   */
  sendVoiceComment(commentId: string, audioBlob: Blob, duration: number): void {
    if (!this.isInitialized || !this.userId) return;

    // Convert blob to base64 for transmission
    const reader = new FileReader();
    reader.onload = () => {
      const audioData = reader.result as string;

      const message: MeshDataMessage = {
        type: 'voice-comment',
        data: {
          userId: this.userId,
          commentId,
          audioData,
          duration,
          timestamp: Date.now()
        },
        timestamp: Date.now(),
        fromUserId: this.userId!
      };

      this.broadcastToMesh(message);
    };
    reader.readAsDataURL(audioBlob);
  }

  /**
   * Send 3D scene update to mesh peers
   */
  sendSceneUpdate(sceneData: any): void {
    if (!this.isInitialized || !this.userId) return;

    const message: MeshDataMessage = {
      type: 'scene-update',
      data: sceneData,
      timestamp: Date.now(),
      fromUserId: this.userId
    };

    this.broadcastToMesh(message);
  }

  /**
   * Send quotation update to mesh peers
   */
  sendQuoteUpdate(quoteData: any): void {
    if (!this.isInitialized || !this.userId) return;

    const message: MeshDataMessage = {
      type: 'quote-update',
      data: quoteData,
      timestamp: Date.now(),
      fromUserId: this.userId
    };

    this.broadcastToMesh(message);
  }

  /**
   * Broadcast message to all connected mesh peers
   */
  private broadcastToMesh(message: MeshDataMessage): void {
    const messageStr = JSON.stringify(message);
    let sentCount = 0;

    for (const connection of this.meshConnections.values()) {
      if (connection.connectionState === 'connected') {
        try {
          connection.peer.send(messageStr);
          sentCount++;
        } catch (error) {
          console.warn('Failed to send message to peer:', connection.id, error);
        }
      }
    }

    console.debug(`Broadcasted message to ${sentCount} peers`, { type: message.type });
  }

  /**
   * Get mesh network status
   */
  getMeshStatus(): {
    isConnected: boolean;
    peerCount: number;
    connectedPeers: number;
    averageLatency: number;
    averageQuality: number;
    topology: MeshTopologyInfo | null;
  } {
    const connectedPeers = Array.from(this.meshConnections.values())
      .filter(c => c.connectionState === 'connected');

    const averageLatency = connectedPeers.length > 0
      ? connectedPeers.reduce((sum, c) => sum + c.latency, 0) / connectedPeers.length
      : 0;

    const averageQuality = connectedPeers.length > 0
      ? connectedPeers.reduce((sum, c) => sum + c.connectionQuality, 0) / connectedPeers.length
      : 0;

    return {
      isConnected: this.isInitialized,
      peerCount: this.meshConnections.size,
      connectedPeers: connectedPeers.length,
      averageLatency,
      averageQuality,
      topology: this.meshTopology
    };
  }

  /**
   * Get connected peer information
   */
  getConnectedPeers(): Array<{
    id: string;
    userId: string;
    connectionState: string;
    connectionQuality: number;
    latency: number;
    lastActivity: number;
  }> {
    return Array.from(this.meshConnections.values()).map(connection => ({
      id: connection.id,
      userId: connection.userId,
      connectionState: connection.connectionState,
      connectionQuality: connection.connectionQuality,
      latency: connection.latency,
      lastActivity: connection.lastActivity
    }));
  }

  /**
   * Event handler setters
   */
  onPeerJoined(handler: (peerId: string, userId: string) => void): void {
    this.onPeerJoinedHandler = handler;
  }

  onPeerLeft(handler: (peerId: string, userId: string) => void): void {
    this.onPeerLeftHandler = handler;
  }

  onDataReceived(handler: (data: MeshDataMessage, fromPeerId: string) => void): void {
    this.onDataReceivedHandler = handler;
  }

  onTopologyUpdated(handler: (topology: MeshTopologyInfo) => void): void {
    this.onTopologyUpdatedHandler = handler;
  }

  onConnectionQualityChanged(handler: (peerId: string, quality: number) => void): void {
    this.onConnectionQualityChangedHandler = handler;
  }

  /**
   * Leave mesh network and cleanup
   */
  async leaveMeshNetwork(): Promise<void> {
    if (!this.isInitialized || !this.socket || !this.projectId || !this.userId) {
      return;
    }

    // Notify server
    this.socket.emit('mesh-leave-project', {
      projectId: this.projectId,
      userId: this.userId
    });

    this.cleanup();
  }

  /**
   * Cleanup resources
   */
  private cleanup(): void {
    // Stop quality monitoring
    if (this.qualityMonitorTimer) {
      clearInterval(this.qualityMonitorTimer);
      this.qualityMonitorTimer = null;
    }

    // Close all peer connections
    for (const connection of this.meshConnections.values()) {
      try {
        connection.peer.destroy();
      } catch (error) {
        console.warn('Error destroying peer connection:', error);
      }
    }

    this.meshConnections.clear();
    this.meshTopology = null;
    this.isInitialized = false;

    console.log('MeshWebRTCService cleanup completed');
  }

  /**
   * Destroy service
   */
  destroy(): void {
    this.leaveMeshNetwork();
  }
}
