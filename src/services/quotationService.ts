import { API_BASE_URL } from '../lib/config';

export interface QuoteTier {
  tier: 'basic' | 'premium' | 'luxury';
  name: string;
  description: string;
  materials: {
    cost: string;
    items: Array<{
      description: string;
      quantity: number;
      unitPrice: string;
      totalPrice: string;
    }>;
  };
  hardware: {
    cost: string;
    items: Array<{
      description: string;
      quantity: number;
      unitPrice: string;
      totalPrice: string;
    }>;
  };
  labor: {
    cost: string;
    items: Array<{
      description: string;
      hours: number;
      hourlyRate: string;
      totalPrice: string;
    }>;
  };
  subtotal: string;
  taxes: string;
  total: string;
  confidence: number;
}

export interface QuoteAlternative {
  description: string;
  impact: string;
  costDifference: string;
}

export interface Quote {
  id: string;
  analysisId: string;
  projectId?: string;
  tiers: QuoteTier[];
  summary: {
    cabinetCount: number;
    linearFeet: number;
    complexity: string;
    estimatedTimeframe: string;
  };
  alternatives: QuoteAlternative[];
  confidence: number;
  createdAt: string;
  validUntil: string;
}

export interface QuoteGenerationRequest {
  analysisId: string;
  projectId?: string;
  regionCode?: string;
  analysisData: any;
}

export interface QuoteUpdateRequest {
  tier?: 'basic' | 'premium' | 'luxury';
  customizations?: any;
  notes?: string;
}

class QuotationService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/quotation`;
  }

  /**
   * Generate a new quote from analysis data
   */
  async generateQuote(request: QuoteGenerationRequest): Promise<Quote> {
    const response = await fetch(`${this.baseUrl}/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.getAuthToken()}`
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to generate quote');
    }

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Quote generation failed');
    }

    return result.data;
  }

  /**
   * Retrieve a quote by ID
   */
  async getQuote(quoteId: string): Promise<Quote> {
    const response = await fetch(`${this.baseUrl}/${quoteId}`, {
      headers: {
        'Authorization': `Bearer ${this.getAuthToken()}`
      }
    });

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error('Quote not found');
      }
      const error = await response.json();
      throw new Error(error.message || 'Failed to retrieve quote');
    }

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Failed to retrieve quote');
    }

    return result.data;
  }

  /**
   * Get all quotes for a project
   */
  async getProjectQuotes(projectId: string): Promise<Array<{
    id: string;
    analysisId: string;
    summary: Quote['summary'];
    confidence: number;
    createdAt: string;
    validUntil: string;
    tierSummary: Array<{
      tier: string;
      name: string;
      total: string;
      confidence: number;
    }>;
  }>> {
    const response = await fetch(`${this.baseUrl}/project/${projectId}`, {
      headers: {
        'Authorization': `Bearer ${this.getAuthToken()}`
      }
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to retrieve project quotes');
    }

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Failed to retrieve project quotes');
    }

    return result.data;
  }

  /**
   * Update a quote with customizations
   */
  async updateQuote(quoteId: string, updates: QuoteUpdateRequest): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${quoteId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.getAuthToken()}`
      },
      body: JSON.stringify(updates)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update quote');
    }

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Failed to update quote');
    }
  }

  /**
   * Download quote as PDF
   */
  async downloadQuotePDF(
    quoteId: string, 
    options: {
      template?: 'basic' | 'detailed' | 'professional';
      includeBranding?: boolean;
      includeAlternatives?: boolean;
    } = {}
  ): Promise<Blob> {
    const params = new URLSearchParams();
    if (options.template) params.append('template', options.template);
    if (options.includeBranding !== undefined) params.append('includeBranding', options.includeBranding.toString());
    if (options.includeAlternatives !== undefined) params.append('includeAlternatives', options.includeAlternatives.toString());

    const response = await fetch(`${this.baseUrl}/${quoteId}/pdf?${params.toString()}`, {
      headers: {
        'Authorization': `Bearer ${this.getAuthToken()}`
      }
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to generate PDF');
    }

    return response.blob();
  }

  /**
   * Get available materials for pricing
   */
  async getMaterials(options: {
    category?: string;
    search?: string;
    limit?: number;
  } = {}): Promise<Array<{
    id: number;
    category: string;
    subcategory?: string;
    material_type: string;
    grade: string;
    finish?: string;
    brand?: string;
    unit_of_measure: string;
    base_price: string;
    min_price?: string;
    max_price?: string;
  }>> {
    const params = new URLSearchParams();
    if (options.category) params.append('category', options.category);
    if (options.search) params.append('search', options.search);
    if (options.limit) params.append('limit', options.limit.toString());

    const response = await fetch(`${this.baseUrl}/pricing/materials?${params.toString()}`, {
      headers: {
        'Authorization': `Bearer ${this.getAuthToken()}`
      }
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to retrieve materials');
    }

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Failed to retrieve materials');
    }

    return result.data;
  }

  /**
   * Get available hardware for pricing
   */
  async getHardware(options: {
    brand?: string;
    search?: string;
    limit?: number;
  } = {}): Promise<Array<{
    id: number;
    category: string;
    subcategory?: string;
    brand: string;
    model?: string;
    finish?: string;
    specifications: any;
    unit_price: string;
    bulk_pricing?: any;
    compatibility?: any;
    installation_complexity: string;
  }>> {
    const params = new URLSearchParams();
    if (options.brand) params.append('brand', options.brand);
    if (options.search) params.append('search', options.search);
    if (options.limit) params.append('limit', options.limit.toString());

    const response = await fetch(`${this.baseUrl}/pricing/hardware?${params.toString()}`, {
      headers: {
        'Authorization': `Bearer ${this.getAuthToken()}`
      }
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to retrieve hardware');
    }

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Failed to retrieve hardware');
    }

    return result.data;
  }

  /**
   * Check if quotation service is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/pricing/materials?limit=1`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`
        }
      });
      return response.status !== 503;
    } catch (error) {
      return false;
    }
  }

  /**
   * Format currency for display
   */
  formatNZD(amount: number): string {
    return new Intl.NumberFormat('en-NZ', {
      style: 'currency',
      currency: 'NZD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }

  /**
   * Parse NZD currency string to number
   */
  parseNZD(formattedAmount: string): number {
    return parseFloat(formattedAmount.replace(/[^\d.-]/g, ''));
  }

  /**
   * Get authentication token
   */
  private getAuthToken(): string {
    // This would typically come from your auth context/store
    return localStorage.getItem('authToken') || 'test-token';
  }

  /**
   * Calculate potential savings between tiers
   */
  calculateSavings(higherTier: QuoteTier, lowerTier: QuoteTier): {
    amount: number;
    percentage: number;
    formatted: string;
  } {
    const higherTotal = this.parseNZD(higherTier.total);
    const lowerTotal = this.parseNZD(lowerTier.total);
    const savings = higherTotal - lowerTotal;
    const percentage = (savings / higherTotal) * 100;

    return {
      amount: savings,
      percentage,
      formatted: this.formatNZD(savings)
    };
  }

  /**
   * Get tier recommendation based on analysis data
   */
  getTierRecommendation(analysisData: any): 'basic' | 'premium' | 'luxury' {
    const complexity = analysisData.complexity || 'moderate';
    const quality = analysisData.materials?.quality || 'standard';
    const cabinetCount = Object.values(analysisData.cabinetCount || {}).reduce((sum: number, count: any) => sum + (count || 0), 0);

    // Simple recommendation logic
    if (quality === 'luxury' || complexity === 'very_complex' || cabinetCount > 25) {
      return 'luxury';
    } else if (quality === 'premium' || complexity === 'complex' || cabinetCount > 15) {
      return 'premium';
    } else {
      return 'basic';
    }
  }
}

export const quotationService = new QuotationService();
