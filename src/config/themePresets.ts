// =============================================================================
// BLACKVEIL DESIGN MIND - THEME PRESET CONFIGURATIONS
// Predefined theme combinations following A.ONE design system patterns
// =============================================================================

import { ThemePresetConfig, AnimationPresetConfig } from '@/types/theme';

// =============================================================================
// THEME PRESET CONFIGURATIONS
// =============================================================================

export const THEME_PRESETS: Record<string, ThemePresetConfig> = {
  professional: {
    id: 'professional',
    name: 'Professional',
    description: 'Sage green with neutral grays for business environments',
    icon: 'briefcase',
    colors: {
      light: {
        sage: '107 122 79',        // Primary sage green (#6B7A4F)
        sageLight: '139 154 116',  // Lighter sage (#8B9A74)
        sageDark: '75 90 52',      // Darker sage (#4B5A34)
        accent: '100 116 139',     // Professional blue-gray (#64748B)
        accentLight: '148 163 184', // Light blue-gray (#94A3B8)
        accentDark: '71 85 105',   // Dark blue-gray (#475569)
        cream: '248 245 240',      // Light cream (#F8F5F0)
        warmWhite: '252 251 249',  // Warm white (#FCFBF9)
        charcoal: '46 46 46',      // Dark charcoal (#2E2E2E)
        softGray: '156 163 175',   // Soft gray (#9CA3AF)
      },
      dark: {
        sage: '139 154 116',       // Enhanced sage for dark mode
        sageLight: '155 170 132',  // Lighter sage for dark mode
        sageDark: '107 122 79',    // Darker sage for dark mode
        accent: '148 163 184',     // Professional blue-gray for dark
        accentLight: '203 213 225', // Light blue-gray for dark
        accentDark: '100 116 139', // Dark blue-gray for dark
        cream: '30 30 30',         // Dark cream background
        warmWhite: '24 24 27',     // Dark warm background
        charcoal: '212 212 216',   // Light charcoal for text
        softGray: '113 113 122',   // Muted gray for dark mode
      },
    },
  },

  creative: {
    id: 'creative',
    name: 'Creative',
    description: 'Sage green with warm accents for creative workflows',
    icon: 'palette',
    colors: {
      light: {
        sage: '107 122 79',        // Primary sage green
        sageLight: '139 154 116',  // Lighter sage
        sageDark: '75 90 52',      // Darker sage
        accent: '251 146 60',      // Warm orange (#FB923C)
        accentLight: '253 186 116', // Light orange (#FDBA74)
        accentDark: '234 88 12',   // Dark orange (#EA580C)
        cream: '254 252 232',      // Warm cream (#FEFCE8)
        warmWhite: '255 255 255',  // Pure white
        charcoal: '41 37 36',      // Warm charcoal (#292524)
        softGray: '168 162 158',   // Warm gray (#A8A29E)
      },
      dark: {
        sage: '139 154 116',       // Enhanced sage for dark mode
        sageLight: '155 170 132',  // Lighter sage for dark mode
        sageDark: '107 122 79',    // Darker sage for dark mode
        accent: '253 186 116',     // Warm orange for dark
        accentLight: '254 215 170', // Light orange for dark
        accentDark: '251 146 60',  // Dark orange for dark
        cream: '41 37 36',         // Dark warm background
        warmWhite: '28 25 23',     // Dark warm surface
        charcoal: '231 229 228',   // Light warm text
        softGray: '120 113 108',   // Muted warm gray
      },
    },
  },

  minimal: {
    id: 'minimal',
    name: 'Minimal',
    description: 'Sage green with high contrast for clean interfaces',
    icon: 'minimize',
    colors: {
      light: {
        sage: '107 122 79',        // Primary sage green
        sageLight: '139 154 116',  // Lighter sage
        sageDark: '75 90 52',      // Darker sage
        accent: '0 0 0',           // Pure black for high contrast
        accentLight: '64 64 64',   // Dark gray
        accentDark: '0 0 0',       // Pure black
        cream: '255 255 255',      // Pure white
        warmWhite: '255 255 255',  // Pure white
        charcoal: '0 0 0',         // Pure black
        softGray: '115 115 115',   // Medium gray
      },
      dark: {
        sage: '139 154 116',       // Enhanced sage for dark mode
        sageLight: '155 170 132',  // Lighter sage for dark mode
        sageDark: '107 122 79',    // Darker sage for dark mode
        accent: '255 255 255',     // Pure white for high contrast
        accentLight: '229 229 229', // Light gray
        accentDark: '255 255 255', // Pure white
        cream: '0 0 0',            // Pure black
        warmWhite: '0 0 0',        // Pure black
        charcoal: '255 255 255',   // Pure white
        softGray: '163 163 163',   // Medium gray
      },
    },
  },

  enterprise: {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'Sage green with corporate blues for enterprise applications',
    icon: 'building',
    colors: {
      light: {
        sage: '107 122 79',        // Primary sage green
        sageLight: '139 154 116',  // Lighter sage
        sageDark: '75 90 52',      // Darker sage
        accent: '37 99 235',       // Corporate blue (#2563EB)
        accentLight: '96 165 250', // Light blue (#60A5FA)
        accentDark: '29 78 216',   // Dark blue (#1D4ED8)
        cream: '248 250 252',      // Cool cream (#F8FAFC)
        warmWhite: '255 255 255',  // Pure white
        charcoal: '15 23 42',      // Deep blue-charcoal (#0F172A)
        softGray: '148 163 184',   // Cool gray (#94A3B8)
      },
      dark: {
        sage: '139 154 116',       // Enhanced sage for dark mode
        sageLight: '155 170 132',  // Lighter sage for dark mode
        sageDark: '107 122 79',    // Darker sage for dark mode
        accent: '96 165 250',      // Corporate blue for dark
        accentLight: '147 197 253', // Light blue for dark
        accentDark: '37 99 235',   // Dark blue for dark
        cream: '15 23 42',         // Deep blue background
        warmWhite: '30 41 59',     // Dark blue surface
        charcoal: '241 245 249',   // Light blue-white text
        softGray: '100 116 139',   // Cool gray for dark
      },
    },
  },
};

// =============================================================================
// ANIMATION PRESET CONFIGURATIONS
// =============================================================================

export const ANIMATION_PRESETS: Record<string, AnimationPresetConfig> = {
  'smooth-fade': {
    id: 'smooth-fade',
    name: 'Smooth Fade',
    description: 'Gentle opacity transitions for subtle theme changes',
    duration: 300,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    properties: ['opacity', 'background-color', 'border-color', 'color'],
  },

  'slide-transform': {
    id: 'slide-transform',
    name: 'Slide Transform',
    description: 'Smooth sliding motion with transform effects',
    duration: 250,
    easing: 'cubic-bezier(0, 0, 0.2, 1)',
    properties: ['transform', 'opacity', 'background-color', 'border-color'],
  },

  'zoom-effect': {
    id: 'zoom-effect',
    name: 'Zoom Effect',
    description: 'Dynamic scaling animation for engaging transitions',
    duration: 200,
    easing: 'cubic-bezier(0.34, 1.56, 0.64, 1)',
    properties: ['transform', 'opacity', 'background-color', 'box-shadow'],
  },
};

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

export const getPresetConfig = (presetId: string): ThemePresetConfig | null => {
  return THEME_PRESETS[presetId] || null;
};

export const getAnimationConfig = (presetId: string): AnimationPresetConfig | null => {
  return ANIMATION_PRESETS[presetId] || null;
};

export const getAllPresets = (): ThemePresetConfig[] => {
  return Object.values(THEME_PRESETS);
};

export const getAllAnimationPresets = (): AnimationPresetConfig[] => {
  return Object.values(ANIMATION_PRESETS);
};

// =============================================================================
// WCAG ACCESSIBILITY COMPLIANCE
// =============================================================================

export const ACCESSIBILITY_RATIOS = {
  // WCAG AA compliance (4.5:1 for normal text, 3:1 for large text)
  AA_NORMAL: 4.5,
  AA_LARGE: 3.0,
  
  // WCAG AAA compliance (7:1 for normal text, 4.5:1 for large text)
  AAA_NORMAL: 7.0,
  AAA_LARGE: 4.5,
} as const;

// High contrast color adjustments for accessibility
export const HIGH_CONTRAST_ADJUSTMENTS = {
  light: {
    backgroundBoost: 1.1,    // Increase background lightness
    textBoost: 0.8,          // Decrease text lightness (darker)
    borderBoost: 0.7,        // Stronger borders
  },
  dark: {
    backgroundBoost: 0.8,    // Decrease background lightness (darker)
    textBoost: 1.2,          // Increase text lightness (brighter)
    borderBoost: 1.3,        // Stronger borders
  },
} as const;
