// API Configuration
export const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

// WebSocket Configuration
export const WS_BASE_URL = process.env.REACT_APP_WS_URL || 'http://localhost:3001';

// Application Configuration
export const APP_CONFIG = {
  // File upload limits
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
  ALLOWED_FILE_TYPES: ['application/pdf', 'image/jpeg', 'image/png'],
  
  // Analysis configuration
  DEFAULT_ANALYSIS_TIMEOUT: 30000, // 30 seconds
  
  // Quotation configuration
  DEFAULT_REGION: 'NZ_NORTH',
  CURRENCY: 'NZD',
  
  // UI configuration
  THEME: {
    PRIMARY_COLOR: '#3b82f6',
    SECONDARY_COLOR: '#64748b',
    SUCCESS_COLOR: '#10b981',
    WARNING_COLOR: '#f59e0b',
    ERROR_COLOR: '#ef4444'
  }
};

// Feature flags
export const FEATURES = {
  QUOTATION_SYSTEM: true,
  REAL_TIME_UPDATES: true,
  PDF_GENERATION: true,
  COLLABORATION: true,
  PERFORMANCE_METRICS: true,
  GPT_O1_INTEGRATION: true
};

// Environment checks
export const IS_DEVELOPMENT = process.env.NODE_ENV === 'development';
export const IS_PRODUCTION = process.env.NODE_ENV === 'production';
