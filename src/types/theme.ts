// =============================================================================
// BLACKVEIL DESIGN MIND - ADVANCED THEME SYSTEM TYPES
// Comprehensive type definitions for theme presets, customization, and accessibility
// =============================================================================

export type Theme = 'dark' | 'light' | 'system';
export type ResolvedTheme = 'dark' | 'light';

// Theme Preset System
export type ThemePreset = 'professional' | 'creative' | 'minimal' | 'enterprise';

export interface ThemePresetConfig {
  id: ThemePreset;
  name: string;
  description: string;
  icon: string;
  colors: {
    light: ThemePresetColors;
    dark: ThemePresetColors;
  };
}

export interface ThemePresetColors {
  // Primary A.ONE sage variations
  sage: string;           // RGB values: "107 122 79"
  sageLight: string;      // RGB values: "139 154 116"
  sageDark: string;       // RGB values: "75 90 52"
  
  // Preset-specific accent colors
  accent: string;         // RGB values for preset accent
  accentLight: string;    // RGB values for lighter accent
  accentDark: string;     // RGB values for darker accent
  
  // Neutral colors
  cream: string;          // RGB values for background
  warmWhite: string;      // RGB values for surface
  charcoal: string;       // RGB values for text
  softGray: string;       // RGB values for muted text
}

// Color Customization System
export interface CustomColor {
  hue: number;           // 70-120 range
  saturation: number;    // 20-40% range
  lightness: number;     // 35-55% range
}

export interface ColorCustomization {
  isCustom: boolean;
  customSage?: CustomColor;
  customAccent?: CustomColor;
}

// Animation Preset System
export type AnimationPreset = 'smooth-fade' | 'slide-transform' | 'zoom-effect';

export interface AnimationPresetConfig {
  id: AnimationPreset;
  name: string;
  description: string;
  duration: number;      // milliseconds
  easing: string;        // CSS easing function
  properties: string[];  // CSS properties to animate
}

// Accessibility Variants
export interface AccessibilitySettings {
  highContrast: boolean;
  reducedMotion: boolean;
  focusEnhancement: boolean;
  autoDetectPreferences: boolean;
}

// Comprehensive Theme State
export interface AdvancedThemeState {
  // Basic theme
  theme: Theme;
  resolvedTheme: ResolvedTheme;
  
  // Preset system
  preset: ThemePreset;
  customColors: ColorCustomization;
  
  // Animation system
  animationPreset: AnimationPreset;
  
  // Accessibility
  accessibility: AccessibilitySettings;
  
  // State management
  isTransitioning: boolean;
  isCustomizing: boolean;
}

// Theme Provider Props
export interface AdvancedThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: Theme;
  defaultPreset?: ThemePreset;
  defaultAnimationPreset?: AnimationPreset;
  storageKey?: string;
  presetStorageKey?: string;
  animationStorageKey?: string;
  accessibilityStorageKey?: string;
  enableTransitions?: boolean;
  enableCustomization?: boolean;
  enableAccessibilityFeatures?: boolean;
}

// Theme Context
export interface AdvancedThemeContextType extends AdvancedThemeState {
  // Basic theme functions
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  
  // Preset functions
  setPreset: (preset: ThemePreset) => void;
  getPresetConfig: (preset: ThemePreset) => ThemePresetConfig;
  
  // Color customization functions
  setCustomColor: (type: 'sage' | 'accent', color: CustomColor) => void;
  resetCustomColors: () => void;
  applyCustomColors: () => void;
  
  // Animation functions
  setAnimationPreset: (preset: AnimationPreset) => void;
  getAnimationConfig: (preset: AnimationPreset) => AnimationPresetConfig;
  
  // Accessibility functions
  setAccessibilitySettings: (settings: Partial<AccessibilitySettings>) => void;
  toggleHighContrast: () => void;
  toggleFocusEnhancement: () => void;
  
  // Utility functions
  exportThemeConfig: () => string;
  importThemeConfig: (config: string) => boolean;
  resetToDefaults: () => void;
}

// Storage Keys
export const STORAGE_KEYS = {
  THEME: 'blackveil-design-mind-theme',
  PRESET: 'blackveil-design-mind-theme-preset',
  ANIMATION: 'blackveil-design-mind-animation-preset',
  ACCESSIBILITY: 'blackveil-design-mind-accessibility',
  CUSTOM_COLORS: 'blackveil-design-mind-custom-colors',
} as const;

// Default Values
export const DEFAULT_VALUES = {
  THEME: 'system' as Theme,
  PRESET: 'professional' as ThemePreset,
  ANIMATION_PRESET: 'smooth-fade' as AnimationPreset,
  ACCESSIBILITY: {
    highContrast: false,
    reducedMotion: false,
    focusEnhancement: false,
    autoDetectPreferences: true,
  } as AccessibilitySettings,
  CUSTOM_COLORS: {
    isCustom: false,
  } as ColorCustomization,
} as const;

// Validation Functions
export const isValidTheme = (theme: string): theme is Theme => {
  return ['dark', 'light', 'system'].includes(theme);
};

export const isValidPreset = (preset: string): preset is ThemePreset => {
  return ['professional', 'creative', 'minimal', 'enterprise'].includes(preset);
};

export const isValidAnimationPreset = (preset: string): preset is AnimationPreset => {
  return ['smooth-fade', 'slide-transform', 'zoom-effect'].includes(preset);
};

export const isValidCustomColor = (color: CustomColor): boolean => {
  return (
    color.hue >= 70 && color.hue <= 120 &&
    color.saturation >= 20 && color.saturation <= 40 &&
    color.lightness >= 35 && color.lightness <= 55
  );
};
