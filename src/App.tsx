import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./hooks/useAuth";
import { CollaborationProvider } from "./hooks/useCollaboration";
import { ThemeProvider } from "./hooks/useTheme";
import Index from "./pages/Index";
import Analysis from "./pages/Analysis";
import Performance from "./pages/Performance";
import PerformanceMonitoring from "./pages/PerformanceMonitoring";
import Features from "./pages/Features";
import NotFound from "./pages/NotFound";
import DebugApiConnection from "./components/DebugApiConnection";
import LoginForm from "./components/auth/LoginForm";
import RegisterForm from "./components/auth/RegisterForm";
import ProjectDashboard from "./components/collaboration/ProjectDashboard";
import PWAInstallPrompt from "./components/PWAInstallPrompt";
import PWAErrorBoundary from "./components/PWAErrorBoundary";
import NetworkErrorBoundary from "./components/NetworkErrorBoundary";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider
      defaultTheme="system"
      defaultPreset="professional"
      defaultAnimationPreset="smooth-fade"
      enableTransitions={true}
      enableCustomization={true}
      enableAccessibilityFeatures={true}
    >
      <AuthProvider>
        <CollaborationProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <NetworkErrorBoundary>
                <div className="mobile-viewport mobile-safe-area">
                  <Routes>
                  <Route path="/" element={<Index />} />
                  <Route path="/analysis" element={<Analysis />} />
                  <Route path="/performance" element={<Performance />} />
                  <Route path="/performance-monitoring" element={<PerformanceMonitoring />} />
                  <Route path="/features" element={<Features />} />
                  <Route path="/debug" element={<DebugApiConnection />} />
                  <Route path="/login" element={<LoginForm />} />
                  <Route path="/register" element={<RegisterForm />} />
                  <Route path="/projects" element={<ProjectDashboard />} />
                  {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
                {/* PWA Install Prompt with Error Boundary */}
                <PWAErrorBoundary>
                  <PWAInstallPrompt autoShow={true} />
                </PWAErrorBoundary>
              </div>
            </NetworkErrorBoundary>
          </BrowserRouter>
          </TooltipProvider>
        </CollaborationProvider>
      </AuthProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
