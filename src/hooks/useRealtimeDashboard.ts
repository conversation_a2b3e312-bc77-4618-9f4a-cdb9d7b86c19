import { useEffect, useState, useCallback, useRef } from 'react';
import { io, Socket } from 'socket.io-client';

// TypeScript interfaces for WebSocket messages
export interface DashboardMetrics {
  timestamp: string;
  successRate: number;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  averageResponseTime: number;
  cacheHitRate: number;
  activeConnections: number;
  systemHealth: {
    status: 'operational' | 'degraded' | 'down';
    uptime: number;
    memoryUsage: any;
    cpuUsage: number;
  };
}

export interface TestExecutionUpdate {
  testId: string;
  testName: string;
  browser: string;
  status: 'running' | 'passed' | 'failed' | 'skipped';
  duration?: number;
  timestamp: string;
  errorMessage?: string;
}

export interface SuccessRateChange {
  previousRate: number;
  currentRate: number;
  change: number;
  threshold: number;
  timestamp: string;
  trend: 'improving' | 'declining' | 'stable';
}

export type ConnectionStatus = 'connected' | 'reconnecting' | 'disconnected';

export interface RealtimeDashboardState {
  connectionStatus: ConnectionStatus;
  metrics: DashboardMetrics | null;
  testUpdates: TestExecutionUpdate[];
  successRateChanges: SuccessRateChange[];
  alerts: any[];
  isConnected: boolean;
  reconnectAttempts: number;
  lastUpdate: string | null;
}

export interface UseRealtimeDashboardOptions {
  dashboardType: 'performance' | 'monitoring';
  maxReconnectAttempts?: number;
  reconnectIntervals?: number[]; // [1000, 2000, 4000] for exponential backoff
  dataHistoryLimit?: number; // Max data points to keep in memory
}

export function useRealtimeDashboard(options: UseRealtimeDashboardOptions) {
  const {
    dashboardType,
    maxReconnectAttempts = 3,
    reconnectIntervals = [1000, 2000, 4000],
    dataHistoryLimit = 100
  } = options;

  const [state, setState] = useState<RealtimeDashboardState>({
    connectionStatus: 'disconnected',
    metrics: null,
    testUpdates: [],
    successRateChanges: [],
    alerts: [],
    isConnected: false,
    reconnectAttempts: 0,
    lastUpdate: null
  });

  const socketRef = useRef<Socket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isManualDisconnect = useRef(false);

  // Validate incoming WebSocket messages
  const validateMessage = useCallback((data: any, expectedType: string): boolean => {
    if (!data || typeof data !== 'object') {
      console.warn(`Invalid ${expectedType} data received:`, data);
      return false;
    }
    
    if (!data.timestamp) {
      console.warn(`Missing timestamp in ${expectedType} data:`, data);
      return false;
    }
    
    return true;
  }, []);

  // Update state with data history management
  const updateStateWithHistory = useCallback((
    updateFn: (prevState: RealtimeDashboardState) => Partial<RealtimeDashboardState>
  ) => {
    setState(prevState => {
      const updates = updateFn(prevState);
      const newState = { ...prevState, ...updates };
      
      // Limit data history to prevent memory issues
      if (newState.testUpdates.length > dataHistoryLimit) {
        newState.testUpdates = newState.testUpdates.slice(-dataHistoryLimit);
      }
      
      if (newState.successRateChanges.length > dataHistoryLimit) {
        newState.successRateChanges = newState.successRateChanges.slice(-dataHistoryLimit);
      }
      
      if (newState.alerts.length > dataHistoryLimit) {
        newState.alerts = newState.alerts.slice(-dataHistoryLimit);
      }
      
      return newState;
    });
  }, [dataHistoryLimit]);

  // Exponential backoff reconnection logic
  const attemptReconnect = useCallback(() => {
    if (isManualDisconnect.current) return;
    
    setState(prevState => {
      if (prevState.reconnectAttempts >= maxReconnectAttempts) {
        console.error('Max reconnection attempts reached');
        return {
          ...prevState,
          connectionStatus: 'disconnected'
        };
      }
      
      const attemptIndex = Math.min(prevState.reconnectAttempts, reconnectIntervals.length - 1);
      const delay = reconnectIntervals[attemptIndex];
      
      console.log(`Attempting reconnection ${prevState.reconnectAttempts + 1}/${maxReconnectAttempts} in ${delay}ms`);
      
      reconnectTimeoutRef.current = setTimeout(() => {
        connectSocket();
      }, delay);
      
      return {
        ...prevState,
        connectionStatus: 'reconnecting',
        reconnectAttempts: prevState.reconnectAttempts + 1
      };
    });
  }, [maxReconnectAttempts, reconnectIntervals]);

  // Socket connection setup
  const connectSocket = useCallback(() => {
    if (socketRef.current?.connected) {
      return;
    }

    console.log(`Connecting to ${dashboardType} dashboard WebSocket...`);
    
    const socket = io('http://localhost:3001', {
      transports: ['websocket'],
      timeout: 5000,
      forceNew: true
    });

    socketRef.current = socket;

    // Connection events
    socket.on('connect', () => {
      console.log(`Connected to ${dashboardType} dashboard`);
      
      // Join dashboard room
      socket.emit('join-dashboard', { dashboardType });
      
      updateStateWithHistory(() => ({
        connectionStatus: 'connected',
        isConnected: true,
        reconnectAttempts: 0
      }));
    });

    socket.on('disconnect', (reason) => {
      console.log(`Disconnected from ${dashboardType} dashboard:`, reason);
      
      updateStateWithHistory(() => ({
        connectionStatus: 'disconnected',
        isConnected: false
      }));
      
      // Attempt reconnection unless manually disconnected
      if (!isManualDisconnect.current && reason !== 'io client disconnect') {
        attemptReconnect();
      }
    });

    socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      
      updateStateWithHistory(() => ({
        connectionStatus: 'disconnected',
        isConnected: false
      }));
      
      if (!isManualDisconnect.current) {
        attemptReconnect();
      }
    });

    // Dashboard-specific events
    socket.on('dashboard-connected', (data) => {
      console.log('Dashboard connection confirmed:', data);
    });

    socket.on('dashboard-metrics-refresh', (metrics: DashboardMetrics) => {
      if (validateMessage(metrics, 'dashboard-metrics')) {
        updateStateWithHistory(() => ({
          metrics,
          lastUpdate: new Date().toISOString()
        }));
      }
    });

    socket.on('test-execution-update', (testUpdate: TestExecutionUpdate) => {
      if (validateMessage(testUpdate, 'test-execution')) {
        updateStateWithHistory(prevState => ({
          testUpdates: [...prevState.testUpdates, testUpdate],
          lastUpdate: new Date().toISOString()
        }));
      }
    });

    socket.on('success-rate-change', (rateChange: SuccessRateChange) => {
      if (validateMessage(rateChange, 'success-rate-change')) {
        updateStateWithHistory(prevState => ({
          successRateChanges: [...prevState.successRateChanges, rateChange],
          lastUpdate: new Date().toISOString()
        }));
      }
    });

    socket.on('performance-alert', (alert: any) => {
      if (validateMessage(alert, 'performance-alert')) {
        updateStateWithHistory(prevState => ({
          alerts: [...prevState.alerts, alert],
          lastUpdate: new Date().toISOString()
        }));
      }
    });

    // Existing events for backward compatibility
    socket.on('gpt-o1-analytics-update', (data) => {
      if (validateMessage(data, 'gpt-o1-analytics')) {
        updateStateWithHistory(() => ({
          lastUpdate: new Date().toISOString()
        }));
      }
    });

    socket.on('caching-efficiency-update', (data) => {
      if (validateMessage(data, 'caching-efficiency')) {
        updateStateWithHistory(() => ({
          lastUpdate: new Date().toISOString()
        }));
      }
    });

    socket.on('system-health-update', (data) => {
      if (validateMessage(data, 'system-health')) {
        updateStateWithHistory(() => ({
          lastUpdate: new Date().toISOString()
        }));
      }
    });

  }, [dashboardType, validateMessage, updateStateWithHistory, attemptReconnect]);

  // Manual disconnect function
  const disconnect = useCallback(() => {
    isManualDisconnect.current = true;
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (socketRef.current) {
      socketRef.current.emit('leave-dashboard', { dashboardType });
      socketRef.current.disconnect();
      socketRef.current = null;
    }
    
    updateStateWithHistory(() => ({
      connectionStatus: 'disconnected',
      isConnected: false,
      reconnectAttempts: 0
    }));
  }, [dashboardType, updateStateWithHistory]);

  // Manual reconnect function
  const reconnect = useCallback(() => {
    isManualDisconnect.current = false;
    disconnect();
    setTimeout(() => {
      connectSocket();
    }, 100);
  }, [disconnect, connectSocket]);

  // Initialize connection on mount
  useEffect(() => {
    isManualDisconnect.current = false;
    connectSocket();
    
    return () => {
      disconnect();
    };
  }, [connectSocket, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  return {
    ...state,
    disconnect,
    reconnect,
    // Helper functions
    getLatestMetrics: () => state.metrics,
    getRecentTestUpdates: (count: number = 10) => state.testUpdates.slice(-count),
    getRecentAlerts: (count: number = 5) => state.alerts.slice(-count),
    clearAlerts: () => updateStateWithHistory(() => ({ alerts: [] })),
    clearTestUpdates: () => updateStateWithHistory(() => ({ testUpdates: [] }))
  };
}
