import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import {
  Theme,
  ResolvedTheme,
  ThemePreset,
  AnimationPreset,
  CustomColor,
  AdvancedThemeProviderProps,
  AdvancedThemeContextType,
  AccessibilitySettings,
  ColorCustomization,
  STORAGE_KEYS,
  DEFAULT_VALUES,
  isValidTheme,
  isValidPreset,
  isValidAnimationPreset,
} from '@/types/theme';
import { THEME_PRESETS, ANIMATION_PRESETS } from '@/config/themePresets';
import {
  generateComplementaryColors,
  applyCSSCustomProperties,
  applyHighContrastAdjustments,
  meetsAccessibilityStandards,
} from '@/utils/colorUtils';

const initialState: AdvancedThemeContextType = {
  // Basic theme
  theme: DEFAULT_VALUES.THEME,
  resolvedTheme: 'light',

  // Preset system
  preset: DEFAULT_VALUES.PRESET,
  customColors: DEFAULT_VALUES.CUSTOM_COLORS,

  // Animation system
  animationPreset: DEFAULT_VALUES.ANIMATION_PRESET,

  // Accessibility
  accessibility: DEFAULT_VALUES.ACCESSIBILITY,

  // State management
  isTransitioning: false,
  isCustomizing: false,

  // Basic theme functions
  setTheme: () => null,
  toggleTheme: () => null,

  // Preset functions
  setPreset: () => null,
  getPresetConfig: () => THEME_PRESETS.professional,

  // Color customization functions
  setCustomColor: () => null,
  resetCustomColors: () => null,
  applyCustomColors: () => null,

  // Animation functions
  setAnimationPreset: () => null,
  getAnimationConfig: () => ANIMATION_PRESETS['smooth-fade'],

  // Accessibility functions
  setAccessibilitySettings: () => null,
  toggleHighContrast: () => null,
  toggleFocusEnhancement: () => null,

  // Utility functions
  exportThemeConfig: () => '',
  importThemeConfig: () => false,
  resetToDefaults: () => null,
};

const ThemeProviderContext = createContext<AdvancedThemeContextType>(initialState);

export const ThemeProvider: React.FC<AdvancedThemeProviderProps> = ({
  children,
  defaultTheme = DEFAULT_VALUES.THEME,
  defaultPreset = DEFAULT_VALUES.PRESET,
  defaultAnimationPreset = DEFAULT_VALUES.ANIMATION_PRESET,
  storageKey = STORAGE_KEYS.THEME,
  presetStorageKey = STORAGE_KEYS.PRESET,
  animationStorageKey = STORAGE_KEYS.ANIMATION,
  accessibilityStorageKey = STORAGE_KEYS.ACCESSIBILITY,
  enableTransitions = true,
  enableCustomization = true,
  enableAccessibilityFeatures = true,
  ...props
}) => {
  // Basic theme state
  const [theme, setThemeState] = useState<Theme>(defaultTheme);
  const [systemTheme, setSystemTheme] = useState<ResolvedTheme>('light');
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Advanced theme state
  const [preset, setPresetState] = useState<ThemePreset>(defaultPreset);
  const [customColors, setCustomColorsState] = useState<ColorCustomization>(DEFAULT_VALUES.CUSTOM_COLORS);
  const [animationPreset, setAnimationPresetState] = useState<AnimationPreset>(defaultAnimationPreset);
  const [accessibility, setAccessibilityState] = useState<AccessibilitySettings>(DEFAULT_VALUES.ACCESSIBILITY);
  const [isCustomizing, setIsCustomizing] = useState(false);

  // Get resolved theme based on current theme setting
  const resolvedTheme: ResolvedTheme = theme === 'system' ? systemTheme : theme;

  // Initialize all settings from localStorage and system preferences
  useEffect(() => {
    // Basic theme initialization
    const savedTheme = localStorage.getItem(storageKey) as Theme;
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    setSystemTheme(systemPrefersDark ? 'dark' : 'light');

    if (savedTheme && isValidTheme(savedTheme)) {
      setThemeState(savedTheme);
    }

    // Preset initialization
    const savedPreset = localStorage.getItem(presetStorageKey) as ThemePreset;
    if (savedPreset && isValidPreset(savedPreset)) {
      setPresetState(savedPreset);
    }

    // Animation preset initialization
    const savedAnimationPreset = localStorage.getItem(animationStorageKey) as AnimationPreset;
    if (savedAnimationPreset && isValidAnimationPreset(savedAnimationPreset)) {
      setAnimationPresetState(savedAnimationPreset);
    }

    // Custom colors initialization
    try {
      const savedCustomColors = localStorage.getItem(STORAGE_KEYS.CUSTOM_COLORS);
      if (savedCustomColors) {
        const parsedColors = JSON.parse(savedCustomColors) as ColorCustomization;
        setCustomColorsState(parsedColors);
      }
    } catch (error) {
      console.warn('Failed to parse custom colors from localStorage:', error);
    }

    // Accessibility settings initialization
    if (enableAccessibilityFeatures) {
      try {
        const savedAccessibility = localStorage.getItem(accessibilityStorageKey);
        if (savedAccessibility) {
          const parsedAccessibility = JSON.parse(savedAccessibility) as AccessibilitySettings;
          setAccessibilityState({
            ...DEFAULT_VALUES.ACCESSIBILITY,
            ...parsedAccessibility,
            // Auto-detect system preferences if enabled
            reducedMotion: parsedAccessibility.autoDetectPreferences ? prefersReducedMotion : parsedAccessibility.reducedMotion,
          });
        } else if (DEFAULT_VALUES.ACCESSIBILITY.autoDetectPreferences) {
          // Set initial accessibility preferences based on system
          setAccessibilityState(prev => ({
            ...prev,
            reducedMotion: prefersReducedMotion,
          }));
        }
      } catch (error) {
        console.warn('Failed to parse accessibility settings from localStorage:', error);
      }
    }
  }, [storageKey, presetStorageKey, animationStorageKey, accessibilityStorageKey, enableAccessibilityFeatures]);

  // Listen for system preference changes
  useEffect(() => {
    const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');

    const handleDarkModeChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light');
    };

    const handleReducedMotionChange = (e: MediaQueryListEvent) => {
      if (accessibility.autoDetectPreferences) {
        setAccessibilityState(prev => ({
          ...prev,
          reducedMotion: e.matches,
        }));
      }
    };

    darkModeQuery.addEventListener('change', handleDarkModeChange);
    reducedMotionQuery.addEventListener('change', handleReducedMotionChange);

    return () => {
      darkModeQuery.removeEventListener('change', handleDarkModeChange);
      reducedMotionQuery.removeEventListener('change', handleReducedMotionChange);
    };
  }, [accessibility.autoDetectPreferences]);

  // Apply comprehensive theme to document
  useEffect(() => {
    const root = document.documentElement;
    const shouldUseTransitions = enableTransitions && !accessibility.reducedMotion;
    const animationConfig = ANIMATION_PRESETS[animationPreset];

    // Start transition if enabled and not reduced motion
    if (shouldUseTransitions) {
      setIsTransitioning(true);
      root.classList.add('theme-transitioning');

      // Apply animation preset styles
      root.style.setProperty('--theme-transition-duration', `${animationConfig.duration}ms`);
      root.style.setProperty('--theme-transition-easing', animationConfig.easing);
    }

    // Remove existing theme and preset classes
    root.classList.remove('light', 'dark');
    Object.keys(THEME_PRESETS).forEach(presetId => {
      root.classList.remove(`preset-${presetId}`);
    });

    // Apply new theme and preset classes
    root.classList.add(resolvedTheme);
    root.classList.add(`preset-${preset}`);

    // Apply accessibility classes
    if (accessibility.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    if (accessibility.focusEnhancement) {
      root.classList.add('focus-enhanced');
    } else {
      root.classList.remove('focus-enhanced');
    }

    if (accessibility.reducedMotion) {
      root.classList.add('reduced-motion');
    } else {
      root.classList.remove('reduced-motion');
    }

    // Set color-scheme for better browser integration
    root.style.colorScheme = resolvedTheme;

    // Apply preset colors
    const presetConfig = THEME_PRESETS[preset];
    if (presetConfig) {
      const presetColors = presetConfig.colors[resolvedTheme];
      const colorProperties: Record<string, string> = {};

      // Apply base preset colors
      colorProperties['aone-sage'] = presetColors.sage;
      colorProperties['aone-sage-light'] = presetColors.sageLight;
      colorProperties['aone-sage-dark'] = presetColors.sageDark;
      colorProperties['aone-accent'] = presetColors.accent;
      colorProperties['aone-accent-light'] = presetColors.accentLight;
      colorProperties['aone-accent-dark'] = presetColors.accentDark;
      colorProperties['aone-cream'] = presetColors.cream;
      colorProperties['aone-warm-white'] = presetColors.warmWhite;
      colorProperties['aone-charcoal'] = presetColors.charcoal;
      colorProperties['aone-soft-gray'] = presetColors.softGray;

      // Apply custom colors if enabled
      if (customColors.isCustom && enableCustomization) {
        if (customColors.customSage) {
          const customSageColors = generateComplementaryColors(customColors.customSage);
          colorProperties['aone-sage'] = customSageColors.sage;
          colorProperties['aone-sage-light'] = customSageColors.sageLight;
          colorProperties['aone-sage-dark'] = customSageColors.sageDark;
        }

        if (customColors.customAccent) {
          const customAccentColors = generateComplementaryColors(customColors.customAccent);
          colorProperties['aone-accent'] = customAccentColors.sage;
          colorProperties['aone-accent-light'] = customAccentColors.sageLight;
          colorProperties['aone-accent-dark'] = customAccentColors.sageDark;
        }
      }

      // Apply high contrast adjustments if enabled
      if (accessibility.highContrast) {
        Object.entries(colorProperties).forEach(([property, value]) => {
          if (property.includes('charcoal') || property.includes('sage-dark')) {
            colorProperties[property] = applyHighContrastAdjustments(value, resolvedTheme, 'text');
          } else if (property.includes('cream') || property.includes('warm-white')) {
            colorProperties[property] = applyHighContrastAdjustments(value, resolvedTheme, 'background');
          }
        });
      }

      // Apply all color properties
      applyCSSCustomProperties(colorProperties);
    }

    // End transition after animation duration
    if (shouldUseTransitions) {
      const timer = setTimeout(() => {
        setIsTransitioning(false);
        root.classList.remove('theme-transitioning');
      }, animationConfig.duration);

      return () => clearTimeout(timer);
    }
  }, [resolvedTheme, preset, customColors, accessibility, animationPreset, enableTransitions, enableCustomization]);

  // Basic theme functions
  const setTheme = useCallback((newTheme: Theme) => {
    setThemeState(newTheme);
    localStorage.setItem(storageKey, newTheme);
  }, [storageKey]);

  const toggleTheme = useCallback(() => {
    if (theme === 'system') {
      setTheme(systemTheme === 'dark' ? 'light' : 'dark');
    } else {
      setTheme(theme === 'dark' ? 'light' : 'dark');
    }
  }, [theme, systemTheme, setTheme]);

  // Preset functions
  const setPreset = useCallback((newPreset: ThemePreset) => {
    setPresetState(newPreset);
    localStorage.setItem(presetStorageKey, newPreset);
  }, [presetStorageKey]);

  const getPresetConfig = useCallback((presetId: ThemePreset) => {
    return THEME_PRESETS[presetId];
  }, []);

  // Color customization functions
  const setCustomColor = useCallback((type: 'sage' | 'accent', color: CustomColor) => {
    setCustomColorsState(prev => {
      const newCustomColors = {
        ...prev,
        isCustom: true,
        [`custom${type.charAt(0).toUpperCase() + type.slice(1)}`]: color,
      };
      localStorage.setItem(STORAGE_KEYS.CUSTOM_COLORS, JSON.stringify(newCustomColors));
      return newCustomColors;
    });
  }, []);

  const resetCustomColors = useCallback(() => {
    setCustomColorsState(DEFAULT_VALUES.CUSTOM_COLORS);
    localStorage.removeItem(STORAGE_KEYS.CUSTOM_COLORS);
  }, []);

  const applyCustomColors = useCallback(() => {
    // This function is called automatically by the theme application effect
    // but can be used to force a re-application if needed
    setIsCustomizing(false);
  }, []);

  // Animation functions
  const setAnimationPreset = useCallback((newPreset: AnimationPreset) => {
    setAnimationPresetState(newPreset);
    localStorage.setItem(animationStorageKey, newPreset);
  }, [animationStorageKey]);

  const getAnimationConfig = useCallback((presetId: AnimationPreset) => {
    return ANIMATION_PRESETS[presetId];
  }, []);

  // Accessibility functions
  const setAccessibilitySettings = useCallback((newSettings: Partial<AccessibilitySettings>) => {
    setAccessibilityState(prev => {
      const updatedSettings = { ...prev, ...newSettings };
      localStorage.setItem(accessibilityStorageKey, JSON.stringify(updatedSettings));
      return updatedSettings;
    });
  }, [accessibilityStorageKey]);

  const toggleHighContrast = useCallback(() => {
    setAccessibilitySettings({ highContrast: !accessibility.highContrast });
  }, [accessibility.highContrast, setAccessibilitySettings]);

  const toggleFocusEnhancement = useCallback(() => {
    setAccessibilitySettings({ focusEnhancement: !accessibility.focusEnhancement });
  }, [accessibility.focusEnhancement, setAccessibilitySettings]);

  // Utility functions
  const exportThemeConfig = useCallback(() => {
    const config = {
      theme,
      preset,
      customColors,
      animationPreset,
      accessibility,
      version: '1.0.0',
      exportedAt: new Date().toISOString(),
    };
    return JSON.stringify(config, null, 2);
  }, [theme, preset, customColors, animationPreset, accessibility]);

  const importThemeConfig = useCallback((configString: string) => {
    try {
      const config = JSON.parse(configString);

      // Validate and apply configuration
      if (config.theme && isValidTheme(config.theme)) {
        setTheme(config.theme);
      }

      if (config.preset && isValidPreset(config.preset)) {
        setPreset(config.preset);
      }

      if (config.animationPreset && isValidAnimationPreset(config.animationPreset)) {
        setAnimationPreset(config.animationPreset);
      }

      if (config.customColors) {
        setCustomColorsState(config.customColors);
        localStorage.setItem(STORAGE_KEYS.CUSTOM_COLORS, JSON.stringify(config.customColors));
      }

      if (config.accessibility) {
        setAccessibilitySettings(config.accessibility);
      }

      return true;
    } catch (error) {
      console.error('Failed to import theme configuration:', error);
      return false;
    }
  }, [setTheme, setPreset, setAnimationPreset, setAccessibilitySettings]);

  const resetToDefaults = useCallback(() => {
    setTheme(DEFAULT_VALUES.THEME);
    setPreset(DEFAULT_VALUES.PRESET);
    setAnimationPreset(DEFAULT_VALUES.ANIMATION_PRESET);
    resetCustomColors();
    setAccessibilitySettings(DEFAULT_VALUES.ACCESSIBILITY);
  }, [setTheme, setPreset, setAnimationPreset, resetCustomColors, setAccessibilitySettings]);

  const value: AdvancedThemeContextType = {
    // Basic theme
    theme,
    resolvedTheme,

    // Preset system
    preset,
    customColors,

    // Animation system
    animationPreset,

    // Accessibility
    accessibility,

    // State management
    isTransitioning,
    isCustomizing,

    // Basic theme functions
    setTheme,
    toggleTheme,

    // Preset functions
    setPreset,
    getPresetConfig,

    // Color customization functions
    setCustomColor,
    resetCustomColors,
    applyCustomColors,

    // Animation functions
    setAnimationPreset,
    getAnimationConfig,

    // Accessibility functions
    setAccessibilitySettings,
    toggleHighContrast,
    toggleFocusEnhancement,

    // Utility functions
    exportThemeConfig,
    importThemeConfig,
    resetToDefaults,
  };

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeProviderContext);

  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }

  return context;
};

// Advanced theme hooks for specific functionality
export const useThemePresets = () => {
  const { preset, setPreset, getPresetConfig } = useTheme();
  return { preset, setPreset, getPresetConfig };
};

export const useThemeCustomization = () => {
  const { customColors, setCustomColor, resetCustomColors, applyCustomColors, isCustomizing } = useTheme();
  return { customColors, setCustomColor, resetCustomColors, applyCustomColors, isCustomizing };
};

export const useThemeAnimation = () => {
  const { animationPreset, setAnimationPreset, getAnimationConfig } = useTheme();
  return { animationPreset, setAnimationPreset, getAnimationConfig };
};

export const useThemeAccessibility = () => {
  const { accessibility, setAccessibilitySettings, toggleHighContrast, toggleFocusEnhancement } = useTheme();
  return { accessibility, setAccessibilitySettings, toggleHighContrast, toggleFocusEnhancement };
};

// Utility hook for theme-aware styling (enhanced)
export const useThemeAwareStyle = () => {
  const { resolvedTheme, preset, accessibility } = useTheme();

  return {
    isDark: resolvedTheme === 'dark',
    isLight: resolvedTheme === 'light',
    themeClass: resolvedTheme,
    preset,
    isHighContrast: accessibility.highContrast,
    isReducedMotion: accessibility.reducedMotion,
    isFocusEnhanced: accessibility.focusEnhancement,
    getThemeValue: <T,>(lightValue: T, darkValue: T): T =>
      resolvedTheme === 'dark' ? darkValue : lightValue,
    getPresetValue: <T,>(values: Record<ThemePreset, T>): T =>
      values[preset] || values.professional,
  };
};

// Utility function for conditional theme classes (enhanced)
export const themeClasses = (lightClasses: string, darkClasses: string): string => {
  return `${lightClasses} dark:${darkClasses}`;
};

export const presetClasses = (classes: Partial<Record<ThemePreset, string>>): string => {
  return Object.entries(classes)
    .map(([preset, className]) => `preset-${preset}:${className}`)
    .join(' ');
};

// Backward compatibility exports
export type { Theme, ResolvedTheme };

// Legacy interface for backward compatibility
export interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
  enableTransitions?: boolean;
}

export interface ThemeProviderState {
  theme: Theme;
  resolvedTheme: ResolvedTheme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  isTransitioning: boolean;
}

// Export all new types
export type {
  ThemePreset,
  AnimationPreset,
  CustomColor,
  ColorCustomization,
  AccessibilitySettings,
  AdvancedThemeProviderProps,
  AdvancedThemeContextType,
} from '@/types/theme';
