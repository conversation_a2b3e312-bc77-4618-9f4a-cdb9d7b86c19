import React, { useState, useEffect, useContext, createContext, ReactNode } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from './useAuth';

export interface Comment {
  id: string;
  projectId?: string;
  analysisId?: string;
  parentCommentId?: string;
  content: string;
  authorId: string;
  authorName: string;
  avatarUrl?: string;
  status: 'open' | 'in_progress' | 'resolved';
  positionData?: any;
  attachments?: string[];
  mentions?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Project {
  id: string;
  name: string;
  description?: string;
  organizationId?: string;
  ownerId: string;
  ownerName: string;
  ownerEmail: string;
  visibility: 'public' | 'private' | 'organization';
  status: 'active' | 'archived' | 'deleted';
  tags?: string[];
  folderPath?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserPresence {
  userId: string;
  userName: string;
  avatarUrl?: string;
  status: 'online' | 'away' | 'offline';
  currentView?: any;
  lastActivity: Date;
}

interface CollaborationContextType {
  socket: Socket | null;
  isConnected: boolean;
  currentProject: Project | null;
  onlineUsers: UserPresence[];
  
  // Project methods
  getProjects: () => Promise<Project[]>;
  createProject: (data: any) => Promise<Project>;
  getProject: (id: string) => Promise<Project>;
  updateProject: (id: string, updates: any) => Promise<Project>;
  deleteProject: (id: string) => Promise<void>;
  shareProject: (projectId: string, data: any) => Promise<any>;
  
  // Comment methods
  getComments: (analysisId: string) => Promise<Comment[]>;
  getComment: (commentId: string) => Promise<Comment>;
  getProjectComments: (projectId: string) => Promise<Comment[]>;
  createComment: (data: any) => Promise<Comment>;
  updateComment: (commentId: string, content: string, mentions?: string[]) => Promise<Comment>;
  updateCommentStatus: (commentId: string, status: string) => Promise<void>;
  deleteComment: (commentId: string) => Promise<void>;
  
  // Presence methods
  joinProject: (projectId: string) => void;
  leaveProject: (projectId: string) => void;
  getProjectPresence: (projectId: string) => Promise<UserPresence[]>;
  
  // Real-time events
  onCommentAdded: (callback: (comment: Comment) => void) => void;
  onUserJoined: (callback: (data: any) => void) => void;
  onUserLeft: (callback: (data: any) => void) => void;
  onPresenceUpdate: (callback: (presence: UserPresence) => void) => void;
}

const CollaborationContext = createContext<CollaborationContextType | undefined>(undefined);

export const useCollaboration = () => {
  const context = useContext(CollaborationContext);
  if (context === undefined) {
    throw new Error('useCollaboration must be used within a CollaborationProvider');
  }
  return context;
};

interface CollaborationProviderProps {
  children: ReactNode;
}

export const CollaborationProvider: React.FC<CollaborationProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [currentProject, setCurrentProject] = useState<Project | null>(null);
  const [onlineUsers, setOnlineUsers] = useState<UserPresence[]>([]);
  
  const { user, tokens } = useAuth();
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

  // Initialize socket connection
  useEffect(() => {
    if (user && tokens) {
      const newSocket = io(API_BASE_URL, {
        auth: {
          token: tokens.accessToken
        }
      });

      newSocket.on('connect', () => {
        setIsConnected(true);
        // Authenticate socket
        newSocket.emit('authenticate', { token: tokens.accessToken });
      });

      newSocket.on('disconnect', () => {
        setIsConnected(false);
      });

      newSocket.on('authenticated', (data) => {
        console.log('Socket authenticated:', data);
      });

      newSocket.on('authentication-error', (error) => {
        console.error('Socket authentication failed:', error);
      });

      setSocket(newSocket);

      return () => {
        newSocket.close();
      };
    }
  }, [user, tokens, API_BASE_URL]);

  // API helper function
  const apiCall = async (endpoint: string, options: RequestInit = {}) => {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${tokens?.accessToken}`,
        ...options.headers
      }
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.error || 'API call failed');
    }

    return data;
  };

  // Project methods
  const getProjects = async (): Promise<Project[]> => {
    const data = await apiCall('/api/collaboration/projects');
    return data.data;
  };

  const createProject = async (projectData: any): Promise<Project> => {
    const data = await apiCall('/api/collaboration/projects', {
      method: 'POST',
      body: JSON.stringify(projectData)
    });
    return data.data;
  };

  const getProject = async (id: string): Promise<Project> => {
    const data = await apiCall(`/api/collaboration/projects/${id}`);
    return data.data;
  };

  const updateProject = async (id: string, updates: any): Promise<Project> => {
    const data = await apiCall(`/api/collaboration/projects/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates)
    });
    return data.data;
  };

  const deleteProject = async (id: string): Promise<void> => {
    await apiCall(`/api/collaboration/projects/${id}`, {
      method: 'DELETE'
    });
  };

  const shareProject = async (projectId: string, shareData: any): Promise<any> => {
    const data = await apiCall(`/api/collaboration/projects/${projectId}/share`, {
      method: 'POST',
      body: JSON.stringify(shareData)
    });
    return data.data;
  };

  // Comment methods
  const getComments = async (analysisId: string): Promise<Comment[]> => {
    const data = await apiCall(`/api/collaboration/analysis/${analysisId}/comments`);
    return data.data;
  };

  const getComment = async (commentId: string): Promise<Comment> => {
    const data = await apiCall(`/api/collaboration/comments/${commentId}`);
    return data.data;
  };

  const getProjectComments = async (projectId: string): Promise<Comment[]> => {
    const data = await apiCall(`/api/collaboration/projects/${projectId}/comments`);
    return data.data;
  };

  const createComment = async (commentData: any): Promise<Comment> => {
    const data = await apiCall('/api/collaboration/comments', {
      method: 'POST',
      body: JSON.stringify(commentData)
    });
    return data.data;
  };

  const updateComment = async (commentId: string, content: string, mentions?: string[]): Promise<Comment> => {
    const data = await apiCall(`/api/collaboration/comments/${commentId}`, {
      method: 'PUT',
      body: JSON.stringify({ content, mentions })
    });
    return data.data;
  };

  const updateCommentStatus = async (commentId: string, status: string): Promise<void> => {
    await apiCall(`/api/collaboration/comments/${commentId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status })
    });
  };

  const deleteComment = async (commentId: string): Promise<void> => {
    await apiCall(`/api/collaboration/comments/${commentId}`, {
      method: 'DELETE'
    });
  };

  // Presence methods
  const joinProject = (projectId: string) => {
    if (socket) {
      socket.emit('join-project', { projectId });
      setCurrentProject(prev => prev?.id === projectId ? prev : null);
    }
  };

  const leaveProject = (projectId: string) => {
    if (socket) {
      socket.emit('leave-project', { projectId });
      if (currentProject?.id === projectId) {
        setCurrentProject(null);
      }
    }
  };

  const getProjectPresence = async (projectId: string): Promise<UserPresence[]> => {
    const data = await apiCall(`/api/collaboration/projects/${projectId}/presence`);
    return data.data;
  };

  // Real-time event handlers
  const onCommentAdded = (callback: (comment: Comment) => void) => {
    if (socket) {
      socket.on('comment-added', callback);
      return () => socket.off('comment-added', callback);
    }
  };

  const onUserJoined = (callback: (data: any) => void) => {
    if (socket) {
      socket.on('user-joined', callback);
      return () => socket.off('user-joined', callback);
    }
  };

  const onUserLeft = (callback: (data: any) => void) => {
    if (socket) {
      socket.on('user-left', callback);
      return () => socket.off('user-left', callback);
    }
  };

  const onPresenceUpdate = (callback: (presence: UserPresence) => void) => {
    if (socket) {
      socket.on('presence-updated', callback);
      return () => socket.off('presence-updated', callback);
    }
  };

  const value: CollaborationContextType = {
    socket,
    isConnected,
    currentProject,
    onlineUsers,
    
    // Project methods
    getProjects,
    createProject,
    getProject,
    updateProject,
    deleteProject,
    shareProject,
    
    // Comment methods
    getComments,
    getComment,
    getProjectComments,
    createComment,
    updateComment,
    updateCommentStatus,
    deleteComment,
    
    // Presence methods
    joinProject,
    leaveProject,
    getProjectPresence,
    
    // Real-time events
    onCommentAdded,
    onUserJoined,
    onUserLeft,
    onPresenceUpdate
  };

  return React.createElement(
    CollaborationContext.Provider,
    { value },
    children
  );
};
