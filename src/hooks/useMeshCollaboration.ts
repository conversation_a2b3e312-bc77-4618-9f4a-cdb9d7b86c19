import { useState, useEffect, useCallback, useRef } from 'react';
import { Socket } from 'socket.io-client';
import { MeshWebRTCService, MeshDataMessage, MeshTopologyInfo } from '@/services/meshWebRTCService';
import { useCollaboration } from './useCollaboration';

export interface MeshPeerInfo {
  id: string;
  userId: string;
  connectionState: string;
  connectionQuality: number;
  latency: number;
  lastActivity: number;
}

export interface MeshNetworkStatus {
  isConnected: boolean;
  peerCount: number;
  connectedPeers: number;
  averageLatency: number;
  averageQuality: number;
  topology: MeshTopologyInfo | null;
}

export interface UseMeshCollaborationProps {
  projectId: string;
  enabled?: boolean;
  maxPeers?: number;
  autoConnect?: boolean;
}

export interface UseMeshCollaborationReturn {
  // Connection status
  meshStatus: MeshNetworkStatus;
  connectedPeers: MeshPeerInfo[];
  isInitialized: boolean;
  
  // Actions
  initializeMesh: () => Promise<void>;
  leaveMesh: () => Promise<void>;
  
  // Communication
  sendCursorPosition: (x: number, y: number, elementId?: string) => void;
  sendVoiceComment: (commentId: string, audioBlob: Blob, duration: number) => void;
  sendSceneUpdate: (sceneData: any) => void;
  sendQuoteUpdate: (quoteData: any) => void;
  
  // Event handlers
  onPeerJoined: (handler: (peerId: string, userId: string) => void) => void;
  onPeerLeft: (handler: (peerId: string, userId: string) => void) => void;
  onDataReceived: (handler: (data: MeshDataMessage, fromPeerId: string) => void) => void;
  onTopologyUpdated: (handler: (topology: MeshTopologyInfo) => void) => void;
  onConnectionQualityChanged: (handler: (peerId: string, quality: number) => void) => void;
}

/**
 * Advanced P2P Mesh Collaboration Hook
 * 
 * Provides mesh networking capabilities for Cabinet Insight Pro collaboration
 * Integrates with existing collaboration infrastructure while adding mesh P2P support
 */
export const useMeshCollaboration = ({
  projectId,
  enabled = true,
  maxPeers = 6,
  autoConnect = true
}: UseMeshCollaborationProps): UseMeshCollaborationReturn => {
  
  const { socket, isConnected, user } = useCollaboration();
  const meshServiceRef = useRef<MeshWebRTCService | null>(null);
  
  const [meshStatus, setMeshStatus] = useState<MeshNetworkStatus>({
    isConnected: false,
    peerCount: 0,
    connectedPeers: 0,
    averageLatency: 0,
    averageQuality: 0,
    topology: null
  });
  
  const [connectedPeers, setConnectedPeers] = useState<MeshPeerInfo[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // Event handler refs to maintain stable references
  const eventHandlersRef = useRef<{
    onPeerJoined?: (peerId: string, userId: string) => void;
    onPeerLeft?: (peerId: string, userId: string) => void;
    onDataReceived?: (data: MeshDataMessage, fromPeerId: string) => void;
    onTopologyUpdated?: (topology: MeshTopologyInfo) => void;
    onConnectionQualityChanged?: (peerId: string, quality: number) => void;
  }>({});

  /**
   * Initialize mesh WebRTC service
   */
  const initializeMesh = useCallback(async (): Promise<void> => {
    if (!enabled || !socket || !isConnected || !user || isInitialized) {
      return;
    }

    try {
      console.log('Initializing mesh collaboration for project:', projectId);

      // Create mesh service if not exists
      if (!meshServiceRef.current) {
        meshServiceRef.current = new MeshWebRTCService({
          maxPeers,
          enableDataChannels: true,
          enableAudio: false,
          enableVideo: false,
          qualityCheckInterval: 5000
        });
      }

      const meshService = meshServiceRef.current;

      // Setup event handlers
      meshService.onPeerJoined((peerId, userId) => {
        console.log('Mesh peer joined:', peerId, userId);
        eventHandlersRef.current.onPeerJoined?.(peerId, userId);
        updateMeshStatus();
      });

      meshService.onPeerLeft((peerId, userId) => {
        console.log('Mesh peer left:', peerId, userId);
        eventHandlersRef.current.onPeerLeft?.(peerId, userId);
        updateMeshStatus();
      });

      meshService.onDataReceived((data, fromPeerId) => {
        console.log('Mesh data received:', data.type, 'from:', fromPeerId);
        eventHandlersRef.current.onDataReceived?.(data, fromPeerId);
      });

      meshService.onTopologyUpdated((topology) => {
        console.log('Mesh topology updated:', topology);
        eventHandlersRef.current.onTopologyUpdated?.(topology);
        updateMeshStatus();
      });

      meshService.onConnectionQualityChanged((peerId, quality) => {
        console.log('Connection quality changed:', peerId, quality);
        eventHandlersRef.current.onConnectionQualityChanged?.(peerId, quality);
        updateMeshStatus();
      });

      // Initialize mesh service
      await meshService.initialize(socket, user.id, projectId);
      
      setIsInitialized(true);
      updateMeshStatus();

      console.log('Mesh collaboration initialized successfully');

    } catch (error) {
      console.error('Failed to initialize mesh collaboration:', error);
      setIsInitialized(false);
    }
  }, [enabled, socket, isConnected, user, projectId, maxPeers, isInitialized]);

  /**
   * Leave mesh network
   */
  const leaveMesh = useCallback(async (): Promise<void> => {
    if (!meshServiceRef.current || !isInitialized) {
      return;
    }

    try {
      await meshServiceRef.current.leaveMeshNetwork();
      setIsInitialized(false);
      setMeshStatus({
        isConnected: false,
        peerCount: 0,
        connectedPeers: 0,
        averageLatency: 0,
        averageQuality: 0,
        topology: null
      });
      setConnectedPeers([]);
      
      console.log('Left mesh network successfully');
    } catch (error) {
      console.error('Failed to leave mesh network:', error);
    }
  }, [isInitialized]);

  /**
   * Update mesh status from service
   */
  const updateMeshStatus = useCallback(() => {
    if (!meshServiceRef.current) return;

    const status = meshServiceRef.current.getMeshStatus();
    const peers = meshServiceRef.current.getConnectedPeers();

    setMeshStatus(status);
    setConnectedPeers(peers);
  }, []);

  /**
   * Auto-initialize when conditions are met
   */
  useEffect(() => {
    if (autoConnect && enabled && socket && isConnected && user && !isInitialized) {
      initializeMesh();
    }
  }, [autoConnect, enabled, socket, isConnected, user, isInitialized, initializeMesh]);

  /**
   * Cleanup on unmount or when leaving project
   */
  useEffect(() => {
    return () => {
      if (meshServiceRef.current && isInitialized) {
        meshServiceRef.current.destroy();
        setIsInitialized(false);
      }
    };
  }, [isInitialized]);

  /**
   * Communication methods
   */
  const sendCursorPosition = useCallback((x: number, y: number, elementId?: string) => {
    meshServiceRef.current?.sendCursorPosition(x, y, elementId);
  }, []);

  const sendVoiceComment = useCallback((commentId: string, audioBlob: Blob, duration: number) => {
    meshServiceRef.current?.sendVoiceComment(commentId, audioBlob, duration);
  }, []);

  const sendSceneUpdate = useCallback((sceneData: any) => {
    meshServiceRef.current?.sendSceneUpdate(sceneData);
  }, []);

  const sendQuoteUpdate = useCallback((quoteData: any) => {
    meshServiceRef.current?.sendQuoteUpdate(quoteData);
  }, []);

  /**
   * Event handler setters
   */
  const onPeerJoined = useCallback((handler: (peerId: string, userId: string) => void) => {
    eventHandlersRef.current.onPeerJoined = handler;
  }, []);

  const onPeerLeft = useCallback((handler: (peerId: string, userId: string) => void) => {
    eventHandlersRef.current.onPeerLeft = handler;
  }, []);

  const onDataReceived = useCallback((handler: (data: MeshDataMessage, fromPeerId: string) => void) => {
    eventHandlersRef.current.onDataReceived = handler;
  }, []);

  const onTopologyUpdated = useCallback((handler: (topology: MeshTopologyInfo) => void) => {
    eventHandlersRef.current.onTopologyUpdated = handler;
  }, []);

  const onConnectionQualityChanged = useCallback((handler: (peerId: string, quality: number) => void) => {
    eventHandlersRef.current.onConnectionQualityChanged = handler;
  }, []);

  return {
    // Status
    meshStatus,
    connectedPeers,
    isInitialized,
    
    // Actions
    initializeMesh,
    leaveMesh,
    
    // Communication
    sendCursorPosition,
    sendVoiceComment,
    sendSceneUpdate,
    sendQuoteUpdate,
    
    // Event handlers
    onPeerJoined,
    onPeerLeft,
    onDataReceived,
    onTopologyUpdated,
    onConnectionQualityChanged
  };
};
