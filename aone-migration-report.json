{"filesProcessed": 71, "replacementsMade": 355, "migrationDetails": [{"file": "src/components/PerformanceMetricsDashboard.tsx", "replacements": 28}, {"file": "src/components/mobile/MobileAnalysisViewer.tsx", "replacements": 19}, {"file": "src/components/EnhancedAnalysisMonitor.tsx", "replacements": 14}, {"file": "src/components/visualization/VisualExperienceSystem.tsx", "replacements": 14}, {"file": "src/components/AnalysisResults.tsx", "replacements": 12}, {"file": "src/components/QuoteComparisonTool.tsx", "replacements": 12}, {"file": "src/components/EnhancedAnalysisUpload.tsx", "replacements": 11}, {"file": "src/components/EnhancedAnalysisResults.tsx", "replacements": 10}, {"file": "src/components/collaboration/CollaborationDashboard.tsx", "replacements": 10}, {"file": "src/components/reasoning/ReasoningStepDetails.tsx", "replacements": 10}, {"file": "src/components/EnhancedWebSocketStatus.tsx", "replacements": 9}, {"file": "src/components/QuoteSummary.tsx", "replacements": 9}, {"file": "src/components/AnalysisDashboard.tsx", "replacements": 8}, {"file": "src/components/CabinetReconstructionViewer.tsx", "replacements": 7}, {"file": "src/components/DashboardPreview.tsx", "replacements": 7}, {"file": "src/components/LayoutOptimizationViewer.tsx", "replacements": 7}, {"file": "src/components/collaboration/ClientReviewMode.tsx", "replacements": 7}, {"file": "src/components/visualization/InteractiveReasoningTree.tsx", "replacements": 7}, {"file": "src/pages/Features.tsx", "replacements": 7}, {"file": "src/components/FeaturesSection.tsx", "replacements": 6}, {"file": "src/components/NetworkErrorBoundary.tsx", "replacements": 6}, {"file": "src/components/collaboration/VersionControlSystem.tsx", "replacements": 6}, {"file": "src/components/mobile/MobileReportViewer.tsx", "replacements": 6}, {"file": "src/components/mobile/MobileUploadInterface.tsx", "replacements": 6}, {"file": "src/components/ui/sidebar.tsx", "replacements": 6}, {"file": "src/components/PerformanceMonitoringDashboard.tsx", "replacements": 5}, {"file": "src/components/QuotationSection.tsx", "replacements": 5}, {"file": "src/components/QuoteTemplateForm.tsx", "replacements": 5}, {"file": "src/components/ReasoningChainVisualization.tsx", "replacements": 5}, {"file": "src/components/collaboration/AdvancedAnnotationSystem.tsx", "replacements": 5}, {"file": "src/components/FeaturesShowcase.tsx", "replacements": 4}, {"file": "src/components/QuoteTemplatePreview.tsx", "replacements": 4}, {"file": "src/components/ReportGenerator.tsx", "replacements": 4}, {"file": "src/components/collaboration/CommentThread.tsx", "replacements": 4}, {"file": "src/components/collaboration/EnhancedUserPresence.tsx", "replacements": 4}, {"file": "src/components/reasoning/ReasoningChainVisualization.tsx", "replacements": 4}, {"file": "src/components/ui/error-state.tsx", "replacements": 4}, {"file": "src/components/AnalysisUpload.tsx", "replacements": 3}, {"file": "src/components/DebugApiConnection.tsx", "replacements": 3}, {"file": "src/components/MeshNetworkVisualization.tsx", "replacements": 3}, {"file": "src/components/QuoteTemplateManager.tsx", "replacements": 3}, {"file": "src/components/collaboration/ProjectDashboard.tsx", "replacements": 3}, {"file": "src/components/collaboration/VoiceCommentsSystem.tsx", "replacements": 3}, {"file": "src/components/mobile/Mobile3DViewer.tsx", "replacements": 3}, {"file": "src/components/ui/loading.tsx", "replacements": 3}, {"file": "src/components/AOneBanner.tsx", "replacements": 2}, {"file": "src/components/Header.tsx", "replacements": 2}, {"file": "src/components/PWAInstallPrompt.tsx", "replacements": 2}, {"file": "src/components/QuoteAlternatives.tsx", "replacements": 2}, {"file": "src/components/QuoteTierCard.tsx", "replacements": 2}, {"file": "src/components/collaboration/UserPresence.tsx", "replacements": 2}, {"file": "src/components/ui/drawer.tsx", "replacements": 2}, {"file": "src/components/visualization/CinematicCabinetViewer.tsx", "replacements": 2}, {"file": "src/components/NetworkStatusIndicator.tsx", "replacements": 1}, {"file": "src/components/collaboration/EnhancedCursorTracker.tsx", "replacements": 1}, {"file": "src/components/ui/alert.tsx", "replacements": 1}, {"file": "src/components/ui/button.tsx", "replacements": 1}, {"file": "src/components/ui/calendar.tsx", "replacements": 1}, {"file": "src/components/ui/chart.tsx", "replacements": 1}, {"file": "src/components/ui/checkbox.tsx", "replacements": 1}, {"file": "src/components/ui/hover-card.tsx", "replacements": 1}, {"file": "src/components/ui/input-otp.tsx", "replacements": 1}, {"file": "src/components/ui/popover.tsx", "replacements": 1}, {"file": "src/components/ui/radio-group.tsx", "replacements": 1}, {"file": "src/components/ui/table.tsx", "replacements": 1}, {"file": "src/components/ui/tabs.tsx", "replacements": 1}, {"file": "src/components/ui/toast.tsx", "replacements": 1}, {"file": "src/components/ui/toggle-group.tsx", "replacements": 1}, {"file": "src/components/ui/toggle.tsx", "replacements": 1}, {"file": "src/pages/Index.tsx", "replacements": 1}, {"file": "src/pages/NotFound.tsx", "replacements": 1}]}