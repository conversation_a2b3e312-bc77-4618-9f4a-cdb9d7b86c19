import { test, expect } from '@playwright/test';
import { TestHelpers } from './utils/test-helpers';

test.describe('Phase 1 Visual Experience System', () => {
  let testHelpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    testHelpers = new TestHelpers(page);
    await page.goto('/');
    await testHelpers.waitForAppLoad();
  });

  test.describe('Enhanced Visual Design System', () => {
    test('should display enterprise-grade hero section with animations', async ({ page }) => {
      // Check hero section structure
      const heroSection = page.locator('div').filter({ hasText: /Transform Kitchen Drawings into/ }).first();
      await expect(heroSection).toBeVisible();

      // Verify enhanced background elements
      const backgroundOrbs = page.locator('.animate-float-gentle');
      await expect(backgroundOrbs).toHaveCount(2);

      // Check cinematic elements
      const pulseElements = page.locator('.animate-pulse-elegant');
      await expect(pulseElements.first()).toBeVisible();

      // Verify enterprise badge
      const enterpriseBadge = page.locator('text=Enterprise-Grade AI Kitchen Analysis');
      await expect(enterpriseBadge).toBeVisible();
      await expect(enterpriseBadge.locator('..').locator('svg')).toBeVisible(); // Sparkles icon
    });

    test('should display enhanced feature cards with enterprise styling', async ({ page }) => {
      // Check for enterprise card styling
      const featureCards = page.locator('.aone-card-enterprise');
      await expect(featureCards).toHaveCount(3);

      // Verify card animations
      const animatedCards = page.locator('.animate-fade-in-elegant');
      await expect(animatedCards.first()).toBeVisible();

      // Check feature icons
      const featureIcons = page.locator('.aone-feature-icon');
      await expect(featureIcons).toHaveCount(3);

      // Verify enterprise typography
      const enterpriseHeadings = page.locator('.aone-heading-enterprise');
      await expect(enterpriseHeadings.first()).toBeVisible();

      const enterpriseBody = page.locator('.aone-body-enterprise');
      await expect(enterpriseBody.first()).toBeVisible();
    });

    test('should display enhanced header with enterprise navigation', async ({ page }) => {
      // Check header animation
      const header = page.locator('header.animate-slide-in-elegant');
      await expect(header).toBeVisible();

      // Verify logo with glow animation
      const logoIcon = page.locator('.animate-glow-sage');
      await expect(logoIcon).toBeVisible();

      // Check enterprise navigation styling
      const navLinks = page.locator('.aone-nav-enterprise');
      await expect(navLinks.first()).toBeVisible();

      // Verify micro-interactions
      const microInteractions = page.locator('.aone-micro-interaction');
      await expect(microInteractions.first()).toBeVisible();
    });
  });

  test.describe('Enhanced Loading Components', () => {
    test('should display enterprise loading states', async ({ page }) => {
      // Navigate to analysis page to trigger loading states
      await page.click('text=Start Analysis');
      await page.waitForURL('/analysis');

      // Check for enhanced loading components (if present)
      const loadingSpinner = page.locator('.aone-loading-spinner-enterprise');
      if (await loadingSpinner.isVisible()) {
        await expect(loadingSpinner).toBeVisible();
      }

      // Verify elegant skeleton loading
      const elegantSkeleton = page.locator('.aone-loading-skeleton-elegant');
      if (await elegantSkeleton.isVisible()) {
        await expect(elegantSkeleton).toBeVisible();
      }
    });

    test('should handle enterprise loading spinner variants', async ({ page }) => {
      // Test different spinner sizes and variants through component interaction
      await page.goto('/analysis');
      
      // Look for any loading states that might be present
      const loadingElements = page.locator('[class*="loading"]');
      const count = await loadingElements.count();
      
      if (count > 0) {
        // Verify at least one loading element is visible
        await expect(loadingElements.first()).toBeVisible();
      }
    });
  });

  test.describe('Enhanced Interactive Elements', () => {
    test('should apply micro-interactions to buttons', async ({ page }) => {
      // Check primary button with micro-interactions
      const primaryButton = page.locator('button').filter({ hasText: /Start Analysis/ });
      await expect(primaryButton).toBeVisible();
      
      // Verify button has micro-interaction class
      await expect(primaryButton).toHaveClass(/aone-micro-interaction/);

      // Test hover state (visual verification)
      await primaryButton.hover();
      await page.waitForTimeout(200); // Allow animation to complete
    });

    test('should display enterprise button variants', async ({ page }) => {
      // Check for enterprise button styling
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();
      
      expect(buttonCount).toBeGreaterThan(0);
      
      // Verify at least one button has enterprise styling
      const enterpriseButton = page.locator('button[class*="enterprise"]');
      if (await enterpriseButton.isVisible()) {
        await expect(enterpriseButton).toBeVisible();
      }
    });

    test('should handle focus states with enterprise styling', async ({ page }) => {
      // Test focus ring on interactive elements
      const focusableElements = page.locator('.aone-focus-ring');
      if (await focusableElements.count() > 0) {
        await focusableElements.first().focus();
        await page.waitForTimeout(100); // Allow focus animation
      }
    });
  });

  test.describe('Enhanced Error States', () => {
    test('should display enterprise error states', async ({ page }) => {
      // Navigate to a page that might trigger error states
      await page.goto('/analysis');
      
      // Look for any error state components
      const errorStates = page.locator('.aone-error-enterprise');
      if (await errorStates.count() > 0) {
        await expect(errorStates.first()).toBeVisible();
      }

      // Check for form error styling
      const formErrors = page.locator('.aone-error-enterprise');
      if (await formErrors.count() > 0) {
        await expect(formErrors.first()).toHaveClass(/animate-slide-in-elegant/);
      }
    });

    test('should handle success states with enterprise styling', async ({ page }) => {
      // Look for success state components
      const successStates = page.locator('.aone-success-enterprise');
      if (await successStates.count() > 0) {
        await expect(successStates.first()).toBeVisible();
        await expect(successStates.first()).toHaveClass(/animate-slide-in-elegant/);
      }
    });
  });

  test.describe('Animation System', () => {
    test('should apply fade-in animations', async ({ page }) => {
      // Check for fade-in animations
      const fadeInElements = page.locator('.animate-fade-in-elegant');
      await expect(fadeInElements.first()).toBeVisible();
    });

    test('should apply slide-in animations', async ({ page }) => {
      // Check for slide-in animations
      const slideInElements = page.locator('.animate-slide-in-elegant');
      await expect(slideInElements.first()).toBeVisible();
    });

    test('should apply floating animations', async ({ page }) => {
      // Check for floating animations
      const floatingElements = page.locator('.animate-float-gentle');
      await expect(floatingElements.first()).toBeVisible();
    });

    test('should apply pulse animations', async ({ page }) => {
      // Check for pulse animations
      const pulseElements = page.locator('.animate-pulse-elegant');
      await expect(pulseElements.first()).toBeVisible();
    });

    test('should handle animation delays', async ({ page }) => {
      // Check for animation delay classes
      const delayedElements = page.locator('[class*="animate-delay"]');
      if (await delayedElements.count() > 0) {
        await expect(delayedElements.first()).toBeVisible();
      }
    });
  });

  test.describe('Glass Effects and Backdrop Blur', () => {
    test('should apply glass effects to components', async ({ page }) => {
      // Check for glass effect styling
      const glassElements = page.locator('.aone-glass');
      if (await glassElements.count() > 0) {
        await expect(glassElements.first()).toBeVisible();
      }
    });

    test('should handle backdrop blur effects', async ({ page }) => {
      // Verify backdrop blur is applied
      const blurElements = page.locator('[class*="backdrop-blur"]');
      if (await blurElements.count() > 0) {
        await expect(blurElements.first()).toBeVisible();
      }
    });
  });

  test.describe('Typography Enhancement', () => {
    test('should apply enterprise typography classes', async ({ page }) => {
      // Check enterprise heading styling
      const enterpriseHeadings = page.locator('.aone-heading-enterprise');
      await expect(enterpriseHeadings.first()).toBeVisible();

      // Check enterprise subheading styling
      const enterpriseSubheadings = page.locator('.aone-subheading-enterprise');
      if (await enterpriseSubheadings.count() > 0) {
        await expect(enterpriseSubheadings.first()).toBeVisible();
      }

      // Check enterprise body text styling
      const enterpriseBody = page.locator('.aone-body-enterprise');
      await expect(enterpriseBody.first()).toBeVisible();
    });
  });

  test.describe('Cross-Browser Compatibility', () => {
    test('should render consistently across browsers', async ({ page, browserName }) => {
      // Verify core visual elements are present regardless of browser
      const heroSection = page.locator('text=Transform Kitchen Drawings into');
      await expect(heroSection).toBeVisible();

      const featureCards = page.locator('.aone-card-enterprise');
      await expect(featureCards).toHaveCount(3);

      // Browser-specific checks
      if (browserName === 'webkit') {
        // Safari-specific checks
        const animations = page.locator('[class*="animate-"]');
        await expect(animations.first()).toBeVisible();
      }
    });
  });

  test.describe('Performance Impact', () => {
    test('should maintain good performance with visual enhancements', async ({ page }) => {
      // Measure page load performance
      const startTime = Date.now();
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      const loadTime = Date.now() - startTime;

      // Ensure page loads within reasonable time (5 seconds)
      expect(loadTime).toBeLessThan(5000);

      // Verify animations don't block interaction
      const button = page.locator('text=Start Analysis');
      await expect(button).toBeVisible();
      await button.click();
      await page.waitForURL('/analysis');
    });
  });

  test.describe('Accessibility Compliance', () => {
    test('should maintain accessibility with visual enhancements', async ({ page }) => {
      // Check for proper focus management
      await page.keyboard.press('Tab');
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();

      // Verify focus ring styling
      const focusRing = page.locator('.aone-focus-ring:focus');
      if (await focusRing.count() > 0) {
        await expect(focusRing.first()).toBeVisible();
      }

      // Check for proper heading hierarchy
      const h1 = page.locator('h1');
      await expect(h1).toBeVisible();
    });

    test('should support reduced motion preferences', async ({ page }) => {
      // Test with reduced motion preference
      await page.emulateMedia({ reducedMotion: 'reduce' });
      await page.reload();

      // Verify page still functions with reduced motion
      const heroSection = page.locator('text=Transform Kitchen Drawings into');
      await expect(heroSection).toBeVisible();
    });
  });
});
