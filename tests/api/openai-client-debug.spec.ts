import { test, expect } from '@playwright/test';

test.describe('OpenAI Client Debug', () => {
  test('should check OpenAI client initialization', async ({ request }) => {
    console.log('🔍 Debugging OpenAI client initialization...');
    
    // Check health endpoint
    const healthResponse = await request.get('http://localhost:3001/api/health/detailed');
    
    if (healthResponse.ok()) {
      const healthResult = await healthResponse.json();
      console.log('🏥 Health check result:', JSON.stringify(healthResult, null, 2));
      
      // Check OpenAI specific health
      if (healthResult.data && healthResult.data.services && healthResult.data.services.openai) {
        console.log('🤖 OpenAI service health:', JSON.stringify(healthResult.data.services.openai, null, 2));
      }
    } else {
      console.log(`❌ Health check failed: ${healthResponse.status()}`);
    }

    // Try to call a simple OpenAI endpoint to test client availability
    const testResponse = await request.post('http://localhost:3001/api/analysis/test-openai', {
      headers: {
        'Content-Type': 'application/json'
      },
      data: {
        test: true
      }
    });

    console.log(`🧪 OpenAI test response status: ${testResponse.status()}`);
    
    if (testResponse.ok()) {
      const testResult = await testResponse.json();
      console.log('🧪 OpenAI test result:', JSON.stringify(testResult, null, 2));
    } else {
      const errorText = await testResponse.text();
      console.log(`❌ OpenAI test failed: ${errorText}`);
    }
  });

  test('should check environment variables', async () => {
    console.log('🔧 Checking environment variables...');
    
    // Note: We can't directly access process.env in Playwright tests,
    // but we can infer from the health check results
    console.log('Environment variables are checked via health endpoint');
  });
});
