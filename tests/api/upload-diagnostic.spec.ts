import { test, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';

test.describe('Upload Diagnostic Tests', () => {
  const testPdfPath = path.join(process.cwd(), 'tests', 'fixtures', 'kitchen-design-test.pdf');

  test('should upload file successfully and get analysis ID', async ({ request }) => {
    console.log('🔬 Testing file upload functionality...');
    
    const fileBuffer = fs.readFileSync(testPdfPath);
    console.log(`📁 File size: ${fileBuffer.length} bytes`);

    const uploadResponse = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fileBuffer
        },
        useGPT4o: 'true',
        useReasoning: 'true',
        focusOnMaterials: 'true',
        focusOnHardware: 'true',
        enableMultiView: 'true',
        enable3DReconstruction: 'true',
        enableIntelligentMeasurement: 'true',
        enableEnhancedHardwareRecognition: 'true',
        enableMaterialRecognition: 'true',
        enableCostEstimation: 'true',
        promptId: 'kitchen_analysis'
      }
    });

    console.log(`📤 Upload response status: ${uploadResponse.status()}`);
    
    if (!uploadResponse.ok()) {
      const errorText = await uploadResponse.text();
      console.log(`❌ Upload failed: ${errorText}`);
      throw new Error(`Upload failed: ${uploadResponse.status()} - ${errorText}`);
    }

    const uploadResult = await uploadResponse.json();
    console.log(`✅ Upload successful:`, JSON.stringify(uploadResult, null, 2));

    // Validate response structure
    expect(uploadResult).toHaveProperty('success', true);
    expect(uploadResult.data).toHaveProperty('analysisId');
    expect(uploadResult.data.analysisId).toMatch(/^[a-f0-9-]{36}$/); // UUID format

    const analysisId = uploadResult.data.analysisId;
    console.log(`📋 Analysis ID: ${analysisId}`);

    // Check initial status
    const statusResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/status`);
    console.log(`📊 Status response status: ${statusResponse.status()}`);
    
    if (statusResponse.ok()) {
      const statusResult = await statusResponse.json();
      console.log(`📊 Initial status:`, JSON.stringify(statusResult, null, 2));
    } else {
      const errorText = await statusResponse.text();
      console.log(`⚠️ Status check failed: ${errorText}`);
    }
  });

  test('should get upload configuration', async ({ request }) => {
    console.log('🔧 Testing upload configuration endpoint...');
    
    const configResponse = await request.get('http://localhost:3001/api/analysis/upload/config');
    console.log(`📊 Config response status: ${configResponse.status()}`);
    
    expect(configResponse.ok()).toBe(true);
    
    const configResult = await configResponse.json();
    console.log(`⚙️ Upload config:`, JSON.stringify(configResult, null, 2));
    
    expect(configResult).toHaveProperty('success', true);
    expect(configResult.data).toHaveProperty('maxFileSize');
    expect(configResult.data).toHaveProperty('allowedTypes');
    expect(configResult.data.allowedTypes).toContain('application/pdf');
  });

  test('should check analysis queue status', async ({ request }) => {
    console.log('📊 Testing analysis queue status...');
    
    const queueResponse = await request.get('http://localhost:3001/api/analysis/queue/status');
    console.log(`📊 Queue response status: ${queueResponse.status()}`);
    
    if (queueResponse.ok()) {
      const queueResult = await queueResponse.json();
      console.log(`📊 Queue status:`, JSON.stringify(queueResult, null, 2));
    } else {
      const errorText = await queueResponse.text();
      console.log(`⚠️ Queue status failed: ${errorText}`);
    }
  });

  test('should check system health', async ({ request }) => {
    console.log('🏥 Testing system health...');
    
    const healthResponse = await request.get('http://localhost:3001/api/health/detailed');
    console.log(`🏥 Health response status: ${healthResponse.status()}`);
    
    if (healthResponse.ok()) {
      const healthResult = await healthResponse.json();
      console.log(`🏥 System health:`, JSON.stringify(healthResult, null, 2));
    } else {
      const errorText = await healthResponse.text();
      console.log(`⚠️ Health check failed: ${errorText}`);
    }
  });

  test('should test document intelligence service directly', async ({ request }) => {
    console.log('🔍 Testing document intelligence service...');
    
    // First upload a file
    const fileBuffer = fs.readFileSync(testPdfPath);
    
    const uploadResponse = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fileBuffer
        },
        useGPT4o: 'false', // Disable AI analysis for this test
        useReasoning: 'false',
        focusOnMaterials: 'false',
        focusOnHardware: 'false',
        enableMultiView: 'false',
        enable3DReconstruction: 'false',
        enableIntelligentMeasurement: 'false',
        enableEnhancedHardwareRecognition: 'false',
        enableMaterialRecognition: 'false',
        enableCostEstimation: 'false',
        promptId: 'kitchen_analysis'
      }
    });

    if (!uploadResponse.ok()) {
      const errorText = await uploadResponse.text();
      console.log(`❌ Upload failed: ${errorText}`);
      return;
    }

    const uploadResult = await uploadResponse.json();
    console.log(`✅ File uploaded for document intelligence test`);

    // Try document intelligence directly if we can get the file path
    if (uploadResult.data && uploadResult.data.filePath) {
      const docResponse = await request.post('http://localhost:3001/api/document-intelligence/analyze', {
        headers: {
          'Content-Type': 'application/json'
        },
        data: {
          filePath: uploadResult.data.filePath,
          options: {
            enableOcr: true,
            enableAzureFallback: true,
            timeout: 30000
          }
        }
      });

      console.log(`🔍 Document intelligence response status: ${docResponse.status()}`);
      
      if (docResponse.ok()) {
        const docResult = await docResponse.json();
        console.log(`🔍 Document intelligence result:`, JSON.stringify(docResult, null, 2));
      } else {
        const errorText = await docResponse.text();
        console.log(`⚠️ Document intelligence failed: ${errorText}`);
      }
    } else {
      console.log(`⚠️ No file path returned from upload`);
    }
  });
});
