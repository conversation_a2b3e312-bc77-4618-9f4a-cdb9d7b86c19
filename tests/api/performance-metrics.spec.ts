import { test, expect } from '@playwright/test';

const API_BASE_URL = 'http://localhost:3001';

test.describe('Performance Metrics API', () => {
  test.beforeAll(async () => {
    console.log('🚀 Starting Performance Metrics API tests');
  });

  test('should return performance overview', async ({ request }) => {
    const response = await request.get(`${API_BASE_URL}/api/performance/overview`);
    
    expect(response.ok()).toBeTruthy();
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toBeDefined();
    expect(data.data.modelComparison).toBeDefined();
    expect(data.data.summary).toBeDefined();
    expect(data.timestamp).toBeDefined();

    // Verify model comparison structure
    expect(data.data.modelComparison.models).toBeInstanceOf(Array);
    expect(data.data.modelComparison.totalRequests).toBeGreaterThanOrEqual(0);
    expect(data.data.modelComparison.totalCost).toBeGreaterThanOrEqual(0);
    expect(data.data.modelComparison.mostEfficientModel).toBeDefined();

    // Verify summary structure
    expect(data.data.summary.totalRequests).toBeGreaterThanOrEqual(0);
    expect(data.data.summary.totalCost).toBeGreaterThanOrEqual(0);
    expect(data.data.summary.alertCount).toBeGreaterThanOrEqual(0);

    console.log('✅ Performance overview API working correctly');
  });

  test('should return performance overview with different time ranges', async ({ request }) => {
    const timeRanges = ['1h', '24h', '7d', '30d'];
    
    for (const timeRange of timeRanges) {
      const response = await request.get(`${API_BASE_URL}/api/performance/overview?timeRange=${timeRange}`);
      
      expect(response.ok()).toBeTruthy();
      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.modelComparison.timeRange).toBe(timeRange);
      
      console.log(`✅ Performance overview API working for ${timeRange} time range`);
    }
  });

  test('should return model performance comparison', async ({ request }) => {
    const response = await request.get(`${API_BASE_URL}/api/performance/models/comparison`);
    
    expect(response.ok()).toBeTruthy();
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toBeDefined();
    expect(data.data.models).toBeInstanceOf(Array);
    expect(data.data.totalRequests).toBeGreaterThanOrEqual(0);
    expect(data.data.totalCost).toBeGreaterThanOrEqual(0);

    // Verify each model has required properties
    data.data.models.forEach((model: any) => {
      expect(model.modelName).toBeDefined();
      expect(model.totalRequests).toBeGreaterThanOrEqual(0);
      expect(model.successfulRequests).toBeGreaterThanOrEqual(0);
      expect(model.failedRequests).toBeGreaterThanOrEqual(0);
      expect(model.averageResponseTime).toBeGreaterThanOrEqual(0);
      expect(model.totalCost).toBeGreaterThanOrEqual(0);
      expect(model.averageConfidence).toBeGreaterThanOrEqual(0);
      expect(model.errorRate).toBeGreaterThanOrEqual(0);
    });

    console.log('✅ Model comparison API working correctly');
  });

  test('should return usage patterns', async ({ request }) => {
    const response = await request.get(`${API_BASE_URL}/api/performance/usage-patterns`);
    
    expect(response.ok()).toBeTruthy();
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toBeDefined();
    expect(data.data.patterns).toBeInstanceOf(Array);
    expect(data.data.summary).toBeDefined();

    // Verify usage pattern structure
    if (data.data.patterns.length > 0) {
      const pattern = data.data.patterns[0];
      expect(pattern.hour).toBeGreaterThanOrEqual(0);
      expect(pattern.hour).toBeLessThanOrEqual(23);
      expect(pattern.day).toBeDefined();
      expect(pattern.requests).toBeGreaterThanOrEqual(0);
      expect(pattern.averageResponseTime).toBeGreaterThanOrEqual(0);
      expect(pattern.modelDistribution).toBeDefined();
    }

    console.log('✅ Usage patterns API working correctly');
  });

  test('should return cost analysis', async ({ request }) => {
    const response = await request.get(`${API_BASE_URL}/api/performance/cost-analysis`);
    
    expect(response.ok()).toBeTruthy();
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toBeDefined();

    // Verify cost analysis structure
    expect(data.data.dailyCost).toBeGreaterThanOrEqual(0);
    expect(data.data.weeklyCost).toBeGreaterThanOrEqual(0);
    expect(data.data.monthlyCost).toBeGreaterThanOrEqual(0);
    expect(data.data.projectedMonthlyCost).toBeGreaterThanOrEqual(0);
    expect(data.data.costByModel).toBeDefined();
    expect(data.data.costTrend).toBeDefined();
    expect(['INCREASING', 'DECREASING', 'STABLE']).toContain(data.data.costTrend);
    expect(data.data.optimizationSuggestions).toBeInstanceOf(Array);

    console.log('✅ Cost analysis API working correctly');
  });

  test('should return performance alerts', async ({ request }) => {
    const response = await request.get(`${API_BASE_URL}/api/performance/alerts`);
    
    expect(response.ok()).toBeTruthy();
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toBeDefined();
    expect(data.data.alerts).toBeInstanceOf(Array);
    expect(data.data.thresholds).toBeInstanceOf(Array);
    expect(data.data.summary).toBeDefined();

    // Verify alert summary structure
    expect(data.data.summary.total).toBeGreaterThanOrEqual(0);
    expect(data.data.summary.critical).toBeGreaterThanOrEqual(0);
    expect(data.data.summary.high).toBeGreaterThanOrEqual(0);
    expect(data.data.summary.medium).toBeGreaterThanOrEqual(0);
    expect(data.data.summary.low).toBeGreaterThanOrEqual(0);
    expect(data.data.summary.unacknowledged).toBeGreaterThanOrEqual(0);

    // Verify threshold structure
    data.data.thresholds.forEach((threshold: any) => {
      expect(threshold.id).toBeDefined();
      expect(threshold.type).toBeDefined();
      expect(['RESPONSE_TIME', 'ERROR_RATE', 'COST_THRESHOLD', 'USAGE_SPIKE']).toContain(threshold.type);
      expect(threshold.threshold).toBeGreaterThanOrEqual(0);
      expect(typeof threshold.enabled).toBe('boolean');
      expect(threshold.description).toBeDefined();
    });

    console.log('✅ Performance alerts API working correctly');
  });

  test('should export metrics data in CSV format', async ({ request }) => {
    const response = await request.get(`${API_BASE_URL}/api/performance/export?format=CSV&timeRange=24h`);

    expect(response.ok()).toBeTruthy();
    expect(response.status()).toBe(200);

    const contentType = response.headers()['content-type'];
    expect(contentType).toContain('text/csv');

    const csvData = await response.text();
    expect(csvData).toBeDefined();
    expect(csvData.length).toBeGreaterThan(0);

    // Verify CSV structure - check for actual format returned by API
    expect(csvData).toContain('Metric,Value,Timestamp');
    expect(csvData).toContain('Total Requests');
    expect(csvData).toContain('Total Cost');
    expect(csvData).toContain('Most Efficient Model');

    console.log('✅ CSV export API working correctly');
  });

  test('should export metrics data in JSON format', async ({ request }) => {
    const response = await request.get(`${API_BASE_URL}/api/performance/export?format=JSON&timeRange=24h`);

    expect(response.ok()).toBeTruthy();
    expect(response.status()).toBe(200);

    const contentType = response.headers()['content-type'];
    expect(contentType).toContain('application/json');

    const jsonData = await response.json();
    expect(jsonData).toBeDefined();

    // Check for actual JSON structure returned by API
    expect(jsonData.exportedAt).toBeDefined();
    expect(jsonData.timeRange).toBe('24h');
    expect(jsonData.format).toBe('JSON');
    expect(jsonData.data).toBeDefined();
    expect(jsonData.data.summary).toBeDefined();

    console.log('✅ JSON export API working correctly');
  });

  test('should handle invalid time range gracefully', async ({ request }) => {
    const response = await request.get(`${API_BASE_URL}/api/performance/overview?timeRange=invalid`);
    
    expect(response.ok()).toBeTruthy();
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    // Should default to 24h when invalid range provided
    expect(data.data.modelComparison.timeRange).toBe('24h');

    console.log('✅ Invalid time range handled gracefully');
  });

  test('should handle invalid export format gracefully', async ({ request }) => {
    const response = await request.get(`${API_BASE_URL}/api/performance/export?format=INVALID&timeRange=24h`);
    
    expect(response.ok()).toBeTruthy();
    expect(response.status()).toBe(200);
    
    // Should default to JSON when invalid format provided
    const contentType = response.headers()['content-type'];
    expect(contentType).toContain('application/json');

    console.log('✅ Invalid export format handled gracefully');
  });

  test('should validate API response times', async ({ request }) => {
    const endpoints = [
      '/api/performance/overview',
      '/api/performance/models/comparison',
      '/api/performance/usage-patterns',
      '/api/performance/cost-analysis',
      '/api/performance/alerts'
    ];

    for (const endpoint of endpoints) {
      const startTime = Date.now();
      const response = await request.get(`${API_BASE_URL}${endpoint}`);
      const responseTime = Date.now() - startTime;

      expect(response.ok()).toBeTruthy();
      expect(responseTime).toBeLessThan(5000); // Should respond within 5 seconds

      console.log(`✅ ${endpoint} responded in ${responseTime}ms`);
    }
  });

  test.afterAll(async () => {
    console.log('🏁 Performance Metrics API tests completed');
  });
});
