import { test, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

test.describe('Analysis API Endpoints', () => {
  const testPdfPath = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
  const testPngPath = path.join(__dirname, '../fixtures/kitchen-design-test.png');

  test('should upload file and start analysis', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        useGPT4o: 'true',
        useReasoning: 'true',
        focusOnMaterials: 'false',
        focusOnHardware: 'false',
        enableMultiView: 'true',
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data).toHaveProperty('data');
    expect(data.data).toHaveProperty('analysisId');
    expect(data.data).toHaveProperty('status', 'QUEUED');
    expect(data.data).toHaveProperty('message');
    
    // Store analysis ID for subsequent tests
    const analysisId = data.data.analysisId;
    expect(analysisId).toMatch(/^[a-f0-9-]{36}$/); // UUID format
  });

  test('should upload PNG file successfully', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'kitchen-design-test.png',
          mimeType: 'image/png',
          buffer: fs.readFileSync(testPngPath),
        },
        useGPT4o: 'false', // Test GPT-4o-mini
        useReasoning: 'true',
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('analysisId');
  });

  test('should reject invalid file types', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'invalid.txt',
          mimeType: 'text/plain',
          buffer: Buffer.from('This is not a valid kitchen design file'),
        },
      },
    });

    expect(response.status()).toBe(400);
    
    const data = await response.json();
    expect(data).toHaveProperty('success', false);
    expect(data).toHaveProperty('error');
  });

  test('should get queue status', async ({ request }) => {
    const response = await request.get('http://localhost:3001/api/analysis/queue/status');

    expect(response.ok()).toBeTruthy();

    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data).toHaveProperty('data');
    expect(data.data).toHaveProperty('queue');
    expect(data.data.queue).toHaveProperty('queued');
    expect(data.data.queue).toHaveProperty('processing');
    expect(data.data.queue).toHaveProperty('completed');
    expect(data.data.queue).toHaveProperty('maxConcurrent');
  });

  test('should get available prompts', async ({ request }) => {
    const response = await request.get('http://localhost:3001/api/analysis/prompts');
    
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data).toHaveProperty('data');
    expect(data.data).toHaveProperty('prompts');
    expect(Array.isArray(data.data.prompts)).toBeTruthy();

    // Should have at least the default prompts
    expect(data.data.prompts.length).toBeGreaterThan(0);

    // Check prompt structure
    const prompt = data.data.prompts[0];
    expect(prompt).toHaveProperty('id');
    expect(prompt).toHaveProperty('versions');
    expect(Array.isArray(prompt.versions)).toBeTruthy();
  });

  test('should get specific prompt details', async ({ request }) => {
    const response = await request.get('http://localhost:3001/api/analysis/prompts/kitchen_analysis');

    expect(response.ok()).toBeTruthy();

    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data).toHaveProperty('data');
    expect(data.data).toHaveProperty('promptId', 'kitchen_analysis');
    expect(data.data).toHaveProperty('text');
  });

  test('should get default configuration', async ({ request }) => {
    const response = await request.get('http://localhost:3001/api/analysis/config/defaults');
    
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data).toHaveProperty('data');
    expect(data.data).toHaveProperty('config');
    expect(data.data).toHaveProperty('options');
    
    const config = data.data.config;
    expect(config).toHaveProperty('useGPT4o');
    expect(config).toHaveProperty('useReasoning');
    expect(config).toHaveProperty('focusOnMaterials');
    expect(config).toHaveProperty('focusOnHardware');
    expect(config).toHaveProperty('enableMultiView');
  });
});
