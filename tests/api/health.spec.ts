import { test, expect } from '@playwright/test';

test.describe('Health API Endpoints', () => {
  test('should return healthy status from basic health endpoint', async ({ request }) => {
    const response = await request.get('http://localhost:3001/api/health');
    
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data).toHaveProperty('data');
    expect(data.data).toHaveProperty('status', 'healthy');
    expect(data.data).toHaveProperty('timestamp');
    expect(data.data).toHaveProperty('uptime');
    expect(data.data).toHaveProperty('memory');
    expect(data.data).toHaveProperty('environment');
    expect(data.data).toHaveProperty('version');
  });

  test('should return detailed health status with Azure OpenAI configuration', async ({ request }) => {
    const response = await request.get('http://localhost:3001/api/health/detailed');
    
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data).toHaveProperty('data');
    
    const healthData = data.data;
    expect(healthData).toHaveProperty('status', 'healthy');
    expect(healthData).toHaveProperty('services');
    
    // Check OpenAI service configuration
    const openaiService = healthData.services.openai;
    expect(openaiService).toHaveProperty('status', 'configured');
    expect(openaiService).toHaveProperty('configured', true);
    expect(openaiService).toHaveProperty('type', 'azure');
    expect(openaiService).toHaveProperty('endpoint');
    expect(openaiService.endpoint).toContain('openai.azure.com');
    
    // Check queue service
    const queueService = healthData.services.queue;
    expect(queueService).toHaveProperty('status', 'active');
    expect(queueService).toHaveProperty('stats');
    expect(queueService.stats).toHaveProperty('maxConcurrent');
    
    // Check WebSocket service
    const websocketService = healthData.services.websocket;
    expect(websocketService).toHaveProperty('status', 'active');
    expect(websocketService).toHaveProperty('stats');
  });

  test('should return readiness status', async ({ request }) => {
    const response = await request.get('http://localhost:3001/api/health/ready');

    // The readiness check might fail if Azure OpenAI is configured but OPENAI_API_KEY is not set
    // This is expected behavior, so we check the response structure regardless of status
    const data = await response.json();
    expect(data).toHaveProperty('data');
    expect(data.data).toHaveProperty('status');
    expect(data.data).toHaveProperty('checks');
    expect(data.data).toHaveProperty('timestamp');

    // Log the actual response for debugging
    console.log('Readiness check response:', JSON.stringify(data, null, 2));
  });

  test('should return liveness status', async ({ request }) => {
    const response = await request.get('http://localhost:3001/api/health/live');

    expect(response.ok()).toBeTruthy();

    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('status', 'alive');
    expect(data.data).toHaveProperty('timestamp');
  });
});
