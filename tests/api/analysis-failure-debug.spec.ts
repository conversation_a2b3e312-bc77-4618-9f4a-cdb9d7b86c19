import { test, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';

test.describe('Analysis Failure Debug', () => {
  const testPdfPath = path.join(process.cwd(), 'tests', 'fixtures', 'kitchen-design-test.pdf');

  test('should debug analysis failure', async ({ request }) => {
    console.log('🔍 Debugging analysis failure...');
    
    // Step 1: Upload file
    const fileBuffer = fs.readFileSync(testPdfPath);
    const uploadResponse = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fileBuffer
        },
        useGPT4o: 'false', // Disable complex features to isolate issue
        useReasoning: 'false',
        focusOnMaterials: 'false',
        focusOnHardware: 'false',
        enableMultiView: 'false',
        enable3DReconstruction: 'false',
        enableIntelligentMeasurement: 'false',
        enableEnhancedHardwareRecognition: 'false',
        enableMaterialRecognition: 'false',
        enableCostEstimation: 'false',
        promptId: 'kitchen_analysis'
      }
    });

    if (!uploadResponse.ok()) {
      console.log(`❌ Upload failed: ${await uploadResponse.text()}`);
      return;
    }

    const uploadResult = await uploadResponse.json();
    const analysisId = uploadResult.data.analysisId;
    console.log(`✅ Upload successful, Analysis ID: ${analysisId}`);

    // Step 2: Monitor status changes
    let attempts = 0;
    const maxAttempts = 10;
    
    while (attempts < maxAttempts) {
      const statusResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/status`);
      
      if (statusResponse.ok()) {
        const statusResult = await statusResponse.json();
        console.log(`📊 Attempt ${attempts + 1}: Status = ${statusResult.data.status}`);
        
        if (statusResult.data.status === 'FAILED') {
          console.log(`❌ Analysis failed. Full status:`, JSON.stringify(statusResult, null, 2));
          
          // Try to get more details about the failure
          const resultsResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/results`);
          if (resultsResponse.ok()) {
            const resultsData = await resultsResponse.json();
            console.log(`📋 Failure details:`, JSON.stringify(resultsData, null, 2));
          } else {
            console.log(`⚠️ Could not get failure details: ${resultsResponse.status()}`);
          }
          break;
        } else if (statusResult.data.status === 'completed') {
          console.log(`✅ Analysis completed successfully!`);
          break;
        }
      } else {
        console.log(`⚠️ Status check failed: ${statusResponse.status()}`);
      }
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      attempts++;
    }

    // Step 3: Check queue status
    const queueResponse = await request.get('http://localhost:3001/api/analysis/queue/status');
    if (queueResponse.ok()) {
      const queueResult = await queueResponse.json();
      console.log(`📊 Queue status:`, JSON.stringify(queueResult, null, 2));
    }

    // Step 4: Check system health
    const healthResponse = await request.get('http://localhost:3001/api/health/detailed');
    if (healthResponse.ok()) {
      const healthResult = await healthResponse.json();
      console.log(`🏥 System health:`, JSON.stringify(healthResult, null, 2));
    }
  });

  test('should test minimal analysis configuration', async ({ request }) => {
    console.log('🧪 Testing minimal analysis configuration...');
    
    const fileBuffer = fs.readFileSync(testPdfPath);
    const uploadResponse = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fileBuffer
        }
        // No additional configuration - use defaults
      }
    });

    if (!uploadResponse.ok()) {
      console.log(`❌ Minimal upload failed: ${await uploadResponse.text()}`);
      return;
    }

    const uploadResult = await uploadResponse.json();
    console.log(`✅ Minimal upload successful:`, JSON.stringify(uploadResult, null, 2));
  });

  test('should check if analysis queue is processing jobs', async ({ request }) => {
    console.log('⚙️ Checking analysis queue processing...');
    
    // Check initial queue state
    let queueResponse = await request.get('http://localhost:3001/api/analysis/queue/status');
    if (queueResponse.ok()) {
      const initialQueue = await queueResponse.json();
      console.log(`📊 Initial queue state:`, JSON.stringify(initialQueue, null, 2));
    }

    // Upload a simple file
    const fileBuffer = fs.readFileSync(testPdfPath);
    const uploadResponse = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'queue-test.pdf',
          mimeType: 'application/pdf',
          buffer: fileBuffer
        },
        useGPT4o: 'false'
      }
    });

    if (uploadResponse.ok()) {
      const uploadResult = await uploadResponse.json();
      console.log(`✅ File queued: ${uploadResult.data.analysisId}`);
      
      // Check queue state after upload
      queueResponse = await request.get('http://localhost:3001/api/analysis/queue/status');
      if (queueResponse.ok()) {
        const afterUploadQueue = await queueResponse.json();
        console.log(`📊 Queue after upload:`, JSON.stringify(afterUploadQueue, null, 2));
      }
      
      // Wait a bit and check again
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      queueResponse = await request.get('http://localhost:3001/api/analysis/queue/status');
      if (queueResponse.ok()) {
        const laterQueue = await queueResponse.json();
        console.log(`📊 Queue after 5 seconds:`, JSON.stringify(laterQueue, null, 2));
      }
    }
  });
});
