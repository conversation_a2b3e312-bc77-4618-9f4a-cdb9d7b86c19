import { test, expect, Page } from '@playwright/test';

// Test configuration for visual experience system
const TEST_CONFIG = {
  timeouts: {
    animation: 3000,
    transition: 5000,
    particleLoad: 8000,
    reasoningLoad: 10000
  },
  performance: {
    minFPS: 25,
    maxParticles: 15000,
    maxMemoryMB: 512
  }
};

// Helper function to wait for 3D canvas to load
async function waitFor3DCanvas(page: Page, timeout = 10000) {
  await page.waitForSelector('canvas', { timeout });
  await page.waitForFunction(() => {
    const canvas = document.querySelector('canvas');
    return canvas && canvas.width > 0 && canvas.height > 0;
  }, { timeout });
}

// Helper function to check particle system status
async function checkParticleSystem(page: Page) {
  return await page.evaluate(() => {
    const canvas = document.querySelector('canvas');
    if (!canvas) return { active: false, count: 0 };
    
    // Check for WebGL context and particle rendering
    const gl = canvas.getContext('webgl') || canvas.getContext('webgl2');
    return {
      active: !!gl,
      count: 0 // Will be updated by actual particle system
    };
  });
}

// Helper function to monitor performance
async function monitorPerformance(page: Page, duration = 5000) {
  const startTime = Date.now();
  const metrics = [];
  
  while (Date.now() - startTime < duration) {
    const metric = await page.evaluate(() => ({
      fps: (window as any).performanceMetrics?.fps || 0,
      memory: performance.memory ? Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) : 0,
      timestamp: Date.now()
    }));
    metrics.push(metric);
    await page.waitForTimeout(100);
  }
  
  return metrics;
}

test.describe('Visual Experience System - Phase 1 Foundation', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to analysis page with visual experience system
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');
  });

  test.describe('Cinematic 3D Transitions System', () => {
    test('should load cinematic 3D viewer with enhanced camera controls', async ({ page }) => {
      // Upload test image to trigger 3D reconstruction
      const fileInput = page.locator('input[type="file"]');
      await fileInput.setInputFiles('tests/fixtures/kitchen-test.jpg');
      
      // Wait for analysis to complete and 3D view to load
      await page.waitForSelector('[data-testid="cinematic-3d-viewer"]', { timeout: 30000 });
      await waitFor3DCanvas(page);
      
      // Check for cinematic mode badge
      const cinematicBadge = page.locator('text=Cinematic Mode');
      await expect(cinematicBadge).toBeVisible();
      
      // Verify enhanced controls are present
      await expect(page.locator('button:has-text("2D View")')).toBeVisible();
      await expect(page.locator('button:has-text("3D View")')).toBeVisible();
      await expect(page.locator('button:has-text("Overview")')).toBeVisible();
    });

    test('should execute smooth camera transitions between view modes', async ({ page }) => {
      // Setup 3D viewer
      await page.goto('/analysis');
      const fileInput = page.locator('input[type="file"]');
      await fileInput.setInputFiles('tests/fixtures/kitchen-test.jpg');
      await page.waitForSelector('[data-testid="cinematic-3d-viewer"]', { timeout: 30000 });
      await waitFor3DCanvas(page);
      
      // Test 2D to 3D transition
      const view3DButton = page.locator('button:has-text("3D View")');
      await view3DButton.click();
      
      // Wait for transition to complete
      await page.waitForSelector('text=Transitioning...', { state: 'visible' });
      await page.waitForSelector('text=Transitioning...', { state: 'hidden', timeout: TEST_CONFIG.timeouts.transition });
      
      // Verify 3D view is active
      await expect(view3DButton).toBeDisabled();
      
      // Test return to 2D
      const view2DButton = page.locator('button:has-text("2D View")');
      await view2DButton.click();
      await page.waitForSelector('text=Transitioning...', { state: 'hidden', timeout: TEST_CONFIG.timeouts.transition });
      
      // Verify 2D view is active
      await expect(view2DButton).toBeDisabled();
    });

    test('should support different transition presets', async ({ page }) => {
      await page.goto('/analysis');
      const fileInput = page.locator('input[type="file"]');
      await fileInput.setInputFiles('tests/fixtures/kitchen-test.jpg');
      await page.waitForSelector('[data-testid="visual-experience-system"]', { timeout: 30000 });
      
      // Open settings panel
      await page.locator('button:has-text("Settings")').click();
      
      // Test different presets
      const presets = ['professional', 'dramatic', 'subtle'];
      for (const preset of presets) {
        await page.locator(`button:has-text("${preset}")`).click();
        await page.waitForTimeout(500);
        
        // Verify preset is selected
        const presetButton = page.locator(`button:has-text("${preset}")`);
        await expect(presetButton).toHaveClass(/bg-aone-sage|variant-default/);
      }
    });

    test('should handle cabinet focus transitions', async ({ page }) => {
      await page.goto('/analysis');
      const fileInput = page.locator('input[type="file"]');
      await fileInput.setInputFiles('tests/fixtures/kitchen-test.jpg');
      await page.waitForSelector('[data-testid="cinematic-3d-viewer"]', { timeout: 30000 });
      await waitFor3DCanvas(page);
      
      // Wait for cabinets to be rendered
      await page.waitForTimeout(2000);
      
      // Click on a cabinet in 3D view
      const canvas = page.locator('canvas').first();
      await canvas.click({ position: { x: 300, y: 300 } });
      
      // Wait for focus transition
      await page.waitForTimeout(TEST_CONFIG.timeouts.transition);
      
      // Verify cabinet details are shown
      await expect(page.locator('[data-testid="cabinet-details"]')).toBeVisible({ timeout: 5000 });
    });
  });

  test.describe('Particle AI Visualization Engine', () => {
    test('should initialize particle system with WebGL support', async ({ page }) => {
      await page.goto('/analysis');
      
      // Navigate to particle visualization mode
      await page.locator('button:has-text("AI Particles")').click();
      await page.waitForSelector('canvas', { timeout: TEST_CONFIG.timeouts.particleLoad });
      
      // Check particle system initialization
      const particleStatus = await checkParticleSystem(page);
      expect(particleStatus.active).toBe(true);
      
      // Verify particle controls are visible
      await expect(page.locator('text=Particles:')).toBeVisible();
      await expect(page.locator('text=FPS:')).toBeVisible();
    });

    test('should visualize AI process states with different particle patterns', async ({ page }) => {
      await page.goto('/analysis');
      
      // Start analysis to trigger AI process visualization
      const fileInput = page.locator('input[type="file"]');
      await fileInput.setInputFiles('tests/fixtures/kitchen-test.jpg');
      
      // Switch to particle view
      await page.locator('button:has-text("AI Particles")').click();
      await waitFor3DCanvas(page);
      
      // Wait for AI analysis to start and particles to appear
      await page.waitForTimeout(3000);
      
      // Check for particle activity during analysis
      const particleMetrics = await page.evaluate(() => {
        const canvas = document.querySelector('canvas');
        const gl = canvas?.getContext('webgl') || canvas?.getContext('webgl2');
        return {
          hasWebGL: !!gl,
          canvasSize: canvas ? { width: canvas.width, height: canvas.height } : null
        };
      });
      
      expect(particleMetrics.hasWebGL).toBe(true);
      expect(particleMetrics.canvasSize).toBeTruthy();
    });

    test('should adjust particle intensity based on settings', async ({ page }) => {
      await page.goto('/analysis');
      await page.locator('button:has-text("AI Particles")').click();
      await waitFor3DCanvas(page);
      
      // Open settings
      await page.locator('button:has-text("Settings")').click();
      await page.locator('text=Particles').click();
      
      // Test different intensity levels
      const intensities = ['low', 'medium', 'high'];
      for (const intensity of intensities) {
        await page.locator(`button:has-text("${intensity}")`).click();
        await page.waitForTimeout(1000);
        
        // Verify intensity setting is applied
        const intensityButton = page.locator(`button:has-text("${intensity}")`);
        await expect(intensityButton).toHaveClass(/bg-aone-sage|variant-default/);
      }
    });

    test('should maintain performance within acceptable limits', async ({ page }) => {
      await page.goto('/analysis');
      await page.locator('button:has-text("AI Particles")').click();
      await waitFor3DCanvas(page);
      
      // Monitor performance for 5 seconds
      const metrics = await monitorPerformance(page, 5000);
      
      // Calculate average FPS
      const avgFPS = metrics.reduce((sum, m) => sum + m.fps, 0) / metrics.length;
      const maxMemory = Math.max(...metrics.map(m => m.memory));
      
      // Performance assertions
      expect(avgFPS).toBeGreaterThan(TEST_CONFIG.performance.minFPS);
      expect(maxMemory).toBeLessThan(TEST_CONFIG.performance.maxMemoryMB);
    });
  });

  test.describe('Interactive Reasoning Trees', () => {
    test('should load reasoning tree visualization with GPT-o1 integration', async ({ page }) => {
      await page.goto('/analysis');
      
      // Start analysis to generate reasoning data
      const fileInput = page.locator('input[type="file"]');
      await fileInput.setInputFiles('tests/fixtures/kitchen-test.jpg');
      
      // Wait for analysis to complete
      await page.waitForSelector('text=Analysis completed', { timeout: 60000 });
      
      // Switch to reasoning tree view
      await page.locator('button:has-text("Reasoning Tree")').click();
      await page.waitForSelector('[data-testid="reasoning-tree"]', { timeout: TEST_CONFIG.timeouts.reasoningLoad });
      
      // Verify reasoning tree components
      await expect(page.locator('.react-flow')).toBeVisible();
      await expect(page.locator('text=Interactive Reasoning Tree')).toBeVisible();
    });

    test('should display reasoning steps as interactive nodes', async ({ page }) => {
      await page.goto('/analysis');
      const fileInput = page.locator('input[type="file"]');
      await fileInput.setInputFiles('tests/fixtures/kitchen-test.jpg');
      
      // Wait for analysis and switch to reasoning view
      await page.waitForSelector('text=Analysis completed', { timeout: 60000 });
      await page.locator('button:has-text("Reasoning Tree")').click();
      await page.waitForSelector('[data-testid="reasoning-tree"]', { timeout: TEST_CONFIG.timeouts.reasoningLoad });
      
      // Check for reasoning step nodes
      const stepNodes = page.locator('[data-testid="reasoning-step-node"]');
      await expect(stepNodes.first()).toBeVisible({ timeout: 10000 });
      
      // Test node interaction
      await stepNodes.first().click();
      await expect(page.locator('text=Step Details')).toBeVisible();
    });

    test('should support playback controls for reasoning chain', async ({ page }) => {
      await page.goto('/analysis');
      const fileInput = page.locator('input[type="file"]');
      await fileInput.setInputFiles('tests/fixtures/kitchen-test.jpg');
      
      await page.waitForSelector('text=Analysis completed', { timeout: 60000 });
      await page.locator('button:has-text("Reasoning Tree")').click();
      await page.waitForSelector('[data-testid="reasoning-tree"]', { timeout: TEST_CONFIG.timeouts.reasoningLoad });
      
      // Test playback controls
      const playButton = page.locator('button[aria-label="Play"]').or(page.locator('button:has([data-testid="play-icon"])'));
      if (await playButton.isVisible()) {
        await playButton.click();
        
        // Wait for playback to start
        await page.waitForTimeout(1000);
        
        // Check for pause button (indicates playback is active)
        const pauseButton = page.locator('button[aria-label="Pause"]').or(page.locator('button:has([data-testid="pause-icon"])'));
        await expect(pauseButton).toBeVisible({ timeout: 5000 });
      }
    });

    test('should handle GPT-o1 enhanced reasoning steps', async ({ page }) => {
      await page.goto('/analysis');
      const fileInput = page.locator('input[type="file"]');
      await fileInput.setInputFiles('tests/fixtures/kitchen-test.jpg');
      
      await page.waitForSelector('text=Analysis completed', { timeout: 60000 });
      await page.locator('button:has-text("Reasoning Tree")').click();
      await page.waitForSelector('[data-testid="reasoning-tree"]', { timeout: TEST_CONFIG.timeouts.reasoningLoad });
      
      // Look for GPT-o1 enhanced steps
      const gptO1Badge = page.locator('text=GPT-o1');
      if (await gptO1Badge.isVisible()) {
        await gptO1Badge.click();
        
        // Verify enhanced reasoning details
        await expect(page.locator('text=Enhanced with GPT-o1')).toBeVisible();
      }
    });
  });

  test.describe('Integrated Visual Experience', () => {
    test('should load integrated view with all systems active', async ({ page }) => {
      await page.goto('/analysis');
      
      // Switch to integrated view
      await page.locator('button:has-text("Integrated View")').click();
      await page.waitForTimeout(3000);
      
      // Verify all components are present
      await expect(page.locator('[data-testid="cinematic-3d-viewer"]')).toBeVisible({ timeout: 15000 });
      await expect(page.locator('canvas')).toHaveCount(2); // 3D viewer + particle system
      await expect(page.locator('.react-flow')).toBeVisible({ timeout: 10000 });
    });

    test('should maintain performance in integrated mode', async ({ page }) => {
      await page.goto('/analysis');
      await page.locator('button:has-text("Integrated View")').click();
      await page.waitForTimeout(5000);
      
      // Monitor performance in integrated mode
      const metrics = await monitorPerformance(page, 5000);
      const avgFPS = metrics.reduce((sum, m) => sum + m.fps, 0) / metrics.length;
      
      // Should maintain reasonable performance even with all systems active
      expect(avgFPS).toBeGreaterThan(20); // Slightly lower threshold for integrated mode
    });

    test('should sync animations across systems', async ({ page }) => {
      await page.goto('/analysis');
      const fileInput = page.locator('input[type="file"]');
      await fileInput.setInputFiles('tests/fixtures/kitchen-test.jpg');
      
      await page.locator('button:has-text("Integrated View")').click();
      await page.waitForTimeout(5000);
      
      // Enable animation sync in settings
      await page.locator('button:has-text("Settings")').click();
      await page.locator('text=Performance').click();
      
      // Verify sync settings are available
      await expect(page.locator('text=Performance Mode')).toBeVisible();
    });

    test('should handle fullscreen mode transitions', async ({ page }) => {
      await page.goto('/analysis');
      await page.locator('button:has-text("Integrated View")').click();
      
      // Enter fullscreen
      const fullscreenButton = page.locator('button[aria-label="Fullscreen"]').or(page.locator('button:has([data-testid="maximize-icon"])'));
      await fullscreenButton.click();
      
      // Verify fullscreen layout
      await expect(page.locator('.fixed.inset-0')).toBeVisible();
      
      // Exit fullscreen (ESC key or click outside)
      await page.keyboard.press('Escape');
      await page.waitForTimeout(1000);
    });
  });

  test.describe('Cross-browser Compatibility', () => {
    ['chromium', 'firefox', 'webkit'].forEach(browserName => {
      test(`should work correctly in ${browserName}`, async ({ page, browserName: currentBrowser }) => {
        test.skip(currentBrowser !== browserName, `Skipping ${browserName} test in ${currentBrowser}`);
        
        await page.goto('/analysis');
        
        // Test basic functionality
        await page.locator('button:has-text("AI Particles")').click();
        await waitFor3DCanvas(page);
        
        const particleStatus = await checkParticleSystem(page);
        expect(particleStatus.active).toBe(true);
        
        // Test WebGL support
        const webglSupport = await page.evaluate(() => {
          const canvas = document.createElement('canvas');
          const gl = canvas.getContext('webgl') || canvas.getContext('webgl2');
          return !!gl;
        });
        
        expect(webglSupport).toBe(true);
      });
    });
  });

  test.describe('Error Handling and Resilience', () => {
    test('should gracefully handle WebGL initialization failures', async ({ page }) => {
      // Disable WebGL to test fallback
      await page.addInitScript(() => {
        const originalGetContext = HTMLCanvasElement.prototype.getContext;
        HTMLCanvasElement.prototype.getContext = function(contextType: string, ...args: any[]) {
          if (contextType === 'webgl' || contextType === 'webgl2') {
            return null;
          }
          return originalGetContext.call(this, contextType, ...args);
        };
      });
      
      await page.goto('/analysis');
      await page.locator('button:has-text("AI Particles")').click();
      
      // Should show fallback message or disable particle system gracefully
      await expect(page.locator('text=WebGL not supported').or(page.locator('text=Particle system disabled'))).toBeVisible({ timeout: 10000 });
    });

    test('should handle missing analysis data gracefully', async ({ page }) => {
      await page.goto('/analysis');
      
      // Try to access reasoning tree without analysis data
      await page.locator('button:has-text("Reasoning Tree")').click();
      
      // Should show appropriate message
      await expect(page.locator('text=No analysis data available')).toBeVisible();
    });
  });
});

// Performance benchmark test
test.describe('Performance Benchmarks', () => {
  test('should meet performance targets under load', async ({ page }) => {
    await page.goto('/analysis');
    
    // Upload multiple files to stress test
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles([
      'tests/fixtures/kitchen-test.jpg',
      'tests/fixtures/kitchen-test-2.jpg'
    ]);
    
    // Switch to integrated view for maximum load
    await page.locator('button:has-text("Integrated View")').click();
    await page.waitForTimeout(10000);
    
    // Monitor performance under load
    const metrics = await monitorPerformance(page, 10000);
    const avgFPS = metrics.reduce((sum, m) => sum + m.fps, 0) / metrics.length;
    const maxMemory = Math.max(...metrics.map(m => m.memory));
    
    // Performance targets
    expect(avgFPS).toBeGreaterThan(15); // Minimum acceptable FPS under load
    expect(maxMemory).toBeLessThan(1024); // Maximum memory usage in MB
    
    console.log(`Performance Results: Avg FPS: ${avgFPS.toFixed(2)}, Max Memory: ${maxMemory}MB`);
  });
});
