import { test, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { TestHelpers } from '../utils/test-helpers';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Enhanced Azure OpenAI Integration Tests
 * Based on advanced testing patterns from archived A.One Kitchen projects
 */
test.describe('Enhanced Azure OpenAI Integration', () => {
  const testPdfPath = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
  const testLargePdfPath = path.join(__dirname, '../fixtures/kitchen-design-large.pdf');
  
  // Test configuration matrix for comprehensive coverage
  const testConfigurations = [
    {
      name: 'GPT-4o Standard Analysis',
      config: { useGPT4o: true, useReasoning: false, focusOnMaterials: false, focusOnHardware: false }
    },
    {
      name: 'GPT-4o with Reasoning',
      config: { useGPT4o: true, useReasoning: true, focusOnMaterials: false, focusOnHardware: false }
    },
    {
      name: 'GPT-4o-mini with Material Focus',
      config: { useGPT4o: false, useReasoning: true, focusOnMaterials: true, focusOnHardware: false }
    },
    {
      name: 'GPT-4o with Hardware Focus',
      config: { useGPT4o: true, useReasoning: true, focusOnMaterials: false, focusOnHardware: true }
    },
    {
      name: 'Multi-view Analysis',
      config: { useGPT4o: true, useReasoning: true, enableMultiView: true }
    }
  ];

  test.beforeEach(async ({ page }) => {
    // Verify test files exist
    expect(fs.existsSync(testPdfPath)).toBeTruthy();
    
    // Navigate to analysis page
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');
  });

  // Test each configuration with comprehensive validation
  for (const testConfig of testConfigurations) {
    test(`should complete ${testConfig.name} successfully`, async ({ page, request }) => {
      console.log(`Testing configuration: ${testConfig.name}`);
      
      // Upload file
      const fileInput = page.locator('input[type="file"]');
      await fileInput.setInputFiles(testPdfPath);
      
      // Wait for file validation with enhanced timeout
      const helpers = new TestHelpers(page);
      await helpers.waitForFileValidation(30000);
      
      // Configure analysis settings
      await configureAnalysisSettings(page, testConfig.config);
      
      // Start analysis
      const analyzeButton = page.locator('button:has-text("Analyze"), button:has-text("Start")').first();
      await analyzeButton.click();
      
      // Monitor progress with enhanced validation
      const analysisId = await monitorAnalysisProgress(page, testConfig.name);
      
      // Validate results with configuration-specific checks
      await validateAnalysisResults(page, request, analysisId, testConfig);
      
      console.log(`✅ ${testConfig.name} completed successfully`);
    });
  }

  test('should handle concurrent analyses efficiently', async ({ page, request, context }) => {
    const concurrentAnalyses = 3;
    const analysisPromises: Promise<string>[] = [];
    
    // Start multiple analyses concurrently
    for (let i = 0; i < concurrentAnalyses; i++) {
      const newPage = await context.newPage();
      analysisPromises.push(startConcurrentAnalysis(newPage, request, i));
    }
    
    // Wait for all analyses to complete
    const analysisIds = await Promise.all(analysisPromises);
    
    // Validate all results
    for (const analysisId of analysisIds) {
      await validateConcurrentAnalysisResult(request, analysisId);
    }
    
    console.log(`✅ Concurrent analyses completed: ${analysisIds.length}`);
  });

  test('should demonstrate adaptive retry and error recovery', async ({ page, request }) => {
    // This test simulates network issues and validates retry mechanisms
    
    // Intercept API calls to simulate intermittent failures
    await page.route('**/api/analysis/**', async (route) => {
      const url = route.request().url();
      
      // Simulate 50% failure rate for the first few requests
      if (Math.random() < 0.5 && !url.includes('status')) {
        await route.abort('failed');
        return;
      }
      
      await route.continue();
    });
    
    // Upload and start analysis
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testPdfPath);
    
    const helpers = new TestHelpers(page);
    await helpers.waitForFileValidation(30000);
    
    const analyzeButton = page.locator('button:has-text("Analyze"), button:has-text("Start")').first();
    await analyzeButton.click();
    
    // Monitor for retry attempts and eventual success
    let retryAttempts = 0;
    const maxRetries = 5;
    
    while (retryAttempts < maxRetries) {
      try {
        await page.waitForSelector('[data-testid="analysis-complete"]', { timeout: 30000 });
        break;
      } catch (error) {
        retryAttempts++;
        console.log(`Retry attempt ${retryAttempts} for error recovery test`);
        
        if (retryAttempts >= maxRetries) {
          throw new Error('Analysis failed to complete after maximum retries');
        }
        
        // Wait before next attempt
        await page.waitForTimeout(2000);
      }
    }
    
    console.log(`✅ Error recovery test completed after ${retryAttempts} retries`);
  });

  test('should validate confidence scoring accuracy', async ({ page, request }) => {
    // Upload a high-quality test file for confidence validation
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testPdfPath);
    
    const helpers = new TestHelpers(page);
    await helpers.waitForFileValidation(30000);
    
    // Configure for high-confidence analysis
    await configureAnalysisSettings(page, {
      useGPT4o: true,
      useReasoning: true,
      focusOnMaterials: true,
      focusOnHardware: true
    });
    
    const analyzeButton = page.locator('button:has-text("Analyze"), button:has-text("Start")').first();
    await analyzeButton.click();
    
    const analysisId = await monitorAnalysisProgress(page, 'Confidence Validation');
    
    // Get detailed results
    const resultsResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/results`);
    expect(resultsResponse.ok()).toBeTruthy();
    
    const results = await resultsResponse.json();
    
    // Validate confidence scores are within expected ranges
    expect(results.data.confidence.overall).toBeGreaterThan(0.7);
    expect(results.data.confidence.cabinets).toBeGreaterThan(0.8);
    expect(results.data.confidence.measurements).toBeGreaterThan(0.6);
    
    // Validate confidence scoring methodology
    expect(results.data.confidence).toHaveProperty('materials');
    expect(results.data.confidence).toHaveProperty('hardware');
    
    console.log('✅ Confidence scoring validation completed');
  });

  test('should handle large PDF files efficiently', async ({ page, request }) => {
    if (!fs.existsSync(testLargePdfPath)) {
      test.skip('Large PDF test file not available');
      return;
    }
    
    // Upload large file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testLargePdfPath);
    
    // Wait for file processing (longer timeout for large files)
    await page.waitForSelector('[data-testid="file-validated"]', { timeout: 30000 });
    
    // Start analysis with optimized settings for large files
    await configureAnalysisSettings(page, {
      useGPT4o: false, // Use GPT-4o-mini for efficiency
      useReasoning: false,
      enableMultiView: true
    });
    
    const analyzeButton = page.locator('button:has-text("Analyze"), button:has-text("Start")').first();
    await analyzeButton.click();
    
    // Monitor with extended timeout for large file processing
    const analysisId = await monitorAnalysisProgress(page, 'Large PDF Analysis', 180000);
    
    // Validate results
    const resultsResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/results`);
    expect(resultsResponse.ok()).toBeTruthy();
    
    const results = await resultsResponse.json();
    expect(results.data).toHaveProperty('measurements');
    expect(results.data).toHaveProperty('cabinets');
    
    console.log('✅ Large PDF processing completed successfully');
  });

  // Helper function to configure analysis settings
  async function configureAnalysisSettings(page: any, config: any) {
    // Open advanced settings if available
    const advancedToggle = page.locator('[data-testid="advanced-settings-toggle"]');
    if (await advancedToggle.isVisible()) {
      await advancedToggle.click();
    }
    
    // Configure model selection
    if (config.useGPT4o !== undefined) {
      const modelSelect = page.locator('[data-testid="model-select"]');
      if (await modelSelect.isVisible()) {
        await modelSelect.selectOption(config.useGPT4o ? 'gpt-4o' : 'gpt-4o-mini');
      }
    }
    
    // Configure reasoning
    if (config.useReasoning !== undefined) {
      const reasoningToggle = page.locator('[data-testid="reasoning-toggle"]');
      if (await reasoningToggle.isVisible()) {
        if (config.useReasoning) {
          await reasoningToggle.check();
        } else {
          await reasoningToggle.uncheck();
        }
      }
    }
    
    // Configure focus areas
    if (config.focusOnMaterials) {
      const materialsToggle = page.locator('[data-testid="materials-focus-toggle"]');
      if (await materialsToggle.isVisible()) {
        await materialsToggle.check();
      }
    }
    
    if (config.focusOnHardware) {
      const hardwareToggle = page.locator('[data-testid="hardware-focus-toggle"]');
      if (await hardwareToggle.isVisible()) {
        await hardwareToggle.check();
      }
    }
    
    if (config.enableMultiView) {
      const multiViewToggle = page.locator('[data-testid="multiview-toggle"]');
      if (await multiViewToggle.isVisible()) {
        await multiViewToggle.check();
      }
    }
  }

  // Helper function to monitor analysis progress
  async function monitorAnalysisProgress(page: any, testName: string, timeout: number = 120000): Promise<string> {
    let analysisId = '';

    // Try multiple methods to get analysis ID
    console.log(`${testName}: Waiting for analysis to start...`);

    // Method 1: Check localStorage
    try {
      await page.waitForFunction(() => {
        return window.localStorage.getItem('currentAnalysisId') !== null;
      }, { timeout: 5000 });

      analysisId = await page.evaluate(() => window.localStorage.getItem('currentAnalysisId'));
      console.log(`${testName}: Analysis ID from localStorage: ${analysisId}`);
    } catch (error) {
      console.log(`${testName}: localStorage method failed, trying alternatives...`);

      // Method 2: Check for progress indicators and extract ID from API calls
      try {
        await page.waitForSelector('[data-testid="analysis-progress"], .analysis-progress, [class*="progress"]', { timeout: 10000 });

        // Try to extract ID from network requests or page state
        analysisId = await page.evaluate(() => {
          // Check various possible storage locations
          const sources = [
            () => window.localStorage.getItem('analysisId'),
            () => window.sessionStorage.getItem('currentAnalysisId'),
            () => window.sessionStorage.getItem('analysisId'),
            () => (window as any).currentAnalysisId,
            () => (window as any).analysisId,
          ];

          for (const source of sources) {
            try {
              const id = source();
              if (id) return id;
            } catch (e) {
              // Continue to next source
            }
          }

          // Generate a fallback ID based on timestamp
          return `test_analysis_${Date.now()}`;
        });

        console.log(`${testName}: Analysis ID from alternative method: ${analysisId}`);
      } catch (error2) {
        // Method 3: Use a fallback ID and monitor by other means
        analysisId = `fallback_analysis_${Date.now()}`;
        console.log(`${testName}: Using fallback analysis ID: ${analysisId}`);
      }
    }
    
    // Monitor progress updates
    const startTime = Date.now();
    let lastProgress = 0;
    let completionDetected = false;

    while (Date.now() - startTime < timeout && !completionDetected) {
      try {
        // Check for completion using multiple selectors
        const completionSelectors = [
          '[data-testid="analysis-complete"]',
          '[data-testid="analysis-results"]',
          '.analysis-complete',
          '.analysis-results',
          '[class*="complete"]',
          '[class*="results"]'
        ];

        for (const selector of completionSelectors) {
          if (await page.locator(selector).isVisible().catch(() => false)) {
            console.log(`${testName}: Analysis completed (detected via ${selector})`);
            completionDetected = true;
            break;
          }
        }

        if (completionDetected) {
          return analysisId;
        }

        // Check for error states
        const errorSelectors = [
          '[data-testid="analysis-error"]',
          '.error',
          '[class*="error"]',
          'text=/error/i',
          'text=/failed/i'
        ];

        for (const selector of errorSelectors) {
          if (await page.locator(selector).isVisible().catch(() => false)) {
            const errorText = await page.locator(selector).textContent().catch(() => 'Unknown error');
            throw new Error(`${testName}: Analysis failed with error: ${errorText}`);
          }
        }

        // Check progress using multiple selectors
        const progressSelectors = [
          '[data-testid="analysis-progress"]',
          '.analysis-progress',
          '[class*="progress"]',
          '[data-progress]'
        ];

        for (const selector of progressSelectors) {
          const progressElement = page.locator(selector);
          if (await progressElement.isVisible().catch(() => false)) {
            const progressText = await progressElement.textContent().catch(() => '');
            const progressMatch = progressText?.match(/(\d+)%/);
            if (progressMatch) {
              const currentProgress = parseInt(progressMatch[1]);
              if (currentProgress > lastProgress) {
                console.log(`${testName}: Progress ${currentProgress}%`);
                lastProgress = currentProgress;
              }
            }
            break;
          }
        }

        // Check if analysis button is still disabled (indicates processing)
        const analyzeButton = page.locator('button:has-text("Analyze"), button:has-text("Start")').first();
        const isDisabled = await analyzeButton.getAttribute('disabled').catch(() => null);
        if (isDisabled !== null) {
          console.log(`${testName}: Analysis in progress (button disabled)`);
        }

        await page.waitForTimeout(3000); // Longer interval for better performance

      } catch (error) {
        console.log(`${testName}: Monitoring error:`, error);
        await page.waitForTimeout(2000);
      }
    }
    
    throw new Error(`${testName}: Analysis did not complete within timeout`);
  }

  // Helper function for concurrent analysis
  async function startConcurrentAnalysis(page: any, request: any, index: number): Promise<string> {
    const helpers = new TestHelpers(page);
    await helpers.navigateToPage('/analysis');

    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testPdfPath);

    await helpers.waitForFileValidation(30000);
    
    const analyzeButton = page.locator('button:has-text("Analyze"), button:has-text("Start")').first();
    await analyzeButton.click();
    
    return await monitorAnalysisProgress(page, `Concurrent Analysis ${index + 1}`);
  }

  // Helper function to validate analysis results
  async function validateAnalysisResults(page: any, request: any, analysisId: string, testConfig: any) {
    const resultsResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/results`);
    expect(resultsResponse.ok()).toBeTruthy();
    
    const results = await resultsResponse.json();
    
    // Basic validation
    expect(results.data).toHaveProperty('measurements');
    expect(results.data).toHaveProperty('cabinets');
    expect(results.data).toHaveProperty('confidence');
    
    // Configuration-specific validation
    if (testConfig.config.useGPT4o) {
      expect(results.data.model).toContain('gpt-4o');
    } else {
      expect(results.data.model).toContain('gpt-4o-mini');
    }
    
    if (testConfig.config.focusOnMaterials) {
      expect(results.data).toHaveProperty('materials');
      expect(results.data.materials).toBeDefined();
    }
    
    if (testConfig.config.focusOnHardware) {
      expect(results.data).toHaveProperty('hardware');
      expect(results.data.hardware).toBeDefined();
    }
  }

  // Helper function to validate concurrent analysis results
  async function validateConcurrentAnalysisResult(request: any, analysisId: string) {
    const resultsResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/results`);
    expect(resultsResponse.ok()).toBeTruthy();
    
    const results = await resultsResponse.json();
    expect(results.data).toHaveProperty('measurements');
    expect(results.data).toHaveProperty('cabinets');
  }
});
