/**
 * Analysis Routes Refactoring Tests - Phase 3.3 Validation
 * 
 * These tests verify that the refactored Analysis Routes maintain ~97-99% success rate
 * and full backward compatibility after the modular architecture implementation.
 * 
 * Refactoring: 545 lines → 4 specialized handlers (~120-150 lines each)
 * Architecture: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ConfigHandler
 */

import { test, expect } from '@playwright/test';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

test.describe('Analysis Routes - Phase 3.3 Refactoring Validation', () => {
  const testPdfPath = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
  const testImagePath = path.join(__dirname, '../fixtures/kitchen-design-test.png');

  test.beforeEach(async () => {
    // Ensure test files exist
    if (!fs.existsSync(testPdfPath)) {
      console.log('⚠️ Test PDF not found, some tests may be skipped');
    }
  });

  test('should maintain backward compatibility for upload endpoint', async ({ request }) => {
    console.log('🧪 Testing Phase 3.3 analysis routes refactoring backward compatibility...');
    console.log('📊 Refactoring: 545 lines → 4 specialized handlers (~120-150 lines each)');
    
    const response = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        useGPT4o: 'true',
        useReasoning: 'false'
      },
      timeout: 60000
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('analysisId');
    expect(data.data).toHaveProperty('status', 'QUEUED');
    expect(data.data).toHaveProperty('config');
    
    console.log('✅ Upload endpoint maintains backward compatibility');
  });

  test('should maintain backward compatibility for enhanced analysis endpoint', async ({ request }) => {
    console.log('🧪 Testing enhanced analysis endpoint...');
    
    const response = await request.post('http://localhost:3001/api/analysis/enhanced', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        }
      },
      timeout: 120000
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('analysisId');
    expect(data.data).toHaveProperty('status', 'PROCESSING');
    expect(data.data.services).toHaveProperty('promptOptimization', true);
    expect(data.data.services).toHaveProperty('reasoningChain', true);
    expect(data.data.services).toHaveProperty('enhancedPdfProcessing', true);
    expect(data.data.services).toHaveProperty('abTesting', true);
    
    console.log('✅ Enhanced analysis endpoint working correctly');
  });

  test('should maintain backward compatibility for 3D reconstruction endpoint', async ({ request }) => {
    console.log('🧪 Testing 3D reconstruction endpoint...');
    
    const response = await request.post('http://localhost:3001/api/analysis/3d-reconstruction', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        spatialResolution: 'MEDIUM', // Use medium for faster testing
        enableDepthEstimation: 'true'
      },
      timeout: 90000
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('analysisId');
    expect(data.data).toHaveProperty('reconstruction3D');
    expect(data.data).toHaveProperty('confidence');
    expect(data.data).toHaveProperty('processingTime');
    
    console.log('✅ 3D reconstruction endpoint working correctly');
  });

  test('should maintain backward compatibility for status endpoints', async ({ request }) => {
    console.log('🧪 Testing status endpoints...');
    
    // First create an analysis
    const uploadResponse = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        useGPT4o: 'false', // Use faster processing for testing
        useReasoning: 'false'
      },
      timeout: 30000
    });

    expect(uploadResponse.ok()).toBeTruthy();
    const uploadData = await uploadResponse.json();
    const analysisId = uploadData.data.analysisId;

    // Test status endpoint
    const statusResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/status`);
    expect(statusResponse.ok()).toBeTruthy();
    
    const statusData = await statusResponse.json();
    expect(statusData).toHaveProperty('success', true);
    expect(statusData.data).toHaveProperty('analysisId', analysisId);
    expect(statusData.data).toHaveProperty('status');
    expect(statusData.data).toHaveProperty('progress');
    expect(statusData.data).toHaveProperty('createdAt');
    
    console.log('✅ Status endpoint working correctly');
  });

  test('should maintain backward compatibility for config endpoints', async ({ request }) => {
    console.log('🧪 Testing config endpoints...');
    
    // Test default config endpoint
    const defaultConfigResponse = await request.get('http://localhost:3001/api/analysis/config/defaults');
    expect(defaultConfigResponse.ok()).toBeTruthy();
    
    const defaultConfigData = await defaultConfigResponse.json();
    expect(defaultConfigData).toHaveProperty('success', true);
    expect(defaultConfigData.data).toHaveProperty('config');
    expect(defaultConfigData.data).toHaveProperty('description');
    expect(defaultConfigData.data).toHaveProperty('options');
    
    // Test prompts endpoint
    const promptsResponse = await request.get('http://localhost:3001/api/analysis/prompts');
    expect(promptsResponse.ok()).toBeTruthy();
    
    const promptsData = await promptsResponse.json();
    expect(promptsData).toHaveProperty('success', true);
    expect(promptsData.data).toHaveProperty('prompts');
    expect(promptsData.data).toHaveProperty('total');
    
    console.log('✅ Config endpoints working correctly');
  });

  test('should provide new enhanced endpoints from modular handlers', async ({ request }) => {
    console.log('🧪 Testing new enhanced endpoints...');
    
    // Test upload config endpoint
    const uploadConfigResponse = await request.get('http://localhost:3001/api/analysis/upload/config');
    expect(uploadConfigResponse.ok()).toBeTruthy();
    
    const uploadConfigData = await uploadConfigResponse.json();
    expect(uploadConfigData).toHaveProperty('success', true);
    expect(uploadConfigData.data).toHaveProperty('maxFileSize');
    expect(uploadConfigData.data).toHaveProperty('allowedTypes');
    expect(uploadConfigData.data).toHaveProperty('limits');
    
    // Test config presets endpoint
    const presetsResponse = await request.get('http://localhost:3001/api/analysis/config/presets');
    expect(presetsResponse.ok()).toBeTruthy();
    
    const presetsData = await presetsResponse.json();
    expect(presetsData).toHaveProperty('success', true);
    expect(presetsData.data).toHaveProperty('presets');
    expect(presetsData.data.presets).toHaveProperty('quick');
    expect(presetsData.data.presets).toHaveProperty('standard');
    expect(presetsData.data.presets).toHaveProperty('professional');
    
    // Test config validation endpoint
    const validationResponse = await request.post('http://localhost:3001/api/analysis/config/validate', {
      data: {
        config: {
          useGPT4o: true,
          useReasoning: true,
          spatialResolution: 'HIGH',
          materialRecognitionDepth: 'DETAILED'
        }
      }
    });
    expect(validationResponse.ok()).toBeTruthy();
    
    const validationData = await validationResponse.json();
    expect(validationData).toHaveProperty('success', true);
    expect(validationData.data).toHaveProperty('validation');
    expect(validationData.data.validation).toHaveProperty('isValid');
    
    console.log('✅ New enhanced endpoints working correctly');
  });

  test('should maintain queue status functionality', async ({ request }) => {
    console.log('🧪 Testing queue status functionality...');
    
    const queueResponse = await request.get('http://localhost:3001/api/analysis/queue/status');
    expect(queueResponse.ok()).toBeTruthy();
    
    const queueData = await queueResponse.json();
    expect(queueData).toHaveProperty('success', true);
    expect(queueData.data).toHaveProperty('queue');
    expect(queueData.data).toHaveProperty('timestamp');
    
    console.log('✅ Queue status functionality working correctly');
  });

  test('should handle error cases gracefully', async ({ request }) => {
    console.log('🧪 Testing error handling...');
    
    // Test upload without file
    const noFileResponse = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {}
    });
    expect(noFileResponse.status()).toBe(400);
    
    // Test status for non-existent analysis
    const nonExistentResponse = await request.get('http://localhost:3001/api/analysis/non-existent-id/status');
    expect(nonExistentResponse.status()).toBe(400);
    
    // Test invalid config validation
    const invalidConfigResponse = await request.post('http://localhost:3001/api/analysis/config/validate', {
      data: {
        config: {
          useGPT4o: 'invalid-boolean',
          spatialResolution: 'INVALID_RESOLUTION'
        }
      }
    });
    expect(invalidConfigResponse.ok()).toBeTruthy();
    
    const invalidConfigData = await invalidConfigResponse.json();
    expect(invalidConfigData.data.validation.isValid).toBe(false);
    expect(invalidConfigData.data.validation.errors.length).toBeGreaterThan(0);
    
    console.log('✅ Error handling working correctly');
  });

  test('should maintain performance characteristics', async ({ request }) => {
    console.log('🧪 Testing performance characteristics...');
    
    const startTime = Date.now();
    
    const response = await request.get('http://localhost:3001/api/analysis/config/defaults');
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    expect(response.ok()).toBeTruthy();
    
    // Config endpoints should be very fast (under 1 second)
    expect(responseTime).toBeLessThan(1000);
    
    console.log(`✅ Performance maintained: ${responseTime}ms response time`);
  });

  test('should support batch operations', async ({ request }) => {
    console.log('🧪 Testing batch operations...');
    
    // Create multiple analyses first
    const analysisIds = [];
    
    for (let i = 0; i < 3; i++) {
      const uploadResponse = await request.post('http://localhost:3001/api/analysis/upload', {
        multipart: {
          file: {
            name: `test-${i}.pdf`,
            mimeType: 'application/pdf',
            buffer: fs.readFileSync(testPdfPath),
          },
          useGPT4o: 'false',
          useReasoning: 'false'
        },
        timeout: 30000
      });
      
      if (uploadResponse.ok()) {
        const data = await uploadResponse.json();
        analysisIds.push(data.data.analysisId);
      }
    }
    
    if (analysisIds.length > 0) {
      // Test batch status endpoint
      const batchResponse = await request.post('http://localhost:3001/api/analysis/batch/status', {
        data: {
          analysisIds
        }
      });
      
      expect(batchResponse.ok()).toBeTruthy();
      
      const batchData = await batchResponse.json();
      expect(batchData).toHaveProperty('success', true);
      expect(batchData.data).toHaveProperty('results');
      expect(batchData.data.results.length).toBe(analysisIds.length);
      expect(batchData.data).toHaveProperty('total', analysisIds.length);
      
      console.log(`✅ Batch operations working correctly for ${analysisIds.length} analyses`);
    } else {
      console.log('ℹ️ No analyses created for batch testing');
    }
  });
});
