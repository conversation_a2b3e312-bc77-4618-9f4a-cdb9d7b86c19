/**
 * Enhanced Reporting Integration Tests
 * 
 * Priority 2 Enhanced Analysis Engine Feature 3
 * Tests comprehensive PDF report generation with all analysis data
 */

import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';
import path from 'path';
import fs from 'fs';

test.describe('Enhanced Reporting Service', () => {
  let testHelpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    testHelpers = new TestHelpers(page);
    await testHelpers.setupTest();
  });

  test.afterEach(async () => {
    await testHelpers.cleanup();
  });

  test('should load report templates successfully', async ({ page }) => {
    const response = await page.request.get('/api/reports/templates');
    expect(response.ok()).toBeTruthy();

    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.templates).toBeDefined();
    expect(data.data.templates.length).toBeGreaterThan(0);

    // Verify template structure
    const template = data.data.templates[0];
    expect(template).toHaveProperty('id');
    expect(template).toHaveProperty('name');
    expect(template).toHaveProperty('description');
    expect(template).toHaveProperty('sections');
    expect(Array.isArray(template.sections)).toBe(true);
  });

  test('should generate basic PDF report successfully', async ({ page }) => {
    // First, perform an analysis to get results
    const testFile = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
    expect(fs.existsSync(testFile)).toBe(true);

    // Upload and analyze file
    const analysisResponse = await testHelpers.uploadAndAnalyze(testFile);
    expect(analysisResponse.success).toBe(true);

    const analysisId = analysisResponse.data.analysisId;

    // Wait for analysis completion
    await testHelpers.waitForAnalysisCompletion(analysisId, 120000);

    // Generate report
    const reportResponse = await page.request.post('/api/reports/generate', {
      data: {
        analysisId,
        templateId: 'basic',
        options: {
          includeCharts: true,
          include3DVisualization: false,
          includeRawData: false,
          quality: 'standard'
        }
      }
    });

    expect(reportResponse.ok()).toBeTruthy();
    const reportData = await reportResponse.json();
    
    expect(reportData.success).toBe(true);
    expect(reportData.data.reportId).toBeDefined();
    expect(reportData.data.filePath).toBeDefined();
    expect(reportData.data.fileSize).toBeGreaterThan(0);
    expect(reportData.data.pageCount).toBeGreaterThan(0);
    expect(reportData.data.generationTime).toBeGreaterThan(0);
    expect(reportData.data.downloadUrl).toBeDefined();

    // Verify report file exists
    expect(fs.existsSync(reportData.data.filePath)).toBe(true);

    // Verify file is a valid PDF (basic check)
    const fileBuffer = fs.readFileSync(reportData.data.filePath);
    expect(fileBuffer.toString('ascii', 0, 4)).toBe('%PDF');
  });

  test('should generate detailed PDF report with all features', async ({ page }) => {
    const testFile = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
    
    // Upload and analyze file
    const analysisResponse = await testHelpers.uploadAndAnalyze(testFile);
    const analysisId = analysisResponse.data.analysisId;

    // Wait for analysis completion
    await testHelpers.waitForAnalysisCompletion(analysisId, 120000);

    // Generate detailed report with all options
    const reportResponse = await page.request.post('/api/reports/generate', {
      data: {
        analysisId,
        templateId: 'detailed',
        customization: {
          companyName: 'Test Kitchen Design Co.',
          brandColors: {
            primary: '#1e40af',
            secondary: '#475569',
            accent: '#0284c7'
          }
        },
        options: {
          includeCharts: true,
          include3DVisualization: true,
          includeRawData: true,
          quality: 'high'
        }
      }
    });

    expect(reportResponse.ok()).toBeTruthy();
    const reportData = await reportResponse.json();
    
    expect(reportData.success).toBe(true);
    expect(reportData.data.pageCount).toBeGreaterThan(3); // Detailed report should have more pages
    expect(reportData.data.sections).toBeDefined();
    expect(reportData.data.sections.length).toBeGreaterThan(0);

    // Verify metadata
    expect(reportData.data.metadata).toBeDefined();
    expect(reportData.data.metadata.generatedAt).toBeDefined();
    expect(reportData.data.metadata.analysisData).toBeDefined();
    expect(reportData.data.metadata.reportVersion).toBeDefined();
  });

  test('should generate professional PDF report', async ({ page }) => {
    const testFile = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
    
    const analysisResponse = await testHelpers.uploadAndAnalyze(testFile);
    const analysisId = analysisResponse.data.analysisId;

    await testHelpers.waitForAnalysisCompletion(analysisId, 120000);

    // Generate professional report
    const reportResponse = await page.request.post('/api/reports/generate', {
      data: {
        analysisId,
        templateId: 'professional',
        customization: {
          companyName: 'Professional Kitchen Designs LLC',
          brandColors: {
            primary: '#1e3a8a',
            secondary: '#374151',
            accent: '#059669'
          }
        },
        options: {
          includeCharts: true,
          include3DVisualization: true,
          includeRawData: true,
          quality: 'high'
        }
      }
    });

    expect(reportResponse.ok()).toBeTruthy();
    const reportData = await reportResponse.json();
    
    expect(reportData.success).toBe(true);
    expect(reportData.data.pageCount).toBeGreaterThan(5); // Professional report should be comprehensive
    
    // Verify all expected sections are included
    const sections = reportData.data.sections;
    const sectionIds = sections.map((s: any) => s.sectionId);
    expect(sectionIds).toContain('summary');
    expect(sectionIds).toContain('cabinets');
    expect(sectionIds).toContain('measurements');
  });

  test('should download generated report successfully', async ({ page }) => {
    const testFile = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
    
    const analysisResponse = await testHelpers.uploadAndAnalyze(testFile);
    const analysisId = analysisResponse.data.analysisId;

    await testHelpers.waitForAnalysisCompletion(analysisId, 120000);

    // Generate report
    const reportResponse = await page.request.post('/api/reports/generate', {
      data: {
        analysisId,
        templateId: 'basic',
        options: { quality: 'standard' }
      }
    });

    const reportData = await reportResponse.json();
    const reportId = reportData.data.reportId;

    // Download report
    const downloadResponse = await page.request.get(`/api/reports/download/${reportId}`);
    expect(downloadResponse.ok()).toBeTruthy();
    expect(downloadResponse.headers()['content-type']).toBe('application/pdf');
    expect(downloadResponse.headers()['content-disposition']).toContain('attachment');

    const pdfBuffer = await downloadResponse.body();
    expect(pdfBuffer.length).toBeGreaterThan(0);
    
    // Verify PDF header
    const pdfHeader = pdfBuffer.toString('ascii', 0, 4);
    expect(pdfHeader).toBe('%PDF');
  });

  test('should manage report history correctly', async ({ page }) => {
    // Get initial history
    const initialHistoryResponse = await page.request.get('/api/reports/history');
    const initialHistory = await initialHistoryResponse.json();
    const initialCount = initialHistory.data.reports.length;

    // Generate a report
    const testFile = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
    const analysisResponse = await testHelpers.uploadAndAnalyze(testFile);
    const analysisId = analysisResponse.data.analysisId;

    await testHelpers.waitForAnalysisCompletion(analysisId, 120000);

    const reportResponse = await page.request.post('/api/reports/generate', {
      data: {
        analysisId,
        templateId: 'basic',
        options: { quality: 'draft' }
      }
    });

    const reportData = await reportResponse.json();
    const reportId = reportData.data.reportId;

    // Check updated history
    const updatedHistoryResponse = await page.request.get('/api/reports/history');
    const updatedHistory = await updatedHistoryResponse.json();
    
    expect(updatedHistory.data.reports.length).toBe(initialCount + 1);
    
    const newReport = updatedHistory.data.reports.find((r: any) => r.reportId === reportId);
    expect(newReport).toBeDefined();
    expect(newReport.metadata).toBeDefined();
    expect(newReport.fileSize).toBeGreaterThan(0);

    // Delete the report
    const deleteResponse = await page.request.delete(`/api/reports/${reportId}`);
    expect(deleteResponse.ok()).toBeTruthy();

    // Verify deletion
    const finalHistoryResponse = await page.request.get('/api/reports/history');
    const finalHistory = await finalHistoryResponse.json();
    expect(finalHistory.data.reports.length).toBe(initialCount);
  });

  test('should schedule and manage report generation', async ({ page }) => {
    const testFile = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
    const analysisResponse = await testHelpers.uploadAndAnalyze(testFile);
    const analysisId = analysisResponse.data.analysisId;

    // Schedule a report
    const scheduleResponse = await page.request.post('/api/reports/schedule', {
      data: {
        analysisId,
        templateId: 'basic',
        frequency: 'weekly',
        nextRun: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        recipients: ['<EMAIL>']
      }
    });

    expect(scheduleResponse.ok()).toBeTruthy();
    const scheduleData = await scheduleResponse.json();
    expect(scheduleData.success).toBe(true);
    expect(scheduleData.data.scheduleId).toBeDefined();

    const scheduleId = scheduleData.data.scheduleId;

    // Get scheduled reports
    const scheduledResponse = await page.request.get('/api/reports/scheduled');
    const scheduledData = await scheduledResponse.json();
    
    expect(scheduledData.success).toBe(true);
    const schedule = scheduledData.data.schedules.find((s: any) => s.id === scheduleId);
    expect(schedule).toBeDefined();
    expect(schedule.analysisId).toBe(analysisId);
    expect(schedule.frequency).toBe('weekly');

    // Cancel the scheduled report
    const cancelResponse = await page.request.delete(`/api/reports/scheduled/${scheduleId}`);
    expect(cancelResponse.ok()).toBeTruthy();

    // Verify cancellation
    const finalScheduledResponse = await page.request.get('/api/reports/scheduled');
    const finalScheduledData = await finalScheduledResponse.json();
    const cancelledSchedule = finalScheduledData.data.schedules.find((s: any) => s.id === scheduleId);
    expect(cancelledSchedule).toBeUndefined();
  });

  test('should get report statistics', async ({ page }) => {
    const statsResponse = await page.request.get('/api/reports/statistics');
    expect(statsResponse.ok()).toBeTruthy();

    const statsData = await statsResponse.json();
    expect(statsData.success).toBe(true);
    expect(statsData.data).toHaveProperty('totalReports');
    expect(statsData.data).toHaveProperty('totalSize');
    expect(statsData.data).toHaveProperty('averageGenerationTime');
    expect(statsData.data).toHaveProperty('templateUsage');
    
    expect(typeof statsData.data.totalReports).toBe('number');
    expect(typeof statsData.data.totalSize).toBe('number');
    expect(typeof statsData.data.averageGenerationTime).toBe('number');
    expect(typeof statsData.data.templateUsage).toBe('object');
  });

  test('should handle invalid report generation requests', async ({ page }) => {
    // Test missing analysisId
    const invalidResponse1 = await page.request.post('/api/reports/generate', {
      data: {
        templateId: 'basic'
      }
    });
    expect(invalidResponse1.status()).toBe(400);

    // Test missing templateId
    const invalidResponse2 = await page.request.post('/api/reports/generate', {
      data: {
        analysisId: 'invalid-id'
      }
    });
    expect(invalidResponse2.status()).toBe(400);

    // Test invalid template
    const invalidResponse3 = await page.request.post('/api/reports/generate', {
      data: {
        analysisId: 'test-id',
        templateId: 'nonexistent-template'
      }
    });
    expect(invalidResponse3.status()).toBe(500); // Should fail during generation
  });

  test('should handle report not found scenarios', async ({ page }) => {
    // Test download non-existent report
    const downloadResponse = await page.request.get('/api/reports/download/nonexistent-report');
    expect(downloadResponse.status()).toBe(404);

    // Test delete non-existent report
    const deleteResponse = await page.request.delete('/api/reports/nonexistent-report');
    expect(deleteResponse.status()).toBe(404);

    // Test cancel non-existent schedule
    const cancelResponse = await page.request.delete('/api/reports/scheduled/nonexistent-schedule');
    expect(cancelResponse.status()).toBe(404);
  });
});
