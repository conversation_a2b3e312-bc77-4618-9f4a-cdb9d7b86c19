import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

test.describe('GPT-o1 Advanced Features Integration', () => {
  let testHelpers: TestHelpers;
  const API_BASE_URL = 'http://localhost:3001';
  const testPdfPath = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');

  test.beforeEach(async ({ page }) => {
    testHelpers = new TestHelpers(page);
    await testHelpers.setupTestEnvironment();
  });

  test.describe('GPT-o1 Model Integration', () => {
    test('should use GPT-o1 for complex reasoning tasks', async ({ request }) => {
      console.log('🧪 Testing GPT-o1 complex reasoning integration');

      const response = await request.post(`${API_BASE_URL}/api/analysis/upload`, {
        multipart: {
          file: {
            name: 'kitchen-design-test.pdf',
            mimeType: 'application/pdf',
            buffer: fs.readFileSync(testPdfPath),
          },
          useGPTO1: 'true',
          enableAdvancedReasoning: 'true',
          complexityLevel: 'HIGH',
          reasoningDepth: 'COMPREHENSIVE'
        },
      });

      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.data.analysisId).toBeDefined();
      expect(data.data.modelUsed).toContain('gpt-o1');
      expect(data.data.reasoningChain).toBeDefined();
      expect(data.data.confidenceScore).toBeGreaterThan(0.8);

      console.log(`✅ GPT-o1 analysis completed with ${data.data.confidenceScore} confidence`);
    });

    test('should provide detailed reasoning chain visualization', async ({ request }) => {
      console.log('🧪 Testing reasoning chain visualization');

      // First create an analysis with GPT-o1
      const analysisResponse = await request.post(`${API_BASE_URL}/api/analysis/upload`, {
        multipart: {
          file: {
            name: 'kitchen-design-test.pdf',
            mimeType: 'application/pdf',
            buffer: fs.readFileSync(testPdfPath),
          },
          useGPTO1: 'true',
          enableReasoningVisualization: 'true'
        },
      });

      const analysisData = await analysisResponse.json();
      const analysisId = analysisData.data.analysisId;

      // Get reasoning chain visualization
      const reasoningResponse = await request.get(`${API_BASE_URL}/api/reasoning/visualization/${analysisId}`);
      expect(reasoningResponse.ok()).toBeTruthy();
      
      const reasoningData = await reasoningResponse.json();
      expect(reasoningData.success).toBe(true);
      expect(reasoningData.data.steps).toBeInstanceOf(Array);
      expect(reasoningData.data.steps.length).toBeGreaterThan(0);
      
      // Check reasoning step structure
      const firstStep = reasoningData.data.steps[0];
      expect(firstStep.stepNumber).toBeDefined();
      expect(firstStep.description).toBeDefined();
      expect(firstStep.confidence).toBeDefined();
      expect(firstStep.duration).toBeDefined();

      console.log(`✅ Reasoning chain has ${reasoningData.data.steps.length} steps`);
    });

    test('should handle GPT-o1 model comparison', async ({ request }) => {
      console.log('🧪 Testing GPT-o1 vs other models comparison');

      // Run analysis with multiple models
      const models = ['gpt-4o', 'gpt-o1', 'o4-mini'];
      const results = [];

      for (const model of models) {
        const response = await request.post(`${API_BASE_URL}/api/analysis/upload`, {
          multipart: {
            file: {
              name: 'kitchen-design-test.pdf',
              mimeType: 'application/pdf',
              buffer: fs.readFileSync(testPdfPath),
            },
            modelOverride: model,
            enableComparison: 'true'
          },
        });

        if (response.ok()) {
          const data = await response.json();
          results.push({
            model,
            confidence: data.data.confidenceScore,
            duration: data.data.processingTime,
            cabinetCount: data.data.cabinetCount
          });
        }
      }

      expect(results.length).toBeGreaterThan(0);
      console.log('✅ Model comparison results:', results);
    });
  });

  test.describe('Intelligent Caching System', () => {
    test('should implement semantic similarity caching', async ({ request }) => {
      console.log('🧪 Testing semantic similarity caching');

      // First request - should miss cache
      const firstResponse = await request.post(`${API_BASE_URL}/api/analysis/upload`, {
        multipart: {
          file: {
            name: 'kitchen-design-test.pdf',
            mimeType: 'application/pdf',
            buffer: fs.readFileSync(testPdfPath),
          },
          useGPTO1: 'true',
          enableCaching: 'true'
        },
      });

      expect(firstResponse.ok()).toBeTruthy();
      const firstData = await firstResponse.json();
      expect(firstData.data.cacheHit).toBe(false);

      // Second similar request - should hit cache
      const secondResponse = await request.post(`${API_BASE_URL}/api/analysis/upload`, {
        multipart: {
          file: {
            name: 'kitchen-design-test.pdf',
            mimeType: 'application/pdf',
            buffer: fs.readFileSync(testPdfPath),
          },
          useGPTO1: 'true',
          enableCaching: 'true'
        },
      });

      expect(secondResponse.ok()).toBeTruthy();
      const secondData = await secondResponse.json();
      
      // Should be faster due to caching
      expect(secondData.data.processingTime).toBeLessThan(firstData.data.processingTime);
      
      console.log(`✅ Cache performance: ${firstData.data.processingTime}ms → ${secondData.data.processingTime}ms`);
    });

    test('should provide cache metrics and statistics', async ({ request }) => {
      console.log('🧪 Testing cache metrics');

      const response = await request.get(`${API_BASE_URL}/api/cache/metrics`);
      expect(response.ok()).toBeTruthy();
      
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.hitRate).toBeDefined();
      expect(data.data.totalRequests).toBeDefined();
      expect(data.data.cacheSize).toBeDefined();
      expect(data.data.averageResponseTime).toBeDefined();

      console.log(`✅ Cache hit rate: ${data.data.hitRate}%`);
      console.log(`📊 Total requests: ${data.data.totalRequests}`);
    });

    test('should handle cache warming strategies', async ({ request }) => {
      console.log('🧪 Testing cache warming');

      const warmingResponse = await request.post(`${API_BASE_URL}/api/cache/warm`, {
        data: {
          strategy: 'popular_queries',
          limit: 10
        }
      });

      if (warmingResponse.ok()) {
        const warmingData = await warmingResponse.json();
        expect(warmingData.success).toBe(true);
        expect(warmingData.data.warmedQueries).toBeDefined();
        
        console.log(`✅ Cache warmed with ${warmingData.data.warmedQueries} queries`);
      } else {
        console.log('ℹ️ Cache warming endpoint not implemented');
      }
    });

    test('should achieve target API call reduction', async ({ request }) => {
      console.log('🧪 Testing API call reduction target (60-80%)');

      // Get baseline metrics
      const baselineResponse = await request.get(`${API_BASE_URL}/api/cache/metrics`);
      const baselineData = await baselineResponse.json();
      
      // Perform multiple similar requests
      const requests = 5;
      for (let i = 0; i < requests; i++) {
        await request.post(`${API_BASE_URL}/api/analysis/upload`, {
          multipart: {
            file: {
              name: 'kitchen-design-test.pdf',
              mimeType: 'application/pdf',
              buffer: fs.readFileSync(testPdfPath),
            },
            useGPTO1: 'true',
            enableCaching: 'true',
            requestId: `test-${i}`
          },
        });
      }

      // Check updated metrics
      const updatedResponse = await request.get(`${API_BASE_URL}/api/cache/metrics`);
      const updatedData = await updatedResponse.json();
      
      const hitRate = updatedData.data.hitRate;
      console.log(`📊 Cache hit rate: ${hitRate}%`);
      
      // Target: 60-80% API call reduction
      if (hitRate >= 60) {
        console.log(`✅ Target achieved: ${hitRate}% hit rate (≥60% target)`);
      } else {
        console.log(`⚠️ Target not met: ${hitRate}% hit rate (<60% target)`);
      }
    });
  });

  test.describe('Performance Metrics Dashboard Enhancement', () => {
    test('should display GPT-o1 specific analytics', async ({ page }) => {
      await testHelpers.navigateToPage('/performance-monitoring');
      
      // Look for GPT-o1 specific metrics
      const gptO1Section = page.locator('[data-testid="gpt-o1-analytics"], .gpt-o1-analytics');
      const reasoningMetrics = page.locator('[data-testid="reasoning-metrics"], .reasoning-metrics');
      const complexityAnalysis = page.locator('[data-testid="complexity-analysis"], .complexity-analysis');
      
      if (await gptO1Section.count() > 0) {
        console.log('✅ GPT-o1 analytics section found');
      }
      
      if (await reasoningMetrics.count() > 0) {
        console.log('✅ Reasoning metrics displayed');
      }
      
      if (await complexityAnalysis.count() > 0) {
        console.log('✅ Complexity analysis available');
      }
    });

    test('should show reasoning chain performance metrics', async ({ request }) => {
      console.log('🧪 Testing reasoning chain performance metrics');

      const response = await request.get(`${API_BASE_URL}/api/performance/gpt-o1-analytics?timeRange=24h`);
      
      if (response.ok()) {
        const data = await response.json();
        expect(data.success).toBe(true);
        expect(data.data.reasoningChainPerformance).toBeDefined();
        expect(data.data.complexityAnalysis).toBeDefined();
        
        const performance = data.data.reasoningChainPerformance;
        expect(performance.averageStepsPerChain).toBeGreaterThan(0);
        expect(performance.averageStepCompletionTime).toBeGreaterThan(0);
        expect(performance.stepSuccessRate).toBeGreaterThan(0);
        
        console.log(`✅ Average steps per chain: ${performance.averageStepsPerChain}`);
        console.log(`⏱️ Average step completion: ${performance.averageStepCompletionTime}ms`);
        console.log(`📊 Step success rate: ${performance.stepSuccessRate}%`);
      } else {
        console.log('ℹ️ GPT-o1 analytics endpoint not available');
      }
    });

    test('should display caching efficiency metrics', async ({ page }) => {
      await testHelpers.navigateToPage('/performance-monitoring');
      
      // Look for caching efficiency tab or section
      const cachingTab = page.locator('button:has-text("Caching"), [data-testid="caching-tab"]');
      const cachingMetrics = page.locator('[data-testid="caching-metrics"], .caching-metrics');
      
      if (await cachingTab.count() > 0) {
        await cachingTab.click();
        await page.waitForTimeout(1000);
        console.log('✅ Caching efficiency tab available');
      }
      
      if (await cachingMetrics.count() > 0) {
        // Check for specific caching metrics
        const hitRateMetric = page.locator('[data-testid="hit-rate"], .hit-rate');
        const reductionMetric = page.locator('[data-testid="api-reduction"], .api-reduction');
        const similarityMetric = page.locator('[data-testid="similarity-score"], .similarity-score');
        
        if (await hitRateMetric.count() > 0) {
          const hitRate = await hitRateMetric.textContent();
          console.log(`📊 Cache hit rate: ${hitRate}`);
        }
        
        if (await reductionMetric.count() > 0) {
          const reduction = await reductionMetric.textContent();
          console.log(`📉 API call reduction: ${reduction}`);
        }
        
        if (await similarityMetric.count() > 0) {
          const similarity = await similarityMetric.textContent();
          console.log(`🔍 Semantic similarity threshold: ${similarity}`);
        }
      }
    });

    test('should handle real-time performance updates', async ({ page }) => {
      await testHelpers.navigateToPage('/performance-monitoring');
      
      // Wait for WebSocket connection
      const wsConnected = await testHelpers.waitForWebSocketConnection();
      
      if (wsConnected) {
        // Monitor for GPT-o1 specific updates
        const performanceUpdates = await page.evaluate(() => {
          return new Promise((resolve) => {
            const updates: any[] = [];
            
            if (window.io && window.io.connected) {
              // Listen for GPT-o1 performance updates
              window.io.on('gpt-o1-metrics-update', (data: any) => {
                updates.push({ type: 'gpt-o1-metrics', data });
              });
              
              window.io.on('reasoning-chain-update', (data: any) => {
                updates.push({ type: 'reasoning-chain', data });
              });
              
              window.io.on('cache-metrics-update', (data: any) => {
                updates.push({ type: 'cache-metrics', data });
              });
              
              // Resolve after 5 seconds
              setTimeout(() => resolve(updates), 5000);
            } else {
              resolve([]);
            }
          });
        });
        
        console.log(`📡 Real-time updates received: ${(performanceUpdates as any[]).length}`);
      }
    });
  });

  test.describe('Advanced Reasoning Features', () => {
    test('should handle multi-step reasoning workflows', async ({ request }) => {
      console.log('🧪 Testing multi-step reasoning workflows');

      const response = await request.post(`${API_BASE_URL}/api/reasoning/multi-step`, {
        data: {
          analysisType: 'comprehensive_kitchen_analysis',
          steps: [
            'spatial_analysis',
            'material_identification',
            'hardware_recognition',
            'layout_optimization',
            'cost_estimation'
          ],
          useGPTO1: true,
          enableStepValidation: true
        }
      });

      if (response.ok()) {
        const data = await response.json();
        expect(data.success).toBe(true);
        expect(data.data.workflow).toBeDefined();
        expect(data.data.steps).toBeInstanceOf(Array);
        expect(data.data.overallConfidence).toBeGreaterThan(0);
        
        console.log(`✅ Multi-step workflow completed with ${data.data.steps.length} steps`);
        console.log(`📊 Overall confidence: ${data.data.overallConfidence}`);
      } else {
        console.log('ℹ️ Multi-step reasoning endpoint not implemented');
      }
    });

    test('should provide reasoning step validation', async ({ request }) => {
      console.log('🧪 Testing reasoning step validation');

      const response = await request.post(`${API_BASE_URL}/api/reasoning/validate-step`, {
        data: {
          stepId: 'spatial_analysis_001',
          stepResult: {
            cabinetCount: 12,
            roomDimensions: { width: 3000, height: 2400, depth: 600 },
            confidence: 0.92
          },
          validationCriteria: {
            minConfidence: 0.8,
            requireDimensions: true,
            validateCabinetCount: true
          }
        }
      });

      if (response.ok()) {
        const data = await response.json();
        expect(data.success).toBe(true);
        expect(data.data.isValid).toBeDefined();
        expect(data.data.validationScore).toBeDefined();
        expect(data.data.issues).toBeInstanceOf(Array);
        
        console.log(`✅ Step validation: ${data.data.isValid ? 'PASSED' : 'FAILED'}`);
        console.log(`📊 Validation score: ${data.data.validationScore}`);
      } else {
        console.log('ℹ️ Step validation endpoint not implemented');
      }
    });

    test('should handle reasoning chain branching', async ({ request }) => {
      console.log('🧪 Testing reasoning chain branching');

      const response = await request.post(`${API_BASE_URL}/api/reasoning/branch`, {
        data: {
          parentChainId: 'chain_001',
          branchPoint: 'material_analysis',
          alternativeApproaches: [
            'visual_material_detection',
            'contextual_material_inference',
            'hybrid_material_analysis'
          ],
          useGPTO1: true
        }
      });

      if (response.ok()) {
        const data = await response.json();
        expect(data.success).toBe(true);
        expect(data.data.branches).toBeInstanceOf(Array);
        expect(data.data.branches.length).toBeGreaterThan(0);
        
        console.log(`✅ Reasoning chain branched into ${data.data.branches.length} alternatives`);
      } else {
        console.log('ℹ️ Reasoning chain branching not implemented');
      }
    });
  });
});
