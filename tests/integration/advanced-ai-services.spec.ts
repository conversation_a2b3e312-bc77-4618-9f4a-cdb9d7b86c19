import { test, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Advanced AI Services Integration Tests
 * Comprehensive testing of all advanced AI services with real Azure OpenAI integration
 */
test.describe('Advanced AI Services Integration', () => {
  const testPdfPath = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');

  test.beforeEach(async ({ page }) => {
    // Ensure we start fresh for each test
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test.describe('Prompt Optimization Service', () => {
    test('should optimize prompts with all 5 heuristic algorithms', async ({ request }) => {
      // Test prompt optimization endpoint
      const optimizationResponse = await request.post('http://localhost:3001/api/optimization/optimize', {
        data: {
          originalPrompt: 'Analyze this kitchen design',
          context: {
            analysisType: 'kitchen_analysis',
            targetMetrics: {
              minAccuracy: 0.85,
              maxResponseTime: 30000
            }
          }
        }
      });

      expect(optimizationResponse.ok()).toBeTruthy();
      const optimizationData = await optimizationResponse.json();
      
      expect(optimizationData.success).toBe(true);
      expect(optimizationData.data).toHaveProperty('optimizedPrompt');
      expect(optimizationData.data).toHaveProperty('appliedHeuristics');
      expect(optimizationData.data).toHaveProperty('estimatedImprovement');
      
      // Verify all 5 heuristics are available
      const heuristics = optimizationData.data.appliedHeuristics;
      const expectedHeuristics = [
        'clarity_enhancement',
        'specificity_enhancement', 
        'structure_enhancement',
        'context_enhancement',
        'validation_enhancement'
      ];
      
      // At least some heuristics should be applied
      expect(heuristics.length).toBeGreaterThan(0);
      
      console.log(`✅ Applied ${heuristics.length} optimization heuristics`);
      console.log(`✅ Estimated accuracy improvement: ${optimizationData.data.estimatedImprovement.accuracy}`);
    });

    test('should track optimization performance history', async ({ request }) => {
      // Get optimization history
      const historyResponse = await request.get('http://localhost:3001/api/optimization/history/kitchen_analysis');
      
      expect(historyResponse.ok()).toBeTruthy();
      const historyData = await historyResponse.json();
      
      expect(historyData.success).toBe(true);
      expect(historyData.data).toHaveProperty('history');
      expect(Array.isArray(historyData.data.history)).toBeTruthy();
      
      console.log(`✅ Optimization history contains ${historyData.data.history.length} entries`);
    });
  });

  test.describe('A/B Testing Framework', () => {
    test('should create and manage A/B tests with statistical significance', async ({ request }) => {
      // Create a new A/B test
      const testConfig = {
        name: 'Kitchen Analysis Prompt Test',
        description: 'Testing different prompt variations for kitchen analysis',
        variants: [
          {
            id: 'control',
            name: 'Control Prompt',
            prompt: 'Analyze this kitchen design for cabinets and materials',
            weight: 0.5
          },
          {
            id: 'enhanced',
            name: 'Enhanced Prompt', 
            prompt: 'Perform detailed analysis of kitchen design focusing on cabinet types, materials, and hardware',
            weight: 0.5
          }
        ],
        trafficAllocation: {
          'control': 0.5,
          'enhanced': 0.5
        },
        targetMetrics: {
          primary: 'accuracy',
          secondary: ['confidence', 'responseTime']
        },
        minimumSampleSize: 10,
        confidenceLevel: 0.95,
        startDate: new Date(),
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
      };

      const createResponse = await request.post('http://localhost:3001/api/ab-tests/create', {
        data: testConfig
      });

      expect(createResponse.ok()).toBeTruthy();
      const createData = await createResponse.json();
      
      expect(createData.success).toBe(true);
      expect(createData.data).toHaveProperty('testId');
      
      const testId = createData.data.testId;
      console.log(`✅ Created A/B test: ${testId}`);

      // Start the test
      const startResponse = await request.post(`http://localhost:3001/api/ab-tests/${testId}/start`);
      expect(startResponse.ok()).toBeTruthy();
      
      console.log(`✅ Started A/B test: ${testId}`);

      // Get test status
      const statusResponse = await request.get(`http://localhost:3001/api/ab-tests/${testId}/status`);
      expect(statusResponse.ok()).toBeTruthy();
      
      const statusData = await statusResponse.json();
      expect(statusData.data.status).toBe('running');
      
      console.log(`✅ A/B test status: ${statusData.data.status}`);
    });

    test('should get prompt variants and record results', async ({ request }) => {
      // Get active A/B tests
      const activeResponse = await request.get('http://localhost:3001/api/ab-tests/active');
      expect(activeResponse.ok()).toBeTruthy();
      
      const activeData = await activeResponse.json();
      expect(activeData.success).toBe(true);
      expect(activeData.data).toHaveProperty('tests');
      
      console.log(`✅ Found ${activeData.data.tests.length} active A/B tests`);

      // Get prompt variant for analysis
      const variantResponse = await request.get('http://localhost:3001/api/ab-tests/variant/kitchen_analysis/test_analysis_123');
      expect(variantResponse.ok()).toBeTruthy();
      
      const variantData = await variantResponse.json();
      expect(variantData.success).toBe(true);
      expect(variantData.data).toHaveProperty('variantId');
      expect(variantData.data).toHaveProperty('prompt');
      
      console.log(`✅ Got variant: ${variantData.data.variantId}`);
    });
  });

  test.describe('Reasoning Manager', () => {
    test('should create and manage structured reasoning chains', async ({ request }) => {
      // Start a reasoning chain
      const chainResponse = await request.post('http://localhost:3001/api/reasoning/start', {
        data: {
          analysisId: 'test_analysis_reasoning_123',
          context: {
            analysisType: 'kitchen_analysis',
            complexity: 'high',
            requirements: ['cabinet_analysis', 'material_identification', 'hardware_detection']
          }
        }
      });

      expect(chainResponse.ok()).toBeTruthy();
      const chainData = await chainResponse.json();
      
      expect(chainData.success).toBe(true);
      expect(chainData.data).toHaveProperty('chainId');
      expect(chainData.data).toHaveProperty('goal');
      expect(chainData.data).toHaveProperty('steps');
      
      const chainId = chainData.data.chainId;
      console.log(`✅ Started reasoning chain: ${chainId}`);
      console.log(`✅ Chain goal: ${chainData.data.goal}`);
      console.log(`✅ Total steps: ${chainData.data.steps.length}`);

      // Progress through reasoning steps
      const progressResponse = await request.post(`http://localhost:3001/api/reasoning/${chainId}/progress`, {
        data: {
          stepIndex: 0,
          evidence: ['Cabinet types identified: base, wall, tall'],
          confidence: 0.9,
          reasoning: 'Clear visual identification of cabinet categories'
        }
      });

      expect(progressResponse.ok()).toBeTruthy();
      const progressData = await progressResponse.json();
      expect(progressData.success).toBe(true);
      
      console.log(`✅ Progressed reasoning chain to step 1`);

      // Get chain status
      const statusResponse = await request.get(`http://localhost:3001/api/reasoning/${chainId}/status`);
      expect(statusResponse.ok()).toBeTruthy();
      
      const statusData = await statusResponse.json();
      expect(statusData.data).toHaveProperty('status');
      expect(statusData.data).toHaveProperty('currentStep');
      expect(statusData.data.currentStep).toBe(1);
      
      console.log(`✅ Reasoning chain status: ${statusData.data.status}`);
    });

    test('should complete reasoning chains with quality scoring', async ({ request }) => {
      // Get active reasoning chains
      const activeResponse = await request.get('http://localhost:3001/api/reasoning/active');
      expect(activeResponse.ok()).toBeTruthy();
      
      const activeData = await activeResponse.json();
      expect(activeData.success).toBe(true);
      expect(activeData.data).toHaveProperty('chains');
      
      console.log(`✅ Found ${activeData.data.chains.length} active reasoning chains`);

      // Test reasoning templates
      const templatesResponse = await request.get('http://localhost:3001/api/reasoning/templates');
      expect(templatesResponse.ok()).toBeTruthy();
      
      const templatesData = await templatesResponse.json();
      expect(templatesData.success).toBe(true);
      expect(templatesData.data).toHaveProperty('templates');
      
      const templates = templatesData.data.templates;
      expect(templates).toHaveProperty('kitchen_analysis');
      expect(templates).toHaveProperty('material_analysis');
      expect(templates).toHaveProperty('hardware_analysis');
      
      console.log(`✅ Available reasoning templates: ${Object.keys(templates).join(', ')}`);
    });
  });

  test.describe('Enhanced PDF Processor', () => {
    test('should process PDFs with OCR and dimension detection', async ({ request }) => {
      // Upload PDF for enhanced processing
      const uploadResponse = await request.post('http://localhost:3001/api/pdf/process-enhanced', {
        multipart: {
          file: {
            name: 'kitchen-design-test.pdf',
            mimeType: 'application/pdf',
            buffer: fs.readFileSync(testPdfPath),
          },
          options: JSON.stringify({
            outputFormat: 'png',
            quality: 95,
            density: 300,
            optimizeForAnalysis: true,
            extractTextLayer: true,
            generateThumbnails: true
          })
        },
      });

      expect(uploadResponse.ok()).toBeTruthy();
      const uploadData = await uploadResponse.json();
      
      expect(uploadData.success).toBe(true);
      expect(uploadData.data).toHaveProperty('pages');
      expect(uploadData.data).toHaveProperty('textContent');
      expect(uploadData.data).toHaveProperty('dimensions');
      expect(uploadData.data).toHaveProperty('kitchenAnalysis');
      
      console.log(`✅ Processed ${uploadData.data.pages.length} pages`);
      console.log(`✅ Extracted ${uploadData.data.textContent.length} characters of text`);
      console.log(`✅ Found ${uploadData.data.dimensions.length} dimensions`);
      console.log(`✅ Kitchen analysis confidence: ${uploadData.data.kitchenAnalysis.confidence}`);

      // Verify dimension detection
      const dimensions = uploadData.data.dimensions;
      if (dimensions.length > 0) {
        expect(dimensions[0]).toHaveProperty('value');
        expect(dimensions[0]).toHaveProperty('unit');
        expect(dimensions[0]).toHaveProperty('confidence');
        expect(dimensions[0]).toHaveProperty('context');
      }

      // Verify kitchen content analysis
      const kitchenAnalysis = uploadData.data.kitchenAnalysis;
      expect(kitchenAnalysis).toHaveProperty('confidence');
      expect(kitchenAnalysis).toHaveProperty('elements');
      expect(kitchenAnalysis.confidence).toBeGreaterThan(0);
    });

    test('should extract text content with OCR fallback', async ({ request }) => {
      // Test text extraction endpoint
      const extractResponse = await request.post('http://localhost:3001/api/pdf/extract-text', {
        multipart: {
          file: {
            name: 'kitchen-design-test.pdf',
            mimeType: 'application/pdf',
            buffer: fs.readFileSync(testPdfPath),
          }
        },
      });

      expect(extractResponse.ok()).toBeTruthy();
      const extractData = await extractResponse.json();
      
      expect(extractData.success).toBe(true);
      expect(extractData.data).toHaveProperty('textContent');
      expect(extractData.data).toHaveProperty('extractionMethod');
      expect(extractData.data).toHaveProperty('confidence');
      
      console.log(`✅ Text extraction method: ${extractData.data.extractionMethod}`);
      console.log(`✅ Extraction confidence: ${extractData.data.confidence}`);
      console.log(`✅ Text length: ${extractData.data.textContent.length} characters`);
    });
  });

  test.describe('Integrated Analysis Pipeline', () => {
    test('should perform complete enhanced analysis with all services', async ({ request }) => {
      // Upload file and trigger enhanced analysis
      const uploadResponse = await request.post('http://localhost:3001/api/analysis/upload', {
        multipart: {
          file: {
            name: 'kitchen-design-test.pdf',
            mimeType: 'application/pdf',
            buffer: fs.readFileSync(testPdfPath),
          },
          useGPT4o: 'true',
          useReasoning: 'true',
          focusOnMaterials: 'true',
          focusOnHardware: 'true',
          enableMultiView: 'true'
        },
      });

      expect(uploadResponse.ok()).toBeTruthy();
      const uploadData = await uploadResponse.json();
      const analysisId = uploadData.data.analysisId;

      console.log(`✅ Started enhanced analysis: ${analysisId}`);

      // Monitor analysis progress with enhanced features
      let attempts = 0;
      const maxAttempts = 60; // 2 minutes
      let analysisComplete = false;
      let finalResults = null;

      while (attempts < maxAttempts && !analysisComplete) {
        await new Promise(resolve => setTimeout(resolve, 2000));

        const statusResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/status`);
        if (statusResponse.ok()) {
          const statusData = await statusResponse.json();
          const status = statusData.data.status;

          console.log(`📊 Enhanced analysis status: ${status}`);

          if (status === 'COMPLETED') {
            analysisComplete = true;

            // Get detailed results
            const resultsResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/results`);
            expect(resultsResponse.ok()).toBeTruthy();

            finalResults = await resultsResponse.json();
            break;
          } else if (status === 'FAILED') {
            throw new Error(`Enhanced analysis failed: ${statusData.data.error}`);
          }
        }

        attempts++;
      }

      // Verify enhanced analysis results
      expect(analysisComplete).toBeTruthy();
      expect(finalResults).toBeTruthy();
      expect(finalResults.data).toHaveProperty('results');

      const results = finalResults.data.results;

      // Verify optimization was applied
      expect(results).toHaveProperty('optimizationApplied');
      if (results.optimizationApplied) {
        console.log(`✅ Prompt optimization was applied`);
      }

      // Verify reasoning chain was used
      expect(results).toHaveProperty('reasoningChainId');
      if (results.reasoningChainId) {
        console.log(`✅ Reasoning chain used: ${results.reasoningChainId}`);
      }

      // Verify A/B test variant
      expect(results).toHaveProperty('abTestVariant');
      if (results.abTestVariant) {
        console.log(`✅ A/B test variant: ${results.abTestVariant}`);
      }

      // Verify enhanced content analysis
      expect(results).toHaveProperty('content');
      expect(results).toHaveProperty('measurements');
      expect(results).toHaveProperty('cabinets');
      expect(results).toHaveProperty('materials');
      expect(results).toHaveProperty('hardware');

      // Verify quality metrics
      expect(results).toHaveProperty('confidence');
      expect(results).toHaveProperty('qualityScore');
      expect(results.confidence).toBeGreaterThan(0);
      expect(results.qualityScore).toBeGreaterThan(0);

      console.log(`✅ Enhanced analysis completed successfully`);
      console.log(`✅ Confidence: ${results.confidence}`);
      console.log(`✅ Quality Score: ${results.qualityScore}`);
      console.log(`✅ Cabinets found: ${results.cabinets?.length || 0}`);
      console.log(`✅ Materials identified: ${results.materials?.length || 0}`);
      console.log(`✅ Hardware detected: ${results.hardware?.length || 0}`);
    });

    test('should validate service health and monitoring', async ({ request }) => {
      // Check advanced services health
      const healthResponse = await request.get('http://localhost:3001/api/health/advanced-services');
      expect(healthResponse.ok()).toBeTruthy();

      const healthData = await healthResponse.json();
      expect(healthData.success).toBe(true);
      expect(healthData.data).toHaveProperty('services');

      const services = healthData.data.services;
      expect(services).toHaveProperty('promptOptimization');
      expect(services).toHaveProperty('abTesting');
      expect(services).toHaveProperty('reasoning');
      expect(services).toHaveProperty('enhancedPdfProcessor');

      // Verify all services are healthy
      Object.keys(services).forEach(serviceName => {
        expect(services[serviceName].status).toBe('healthy');
        console.log(`✅ ${serviceName} service: ${services[serviceName].status}`);
      });

      // Check service metrics
      const metricsResponse = await request.get('http://localhost:3001/api/metrics/advanced-services');
      expect(metricsResponse.ok()).toBeTruthy();

      const metricsData = await metricsResponse.json();
      expect(metricsData.success).toBe(true);
      expect(metricsData.data).toHaveProperty('metrics');

      console.log(`✅ Advanced services metrics collected`);
    });

    test('should handle concurrent enhanced analyses', async ({ request }) => {
      // Start multiple enhanced analyses concurrently
      const concurrentAnalyses = [];
      const numConcurrent = 3;

      for (let i = 0; i < numConcurrent; i++) {
        const uploadPromise = request.post('http://localhost:3001/api/analysis/upload', {
          multipart: {
            file: {
              name: `kitchen-design-test-${i}.pdf`,
              mimeType: 'application/pdf',
              buffer: fs.readFileSync(testPdfPath),
            },
            useGPT4o: i % 2 === 0 ? 'true' : 'false', // Alternate models
            useReasoning: 'true',
            focusOnMaterials: 'true'
          },
        });

        concurrentAnalyses.push(uploadPromise);
      }

      // Wait for all uploads to complete
      const uploadResults = await Promise.all(concurrentAnalyses);

      // Verify all uploads succeeded
      uploadResults.forEach((response, index) => {
        expect(response.ok()).toBeTruthy();
        console.log(`✅ Concurrent analysis ${index + 1} started`);
      });

      // Get analysis IDs
      const analysisIds = await Promise.all(
        uploadResults.map(async (response) => {
          const data = await response.json();
          return data.data.analysisId;
        })
      );

      console.log(`✅ Started ${analysisIds.length} concurrent enhanced analyses`);

      // Monitor queue status
      const queueResponse = await request.get('http://localhost:3001/api/analysis/queue/status');
      expect(queueResponse.ok()).toBeTruthy();

      const queueData = await queueResponse.json();
      expect(queueData.data.queue.queued + queueData.data.queue.processing).toBeGreaterThanOrEqual(numConcurrent);

      console.log(`✅ Queue status: ${JSON.stringify(queueData.data.queue)}`);
    });
  });

  test.describe('Cross-Service Integration', () => {
    test('should demonstrate service interaction and data flow', async ({ request }) => {
      // Test the complete flow: Optimization -> A/B Testing -> Reasoning -> Enhanced Processing

      // 1. Create optimized prompt
      const optimizationResponse = await request.post('http://localhost:3001/api/optimization/optimize', {
        data: {
          originalPrompt: 'Analyze kitchen cabinets',
          context: {
            analysisType: 'kitchen_analysis',
            targetMetrics: { minAccuracy: 0.9 }
          }
        }
      });

      expect(optimizationResponse.ok()).toBeTruthy();
      const optimizedPrompt = (await optimizationResponse.json()).data.optimizedPrompt;

      // 2. Use optimized prompt in A/B test
      const abTestResponse = await request.post('http://localhost:3001/api/ab-tests/create', {
        data: {
          name: 'Optimized Prompt Test',
          description: 'Testing optimized vs original prompt',
          variants: [
            { id: 'original', name: 'Original', prompt: 'Analyze kitchen cabinets', weight: 0.5 },
            { id: 'optimized', name: 'Optimized', prompt: optimizedPrompt, weight: 0.5 }
          ],
          trafficAllocation: { 'original': 0.5, 'optimized': 0.5 },
          targetMetrics: { primary: 'accuracy', secondary: ['confidence'] },
          minimumSampleSize: 5,
          confidenceLevel: 0.95,
          startDate: new Date()
        }
      });

      expect(abTestResponse.ok()).toBeTruthy();
      const testId = (await abTestResponse.json()).data.testId;

      // 3. Start A/B test
      await request.post(`http://localhost:3001/api/ab-tests/${testId}/start`);

      // 4. Get variant for analysis
      const variantResponse = await request.get(`http://localhost:3001/api/ab-tests/variant/kitchen_analysis/integration_test_123`);
      expect(variantResponse.ok()).toBeTruthy();

      // 5. Start reasoning chain with variant
      const reasoningResponse = await request.post('http://localhost:3001/api/reasoning/start', {
        data: {
          analysisId: 'integration_test_123',
          context: {
            analysisType: 'kitchen_analysis',
            prompt: (await variantResponse.json()).data.prompt
          }
        }
      });

      expect(reasoningResponse.ok()).toBeTruthy();
      const chainId = (await reasoningResponse.json()).data.chainId;

      console.log(`✅ Cross-service integration test completed`);
      console.log(`✅ Optimization -> A/B Test (${testId}) -> Reasoning (${chainId})`);
    });
  });
});
