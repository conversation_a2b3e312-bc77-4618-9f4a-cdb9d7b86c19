import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

test.describe('Azure OpenAI Integration', () => {
  const testPdfPath = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
  
  test('should verify Azure OpenAI configuration is active', async ({ request }) => {
    const response = await request.get('http://localhost:3001/api/health/detailed');
    const data = await response.json();
    
    const openaiService = data.data.services.openai;
    expect(openaiService.configured).toBe(true);
    expect(openaiService.type).toBe('azure');
    expect(openaiService.endpoint).toContain('openai.azure.com');
    
    console.log(`Azure OpenAI Endpoint: ${openaiService.endpoint}`);
  });

  test('should make real Azure OpenAI API calls (not mock responses)', async ({ request }) => {
    // Upload a file to trigger real API analysis
    const uploadResponse = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        useGPT4o: 'true',
        useReasoning: 'true',
      },
    });

    expect(uploadResponse.ok()).toBeTruthy();
    const uploadData = await uploadResponse.json();
    const analysisId = uploadData.data.analysisId;

    // Monitor the analysis progress
    let attempts = 0;
    const maxAttempts = 60; // 2 minutes with 2-second intervals
    let analysisComplete = false;
    let finalResults = null;

    while (attempts < maxAttempts && !analysisComplete) {
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      
      const statusResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/status`);
      if (statusResponse.ok()) {
        const statusData = await statusResponse.json();
        console.log(`Analysis status: ${statusData.data.status}`);
        
        if (statusData.data.status === 'completed') {
          analysisComplete = true;
          
          // Get the results
          const resultsResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/results`);
          if (resultsResponse.ok()) {
            finalResults = await resultsResponse.json();
          }
        } else if (statusData.data.status === 'failed') {
          throw new Error(`Analysis failed: ${statusData.data.error || 'Unknown error'}`);
        }
      }
      
      attempts++;
    }

    // Verify we got real results (not mock data)
    expect(analysisComplete).toBe(true);
    expect(finalResults).toBeTruthy();
    expect(finalResults.success).toBe(true);
    expect(finalResults.data).toBeTruthy();
    
    // Check for real analysis content (not mock patterns)
    const results = finalResults.data;
    expect(results).toHaveProperty('analysis');
    expect(results).toHaveProperty('model');
    expect(results).toHaveProperty('usage');
    
    // Verify it's not mock data by checking for real usage statistics
    expect(results.usage.total_tokens).toBeGreaterThan(0);
    expect(results.usage.prompt_tokens).toBeGreaterThan(0);
    expect(results.usage.completion_tokens).toBeGreaterThan(0);
    
    console.log(`Real Azure OpenAI analysis completed with ${results.usage.total_tokens} tokens`);
  });

  test('should test GPT-4o model specifically', async ({ request }) => {
    const uploadResponse = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        useGPT4o: 'true',  // Explicitly use GPT-4o
        useReasoning: 'false', // Disable reasoning to test just GPT-4o
      },
    });

    expect(uploadResponse.ok()).toBeTruthy();
    const uploadData = await uploadResponse.json();
    const analysisId = uploadData.data.analysisId;

    // Wait for completion and verify model used
    let attempts = 0;
    const maxAttempts = 30;
    
    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const statusResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/status`);
      if (statusResponse.ok()) {
        const statusData = await statusResponse.json();

        if (statusData.data.status === 'completed') {
          const resultsResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/results`);
          const results = await resultsResponse.json();
          
          // Verify GPT-4o was used
          expect(results.data.model).toContain('gpt-4o');
          expect(results.data.model).not.toContain('mini');
          
          console.log(`GPT-4o model confirmed: ${results.data.model}`);
          return;
        }
      }
      attempts++;
    }
    
    throw new Error('GPT-4o analysis did not complete within timeout');
  });

  test('should test GPT-4o-mini model specifically', async ({ request }) => {
    const uploadResponse = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        useGPT4o: 'false',  // Use GPT-4o-mini
        useReasoning: 'false',
      },
    });

    expect(uploadResponse.ok()).toBeTruthy();
    const uploadData = await uploadResponse.json();
    const analysisId = uploadData.data.analysisId;

    // Wait for completion and verify model used
    let attempts = 0;
    const maxAttempts = 30;
    
    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const statusResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/status`);
      if (statusResponse.ok()) {
        const statusData = await statusResponse.json();

        if (statusData.data.status === 'completed') {
          const resultsResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/results`);
          const results = await resultsResponse.json();
          
          // Verify GPT-4o-mini was used
          expect(results.data.model).toContain('gpt-4o-mini');
          
          console.log(`GPT-4o-mini model confirmed: ${results.data.model}`);
          return;
        }
      }
      attempts++;
    }
    
    throw new Error('GPT-4o-mini analysis did not complete within timeout');
  });

  test('should test dual-model reasoning workflow', async ({ request }) => {
    const uploadResponse = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        useGPT4o: 'true',
        useReasoning: 'true', // Enable dual-model workflow
      },
    });

    expect(uploadResponse.ok()).toBeTruthy();
    const uploadData = await uploadResponse.json();
    const analysisId = uploadData.data.analysisId;

    // Wait for completion
    let attempts = 0;
    const maxAttempts = 60; // Longer timeout for dual-model
    
    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const statusResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/status`);
      if (statusResponse.ok()) {
        const statusData = await statusResponse.json();

        if (statusData.data.status === 'completed') {
          const resultsResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/results`);
          const results = await resultsResponse.json();
          
          // Verify both models were used in the workflow
          expect(results.data).toHaveProperty('primaryAnalysis');
          expect(results.data).toHaveProperty('reasoning');
          
          // Check that both analyses have real content
          expect(results.data.primaryAnalysis.content.length).toBeGreaterThan(100);
          expect(results.data.reasoning.content.length).toBeGreaterThan(50);
          
          console.log('Dual-model reasoning workflow completed successfully');
          return;
        }
      }
      attempts++;
    }
    
    throw new Error('Dual-model analysis did not complete within timeout');
  });
});
