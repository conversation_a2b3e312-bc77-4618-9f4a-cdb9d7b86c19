import { test, expect } from '@playwright/test';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

test.describe('Intelligent Measurement System', () => {
  const testPdfPath = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
  const testPngPath = path.join(__dirname, '../fixtures/kitchen-design-test.png');

  test.beforeEach(async () => {
    // Ensure test files exist
    if (!fs.existsSync(testPdfPath)) {
      throw new Error(`Test PDF file not found: ${testPdfPath}`);
    }
    if (!fs.existsSync(testPngPath)) {
      throw new Error(`Test PNG file not found: ${testPngPath}`);
    }
  });

  test('should provide intelligent measurement API endpoint', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/intelligent-measurement', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        enableAutoScale: 'true',
        enableLayoutAnalysis: 'true',
        enableSpaceOptimization: 'true',
        enableMeasurementValidation: 'true',
        accuracyThreshold: '0.85',
        optimizationLevel: 'ADVANCED'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('intelligentMeasurement');
    
    const measurement = data.data.intelligentMeasurement;
    expect(measurement).toHaveProperty('scaleDetection');
    expect(measurement).toHaveProperty('roomLayout');
    expect(measurement).toHaveProperty('spaceOptimization');
    expect(measurement).toHaveProperty('measurementValidation');
    expect(measurement).toHaveProperty('processingMetrics');

    console.log('✅ Intelligent measurement analysis completed');
    console.log(`📊 Confidence Score: ${measurement.processingMetrics.confidenceScore}`);
    console.log(`🔍 Features Detected: ${measurement.processingMetrics.featuresDetected.join(', ')}`);
  });

  test('should detect auto-scale from dimension lines', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/intelligent-measurement', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        enableAutoScale: 'true',
        enableLayoutAnalysis: 'false',
        enableSpaceOptimization: 'false',
        enableMeasurementValidation: 'false'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    const scaleDetection = data.data.intelligentMeasurement.scaleDetection;
    
    expect(scaleDetection).toHaveProperty('detectedScales');
    expect(scaleDetection).toHaveProperty('primaryScale');
    expect(scaleDetection).toHaveProperty('confidence');
    
    if (scaleDetection.detectedScales.length > 0) {
      const scale = scaleDetection.detectedScales[0];
      expect(scale).toHaveProperty('type');
      expect(scale).toHaveProperty('value');
      expect(scale).toHaveProperty('unit');
      expect(scale).toHaveProperty('pixelLength');
      expect(scale).toHaveProperty('confidence');
      
      console.log(`✅ Scale detected: ${scale.value}${scale.unit} = ${scale.pixelLength}px`);
      console.log(`📏 Scale type: ${scale.type}`);
      console.log(`🎯 Confidence: ${scale.confidence}`);
    }
  });

  test('should analyze room layout and work triangle', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/intelligent-measurement', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        enableAutoScale: 'false',
        enableLayoutAnalysis: 'true',
        enableSpaceOptimization: 'false',
        enableMeasurementValidation: 'false'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    const roomLayout = data.data.intelligentMeasurement.roomLayout;
    
    expect(roomLayout).toHaveProperty('boundaries');
    expect(roomLayout).toHaveProperty('workTriangle');
    expect(roomLayout).toHaveProperty('trafficFlow');
    expect(roomLayout).toHaveProperty('zones');
    
    // Validate work triangle
    const workTriangle = roomLayout.workTriangle;
    expect(workTriangle).toHaveProperty('sink');
    expect(workTriangle).toHaveProperty('stove');
    expect(workTriangle).toHaveProperty('refrigerator');
    expect(workTriangle).toHaveProperty('efficiency');
    
    console.log(`✅ Work triangle efficiency: ${workTriangle.efficiency}`);
    console.log(`🚰 Sink position: (${workTriangle.sink.x}, ${workTriangle.sink.y})`);
    console.log(`🔥 Stove position: (${workTriangle.stove.x}, ${workTriangle.stove.y})`);
    console.log(`❄️ Refrigerator position: (${workTriangle.refrigerator.x}, ${workTriangle.refrigerator.y})`);
    
    // Validate traffic flow
    const trafficFlow = roomLayout.trafficFlow;
    expect(trafficFlow).toHaveProperty('primaryPaths');
    expect(trafficFlow).toHaveProperty('bottlenecks');
    expect(trafficFlow).toHaveProperty('clearanceIssues');
    
    console.log(`🚶 Primary paths: ${trafficFlow.primaryPaths.length}`);
    console.log(`⚠️ Bottlenecks: ${trafficFlow.bottlenecks.length}`);
    console.log(`🚧 Clearance issues: ${trafficFlow.clearanceIssues.length}`);
  });

  test('should generate space optimization recommendations', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/intelligent-measurement', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        enableAutoScale: 'false',
        enableLayoutAnalysis: 'true',
        enableSpaceOptimization: 'true',
        enableMeasurementValidation: 'false',
        optimizationLevel: 'COMPREHENSIVE'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    const spaceOptimization = data.data.intelligentMeasurement.spaceOptimization;
    
    expect(spaceOptimization).toHaveProperty('currentEfficiency');
    expect(spaceOptimization).toHaveProperty('recommendations');
    
    console.log(`📊 Current efficiency: ${spaceOptimization.currentEfficiency}`);
    console.log(`💡 Recommendations: ${spaceOptimization.recommendations.length}`);
    
    if (spaceOptimization.recommendations.length > 0) {
      const recommendation = spaceOptimization.recommendations[0];
      expect(recommendation).toHaveProperty('type');
      expect(recommendation).toHaveProperty('priority');
      expect(recommendation).toHaveProperty('description');
      expect(recommendation).toHaveProperty('expectedImprovement');
      expect(recommendation).toHaveProperty('implementationComplexity');
      
      console.log(`🎯 Top recommendation: ${recommendation.description}`);
      console.log(`⚡ Priority: ${recommendation.priority}`);
      console.log(`📈 Expected improvement: ${recommendation.expectedImprovement}`);
    }
    
    // Check for alternative layouts if comprehensive mode
    if (spaceOptimization.alternativeLayouts) {
      expect(Array.isArray(spaceOptimization.alternativeLayouts)).toBeTruthy();
      console.log(`🏗️ Alternative layouts: ${spaceOptimization.alternativeLayouts.length}`);
      
      if (spaceOptimization.alternativeLayouts.length > 0) {
        const layout = spaceOptimization.alternativeLayouts[0];
        expect(layout).toHaveProperty('name');
        expect(layout).toHaveProperty('description');
        expect(layout).toHaveProperty('efficiency');
        expect(layout).toHaveProperty('changes');
        
        console.log(`🏠 Layout option: ${layout.name} (${layout.efficiency} efficiency)`);
      }
    }
  });

  test('should validate measurements across sources', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/intelligent-measurement', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        enableAutoScale: 'true',
        enableLayoutAnalysis: 'false',
        enableSpaceOptimization: 'false',
        enableMeasurementValidation: 'true'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    const measurementValidation = data.data.intelligentMeasurement.measurementValidation;
    
    expect(measurementValidation).toHaveProperty('accuracy');
    expect(measurementValidation).toHaveProperty('crossReferences');
    expect(measurementValidation).toHaveProperty('inconsistencies');
    
    console.log(`🎯 Measurement accuracy: ${measurementValidation.accuracy}`);
    console.log(`🔗 Cross-references: ${measurementValidation.crossReferences.length}`);
    console.log(`⚠️ Inconsistencies: ${measurementValidation.inconsistencies.length}`);
    
    if (measurementValidation.crossReferences.length > 0) {
      const crossRef = measurementValidation.crossReferences[0];
      expect(crossRef).toHaveProperty('source1');
      expect(crossRef).toHaveProperty('source2');
      expect(crossRef).toHaveProperty('variance');
      expect(crossRef).toHaveProperty('confidence');
      
      console.log(`📊 Cross-reference variance: ${(crossRef.variance * 100).toFixed(1)}%`);
    }
    
    if (measurementValidation.inconsistencies.length > 0) {
      const inconsistency = measurementValidation.inconsistencies[0];
      expect(inconsistency).toHaveProperty('description');
      expect(inconsistency).toHaveProperty('severity');
      expect(inconsistency).toHaveProperty('suggestions');
      
      console.log(`⚠️ Inconsistency: ${inconsistency.description}`);
      console.log(`🚨 Severity: ${inconsistency.severity}`);
    }
  });

  test('should handle PNG files for measurement analysis', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/intelligent-measurement', {
      multipart: {
        file: {
          name: 'kitchen-design-test.png',
          mimeType: 'image/png',
          buffer: fs.readFileSync(testPngPath),
        },
        enableAutoScale: 'true',
        enableLayoutAnalysis: 'true'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('intelligentMeasurement');
    
    console.log('✅ PNG file measurement analysis completed successfully');
  });

  test('should integrate with existing analysis pipeline', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        useGPT4o: 'true',
        useReasoning: 'true',
        enableIntelligentMeasurement: 'true',
        enableAutoScale: 'true',
        enableLayoutAnalysis: 'true',
        enableSpaceOptimization: 'true'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('analysisId');
    
    console.log('✅ Intelligent measurement integrated with standard analysis pipeline');
  });

  test('should handle measurement analysis errors gracefully', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/intelligent-measurement', {
      multipart: {
        file: {
          name: 'invalid-file.txt',
          mimeType: 'text/plain',
          buffer: Buffer.from('This is not a valid image or PDF file'),
        }
      },
    });

    // Should handle error gracefully
    if (!response.ok()) {
      const data = await response.json();
      expect(data).toHaveProperty('success', false);
      console.log('✅ Invalid file type properly rejected');
    } else {
      // If it doesn't reject, it should at least not crash
      const data = await response.json();
      expect(data).toHaveProperty('success');
      console.log('✅ Error handled gracefully without crashing');
    }
  });
});
