import { test, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { TestHelpers } from '../utils/test-helpers';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

test.describe('Cabinet Reconstruction Service - Phase 3.1 Refactoring Validation', () => {
  const testPdfPath = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
  const testPngPath = path.join(__dirname, '../fixtures/kitchen-design-test.png');

  test.beforeEach(async () => {
    // Ensure test files exist
    if (!fs.existsSync(testPdfPath)) {
      console.warn('⚠️ Test PDF not found, creating placeholder');
      fs.writeFileSync(testPdfPath, Buffer.from('%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n174\n%%EOF'));
    }
  });

  test('should maintain backward compatibility for main API endpoint', async ({ request }) => {
    console.log('🧪 Testing backward compatibility for 3D reconstruction API...');
    
    const response = await request.post('http://localhost:3001/api/analysis/3d-reconstruction', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        enableDepthEstimation: 'true',
        spatialResolution: 'HIGH',
        includeHardwarePositioning: 'true',
        optimizeForAccuracy: 'true',
        generateWireframe: 'false'
      },
      timeout: 60000
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('analysisId');
    expect(data.data).toHaveProperty('reconstruction3D');
    
    // Verify response structure matches original API contract
    const reconstruction = data.data.reconstruction3D;
    expect(reconstruction).toHaveProperty('cabinets');
    expect(reconstruction).toHaveProperty('spatialRelationships');
    expect(reconstruction).toHaveProperty('roomDimensions');
    expect(reconstruction).toHaveProperty('reconstructionMetrics');
    expect(reconstruction).toHaveProperty('confidence');
    
    // Verify confidence structure
    expect(reconstruction.confidence).toHaveProperty('overall');
    expect(reconstruction.confidence).toHaveProperty('spatialMapping');
    expect(reconstruction.confidence).toHaveProperty('dimensionAccuracy');
    expect(reconstruction.confidence).toHaveProperty('cabinetPositioning');
    
    console.log('✅ API endpoint maintains backward compatibility');
    console.log(`📊 Generated ${reconstruction.cabinets.length} cabinet models`);
    console.log(`🎯 Overall confidence: ${reconstruction.confidence.overall}`);
  });

  test('should integrate correctly with enhanced analysis pipeline', async ({ request }) => {
    console.log('🧪 Testing integration with enhanced analysis pipeline...');
    
    const response = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        useGPT4o: 'true',
        useReasoning: 'true',
        enable3DReconstruction: 'true',
        spatialResolution: 'HIGH',
        includeHardwarePositioning: 'true'
      },
      timeout: 120000
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('analysisId');
    
    console.log('✅ 3D reconstruction integrates correctly with analysis pipeline');
  });

  test('should maintain performance characteristics', async ({ request }) => {
    console.log('🧪 Testing performance characteristics...');
    
    const startTime = Date.now();
    
    const response = await request.post('http://localhost:3001/api/analysis/3d-reconstruction', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        spatialResolution: 'MEDIUM' // Use medium for faster testing
      },
      timeout: 60000
    });

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    const reconstruction = data.data.reconstruction3D;
    
    // Verify performance metrics are included
    expect(reconstruction.reconstructionMetrics).toHaveProperty('reconstructionTime');
    expect(reconstruction.reconstructionMetrics.reconstructionTime).toBeGreaterThan(0);
    
    // Performance should be reasonable (under 30 seconds for test)
    expect(responseTime).toBeLessThan(30000);
    
    console.log(`✅ Performance maintained: ${responseTime}ms total, ${reconstruction.reconstructionMetrics.reconstructionTime}ms processing`);
  });

  test('should handle error scenarios gracefully', async ({ request }) => {
    console.log('🧪 Testing error handling...');
    
    // Test with invalid file
    const response = await request.post('http://localhost:3001/api/analysis/3d-reconstruction', {
      multipart: {
        file: {
          name: 'invalid.txt',
          mimeType: 'text/plain',
          buffer: Buffer.from('This is not a valid image or PDF'),
        }
      },
      timeout: 30000
    });

    // Should handle error gracefully
    if (!response.ok()) {
      const errorData = await response.json();
      expect(errorData).toHaveProperty('success', false);
      expect(errorData).toHaveProperty('error');
      console.log('✅ Error handling works correctly');
    } else {
      console.log('ℹ️ Service accepted invalid file (may have fallback handling)');
    }
  });

  test('should support different configuration levels', async ({ request }) => {
    console.log('🧪 Testing configuration levels...');
    
    const configs = [
      { level: 'BASIC', spatialResolution: 'LOW', optimizeForAccuracy: 'false' },
      { level: 'DETAILED', spatialResolution: 'MEDIUM', optimizeForAccuracy: 'false' },
      { level: 'COMPREHENSIVE', spatialResolution: 'HIGH', optimizeForAccuracy: 'true' }
    ];

    for (const config of configs) {
      const response = await request.post('http://localhost:3001/api/analysis/3d-reconstruction', {
        multipart: {
          file: {
            name: 'kitchen-design-test.pdf',
            mimeType: 'application/pdf',
            buffer: fs.readFileSync(testPdfPath),
          },
          ...config
        },
        timeout: 60000
      });

      expect(response.ok()).toBeTruthy();
      
      const data = await response.json();
      expect(data).toHaveProperty('success', true);
      
      console.log(`✅ ${config.level} configuration works correctly`);
    }
  });

  test('should maintain WebSocket integration', async ({ page }) => {
    console.log('🧪 Testing WebSocket integration...');
    
    const helpers = new TestHelpers(page);
    await helpers.navigateToPage('/analysis');
    
    // Set up WebSocket monitoring
    await page.evaluate(() => {
      (window as any).testWebSocketMessages = [];
      if (window.io) {
        window.io.on('analysisProgress', (data: any) => {
          (window as any).testWebSocketMessages.push(data);
        });
      }
    });
    
    // Upload file and enable 3D reconstruction
    const fileInput = page.locator('input[type="file"]').first();
    await fileInput.setInputFiles(testPdfPath);
    await page.waitForTimeout(2000);
    
    // Enable 3D reconstruction if available
    const enable3DCheckbox = page.locator('input[type="checkbox"]').filter({ 
      has: page.locator('text=3D Cabinet Reconstruction') 
    });
    
    if (await enable3DCheckbox.isVisible()) {
      await enable3DCheckbox.check();
    }
    
    // Start analysis
    const analyzeButton = page.locator('button:has-text("Analyze"), button:has-text("Start")').first();
    if (await analyzeButton.isVisible()) {
      await analyzeButton.click();
      
      // Wait for WebSocket messages
      await page.waitForTimeout(10000);
      
      const messages = await page.evaluate(() => (window as any).testWebSocketMessages || []);
      
      if (messages.length > 0) {
        console.log(`✅ Received ${messages.length} WebSocket messages`);
        
        // Check for 3D reconstruction specific messages
        const reconstructionMessages = messages.filter((msg: any) => 
          msg.step && msg.step.includes('3d_reconstruction')
        );
        
        if (reconstructionMessages.length > 0) {
          console.log(`✅ 3D reconstruction WebSocket messages working: ${reconstructionMessages.length} messages`);
        }
      } else {
        console.log('ℹ️ No WebSocket messages received (may be too fast or not enabled)');
      }
    }
  });

  test('should maintain frontend 3D visualization compatibility', async ({ page }) => {
    console.log('🧪 Testing frontend 3D visualization compatibility...');
    
    const helpers = new TestHelpers(page);
    await helpers.navigateToPage('/analysis');
    
    // Upload file
    const fileInput = page.locator('input[type="file"]').first();
    await fileInput.setInputFiles(testPdfPath);
    await page.waitForTimeout(2000);
    
    // Enable 3D reconstruction
    const enable3DCheckbox = page.locator('input[type="checkbox"]').filter({ 
      has: page.locator('text=3D Cabinet Reconstruction') 
    });
    
    if (await enable3DCheckbox.isVisible()) {
      await enable3DCheckbox.check();
      console.log('✅ 3D reconstruction option available');
    }
    
    // Start analysis
    const analyzeButton = page.locator('button:has-text("Analyze"), button:has-text("Start")').first();
    if (await analyzeButton.isVisible()) {
      await analyzeButton.click();
      
      // Wait for analysis completion
      await page.waitForTimeout(30000);
      
      // Check for 3D visualization tab
      const threeDTab = page.locator('text=3D Model');
      if (await threeDTab.isVisible()) {
        await threeDTab.click();
        console.log('✅ 3D Model tab available');
        
        // Wait for 3D viewer to load
        await page.waitForTimeout(5000);
        
        // Check for 3D viewer canvas
        const canvas = page.locator('canvas');
        if (await canvas.isVisible()) {
          console.log('✅ 3D viewer canvas rendered');
        }
        
        // Check for reconstruction metrics
        const metricsCard = page.locator('text=Reconstruction Metrics');
        if (await metricsCard.isVisible()) {
          console.log('✅ Reconstruction metrics displayed');
        }
      } else {
        console.log('ℹ️ 3D Model tab not visible (analysis may not have completed)');
      }
    }
  });
});
