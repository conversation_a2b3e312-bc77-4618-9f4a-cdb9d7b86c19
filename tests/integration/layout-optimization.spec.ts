import { test, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { TestHelpers } from '../utils/test-helpers';

// ES module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Layout Optimization Tests - Priority 2 Enhanced Analysis Engine Feature 2
 * Tests the Smart Layout Optimization functionality including workflow analysis,
 * space utilization, ergonomic assessment, traffic flow analysis, and cost-benefit analysis
 */
test.describe('Layout Optimization Analysis', () => {
  const testPdfPath = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
    await helpers.initializeTestEnvironment('Layout Optimization Tests');
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should perform comprehensive layout optimization analysis', async ({ page, request }) => {
    console.log('🧪 Testing comprehensive layout optimization analysis');

    // Upload file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testPdfPath);
    
    // Wait for file validation
    await helpers.waitForFileValidation(30000);
    
    // Start analysis with layout optimization enabled
    const analyzeButton = page.locator('button:has-text("Analyze"), button:has-text("Start")').first();
    await analyzeButton.click();
    
    // Monitor analysis progress with extended timeout
    const analysisId = await helpers.monitorAnalysisProgress(page, 'Layout Optimization Analysis', 180000);
    
    // Verify analysis completion
    const resultsResponse = await request.get(`http://localhost:3001/api/analysis/${analysisId}/results`);
    expect(resultsResponse.ok()).toBeTruthy();
    
    const results = await resultsResponse.json();
    expect(results.data).toHaveProperty('measurements');
    expect(results.data).toHaveProperty('cabinets');
    
    console.log('✅ Comprehensive layout optimization analysis completed successfully');
  });

  test('should test layout optimization API endpoint directly', async ({ request }) => {
    console.log('🧪 Testing layout optimization API endpoint');

    const response = await request.post('http://localhost:3001/api/analysis/layout-optimization', {
      multipart: {
        files: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        enableWorkflowAnalysis: 'true',
        enableSpaceUtilization: 'true',
        enableErgonomicAssessment: 'true',
        enableTrafficFlowAnalysis: 'true',
        enableCostBenefitAnalysis: 'true',
        optimizationLevel: 'DETAILED',
        confidenceThreshold: '0.8',
        includeAccessibilityFeatures: 'true',
        maxLayoutAlternatives: '3'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('analysisId');
    expect(data.data).toHaveProperty('layoutOptimization');
    
    const layoutOptimization = data.data.layoutOptimization;
    
    // Verify workflow optimization
    expect(layoutOptimization).toHaveProperty('workflowOptimization');
    expect(layoutOptimization.workflowOptimization).toHaveProperty('currentWorkflow');
    expect(layoutOptimization.workflowOptimization).toHaveProperty('optimizedWorkflow');
    expect(layoutOptimization.workflowOptimization).toHaveProperty('confidence');
    
    // Verify space utilization
    expect(layoutOptimization).toHaveProperty('spaceUtilization');
    expect(layoutOptimization.spaceUtilization).toHaveProperty('currentUtilization');
    expect(layoutOptimization.spaceUtilization).toHaveProperty('optimizationOpportunities');
    expect(layoutOptimization.spaceUtilization).toHaveProperty('recommendations');
    
    // Verify ergonomic assessment
    expect(layoutOptimization).toHaveProperty('ergonomicAssessment');
    expect(layoutOptimization.ergonomicAssessment).toHaveProperty('currentErgonomics');
    expect(layoutOptimization.ergonomicAssessment).toHaveProperty('improvements');
    expect(layoutOptimization.ergonomicAssessment).toHaveProperty('accessibilityCompliance');
    
    // Verify traffic flow analysis
    expect(layoutOptimization).toHaveProperty('trafficFlowAnalysis');
    expect(layoutOptimization.trafficFlowAnalysis).toHaveProperty('currentFlow');
    expect(layoutOptimization.trafficFlowAnalysis).toHaveProperty('optimizedFlow');
    
    // Verify cost-benefit analysis
    expect(layoutOptimization).toHaveProperty('costBenefitAnalysis');
    expect(layoutOptimization.costBenefitAnalysis).toHaveProperty('totalInvestment');
    expect(layoutOptimization.costBenefitAnalysis).toHaveProperty('expectedBenefits');
    expect(layoutOptimization.costBenefitAnalysis).toHaveProperty('roi');
    
    // Verify processing metrics
    expect(layoutOptimization).toHaveProperty('processingMetrics');
    expect(layoutOptimization.processingMetrics).toHaveProperty('analysisTime');
    expect(layoutOptimization.processingMetrics).toHaveProperty('confidenceScore');
    expect(layoutOptimization.processingMetrics).toHaveProperty('featuresAnalyzed');
    
    console.log('✅ Layout optimization API endpoint test completed successfully');
  });

  test('should test layout optimization configuration endpoint', async ({ request }) => {
    console.log('🧪 Testing layout optimization configuration endpoint');

    const response = await request.get('http://localhost:3001/api/analysis/layout-optimization/config');
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('defaultConfig');
    expect(data.data).toHaveProperty('configOptions');
    
    const configOptions = data.data.configOptions;
    expect(configOptions).toHaveProperty('optimizationLevels');
    expect(configOptions).toHaveProperty('confidenceThresholds');
    expect(configOptions).toHaveProperty('maxLayoutAlternatives');
    expect(configOptions).toHaveProperty('features');
    
    // Verify feature descriptions
    expect(configOptions.features).toHaveProperty('workflowAnalysis');
    expect(configOptions.features).toHaveProperty('spaceUtilization');
    expect(configOptions.features).toHaveProperty('ergonomicAssessment');
    expect(configOptions.features).toHaveProperty('trafficFlowAnalysis');
    expect(configOptions.features).toHaveProperty('costBenefitAnalysis');
    expect(configOptions.features).toHaveProperty('accessibilityFeatures');
    
    console.log('✅ Layout optimization configuration endpoint test completed successfully');
  });

  test('should test layout optimization templates endpoint', async ({ request }) => {
    console.log('🧪 Testing layout optimization templates endpoint');

    const response = await request.get('http://localhost:3001/api/analysis/layout-optimization/templates');
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('layoutTemplates');
    expect(data.data).toHaveProperty('workflowPrinciples');
    expect(data.data).toHaveProperty('ergonomicGuidelines');
    
    const layoutTemplates = data.data.layoutTemplates;
    expect(Array.isArray(layoutTemplates)).toBeTruthy();
    expect(layoutTemplates.length).toBeGreaterThan(0);
    
    // Verify template structure
    const firstTemplate = layoutTemplates[0];
    expect(firstTemplate).toHaveProperty('id');
    expect(firstTemplate).toHaveProperty('name');
    expect(firstTemplate).toHaveProperty('description');
    expect(firstTemplate).toHaveProperty('workTriangleOptimal');
    expect(firstTemplate).toHaveProperty('bestFor');
    expect(firstTemplate).toHaveProperty('considerations');
    
    console.log('✅ Layout optimization templates endpoint test completed successfully');
  });

  test('should test batch layout optimization processing', async ({ request }) => {
    console.log('🧪 Testing batch layout optimization processing');

    // Create multiple test files for batch processing
    const files = [
      {
        name: 'kitchen-design-test-1.pdf',
        mimeType: 'application/pdf',
        buffer: fs.readFileSync(testPdfPath),
      },
      {
        name: 'kitchen-design-test-2.pdf',
        mimeType: 'application/pdf',
        buffer: fs.readFileSync(testPdfPath),
      }
    ];

    const response = await request.post('http://localhost:3001/api/analysis/layout-optimization/batch', {
      multipart: {
        files: files,
        enableWorkflowAnalysis: 'true',
        enableSpaceUtilization: 'true',
        enableErgonomicAssessment: 'true',
        enableTrafficFlowAnalysis: 'true',
        enableCostBenefitAnalysis: 'true',
        optimizationLevel: 'DETAILED',
        confidenceThreshold: '0.8'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('batchId');
    expect(data.data).toHaveProperty('results');
    expect(data.data).toHaveProperty('summary');
    
    const results = data.data.results;
    expect(Array.isArray(results)).toBeTruthy();
    expect(results.length).toBeGreaterThan(0);
    
    // Verify batch summary
    const summary = data.data.summary;
    expect(summary).toHaveProperty('totalGroups');
    expect(summary).toHaveProperty('successfulGroups');
    expect(summary).toHaveProperty('failedGroups');
    
    console.log('✅ Batch layout optimization processing test completed successfully');
  });

  test('should handle invalid file types gracefully', async ({ request }) => {
    console.log('🧪 Testing invalid file type handling');

    const invalidFile = Buffer.from('This is not a valid PDF or image file');

    const response = await request.post('http://localhost:3001/api/analysis/layout-optimization', {
      multipart: {
        files: {
          name: 'invalid-file.txt',
          mimeType: 'text/plain',
          buffer: invalidFile,
        },
        enableWorkflowAnalysis: 'true'
      },
    });

    expect(response.status()).toBe(400);
    
    const data = await response.json();
    expect(data).toHaveProperty('success', false);
    expect(data).toHaveProperty('error');
    expect(data.error).toContain('Unsupported file type');
    
    console.log('✅ Invalid file type handling test completed successfully');
  });

  test('should handle missing files gracefully', async ({ request }) => {
    console.log('🧪 Testing missing files handling');

    const response = await request.post('http://localhost:3001/api/analysis/layout-optimization', {
      multipart: {
        enableWorkflowAnalysis: 'true'
      },
    });

    expect(response.status()).toBe(400);
    
    const data = await response.json();
    expect(data).toHaveProperty('success', false);
    expect(data).toHaveProperty('error');
    expect(data.error).toContain('No files uploaded');
    
    console.log('✅ Missing files handling test completed successfully');
  });

  test('should test different optimization levels', async ({ request }) => {
    console.log('🧪 Testing different optimization levels');

    const optimizationLevels = ['BASIC', 'DETAILED', 'COMPREHENSIVE'];

    for (const level of optimizationLevels) {
      const response = await request.post('http://localhost:3001/api/analysis/layout-optimization', {
        multipart: {
          files: {
            name: 'kitchen-design-test.pdf',
            mimeType: 'application/pdf',
            buffer: fs.readFileSync(testPdfPath),
          },
          enableWorkflowAnalysis: 'true',
          enableSpaceUtilization: 'true',
          enableErgonomicAssessment: 'true',
          enableTrafficFlowAnalysis: 'true',
          enableCostBenefitAnalysis: 'true',
          optimizationLevel: level,
          confidenceThreshold: '0.8'
        },
      });

      expect(response.ok()).toBeTruthy();
      
      const data = await response.json();
      expect(data).toHaveProperty('success', true);
      expect(data.data).toHaveProperty('layoutOptimization');
      
      console.log(`✅ Optimization level ${level} test completed successfully`);
    }
  });

  test('should test confidence threshold variations', async ({ request }) => {
    console.log('🧪 Testing confidence threshold variations');

    const confidenceThresholds = [0.6, 0.7, 0.8, 0.9];

    for (const threshold of confidenceThresholds) {
      const response = await request.post('http://localhost:3001/api/analysis/layout-optimization', {
        multipart: {
          files: {
            name: 'kitchen-design-test.pdf',
            mimeType: 'application/pdf',
            buffer: fs.readFileSync(testPdfPath),
          },
          enableWorkflowAnalysis: 'true',
          enableSpaceUtilization: 'true',
          confidenceThreshold: threshold.toString()
        },
      });

      expect(response.ok()).toBeTruthy();

      const data = await response.json();
      expect(data).toHaveProperty('success', true);
      expect(data.data.layoutOptimization.processingMetrics.confidenceScore).toBeGreaterThanOrEqual(0);

      console.log(`✅ Confidence threshold ${threshold} test completed successfully`);
    }
  });

  test('should test individual feature toggles', async ({ request }) => {
    console.log('🧪 Testing individual feature toggles');

    const features = [
      { name: 'workflowAnalysis', param: 'enableWorkflowAnalysis' },
      { name: 'spaceUtilization', param: 'enableSpaceUtilization' },
      { name: 'ergonomicAssessment', param: 'enableErgonomicAssessment' },
      { name: 'trafficFlowAnalysis', param: 'enableTrafficFlowAnalysis' },
      { name: 'costBenefitAnalysis', param: 'enableCostBenefitAnalysis' }
    ];

    for (const feature of features) {
      const formData: any = {
        files: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        }
      };

      // Enable only the current feature
      formData[feature.param] = 'true';

      const response = await request.post('http://localhost:3001/api/analysis/layout-optimization', {
        multipart: formData,
      });

      expect(response.ok()).toBeTruthy();

      const data = await response.json();
      expect(data).toHaveProperty('success', true);
      expect(data.data).toHaveProperty('layoutOptimization');

      // Verify the specific feature is included in analyzed features
      const analyzedFeatures = data.data.layoutOptimization.processingMetrics.featuresAnalyzed;
      expect(Array.isArray(analyzedFeatures)).toBeTruthy();

      console.log(`✅ Feature toggle ${feature.name} test completed successfully`);
    }
  });

  test('should test accessibility features integration', async ({ request }) => {
    console.log('🧪 Testing accessibility features integration');

    const response = await request.post('http://localhost:3001/api/analysis/layout-optimization', {
      multipart: {
        files: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        enableErgonomicAssessment: 'true',
        includeAccessibilityFeatures: 'true',
        optimizationLevel: 'COMPREHENSIVE'
      },
    });

    expect(response.ok()).toBeTruthy();

    const data = await response.json();
    expect(data).toHaveProperty('success', true);

    const ergonomicAssessment = data.data.layoutOptimization.ergonomicAssessment;
    expect(ergonomicAssessment).toHaveProperty('accessibilityCompliance');
    expect(ergonomicAssessment.accessibilityCompliance).toHaveProperty('adaCompliant');
    expect(ergonomicAssessment.accessibilityCompliance).toHaveProperty('universalDesignScore');
    expect(ergonomicAssessment.accessibilityCompliance).toHaveProperty('recommendations');

    console.log('✅ Accessibility features integration test completed successfully');
  });

  test('should test WebSocket real-time updates for layout optimization', async ({ page }) => {
    console.log('🧪 Testing WebSocket real-time updates for layout optimization');

    // Set up WebSocket message listener
    const progressUpdates: any[] = [];

    await page.evaluate(() => {
      if (window.io) {
        window.io.on('analysis-progress', (data: any) => {
          if (data.step && data.step.includes('layout_optimization')) {
            (window as any).testLayoutOptimizationUpdates = (window as any).testLayoutOptimizationUpdates || [];
            (window as any).testLayoutOptimizationUpdates.push(data);
          }
        });
      }
    });

    // Upload file and start analysis
    const fileInput = page.locator('input[type="file"]').first();
    await fileInput.setInputFiles(testPdfPath);
    await page.waitForTimeout(2000);

    const analyzeButton = page.locator('button:has-text("Analyze"), button:has-text("Start")').first();
    if (await analyzeButton.isVisible()) {
      await analyzeButton.click();

      // Wait for layout optimization progress updates
      await page.waitForTimeout(10000);

      // Check if we received layout optimization specific updates
      const updates = await page.evaluate(() => (window as any).testLayoutOptimizationUpdates || []);

      if (updates.length > 0) {
        console.log(`Received ${updates.length} layout optimization progress updates via WebSocket`);
        expect(updates.length).toBeGreaterThan(0);

        // Verify update structure
        const firstUpdate = updates[0];
        expect(firstUpdate).toHaveProperty('step');
        expect(firstUpdate).toHaveProperty('progress');
        expect(firstUpdate).toHaveProperty('message');
        expect(firstUpdate.step).toContain('layout_optimization');
      } else {
        console.log('No layout optimization specific WebSocket updates received (may be too fast)');
      }
    }

    console.log('✅ WebSocket real-time updates test completed successfully');
  });

  test('should test performance with large files', async ({ request }) => {
    console.log('🧪 Testing performance with large files');

    // Create a larger test file by duplicating content
    const originalBuffer = fs.readFileSync(testPdfPath);
    const largeBuffer = Buffer.concat([originalBuffer, originalBuffer, originalBuffer]);

    const startTime = Date.now();

    const response = await request.post('http://localhost:3001/api/analysis/layout-optimization', {
      multipart: {
        files: {
          name: 'kitchen-design-large.pdf',
          mimeType: 'application/pdf',
          buffer: largeBuffer,
        },
        enableWorkflowAnalysis: 'true',
        enableSpaceUtilization: 'true',
        optimizationLevel: 'BASIC' // Use basic level for performance test
      },
    });

    const endTime = Date.now();
    const processingTime = endTime - startTime;

    expect(response.ok()).toBeTruthy();

    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('layoutOptimization');

    // Verify processing time is reasonable (under 60 seconds)
    expect(processingTime).toBeLessThan(60000);

    console.log(`✅ Large file performance test completed in ${processingTime}ms`);
  });

  test('should integrate with existing analysis pipeline', async ({ request }) => {
    console.log('🧪 Testing integration with existing analysis pipeline');

    const response = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        useGPT4o: 'true',
        useReasoning: 'true',
        enableLayoutOptimization: 'true', // This should trigger layout optimization
        enableWorkflowAnalysis: 'true',
        enableSpaceUtilization: 'true',
        enableErgonomicAssessment: 'true'
      },
    });

    expect(response.ok()).toBeTruthy();

    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('analysisId');

    // The analysis should include layout optimization data when enabled
    console.log('✅ Integration with existing analysis pipeline test completed successfully');
  });
});
