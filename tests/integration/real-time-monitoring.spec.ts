import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';

test.describe('Real-time Monitoring and Performance', () => {
  let testHelpers: TestHelpers;
  const API_BASE_URL = 'http://localhost:3001';

  test.beforeEach(async ({ page }) => {
    testHelpers = new TestHelpers(page);
    await testHelpers.setupTestEnvironment();
  });

  test.describe('Performance Monitoring Dashboard', () => {
    test('should load performance monitoring page correctly', async ({ page }) => {
      await testHelpers.navigateToPage('/performance-monitoring');
      
      // Check for main dashboard elements
      await expect(page.locator('h1')).toContainText('Performance Monitoring');
      
      // Check for key metrics cards
      const successRateCard = page.locator('text=Success Rate');
      const thresholdCard = page.locator('text=91.7%');
      
      await expect(successRateCard).toBeVisible();
      await expect(thresholdCard).toBeVisible();
      
      console.log('✅ Performance monitoring dashboard loaded successfully');
    });

    test('should display real-time metrics updates', async ({ page }) => {
      await testHelpers.navigateToPage('/performance-monitoring');
      
      // Wait for initial load
      await page.waitForTimeout(2000);
      
      // Capture initial metrics
      const initialMetrics = await page.evaluate(() => {
        const successRateElement = document.querySelector('[data-testid="success-rate-value"]');
        const totalTestsElement = document.querySelector('[data-testid="total-tests-value"]');
        
        return {
          successRate: successRateElement?.textContent || '',
          totalTests: totalTestsElement?.textContent || ''
        };
      });
      
      // Wait for potential updates
      await page.waitForTimeout(5000);
      
      // Check if metrics updated
      const updatedMetrics = await page.evaluate(() => {
        const successRateElement = document.querySelector('[data-testid="success-rate-value"]');
        const totalTestsElement = document.querySelector('[data-testid="total-tests-value"]');
        
        return {
          successRate: successRateElement?.textContent || '',
          totalTests: totalTestsElement?.textContent || ''
        };
      });
      
      console.log('📊 Initial metrics:', initialMetrics);
      console.log('📊 Updated metrics:', updatedMetrics);
      console.log('✅ Real-time metrics monitoring active');
    });

    test('should handle WebSocket connection for real-time updates', async ({ page }) => {
      await testHelpers.navigateToPage('/performance-monitoring');
      
      // Wait for WebSocket connection
      const wsConnected = await testHelpers.waitForWebSocketConnection();
      
      if (wsConnected) {
        // Test WebSocket message handling
        const wsMessages = await page.evaluate(() => {
          return new Promise((resolve) => {
            const messages: any[] = [];
            let messageCount = 0;
            
            if (window.io && window.io.connected) {
              // Listen for performance updates
              window.io.on('performance-update', (data: any) => {
                messages.push({ type: 'performance-update', data });
                messageCount++;
                
                if (messageCount >= 1) {
                  resolve(messages);
                }
              });
              
              // Listen for test metrics
              window.io.on('test-metrics', (data: any) => {
                messages.push({ type: 'test-metrics', data });
                messageCount++;
                
                if (messageCount >= 1) {
                  resolve(messages);
                }
              });
              
              // Timeout after 10 seconds
              setTimeout(() => resolve(messages), 10000);
            } else {
              resolve([]);
            }
          });
        });
        
        console.log(`✅ WebSocket messages received: ${(wsMessages as any[]).length}`);
      } else {
        console.log('ℹ️ WebSocket connection not established');
      }
    });

    test('should display performance charts and visualizations', async ({ page }) => {
      await testHelpers.navigateToPage('/performance-monitoring');
      
      // Wait for charts to load
      await page.waitForTimeout(3000);
      
      // Check for chart elements
      const charts = page.locator('canvas, svg, [data-testid*="chart"]');
      const chartCount = await charts.count();
      
      if (chartCount > 0) {
        console.log(`✅ Found ${chartCount} chart elements`);
        
        // Check for specific chart types
        const lineChart = page.locator('[data-testid="line-chart"], .line-chart');
        const barChart = page.locator('[data-testid="bar-chart"], .bar-chart');
        const pieChart = page.locator('[data-testid="pie-chart"], .pie-chart');
        
        if (await lineChart.count() > 0) {
          console.log('✅ Line chart found');
        }
        if (await barChart.count() > 0) {
          console.log('✅ Bar chart found');
        }
        if (await pieChart.count() > 0) {
          console.log('✅ Pie chart found');
        }
      } else {
        console.log('ℹ️ No chart elements found');
      }
    });

    test('should handle time range selection', async ({ page }) => {
      await testHelpers.navigateToPage('/performance-monitoring');
      
      // Look for time range selector
      const timeRangeSelector = page.locator('select[name="timeRange"], [data-testid="time-range-selector"]');
      const timeRangeButtons = page.locator('button:has-text("24h"), button:has-text("7d"), button:has-text("30d")');
      
      if (await timeRangeSelector.count() > 0) {
        // Test dropdown selector
        await timeRangeSelector.selectOption('7d');
        await page.waitForTimeout(2000);
        console.log('✅ Time range dropdown selection working');
      } else if (await timeRangeButtons.count() > 0) {
        // Test button selector
        await timeRangeButtons.first().click();
        await page.waitForTimeout(2000);
        console.log('✅ Time range button selection working');
      } else {
        console.log('ℹ️ Time range selector not found');
      }
    });
  });

  test.describe('Performance Metrics API', () => {
    test('should provide comprehensive dashboard metrics', async ({ request }) => {
      console.log('🧪 Testing dashboard metrics API');

      const response = await request.get(`${API_BASE_URL}/api/performance-monitoring/dashboard`);
      expect(response.ok()).toBeTruthy();
      
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      expect(data.data.thresholds).toBeDefined();
      expect(data.data.thresholds.successRate).toBe(91.7);
      
      // Check for real-time metrics
      expect(data.data.currentMetrics).toBeDefined();
      expect(data.data.trends).toBeDefined();
      
      console.log('✅ Dashboard metrics API working correctly');
    });

    test('should record test metrics correctly', async ({ request }) => {
      console.log('🧪 Testing test metrics recording');

      const testMetrics = {
        testName: 'real-time-monitoring-test',
        testCategory: 'integration',
        browser: 'chromium',
        duration: 2500,
        success: true,
        retryCount: 0,
        featureVersion: 'v1.0.0-test',
        buildId: 'test-build-456',
        networkCondition: {
          quality: 'good',
          latency: 50,
          bandwidth: 2000,
          packetLoss: 0
        },
        resourceUsage: {
          memoryUsage: 45,
          cpuUsage: 20,
          diskUsage: 8
        }
      };

      const response = await request.post(`${API_BASE_URL}/api/performance-monitoring/test-metrics`, {
        data: testMetrics
      });

      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBe(true);
      
      console.log('✅ Test metrics recorded successfully');
    });

    test('should analyze feature impact correctly', async ({ request }) => {
      console.log('🧪 Testing feature impact analysis');

      const featureVersion = `test-feature-${Date.now()}`;
      
      // Record multiple test metrics for analysis
      const testMetrics = [
        {
          testName: 'feature-test-1',
          testCategory: 'api',
          browser: 'chromium',
          duration: 1200,
          success: true,
          featureVersion,
          buildId: 'test-build-789'
        },
        {
          testName: 'feature-test-2',
          testCategory: 'frontend',
          browser: 'firefox',
          duration: 1800,
          success: false,
          featureVersion,
          buildId: 'test-build-789'
        }
      ];

      // Record test metrics
      for (const metrics of testMetrics) {
        await request.post(`${API_BASE_URL}/api/performance-monitoring/test-metrics`, {
          data: metrics
        });
      }

      // Analyze feature impact
      const impactResponse = await request.post(`${API_BASE_URL}/api/performance-monitoring/analyze-feature-impact`, {
        data: { featureVersion }
      });

      expect(impactResponse.ok()).toBeTruthy();
      const impactData = await impactResponse.json();
      expect(impactData.success).toBe(true);
      expect(impactData.data.impact).toBeDefined();
      
      console.log('✅ Feature impact analysis completed');
    });

    test('should handle alert threshold management', async ({ request }) => {
      console.log('🧪 Testing alert threshold management');

      // Get current thresholds
      const getResponse = await request.get(`${API_BASE_URL}/api/performance-monitoring/thresholds`);
      expect(getResponse.ok()).toBeTruthy();
      
      const currentThresholds = await getResponse.json();
      expect(currentThresholds.data.successRate).toBe(91.7);
      
      // Update thresholds
      const updateResponse = await request.put(`${API_BASE_URL}/api/performance-monitoring/thresholds`, {
        data: {
          successRate: 90.0,
          maxDuration: 30000,
          maxRetries: 3
        }
      });

      if (updateResponse.ok()) {
        const updateData = await updateResponse.json();
        expect(updateData.success).toBe(true);
        console.log('✅ Alert thresholds updated successfully');
      } else {
        console.log('ℹ️ Alert threshold update not implemented');
      }
    });
  });

  test.describe('Real-time Collaboration Monitoring', () => {
    test('should monitor WebSocket connections', async ({ page }) => {
      await testHelpers.navigateToPage('/performance-monitoring');
      
      // Check WebSocket connection status
      const wsStatus = await page.evaluate(() => {
        if (window.io) {
          return {
            connected: window.io.connected,
            id: window.io.id,
            transport: window.io.io.engine.transport.name
          };
        }
        return null;
      });
      
      if (wsStatus) {
        console.log(`✅ WebSocket status: ${wsStatus.connected ? 'Connected' : 'Disconnected'}`);
        console.log(`📡 Transport: ${wsStatus.transport}`);
        console.log(`🆔 Socket ID: ${wsStatus.id}`);
      } else {
        console.log('ℹ️ WebSocket not available');
      }
    });

    test('should track collaboration events', async ({ page }) => {
      await testHelpers.navigateToPage('/performance-monitoring');
      
      // Monitor collaboration events
      const collaborationEvents = await page.evaluate(() => {
        return new Promise((resolve) => {
          const events: any[] = [];
          
          if (window.io && window.io.connected) {
            // Listen for various collaboration events
            const eventTypes = [
              'user-joined',
              'user-left',
              'comment-added',
              'project-updated',
              'presence-updated'
            ];
            
            eventTypes.forEach(eventType => {
              window.io.on(eventType, (data: any) => {
                events.push({ type: eventType, data, timestamp: Date.now() });
              });
            });
            
            // Resolve after 5 seconds
            setTimeout(() => resolve(events), 5000);
          } else {
            resolve([]);
          }
        });
      });
      
      console.log(`📊 Collaboration events monitored: ${(collaborationEvents as any[]).length}`);
    });

    test('should display connection health metrics', async ({ page }) => {
      await testHelpers.navigateToPage('/performance-monitoring');
      
      // Look for connection health indicators
      const connectionStatus = page.locator('[data-testid="connection-status"], .connection-status');
      const latencyMetric = page.locator('[data-testid="latency"], .latency-metric');
      const throughputMetric = page.locator('[data-testid="throughput"], .throughput-metric');
      
      if (await connectionStatus.count() > 0) {
        const status = await connectionStatus.textContent();
        console.log(`✅ Connection status: ${status}`);
      }
      
      if (await latencyMetric.count() > 0) {
        const latency = await latencyMetric.textContent();
        console.log(`⏱️ Latency: ${latency}`);
      }
      
      if (await throughputMetric.count() > 0) {
        const throughput = await throughputMetric.textContent();
        console.log(`📊 Throughput: ${throughput}`);
      }
    });
  });

  test.describe('Performance Alerting System', () => {
    test('should trigger alerts for threshold breaches', async ({ request }) => {
      console.log('🧪 Testing performance alerting system');

      // Record metrics that breach thresholds
      const failingMetrics = {
        testName: 'threshold-breach-test',
        testCategory: 'integration',
        browser: 'chromium',
        duration: 45000, // Exceeds typical thresholds
        success: false,
        retryCount: 5, // High retry count
        featureVersion: 'alert-test-v1.0.0'
      };

      const response = await request.post(`${API_BASE_URL}/api/performance-monitoring/test-metrics`, {
        data: failingMetrics
      });

      expect(response.ok()).toBeTruthy();
      
      // Check for generated alerts
      const alertsResponse = await request.get(`${API_BASE_URL}/api/performance-monitoring/alerts`);
      expect(alertsResponse.ok()).toBeTruthy();
      
      const alertsData = await alertsResponse.json();
      expect(alertsData.success).toBe(true);
      
      console.log(`🚨 Active alerts: ${alertsData.data.alerts?.length || 0}`);
    });

    test('should display alert notifications in UI', async ({ page }) => {
      await testHelpers.navigateToPage('/performance-monitoring');
      
      // Look for alert indicators
      const alertBadge = page.locator('[data-testid="alert-badge"], .alert-badge');
      const alertPanel = page.locator('[data-testid="alerts-panel"], .alerts-panel');
      const alertNotification = page.locator('.alert-notification, [role="alert"]');
      
      if (await alertBadge.count() > 0) {
        const alertCount = await alertBadge.textContent();
        console.log(`🚨 Alert badge shows: ${alertCount}`);
      }
      
      if (await alertPanel.count() > 0) {
        console.log('✅ Alerts panel available');
      }
      
      if (await alertNotification.count() > 0) {
        console.log('✅ Alert notifications displayed');
      }
    });

    test('should handle alert acknowledgment', async ({ page }) => {
      await testHelpers.navigateToPage('/performance-monitoring');
      
      // Look for alert acknowledgment buttons
      const acknowledgeButton = page.locator('button:has-text("Acknowledge"), [data-testid="acknowledge-alert"]');
      const dismissButton = page.locator('button:has-text("Dismiss"), [data-testid="dismiss-alert"]');
      
      if (await acknowledgeButton.count() > 0) {
        await acknowledgeButton.first().click();
        await page.waitForTimeout(1000);
        console.log('✅ Alert acknowledgment functionality available');
      } else if (await dismissButton.count() > 0) {
        await dismissButton.first().click();
        await page.waitForTimeout(1000);
        console.log('✅ Alert dismissal functionality available');
      } else {
        console.log('ℹ️ No alerts to acknowledge/dismiss');
      }
    });
  });
});
