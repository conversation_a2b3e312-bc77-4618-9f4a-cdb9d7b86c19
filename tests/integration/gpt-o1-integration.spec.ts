import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

test.describe('GPT-o1 Integration Tests', () => {
  let helpers: TestHelpers;
  const testPdfPath = path.join(__dirname, '../fixtures/test-kitchen.pdf');

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
    await helpers.navigateToPage('/analysis');
  });

  test('should display GPT-o1 model selection option', async ({ page }) => {
    // Upload a test file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testPdfPath);
    
    // Wait for file validation
    await helpers.waitForFileValidation(10000);
    
    // Check if model selection is available
    const modelSelect = page.locator('[data-testid="model-select"], .select-trigger').first();
    await expect(modelSelect).toBeVisible();
    
    // Click to open model selection
    await modelSelect.click();
    
    // Verify GPT-o1 option is available
    const gptO1Option = page.locator('text=GPT-o1 (Advanced Reasoning)');
    await expect(gptO1Option).toBeVisible();
    
    console.log('✓ GPT-o1 model selection option is available');
  });

  test('should show GPT-o1 advanced reasoning notice when selected', async ({ page }) => {
    // Upload a test file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testPdfPath);
    
    // Wait for file validation
    await helpers.waitForFileValidation(10000);
    
    // Select GPT-o1 model
    const modelSelect = page.locator('[data-testid="model-select"], .select-trigger').first();
    await modelSelect.click();
    
    const gptO1Option = page.locator('text=GPT-o1 (Advanced Reasoning)');
    await gptO1Option.click();
    
    // Verify advanced reasoning notice appears
    const reasoningNotice = page.locator('text=Optimal for complex spatial analysis');
    await expect(reasoningNotice).toBeVisible();
    
    console.log('✓ GPT-o1 advanced reasoning notice is displayed');
  });

  test('should update processing status for GPT-o1 model', async ({ page }) => {
    // Upload a test file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testPdfPath);
    
    // Wait for file validation
    await helpers.waitForFileValidation(10000);
    
    // Select GPT-o1 model
    const modelSelect = page.locator('[data-testid="model-select"], .select-trigger').first();
    await modelSelect.click();
    
    const gptO1Option = page.locator('text=GPT-o1 (Advanced Reasoning)');
    await gptO1Option.click();
    
    // Start analysis
    const analyzeButton = page.locator('button:has-text("Start AI Analysis")');
    await analyzeButton.click();
    
    // Check processing status shows GPT-o1
    const processingStatus = page.locator('text=Processing with Azure OpenAI GPT-o1');
    await expect(processingStatus).toBeVisible({ timeout: 5000 });
    
    console.log('✓ Processing status correctly shows GPT-o1 model');
  });

  test('should handle auto-selection model option', async ({ page }) => {
    // Upload a test file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testPdfPath);
    
    // Wait for file validation
    await helpers.waitForFileValidation(10000);
    
    // Verify auto-selection is default
    const modelSelect = page.locator('[data-testid="model-select"], .select-trigger').first();
    await expect(modelSelect).toContainText('Auto-Select');
    
    // Start analysis with auto-selection
    const analyzeButton = page.locator('button:has-text("Start AI Analysis")');
    await analyzeButton.click();
    
    // Check processing status shows auto-selected model
    const processingStatus = page.locator('text=Processing with Azure OpenAI Auto-Selected Model');
    await expect(processingStatus).toBeVisible({ timeout: 5000 });
    
    console.log('✓ Auto-selection model option works correctly');
  });

  test('should enable complex reasoning for 3D reconstruction with GPT-o1', async ({ page }) => {
    // Upload a test file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testPdfPath);
    
    // Wait for file validation
    await helpers.waitForFileValidation(10000);
    
    // Enable 3D reconstruction
    const reconstruction3D = page.locator('input[type="checkbox"]').filter({ hasText: '3D Cabinet Reconstruction' });
    if (!(await reconstruction3D.isChecked())) {
      await reconstruction3D.check();
    }
    
    // Select high spatial resolution
    const spatialResolution = page.locator('select[name="spatialResolution"]');
    if (await spatialResolution.isVisible()) {
      await spatialResolution.selectOption('HIGH');
    }
    
    // Select GPT-o1 model
    const modelSelect = page.locator('[data-testid="model-select"], .select-trigger').first();
    await modelSelect.click();
    
    const gptO1Option = page.locator('text=GPT-o1 (Advanced Reasoning)');
    await gptO1Option.click();
    
    // Start analysis
    const analyzeButton = page.locator('button:has-text("Start AI Analysis")');
    await analyzeButton.click();
    
    // Monitor analysis progress
    const analysisId = await helpers.monitorAnalysisProgress(page, 'GPT-o1 3D Reconstruction');
    
    console.log(`✓ GPT-o1 3D reconstruction analysis completed: ${analysisId}`);
  });

  test('should validate GPT-o1 API connectivity', async ({ page, request }) => {
    // Test GPT-o1 API endpoint availability
    const response = await request.get('http://localhost:3001/api/health');
    expect(response.ok()).toBeTruthy();
    
    const healthData = await response.json();
    console.log('Health check response:', healthData);
    
    // Verify Azure OpenAI configuration includes GPT-o1
    const configResponse = await request.get('http://localhost:3001/api/config/models');
    if (configResponse.ok()) {
      const configData = await configResponse.json();
      console.log('Available models:', configData);
    }
    
    console.log('✓ GPT-o1 API connectivity validated');
  });
});
