import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';
import { NetworkMonitor } from '../utils/network-monitor';
import { TestBatchingManager } from '../utils/test-batching';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

test.describe('Enhanced Monitoring Demo', () => {
  let testHelpers: TestHelpers;
  let networkMonitor: NetworkMonitor;
  let batchingManager: TestBatchingManager;

  test.beforeEach(async ({ page }) => {
    testHelpers = new TestHelpers(page);
    networkMonitor = NetworkMonitor.getInstance();
    batchingManager = TestBatchingManager.getInstance();
  });

  test('should demonstrate adaptive timeout and network monitoring', async ({ page }) => {
    const testName = 'enhanced-monitoring-demo/adaptive-timeout';
    const startTime = Date.now();
    let success = false;
    let retryCount = 0;

    try {
      // Initialize test environment with comprehensive monitoring
      const envInit = await testHelpers.initializeTestEnvironment(testName);
      console.log(`🔍 Environment initialized - Network: ${envInit.networkCondition.quality}`);
      
      // Check if test should be skipped based on compatibility
      if (testHelpers.shouldSkipTest(testName)) {
        test.skip(true, 'Test skipped due to browser compatibility issues');
        return;
      }

      // Navigate with adaptive timeout
      await testHelpers.navigateToPage('/', 3, testName);
      
      // Wait for app to load with adaptive timeout
      const adaptiveTimeout = testHelpers.getAdaptiveTimeout(30000, testName);
      await testHelpers.waitForAppLoad();
      
      // Verify the page loaded successfully
      await expect(page).toHaveTitle(/Cabinet Insight Pro/);
      
      success = true;
      console.log('✅ Adaptive timeout test completed successfully');
      
    } catch (error) {
      console.error('❌ Adaptive timeout test failed:', error);
      retryCount++;
      throw error;
    } finally {
      // Record test metrics
      testHelpers.recordTestMetrics(testName, startTime, success, retryCount, success ? undefined : 'navigation_failure');
    }
  });

  test('should demonstrate intelligent retry with browser-specific handling', async ({ page }) => {
    const testName = 'enhanced-monitoring-demo/intelligent-retry';
    const startTime = Date.now();
    let success = false;
    let retryCount = 0;

    try {
      // Initialize environment
      await testHelpers.initializeTestEnvironment(testName);
      
      // Navigate to upload page with retry logic
      await testHelpers.navigateToPage('/upload', 3, testName);
      
      // Test file upload with adaptive timeouts
      const testPdfPath = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
      
      // Upload file with enhanced error handling
      const fileInput = page.locator('input[type="file"]');
      await fileInput.setInputFiles(testPdfPath);
      
      // Wait for file validation with adaptive timeout
      const validationTimeout = testHelpers.getAdaptiveTimeout(30000, testName);
      await testHelpers.waitForFileValidation(validationTimeout);
      
      success = true;
      console.log('✅ Intelligent retry test completed successfully');
      
    } catch (error) {
      console.error('❌ Intelligent retry test failed:', error);
      retryCount++;
      
      // Demonstrate retry strategy based on error type
      if (error instanceof Error && error.message.includes('timeout')) {
        console.log('🔄 Timeout detected - would retry with increased timeout');
      }
      
      throw error;
    } finally {
      testHelpers.recordTestMetrics(testName, startTime, success, retryCount, success ? undefined : 'file_upload_failure');
    }
  });

  test('should demonstrate performance metrics collection', async ({ page }) => {
    const testName = 'enhanced-monitoring-demo/performance-metrics';
    const startTime = Date.now();
    let success = false;

    try {
      // Initialize with performance tracking
      const envInit = await testHelpers.initializeTestEnvironment(testName);
      
      // Perform multiple operations to collect metrics
      await testHelpers.navigateToPage('/', 1, testName);
      
      // Test API endpoint performance
      const healthResponse = await testHelpers.getApiResponse('/api/health');
      expect(healthResponse.success).toBe(true);
      
      // Test detailed health endpoint
      const detailedResponse = await testHelpers.getApiResponse('/api/health/detailed');
      expect(detailedResponse.success).toBe(true);
      
      // Navigate to different pages to test routing performance
      await testHelpers.navigateToPage('/upload', 1, testName);
      await testHelpers.navigateToPage('/analysis', 1, testName);
      
      success = true;
      console.log('✅ Performance metrics collection completed');
      
      // Display current performance statistics
      const currentMetrics = networkMonitor.getAveragePerformance();
      console.log(`📊 Current Performance Stats:`);
      console.log(`  Average Duration: ${currentMetrics.avgDuration.toFixed(2)}ms`);
      console.log(`  Success Rate: ${currentMetrics.successRate.toFixed(1)}%`);
      console.log(`  Average Retries: ${currentMetrics.avgRetries.toFixed(2)}`);
      
    } catch (error) {
      console.error('❌ Performance metrics test failed:', error);
      throw error;
    } finally {
      testHelpers.recordTestMetrics(testName, startTime, success, 0, success ? undefined : 'performance_test_failure');
    }
  });

  test('should demonstrate test batching recommendations', async ({ page }) => {
    const testName = 'enhanced-monitoring-demo/test-batching';
    const startTime = Date.now();
    let success = false;

    try {
      await testHelpers.initializeTestEnvironment(testName);
      
      // Get test complexity information
      const complexity = batchingManager.getTestComplexity(testName);
      console.log(`📦 Test Complexity for ${testName}:`);
      console.log(`  Estimated Duration: ${complexity?.estimatedDuration}ms`);
      console.log(`  Resource Intensity: ${complexity?.resourceIntensity}`);
      console.log(`  Network Dependency: ${complexity?.networkDependency}`);
      
      // Get browser recommendation
      const browserRecommendation = batchingManager.getBrowserRecommendation(testName);
      console.log(`🌐 Recommended Browser: ${browserRecommendation}`);
      
      // Demonstrate batching strategy
      const sampleTests = [
        'api/health.spec.ts',
        'frontend/app-loading.spec.ts',
        'integration/azure-openai.spec.ts',
        'integration/websocket.spec.ts'
      ];
      
      const batches = batchingManager.createOptimalBatches(sampleTests, 300000, 3);
      console.log(batchingManager.generateBatchingReport(batches));
      
      success = true;
      console.log('✅ Test batching demonstration completed');
      
    } catch (error) {
      console.error('❌ Test batching demonstration failed:', error);
      throw error;
    } finally {
      testHelpers.recordTestMetrics(testName, startTime, success, 0, success ? undefined : 'batching_demo_failure');
    }
  });

  test('should demonstrate cross-browser compatibility handling', async ({ page }) => {
    const testName = 'enhanced-monitoring-demo/cross-browser-compatibility';
    const startTime = Date.now();
    let success = false;

    try {
      const envInit = await testHelpers.initializeTestEnvironment(testName);
      const browser = page.context().browser()?.browserType().name() || 'unknown';
      
      console.log(`🌐 Testing cross-browser compatibility on: ${browser}`);
      
      // Get browser-specific recommendations
      const compatibilityMatrix = testHelpers['compatibilityMatrix'];
      const recommendations = compatibilityMatrix.getRecommendations(testName, browser);
      
      if (recommendations.length > 0) {
        console.log(`💡 Browser-specific recommendations for ${browser}:`);
        recommendations.forEach(rec => console.log(`  - ${rec}`));
      }
      
      // Test with browser-specific timeout
      const browserTimeout = compatibilityMatrix.getBrowserSpecificTimeout(testName, browser, 30000);
      console.log(`⏱️ Browser-specific timeout: ${browserTimeout}ms`);
      
      // Navigate with browser-aware settings
      await testHelpers.navigateToPage('/', 2, testName);
      
      // Test WebSocket functionality (known to have browser differences)
      const wsConnected = await testHelpers.waitForWebSocketConnection(3);
      console.log(`🔌 WebSocket connection: ${wsConnected ? 'Success' : 'Failed'}`);
      
      success = true;
      console.log('✅ Cross-browser compatibility test completed');
      
    } catch (error) {
      console.error('❌ Cross-browser compatibility test failed:', error);
      throw error;
    } finally {
      testHelpers.recordTestMetrics(testName, startTime, success, 0, success ? undefined : 'compatibility_test_failure');
    }
  });

  test.afterAll(async () => {
    // Generate final performance report for this test suite
    console.log('\n📊 Enhanced Monitoring Demo - Final Report:');
    console.log(networkMonitor.generatePerformanceReport());
  });
});
