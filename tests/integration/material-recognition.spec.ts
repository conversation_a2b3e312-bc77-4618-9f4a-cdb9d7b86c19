import { test, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';

const API_BASE_URL = 'http://localhost:3001/api';
const TEST_TIMEOUT = 60000; // 60 seconds for material recognition analysis

test.describe('Material Recognition Service', () => {
  test.beforeAll(async () => {
    // Verify test files exist
    const testFiles = [
      'tests/fixtures/kitchen-design-test.pdf',
      'tests/fixtures/kitchen-design-test.png'
    ];

    for (const file of testFiles) {
      if (!fs.existsSync(file)) {
        throw new Error(`Test file not found: ${file}`);
      }
    }
  });

  test('should get material recognition configuration', async ({ request }) => {
    const response = await request.get(`${API_BASE_URL}/analysis/material-recognition/config`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toHaveProperty('defaultConfig');
    expect(data.data).toHaveProperty('configOptions');
    expect(data.data).toHaveProperty('materialDatabase');
    
    // Verify default configuration structure
    const defaultConfig = data.data.defaultConfig;
    expect(defaultConfig).toHaveProperty('enableBrandRecognition');
    expect(defaultConfig).toHaveProperty('enableCostEstimation');
    expect(defaultConfig).toHaveProperty('enableQualityAssessment');
    expect(defaultConfig).toHaveProperty('confidenceThreshold');
    expect(defaultConfig).toHaveProperty('analysisDepth');
    expect(defaultConfig).toHaveProperty('costEstimationRegion');
    
    // Verify configuration options
    const configOptions = data.data.configOptions;
    expect(configOptions).toHaveProperty('analysisDepths');
    expect(configOptions).toHaveProperty('confidenceThresholds');
    expect(configOptions).toHaveProperty('costEstimationRegions');
    expect(configOptions).toHaveProperty('features');
    
    // Verify material database structure
    const materialDatabase = data.data.materialDatabase;
    expect(materialDatabase).toHaveProperty('materialTypes');
    expect(materialDatabase).toHaveProperty('brands');
    expect(materialDatabase).toHaveProperty('finishes');
    expect(materialDatabase).toHaveProperty('grades');
    expect(Array.isArray(materialDatabase.materialTypes)).toBe(true);
    expect(Array.isArray(materialDatabase.brands)).toBe(true);
    expect(Array.isArray(materialDatabase.finishes)).toBe(true);
    expect(Array.isArray(materialDatabase.grades)).toBe(true);
  });

  test('should get material database information', async ({ request }) => {
    const response = await request.get(`${API_BASE_URL}/analysis/material-recognition/database`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toHaveProperty('materialTypes');
    expect(data.data).toHaveProperty('brands');
    expect(data.data).toHaveProperty('finishes');
    expect(data.data).toHaveProperty('grades');
  });

  test('should perform material recognition analysis on PDF', async ({ request }) => {
    const testFile = path.resolve('tests/fixtures/kitchen-design-test.pdf');
    
    const response = await request.post(`${API_BASE_URL}/analysis/material-recognition`, {
      multipart: {
        files: fs.createReadStream(testFile),
        enableBrandRecognition: 'true',
        enableCostEstimation: 'true',
        enableQualityAssessment: 'true',
        enableSpecificationExtraction: 'true',
        confidenceThreshold: '0.75',
        analysisDepth: 'DETAILED',
        costEstimationRegion: 'US_NATIONAL',
        includeAlternatives: 'true'
      },
      timeout: TEST_TIMEOUT
    });

    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toHaveProperty('analysisId');
    expect(data.data).toHaveProperty('materialRecognition');
    expect(data.data).toHaveProperty('processingTime');
    expect(data.data).toHaveProperty('confidence');
    
    // Verify material recognition result structure
    const materialRecognition = data.data.materialRecognition;
    expect(materialRecognition).toHaveProperty('materials');
    expect(materialRecognition).toHaveProperty('costEstimation');
    expect(materialRecognition).toHaveProperty('qualityAssessment');
    expect(materialRecognition).toHaveProperty('alternatives');
    expect(materialRecognition).toHaveProperty('processingMetrics');
    
    // Verify materials array
    expect(Array.isArray(materialRecognition.materials)).toBe(true);
    if (materialRecognition.materials.length > 0) {
      const material = materialRecognition.materials[0];
      expect(material).toHaveProperty('id');
      expect(material).toHaveProperty('type');
      expect(material).toHaveProperty('subtype');
      expect(material).toHaveProperty('finish');
      expect(material).toHaveProperty('color');
      expect(material).toHaveProperty('grade');
      expect(material).toHaveProperty('confidence');
      expect(material).toHaveProperty('visualCharacteristics');
      expect(material).toHaveProperty('specifications');
      
      // Verify confidence scores
      expect(material.confidence).toHaveProperty('materialType');
      expect(material.confidence).toHaveProperty('brandIdentification');
      expect(material.confidence).toHaveProperty('finishAccuracy');
      expect(material.confidence).toHaveProperty('gradeAssessment');
      expect(material.confidence).toHaveProperty('overall');
      expect(typeof material.confidence.overall).toBe('number');
      expect(material.confidence.overall).toBeGreaterThan(0);
      expect(material.confidence.overall).toBeLessThanOrEqual(1);
    }
    
    // Verify cost estimation structure
    if (materialRecognition.costEstimation) {
      const costEstimation = materialRecognition.costEstimation;
      expect(costEstimation).toHaveProperty('materialCosts');
      expect(costEstimation).toHaveProperty('laborCosts');
      expect(costEstimation).toHaveProperty('additionalCosts');
      expect(costEstimation).toHaveProperty('totals');
      expect(costEstimation).toHaveProperty('breakdown');
      expect(costEstimation).toHaveProperty('regionalFactors');
      
      // Verify totals structure
      expect(costEstimation.totals).toHaveProperty('materials');
      expect(costEstimation.totals).toHaveProperty('labor');
      expect(costEstimation.totals).toHaveProperty('additional');
      expect(costEstimation.totals).toHaveProperty('subtotal');
      expect(costEstimation.totals).toHaveProperty('tax');
      expect(costEstimation.totals).toHaveProperty('grandTotal');
      expect(typeof costEstimation.totals.grandTotal).toBe('number');
      expect(costEstimation.totals.grandTotal).toBeGreaterThan(0);
    }
    
    // Verify quality assessment structure
    const qualityAssessment = materialRecognition.qualityAssessment;
    expect(qualityAssessment).toHaveProperty('overallGrade');
    expect(qualityAssessment).toHaveProperty('durabilityScore');
    expect(qualityAssessment).toHaveProperty('aestheticScore');
    expect(qualityAssessment).toHaveProperty('valueScore');
    expect(qualityAssessment).toHaveProperty('recommendations');
    expect(['ECONOMY', 'STANDARD', 'PREMIUM', 'LUXURY']).toContain(qualityAssessment.overallGrade);
    expect(Array.isArray(qualityAssessment.recommendations)).toBe(true);
    
    // Verify processing metrics
    const processingMetrics = materialRecognition.processingMetrics;
    expect(processingMetrics).toHaveProperty('analysisTime');
    expect(processingMetrics).toHaveProperty('confidenceScore');
    expect(processingMetrics).toHaveProperty('materialsDetected');
    expect(typeof processingMetrics.analysisTime).toBe('number');
    expect(typeof processingMetrics.confidenceScore).toBe('number');
    expect(typeof processingMetrics.materialsDetected).toBe('number');
    expect(processingMetrics.analysisTime).toBeGreaterThan(0);
    expect(processingMetrics.confidenceScore).toBeGreaterThan(0);
    expect(processingMetrics.confidenceScore).toBeLessThanOrEqual(1);
  });

  test('should perform material recognition analysis on image', async ({ request }) => {
    const testFile = path.resolve('tests/fixtures/kitchen-design-test.png');
    
    const response = await request.post(`${API_BASE_URL}/analysis/material-recognition`, {
      multipart: {
        files: fs.createReadStream(testFile),
        enableBrandRecognition: 'true',
        enableCostEstimation: 'false',
        enableQualityAssessment: 'true',
        confidenceThreshold: '0.8',
        analysisDepth: 'BASIC',
        includeAlternatives: 'false'
      },
      timeout: TEST_TIMEOUT
    });

    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toHaveProperty('materialRecognition');
    
    const materialRecognition = data.data.materialRecognition;
    expect(materialRecognition).toHaveProperty('materials');
    expect(materialRecognition).toHaveProperty('qualityAssessment');
    expect(materialRecognition).toHaveProperty('processingMetrics');
    
    // Cost estimation should be undefined when disabled
    expect(materialRecognition.costEstimation).toBeUndefined();
    
    // Alternatives should be empty when disabled
    expect(Array.isArray(materialRecognition.alternatives)).toBe(true);
    expect(materialRecognition.alternatives.length).toBe(0);
  });

  test('should handle batch material recognition analysis', async ({ request }) => {
    const testFiles = [
      path.resolve('tests/fixtures/kitchen-design-test.pdf'),
      path.resolve('tests/fixtures/kitchen-design-test.png')
    ];
    
    const formData = new FormData();
    testFiles.forEach((filePath, index) => {
      const fileBuffer = fs.readFileSync(filePath);
      const fileName = path.basename(filePath);
      formData.append('files', new Blob([fileBuffer]), fileName);
    });
    
    formData.append('enableBrandRecognition', 'true');
    formData.append('enableCostEstimation', 'true');
    formData.append('enableQualityAssessment', 'true');
    formData.append('confidenceThreshold', '0.75');
    formData.append('analysisDepth', 'DETAILED');
    
    const response = await request.post(`${API_BASE_URL}/analysis/material-recognition/batch`, {
      data: formData,
      timeout: TEST_TIMEOUT * 2 // Double timeout for batch processing
    });

    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toHaveProperty('batchId');
    expect(data.data).toHaveProperty('results');
    expect(data.data).toHaveProperty('summary');
    
    // Verify batch results structure
    const results = data.data.results;
    expect(Array.isArray(results)).toBe(true);
    expect(results.length).toBeGreaterThan(0);
    
    // Verify summary structure
    const summary = data.data.summary;
    expect(summary).toHaveProperty('totalGroups');
    expect(summary).toHaveProperty('successfulGroups');
    expect(summary).toHaveProperty('failedGroups');
    expect(summary).toHaveProperty('overallConfidence');
    expect(summary).toHaveProperty('totalFiles');
    expect(typeof summary.totalFiles).toBe('number');
    expect(summary.totalFiles).toBe(testFiles.length);
  });

  test('should handle invalid file types', async ({ request }) => {
    // Create a temporary text file
    const tempFile = path.resolve('tests/fixtures/temp-invalid.txt');
    fs.writeFileSync(tempFile, 'This is not a valid image or PDF file');
    
    try {
      const response = await request.post(`${API_BASE_URL}/analysis/material-recognition`, {
        multipart: {
          files: fs.createReadStream(tempFile)
        },
        timeout: TEST_TIMEOUT
      });

      expect(response.status()).toBe(400);
      
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toContain('Invalid file type');
    } finally {
      // Clean up temporary file
      if (fs.existsSync(tempFile)) {
        fs.unlinkSync(tempFile);
      }
    }
  });

  test('should handle missing files', async ({ request }) => {
    const response = await request.post(`${API_BASE_URL}/analysis/material-recognition`, {
      multipart: {},
      timeout: TEST_TIMEOUT
    });

    expect(response.status()).toBe(400);
    
    const data = await response.json();
    expect(data.success).toBe(false);
    expect(data.error).toBe('No files uploaded');
  });

  test('should validate confidence threshold parameter', async ({ request }) => {
    const testFile = path.resolve('tests/fixtures/kitchen-design-test.png');
    
    const response = await request.post(`${API_BASE_URL}/analysis/material-recognition`, {
      multipart: {
        files: fs.createReadStream(testFile),
        confidenceThreshold: '1.5' // Invalid threshold > 1.0
      },
      timeout: TEST_TIMEOUT
    });

    expect(response.status()).toBe(200); // Service should handle invalid values gracefully
    
    const data = await response.json();
    expect(data.success).toBe(true);
    // Service should use default threshold when invalid value provided
  });

  test('should support different cost estimation regions', async ({ request }) => {
    const testFile = path.resolve('tests/fixtures/kitchen-design-test.pdf');
    
    const regions = ['US_NATIONAL', 'US_WEST_COAST', 'US_NORTHEAST', 'US_SOUTH', 'US_MIDWEST'];
    
    for (const region of regions) {
      const response = await request.post(`${API_BASE_URL}/analysis/material-recognition`, {
        multipart: {
          files: fs.createReadStream(testFile),
          enableCostEstimation: 'true',
          costEstimationRegion: region
        },
        timeout: TEST_TIMEOUT
      });

      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(data.success).toBe(true);
      
      if (data.data.materialRecognition.costEstimation) {
        expect(data.data.materialRecognition.costEstimation.regionalFactors.location).toBe(region);
      }
    }
  });
});
