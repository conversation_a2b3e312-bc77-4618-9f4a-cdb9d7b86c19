import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';
import { NetworkMonitor } from '../utils/network-monitor';

const networkMonitor = new NetworkMonitor();

test.describe('Performance Metrics Dashboard', () => {
  let testHelpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    testHelpers = new TestHelpers(page, networkMonitor);
    await testHelpers.initializeTestEnvironment('performance-metrics-dashboard');
  });

  test('should load performance metrics dashboard', async ({ page }) => {
    const testName = 'performance-metrics-dashboard/load-dashboard';
    const startTime = Date.now();
    let success = false;

    try {
      // Navigate to performance dashboard tab using the new helper method
      await testHelpers.navigateToPerformanceTab(testHelpers.getTimeout());

      // Verify dashboard components are present
      await expect(page.locator('h1')).toContainText('Performance Metrics Dashboard');
      await expect(page.locator('p')).toContainText('Comprehensive AI model performance analytics');

      // Verify summary cards are present
      await expect(page.locator('text=Total Requests')).toBeVisible();
      await expect(page.locator('text=Total Cost')).toBeVisible();
      await expect(page.locator('text=Most Efficient')).toBeVisible();
      await expect(page.locator('text=Active Alerts')).toBeVisible();

      // Verify tab navigation is present
      await expect(page.locator('[data-value="overview"]')).toBeVisible();
      await expect(page.locator('[data-value="models"]')).toBeVisible();
      await expect(page.locator('[data-value="costs"]')).toBeVisible();
      await expect(page.locator('[data-value="alerts"]')).toBeVisible();

      success = true;
      console.log('✅ Performance metrics dashboard loaded successfully');

    } catch (error) {
      console.error('❌ Performance metrics dashboard load failed:', error);
      throw error;
    } finally {
      testHelpers.recordTestMetrics(testName, startTime, success, 0, success ? undefined : 'dashboard_load_failure');
    }
  });

  test('should fetch and display performance overview data', async ({ page }) => {
    const testName = 'performance-metrics-dashboard/overview-data';
    const startTime = Date.now();
    let success = false;

    try {
      // Navigate to performance dashboard tab
      await testHelpers.navigateToPerformanceTab(testHelpers.getTimeout());

      // Test API endpoint directly
      const overviewResponse = await testHelpers.getApiResponse('/api/performance/overview?timeRange=24h');
      expect(overviewResponse.success).toBe(true);
      expect(overviewResponse.data).toBeDefined();
      expect(overviewResponse.data.modelComparison).toBeDefined();
      expect(overviewResponse.data.summary).toBeDefined();

      // Verify data structure
      expect(overviewResponse.data.modelComparison.models).toBeInstanceOf(Array);
      expect(overviewResponse.data.summary.totalRequests).toBeGreaterThanOrEqual(0);
      expect(overviewResponse.data.summary.totalCost).toBeGreaterThanOrEqual(0);

      // Wait for data to be displayed in UI
      await page.waitForFunction(() => {
        const totalRequestsElement = document.querySelector('[data-testid="total-requests"]') || 
                                   document.querySelector('text=Total Requests');
        return totalRequestsElement && totalRequestsElement.textContent !== '0';
      }, { timeout: 10000 });

      success = true;
      console.log('✅ Performance overview data fetched and displayed successfully');

    } catch (error) {
      console.error('❌ Performance overview data test failed:', error);
      throw error;
    } finally {
      testHelpers.recordTestMetrics(testName, startTime, success, 0, success ? undefined : 'overview_data_failure');
    }
  });

  test('should navigate between dashboard tabs', async ({ page }) => {
    const testName = 'performance-metrics-dashboard/tab-navigation';
    const startTime = Date.now();
    let success = false;

    try {
      // Navigate to performance dashboard tab
      await testHelpers.navigateToPerformanceTab(testHelpers.getTimeout());

      // Test Overview tab (default)
      await expect(page.locator('[data-value="overview"][data-state="active"]')).toBeVisible();
      await expect(page.locator('text=Model Performance Comparison')).toBeVisible();

      // Test Model Comparison tab
      await page.click('[data-value="models"]');
      await expect(page.locator('[data-value="models"][data-state="active"]')).toBeVisible();
      await page.waitForSelector('text=GPT4O', { timeout: 5000 });

      // Test Cost Analysis tab
      await page.click('[data-value="costs"]');
      await expect(page.locator('[data-value="costs"][data-state="active"]')).toBeVisible();
      await expect(page.locator('text=Cost Overview')).toBeVisible();

      // Test Alerts tab
      await page.click('[data-value="alerts"]');
      await expect(page.locator('[data-value="alerts"][data-state="active"]')).toBeVisible();
      
      // Should show either alerts or "No Active Alerts" message
      const hasAlerts = await page.locator('text=No Active Alerts').isVisible();
      if (hasAlerts) {
        await expect(page.locator('text=All systems are operating within normal parameters')).toBeVisible();
      }

      success = true;
      console.log('✅ Dashboard tab navigation working correctly');

    } catch (error) {
      console.error('❌ Dashboard tab navigation test failed:', error);
      throw error;
    } finally {
      testHelpers.recordTestMetrics(testName, startTime, success, 0, success ? undefined : 'tab_navigation_failure');
    }
  });

  test('should test time range selector functionality', async ({ page }) => {
    const testName = 'performance-metrics-dashboard/time-range-selector';
    const startTime = Date.now();
    let success = false;

    try {
      // Navigate to performance dashboard tab
      await testHelpers.navigateToPerformanceTab(testHelpers.getTimeout());

      // Find and test time range selector
      const timeRangeSelector = page.locator('select');
      await expect(timeRangeSelector).toBeVisible();

      // Test different time ranges
      const timeRanges = ['1h', '24h', '7d', '30d'];
      
      for (const range of timeRanges) {
        await timeRangeSelector.selectOption(range);
        
        // Wait for data to refresh
        await page.waitForTimeout(1000);
        
        // Verify API call was made with correct time range
        const response = await testHelpers.getApiResponse(`/api/performance/overview?timeRange=${range}`);
        expect(response.success).toBe(true);
        expect(response.data.modelComparison.timeRange).toBe(range);
      }

      success = true;
      console.log('✅ Time range selector functionality working correctly');

    } catch (error) {
      console.error('❌ Time range selector test failed:', error);
      throw error;
    } finally {
      testHelpers.recordTestMetrics(testName, startTime, success, 0, success ? undefined : 'time_range_failure');
    }
  });

  test('should test export functionality', async ({ page }) => {
    const testName = 'performance-metrics-dashboard/export-functionality';
    const startTime = Date.now();
    let success = false;

    try {
      // Navigate to performance dashboard tab
      await testHelpers.navigateToPerformanceTab(testHelpers.getTimeout());

      // Test CSV export
      const csvExportButton = page.locator('button:has-text("CSV")');
      await expect(csvExportButton).toBeVisible();

      // Test JSON export
      const jsonExportButton = page.locator('button:has-text("JSON")');
      await expect(jsonExportButton).toBeVisible();

      // Test export API endpoints directly
      const csvResponse = await testHelpers.getApiResponse('/api/performance/export?format=CSV&timeRange=24h');
      expect(csvResponse).toBeDefined();

      const jsonResponse = await testHelpers.getApiResponse('/api/performance/export?format=JSON&timeRange=24h');
      expect(jsonResponse).toBeDefined();

      success = true;
      console.log('✅ Export functionality working correctly');

    } catch (error) {
      console.error('❌ Export functionality test failed:', error);
      throw error;
    } finally {
      testHelpers.recordTestMetrics(testName, startTime, success, 0, success ? undefined : 'export_failure');
    }
  });

  test('should test refresh functionality', async ({ page }) => {
    const testName = 'performance-metrics-dashboard/refresh-functionality';
    const startTime = Date.now();
    let success = false;

    try {
      // Navigate to performance dashboard tab
      await testHelpers.navigateToPerformanceTab(testHelpers.getTimeout());

      // Find refresh button
      const refreshButton = page.locator('button:has-text("Refresh")');
      await expect(refreshButton).toBeVisible();

      // Click refresh button
      await refreshButton.click();

      // Verify loading state
      await expect(page.locator('.animate-spin')).toBeVisible();

      // Wait for refresh to complete
      await page.waitForSelector('.animate-spin', { state: 'detached', timeout: 10000 });

      // Verify last updated timestamp is present
      await expect(page.locator('text=Last updated:')).toBeVisible();

      success = true;
      console.log('✅ Refresh functionality working correctly');

    } catch (error) {
      console.error('❌ Refresh functionality test failed:', error);
      throw error;
    } finally {
      testHelpers.recordTestMetrics(testName, startTime, success, 0, success ? undefined : 'refresh_failure');
    }
  });

  test('should test model performance comparison display', async ({ page }) => {
    const testName = 'performance-metrics-dashboard/model-comparison';
    const startTime = Date.now();
    let success = false;

    try {
      // Navigate to performance dashboard tab
      await testHelpers.navigateToPerformanceTab(testHelpers.getTimeout());

      // Navigate to Models tab
      await page.click('[data-value="models"]');

      // Verify model cards are displayed
      const modelNames = ['GPT4O', 'GPTO1', 'GPT4O_MINI'];
      
      for (const modelName of modelNames) {
        await expect(page.locator(`text=${modelName}`)).toBeVisible();
      }

      // Verify model metrics are displayed
      await expect(page.locator('text=Total Requests')).toBeVisible();
      await expect(page.locator('text=Success Rate')).toBeVisible();
      await expect(page.locator('text=Avg Response Time')).toBeVisible();
      await expect(page.locator('text=Total Cost')).toBeVisible();
      await expect(page.locator('text=Confidence Score')).toBeVisible();

      success = true;
      console.log('✅ Model performance comparison display working correctly');

    } catch (error) {
      console.error('❌ Model comparison display test failed:', error);
      throw error;
    } finally {
      testHelpers.recordTestMetrics(testName, startTime, success, 0, success ? undefined : 'model_comparison_failure');
    }
  });

  test.afterAll(async () => {
    // Generate final performance report for this test suite
    console.log('\n📊 Performance Metrics Dashboard Tests - Final Report:');
    console.log(networkMonitor.generatePerformanceReport());
  });
});
