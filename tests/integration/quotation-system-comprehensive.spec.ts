import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

test.describe('Comprehensive Quotation System', () => {
  let testHelpers: TestHelpers;
  const API_BASE_URL = 'http://localhost:3001';
  const testPdfPath = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');

  test.beforeEach(async ({ page }) => {
    testHelpers = new TestHelpers(page);
    await testHelpers.setupTestEnvironment();
  });

  test.describe('Quotation API Integration', () => {
    test('should generate comprehensive quote from analysis data', async ({ request }) => {
      console.log('🧪 Testing comprehensive quotation generation');

      // First, create an analysis to get analysis data
      const analysisResponse = await request.post(`${API_BASE_URL}/api/analysis/upload`, {
        multipart: {
          file: {
            name: 'kitchen-design-test.pdf',
            mimeType: 'application/pdf',
            buffer: fs.readFileSync(testPdfPath),
          },
          useGPT4o: 'true',
          useReasoning: 'true',
          enableMaterialRecognition: 'true',
          enableLayoutOptimization: 'true'
        },
      });

      expect(analysisResponse.ok()).toBeTruthy();
      const analysisData = await analysisResponse.json();
      expect(analysisData.success).toBe(true);

      // Generate quote from analysis
      const authHeaders = await testHelpers.getAuthHeaders();

      const quoteResponse = await request.post(`${API_BASE_URL}/api/quotation/generate`, {
        data: {
          analysisId: analysisData.data.analysisId,
          customerInfo: {
            name: 'Test Customer',
            email: '<EMAIL>',
            phone: '+64 21 123 4567',
            address: '123 Test Street, Auckland, New Zealand'
          },
          quoteTier: 'Standard',
          includeInstallation: true,
          includeDelivery: true,
          validityDays: 30
        },
        headers: authHeaders
      });

      expect(quoteResponse.ok()).toBeTruthy();
      const quoteData = await quoteResponse.json();
      
      expect(quoteData.success).toBe(true);
      expect(quoteData.data.quoteId).toBeDefined();
      expect(quoteData.data.totalCost).toBeGreaterThan(0);
      expect(quoteData.data.currency).toBe('NZD');
      expect(quoteData.data.items).toBeInstanceOf(Array);
      expect(quoteData.data.items.length).toBeGreaterThan(0);

      console.log(`✅ Quote generated successfully: ${quoteData.data.quoteId}`);
      console.log(`💰 Total cost: ${quoteData.data.currency} ${quoteData.data.totalCost}`);
    });

    test('should retrieve quote by ID with full details', async ({ request }) => {
      console.log('🧪 Testing quote retrieval by ID');

      // First generate a quote
      const authHeaders = await testHelpers.getAuthHeaders();

      const quoteResponse = await request.post(`${API_BASE_URL}/api/quotation/generate`, {
        data: {
          analysisData: {
            cabinetCount: 12,
            materials: ['Plywood', 'MDF'],
            hardware: ['Blum hinges', 'Soft-close drawers'],
            dimensions: { width: 3000, height: 2400, depth: 600 }
          },
          customerInfo: {
            name: 'Test Customer',
            email: '<EMAIL>'
          },
          quoteTier: 'Basic'
        },
        headers: authHeaders
      });

      expect(quoteResponse.ok()).toBeTruthy();
      const quoteData = await quoteResponse.json();
      const quoteId = quoteData.data.quoteId;

      // Retrieve the quote
      const retrieveResponse = await request.get(`${API_BASE_URL}/api/quotation/${quoteId}`, {
        headers: authHeaders
      });
      expect(retrieveResponse.ok()).toBeTruthy();
      
      const retrievedData = await retrieveResponse.json();
      expect(retrievedData.success).toBe(true);
      expect(retrievedData.data.quoteId).toBe(quoteId);
      expect(retrievedData.data.customerInfo).toBeDefined();
      expect(retrievedData.data.items).toBeInstanceOf(Array);
      expect(retrievedData.data.createdAt).toBeDefined();

      console.log('✅ Quote retrieved successfully');
    });

    test('should generate PDF quote document', async ({ request }) => {
      console.log('🧪 Testing PDF quote generation');

      // Generate a quote first
      const quoteResponse = await request.post(`${API_BASE_URL}/api/quotation/generate`, {
        data: {
          analysisData: {
            cabinetCount: 8,
            materials: ['Solid Oak', 'Plywood'],
            hardware: ['Blum hinges', 'Hafele handles'],
            dimensions: { width: 2500, height: 2400, depth: 600 }
          },
          customerInfo: {
            name: 'Premium Customer',
            email: '<EMAIL>',
            company: 'Premium Kitchens Ltd'
          },
          quoteTier: 'Premium'
        }
      });

      const quoteData = await quoteResponse.json();
      const quoteId = quoteData.data.quoteId;

      // Generate PDF
      const pdfResponse = await request.get(`${API_BASE_URL}/api/quotation/${quoteId}/pdf`);
      expect(pdfResponse.ok()).toBeTruthy();
      
      const contentType = pdfResponse.headers()['content-type'];
      expect(contentType).toContain('application/pdf');
      
      const pdfBuffer = await pdfResponse.body();
      expect(pdfBuffer.length).toBeGreaterThan(1000); // PDF should be substantial

      console.log(`✅ PDF generated successfully (${pdfBuffer.length} bytes)`);
    });

    test('should handle different quote tiers correctly', async ({ request }) => {
      console.log('🧪 Testing different quote tiers');

      const tiers = ['Basic', 'Standard', 'Premium'];
      const analysisData = {
        cabinetCount: 10,
        materials: ['Plywood', 'MDF'],
        hardware: ['Standard hinges', 'Basic handles'],
        dimensions: { width: 3000, height: 2400, depth: 600 }
      };

      for (const tier of tiers) {
        const response = await request.post(`${API_BASE_URL}/api/quotation/generate`, {
          data: {
            analysisData,
            customerInfo: {
              name: `${tier} Customer`,
              email: `${tier.toLowerCase()}@example.com`
            },
            quoteTier: tier
          }
        });

        expect(response.ok()).toBeTruthy();
        const data = await response.json();
        
        expect(data.success).toBe(true);
        expect(data.data.quoteTier).toBe(tier);
        expect(data.data.totalCost).toBeGreaterThan(0);

        console.log(`✅ ${tier} tier quote: ${data.data.currency} ${data.data.totalCost}`);
      }
    });

    test('should validate pricing database integration', async ({ request }) => {
      console.log('🧪 Testing pricing database integration');

      // Test pricing lookup endpoint
      const pricingResponse = await request.get(`${API_BASE_URL}/api/quotation/pricing/materials`);
      expect(pricingResponse.ok()).toBeTruthy();
      
      const pricingData = await pricingResponse.json();
      expect(pricingData.success).toBe(true);
      expect(pricingData.data.materials).toBeInstanceOf(Array);
      expect(pricingData.data.materials.length).toBeGreaterThan(0);

      // Check for NZD pricing
      const firstMaterial = pricingData.data.materials[0];
      expect(firstMaterial.priceNZD).toBeDefined();
      expect(firstMaterial.priceNZD).toBeGreaterThan(0);

      console.log(`✅ Pricing database contains ${pricingData.data.materials.length} materials`);
    });

    test('should handle quote modifications and updates', async ({ request }) => {
      console.log('🧪 Testing quote modifications');

      // Generate initial quote
      const initialResponse = await request.post(`${API_BASE_URL}/api/quotation/generate`, {
        data: {
          analysisData: {
            cabinetCount: 6,
            materials: ['MDF'],
            hardware: ['Basic hinges']
          },
          customerInfo: {
            name: 'Modification Test',
            email: '<EMAIL>'
          },
          quoteTier: 'Basic'
        }
      });

      const initialData = await initialResponse.json();
      const quoteId = initialData.data.quoteId;

      // Update quote
      const updateResponse = await request.put(`${API_BASE_URL}/api/quotation/${quoteId}`, {
        data: {
          quoteTier: 'Standard',
          includeInstallation: true,
          customerInfo: {
            name: 'Modified Customer',
            email: '<EMAIL>',
            phone: '+64 21 999 8888'
          }
        }
      });

      if (updateResponse.ok()) {
        const updateData = await updateResponse.json();
        expect(updateData.success).toBe(true);
        expect(updateData.data.quoteTier).toBe('Standard');
        console.log('✅ Quote modification successful');
      } else {
        console.log('ℹ️ Quote modification endpoint not implemented');
      }
    });
  });

  test.describe('Quotation Frontend Integration', () => {
    test('should display quotation interface in analysis results', async ({ page }) => {
      await testHelpers.navigateToPage('/');
      
      // Look for quotation-related elements
      const quoteButton = page.locator('button:has-text("Quote"), button:has-text("Generate Quote")');
      const pricingSection = page.locator('[data-testid="pricing"], .pricing-section');
      const costEstimate = page.locator('[data-testid="cost-estimate"], .cost-estimate');
      
      const hasQuoteButton = await quoteButton.count() > 0;
      const hasPricingSection = await pricingSection.count() > 0;
      const hasCostEstimate = await costEstimate.count() > 0;
      
      if (hasQuoteButton || hasPricingSection || hasCostEstimate) {
        console.log('✅ Quotation interface elements found');
      } else {
        console.log('ℹ️ Quotation interface not visible (may require analysis completion)');
      }
    });

    test('should handle quote generation from UI', async ({ page }) => {
      await testHelpers.navigateToPage('/');
      
      // Look for quote generation button
      const generateQuoteButton = page.locator('button:has-text("Generate Quote"), button:has-text("Get Quote")');
      
      if (await generateQuoteButton.count() > 0) {
        await generateQuoteButton.first().click();
        await page.waitForTimeout(1000);
        
        // Check for quote form or modal
        const quoteForm = page.locator('[data-testid="quote-form"], .quote-form');
        const customerInfoForm = page.locator('input[name="customerName"], input[placeholder*="name" i]');
        
        if (await quoteForm.count() > 0 || await customerInfoForm.count() > 0) {
          console.log('✅ Quote generation UI available');
          
          // Fill customer information if form is present
          if (await customerInfoForm.count() > 0) {
            await customerInfoForm.fill('Test Customer');
            
            const emailInput = page.locator('input[type="email"], input[name="email"]');
            if (await emailInput.count() > 0) {
              await emailInput.fill('<EMAIL>');
            }
            
            console.log('✅ Customer information form filled');
          }
        }
      } else {
        console.log('ℹ️ Quote generation button not found');
      }
    });

    test('should display pricing breakdown', async ({ page }) => {
      await testHelpers.navigateToPage('/');
      
      // Look for pricing breakdown elements
      const pricingTable = page.locator('[data-testid="pricing-table"], .pricing-table');
      const costBreakdown = page.locator('[data-testid="cost-breakdown"], .cost-breakdown');
      const totalCost = page.locator('[data-testid="total-cost"], .total-cost');
      
      const hasPricingTable = await pricingTable.count() > 0;
      const hasCostBreakdown = await costBreakdown.count() > 0;
      const hasTotalCost = await totalCost.count() > 0;
      
      if (hasPricingTable || hasCostBreakdown || hasTotalCost) {
        console.log('✅ Pricing breakdown elements found');
        
        // Check for NZD currency
        const nzdElements = page.locator('text=/NZD|\\$NZ/');
        if (await nzdElements.count() > 0) {
          console.log('✅ NZD pricing displayed');
        }
      } else {
        console.log('ℹ️ Pricing breakdown not visible');
      }
    });
  });

  test.describe('Enhanced Quotation Features', () => {
    test('should display WebSocket connection status and real-time updates', async ({ page }) => {
      await testHelpers.navigateToPage('/');

      // Navigate to quotation tab if available
      const quotationTab = page.locator('button:has-text("Quotation"), button:has-text("Quote")');
      if (await quotationTab.count() > 0) {
        await quotationTab.click();
        console.log('✅ Navigated to quotation tab');
      }

      // Check for WebSocket connection indicators
      const wifiIcon = page.locator('svg[title*="Real-time updates"]');
      const connectionStatus = page.locator('text=Real-time updates enabled');

      if (await wifiIcon.count() > 0) {
        console.log('✅ WebSocket connection indicator found');
      }

      if (await connectionStatus.count() > 0) {
        console.log('✅ Real-time updates status displayed');
      }

      // Test WebSocket functionality by checking console logs
      const logs: string[] = [];
      page.on('console', msg => {
        if (msg.text().includes('WebSocket') || msg.text().includes('Real-time')) {
          logs.push(msg.text());
        }
      });

      // Wait a moment for WebSocket connection
      await page.waitForTimeout(2000);

      if (logs.length > 0) {
        console.log('✅ WebSocket activity detected in console');
        logs.forEach(log => console.log(`  📡 ${log}`));
      }
    });

    test('should show progress indicators during quote generation', async ({ page }) => {
      await testHelpers.navigateToPage('/');

      // Navigate to quotation tab
      const quotationTab = page.locator('button:has-text("Quotation"), button:has-text("Quote")');
      if (await quotationTab.count() > 0) {
        await quotationTab.click();
      }

      // Look for enhanced quote generation button
      const generateQuoteButton = page.locator('[data-testid="generate-quote-btn"]');

      if (await generateQuoteButton.count() > 0) {
        const isEnabled = await generateQuoteButton.isEnabled();
        if (isEnabled) {
          // Click to generate quote
          await generateQuoteButton.click();
          console.log('✅ Enhanced quote generation initiated');

          // Look for progress indicators
          const progressIndicators = [
            'Initializing quote generation',
            'Analyzing cabinet data',
            'Calculating material costs',
            'Processing hardware pricing',
            'Generating multi-tier quotes'
          ];

          for (const progressText of progressIndicators) {
            const progressElement = page.locator(`text=${progressText}`);
            if (await progressElement.count() > 0) {
              console.log(`✅ Progress indicator found: ${progressText}`);
            }
          }

          // Wait for quote results or timeout
          try {
            await page.waitForSelector('[data-testid="quote-results"]', { timeout: 30000 });
            console.log('✅ Quote results displayed');

            // Check for tier cards
            const tierCards = page.locator('[data-testid^="quote-tier-"]');
            const tierCount = await tierCards.count();
            if (tierCount > 0) {
              console.log(`✅ ${tierCount} quote tiers displayed`);
            }

          } catch (error) {
            console.log('ℹ️ Quote generation timed out (may require valid analysis data)');
          }

        } else {
          console.log('ℹ️ Quote generation button disabled (requires analysis)');
        }
      } else {
        console.log('ℹ️ Enhanced quote generation button not found');
      }
    });

    test('should integrate with analysis workflow', async ({ page }) => {
      await testHelpers.navigateToPage('/');

      // Check if quotation tab is available after analysis
      const analysisTab = page.locator('button:has-text("Results"), button:has-text("Analysis")');
      if (await analysisTab.count() > 0) {
        await analysisTab.click();

        // Look for generate quote button in analysis results
        const generateQuoteButton = page.locator('button:has-text("Generate Quote")');
        if (await generateQuoteButton.count() > 0) {
          console.log('✅ Quote generation integrated in analysis workflow');

          // Click should navigate to quotation tab
          await generateQuoteButton.click();

          // Check if we're now on quotation tab
          const quotationSection = page.locator('[data-testid="quotation-section"]');
          if (await quotationSection.count() > 0) {
            console.log('✅ Successfully navigated to quotation section');
          }
        }
      }
    });
  });

  test.describe('Quote PDF Generation and Download', () => {
    test('should generate and download quote PDF', async ({ page }) => {
      await testHelpers.navigateToPage('/');
      
      // Look for PDF download functionality
      const downloadButton = page.locator('button:has-text("Download"), button:has-text("PDF"), a[href*="pdf"]');
      
      if (await downloadButton.count() > 0) {
        // Set up download handler
        const downloadPromise = page.waitForEvent('download');
        
        await downloadButton.first().click();
        
        try {
          const download = await downloadPromise;
          expect(download.suggestedFilename()).toMatch(/\.pdf$/);
          console.log(`✅ PDF download initiated: ${download.suggestedFilename()}`);
        } catch (error) {
          console.log('ℹ️ PDF download not triggered (may require quote generation first)');
        }
      } else {
        console.log('ℹ️ PDF download button not found');
      }
    });

    test('should validate PDF content structure', async ({ request }) => {
      console.log('🧪 Testing PDF content validation');

      // Generate a quote with comprehensive data
      const quoteResponse = await request.post(`${API_BASE_URL}/api/quotation/generate`, {
        data: {
          analysisData: {
            cabinetCount: 15,
            materials: ['Solid Oak', 'Plywood', 'MDF'],
            hardware: ['Blum hinges', 'Hafele handles', 'Soft-close drawers'],
            dimensions: { width: 4000, height: 2400, depth: 650 },
            roomLayout: 'L-shaped kitchen',
            specialFeatures: ['Island', 'Pantry', 'Wine rack']
          },
          customerInfo: {
            name: 'Comprehensive Test Customer',
            email: '<EMAIL>',
            phone: '+64 9 123 4567',
            address: '456 Premium Street, Wellington, New Zealand',
            company: 'Premium Homes Ltd'
          },
          quoteTier: 'Premium',
          includeInstallation: true,
          includeDelivery: true,
          includeWarranty: true,
          validityDays: 45
        }
      });

      const quoteData = await quoteResponse.json();
      const quoteId = quoteData.data.quoteId;

      // Get PDF
      const pdfResponse = await request.get(`${API_BASE_URL}/api/quotation/${quoteId}/pdf`);
      expect(pdfResponse.ok()).toBeTruthy();
      
      const pdfBuffer = await pdfResponse.body();
      expect(pdfBuffer.length).toBeGreaterThan(5000); // Comprehensive PDF should be substantial
      
      // Validate PDF headers
      const pdfHeader = pdfBuffer.slice(0, 4).toString();
      expect(pdfHeader).toBe('%PDF');

      console.log(`✅ Comprehensive PDF validated (${pdfBuffer.length} bytes)`);
    });
  });
});
