import { test, expect } from '@playwright/test';
import path from 'path';
import { fileURLToPath } from 'url';
import { TestHelpers } from '../utils/test-helpers';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

test.describe('GPT-o1 Reasoning Chain Visualization - Priority 4 Feature 1', () => {
  const testPdfPath = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
    await helpers.navigateToPage('/analysis');
  });



  test('should initialize GPT-o1 reasoning chain visualization', async ({ page }) => {
    // Upload test file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testPdfPath);

    // Wait for file validation
    await helpers.waitForFileValidation(10000);

    // Select GPT-o1 model for complex reasoning
    const modelSelect = page.locator('[data-testid="model-select"], .select-trigger').first();
    await modelSelect.click();

    const gptO1Option = page.locator('text=GPT-o1 (Advanced Reasoning)');
    await gptO1Option.click();

    // Start analysis
    const analyzeButton = page.locator('button:has-text("Start AI Analysis"), button:has-text("Analyze")').first();
    await analyzeButton.click();

    // Monitor analysis progress
    const analysisId = await helpers.monitorAnalysisProgress(page, 'GPT-o1 Reasoning Analysis');

    expect(analysisId).toBeTruthy();
    console.log('✓ GPT-o1 reasoning chain analysis completed successfully');
  });







  test('should test reasoning chain API endpoints', async ({ page, request }) => {
    // Test reasoning chain API endpoints
    const healthResponse = await request.get('http://localhost:3001/api/health');
    expect(healthResponse.ok()).toBeTruthy();

    // Test reasoning stats endpoint
    const statsResponse = await request.get('http://localhost:3001/api/reasoning/stats');
    if (statsResponse.ok()) {
      const statsData = await statsResponse.json();
      expect(statsData.success).toBeTruthy();
      expect(statsData.data).toBeDefined();

      console.log('✓ Reasoning stats API working:', {
        activeChains: statsData.data.activeChains,
        totalTemplates: statsData.data.totalTemplates
      });
    }

    console.log('✓ Reasoning chain API endpoints validated');
  });

  test('should maintain backward compatibility with existing analysis', async ({ page }) => {
    // Verify that reasoning visualization doesn't break existing functionality

    // Upload test file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testPdfPath);

    await helpers.waitForFileValidation(10000);

    // Test with standard GPT-4o model (existing functionality)
    const modelSelect = page.locator('[data-testid="model-select"], .select-trigger').first();
    await modelSelect.click();

    const gpt4oOption = page.locator('text=GPT-4o');
    if (await gpt4oOption.isVisible()) {
      await gpt4oOption.click();
    }

    // Start analysis
    const analyzeButton = page.locator('button:has-text("Start AI Analysis"), button:has-text("Analyze")').first();
    await analyzeButton.click();

    // Verify existing analysis flow still works
    const analysisId = await helpers.monitorAnalysisProgress(page, 'Standard Analysis');

    expect(analysisId).toBeTruthy();
    console.log('✓ Existing functionality preserved with reasoning visualization');
  });
});
