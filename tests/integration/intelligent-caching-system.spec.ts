import { test, expect } from '@playwright/test';
import path from 'path';
import { fileURLToPath } from 'url';
import { TestHelpers } from '../utils/test-helpers';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Priority 4 Feature 2: Intelligent Caching System with Redis-based Semantic Similarity
 * Tests the enhanced GPT cache service with vector embeddings and semantic matching
 */
test.describe('Intelligent Caching System - Priority 4 Feature 2', () => {
  const testPdfPath = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
    await helpers.navigateToPage('/analysis');
  });

  test('should demonstrate semantic similarity cache hits', async ({ page }) => {
    const startTime = Date.now();
    
    try {
      // First analysis with specific prompt
      await page.setInputFiles('input[type="file"]', testPdfPath);
      await page.waitForTimeout(1000);
      
      // Start first analysis
      await page.click('button:has-text("Start AI Analysis")');
      
      // Wait for analysis completion
      await page.waitForSelector('[data-testid="analysis-complete"], .analysis-results', {
        timeout: helpers.getAdaptiveTimeout(120000, 'semantic-cache-analysis')
      });
      
      // Get initial cache metrics
      const initialMetrics = await page.request.get('http://localhost:3001/api/cache/metrics');
      const initialData = await initialMetrics.json();
      
      // Second analysis with semantically similar but different prompt
      await page.reload();
      await page.setInputFiles('input[type="file"]', testPdfPath);
      await page.waitForTimeout(1000);
      
      // Start second analysis
      await page.click('button:has-text("Start AI Analysis")');
      
      // Wait for analysis completion
      await page.waitForSelector('[data-testid="analysis-complete"], .analysis-results', {
        timeout: helpers.getAdaptiveTimeout(120000, 'semantic-cache-analysis')
      });
      
      // Get updated cache metrics
      const updatedMetrics = await page.request.get('http://localhost:3001/api/cache/metrics');
      const updatedData = await updatedMetrics.json();
      
      // Verify semantic cache functionality
      expect(updatedData.data.totalRequests).toBeGreaterThan(initialData.data.totalRequests);
      expect(updatedData.data.semanticMetrics).toBeDefined();
      expect(updatedData.data.performance.semanticHitRate).toBeGreaterThanOrEqual(0);
      
      helpers.recordTestMetrics('semantic-similarity-cache-hits', startTime, true);
    } catch (error) {
      helpers.recordTestMetrics('semantic-similarity-cache-hits', startTime, false, 0, error.message);
      throw error;
    }
  });

  test('should track semantic similarity metrics accurately', async ({ page }) => {
    const startTime = Date.now();
    
    try {
      // Get initial metrics
      const initialResponse = await page.request.get('http://localhost:3001/api/cache/metrics');
      const initialData = await initialResponse.json();
      
      // Verify semantic metrics structure
      expect(initialData.data.semanticMetrics).toBeDefined();
      expect(initialData.data.semanticMetrics.averageSimilarityScore).toBeGreaterThanOrEqual(0);
      expect(initialData.data.semanticMetrics.embeddingGenerationTime).toBeGreaterThanOrEqual(0);
      expect(initialData.data.semanticMetrics.similarityCalculationTime).toBeGreaterThanOrEqual(0);
      
      // Verify performance metrics include semantic data
      expect(initialData.data.performance.semanticHitRate).toBeDefined();
      expect(initialData.data.performance.exactHitRate).toBeDefined();
      expect(initialData.data.efficiency.semanticEfficiency).toBeDefined();
      
      helpers.recordTestMetrics('semantic-metrics-tracking', startTime, true);
    } catch (error) {
      helpers.recordTestMetrics('semantic-metrics-tracking', startTime, false, 0, error.message);
      throw error;
    }
  });

  test('should achieve target API call reduction through intelligent caching', async ({ page }) => {
    const startTime = Date.now();
    
    try {
      // Perform multiple similar analyses to test cache efficiency
      const analysisCount = 3;
      let totalAnalysisTime = 0;
      
      for (let i = 0; i < analysisCount; i++) {
        await page.reload();
        await page.setInputFiles('input[type="file"]', testPdfPath);
        await page.waitForTimeout(1000);
        
        const analysisStart = Date.now();
        await page.click('button:has-text("Start AI Analysis")');
        
        await page.waitForSelector('[data-testid="analysis-complete"], .analysis-results', {
          timeout: helpers.getAdaptiveTimeout(120000, 'cache-efficiency-test')
        });
        
        totalAnalysisTime += Date.now() - analysisStart;
      }
      
      // Get final cache metrics
      const metricsResponse = await page.request.get('http://localhost:3001/api/cache/metrics');
      const metricsData = await metricsResponse.json();
      
      // Verify cache efficiency
      const hitRate = metricsData.data.hitRate;
      const costSavings = metricsData.data.costSavings.estimatedCostSaved;
      
      // Target: 60-80% API call reduction through caching
      if (metricsData.data.totalRequests >= 3) {
        expect(hitRate).toBeGreaterThanOrEqual(0.6); // At least 60% hit rate
        expect(costSavings).toBeGreaterThan(0); // Some cost savings achieved
      }
      
      helpers.recordTestMetrics('api-call-reduction-target', startTime, true);
    } catch (error) {
      helpers.recordTestMetrics('api-call-reduction-target', startTime, false, 0, error.message);
      throw error;
    }
  });

  test('should handle cache warmup with semantic patterns', async ({ page }) => {
    const startTime = Date.now();
    
    try {
      // Test cache warmup endpoint
      const warmupResponse = await page.request.post('http://localhost:3001/api/cache/warmup');
      expect(warmupResponse.status()).toBe(200);
      
      const warmupData = await warmupResponse.json();
      expect(warmupData.success).toBe(true);
      expect(warmupData.data.message).toContain('warmup initiated');
      
      // Verify that warmup affects cache metrics
      await page.waitForTimeout(2000); // Allow warmup to complete
      
      const metricsResponse = await page.request.get('http://localhost:3001/api/cache/metrics');
      const metricsData = await metricsResponse.json();
      
      // Semantic metrics should be available after warmup
      expect(metricsData.data.semanticMetrics).toBeDefined();
      
      helpers.recordTestMetrics('cache-warmup-semantic-patterns', startTime, true);
    } catch (error) {
      helpers.recordTestMetrics('cache-warmup-semantic-patterns', startTime, false, 0, error.message);
      throw error;
    }
  });

  test('should gracefully fallback to exact matching when semantic similarity fails', async ({ page }) => {
    const startTime = Date.now();
    
    try {
      // Perform analysis to test fallback behavior
      await page.setInputFiles('input[type="file"]', testPdfPath);
      await page.waitForTimeout(1000);
      
      await page.click('button:has-text("Start AI Analysis")');
      
      // Wait for analysis completion
      await page.waitForSelector('[data-testid="analysis-complete"], .analysis-results', {
        timeout: helpers.getAdaptiveTimeout(120000, 'fallback-behavior-test')
      });
      
      // Verify analysis completed successfully regardless of cache behavior
      const analysisResults = page.locator('[data-testid="analysis-complete"], .analysis-results');
      await expect(analysisResults).toBeVisible();
      
      // Verify that analysis content is present
      const cabinetCount = page.locator('text=/Total Cabinets.*\\d+/');
      await expect(cabinetCount).toBeVisible();
      
      helpers.recordTestMetrics('semantic-fallback-graceful', startTime, true);
    } catch (error) {
      helpers.recordTestMetrics('semantic-fallback-graceful', startTime, false, 0, error.message);
      throw error;
    }
  });

  test('should maintain thread-safe cache operations under concurrent requests', async ({ page }) => {
    const startTime = Date.now();
    
    try {
      // Test concurrent cache operations by triggering multiple analyses
      const concurrentAnalyses = [];
      
      for (let i = 0; i < 2; i++) {
        const analysisPromise = (async () => {
          const newPage = await page.context().newPage();
          await newPage.goto('/analysis');
          await newPage.setInputFiles('input[type="file"]', testPdfPath);
          await newPage.waitForTimeout(1000);
          await newPage.click('button:has-text("Start AI Analysis")');
          
          await newPage.waitForSelector('[data-testid="analysis-complete"], .analysis-results', {
            timeout: helpers.getAdaptiveTimeout(120000, 'concurrent-cache-test')
          });
          
          await newPage.close();
        })();
        
        concurrentAnalyses.push(analysisPromise);
      }
      
      // Wait for all concurrent analyses to complete
      await Promise.all(concurrentAnalyses);
      
      // Verify cache metrics are consistent
      const metricsResponse = await page.request.get('http://localhost:3001/api/cache/metrics');
      const metricsData = await metricsResponse.json();
      
      // Cache should handle concurrent operations without corruption
      expect(metricsData.data.totalRequests).toBeGreaterThanOrEqual(2);
      expect(metricsData.data.hitRate).toBeGreaterThanOrEqual(0);
      expect(metricsData.data.hitRate).toBeLessThanOrEqual(1);
      
      helpers.recordTestMetrics('thread-safe-cache-operations', startTime, true);
    } catch (error) {
      helpers.recordTestMetrics('thread-safe-cache-operations', startTime, false, 0, error.message);
      throw error;
    }
  });

  test('should provide comprehensive cache health monitoring', async ({ page }) => {
    const startTime = Date.now();
    
    try {
      // Test cache health endpoint
      const healthResponse = await page.request.get('http://localhost:3001/api/cache/health');
      expect(healthResponse.status()).toBe(200);
      
      const healthData = await healthResponse.json();
      expect(healthData.success).toBe(true);
      expect(healthData.data.status).toBeDefined();
      
      // Test real-time stats endpoint
      const statsResponse = await page.request.get('http://localhost:3001/api/cache/stats/realtime');
      expect(statsResponse.status()).toBe(200);
      
      const statsData = await statsResponse.json();
      expect(statsData.data.status).toBeDefined();
      expect(statsData.data.hitRate).toBeDefined();
      expect(statsData.data.costSavings).toBeDefined();
      expect(statsData.data.performance).toBeDefined();
      
      helpers.recordTestMetrics('cache-health-monitoring', startTime, true);
    } catch (error) {
      helpers.recordTestMetrics('cache-health-monitoring', startTime, false, 0, error.message);
      throw error;
    }
  });
});
