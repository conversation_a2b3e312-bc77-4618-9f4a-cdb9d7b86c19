/**
 * Test Infrastructure Refactoring Tests - Phase 3.4 Validation
 * 
 * These tests verify that the refactored Test Infrastructure maintains ~97-99% success rate
 * and full backward compatibility after the modular architecture implementation.
 * 
 * Refactoring: 951 lines → 4 specialized helpers (~200-250 lines each)
 * Architecture: B<PERSON><PERSON><PERSON><PERSON><PERSON>el<PERSON>, NetworkTestHelper, EnvironmentTestHelper, AuthTestHelper
 */

import { test, expect } from '@playwright/test';
import { 
  TestHelpers,
  BrowserTestHelper, 
  NetworkTestHelper, 
  EnvironmentTestHelper, 
  AuthTestHelper 
} from '../utils/test-helpers';

test.describe('Test Infrastructure - Phase 3.4 Refactoring Validation', () => {

  test('should maintain backward compatibility with legacy TestHelpers class', async ({ page }) => {
    console.log('🧪 Testing Phase 3.4 test infrastructure refactoring backward compatibility...');
    console.log('📊 Refactoring: 951 lines → 4 specialized helpers (~200-250 lines each)');
    
    const testHelpers = new TestHelpers(page);
    
    // Test basic functionality
    const browserInfo = testHelpers.getBrowserInfo();
    expect(browserInfo).toHaveProperty('name');
    expect(browserInfo).toHaveProperty('version');
    expect(browserInfo).toHaveProperty('platform');
    
    console.log(`✅ Legacy TestHelpers class working correctly on ${browserInfo.name}`);
  });

  test('should provide modular BrowserTestHelper functionality', async ({ page }) => {
    console.log('🧪 Testing BrowserTestHelper modular functionality...');
    
    const browserHelper = new BrowserTestHelper(page);
    
    // Test adaptive timeout calculation
    const baseTimeout = 30000;
    const adaptiveTimeout = browserHelper.getAdaptiveTimeout(baseTimeout, 'test-navigation');
    expect(adaptiveTimeout).toBeGreaterThan(0);
    expect(adaptiveTimeout).toBeGreaterThanOrEqual(baseTimeout * 0.8); // Should be within reasonable range
    
    // Test browser info
    const browserInfo = browserHelper.getBrowserInfo();
    expect(browserInfo.name).toMatch(/chromium|firefox|webkit/);
    
    // Test browser capabilities
    const capabilities = await browserHelper.checkBrowserCapabilities();
    expect(capabilities).toHaveProperty('webgl');
    expect(capabilities).toHaveProperty('websockets');
    expect(capabilities).toHaveProperty('fileApi');
    
    console.log(`✅ BrowserTestHelper working correctly with capabilities:`, capabilities);
  });

  test('should provide modular NetworkTestHelper functionality', async ({ page }) => {
    console.log('🧪 Testing NetworkTestHelper modular functionality...');
    
    const networkHelper = new NetworkTestHelper(page);
    
    // Test network condition detection
    const networkCondition = await networkHelper.detectNetworkConditions();
    expect(networkCondition).toHaveProperty('quality');
    expect(networkCondition).toHaveProperty('latency');
    expect(networkCondition).toHaveProperty('bandwidth');
    expect(networkCondition).toHaveProperty('recommendedTimeout');
    
    // Test adaptive timeout
    const baseTimeout = 30000;
    const adaptiveTimeout = networkHelper.getAdaptiveTimeout(baseTimeout);
    expect(adaptiveTimeout).toBeGreaterThan(0);
    
    // Test API endpoint testing
    const apiResult = await networkHelper.testApiEndpoint('https://httpbin.org/get');
    expect(apiResult).toHaveProperty('success');
    expect(apiResult).toHaveProperty('status');
    expect(apiResult).toHaveProperty('responseTime');
    
    console.log(`✅ NetworkTestHelper working correctly with network quality: ${networkCondition.quality}`);
  });

  test('should provide modular EnvironmentTestHelper functionality', async ({ page }) => {
    console.log('🧪 Testing EnvironmentTestHelper modular functionality...');
    
    const environmentHelper = new EnvironmentTestHelper(page);
    
    // Test environment setup
    await environmentHelper.setupTestEnvironment();
    
    // Test environment validation
    await environmentHelper.validateEnvironment();
    
    // Test system resource monitoring
    const resources = await environmentHelper.monitorSystemResources();
    expect(resources).toHaveProperty('memory');
    expect(resources).toHaveProperty('cpu');
    expect(resources).toHaveProperty('disk');
    expect(resources.memory).toHaveProperty('percentage');
    
    // Test environment suitability check
    const suitability = await environmentHelper.isEnvironmentSuitable();
    expect(suitability).toHaveProperty('suitable');
    expect(suitability).toHaveProperty('issues');
    expect(suitability).toHaveProperty('recommendations');
    
    // Test environment info
    const envInfo = environmentHelper.getEnvironmentInfo();
    expect(envInfo).toHaveProperty('platform');
    expect(envInfo).toHaveProperty('nodeVersion');
    expect(envInfo).toHaveProperty('workingDirectory');
    
    console.log(`✅ EnvironmentTestHelper working correctly on ${envInfo.platform}`);
  });

  test('should provide modular AuthTestHelper functionality', async ({ page }) => {
    console.log('🧪 Testing AuthTestHelper modular functionality...');
    
    const authHelper = new AuthTestHelper(page);
    
    // Test user authentication
    const authState = await authHelper.authenticateUser({
      email: '<EMAIL>',
      role: 'user'
    });
    
    expect(authState).toHaveProperty('isAuthenticated', true);
    expect(authState).toHaveProperty('user');
    expect(authState.user).toHaveProperty('email', '<EMAIL>');
    expect(authState.user).toHaveProperty('role', 'user');
    expect(authState.user).toHaveProperty('permissions');
    
    // Test permission checking
    const hasReadPermission = authHelper.hasPermission('read:own');
    expect(hasReadPermission).toBe(true);
    
    const hasAdminPermission = authHelper.hasPermission('manage:system');
    expect(hasAdminPermission).toBe(false); // User role shouldn't have admin permissions
    
    // Test role checking
    const hasUserRole = authHelper.hasRole('user');
    expect(hasUserRole).toBe(true);
    
    const hasAdminRole = authHelper.hasRole('admin');
    expect(hasAdminRole).toBe(false);
    
    // Test session validation
    const sessionValidation = await authHelper.validateSessionExpiry();
    expect(sessionValidation).toHaveProperty('isValid', true);
    expect(sessionValidation).toHaveProperty('timeRemaining');
    
    // Test logout
    await authHelper.logoutUser();
    expect(authHelper.isAuthenticated()).toBe(false);
    
    console.log('✅ AuthTestHelper working correctly with authentication flow');
  });

  test('should maintain performance characteristics after refactoring', async ({ page }) => {
    console.log('🧪 Testing performance characteristics...');
    
    const startTime = Date.now();
    
    // Test initialization performance
    const testHelpers = new TestHelpers(page);
    const browserHelper = new BrowserTestHelper(page);
    const networkHelper = new NetworkTestHelper(page);
    const environmentHelper = new EnvironmentTestHelper(page);
    const authHelper = new AuthTestHelper(page);
    
    const initTime = Date.now() - startTime;
    
    // Initialization should be fast (under 100ms)
    expect(initTime).toBeLessThan(100);
    
    // Test method call performance
    const methodStartTime = Date.now();
    const adaptiveTimeout = browserHelper.getAdaptiveTimeout(30000, 'performance-test');
    const methodTime = Date.now() - methodStartTime;
    
    // Method calls should be very fast (under 10ms)
    expect(methodTime).toBeLessThan(10);
    expect(adaptiveTimeout).toBeGreaterThan(0);
    
    console.log(`✅ Performance maintained: Init ${initTime}ms, Method ${methodTime}ms`);
  });

  test('should support cross-browser compatibility', async ({ page }) => {
    console.log('🧪 Testing cross-browser compatibility...');
    
    const browserHelper = new BrowserTestHelper(page);
    const browserInfo = browserHelper.getBrowserInfo();
    
    // Test browser-specific timeout adjustments
    const baseTimeout = 30000;
    const adaptiveTimeout = browserHelper.getAdaptiveTimeout(baseTimeout, 'cross-browser-test');
    
    // Timeout should be adjusted based on browser
    if (browserInfo.name === 'firefox') {
      expect(adaptiveTimeout).toBeGreaterThanOrEqual(baseTimeout); // Firefox typically needs more time
    } else if (browserInfo.name === 'webkit') {
      expect(adaptiveTimeout).toBeGreaterThanOrEqual(baseTimeout); // WebKit may need adjustments
    } else {
      expect(adaptiveTimeout).toBeGreaterThanOrEqual(baseTimeout * 0.8); // Chromium baseline
    }
    
    // Test browser capabilities detection
    const capabilities = await browserHelper.checkBrowserCapabilities();
    
    // All modern browsers should support these features
    expect(capabilities.websockets).toBe(true);
    expect(capabilities.fileApi).toBe(true);
    
    console.log(`✅ Cross-browser compatibility verified for ${browserInfo.name}`);
  });

  test('should handle error cases gracefully', async ({ page }) => {
    console.log('🧪 Testing error handling...');
    
    const networkHelper = new NetworkTestHelper(page);
    
    // Test API endpoint with invalid URL
    const invalidResult = await networkHelper.testApiEndpoint('http://invalid-url-that-does-not-exist.com');
    expect(invalidResult.success).toBe(false);
    expect(invalidResult).toHaveProperty('error');
    
    // Test WebSocket connection to invalid URL
    const wsResult = await networkHelper.monitorWebSocketConnection('ws://invalid-websocket-url.com', 5000);
    expect(wsResult.connected).toBe(false);
    expect(wsResult).toHaveProperty('error');
    
    console.log('✅ Error handling working correctly');
  });

  test('should support test environment initialization', async ({ page }) => {
    console.log('🧪 Testing test environment initialization...');
    
    const testHelpers = new TestHelpers(page);
    
    // Test legacy initialization method
    const initResult = await testHelpers.initializeTestEnvironment('refactoring-validation');
    
    expect(initResult).toHaveProperty('networkCondition');
    expect(initResult).toHaveProperty('environmentValid');
    expect(initResult).toHaveProperty('recommendations');
    
    expect(initResult.networkCondition).toHaveProperty('quality');
    expect(typeof initResult.environmentValid).toBe('boolean');
    expect(Array.isArray(initResult.recommendations)).toBe(true);
    
    console.log(`✅ Environment initialization working: ${initResult.environmentValid ? 'Valid' : 'Issues detected'}`);
  });

  test('should maintain test success rate standards', async ({ page }) => {
    console.log('🧪 Testing success rate maintenance...');
    
    const testHelpers = new TestHelpers(page);
    let successCount = 0;
    const totalTests = 10;
    
    // Run multiple operations to test reliability
    for (let i = 0; i < totalTests; i++) {
      try {
        const browserInfo = testHelpers.getBrowserInfo();
        const adaptiveTimeout = testHelpers.getAdaptiveTimeout(30000, `reliability-test-${i}`);
        
        // Basic validation
        expect(browserInfo).toHaveProperty('name');
        expect(adaptiveTimeout).toBeGreaterThan(0);
        
        successCount++;
      } catch (error) {
        console.warn(`Test ${i + 1} failed:`, error);
      }
    }
    
    const successRate = (successCount / totalTests) * 100;
    
    // Should maintain high success rate (>95%)
    expect(successRate).toBeGreaterThanOrEqual(95);
    
    console.log(`✅ Success rate maintained: ${successRate}% (${successCount}/${totalTests})`);
  });

  test('should provide comprehensive test artifact management', async ({ page }) => {
    console.log('🧪 Testing test artifact management...');
    
    const environmentHelper = new EnvironmentTestHelper(page);
    const browserHelper = new BrowserTestHelper(page);
    
    // Test screenshot functionality
    const screenshotName = await browserHelper.takeScreenshot('refactoring-validation');
    expect(screenshotName).toMatch(/refactoring-validation.*\.png$/);
    
    // Test test report directory creation
    const reportDir = await environmentHelper.createTestReportDirectory('phase-3-4-validation');
    expect(reportDir).toContain('phase-3-4-validation');
    
    // Test artifact saving
    await environmentHelper.saveTestArtifacts('refactoring-test', {
      logs: ['Test log entry 1', 'Test log entry 2'],
      data: { testResult: 'success', phase: '3.4' }
    });
    
    console.log(`✅ Test artifact management working correctly`);
  });
});
