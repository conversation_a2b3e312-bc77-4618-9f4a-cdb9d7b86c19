import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';

test.describe('GPT Cache System Integration', () => {
  let testHelpers: TestHelpers;

  // Helper method to upload a test image
  async function uploadTestImage(page: any, filename: string = 'kitchen-sample.jpg') {
    const fileInput = page.locator('input[type="file"]');
    
    try {
      await fileInput.setInputFiles(`tests/fixtures/${filename}`);
    } catch {
      // Fallback: simulate file upload with a mock file
      await page.evaluate(() => {
        const input = document.querySelector('input[type="file"]') as HTMLInputElement;
        if (input) {
          const file = new File(['mock kitchen image'], filename, { type: 'image/jpeg' });
          const dataTransfer = new DataTransfer();
          dataTransfer.items.add(file);
          input.files = dataTransfer.files;
          input.dispatchEvent(new Event('change', { bubbles: true }));
        }
      });
    }
  }

  // Helper method to wait for analysis completion
  async function waitForAnalysisCompletion(page: any, timeout: number = 60000) {
    await page.waitForSelector('[data-testid="analysis-progress"], .analysis-progress, text=Analyzing', { timeout: 10000 });
    await page.waitForSelector(
      '[data-testid="analysis-complete"], .analysis-results, text=Analysis completed, text=Total Cabinets',
      { timeout }
    );
  }

  test.beforeEach(async ({ page }) => {
    testHelpers = new TestHelpers(page);
    await testHelpers.initializeTestEnvironment('gpt-cache-system');
    await testHelpers.navigateToPage('/analysis', 3, 'cache-system-navigation');
  });

  test('should provide cache health status endpoint', async ({ page }) => {
    const startTime = Date.now();
    
    try {
      // Test cache health endpoint
      const response = await page.request.get('http://localhost:3001/api/cache/health');
      expect(response.status()).toBe(200);
      
      const healthData = await response.json();
      expect(healthData.success).toBe(true);
      expect(healthData.data).toHaveProperty('status');
      expect(healthData.data).toHaveProperty('connected');
      expect(healthData.data).toHaveProperty('timestamp');
      
      testHelpers.recordTestMetrics('cache-health-endpoint', startTime, true);
    } catch (error) {
      testHelpers.recordTestMetrics('cache-health-endpoint', startTime, false, 0, error.message);
      throw error;
    }
  });

  test('should provide cache metrics endpoint', async ({ page }) => {
    const startTime = Date.now();
    
    try {
      // Test cache metrics endpoint
      const response = await page.request.get('http://localhost:3001/api/cache/metrics');
      expect(response.status()).toBe(200);
      
      const metricsData = await response.json();
      expect(metricsData.success).toBe(true);
      expect(metricsData.data).toHaveProperty('totalRequests');
      expect(metricsData.data).toHaveProperty('cacheHits');
      expect(metricsData.data).toHaveProperty('cacheMisses');
      expect(metricsData.data).toHaveProperty('hitRate');
      expect(metricsData.data).toHaveProperty('costSavings');
      expect(metricsData.data).toHaveProperty('performance');
      expect(metricsData.data).toHaveProperty('efficiency');
      
      testHelpers.recordTestMetrics('cache-metrics-endpoint', startTime, true);
    } catch (error) {
      testHelpers.recordTestMetrics('cache-metrics-endpoint', startTime, false, 0, error.message);
      throw error;
    }
  });

  test('should cache GPT-o1 analysis results and improve response times', async ({ page }) => {
    const startTime = Date.now();
    
    try {
      // First analysis - should be uncached
      await uploadTestImage(page);
      
      const firstAnalysisStart = Date.now();
      await waitForAnalysisCompletion(page);
      const firstAnalysisTime = Date.now() - firstAnalysisStart;
      
      // Get initial cache metrics
      const initialMetrics = await page.request.get('http://localhost:3001/api/cache/metrics');
      const initialData = await initialMetrics.json();

      // Second analysis with same image - should hit cache
      await page.reload();
      await uploadTestImage(page);

      const secondAnalysisStart = Date.now();
      await waitForAnalysisCompletion(page);
      const secondAnalysisTime = Date.now() - secondAnalysisStart;

      // Get updated cache metrics
      const updatedMetrics = await page.request.get('http://localhost:3001/api/cache/metrics');
      const updatedData = await updatedMetrics.json();
      
      // Verify cache hit occurred
      expect(updatedData.data.cacheHits).toBeGreaterThan(initialData.data.cacheHits);
      
      // Verify response time improvement (cached should be significantly faster)
      expect(secondAnalysisTime).toBeLessThan(firstAnalysisTime * 0.5); // At least 50% faster
      
      testHelpers.recordTestMetrics('cache-performance-improvement', startTime, true);
    } catch (error) {
      testHelpers.recordTestMetrics('cache-performance-improvement', startTime, false, 0, error.message);
      throw error;
    }
  });

  test('should track cost savings from cache hits', async ({ page }) => {
    const startTime = Date.now();
    
    try {
      // Perform multiple analyses to generate cache hits
      for (let i = 0; i < 3; i++) {
        await uploadTestImage(page);
        await waitForAnalysisCompletion(page);
        
        if (i < 2) {
          await page.reload();
        }
      }
      
      // Check cost savings metrics
      const metricsResponse = await page.request.get('http://localhost:3001/api/cache/metrics');
      const metricsData = await metricsResponse.json();
      
      expect(metricsData.data.costSavings.tokensAvoided).toBeGreaterThan(0);
      expect(metricsData.data.costSavings.estimatedCostSaved).toBeGreaterThan(0);
      expect(metricsData.data.efficiency.avgCostSavingsPerHit).toBeDefined();
      
      testHelpers.recordTestMetrics('cache-cost-savings-tracking', startTime, true);
    } catch (error) {
      testHelpers.recordTestMetrics('cache-cost-savings-tracking', startTime, false, 0, error.message);
      throw error;
    }
  });

  test('should support cache invalidation', async ({ page }) => {
    const startTime = Date.now();
    
    try {
      // First, create some cached entries
      await uploadTestImage(page);
      await waitForAnalysisCompletion(page);
      
      // Get initial metrics
      const initialMetrics = await page.request.get('http://localhost:3001/api/cache/metrics');
      const initialData = await initialMetrics.json();

      // Invalidate cache entries
      const invalidateResponse = await page.request.post('http://localhost:3001/api/cache/invalidate', {
        data: { pattern: 'gpt_cache:*' },
        headers: { 'Content-Type': 'application/json' }
      });
      
      expect(invalidateResponse.status()).toBe(200);
      
      const invalidateData = await invalidateResponse.json();
      expect(invalidateData.success).toBe(true);
      expect(invalidateData.data.deletedCount).toBeGreaterThanOrEqual(0);
      
      testHelpers.recordTestMetrics('cache-invalidation', startTime, true);
    } catch (error) {
      testHelpers.recordTestMetrics('cache-invalidation', startTime, false, 0, error.message);
      throw error;
    }
  });

  test('should provide real-time cache statistics', async ({ page }) => {
    const startTime = Date.now();
    
    try {
      // Test real-time stats endpoint
      const response = await page.request.get('http://localhost:3001/api/cache/stats/realtime');
      expect(response.status()).toBe(200);
      
      const statsData = await response.json();
      expect(statsData.success).toBe(true);
      expect(statsData.data).toHaveProperty('status');
      expect(statsData.data).toHaveProperty('hitRate');
      expect(statsData.data).toHaveProperty('totalRequests');
      expect(statsData.data).toHaveProperty('costSavings');
      expect(statsData.data).toHaveProperty('performance');
      expect(statsData.data).toHaveProperty('storage');
      expect(statsData.data).toHaveProperty('timestamp');
      
      // Verify status is either 'operational' or 'degraded'
      expect(['operational', 'degraded']).toContain(statsData.data.status);
      
      testHelpers.recordTestMetrics('cache-realtime-stats', startTime, true);
    } catch (error) {
      testHelpers.recordTestMetrics('cache-realtime-stats', startTime, false, 0, error.message);
      throw error;
    }
  });

  test('should handle cache warmup functionality', async ({ page }) => {
    const startTime = Date.now();
    
    try {
      // Test cache warmup endpoint
      const response = await page.request.post('http://localhost:3001/api/cache/warmup');
      expect(response.status()).toBe(200);
      
      const warmupData = await response.json();
      expect(warmupData.success).toBe(true);
      expect(warmupData.data.message).toContain('warmup initiated');
      
      testHelpers.recordTestMetrics('cache-warmup', startTime, true);
    } catch (error) {
      testHelpers.recordTestMetrics('cache-warmup', startTime, false, 0, error.message);
      throw error;
    }
  });

  test('should maintain cache security with proper access controls', async ({ page }) => {
    const startTime = Date.now();
    
    try {
      // Test cache clear endpoint without authorization (should fail)
      const unauthorizedResponse = await page.request.delete('http://localhost:3001/api/cache/clear');
      expect(unauthorizedResponse.status()).toBe(401);

      const unauthorizedData = await unauthorizedResponse.json();
      expect(unauthorizedData.success).toBe(false);
      expect(unauthorizedData.error).toContain('Authorization required');

      // Test invalid invalidation pattern (should fail)
      const invalidPatternResponse = await page.request.post('http://localhost:3001/api/cache/invalidate', {
        data: { pattern: 'invalid_pattern' },
        headers: { 'Content-Type': 'application/json' }
      });
      
      expect(invalidPatternResponse.status()).toBe(403);
      
      const invalidPatternData = await invalidPatternResponse.json();
      expect(invalidPatternData.success).toBe(false);
      expect(invalidPatternData.error).toContain('Pattern not allowed');
      
      testHelpers.recordTestMetrics('cache-security-controls', startTime, true);
    } catch (error) {
      testHelpers.recordTestMetrics('cache-security-controls', startTime, false, 0, error.message);
      throw error;
    }
  });

  test('should handle cache failures gracefully', async ({ page }) => {
    const startTime = Date.now();
    
    try {
      // Test analysis when cache might be unavailable
      await uploadTestImage(page);
      await waitForAnalysisCompletion(page);
      
      // Analysis should complete even if cache is not available
      const analysisResults = page.locator('[data-testid="analysis-complete"], .analysis-results');
      await expect(analysisResults).toBeVisible();
      
      // Verify that analysis content is present
      const cabinetCount = page.locator('text=/Total Cabinets.*\\d+/');
      await expect(cabinetCount).toBeVisible();
      
      testHelpers.recordTestMetrics('cache-failure-graceful-handling', startTime, true);
    } catch (error) {
      testHelpers.recordTestMetrics('cache-failure-graceful-handling', startTime, false, 0, error.message);
      throw error;
    }
  });

  test('should work across different browsers', async ({ page, browserName }) => {
    const startTime = Date.now();
    
    try {
      // Test basic cache functionality across browsers
      await uploadTestImage(page);
      await waitForAnalysisCompletion(page);
      
      // Test cache metrics endpoint
      const metricsResponse = await page.request.get('http://localhost:3001/api/cache/metrics');
      expect(metricsResponse.status()).toBe(200);
      
      const metricsData = await metricsResponse.json();
      expect(metricsData.success).toBe(true);
      
      testHelpers.recordTestMetrics(`cache-${browserName}-compatibility`, startTime, true);
    } catch (error) {
      testHelpers.recordTestMetrics(`cache-${browserName}-compatibility`, startTime, false, 0, error.message);
      throw error;
    }
  });

  test.afterEach(async ({ page }) => {
    // Record test completion metrics
    if (testHelpers) {
      await testHelpers.takeScreenshot('cache-system-test-complete');
    }
  });
});
