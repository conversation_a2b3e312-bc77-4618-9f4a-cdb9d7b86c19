import { test, expect } from '@playwright/test';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

test.describe('Enhanced Hardware Recognition', () => {
  const testPdfPath = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
  const testPngPath = path.join(__dirname, '../fixtures/kitchen-design-test.png');

  test.beforeEach(async () => {
    // Ensure test files exist
    if (!fs.existsSync(testPdfPath)) {
      throw new Error(`Test PDF file not found: ${testPdfPath}`);
    }
    if (!fs.existsSync(testPngPath)) {
      throw new Error(`Test PNG file not found: ${testPngPath}`);
    }
  });

  test('should provide enhanced hardware recognition API endpoint', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/enhanced-hardware', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        enableBrandRecognition: 'true',
        enableModelIdentification: 'true',
        enableCompatibilityAnalysis: 'true',
        enableUpgradeRecommendations: 'true',
        confidenceThreshold: '0.7',
        analysisDepth: 'DETAILED'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('enhancedHardware');
    
    const hardware = data.data.enhancedHardware;
    expect(hardware).toHaveProperty('detectedHardware');
    expect(hardware).toHaveProperty('brandSummary');
    expect(hardware).toHaveProperty('upgradeRecommendations');
    expect(hardware).toHaveProperty('compatibilityMatrix');
    expect(hardware).toHaveProperty('processingMetrics');

    console.log('✅ Enhanced hardware recognition completed');
    console.log(`🔧 Hardware items detected: ${hardware.processingMetrics.hardwareItemsDetected}`);
    console.log(`🏷️ Brands identified: ${hardware.processingMetrics.brandsIdentified}`);
    console.log(`📊 Confidence score: ${hardware.processingMetrics.confidenceScore}`);
  });

  test('should identify hardware brands and models', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/enhanced-hardware', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        enableBrandRecognition: 'true',
        enableModelIdentification: 'true',
        analysisDepth: 'COMPREHENSIVE'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    const detectedHardware = data.data.enhancedHardware.detectedHardware;
    
    expect(Array.isArray(detectedHardware)).toBeTruthy();
    
    if (detectedHardware.length > 0) {
      const hardwareItem = detectedHardware[0];
      expect(hardwareItem).toHaveProperty('id');
      expect(hardwareItem).toHaveProperty('type');
      expect(hardwareItem).toHaveProperty('confidence');
      expect(hardwareItem).toHaveProperty('visualFeatures');
      
      console.log(`🔧 Hardware type: ${hardwareItem.type}`);
      console.log(`🎯 Overall confidence: ${hardwareItem.confidence.overall}`);
      
      if (hardwareItem.detectedBrand) {
        expect(hardwareItem.detectedBrand).toHaveProperty('name');
        expect(hardwareItem.detectedBrand).toHaveProperty('qualityRating');
        expect(hardwareItem.detectedBrand).toHaveProperty('priceRange');
        
        console.log(`🏷️ Brand: ${hardwareItem.detectedBrand.name}`);
        console.log(`⭐ Quality rating: ${hardwareItem.detectedBrand.qualityRating}`);
        console.log(`💰 Price range: ${hardwareItem.detectedBrand.priceRange}`);
      }
      
      if (hardwareItem.detectedModel) {
        expect(hardwareItem.detectedModel).toHaveProperty('model');
        expect(hardwareItem.detectedModel).toHaveProperty('features');
        
        console.log(`📦 Model: ${hardwareItem.detectedModel.model}`);
        console.log(`✨ Features: ${hardwareItem.detectedModel.features.join(', ')}`);
      }
      
      // Validate visual features
      const visualFeatures = hardwareItem.visualFeatures;
      expect(visualFeatures).toHaveProperty('finish');
      expect(visualFeatures).toHaveProperty('style');
      expect(visualFeatures).toHaveProperty('material');
      
      console.log(`🎨 Finish: ${visualFeatures.finish}`);
      console.log(`🎭 Style: ${visualFeatures.style}`);
      console.log(`🔩 Material: ${visualFeatures.material}`);
    }
  });

  test('should analyze hardware compatibility', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/enhanced-hardware', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        enableCompatibilityAnalysis: 'true',
        analysisDepth: 'DETAILED'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    const compatibilityMatrix = data.data.enhancedHardware.compatibilityMatrix;
    
    expect(Array.isArray(compatibilityMatrix)).toBeTruthy();
    
    if (compatibilityMatrix.length > 0) {
      const compatibility = compatibilityMatrix[0];
      expect(compatibility).toHaveProperty('hardwareType');
      expect(compatibility).toHaveProperty('compatibleStyles');
      expect(compatibility).toHaveProperty('incompatibleStyles');
      expect(compatibility).toHaveProperty('notes');
      
      console.log(`🔧 Hardware type: ${compatibility.hardwareType}`);
      console.log(`✅ Compatible styles: ${compatibility.compatibleStyles.join(', ')}`);
      console.log(`❌ Incompatible styles: ${compatibility.incompatibleStyles.join(', ')}`);
      console.log(`📝 Notes: ${compatibility.notes}`);
    }
    
    // Check individual hardware compatibility
    const detectedHardware = data.data.enhancedHardware.detectedHardware;
    if (detectedHardware.length > 0) {
      const hardwareItem = detectedHardware[0];
      const compatibility = hardwareItem.compatibility;
      
      expect(compatibility).toHaveProperty('cabinetStyles');
      expect(compatibility).toHaveProperty('cabinetTypes');
      expect(compatibility).toHaveProperty('doorThickness');
      expect(compatibility).toHaveProperty('mountingRequirements');
      expect(compatibility).toHaveProperty('installationNotes');
      
      console.log(`🏠 Compatible cabinet styles: ${compatibility.cabinetStyles.join(', ')}`);
      console.log(`📏 Door thickness: ${compatibility.doorThickness.min}-${compatibility.doorThickness.max}${compatibility.doorThickness.unit}`);
      console.log(`🔧 Mounting requirements: ${compatibility.mountingRequirements.join(', ')}`);
    }
  });

  test('should assess installation complexity', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/enhanced-hardware', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        enableBrandRecognition: 'true',
        enableModelIdentification: 'true'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    const detectedHardware = data.data.enhancedHardware.detectedHardware;
    
    if (detectedHardware.length > 0) {
      const hardwareItem = detectedHardware[0];
      const installationComplexity = hardwareItem.installationComplexity;
      
      expect(installationComplexity).toHaveProperty('level');
      expect(installationComplexity).toHaveProperty('timeEstimate');
      expect(installationComplexity).toHaveProperty('toolsRequired');
      expect(installationComplexity).toHaveProperty('skillsRequired');
      expect(installationComplexity).toHaveProperty('difficultyFactors');
      expect(installationComplexity).toHaveProperty('professionalRecommended');
      
      console.log(`⚡ Complexity level: ${installationComplexity.level}`);
      console.log(`⏱️ Time estimate: ${installationComplexity.timeEstimate} minutes`);
      console.log(`🔨 Tools required: ${installationComplexity.toolsRequired.join(', ')}`);
      console.log(`🎓 Skills required: ${installationComplexity.skillsRequired.join(', ')}`);
      console.log(`👨‍🔧 Professional recommended: ${installationComplexity.professionalRecommended ? 'Yes' : 'No'}`);
      
      if (installationComplexity.difficultyFactors.length > 0) {
        console.log(`⚠️ Difficulty factors: ${installationComplexity.difficultyFactors.join(', ')}`);
      }
    }
  });

  test('should generate upgrade recommendations', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/enhanced-hardware', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        enableUpgradeRecommendations: 'true',
        analysisDepth: 'COMPREHENSIVE'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    const upgradeRecommendations = data.data.enhancedHardware.upgradeRecommendations;
    
    expect(Array.isArray(upgradeRecommendations)).toBeTruthy();
    console.log(`💡 Upgrade recommendations: ${upgradeRecommendations.length}`);
    
    if (upgradeRecommendations.length > 0) {
      const recommendation = upgradeRecommendations[0];
      expect(recommendation).toHaveProperty('type');
      expect(recommendation).toHaveProperty('priority');
      expect(recommendation).toHaveProperty('description');
      expect(recommendation).toHaveProperty('estimatedCost');
      expect(recommendation).toHaveProperty('expectedBenefit');
      
      console.log(`🎯 Recommendation type: ${recommendation.type}`);
      console.log(`⚡ Priority: ${recommendation.priority}`);
      console.log(`📝 Description: ${recommendation.description}`);
      console.log(`💰 Estimated cost: $${recommendation.estimatedCost}`);
      console.log(`📈 Expected benefit: ${recommendation.expectedBenefit}`);
    }
  });

  test('should provide brand summary and quality assessment', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/enhanced-hardware', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        enableBrandRecognition: 'true',
        analysisDepth: 'DETAILED'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    const brandSummary = data.data.enhancedHardware.brandSummary;
    
    expect(brandSummary).toHaveProperty('primaryBrands');
    expect(brandSummary).toHaveProperty('qualityAssessment');
    expect(brandSummary).toHaveProperty('consistencyScore');
    
    console.log(`🏷️ Primary brands: ${brandSummary.primaryBrands.join(', ')}`);
    console.log(`⭐ Quality assessment: ${brandSummary.qualityAssessment}`);
    console.log(`📊 Consistency score: ${brandSummary.consistencyScore}`);
    
    expect(['BUDGET', 'MID_RANGE', 'PREMIUM', 'MIXED']).toContain(brandSummary.qualityAssessment);
    expect(brandSummary.consistencyScore).toBeGreaterThanOrEqual(0);
    expect(brandSummary.consistencyScore).toBeLessThanOrEqual(1);
  });

  test('should handle PNG files for hardware recognition', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/enhanced-hardware', {
      multipart: {
        file: {
          name: 'kitchen-design-test.png',
          mimeType: 'image/png',
          buffer: fs.readFileSync(testPngPath),
        },
        enableBrandRecognition: 'true'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('enhancedHardware');
    
    console.log('✅ PNG file hardware recognition completed successfully');
  });

  test('should integrate with existing analysis pipeline', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/upload', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        useGPT4o: 'true',
        useReasoning: 'true',
        enableEnhancedHardwareRecognition: 'true',
        enableBrandRecognition: 'true',
        enableModelIdentification: 'true',
        enableCompatibilityAnalysis: 'true'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('success', true);
    expect(data.data).toHaveProperty('analysisId');
    
    console.log('✅ Enhanced hardware recognition integrated with standard analysis pipeline');
  });

  test('should validate confidence scoring across all components', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/enhanced-hardware', {
      multipart: {
        file: {
          name: 'kitchen-design-test.pdf',
          mimeType: 'application/pdf',
          buffer: fs.readFileSync(testPdfPath),
        },
        enableBrandRecognition: 'true',
        enableModelIdentification: 'true',
        enableCompatibilityAnalysis: 'true',
        confidenceThreshold: '0.8'
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    const detectedHardware = data.data.enhancedHardware.detectedHardware;
    
    if (detectedHardware.length > 0) {
      const hardwareItem = detectedHardware[0];
      const confidence = hardwareItem.confidence;
      
      expect(confidence).toHaveProperty('brandIdentification');
      expect(confidence).toHaveProperty('modelIdentification');
      expect(confidence).toHaveProperty('compatibilityAssessment');
      expect(confidence).toHaveProperty('pricingAccuracy');
      expect(confidence).toHaveProperty('overall');
      
      // Validate confidence ranges
      Object.values(confidence).forEach(score => {
        expect(score).toBeGreaterThanOrEqual(0);
        expect(score).toBeLessThanOrEqual(1);
      });
      
      console.log(`🎯 Brand identification confidence: ${confidence.brandIdentification}`);
      console.log(`📦 Model identification confidence: ${confidence.modelIdentification}`);
      console.log(`🔗 Compatibility assessment confidence: ${confidence.compatibilityAssessment}`);
      console.log(`💰 Pricing accuracy confidence: ${confidence.pricingAccuracy}`);
      console.log(`📊 Overall confidence: ${confidence.overall}`);
    }
  });

  test('should handle hardware recognition errors gracefully', async ({ request }) => {
    const response = await request.post('http://localhost:3001/api/analysis/enhanced-hardware', {
      multipart: {
        file: {
          name: 'invalid-file.txt',
          mimeType: 'text/plain',
          buffer: Buffer.from('This is not a valid image or PDF file'),
        }
      },
    });

    // Should handle error gracefully
    if (!response.ok()) {
      const data = await response.json();
      expect(data).toHaveProperty('success', false);
      console.log('✅ Invalid file type properly rejected');
    } else {
      // If it doesn't reject, it should at least not crash
      const data = await response.json();
      expect(data).toHaveProperty('success');
      console.log('✅ Error handled gracefully without crashing');
    }
  });
});
