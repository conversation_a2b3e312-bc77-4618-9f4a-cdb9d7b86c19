import { test, expect } from '@playwright/test';
import path from 'path';

/**
 * Enhanced Style Analysis Tests - Priority 1 Enhancement
 * Tests the new advanced style classification, material identification, and hardware recognition features
 */
test.describe('Enhanced Style Analysis', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should perform enhanced style analysis with new features', async ({ page }) => {
    // Upload a test kitchen design file
    const fileInput = page.locator('input[type="file"]');
    const testFile = path.join(__dirname, '../fixtures/kitchen-design-modern.pdf');
    
    await fileInput.setInputFiles(testFile);
    
    // Wait for upload and analysis to complete
    await page.waitForSelector('[data-testid="analysis-results"]', { timeout: 180000 });
    
    // Verify the new Style Analysis tab is present
    const styleTab = page.locator('[data-value="style"]');
    await expect(styleTab).toBeVisible();
    await expect(styleTab).toContainText('Style Analysis');
    
    // Click on the Style Analysis tab
    await styleTab.click();
    
    // Verify enhanced style analysis content is displayed
    await expect(page.locator('text=Dominant Style Analysis')).toBeVisible();
    
    // Check for style classification elements
    const styleClassification = page.locator('[data-testid="dominant-style"]');
    if (await styleClassification.isVisible()) {
      // Verify style confidence score is displayed
      await expect(page.locator('[data-testid="style-confidence"]')).toBeVisible();
      
      // Verify style characteristics are shown
      await expect(page.locator('[data-testid="style-characteristics"]')).toBeVisible();
      
      // Verify style consistency score
      await expect(page.locator('text=Style Consistency')).toBeVisible();
    }
    
    // Test enhanced hardware analysis
    await page.locator('[data-value="hardware"]').click();
    
    // Check for enhanced hardware information
    const hardwareItems = page.locator('[data-testid="hardware-item"]');
    if (await hardwareItems.count() > 0) {
      const firstItem = hardwareItems.first();
      
      // Check for brand information (if available)
      const brandInfo = firstItem.locator('text=Brand:');
      if (await brandInfo.isVisible()) {
        await expect(brandInfo).toBeVisible();
      }
      
      // Check for style compatibility
      const compatibilityInfo = firstItem.locator('text=Compatible Styles:');
      if (await compatibilityInfo.isVisible()) {
        await expect(compatibilityInfo).toBeVisible();
      }
      
      // Check for installation complexity
      const installationInfo = firstItem.locator('text=Installation:');
      if (await installationInfo.isVisible()) {
        await expect(installationInfo).toBeVisible();
      }
    }
    
    // Test enhanced cabinet information
    await page.locator('[data-value="cabinets"]').click();
    
    // Check for enhanced cabinet style information
    const cabinetItems = page.locator('[data-testid="cabinet-item"]');
    if (await cabinetItems.count() > 0) {
      const firstCabinet = cabinetItems.first();
      
      // Look for style badges on individual cabinets
      const styleBadge = firstCabinet.locator('.badge');
      if (await styleBadge.count() > 0) {
        // Verify style information is displayed
        await expect(styleBadge.first()).toBeVisible();
      }
    }
    
    // Test enhanced confidence metrics
    await page.locator('[data-value="measurements"]').click();
    
    // Check for new confidence metrics
    const styleClassificationMetric = page.locator('text=Style Classification');
    if (await styleClassificationMetric.isVisible()) {
      await expect(styleClassificationMetric).toBeVisible();
      
      // Verify progress bar is present
      const progressBar = page.locator('[data-testid="style-classification-progress"]');
      if (await progressBar.isVisible()) {
        await expect(progressBar).toBeVisible();
      }
    }
    
    const materialIdentificationMetric = page.locator('text=Material Identification');
    if (await materialIdentificationMetric.isVisible()) {
      await expect(materialIdentificationMetric).toBeVisible();
    }
  });

  test('should handle style analysis gracefully when not available', async ({ page }) => {
    // Upload a test file
    const fileInput = page.locator('input[type="file"]');
    const testFile = path.join(__dirname, '../fixtures/simple-kitchen.pdf');
    
    await fileInput.setInputFiles(testFile);
    
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="analysis-results"]', { timeout: 120000 });
    
    // Click on Style Analysis tab
    await page.locator('[data-value="style"]').click();
    
    // Should show fallback message if style analysis is not available
    const fallbackMessage = page.locator('text=Style analysis not available');
    if (await fallbackMessage.isVisible()) {
      await expect(fallbackMessage).toBeVisible();
      await expect(page.locator('text=Enhanced style analysis is available')).toBeVisible();
    }
  });

  test('should display style recommendations when available', async ({ page }) => {
    // Upload a test kitchen design file
    const fileInput = page.locator('input[type="file"]');
    const testFile = path.join(__dirname, '../fixtures/kitchen-design-mixed-styles.pdf');
    
    await fileInput.setInputFiles(testFile);
    
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="analysis-results"]', { timeout: 180000 });
    
    // Navigate to Style Analysis tab
    await page.locator('[data-value="style"]').click();
    
    // Check for style recommendations section
    const recommendationsSection = page.locator('text=Style Recommendations');
    if (await recommendationsSection.isVisible()) {
      await expect(recommendationsSection).toBeVisible();
      
      // Verify recommendations are displayed
      const recommendations = page.locator('[data-testid="style-recommendation"]');
      if (await recommendations.count() > 0) {
        await expect(recommendations.first()).toBeVisible();
      }
    }
  });

  test('should show mixed styles when detected', async ({ page }) => {
    // Upload a test kitchen design file with mixed styles
    const fileInput = page.locator('input[type="file"]');
    const testFile = path.join(__dirname, '../fixtures/kitchen-design-mixed-styles.pdf');
    
    await fileInput.setInputFiles(testFile);
    
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="analysis-results"]', { timeout: 180000 });
    
    // Navigate to Style Analysis tab
    await page.locator('[data-value="style"]').click();
    
    // Check for mixed styles section
    const mixedStylesSection = page.locator('text=Mixed Styles Detected');
    if (await mixedStylesSection.isVisible()) {
      await expect(mixedStylesSection).toBeVisible();
      
      // Verify mixed styles are listed
      const mixedStyles = page.locator('[data-testid="mixed-style"]');
      if (await mixedStyles.count() > 0) {
        await expect(mixedStyles.first()).toBeVisible();
      }
    }
  });

  test('should maintain backward compatibility with existing analysis', async ({ page }) => {
    // Upload a test file
    const fileInput = page.locator('input[type="file"]');
    const testFile = path.join(__dirname, '../fixtures/kitchen-design-simple.pdf');
    
    await fileInput.setInputFiles(testFile);
    
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="analysis-results"]', { timeout: 120000 });
    
    // Verify all existing tabs are still present and functional
    await expect(page.locator('[data-value="cabinets"]')).toBeVisible();
    await expect(page.locator('[data-value="hardware"]')).toBeVisible();
    await expect(page.locator('[data-value="measurements"]')).toBeVisible();
    await expect(page.locator('[data-value="materials"]')).toBeVisible();
    
    // Verify existing functionality still works
    await page.locator('[data-value="cabinets"]').click();
    await expect(page.locator('text=Cabinet Breakdown')).toBeVisible();
    
    await page.locator('[data-value="hardware"]').click();
    await expect(page.locator('text=Hardware Analysis')).toBeVisible();
    
    await page.locator('[data-value="measurements"]').click();
    await expect(page.locator('text=Measurement Accuracy')).toBeVisible();
    
    await page.locator('[data-value="materials"]').click();
    await expect(page.locator('text=Material Analysis')).toBeVisible();
  });
});
