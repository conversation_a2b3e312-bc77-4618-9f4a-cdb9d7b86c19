import { test, expect } from '@playwright/test';

test.describe('A.ONE Inspired Styling', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');
  });

  test('should display A.ONE banner correctly', async ({ page }) => {
    // Check if banner is visible
    const banner = page.locator('.aone-banner');
    await expect(banner).toBeVisible();
    
    // Check banner content
    await expect(banner).toContainText('AI-powered insights');
    
    // Check banner styling
    await expect(banner).toHaveCSS('background-image', /linear-gradient/);
  });

  test('should display enhanced header with A.ONE styling', async ({ page }) => {
    // Check header is present
    const header = page.locator('header.aone-header');
    await expect(header).toBeVisible();
    
    // Check logo styling
    const logo = page.locator('.aone-logo');
    await expect(logo).toBeVisible();
    await expect(logo).toContainText('BLACKVEIL DESIGN MIND');
    
    // Check logo accent
    const logoAccent = page.locator('.aone-logo-accent');
    await expect(logoAccent).toBeVisible();
    await expect(logoAccent).toContainText('AI KITCHEN ANALYSIS');
  });

  test('should display navigation with A.ONE styling', async ({ page }) => {
    // Check navigation links
    const navLinks = page.locator('.aone-nav-link');
    await expect(navLinks.first()).toBeVisible();
    
    // Check active link styling
    const activeLink = page.locator('.aone-nav-link-active');
    if (await activeLink.count() > 0) {
      await expect(activeLink.first()).toBeVisible();
    }
  });

  test('should display hero section with A.ONE styling', async ({ page }) => {
    // Check hero section
    const heroSection = page.locator('.aone-hero-section');
    await expect(heroSection).toBeVisible();

    // Check hero title within the hero section
    const heroTitle = heroSection.locator('h1');
    await expect(heroTitle).toBeVisible();
    await expect(heroTitle).toContainText('Transform Kitchen Drawings');
  });

  test('should display A.ONE styled buttons', async ({ page }) => {
    // Check primary button
    const primaryButton = page.locator('.aone-button-primary');
    await expect(primaryButton.first()).toBeVisible();
    
    // Check secondary button
    const secondaryButton = page.locator('.aone-button-secondary');
    await expect(secondaryButton.first()).toBeVisible();
  });

  test('should display A.ONE styled cards', async ({ page }) => {
    // Check for elegant cards
    const elegantCards = page.locator('.aone-card-elegant');
    await expect(elegantCards.first()).toBeVisible();
    
    // Check card content
    const cardTitle = page.locator('.aone-text-primary');
    await expect(cardTitle.first()).toBeVisible();
    
    const cardDescription = page.locator('.aone-text-secondary');
    await expect(cardDescription.first()).toBeVisible();
  });

  test('should handle banner dismissal', async ({ page }) => {
    // Check if banner is initially visible
    const banner = page.locator('.aone-banner');
    await expect(banner).toBeVisible();
    
    // Find and click dismiss button if present
    const dismissButton = banner.locator('button[aria-label="Dismiss banner"]');
    if (await dismissButton.count() > 0) {
      await dismissButton.click();
      
      // Check if banner is hidden after dismissal
      await expect(banner).not.toBeVisible();
    }
  });

  test('should maintain responsive design', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check if header is still visible and functional
    const header = page.locator('header.aone-header');
    await expect(header).toBeVisible();
    
    // Check if banner adapts to mobile
    const banner = page.locator('.aone-banner');
    await expect(banner).toBeVisible();
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    
    // Check if navigation is visible on tablet
    const nav = page.locator('nav');
    await expect(nav).toBeVisible();
  });

  test('should maintain accessibility features', async ({ page }) => {
    // Check for proper heading hierarchy
    const h1 = page.locator('h1');
    await expect(h1).toBeVisible();
    
    // Check for proper button labels
    const buttons = page.locator('button');
    for (let i = 0; i < await buttons.count(); i++) {
      const button = buttons.nth(i);
      const ariaLabel = await button.getAttribute('aria-label');
      const textContent = await button.textContent();
      
      // Button should have either aria-label or text content
      expect(ariaLabel || textContent).toBeTruthy();
    }
    
    // Check for proper link accessibility
    const links = page.locator('a');
    for (let i = 0; i < await links.count(); i++) {
      const link = links.nth(i);
      const href = await link.getAttribute('href');
      const textContent = await link.textContent();
      
      // Links should have href and text content
      if (href) {
        expect(textContent?.trim()).toBeTruthy();
      }
    }
  });

  test('should load without console errors', async ({ page }) => {
    const consoleErrors: string[] = [];
    
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    await page.reload();
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle');
    
    // Check for console errors
    expect(consoleErrors).toHaveLength(0);
  });
});
