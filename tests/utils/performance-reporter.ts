import { Reporter, TestCase, TestResult, FullResult, Suite } from '@playwright/test/reporter';
import { randomUUID } from 'crypto';

interface TestMetrics {
  testName: string;
  testCategory: 'api' | 'ui' | 'integration' | 'cross-browser' | 'mobile' | 'performance';
  browser: string;
  duration: number;
  success: boolean;
  retryCount: number;
  errorType?: string;
  errorMessage?: string;
  featureVersion?: string;
  buildId?: string;
  networkCondition?: any;
  resourceUsage?: any;
  metadata?: any;
}

interface TestSuiteResult {
  suiteId: string;
  suiteName: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  successRate: number;
  duration: number;
  featureVersion?: string;
  buildId?: string;
  browser: string;
  testCategory: string;
}

class PerformanceReporter implements Reporter {
  private apiBaseUrl: string;
  private featureVersion?: string;
  private buildId: string;
  private suiteResults: Map<string, TestSuiteResult> = new Map();
  private testMetrics: TestMetrics[] = [];
  private startTime: number = Date.now();

  constructor(options: { apiBaseUrl?: string; featureVersion?: string; buildId?: string } = {}) {
    this.apiBaseUrl = options.apiBaseUrl || 'http://localhost:3001/api/performance-monitoring';
    this.featureVersion = options.featureVersion || process.env.FEATURE_VERSION;
    this.buildId = options.buildId || process.env.BUILD_ID || randomUUID();
  }

  onBegin(config: any, suite: Suite): void {
    console.log(`🚀 Performance monitoring started for build: ${this.buildId}`);
    if (this.featureVersion) {
      console.log(`📊 Tracking feature version: ${this.featureVersion}`);
    }
  }

  onTestEnd(test: TestCase, result: TestResult): void {
    try {
      const testCategory = this.categorizeTest(test.title, test.location.file);
      const browser = this.extractBrowser(test);
      
      const metrics: TestMetrics = {
        testName: test.title,
        testCategory,
        browser,
        duration: result.duration,
        success: result.status === 'passed',
        retryCount: result.retry,
        errorType: result.error?.name,
        errorMessage: result.error?.message,
        featureVersion: this.featureVersion,
        buildId: this.buildId,
        networkCondition: this.extractNetworkCondition(result),
        resourceUsage: this.extractResourceUsage(result),
        metadata: {
          testFile: test.location.file,
          testLine: test.location.line,
          testColumn: test.location.column,
          annotations: test.annotations,
          tags: test.tags
        }
      };

      this.testMetrics.push(metrics);

      // Update suite results
      this.updateSuiteResults(test, result, testCategory, browser);

      // Send metrics to API (async, don't block test execution)
      this.sendTestMetrics(metrics).catch(error => {
        console.warn(`Failed to send test metrics for ${test.title}:`, error.message);
      });
    } catch (error) {
      console.warn(`Failed to process test metrics for ${test.title}:`, error);
    }
  }

  onEnd(result: FullResult): void {
    try {
      const totalDuration = Date.now() - this.startTime;
      
      console.log(`\n📊 Performance Monitoring Summary:`);
      console.log(`   Total Duration: ${totalDuration}ms`);
      console.log(`   Total Tests: ${this.testMetrics.length}`);
      console.log(`   Passed: ${this.testMetrics.filter(m => m.success).length}`);
      console.log(`   Failed: ${this.testMetrics.filter(m => !m.success).length}`);
      
      const overallSuccessRate = (this.testMetrics.filter(m => m.success).length / this.testMetrics.length) * 100;
      console.log(`   Success Rate: ${overallSuccessRate.toFixed(1)}%`);
      
      if (overallSuccessRate < 91.7) {
        console.log(`   ⚠️  WARNING: Success rate below 91.7% threshold!`);
      } else {
        console.log(`   ✅ Success rate meets 91.7% standard`);
      }

      // Send suite results
      this.sendAllSuiteResults().catch(error => {
        console.warn('Failed to send suite results:', error.message);
      });

      // Analyze feature impact if feature version is provided
      if (this.featureVersion) {
        this.analyzeFeatureImpact().catch(error => {
          console.warn('Failed to analyze feature impact:', error.message);
        });
      }
    } catch (error) {
      console.warn('Failed to process final performance monitoring results:', error);
    }
  }

  private categorizeTest(testTitle: string, testFile: string): TestMetrics['testCategory'] {
    const filePath = testFile.toLowerCase();
    
    if (filePath.includes('/api/')) return 'api';
    if (filePath.includes('/mobile/')) return 'mobile';
    if (filePath.includes('/integration/')) return 'integration';
    if (filePath.includes('/performance/')) return 'performance';
    if (filePath.includes('/frontend/') || filePath.includes('/ui/')) return 'ui';
    
    // Check test title for category hints
    const titleLower = testTitle.toLowerCase();
    if (titleLower.includes('api') || titleLower.includes('endpoint')) return 'api';
    if (titleLower.includes('mobile') || titleLower.includes('responsive')) return 'mobile';
    if (titleLower.includes('browser') || titleLower.includes('cross-browser')) return 'cross-browser';
    if (titleLower.includes('performance') || titleLower.includes('speed')) return 'performance';
    
    return 'integration'; // Default category
  }

  private extractBrowser(test: TestCase): string {
    // Extract browser from test project name or other metadata
    const project = (test as any).parent?.project?.name || 'unknown';
    
    if (project.toLowerCase().includes('chromium')) return 'chromium';
    if (project.toLowerCase().includes('firefox')) return 'firefox';
    if (project.toLowerCase().includes('webkit') || project.toLowerCase().includes('safari')) return 'webkit';
    if (project.toLowerCase().includes('chrome')) return 'chrome';
    
    return project || 'chromium'; // Default to chromium
  }

  private extractNetworkCondition(result: TestResult): any {
    // Extract network condition from test attachments or metadata
    // This would be populated by the network monitoring utilities
    return {
      quality: 'good', // Default value
      latency: 100,
      bandwidth: 1000,
      packetLoss: 0
    };
  }

  private extractResourceUsage(result: TestResult): any {
    // Extract resource usage if available
    return {
      memoryUsage: 0, // Would be populated by actual monitoring
      cpuUsage: 0,
      diskUsage: 0
    };
  }

  private updateSuiteResults(test: TestCase, result: TestResult, testCategory: string, browser: string): void {
    const suiteName = this.getSuiteName(test);
    const suiteKey = `${suiteName}-${browser}-${testCategory}`;
    
    let suiteResult = this.suiteResults.get(suiteKey);
    
    if (!suiteResult) {
      suiteResult = {
        suiteId: randomUUID(),
        suiteName,
        totalTests: 0,
        passedTests: 0,
        failedTests: 0,
        skippedTests: 0,
        successRate: 0,
        duration: 0,
        featureVersion: this.featureVersion,
        buildId: this.buildId,
        browser,
        testCategory
      };
      this.suiteResults.set(suiteKey, suiteResult);
    }
    
    suiteResult.totalTests++;
    suiteResult.duration += result.duration;
    
    if (result.status === 'passed') {
      suiteResult.passedTests++;
    } else if (result.status === 'failed') {
      suiteResult.failedTests++;
    } else {
      suiteResult.skippedTests++;
    }
    
    suiteResult.successRate = (suiteResult.passedTests / suiteResult.totalTests) * 100;
  }

  private getSuiteName(test: TestCase): string {
    // Extract suite name from test file path
    const filePath = test.location.file;
    const fileName = filePath.split('/').pop()?.replace('.spec.ts', '') || 'unknown';
    return fileName;
  }

  private async sendTestMetrics(metrics: TestMetrics): Promise<void> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/test-metrics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(metrics)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      // Don't throw - we don't want to fail tests due to monitoring issues
      console.warn(`Failed to send test metrics: ${error}`);
    }
  }

  private async sendAllSuiteResults(): Promise<void> {
    const promises = Array.from(this.suiteResults.values()).map(suiteResult =>
      this.sendSuiteResult(suiteResult)
    );
    
    await Promise.allSettled(promises);
  }

  private async sendSuiteResult(suiteResult: TestSuiteResult): Promise<void> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/test-suite-results`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(suiteResult)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.warn(`Failed to send suite result for ${suiteResult.suiteName}: ${error}`);
    }
  }

  private async analyzeFeatureImpact(): Promise<void> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/analyze-feature-impact`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          featureVersion: this.featureVersion
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      const analysis = result.data;
      
      console.log(`\n🔍 Feature Impact Analysis for ${this.featureVersion}:`);
      console.log(`   Success Rate Change: ${analysis.impact.successRateChange.toFixed(1)}%`);
      console.log(`   Duration Change: ${analysis.impact.durationChange.toFixed(1)}%`);
      console.log(`   New Failures: ${analysis.impact.newFailures.length}`);
      console.log(`   Recommendation: ${analysis.recommendation.toUpperCase()}`);
      
      if (analysis.recommendation !== 'proceed') {
        console.log(`   ⚠️  ATTENTION: Feature impact analysis recommends: ${analysis.recommendation}`);
      }
    } catch (error) {
      console.warn(`Failed to analyze feature impact: ${error}`);
    }
  }
}

export default PerformanceReporter;
