/**
 * Browser Test Helper - Specialized helper for browser-specific test operations
 * 
 * Part of Phase 3.4 Test Infrastructure Refactoring
 * Extracted from test-helpers.ts (951 lines → modular architecture)
 * 
 * Responsibilities:
 * - Browser-specific navigation and interaction
 * - Cross-browser compatibility handling
 * - Page loading and element waiting
 * - Browser-specific timeout management
 */

import { Page, expect } from '@playwright/test';
import { NetworkMonitor, NetworkCondition } from '../network-monitor';
import { CompatibilityMatrix } from '../compatibility-matrix';

export class BrowserTestHelper {
  private compatibilityMatrix: CompatibilityMatrix;
  private networkMonitor: NetworkMonitor;

  constructor(private page: Page) {
    this.compatibilityMatrix = CompatibilityMatrix.getInstance();
    this.networkMonitor = NetworkMonitor.getInstance();
  }

  /**
   * Get adaptive timeout based on network conditions and browser compatibility
   */
  getAdaptiveTimeout(baseTimeout: number, testName: string): number {
    const browser = this.page.context().browser()?.browserType().name() || 'chromium';

    // Get network-based timeout
    let adaptiveTimeout = this.networkMonitor.getAdaptiveTimeout(
      baseTimeout,
      undefined // Will use current network condition
    );

    // Apply browser-specific adjustments
    adaptiveTimeout = this.compatibilityMatrix.getBrowserSpecificTimeout(
      testName,
      browser,
      adaptiveTimeout
    );

    console.log(`⏱️ Adaptive timeout for ${testName} on ${browser}: ${adaptiveTimeout}ms (base: ${baseTimeout}ms)`);
    return adaptiveTimeout;
  }

  /**
   * Navigate to a specific page and wait for it to load
   * Includes browser-specific retry logic for network issues and adaptive timeouts
   */
  async navigateToPage(path: string, retries: number = 3, testName: string = 'navigation') {
    const browserName = this.page.context().browser()?.browserType().name() || 'unknown';

    // Get adaptive timeout based on network conditions
    const baseTimeout = 30000;
    const adaptiveTimeout = this.getAdaptiveTimeout(baseTimeout, testName);

    // Check for known compatibility issues
    const knownIssues = this.compatibilityMatrix.getKnownIssues(testName, browserName);
    if (knownIssues.length > 0) {
      console.log(`⚠️ Known issues for ${testName} on ${browserName}:`);
      knownIssues.forEach(issue => console.log(`  - ${issue.description}`));
    }

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`🌐 Navigating to ${path} (attempt ${attempt}/${retries}) on ${browserName}`);
        
        await this.page.goto(`http://localhost:8080${path}`, { 
          timeout: adaptiveTimeout,
          waitUntil: 'domcontentloaded'
        });

        // Wait for the page to be fully loaded
        await this.waitForPageLoad(adaptiveTimeout);
        
        console.log(`✅ Successfully navigated to ${path} on ${browserName}`);
        return;

      } catch (error) {
        console.warn(`⚠️ Navigation attempt ${attempt} failed on ${browserName}:`, error);
        
        if (attempt < retries) {
          // Apply browser-specific retry strategy
          const retryDelay = this.getBrowserSpecificRetryDelay(browserName, attempt);
          console.log(`⏳ Retrying in ${retryDelay}ms...`);
          await this.page.waitForTimeout(retryDelay);
        } else {
          throw new Error(`Failed to navigate to ${path} after ${retries} attempts on ${browserName}: ${error}`);
        }
      }
    }
  }

  /**
   * Wait for page to be fully loaded with browser-specific strategies
   */
  async waitForPageLoad(timeout: number = 30000): Promise<void> {
    const browserName = this.page.context().browser()?.browserType().name() || 'unknown';

    try {
      // Strategy 1: Wait for network idle (works well for most browsers)
      await this.page.waitForLoadState('networkidle', { timeout: timeout / 2 });
      console.log(`✅ Page loaded (networkidle) on ${browserName}`);
      return;
    } catch (error) {
      console.log(`⚠️ Network idle timeout on ${browserName}, trying alternative strategies...`);
    }

    try {
      // Strategy 2: Wait for main app container
      await this.page.waitForSelector('#root', { timeout: timeout / 4 });
      console.log(`✅ Page loaded (app container) on ${browserName}`);
      return;
    } catch (error) {
      console.log(`⚠️ App container not found on ${browserName}, trying final strategy...`);
    }

    // Strategy 3: Basic DOM content loaded (fallback)
    await this.page.waitForLoadState('domcontentloaded', { timeout: timeout / 4 });
    console.log(`✅ Page loaded (domcontentloaded fallback) on ${browserName}`);
  }

  /**
   * Wait for element with browser-specific strategies and fallbacks
   */
  async waitForElement(
    selector: string, 
    timeout: number = 30000, 
    alternatives: string[] = []
  ): Promise<void> {
    const browserName = this.page.context().browser()?.browserType().name() || 'unknown';
    
    try {
      await this.page.waitForSelector(selector, { timeout });
      console.log(`✅ Element found: ${selector} on ${browserName}`);
      return;
    } catch (error) {
      console.log(`⚠️ Primary selector failed on ${browserName}: ${selector}`);
      
      // Try alternative selectors
      for (const alternative of alternatives) {
        try {
          await this.page.waitForSelector(alternative, { timeout: 5000 });
          console.log(`✅ Alternative selector found: ${alternative} on ${browserName}`);
          return;
        } catch {
          console.log(`⚠️ Alternative selector failed on ${browserName}: ${alternative}`);
        }
      }
      
      throw new Error(`Element not found after trying all selectors on ${browserName}: ${selector}, ${alternatives.join(', ')}`);
    }
  }

  /**
   * Click element with browser-specific retry logic
   */
  async clickElement(selector: string, retries: number = 3): Promise<void> {
    const browserName = this.page.context().browser()?.browserType().name() || 'unknown';

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        // Wait for element to be visible and enabled
        await this.page.waitForSelector(selector, { state: 'visible', timeout: 10000 });
        
        // Scroll element into view if needed
        await this.page.locator(selector).scrollIntoViewIfNeeded();
        
        // Click the element
        await this.page.click(selector);
        
        console.log(`✅ Successfully clicked ${selector} on ${browserName}`);
        return;

      } catch (error) {
        console.warn(`⚠️ Click attempt ${attempt} failed on ${browserName}:`, error);
        
        if (attempt < retries) {
          const retryDelay = this.getBrowserSpecificRetryDelay(browserName, attempt);
          await this.page.waitForTimeout(retryDelay);
        } else {
          throw new Error(`Failed to click ${selector} after ${retries} attempts on ${browserName}: ${error}`);
        }
      }
    }
  }

  /**
   * Fill form field with browser-specific handling
   */
  async fillField(selector: string, value: string, retries: number = 3): Promise<void> {
    const browserName = this.page.context().browser()?.browserType().name() || 'unknown';

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        await this.page.waitForSelector(selector, { state: 'visible', timeout: 10000 });
        
        // Clear field first
        await this.page.fill(selector, '');
        
        // Fill with new value
        await this.page.fill(selector, value);
        
        // Verify the value was set correctly
        const actualValue = await this.page.inputValue(selector);
        if (actualValue !== value) {
          throw new Error(`Value mismatch: expected "${value}", got "${actualValue}"`);
        }
        
        console.log(`✅ Successfully filled ${selector} with "${value}" on ${browserName}`);
        return;

      } catch (error) {
        console.warn(`⚠️ Fill attempt ${attempt} failed on ${browserName}:`, error);
        
        if (attempt < retries) {
          const retryDelay = this.getBrowserSpecificRetryDelay(browserName, attempt);
          await this.page.waitForTimeout(retryDelay);
        } else {
          throw new Error(`Failed to fill ${selector} after ${retries} attempts on ${browserName}: ${error}`);
        }
      }
    }
  }

  /**
   * Get browser-specific retry delay
   */
  private getBrowserSpecificRetryDelay(browserName: string, attempt: number): number {
    const baseDelay = 1000;
    const multiplier = Math.pow(1.5, attempt - 1); // Exponential backoff
    
    // Browser-specific adjustments
    switch (browserName.toLowerCase()) {
      case 'firefox':
        return Math.floor(baseDelay * multiplier * 1.2); // Firefox needs slightly more time
      case 'webkit':
      case 'safari':
        return Math.floor(baseDelay * multiplier * 1.3); // WebKit needs more time
      default:
        return Math.floor(baseDelay * multiplier);
    }
  }

  /**
   * Check if browser supports specific features
   */
  async checkBrowserCapabilities(): Promise<{
    webgl: boolean;
    webrtc: boolean;
    websockets: boolean;
    fileApi: boolean;
  }> {
    const capabilities = await this.page.evaluate(() => {
      return {
        webgl: !!window.WebGLRenderingContext,
        webrtc: !!(window.RTCPeerConnection || window.webkitRTCPeerConnection),
        websockets: !!window.WebSocket,
        fileApi: !!(window.File && window.FileReader && window.FileList && window.Blob)
      };
    });

    const browserName = this.page.context().browser()?.browserType().name() || 'unknown';
    console.log(`🔍 Browser capabilities for ${browserName}:`, capabilities);
    
    return capabilities;
  }

  /**
   * Get browser information
   */
  getBrowserInfo(): {
    name: string;
    version: string;
    platform: string;
  } {
    const browser = this.page.context().browser();
    return {
      name: browser?.browserType().name() || 'unknown',
      version: browser?.version() || 'unknown',
      platform: process.platform
    };
  }

  /**
   * Take screenshot with browser-specific naming
   */
  async takeScreenshot(name: string): Promise<string> {
    const browserInfo = this.getBrowserInfo();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${name}-${browserInfo.name}-${timestamp}.png`;
    
    await this.page.screenshot({ 
      path: `test-results/screenshots/${filename}`,
      fullPage: true 
    });
    
    console.log(`📸 Screenshot saved: ${filename}`);
    return filename;
  }
}
