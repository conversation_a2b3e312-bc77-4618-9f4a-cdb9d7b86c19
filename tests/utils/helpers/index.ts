/**
 * Test Helpers - Modular Architecture Index
 * 
 * Part of Phase 3.4 Test Infrastructure Refactoring
 * Extracted from test-helpers.ts (951 lines → modular architecture)
 * 
 * This file provides a clean interface to all test helper modules
 * and maintains backward compatibility with existing test structure.
 */

export { BrowserTestHelper } from './BrowserTestHelper';
export { NetworkTestHelper } from './NetworkTestHelper';
export { EnvironmentTestHelper } from './EnvironmentTestHelper';
export { AuthTestHelper, type AuthState } from './AuthTestHelper';

// Re-export common types for convenience
export type { NetworkCondition } from '../network-monitor';
export type { CompatibilityIssue } from '../compatibility-matrix';
