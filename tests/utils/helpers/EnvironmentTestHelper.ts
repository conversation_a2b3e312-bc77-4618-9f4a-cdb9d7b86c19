/**
 * Environment Test Helper - Specialized helper for environment validation and setup
 * 
 * Part of Phase 3.4 Test Infrastructure Refactoring
 * Extracted from test-helpers.ts (951 lines → modular architecture)
 * 
 * Responsibilities:
 * - Environment validation and health checks
 * - Test data setup and cleanup
 * - File system operations
 * - System resource monitoring
 */

import { Page } from '@playwright/test';
import fs from 'fs';
import path from 'path';
import { EnvironmentValidator } from '../environment-validator';

export class EnvironmentTestHelper {
  private environmentValidator: EnvironmentValidator;

  constructor(private page: Page) {
    this.environmentValidator = EnvironmentValidator.getInstance();
  }

  /**
   * Setup test environment for API tests
   */
  async setupTestEnvironment(options: { apiOnly?: boolean } = {}): Promise<void> {
    try {
      console.log('🔧 Setting up test environment...');

      // Validate environment (skip frontend/WebSocket for API-only tests)
      await this.validateEnvironment(options.apiOnly);

      // Ensure required directories exist
      await this.ensureDirectoriesExist();

      // Clear any existing test data if needed
      await this.clearTestData();

      // Verify test fixtures
      await this.verifyTestFixtures();

      console.log('✅ Test environment setup completed');
    } catch (error) {
      console.error('❌ Test environment setup failed:', error);
      throw error;
    }
  }

  /**
   * Validate complete test environment
   */
  async validateEnvironment(apiOnly: boolean = false): Promise<void> {
    console.log('🔍 Validating test environment...');

    const validation = await this.environmentValidator.validateEnvironment(this.page, {
      skipFrontend: apiOnly,
      skipWebSocket: apiOnly
    });

    if (validation.overall === 'fail') {
      const failedChecks = validation.checks.filter(c => c.status === 'fail');
      console.error('❌ Environment validation failed:');
      failedChecks.forEach(check => {
        console.error(`  - ${check.name}: ${check.message}`);
      });
      throw new Error('Environment validation failed');
    }

    if (validation.overall === 'warning') {
      const warningChecks = validation.checks.filter(c => c.status === 'warning');
      console.warn('⚠️ Environment validation warnings:');
      warningChecks.forEach(check => {
        console.warn(`  - ${check.name}: ${check.message}`);
      });
    }

    console.log('✅ Environment validation passed');
  }

  /**
   * Ensure required directories exist
   */
  async ensureDirectoriesExist(): Promise<void> {
    const requiredDirs = [
      'tests/fixtures',
      'test-results',
      'test-results/screenshots',
      'uploads',
      'temp'
    ];

    console.log('📁 Ensuring required directories exist...');

    for (const dir of requiredDirs) {
      try {
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
          console.log(`✅ Created directory: ${dir}`);
        } else {
          console.log(`✅ Directory exists: ${dir}`);
        }
      } catch (error) {
        console.error(`❌ Failed to create directory ${dir}:`, error);
        throw error;
      }
    }
  }

  /**
   * Verify test fixtures are available
   */
  async verifyTestFixtures(): Promise<void> {
    const requiredFixtures = [
      'tests/fixtures/kitchen-design-test.pdf',
      'tests/fixtures/kitchen-design-test.png'
    ];

    console.log('📋 Verifying test fixtures...');

    for (const fixture of requiredFixtures) {
      if (!fs.existsSync(fixture)) {
        console.warn(`⚠️ Test fixture missing: ${fixture}`);
        // Create a minimal test fixture if needed
        await this.createMinimalTestFixture(fixture);
      } else {
        console.log(`✅ Test fixture available: ${fixture}`);
      }
    }
  }

  /**
   * Create minimal test fixture if missing
   */
  private async createMinimalTestFixture(fixturePath: string): Promise<void> {
    const dir = path.dirname(fixturePath);
    const ext = path.extname(fixturePath);

    // Ensure fixture directory exists
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    if (ext === '.pdf') {
      // Create a minimal PDF placeholder
      const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test Kitchen Design) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
299
%%EOF`;
      
      fs.writeFileSync(fixturePath, pdfContent);
      console.log(`✅ Created minimal PDF fixture: ${fixturePath}`);
      
    } else if (ext === '.png') {
      // Create a minimal PNG placeholder (1x1 transparent pixel)
      const pngData = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
        0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
        0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4,
        0x89, 0x00, 0x00, 0x00, 0x0A, 0x49, 0x44, 0x41,
        0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
        0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00,
        0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE,
        0x42, 0x60, 0x82
      ]);
      
      fs.writeFileSync(fixturePath, pngData);
      console.log(`✅ Created minimal PNG fixture: ${fixturePath}`);
    }
  }

  /**
   * Clear test data for clean test runs
   */
  async clearTestData(): Promise<void> {
    try {
      console.log('🧹 Clearing test data...');

      // Clear temporary files
      const tempDirs = ['temp', 'uploads'];
      for (const dir of tempDirs) {
        if (fs.existsSync(dir)) {
          const files = fs.readdirSync(dir);
          for (const file of files) {
            if (file.startsWith('test_') || file.startsWith('upload_')) {
              const filePath = path.join(dir, file);
              try {
                fs.unlinkSync(filePath);
                console.log(`🗑️ Removed test file: ${filePath}`);
              } catch (error) {
                console.warn(`⚠️ Failed to remove ${filePath}:`, error);
              }
            }
          }
        }
      }

      // Clear old screenshots
      const screenshotDir = 'test-results/screenshots';
      if (fs.existsSync(screenshotDir)) {
        const files = fs.readdirSync(screenshotDir);
        const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago
        
        for (const file of files) {
          const filePath = path.join(screenshotDir, file);
          const stats = fs.statSync(filePath);
          
          if (stats.mtime.getTime() < cutoffTime) {
            try {
              fs.unlinkSync(filePath);
              console.log(`🗑️ Removed old screenshot: ${filePath}`);
            } catch (error) {
              console.warn(`⚠️ Failed to remove ${filePath}:`, error);
            }
          }
        }
      }

      console.log('✅ Test data cleared');
    } catch (error) {
      console.warn('⚠️ Test data clearing failed (non-critical):', error);
    }
  }

  /**
   * Monitor system resources
   */
  async monitorSystemResources(): Promise<{
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    cpu: {
      usage: number;
    };
    disk: {
      available: number;
      total: number;
      percentage: number;
    };
  }> {
    console.log('📊 Monitoring system resources...');

    const memoryUsage = process.memoryUsage();
    const totalMemory = require('os').totalmem();
    const freeMemory = require('os').freemem();
    const usedMemory = totalMemory - freeMemory;

    // Get disk space (simplified)
    let diskInfo = { available: 0, total: 0, percentage: 0 };
    try {
      const stats = fs.statSync('.');
      // This is a simplified approach - in a real implementation,
      // you might use a library like 'statvfs' for accurate disk info
      diskInfo = {
        available: 1000000000, // 1GB placeholder
        total: 10000000000, // 10GB placeholder
        percentage: 10 // 10% placeholder
      };
    } catch (error) {
      console.warn('⚠️ Could not get disk information:', error);
    }

    const resources = {
      memory: {
        used: usedMemory,
        total: totalMemory,
        percentage: (usedMemory / totalMemory) * 100
      },
      cpu: {
        usage: 0 // CPU usage would require additional monitoring
      },
      disk: diskInfo
    };

    console.log('📊 System resources:', {
      memory: `${(resources.memory.percentage).toFixed(1)}%`,
      memoryMB: `${(resources.memory.used / 1024 / 1024).toFixed(0)}MB / ${(resources.memory.total / 1024 / 1024).toFixed(0)}MB`,
      disk: `${resources.disk.percentage.toFixed(1)}%`
    });

    return resources;
  }

  /**
   * Check if environment is suitable for testing
   */
  async isEnvironmentSuitable(): Promise<{
    suitable: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check system resources
    const resources = await this.monitorSystemResources();
    
    if (resources.memory.percentage > 90) {
      issues.push('High memory usage detected');
      recommendations.push('Close unnecessary applications to free memory');
    }

    if (resources.disk.percentage > 95) {
      issues.push('Low disk space detected');
      recommendations.push('Free up disk space before running tests');
    }

    // Check network connectivity
    try {
      await this.page.request.get('http://localhost:3001/api/health', { timeout: 5000 });
    } catch (error) {
      issues.push('Backend server not accessible');
      recommendations.push('Ensure backend server is running on port 3001');
    }

    try {
      await this.page.goto('http://localhost:8080', { timeout: 5000 });
    } catch (error) {
      issues.push('Frontend server not accessible');
      recommendations.push('Ensure frontend server is running on port 8080');
    }

    const suitable = issues.length === 0;

    if (suitable) {
      console.log('✅ Environment is suitable for testing');
    } else {
      console.warn('⚠️ Environment issues detected:', issues);
      console.warn('💡 Recommendations:', recommendations);
    }

    return { suitable, issues, recommendations };
  }

  /**
   * Get environment information
   */
  getEnvironmentInfo(): {
    platform: string;
    nodeVersion: string;
    workingDirectory: string;
    timestamp: string;
  } {
    return {
      platform: process.platform,
      nodeVersion: process.version,
      workingDirectory: process.cwd(),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Create test report directory
   */
  async createTestReportDirectory(testName: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportDir = path.join('test-results', `${testName}-${timestamp}`);
    
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
      console.log(`📁 Created test report directory: ${reportDir}`);
    }
    
    return reportDir;
  }

  /**
   * Save test artifacts
   */
  async saveTestArtifacts(
    testName: string, 
    artifacts: {
      logs?: string[];
      screenshots?: string[];
      data?: any;
    }
  ): Promise<void> {
    const reportDir = await this.createTestReportDirectory(testName);
    
    // Save logs
    if (artifacts.logs && artifacts.logs.length > 0) {
      const logFile = path.join(reportDir, 'test.log');
      fs.writeFileSync(logFile, artifacts.logs.join('\n'));
      console.log(`📝 Saved test logs: ${logFile}`);
    }
    
    // Save test data
    if (artifacts.data) {
      const dataFile = path.join(reportDir, 'test-data.json');
      fs.writeFileSync(dataFile, JSON.stringify(artifacts.data, null, 2));
      console.log(`💾 Saved test data: ${dataFile}`);
    }
    
    console.log(`✅ Test artifacts saved to: ${reportDir}`);
  }
}
