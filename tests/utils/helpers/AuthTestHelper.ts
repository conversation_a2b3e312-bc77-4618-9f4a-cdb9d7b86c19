/**
 * Auth Test Helper - Specialized helper for authentication and authorization testing
 * 
 * Part of Phase 3.4 Test Infrastructure Refactoring
 * Extracted from test-helpers.ts (951 lines → modular architecture)
 * 
 * Responsibilities:
 * - Authentication state management
 * - Session handling and validation
 * - Permission testing
 * - Security-related test utilities
 */

import { Page, expect } from '@playwright/test';

export interface AuthState {
  isAuthenticated: boolean;
  user?: {
    id: string;
    email: string;
    role: string;
    permissions: string[];
  };
  token?: string;
  sessionId?: string;
  expiresAt?: Date;
}

export class AuthTestHelper {
  private currentAuthState: AuthState = { isAuthenticated: false };

  constructor(private page: Page) {}

  /**
   * Authenticate user for testing
   */
  async authenticateUser(
    credentials?: {
      email?: string;
      password?: string;
      role?: string;
    },
    options?: {
      skipBrowserStorage?: boolean;
    }
  ): Promise<AuthState> {
    const defaultCredentials = {
      email: '<EMAIL>',
      password: 'testpassword123',
      role: 'user'
    };

    const authCredentials = { ...defaultCredentials, ...credentials };
    const authOptions = { skipBrowserStorage: false, ...options };

    try {
      console.log(`🔐 Authenticating user: ${authCredentials.email}`);

      // For now, we'll simulate authentication since the app doesn't have auth yet
      // In a real implementation, this would make actual login requests

      const mockAuthState: AuthState = {
        isAuthenticated: true,
        user: {
          id: 'test-user-id',
          email: authCredentials.email,
          role: authCredentials.role,
          permissions: this.getPermissionsForRole(authCredentials.role)
        },
        token: 'mock-jwt-token',
        sessionId: 'mock-session-id',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      };

      this.currentAuthState = mockAuthState;

      // Set authentication state in browser storage (skip for API-only tests)
      if (!authOptions.skipBrowserStorage) {
        await this.setAuthStateInBrowser(mockAuthState);
      }

      console.log(`✅ User authenticated: ${authCredentials.email} (${authCredentials.role})`);
      return mockAuthState;

    } catch (error) {
      console.error('❌ Authentication failed:', error);
      throw new Error(`Authentication failed: ${error}`);
    }
  }

  /**
   * Logout user and clear authentication state
   */
  async logoutUser(): Promise<void> {
    try {
      console.log('🚪 Logging out user...');

      // Clear authentication state
      this.currentAuthState = { isAuthenticated: false };

      // Clear browser storage
      await this.clearAuthStateFromBrowser();

      console.log('✅ User logged out successfully');

    } catch (error) {
      console.error('❌ Logout failed:', error);
      throw new Error(`Logout failed: ${error}`);
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.currentAuthState.isAuthenticated;
  }

  /**
   * Get current authentication state
   */
  getCurrentAuthState(): AuthState {
    return { ...this.currentAuthState };
  }

  /**
   * Get authenticated request headers
   */
  async getAuthHeaders(): Promise<Record<string, string>> {
    // Ensure user is authenticated
    if (!this.isAuthenticated()) {
      // Auto-authenticate with default test user (skip browser storage for API tests)
      await this.authenticateUser(undefined, { skipBrowserStorage: true });
    }

    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (this.currentAuthState.token) {
      headers['Authorization'] = `Bearer ${this.currentAuthState.token}`;
    }

    return headers;
  }

  /**
   * Check if user has specific permission
   */
  hasPermission(permission: string): boolean {
    if (!this.currentAuthState.isAuthenticated || !this.currentAuthState.user) {
      return false;
    }

    return this.currentAuthState.user.permissions.includes(permission);
  }

  /**
   * Check if user has specific role
   */
  hasRole(role: string): boolean {
    if (!this.currentAuthState.isAuthenticated || !this.currentAuthState.user) {
      return false;
    }

    return this.currentAuthState.user.role === role;
  }

  /**
   * Test API endpoint with authentication
   */
  async testAuthenticatedApiCall(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: any
  ): Promise<{
    success: boolean;
    status: number;
    data?: any;
    error?: string;
  }> {
    if (!this.isAuthenticated()) {
      throw new Error('User must be authenticated to make authenticated API calls');
    }

    try {
      console.log(`🔒 Making authenticated ${method} request to ${endpoint}`);

      const headers: Record<string, string> = {};
      
      if (this.currentAuthState.token) {
        headers['Authorization'] = `Bearer ${this.currentAuthState.token}`;
      }

      let response;
      const requestOptions = { 
        headers,
        timeout: 30000
      };

      switch (method) {
        case 'GET':
          response = await this.page.request.get(endpoint, requestOptions);
          break;
        case 'POST':
          response = await this.page.request.post(endpoint, { 
            ...requestOptions,
            data 
          });
          break;
        case 'PUT':
          response = await this.page.request.put(endpoint, { 
            ...requestOptions,
            data 
          });
          break;
        case 'DELETE':
          response = await this.page.request.delete(endpoint, requestOptions);
          break;
      }

      const success = response.ok();
      let responseData;
      
      try {
        responseData = await response.json();
      } catch {
        // Response might not be JSON
      }

      const result = {
        success,
        status: response.status(),
        data: responseData
      };

      if (success) {
        console.log(`✅ Authenticated ${method} ${endpoint} succeeded`);
      } else {
        console.log(`❌ Authenticated ${method} ${endpoint} failed with status ${response.status()}`);
      }

      return result;

    } catch (error) {
      const result = {
        success: false,
        status: 0,
        error: error instanceof Error ? error.message : String(error)
      };

      console.log(`❌ Authenticated ${method} ${endpoint} failed with error:`, error);
      return result;
    }
  }

  /**
   * Test permission-based access
   */
  async testPermissionBasedAccess(
    requiredPermission: string,
    testAction: () => Promise<void>
  ): Promise<{
    hasPermission: boolean;
    actionSucceeded: boolean;
    error?: string;
  }> {
    const hasPermission = this.hasPermission(requiredPermission);
    
    console.log(`🔐 Testing permission-based access: ${requiredPermission}`);
    console.log(`👤 User has permission: ${hasPermission}`);

    let actionSucceeded = false;
    let error: string | undefined;

    try {
      await testAction();
      actionSucceeded = true;
      console.log(`✅ Action succeeded`);
    } catch (actionError) {
      error = actionError instanceof Error ? actionError.message : String(actionError);
      console.log(`❌ Action failed: ${error}`);
    }

    return {
      hasPermission,
      actionSucceeded,
      error
    };
  }

  /**
   * Test role-based access
   */
  async testRoleBasedAccess(
    requiredRole: string,
    testAction: () => Promise<void>
  ): Promise<{
    hasRole: boolean;
    actionSucceeded: boolean;
    error?: string;
  }> {
    const hasRole = this.hasRole(requiredRole);
    
    console.log(`👥 Testing role-based access: ${requiredRole}`);
    console.log(`👤 User has role: ${hasRole}`);

    let actionSucceeded = false;
    let error: string | undefined;

    try {
      await testAction();
      actionSucceeded = true;
      console.log(`✅ Action succeeded`);
    } catch (actionError) {
      error = actionError instanceof Error ? actionError.message : String(actionError);
      console.log(`❌ Action failed: ${error}`);
    }

    return {
      hasRole,
      actionSucceeded,
      error
    };
  }

  /**
   * Validate session expiry
   */
  async validateSessionExpiry(): Promise<{
    isValid: boolean;
    expiresAt?: Date;
    timeRemaining?: number;
  }> {
    if (!this.currentAuthState.isAuthenticated || !this.currentAuthState.expiresAt) {
      return { isValid: false };
    }

    const now = new Date();
    const expiresAt = this.currentAuthState.expiresAt;
    const isValid = now < expiresAt;
    const timeRemaining = isValid ? expiresAt.getTime() - now.getTime() : 0;

    console.log(`⏰ Session validation: ${isValid ? 'Valid' : 'Expired'}`);
    if (isValid) {
      console.log(`⏱️ Time remaining: ${Math.floor(timeRemaining / 1000 / 60)} minutes`);
    }

    return {
      isValid,
      expiresAt,
      timeRemaining
    };
  }

  /**
   * Set authentication state in browser storage
   */
  private async setAuthStateInBrowser(authState: AuthState): Promise<void> {
    await this.page.evaluate((state) => {
      localStorage.setItem('authState', JSON.stringify(state));
      if (state.token) {
        localStorage.setItem('authToken', state.token);
      }
      if (state.sessionId) {
        sessionStorage.setItem('sessionId', state.sessionId);
      }
    }, authState);
  }

  /**
   * Clear authentication state from browser storage
   */
  private async clearAuthStateFromBrowser(): Promise<void> {
    await this.page.evaluate(() => {
      localStorage.removeItem('authState');
      localStorage.removeItem('authToken');
      sessionStorage.removeItem('sessionId');
      
      // Clear any other auth-related storage
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.startsWith('auth') || key.startsWith('user'))) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));
    });
  }

  /**
   * Get permissions for a specific role
   */
  private getPermissionsForRole(role: string): string[] {
    const rolePermissions: Record<string, string[]> = {
      'admin': [
        'read:all',
        'write:all',
        'delete:all',
        'manage:users',
        'manage:system',
        'view:analytics',
        'export:data'
      ],
      'manager': [
        'read:all',
        'write:own',
        'delete:own',
        'view:analytics',
        'export:data'
      ],
      'user': [
        'read:own',
        'write:own',
        'view:dashboard'
      ],
      'viewer': [
        'read:own',
        'view:dashboard'
      ]
    };

    return rolePermissions[role] || rolePermissions['user'];
  }

  /**
   * Create test user with specific role and permissions
   */
  async createTestUser(userConfig: {
    email: string;
    role: string;
    permissions?: string[];
  }): Promise<AuthState> {
    const permissions = userConfig.permissions || this.getPermissionsForRole(userConfig.role);

    const testUser: AuthState = {
      isAuthenticated: true,
      user: {
        id: `test-${Date.now()}`,
        email: userConfig.email,
        role: userConfig.role,
        permissions
      },
      token: `test-token-${Date.now()}`,
      sessionId: `test-session-${Date.now()}`,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
    };

    console.log(`👤 Created test user: ${userConfig.email} (${userConfig.role})`);
    console.log(`🔑 Permissions: ${permissions.join(', ')}`);

    return testUser;
  }

  /**
   * Switch to different user context
   */
  async switchUserContext(userConfig: {
    email: string;
    role: string;
    permissions?: string[];
  }): Promise<AuthState> {
    console.log(`🔄 Switching user context to: ${userConfig.email}`);
    
    // Logout current user
    await this.logoutUser();
    
    // Create and authenticate new user
    const newUser = await this.createTestUser(userConfig);
    this.currentAuthState = newUser;
    
    // Set new auth state in browser
    await this.setAuthStateInBrowser(newUser);
    
    console.log(`✅ Switched to user: ${userConfig.email} (${userConfig.role})`);
    return newUser;
  }
}
