/**
 * Network Test Helper - Specialized helper for network-related test operations
 * 
 * Part of Phase 3.4 Test Infrastructure Refactoring
 * Extracted from test-helpers.ts (951 lines → modular architecture)
 * 
 * Responsibilities:
 * - Network condition detection and monitoring
 * - Adaptive timeout management
 * - Network performance testing
 * - Connection reliability validation
 */

import { Page } from '@playwright/test';
import { NetworkMonitor, NetworkCondition } from '../network-monitor';

export class NetworkTestHelper {
  private networkMonitor: NetworkMonitor;
  private currentNetworkCondition: NetworkCondition | null = null;

  constructor(private page: Page) {
    this.networkMonitor = NetworkMonitor.getInstance();
  }

  /**
   * Detect current network conditions
   */
  async detectNetworkConditions(): Promise<NetworkCondition> {
    console.log('🌐 Detecting network conditions...');
    
    this.currentNetworkCondition = await this.networkMonitor.detectNetworkCondition(this.page);
    
    console.log(`📊 Network quality: ${this.currentNetworkCondition.quality}`);
    console.log(`⏱️ Latency: ${this.currentNetworkCondition.latency}ms`);
    console.log(`📡 Bandwidth: ${this.currentNetworkCondition.bandwidth} Kbps`);
    console.log(`📦 Packet loss: ${this.currentNetworkCondition.packetLoss}%`);
    console.log(`⏰ Recommended timeout: ${this.currentNetworkCondition.recommendedTimeout}ms`);
    
    return this.currentNetworkCondition;
  }

  /**
   * Get adaptive timeout based on current network conditions
   */
  getAdaptiveTimeout(baseTimeout: number): number {
    if (!this.currentNetworkCondition) {
      console.log('⚠️ No network condition detected, using base timeout');
      return baseTimeout;
    }

    const adaptiveTimeout = this.networkMonitor.getAdaptiveTimeout(
      baseTimeout,
      this.currentNetworkCondition
    );

    console.log(`⏱️ Adaptive timeout: ${adaptiveTimeout}ms (base: ${baseTimeout}ms, quality: ${this.currentNetworkCondition.quality})`);
    return adaptiveTimeout;
  }

  /**
   * Wait for backend to be ready with network-aware timeouts
   */
  async waitForBackendReady(maxRetries: number = 5): Promise<void> {
    const baseTimeout = 10000;
    const adaptiveTimeout = this.getAdaptiveTimeout(baseTimeout);

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔍 Checking backend readiness (attempt ${attempt}/${maxRetries})...`);
        
        const response = await this.page.request.get('http://localhost:3001/api/health', {
          timeout: adaptiveTimeout
        });

        if (response.ok()) {
          console.log('✅ Backend is ready');
          return;
        } else {
          throw new Error(`Backend health check failed: ${response.status()}`);
        }

      } catch (error) {
        console.warn(`⚠️ Backend check attempt ${attempt} failed:`, error);
        
        if (attempt < maxRetries) {
          const retryDelay = this.getNetworkAwareRetryDelay(attempt);
          console.log(`⏳ Retrying in ${retryDelay}ms...`);
          await this.page.waitForTimeout(retryDelay);
        } else {
          throw new Error(`Backend not ready after ${maxRetries} attempts: ${error}`);
        }
      }
    }
  }

  /**
   * Test API endpoint with network-aware configuration
   */
  async testApiEndpoint(
    endpoint: string, 
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: any
  ): Promise<{
    success: boolean;
    status: number;
    responseTime: number;
    data?: any;
    error?: string;
  }> {
    const startTime = Date.now();
    const adaptiveTimeout = this.getAdaptiveTimeout(30000);

    try {
      console.log(`🔍 Testing ${method} ${endpoint}...`);

      let response;
      switch (method) {
        case 'GET':
          response = await this.page.request.get(endpoint, { timeout: adaptiveTimeout });
          break;
        case 'POST':
          response = await this.page.request.post(endpoint, { 
            data, 
            timeout: adaptiveTimeout 
          });
          break;
        case 'PUT':
          response = await this.page.request.put(endpoint, { 
            data, 
            timeout: adaptiveTimeout 
          });
          break;
        case 'DELETE':
          response = await this.page.request.delete(endpoint, { timeout: adaptiveTimeout });
          break;
      }

      const responseTime = Date.now() - startTime;
      const success = response.ok();
      
      let responseData;
      try {
        responseData = await response.json();
      } catch {
        // Response might not be JSON
      }

      const result = {
        success,
        status: response.status(),
        responseTime,
        data: responseData
      };

      if (success) {
        console.log(`✅ ${method} ${endpoint} succeeded in ${responseTime}ms`);
      } else {
        console.log(`❌ ${method} ${endpoint} failed with status ${response.status()} in ${responseTime}ms`);
      }

      return result;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      const result = {
        success: false,
        status: 0,
        responseTime,
        error: error instanceof Error ? error.message : String(error)
      };

      console.log(`❌ ${method} ${endpoint} failed with error in ${responseTime}ms:`, error);
      return result;
    }
  }

  /**
   * Monitor WebSocket connection with network awareness
   */
  async monitorWebSocketConnection(
    url: string = 'ws://localhost:3001',
    timeout: number = 30000
  ): Promise<{
    connected: boolean;
    connectionTime: number;
    error?: string;
  }> {
    const adaptiveTimeout = this.getAdaptiveTimeout(timeout);
    const startTime = Date.now();

    try {
      console.log(`🔌 Testing WebSocket connection to ${url}...`);

      const connectionResult = await this.page.evaluate(async (wsUrl, timeoutMs) => {
        return new Promise((resolve) => {
          const startTime = Date.now();
          const ws = new WebSocket(wsUrl);
          
          const cleanup = () => {
            try {
              ws.close();
            } catch (e) {
              // Ignore cleanup errors
            }
          };

          const timeout = setTimeout(() => {
            cleanup();
            resolve({
              connected: false,
              connectionTime: Date.now() - startTime,
              error: 'Connection timeout'
            });
          }, timeoutMs);

          ws.onopen = () => {
            clearTimeout(timeout);
            const connectionTime = Date.now() - startTime;
            cleanup();
            resolve({
              connected: true,
              connectionTime,
            });
          };

          ws.onerror = (error) => {
            clearTimeout(timeout);
            cleanup();
            resolve({
              connected: false,
              connectionTime: Date.now() - startTime,
              error: 'WebSocket error'
            });
          };
        });
      }, url, adaptiveTimeout);

      if (connectionResult.connected) {
        console.log(`✅ WebSocket connected in ${connectionResult.connectionTime}ms`);
      } else {
        console.log(`❌ WebSocket connection failed: ${connectionResult.error}`);
      }

      return connectionResult;

    } catch (error) {
      const connectionTime = Date.now() - startTime;
      const result = {
        connected: false,
        connectionTime,
        error: error instanceof Error ? error.message : String(error)
      };

      console.log(`❌ WebSocket test failed in ${connectionTime}ms:`, error);
      return result;
    }
  }

  /**
   * Perform network performance test
   */
  async performNetworkPerformanceTest(): Promise<{
    downloadSpeed: number; // Kbps
    uploadSpeed: number; // Kbps
    latency: number; // ms
    jitter: number; // ms
    packetLoss: number; // percentage
  }> {
    console.log('🚀 Performing network performance test...');

    // Test download speed
    const downloadSpeed = await this.testDownloadSpeed();
    
    // Test upload speed
    const uploadSpeed = await this.testUploadSpeed();
    
    // Test latency and jitter
    const { latency, jitter } = await this.testLatencyAndJitter();
    
    // Test packet loss
    const packetLoss = await this.testPacketLoss();

    const results = {
      downloadSpeed,
      uploadSpeed,
      latency,
      jitter,
      packetLoss
    };

    console.log('📊 Network performance test results:', results);
    return results;
  }

  /**
   * Get network-aware retry delay
   */
  private getNetworkAwareRetryDelay(attempt: number): number {
    const baseDelay = 1000;
    const exponentialBackoff = Math.pow(1.5, attempt - 1);
    
    // Adjust based on network quality
    let networkMultiplier = 1;
    if (this.currentNetworkCondition) {
      switch (this.currentNetworkCondition.quality) {
        case 'excellent':
          networkMultiplier = 0.8;
          break;
        case 'good':
          networkMultiplier = 1.0;
          break;
        case 'fair':
          networkMultiplier = 1.5;
          break;
        case 'poor':
          networkMultiplier = 2.0;
          break;
      }
    }

    return Math.floor(baseDelay * exponentialBackoff * networkMultiplier);
  }

  /**
   * Test download speed
   */
  private async testDownloadSpeed(): Promise<number> {
    try {
      const testSize = 1024 * 100; // 100KB test
      const startTime = Date.now();
      
      const response = await this.page.request.get('https://httpbin.org/bytes/' + testSize, {
        timeout: 30000
      });
      
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000; // seconds
      const speed = (testSize * 8) / (duration * 1000); // Kbps
      
      console.log(`📥 Download speed: ${speed.toFixed(2)} Kbps`);
      return speed;
      
    } catch (error) {
      console.warn('⚠️ Download speed test failed:', error);
      return 0;
    }
  }

  /**
   * Test upload speed
   */
  private async testUploadSpeed(): Promise<number> {
    try {
      const testData = 'x'.repeat(1024 * 50); // 50KB test data
      const startTime = Date.now();
      
      const response = await this.page.request.post('https://httpbin.org/post', {
        data: { data: testData },
        timeout: 30000
      });
      
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000; // seconds
      const speed = (testData.length * 8) / (duration * 1000); // Kbps
      
      console.log(`📤 Upload speed: ${speed.toFixed(2)} Kbps`);
      return speed;
      
    } catch (error) {
      console.warn('⚠️ Upload speed test failed:', error);
      return 0;
    }
  }

  /**
   * Test latency and jitter
   */
  private async testLatencyAndJitter(): Promise<{ latency: number; jitter: number }> {
    const measurements: number[] = [];
    const testCount = 5;

    for (let i = 0; i < testCount; i++) {
      try {
        const startTime = Date.now();
        await this.page.request.get('https://httpbin.org/get', { timeout: 10000 });
        const latency = Date.now() - startTime;
        measurements.push(latency);
      } catch (error) {
        console.warn(`⚠️ Latency test ${i + 1} failed:`, error);
      }
    }

    if (measurements.length === 0) {
      return { latency: 0, jitter: 0 };
    }

    const avgLatency = measurements.reduce((sum, val) => sum + val, 0) / measurements.length;
    const jitter = Math.sqrt(
      measurements.reduce((sum, val) => sum + Math.pow(val - avgLatency, 2), 0) / measurements.length
    );

    console.log(`⏱️ Average latency: ${avgLatency.toFixed(2)}ms, Jitter: ${jitter.toFixed(2)}ms`);
    return { latency: avgLatency, jitter };
  }

  /**
   * Test packet loss
   */
  private async testPacketLoss(): Promise<number> {
    const testCount = 10;
    let successCount = 0;

    for (let i = 0; i < testCount; i++) {
      try {
        const response = await this.page.request.get('https://httpbin.org/get', { timeout: 5000 });
        if (response.ok()) {
          successCount++;
        }
      } catch (error) {
        // Count as packet loss
      }
    }

    const packetLoss = ((testCount - successCount) / testCount) * 100;
    console.log(`📦 Packet loss: ${packetLoss.toFixed(1)}%`);
    return packetLoss;
  }

  /**
   * Get current network condition
   */
  getCurrentNetworkCondition(): NetworkCondition | null {
    return this.currentNetworkCondition;
  }

  /**
   * Get network history from monitor
   */
  getNetworkHistory(): NetworkCondition[] {
    return this.networkMonitor.getNetworkHistory();
  }
}
