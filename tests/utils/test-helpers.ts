/**
 * Test Helpers - Backward Compatibility Facade
 *
 * Part of Phase 3.4 Test Infrastructure Refactoring
 * Original file: 951 lines → 4 specialized helpers (~200-250 lines each)
 *
 * This file maintains backward compatibility with existing test structure
 * while delegating to the new modular architecture. All new test development should
 * use the modular helpers directly from './helpers/'.
 *
 * @deprecated Use the modular helpers from './helpers/' for new development
 */

import { Page, expect } from '@playwright/test';
import {
  BrowserTestHelper,
  NetworkTestHelper,
  EnvironmentTestHelper,
  AuthTestHelper,
  type AuthState,
  type NetworkCondition
} from './helpers';

console.warn('⚠️ Using legacy TestHelpers - consider migrating to modular helpers from ./helpers/');

export class TestHelpers {
  private browserHelper: BrowserTestHelper;
  private networkHelper: NetworkTestHelper;
  private environmentHelper: EnvironmentTestHelper;
  private authHelper: AuthTestHelper;

  constructor(private page: Page) {
    this.browserHelper = new BrowserTestHelper(page);
    this.networkHelper = new NetworkTestHelper(page);
    this.environmentHelper = new EnvironmentTestHelper(page);
    this.authHelper = new AuthTestHelper(page);
  }

  /**
   * Initialize test environment with comprehensive validation and monitoring
   * @deprecated Use EnvironmentTestHelper.setupTestEnvironment and NetworkTestHelper.detectNetworkConditions directly
   */
  async initializeTestEnvironment(testName: string): Promise<{
    networkCondition: NetworkCondition;
    environmentValid: boolean;
    recommendations: string[];
  }> {
    console.log(`🔍 Initializing test environment for: ${testName} (legacy method)`);

    // Setup environment using modular helper
    await this.environmentHelper.setupTestEnvironment();

    // Detect network conditions using modular helper
    const networkCondition = await this.networkHelper.detectNetworkConditions();

    // Check environment suitability
    const { suitable, issues, recommendations } = await this.environmentHelper.isEnvironmentSuitable();

    return {
      networkCondition,
      environmentValid: suitable,
      recommendations
    };
  }

  /**
   * Get adaptive timeout based on network conditions and browser compatibility
   * @deprecated Use BrowserTestHelper.getAdaptiveTimeout directly
   */
  getAdaptiveTimeout(baseTimeout: number, testName: string): number {
    return this.browserHelper.getAdaptiveTimeout(baseTimeout, testName);
  }

  /**
   * Check if test should be skipped based on compatibility matrix
   */
  shouldSkipTest(testName: string): boolean {
    const browser = this.page.context().browser()?.browserType().name() || 'chromium';
    return this.compatibilityMatrix.shouldSkipTest(testName, browser);
  }

  /**
   * Record test metrics for performance analysis
   */
  recordTestMetrics(testName: string, startTime: number, success: boolean, retryCount: number = 0, errorType?: string): void {
    const browser = this.page.context().browser()?.browserType().name() || 'chromium';
    const duration = Date.now() - startTime;

    const metrics: TestMetrics = {
      testName,
      browser,
      duration,
      networkCondition: this.currentNetworkCondition || {
        quality: 'fair',
        latency: 1000,
        bandwidth: 1000,
        packetLoss: 10,
        recommendedTimeout: 120000,
        timestamp: Date.now()
      },
      retryCount,
      success,
      errorType,
      timestamp: Date.now()
    };

    this.networkMonitor.recordMetrics(metrics);
    console.log(`📊 Test metrics recorded: ${testName} - ${success ? 'PASS' : 'FAIL'} (${duration}ms)`);
  }

  /**
   * Wait for the application to load completely
   */
  async waitForAppLoad() {
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForSelector('[data-testid="app-loaded"]', { timeout: 10000 }).catch(async () => {
      // Fallback: wait for main content to be visible
      try {
        await this.page.waitForSelector('main', { timeout: 10000 });
      } catch {
        // Second fallback: wait for any content container
        await this.page.waitForSelector('.min-h-screen', { timeout: 10000 });
      }
    });
  }

  /**
   * Check if server is available
   */
  async isServerAvailable(url: string): Promise<boolean> {
    try {
      const response = await this.page.request.get(url);
      return response.status() < 500;
    } catch {
      return false;
    }
  }

  /**
   * Navigate to a specific page and wait for it to load
   * @deprecated Use BrowserTestHelper.navigateToPage directly
   */
  async navigateToPage(path: string, retries: number = 3, testName: string = 'navigation') {
    return this.browserHelper.navigateToPage(path, retries, testName);
  }

  /**
   * Wait for the application to load completely
   * @deprecated Use BrowserTestHelper.waitForPageLoad directly
   */
  async waitForAppLoad() {
    return this.browserHelper.waitForElement('#root', 10000, ['main', '.min-h-screen']);
  }

  /**
   * Upload a file using the file input
   * @deprecated Use BrowserTestHelper.fillField for file inputs
   */
  async uploadFile(filePath: string, inputSelector: string = 'input[type="file"]') {
    await this.page.locator(inputSelector).setInputFiles(filePath);
  }

  /**
   * Wait for WebSocket connection to be established
   * @deprecated Use NetworkTestHelper.monitorWebSocketConnection directly
   */
  async waitForWebSocketConnection(maxAttempts: number = 3) {
    const result = await this.networkHelper.monitorWebSocketConnection('ws://localhost:3001', 30000);
    return result.connected;
  }

  // ============================================================================
  // ADDITIONAL LEGACY METHODS - Delegate to modular helpers
  // ============================================================================

  /**
   * Check if server is available
   * @deprecated Use NetworkTestHelper.testApiEndpoint directly
   */
  async isServerAvailable(url: string): Promise<boolean> {
    const result = await this.networkHelper.testApiEndpoint(url);
    return result.success;
  }

  /**
   * Get browser information
   * @deprecated Use BrowserTestHelper.getBrowserInfo directly
   */
  getBrowserInfo() {
    return this.browserHelper.getBrowserInfo();
  }

  /**
   * Take screenshot
   * @deprecated Use BrowserTestHelper.takeScreenshot directly
   */
  async takeScreenshot(name: string): Promise<string> {
    return this.browserHelper.takeScreenshot(name);
  }

  /**
   * Monitor system resources
   * @deprecated Use EnvironmentTestHelper.monitorSystemResources directly
   */
  async monitorSystemResources() {
    return this.environmentHelper.monitorSystemResources();
  }

  /**
   * Authenticate user for testing
   * @deprecated Use AuthTestHelper.authenticateUser directly
   */
  async authenticateUser(credentials?: any): Promise<AuthState> {
    return this.authHelper.authenticateUser(credentials);
  }

  /**
   * Test authenticated API call
   * @deprecated Use AuthTestHelper.testAuthenticatedApiCall directly
   */
  async testAuthenticatedApiCall(endpoint: string, method: any = 'GET', data?: any) {
    return this.authHelper.testAuthenticatedApiCall(endpoint, method, data);
  }

  /**
   * Setup test environment for API tests
   */
  async setupTestEnvironment(options: { apiOnly?: boolean } = {}): Promise<void> {
    return this.environmentHelper.setupTestEnvironment(options);
  }

  /**
   * Get authenticated request headers
   */
  async getAuthHeaders(): Promise<Record<string, string>> {
    return this.authHelper.getAuthHeaders();
  }

  /**
   * Wait for analysis to complete
   */
  async waitForAnalysisComplete(timeout: number = 120000): Promise<void> {
    return this.browserHelper.waitForAnalysisComplete(timeout);
  }
}

// ============================================================================
// EXPORT MODULAR HELPERS FOR DIRECT USE
// ============================================================================

// Re-export modular helpers for direct use
export {
  BrowserTestHelper,
  NetworkTestHelper,
  EnvironmentTestHelper,
  AuthTestHelper,
  type AuthState,
  type NetworkCondition
};
