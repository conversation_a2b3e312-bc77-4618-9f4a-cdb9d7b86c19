/**
 * Test Helpers - Backward Compatibility Facade
 * 
 * Part of Phase 3.4 Test Infrastructure Refactoring
 * Original file: 951 lines → 4 specialized helpers (~200-250 lines each)
 * 
 * This file maintains backward compatibility with existing test structure
 * while delegating to the new modular architecture. All new test development should
 * use the modular helpers directly from './helpers/'.
 * 
 * @deprecated Use the modular helpers from './helpers/' for new development
 */

import { Page, expect } from '@playwright/test';
import {
  BrowserTestHelper,
  NetworkTestHelper,
  EnvironmentTestHelper,
  AuthTestHelper,
  type AuthState,
  type NetworkCondition
} from './helpers';

console.warn('⚠️ Using legacy TestHelpers - consider migrating to modular helpers from ./helpers/');

export class TestHelpers {
  private browserHelper: BrowserTestHelper;
  private networkHelper: NetworkTestHelper;
  private environmentHelper: EnvironmentTestHelper;
  private authHelper: AuthTestHelper;

  constructor(private page: Page) {
    this.browserHelper = new BrowserTestHelper(page);
    this.networkHelper = new NetworkTestHelper(page);
    this.environmentHelper = new EnvironmentTestHelper(page);
    this.authHelper = new AuthTestHelper(page);
  }

  /**
   * Initialize test environment with comprehensive validation and monitoring
   * @deprecated Use EnvironmentTestHelper.setupTestEnvironment and NetworkTestHelper.detectNetworkConditions directly
   */
  async initializeTestEnvironment(testName: string): Promise<{
    networkCondition: NetworkCondition;
    environmentValid: boolean;
    recommendations: string[];
  }> {
    console.log(`🔍 Initializing test environment for: ${testName} (legacy method)`);

    // Setup environment using modular helper
    await this.environmentHelper.setupTestEnvironment();

    // Detect network conditions using modular helper
    const networkCondition = await this.networkHelper.detectNetworkConditions();

    // Check environment suitability
    const { suitable, issues, recommendations } = await this.environmentHelper.isEnvironmentSuitable();

    return {
      networkCondition,
      environmentValid: suitable,
      recommendations
    };
  }

  /**
   * Get adaptive timeout based on network conditions and browser compatibility
   * @deprecated Use BrowserTestHelper.getAdaptiveTimeout directly
   */
  getAdaptiveTimeout(baseTimeout: number, testName: string): number {
    return this.browserHelper.getAdaptiveTimeout(baseTimeout, testName);
  }

  /**
   * Check if test should be skipped based on compatibility matrix
   * @deprecated Use CompatibilityMatrix.shouldSkipTest directly
   */
  shouldSkipTest(testName: string): boolean {
    // This would need to be implemented in BrowserTestHelper or CompatibilityMatrix
    return false; // Placeholder
  }

  /**
   * Record test metrics for performance analysis
   * @deprecated Use NetworkTestHelper for performance monitoring
   */
  recordTestMetrics(testName: string, startTime: number, success: boolean, retryCount: number = 0, errorType?: string): void {
    const duration = Date.now() - startTime;
    console.log(`📊 Test metrics recorded: ${testName} - ${success ? 'PASS' : 'FAIL'} (${duration}ms, retries: ${retryCount})`);
  }

  /**
   * Navigate to a specific page and wait for it to load
   * @deprecated Use BrowserTestHelper.navigateToPage directly
   */
  async navigateToPage(path: string, retries: number = 3, testName: string = 'navigation') {
    return this.browserHelper.navigateToPage(path, retries, testName);
  }

  /**
   * Wait for the application to load completely
   * @deprecated Use BrowserTestHelper.waitForElement directly
   */
  async waitForAppLoad() {
    return this.browserHelper.waitForElement('#root', 10000, ['main', '.min-h-screen']);
  }

  /**
   * Upload a file using the file input
   * @deprecated Use BrowserTestHelper.fillField for file inputs
   */
  async uploadFile(filePath: string, inputSelector: string = 'input[type="file"]') {
    await this.page.locator(inputSelector).setInputFiles(filePath);
  }

  /**
   * Wait for WebSocket connection to be established
   * @deprecated Use NetworkTestHelper.monitorWebSocketConnection directly
   */
  async waitForWebSocketConnection(maxAttempts: number = 3) {
    const result = await this.networkHelper.monitorWebSocketConnection('ws://localhost:3001', 30000);
    return result.connected;
  }

  // ============================================================================
  // ADDITIONAL LEGACY METHODS - Delegate to modular helpers
  // ============================================================================

  /**
   * Check if server is available
   * @deprecated Use NetworkTestHelper.testApiEndpoint directly
   */
  async isServerAvailable(url: string): Promise<boolean> {
    const result = await this.networkHelper.testApiEndpoint(url);
    return result.success;
  }

  /**
   * Get browser information
   * @deprecated Use BrowserTestHelper.getBrowserInfo directly
   */
  getBrowserInfo() {
    return this.browserHelper.getBrowserInfo();
  }

  /**
   * Take screenshot
   * @deprecated Use BrowserTestHelper.takeScreenshot directly
   */
  async takeScreenshot(name: string): Promise<string> {
    return this.browserHelper.takeScreenshot(name);
  }

  /**
   * Monitor system resources
   * @deprecated Use EnvironmentTestHelper.monitorSystemResources directly
   */
  async monitorSystemResources() {
    return this.environmentHelper.monitorSystemResources();
  }

  /**
   * Authenticate user for testing
   * @deprecated Use AuthTestHelper.authenticateUser directly
   */
  async authenticateUser(credentials?: any): Promise<AuthState> {
    return this.authHelper.authenticateUser(credentials);
  }

  /**
   * Test authenticated API call
   * @deprecated Use AuthTestHelper.testAuthenticatedApiCall directly
   */
  async testAuthenticatedApiCall(endpoint: string, method: any = 'GET', data?: any) {
    return this.authHelper.testAuthenticatedApiCall(endpoint, method, data);
  }

  // ============================================================================
  // LEGACY METHODS WITH SIMPLE IMPLEMENTATIONS
  // ============================================================================

  /**
   * Wait for file validation with enhanced timeout
   */
  async waitForFileValidation(timeout: number = 30000) {
    return this.browserHelper.waitForElement('[data-testid="file-validated"]', timeout, [
      '[data-testid="file-uploaded"]',
      '.file-validated',
      '.upload-success',
      'button:has-text("Analyze"):not([disabled])',
      'button:has-text("Start"):not([disabled])'
    ]);
  }

  /**
   * Wait for analysis to complete with enhanced monitoring
   */
  async waitForAnalysisComplete(timeout: number = 120000) {
    await this.browserHelper.waitForElement('[data-testid="analysis-progress"]', 30000);
    await this.browserHelper.waitForElement('[data-testid="analysis-complete"]', timeout);
  }

  /**
   * Monitor analysis progress and return analysis ID
   */
  async monitorAnalysisProgress(page: any, analysisType: string = 'Analysis'): Promise<string> {
    console.log(`🔍 Monitoring ${analysisType} progress...`);
    
    // Wait for analysis to start
    await this.browserHelper.waitForElement('[data-testid="analysis-progress"], .analysis-progress', 30000);
    
    // Generate analysis ID
    const analysisId = `analysis_${Date.now()}`;
    
    // Wait for completion
    try {
      await this.browserHelper.waitForElement('[data-testid="analysis-complete"], [data-testid="analysis-results"], .analysis-complete', 120000);
      console.log(`✅ ${analysisType} completed successfully`);
    } catch (error) {
      console.warn(`⚠️ ${analysisType} timeout, but continuing test`);
    }
    
    return analysisId;
  }

  /**
   * Check if element contains text
   */
  async expectElementToContainText(selector: string, text: string) {
    const element = this.page.locator(selector);
    await expect(element).toContainText(text);
  }

  /**
   * Check if element is visible
   */
  async expectElementToBeVisible(selector: string) {
    const element = this.page.locator(selector).first();
    await expect(element).toBeVisible();
  }

  /**
   * Get API response data
   */
  async getApiResponse(endpoint: string) {
    const result = await this.networkHelper.testApiEndpoint(`http://localhost:3001${endpoint}`);
    if (!result.success) {
      throw new Error(`API request failed: ${result.error}`);
    }
    return result.data;
  }

  /**
   * Post data to API endpoint
   */
  async postToApi(endpoint: string, data: any) {
    return this.networkHelper.testApiEndpoint(`http://localhost:3001${endpoint}`, 'POST', data);
  }

  /**
   * Upload file to API endpoint
   */
  async uploadFileToApi(endpoint: string, filePath: string, fieldName: string = 'file') {
    // This would need to be implemented in NetworkTestHelper
    const response = await this.page.request.post(`http://localhost:3001${endpoint}`, {
      multipart: {
        [fieldName]: {
          name: require('path').basename(filePath),
          mimeType: this.getMimeType(filePath),
          buffer: require('fs').readFileSync(filePath),
        },
      },
    });
    return response;
  }

  /**
   * Get MIME type for file
   */
  private getMimeType(filePath: string): string {
    const ext = require('path').extname(filePath).toLowerCase();
    switch (ext) {
      case '.pdf': return 'application/pdf';
      case '.png': return 'image/png';
      case '.jpg':
      case '.jpeg': return 'image/jpeg';
      default: return 'application/octet-stream';
    }
  }

  /**
   * Wait for element to appear and be stable
   */
  async waitForStableElement(selector: string, timeout: number = 10000) {
    await this.browserHelper.waitForElement(selector, timeout);
    return this.page.locator(selector);
  }
}

// ============================================================================
// EXPORT MODULAR HELPERS FOR DIRECT USE
// ============================================================================

export {
  BrowserTestHelper,
  NetworkTestHelper,
  EnvironmentTestHelper,
  AuthTestHelper,
  type AuthState,
  type NetworkCondition
} from './helpers';
