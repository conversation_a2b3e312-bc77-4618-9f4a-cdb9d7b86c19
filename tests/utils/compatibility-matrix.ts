export interface BrowserIssue {
  browser: string;
  testPattern: string;
  issueType: 'known_failure' | 'flaky' | 'slow' | 'unsupported';
  description: string;
  workaround?: string;
  skipCondition?: () => boolean;
  retryStrategy?: {
    maxRetries: number;
    backoffMultiplier: number;
    customTimeout?: number;
  };
}

export interface CompatibilityRule {
  testPattern: string;
  browsers: {
    [browser: string]: {
      status: 'supported' | 'limited' | 'unsupported';
      issues?: string[];
      recommendations?: string[];
    };
  };
}

export class CompatibilityMatrix {
  private static instance: CompatibilityMatrix;
  private knownIssues: BrowserIssue[] = [];
  private compatibilityRules: CompatibilityRule[] = [];
  
  static getInstance(): CompatibilityMatrix {
    if (!CompatibilityMatrix.instance) {
      CompatibilityMatrix.instance = new CompatibilityMatrix();
      CompatibilityMatrix.instance.initializeKnownIssues();
    }
    return CompatibilityMatrix.instance;
  }

  private initializeKnownIssues(): void {
    // Firefox-specific issues
    this.addKnownIssue({
      browser: 'firefox',
      testPattern: 'frontend/.*',
      issueType: 'flaky',
      description: 'Firefox occasionally throws NS_ERROR_NET_EMPTY_RESPONSE on navigation',
      workaround: 'Implement multi-strategy navigation with retries',
      retryStrategy: {
        maxRetries: 3,
        backoffMultiplier: 1.5,
        customTimeout: 45000
      }
    });

    this.addKnownIssue({
      browser: 'firefox',
      testPattern: 'integration/websocket.*',
      issueType: 'slow',
      description: 'WebSocket connections are slower to establish in Firefox, may require NS_ERROR_NET_EMPTY_RESPONSE workarounds',
      workaround: 'Enhanced Firefox WebSocket handling with page reload and extended timeouts',
      retryStrategy: {
        maxRetries: 5, // Increased for Firefox WebSocket issues
        backoffMultiplier: 1.5,
        customTimeout: 45000 // Extended timeout for Firefox
      }
    });

    // WebKit-specific issues
    this.addKnownIssue({
      browser: 'webkit',
      testPattern: 'frontend/file-upload.*',
      issueType: 'flaky',
      description: 'File upload UI interactions can be unreliable in WebKit',
      workaround: 'Use alternative selectors and longer waits',
      retryStrategy: {
        maxRetries: 2,
        backoffMultiplier: 1.5,
        customTimeout: 60000
      }
    });

    this.addKnownIssue({
      browser: 'webkit',
      testPattern: 'integration/enhanced-azure-openai.*',
      issueType: 'slow',
      description: 'Complex AI integration tests run slower on WebKit',
      workaround: 'Increase timeouts for AI operations',
      retryStrategy: {
        maxRetries: 1,
        backoffMultiplier: 1.0,
        customTimeout: 240000
      }
    });

    // Mobile browser issues
    this.addKnownIssue({
      browser: 'Mobile Safari',
      testPattern: 'integration/.*',
      issueType: 'slow',
      description: 'Integration tests are significantly slower on mobile browsers',
      workaround: 'Increase timeouts and reduce parallelism',
      retryStrategy: {
        maxRetries: 1,
        backoffMultiplier: 1.0,
        customTimeout: 180000
      }
    });

    this.addKnownIssue({
      browser: 'Mobile Chrome',
      testPattern: 'frontend/.*responsive.*',
      issueType: 'flaky',
      description: 'Responsive design tests can be flaky due to viewport changes',
      workaround: 'Stabilize viewport before testing',
      retryStrategy: {
        maxRetries: 2,
        backoffMultiplier: 1.2
      }
    });

    // Performance Metrics Dashboard specific issues
    this.addKnownIssue({
      browser: 'firefox',
      testPattern: 'integration/performance-metrics-dashboard.*',
      issueType: 'flaky',
      description: 'Performance dashboard navigation can timeout in Firefox',
      workaround: 'Use navigateToPerformanceTab helper method with increased timeout',
      retryStrategy: {
        maxRetries: 3,
        backoffMultiplier: 1.5,
        customTimeout: 45000
      }
    });

    this.addKnownIssue({
      browser: 'webkit',
      testPattern: 'integration/performance-metrics-dashboard.*model-comparison.*',
      issueType: 'slow',
      description: 'Chart rendering and model comparison display slower in WebKit',
      workaround: 'Wait for chart elements to be fully rendered',
      retryStrategy: {
        maxRetries: 2,
        backoffMultiplier: 1.3,
        customTimeout: 35000
      }
    });

    this.addKnownIssue({
      browser: 'firefox',
      testPattern: 'integration/performance-metrics-dashboard.*tab-navigation.*',
      issueType: 'flaky',
      description: 'Tab state detection can be delayed in Firefox',
      workaround: 'Use explicit wait for tab state changes',
      retryStrategy: {
        maxRetries: 2,
        backoffMultiplier: 1.2,
        customTimeout: 35000
      }
    });

    // Initialize compatibility rules
    this.initializeCompatibilityRules();
  }

  private initializeCompatibilityRules(): void {
    // API tests - generally well supported
    this.addCompatibilityRule({
      testPattern: 'api/.*',
      browsers: {
        chromium: { status: 'supported' },
        firefox: { status: 'supported' },
        webkit: { status: 'supported' },
        'Mobile Chrome': { status: 'supported' },
        'Mobile Safari': { status: 'supported' }
      }
    });

    // Frontend tests - some browser variations
    this.addCompatibilityRule({
      testPattern: 'frontend/app-loading.*',
      browsers: {
        chromium: { status: 'supported' },
        firefox: { 
          status: 'limited', 
          issues: ['Occasional navigation timeouts'],
          recommendations: ['Use retry logic', 'Increase timeouts']
        },
        webkit: { status: 'supported' },
        'Mobile Chrome': { status: 'supported' },
        'Mobile Safari': { status: 'supported' }
      }
    });

    this.addCompatibilityRule({
      testPattern: 'frontend/file-upload.*',
      browsers: {
        chromium: { status: 'supported' },
        firefox: { status: 'supported' },
        webkit: { 
          status: 'limited',
          issues: ['File input interactions can be flaky'],
          recommendations: ['Use alternative selectors', 'Add stability waits']
        },
        'Mobile Chrome': { status: 'supported' },
        'Mobile Safari': { 
          status: 'limited',
          issues: ['Touch interactions differ from desktop'],
          recommendations: ['Use mobile-specific selectors']
        }
      }
    });

    // Integration tests - more complex compatibility
    this.addCompatibilityRule({
      testPattern: 'integration/azure-openai.*',
      browsers: {
        chromium: { status: 'supported' },
        firefox: { 
          status: 'supported',
          recommendations: ['Monitor for network-related timeouts']
        },
        webkit: { status: 'supported' },
        'Mobile Chrome': { 
          status: 'limited',
          issues: ['Slower performance on mobile'],
          recommendations: ['Increase timeouts', 'Reduce parallelism']
        },
        'Mobile Safari': { 
          status: 'limited',
          issues: ['Slower performance on mobile'],
          recommendations: ['Increase timeouts', 'Reduce parallelism']
        }
      }
    });

    this.addCompatibilityRule({
      testPattern: 'integration/websocket.*',
      browsers: {
        chromium: { status: 'supported' },
        firefox: {
          status: 'limited',
          issues: ['Slower WebSocket connection establishment'],
          recommendations: ['Increase connection timeouts']
        },
        webkit: { status: 'supported' },
        'Mobile Chrome': { status: 'supported' },
        'Mobile Safari': { status: 'supported' }
      }
    });

    // Performance Metrics Dashboard tests
    this.addCompatibilityRule({
      testPattern: 'integration/performance-metrics-dashboard.*',
      browsers: {
        chromium: { status: 'supported' },
        firefox: {
          status: 'limited',
          issues: ['Navigation timeouts', 'Tab switching delays'],
          recommendations: ['Use navigateToPerformanceTab helper', 'Increase timeouts', 'Add retry logic']
        },
        webkit: {
          status: 'limited',
          issues: ['Chart rendering delays', 'Progress bar animations'],
          recommendations: ['Wait for chart elements', 'Increase timeout for visualizations']
        },
        'Mobile Chrome': {
          status: 'limited',
          issues: ['Touch interactions with dashboard controls'],
          recommendations: ['Use mobile-specific selectors', 'Increase timeouts']
        },
        'Mobile Safari': {
          status: 'limited',
          issues: ['Touch interactions with dashboard controls'],
          recommendations: ['Use mobile-specific selectors', 'Increase timeouts']
        }
      }
    });

    this.addCompatibilityRule({
      testPattern: 'api/performance-metrics.*',
      browsers: {
        chromium: { status: 'supported' },
        firefox: { status: 'supported' },
        webkit: { status: 'supported' },
        'Mobile Chrome': { status: 'supported' },
        'Mobile Safari': { status: 'supported' }
      }
    });
  }

  addKnownIssue(issue: BrowserIssue): void {
    this.knownIssues.push(issue);
  }

  addCompatibilityRule(rule: CompatibilityRule): void {
    this.compatibilityRules.push(rule);
  }

  getKnownIssues(testName: string, browser: string): BrowserIssue[] {
    return this.knownIssues.filter(issue => 
      issue.browser === browser && 
      new RegExp(issue.testPattern).test(testName)
    );
  }

  shouldSkipTest(testName: string, browser: string): boolean {
    const issues = this.getKnownIssues(testName, browser);
    return issues.some(issue => issue.issueType === 'unsupported');
  }

  getRetryStrategy(testName: string, browser: string): BrowserIssue['retryStrategy'] | null {
    const issues = this.getKnownIssues(testName, browser);
    const issueWithRetry = issues.find(issue => issue.retryStrategy);
    return issueWithRetry?.retryStrategy || null;
  }

  getCompatibilityStatus(testName: string, browser: string): 'supported' | 'limited' | 'unsupported' {
    const rule = this.compatibilityRules.find(rule => 
      new RegExp(rule.testPattern).test(testName)
    );
    
    if (rule && rule.browsers[browser]) {
      return rule.browsers[browser].status;
    }
    
    // Default to supported if no specific rule found
    return 'supported';
  }

  getRecommendations(testName: string, browser: string): string[] {
    const rule = this.compatibilityRules.find(rule => 
      new RegExp(rule.testPattern).test(testName)
    );
    
    if (rule && rule.browsers[browser]) {
      return rule.browsers[browser].recommendations || [];
    }
    
    return [];
  }

  getBrowserSpecificTimeout(testName: string, browser: string, baseTimeout: number): number {
    const retryStrategy = this.getRetryStrategy(testName, browser);
    if (retryStrategy?.customTimeout) {
      return retryStrategy.customTimeout;
    }

    // Apply browser-specific multipliers
    const multipliers: { [key: string]: number } = {
      'chromium': 1.0,
      'firefox': 1.2,
      'webkit': 1.1,
      'Mobile Chrome': 1.5,
      'Mobile Safari': 1.5
    };

    const multiplier = multipliers[browser] || 1.0;
    return Math.round(baseTimeout * multiplier);
  }

  generateCompatibilityReport(): string {
    let report = '\n🔄 Cross-Browser Compatibility Matrix\n';
    report += '======================================\n\n';
    
    const browsers = ['chromium', 'firefox', 'webkit', 'Mobile Chrome', 'Mobile Safari'];
    const testCategories = ['api', 'frontend', 'integration'];
    
    report += 'Compatibility Overview:\n';
    testCategories.forEach(category => {
      report += `\n${category.toUpperCase()} Tests:\n`;
      browsers.forEach(browser => {
        const rules = this.compatibilityRules.filter(rule => 
          rule.testPattern.includes(category)
        );
        
        if (rules.length > 0) {
          const statuses = rules.map(rule => 
            rule.browsers[browser]?.status || 'supported'
          );
          const overallStatus = statuses.includes('unsupported') ? 'unsupported' :
                               statuses.includes('limited') ? 'limited' : 'supported';
          
          const icon = overallStatus === 'supported' ? '✅' : 
                      overallStatus === 'limited' ? '⚠️' : '❌';
          
          report += `  ${icon} ${browser}: ${overallStatus}\n`;
        }
      });
    });
    
    report += '\nKnown Issues Summary:\n';
    const issuesByBrowser = this.knownIssues.reduce((acc, issue) => {
      if (!acc[issue.browser]) acc[issue.browser] = [];
      acc[issue.browser].push(issue);
      return acc;
    }, {} as { [browser: string]: BrowserIssue[] });
    
    Object.entries(issuesByBrowser).forEach(([browser, issues]) => {
      report += `\n${browser}:\n`;
      issues.forEach(issue => {
        const icon = issue.issueType === 'known_failure' ? '❌' :
                    issue.issueType === 'flaky' ? '⚠️' :
                    issue.issueType === 'slow' ? '🐌' : '🚫';
        report += `  ${icon} ${issue.testPattern}: ${issue.description}\n`;
        if (issue.workaround) {
          report += `      Workaround: ${issue.workaround}\n`;
        }
      });
    });
    
    return report;
  }

  exportCompatibilityData(): string {
    return JSON.stringify({
      knownIssues: this.knownIssues,
      compatibilityRules: this.compatibilityRules,
      timestamp: Date.now()
    }, null, 2);
  }
}
