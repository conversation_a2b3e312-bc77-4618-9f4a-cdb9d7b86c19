import { Page } from '@playwright/test';
import fs from 'fs';
import path from 'path';

export interface EnvironmentCheck {
  name: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: any;
  timestamp: number;
}

export interface EnvironmentValidationResult {
  overall: 'pass' | 'fail' | 'warning';
  checks: EnvironmentCheck[];
  recommendations: string[];
  timestamp: number;
}

export class EnvironmentValidator {
  private static instance: EnvironmentValidator;
  
  static getInstance(): EnvironmentValidator {
    if (!EnvironmentValidator.instance) {
      EnvironmentValidator.instance = new EnvironmentValidator();
    }
    return EnvironmentValidator.instance;
  }

  async validateEnvironment(page: Page, options: { skipFrontend?: boolean; skipWebSocket?: boolean } = {}): Promise<EnvironmentValidationResult> {
    const checks: EnvironmentCheck[] = [];
    const recommendations: string[] = [];

    // Check frontend server (skip for API-only tests)
    if (!options.skipFrontend) {
      checks.push(await this.checkFrontendServer(page));
    }

    // Check backend server
    checks.push(await this.checkBackendServer(page));

    // Check Azure OpenAI configuration
    checks.push(await this.checkAzureOpenAI(page));

    // Check file system dependencies
    checks.push(await this.checkFileSystemDependencies());

    // Check test fixtures
    checks.push(await this.checkTestFixtures());

    // Check browser capabilities
    checks.push(await this.checkBrowserCapabilities(page));

    // Check WebSocket connectivity (skip for API-only tests)
    if (!options.skipWebSocket) {
      checks.push(await this.checkWebSocketConnectivity(page));
    }

    // Check system resources
    checks.push(await this.checkSystemResources());

    // Determine overall status
    const failedChecks = checks.filter(c => c.status === 'fail');
    const warningChecks = checks.filter(c => c.status === 'warning');
    
    let overall: 'pass' | 'fail' | 'warning';
    if (failedChecks.length > 0) {
      overall = 'fail';
      recommendations.push('Fix failed checks before running tests');
    } else if (warningChecks.length > 0) {
      overall = 'warning';
      recommendations.push('Consider addressing warnings for optimal test performance');
    } else {
      overall = 'pass';
    }

    // Add specific recommendations based on checks
    if (failedChecks.some(c => c.name.includes('Server'))) {
      recommendations.push('Ensure both frontend and backend servers are running');
    }
    
    if (warningChecks.some(c => c.name.includes('Network'))) {
      recommendations.push('Consider running tests during off-peak hours for better network performance');
    }

    return {
      overall,
      checks,
      recommendations,
      timestamp: Date.now()
    };
  }

  private async checkFrontendServer(page: Page): Promise<EnvironmentCheck> {
    try {
      const response = await page.request.get('http://localhost:8080/', { timeout: 10000 });
      
      if (response.ok()) {
        return {
          name: 'Frontend Server',
          status: 'pass',
          message: 'Frontend server is running and responsive',
          details: { status: response.status(), url: 'http://localhost:8080/' },
          timestamp: Date.now()
        };
      } else {
        return {
          name: 'Frontend Server',
          status: 'fail',
          message: `Frontend server returned status ${response.status()}`,
          details: { status: response.status() },
          timestamp: Date.now()
        };
      }
    } catch (error) {
      return {
        name: 'Frontend Server',
        status: 'fail',
        message: 'Frontend server is not accessible',
        details: { error: error instanceof Error ? error.message : String(error) },
        timestamp: Date.now()
      };
    }
  }

  private async checkBackendServer(page: Page): Promise<EnvironmentCheck> {
    try {
      const response = await page.request.get('http://localhost:3001/api/health', { timeout: 10000 });
      
      if (response.ok()) {
        const data = await response.json();
        return {
          name: 'Backend Server',
          status: 'pass',
          message: 'Backend server is healthy',
          details: { status: response.status(), health: data },
          timestamp: Date.now()
        };
      } else {
        return {
          name: 'Backend Server',
          status: 'fail',
          message: `Backend server health check failed with status ${response.status()}`,
          details: { status: response.status() },
          timestamp: Date.now()
        };
      }
    } catch (error) {
      return {
        name: 'Backend Server',
        status: 'fail',
        message: 'Backend server is not accessible',
        details: { error: error instanceof Error ? error.message : String(error) },
        timestamp: Date.now()
      };
    }
  }

  private async checkAzureOpenAI(page: Page): Promise<EnvironmentCheck> {
    try {
      const response = await page.request.get('http://localhost:3001/api/health/detailed', { timeout: 15000 });
      
      if (response.ok()) {
        const data = await response.json();
        const openaiService = data.data?.services?.openai;
        
        if (openaiService?.configured && openaiService?.type === 'azure') {
          return {
            name: 'Azure OpenAI Configuration',
            status: 'pass',
            message: 'Azure OpenAI is properly configured',
            details: { 
              endpoint: openaiService.endpoint,
              status: openaiService.status,
              type: openaiService.type
            },
            timestamp: Date.now()
          };
        } else {
          return {
            name: 'Azure OpenAI Configuration',
            status: 'fail',
            message: 'Azure OpenAI is not properly configured',
            details: { openaiService },
            timestamp: Date.now()
          };
        }
      } else {
        return {
          name: 'Azure OpenAI Configuration',
          status: 'fail',
          message: 'Unable to check Azure OpenAI configuration',
          details: { status: response.status() },
          timestamp: Date.now()
        };
      }
    } catch (error) {
      return {
        name: 'Azure OpenAI Configuration',
        status: 'fail',
        message: 'Error checking Azure OpenAI configuration',
        details: { error: error instanceof Error ? error.message : String(error) },
        timestamp: Date.now()
      };
    }
  }

  private async checkFileSystemDependencies(): Promise<EnvironmentCheck> {
    const requiredDirs = [
      'tests/fixtures',
      'test-results',
      'uploads',
      'temp'
    ];

    const missingDirs: string[] = [];
    const existingDirs: string[] = [];

    for (const dir of requiredDirs) {
      if (fs.existsSync(dir)) {
        existingDirs.push(dir);
      } else {
        missingDirs.push(dir);
      }
    }

    if (missingDirs.length === 0) {
      return {
        name: 'File System Dependencies',
        status: 'pass',
        message: 'All required directories exist',
        details: { existingDirs },
        timestamp: Date.now()
      };
    } else {
      return {
        name: 'File System Dependencies',
        status: 'warning',
        message: `Some directories are missing: ${missingDirs.join(', ')}`,
        details: { missingDirs, existingDirs },
        timestamp: Date.now()
      };
    }
  }

  private async checkTestFixtures(): Promise<EnvironmentCheck> {
    const requiredFixtures = [
      'tests/fixtures/kitchen-design-test.pdf',
      'tests/fixtures/kitchen-design-test.png',
      'tests/fixtures/kitchen-design-large.pdf'
    ];

    const missingFixtures: string[] = [];
    const existingFixtures: string[] = [];

    for (const fixture of requiredFixtures) {
      if (fs.existsSync(fixture)) {
        existingFixtures.push(fixture);
      } else {
        missingFixtures.push(fixture);
      }
    }

    if (missingFixtures.length === 0) {
      return {
        name: 'Test Fixtures',
        status: 'pass',
        message: 'All test fixtures are available',
        details: { existingFixtures },
        timestamp: Date.now()
      };
    } else {
      return {
        name: 'Test Fixtures',
        status: 'fail',
        message: `Missing test fixtures: ${missingFixtures.join(', ')}`,
        details: { missingFixtures, existingFixtures },
        timestamp: Date.now()
      };
    }
  }

  private async checkBrowserCapabilities(page: Page): Promise<EnvironmentCheck> {
    try {
      const browserInfo = await page.evaluate(() => ({
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
        hardwareConcurrency: navigator.hardwareConcurrency,
        maxTouchPoints: navigator.maxTouchPoints
      }));

      const capabilities = await page.evaluate(() => ({
        localStorage: typeof Storage !== 'undefined',
        sessionStorage: typeof Storage !== 'undefined',
        webSocket: typeof WebSocket !== 'undefined',
        fetch: typeof fetch !== 'undefined',
        promises: typeof Promise !== 'undefined'
      }));

      const allCapabilitiesSupported = Object.values(capabilities).every(Boolean);

      return {
        name: 'Browser Capabilities',
        status: allCapabilitiesSupported ? 'pass' : 'warning',
        message: allCapabilitiesSupported 
          ? 'All required browser capabilities are supported'
          : 'Some browser capabilities may be limited',
        details: { browserInfo, capabilities },
        timestamp: Date.now()
      };
    } catch (error) {
      return {
        name: 'Browser Capabilities',
        status: 'warning',
        message: 'Unable to fully assess browser capabilities',
        details: { error: error instanceof Error ? error.message : String(error) },
        timestamp: Date.now()
      };
    }
  }

  private async checkWebSocketConnectivity(page: Page): Promise<EnvironmentCheck> {
    try {
      // Navigate to the app to test WebSocket
      await page.goto('http://localhost:8080/', { timeout: 10000 });
      
      // Wait for Socket.IO to load
      const socketIOLoaded = await page.waitForFunction(() => {
        return typeof window.io !== 'undefined';
      }, { timeout: 15000 }).then(() => true).catch(() => false);

      if (!socketIOLoaded) {
        return {
          name: 'WebSocket Connectivity',
          status: 'warning',
          message: 'Socket.IO library not loaded',
          timestamp: Date.now()
        };
      }

      // Test WebSocket connection
      const connected = await page.waitForFunction(() => {
        return window.io && window.io.connected;
      }, { timeout: 10000 }).then(() => true).catch(() => false);

      return {
        name: 'WebSocket Connectivity',
        status: connected ? 'pass' : 'warning',
        message: connected 
          ? 'WebSocket connection established successfully'
          : 'WebSocket connection could not be established',
        timestamp: Date.now()
      };
    } catch (error) {
      return {
        name: 'WebSocket Connectivity',
        status: 'warning',
        message: 'Unable to test WebSocket connectivity',
        details: { error: error instanceof Error ? error.message : String(error) },
        timestamp: Date.now()
      };
    }
  }

  private async checkSystemResources(): Promise<EnvironmentCheck> {
    try {
      // Basic system resource check (limited in browser environment)
      const memoryInfo = await (global as any).process?.memoryUsage?.() || null;
      
      return {
        name: 'System Resources',
        status: 'pass',
        message: 'System resources check completed',
        details: { memoryInfo },
        timestamp: Date.now()
      };
    } catch (error) {
      return {
        name: 'System Resources',
        status: 'warning',
        message: 'Unable to check system resources',
        details: { error: error instanceof Error ? error.message : String(error) },
        timestamp: Date.now()
      };
    }
  }

  generateValidationReport(result: EnvironmentValidationResult): string {
    let report = '\n🔍 Environment Validation Report\n';
    report += '=====================================\n\n';
    
    report += `Overall Status: ${result.overall.toUpperCase()}\n`;
    report += `Timestamp: ${new Date(result.timestamp).toISOString()}\n\n`;
    
    report += 'Detailed Checks:\n';
    result.checks.forEach(check => {
      const icon = check.status === 'pass' ? '✅' : check.status === 'warning' ? '⚠️' : '❌';
      report += `  ${icon} ${check.name}: ${check.message}\n`;
    });
    
    if (result.recommendations.length > 0) {
      report += '\nRecommendations:\n';
      result.recommendations.forEach(rec => {
        report += `  • ${rec}\n`;
      });
    }
    
    return report;
  }
}
