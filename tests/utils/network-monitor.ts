import { Page } from '@playwright/test';

export interface NetworkCondition {
  quality: 'excellent' | 'good' | 'fair' | 'poor';
  latency: number;
  bandwidth: number;
  packetLoss: number;
  recommendedTimeout: number;
  timestamp: number;
}

export interface TestMetrics {
  testName: string;
  browser: string;
  duration: number;
  networkCondition: NetworkCondition;
  retryCount: number;
  success: boolean;
  errorType?: string;
  timestamp: number;
  resourceUsage?: {
    memoryUsage: number;
    cpuUsage: number;
  };
}

export class NetworkMonitor {
  private static instance: NetworkMonitor;
  private metrics: TestMetrics[] = [];
  private networkHistory: NetworkCondition[] = [];
  
  static getInstance(): NetworkMonitor {
    if (!NetworkMonitor.instance) {
      NetworkMonitor.instance = new NetworkMonitor();
    }
    return NetworkMonitor.instance;
  }

  async detectNetworkCondition(page: Page): Promise<NetworkCondition> {
    const startTime = Date.now();
    
    try {
      // Test multiple endpoints to get accurate network assessment
      const testEndpoints = [
        { url: 'http://localhost:3001/api/health', weight: 0.4 },
        { url: 'http://localhost:8080/', weight: 0.4 },
        { url: 'https://httpbin.org/delay/1', weight: 0.2 }
      ];

      const results = await Promise.allSettled(
        testEndpoints.map(async ({ url, weight }) => {
          const requestStart = Date.now();
          try {
            const response = await page.request.get(url, { timeout: 10000 });
            const requestEnd = Date.now();
            return {
              url,
              weight,
              latency: requestEnd - requestStart,
              success: response.ok(),
              status: response.status()
            };
          } catch (error) {
            return {
              url,
              weight,
              latency: Date.now() - requestStart,
              success: false,
              error: error instanceof Error ? error.message : String(error)
            };
          }
        })
      );

      // Calculate weighted network metrics
      const successfulRequests = results
        .filter((result): result is PromiseFulfilledResult<any> => 
          result.status === 'fulfilled' && result.value.success
        )
        .map(result => result.value);

      if (successfulRequests.length === 0) {
        const condition: NetworkCondition = {
          quality: 'poor',
          latency: 5000,
          bandwidth: 0,
          packetLoss: 100,
          recommendedTimeout: 180000,
          timestamp: Date.now()
        };
        this.networkHistory.push(condition);
        return condition;
      }

      // Calculate weighted average latency
      const totalWeight = successfulRequests.reduce((sum, req) => sum + req.weight, 0);
      const weightedLatency = successfulRequests.reduce((sum, req) => 
        sum + (req.latency * req.weight), 0) / totalWeight;

      const packetLoss = ((results.length - successfulRequests.length) / results.length) * 100;

      // Determine quality based on latency and packet loss
      let quality: NetworkCondition['quality'];
      let recommendedTimeout: number;

      if (weightedLatency < 100 && packetLoss < 5) {
        quality = 'excellent';
        recommendedTimeout = 30000;
      } else if (weightedLatency < 300 && packetLoss < 15) {
        quality = 'good';
        recommendedTimeout = 60000;
      } else if (weightedLatency < 1000 && packetLoss < 30) {
        quality = 'fair';
        recommendedTimeout = 120000;
      } else {
        quality = 'poor';
        recommendedTimeout = 180000;
      }

      const condition: NetworkCondition = {
        quality,
        latency: weightedLatency,
        bandwidth: this.estimateBandwidth(weightedLatency),
        packetLoss,
        recommendedTimeout,
        timestamp: Date.now()
      };

      this.networkHistory.push(condition);
      
      // Keep only last 100 network measurements
      if (this.networkHistory.length > 100) {
        this.networkHistory = this.networkHistory.slice(-100);
      }

      return condition;

    } catch (error) {
      console.warn('Network condition detection failed:', error);
      const condition: NetworkCondition = {
        quality: 'fair',
        latency: 1000,
        bandwidth: 1000,
        packetLoss: 10,
        recommendedTimeout: 120000,
        timestamp: Date.now()
      };
      this.networkHistory.push(condition);
      return condition;
    }
  }

  private estimateBandwidth(latency: number): number {
    // Rough bandwidth estimation based on latency
    if (latency < 50) return 10000; // 10 Mbps
    if (latency < 100) return 5000;  // 5 Mbps
    if (latency < 300) return 2000;  // 2 Mbps
    if (latency < 1000) return 1000; // 1 Mbps
    return 500; // 0.5 Mbps
  }

  getAdaptiveTimeout(baseTimeout: number, networkCondition?: NetworkCondition): number {
    const condition = networkCondition || this.getLatestNetworkCondition();
    if (!condition) return baseTimeout;

    // Adjust timeout based on network quality
    const multiplier = {
      excellent: 1.0,
      good: 1.5,
      fair: 2.0,
      poor: 3.0
    }[condition.quality];

    return Math.min(baseTimeout * multiplier, 300000); // Cap at 5 minutes
  }

  getLatestNetworkCondition(): NetworkCondition | null {
    return this.networkHistory.length > 0 
      ? this.networkHistory[this.networkHistory.length - 1] 
      : null;
  }

  recordMetrics(metrics: TestMetrics): void {
    this.metrics.push(metrics);
    
    // Keep only last 1000 metrics to prevent memory issues
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  getMetrics(): TestMetrics[] {
    return [...this.metrics];
  }

  getAveragePerformance(browser?: string): {
    avgDuration: number;
    successRate: number;
    avgRetries: number;
    networkQualityDistribution: Record<string, number>;
  } {
    const filteredMetrics = browser 
      ? this.metrics.filter(m => m.browser === browser)
      : this.metrics;

    if (filteredMetrics.length === 0) {
      return { 
        avgDuration: 0, 
        successRate: 0, 
        avgRetries: 0,
        networkQualityDistribution: {}
      };
    }

    const avgDuration = filteredMetrics.reduce((sum, m) => sum + m.duration, 0) / filteredMetrics.length;
    const successRate = (filteredMetrics.filter(m => m.success).length / filteredMetrics.length) * 100;
    const avgRetries = filteredMetrics.reduce((sum, m) => sum + m.retryCount, 0) / filteredMetrics.length;

    // Calculate network quality distribution
    const qualityCount = filteredMetrics.reduce((acc, m) => {
      const quality = m.networkCondition.quality;
      acc[quality] = (acc[quality] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const networkQualityDistribution = Object.entries(qualityCount).reduce((acc, [quality, count]) => {
      acc[quality] = (count / filteredMetrics.length) * 100;
      return acc;
    }, {} as Record<string, number>);

    return { avgDuration, successRate, avgRetries, networkQualityDistribution };
  }

  generatePerformanceReport(): string {
    const overall = this.getAveragePerformance();
    const browsers = ['chromium', 'firefox', 'webkit'];
    
    let report = '\n📊 Test Performance Report\n';
    report += '================================\n\n';
    
    report += `Overall Performance:\n`;
    report += `  Average Duration: ${overall.avgDuration.toFixed(2)}ms\n`;
    report += `  Success Rate: ${overall.successRate.toFixed(1)}%\n`;
    report += `  Average Retries: ${overall.avgRetries.toFixed(2)}\n\n`;
    
    report += `Network Quality Distribution:\n`;
    Object.entries(overall.networkQualityDistribution).forEach(([quality, percentage]) => {
      report += `  ${quality}: ${percentage.toFixed(1)}%\n`;
    });
    
    report += '\nBrowser-Specific Performance:\n';
    browsers.forEach(browser => {
      const browserStats = this.getAveragePerformance(browser);
      report += `  ${browser}:\n`;
      report += `    Success Rate: ${browserStats.successRate.toFixed(1)}%\n`;
      report += `    Avg Duration: ${browserStats.avgDuration.toFixed(2)}ms\n`;
      report += `    Avg Retries: ${browserStats.avgRetries.toFixed(2)}\n`;
    });
    
    return report;
  }

  exportMetrics(): string {
    return JSON.stringify({
      metrics: this.metrics,
      networkHistory: this.networkHistory,
      timestamp: Date.now()
    }, null, 2);
  }
}
