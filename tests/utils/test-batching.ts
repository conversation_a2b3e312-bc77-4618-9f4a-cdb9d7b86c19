import { TestInfo } from '@playwright/test';

export interface TestComplexity {
  name: string;
  estimatedDuration: number;
  resourceIntensity: 'low' | 'medium' | 'high';
  networkDependency: 'none' | 'low' | 'medium' | 'high';
  browserCompatibility: {
    chromium: 'excellent' | 'good' | 'fair' | 'poor';
    firefox: 'excellent' | 'good' | 'fair' | 'poor';
    webkit: 'excellent' | 'good' | 'fair' | 'poor';
  };
  dependencies: string[];
  category: 'api' | 'frontend' | 'integration' | 'performance';
}

export interface TestBatch {
  id: string;
  tests: string[];
  estimatedDuration: number;
  maxParallelism: number;
  priority: 'high' | 'medium' | 'low';
  resourceRequirement: 'low' | 'medium' | 'high';
  recommendedBrowser?: string;
}

export class TestBatchingManager {
  private static instance: TestBatchingManager;
  private testComplexities: Map<string, TestComplexity> = new Map();
  
  static getInstance(): TestBatchingManager {
    if (!TestBatchingManager.instance) {
      TestBatchingManager.instance = new TestBatchingManager();
      TestBatchingManager.instance.initializeTestComplexities();
    }
    return TestBatchingManager.instance;
  }

  private initializeTestComplexities(): void {
    // API Tests - Generally fast and reliable
    this.addTestComplexity({
      name: 'api/health.spec.ts',
      estimatedDuration: 5000,
      resourceIntensity: 'low',
      networkDependency: 'low',
      browserCompatibility: { chromium: 'excellent', firefox: 'excellent', webkit: 'excellent' },
      dependencies: [],
      category: 'api'
    });

    this.addTestComplexity({
      name: 'api/analysis.spec.ts',
      estimatedDuration: 15000,
      resourceIntensity: 'medium',
      networkDependency: 'medium',
      browserCompatibility: { chromium: 'excellent', firefox: 'excellent', webkit: 'excellent' },
      dependencies: ['backend-server'],
      category: 'api'
    });

    // Frontend Tests - Medium complexity
    this.addTestComplexity({
      name: 'frontend/app-loading.spec.ts',
      estimatedDuration: 20000,
      resourceIntensity: 'medium',
      networkDependency: 'medium',
      browserCompatibility: { chromium: 'excellent', firefox: 'good', webkit: 'good' },
      dependencies: ['frontend-server'],
      category: 'frontend'
    });

    this.addTestComplexity({
      name: 'frontend/file-upload.spec.ts',
      estimatedDuration: 30000,
      resourceIntensity: 'medium',
      networkDependency: 'medium',
      browserCompatibility: { chromium: 'excellent', firefox: 'good', webkit: 'fair' },
      dependencies: ['frontend-server', 'backend-server'],
      category: 'frontend'
    });

    // Integration Tests - High complexity
    this.addTestComplexity({
      name: 'integration/azure-openai.spec.ts',
      estimatedDuration: 120000,
      resourceIntensity: 'high',
      networkDependency: 'high',
      browserCompatibility: { chromium: 'excellent', firefox: 'good', webkit: 'good' },
      dependencies: ['azure-openai', 'backend-server'],
      category: 'integration'
    });

    this.addTestComplexity({
      name: 'integration/enhanced-azure-openai.spec.ts',
      estimatedDuration: 180000,
      resourceIntensity: 'high',
      networkDependency: 'high',
      browserCompatibility: { chromium: 'excellent', firefox: 'good', webkit: 'fair' },
      dependencies: ['azure-openai', 'backend-server', 'advanced-ai-services'],
      category: 'integration'
    });

    this.addTestComplexity({
      name: 'integration/advanced-ai-services.spec.ts',
      estimatedDuration: 150000,
      resourceIntensity: 'high',
      networkDependency: 'high',
      browserCompatibility: { chromium: 'excellent', firefox: 'good', webkit: 'good' },
      dependencies: ['azure-openai', 'backend-server', 'advanced-ai-services'],
      category: 'integration'
    });

    this.addTestComplexity({
      name: 'integration/websocket.spec.ts',
      estimatedDuration: 45000,
      resourceIntensity: 'medium',
      networkDependency: 'high',
      browserCompatibility: { chromium: 'excellent', firefox: 'fair', webkit: 'good' },
      dependencies: ['websocket-server', 'backend-server'],
      category: 'integration'
    });

    // Performance Metrics Dashboard tests
    this.addTestComplexity({
      name: 'api/performance-metrics.spec.ts',
      estimatedDuration: 60000, // 1 minute
      resourceIntensity: 'medium',
      networkDependency: 'high',
      browserCompatibility: { chromium: 'excellent', firefox: 'excellent', webkit: 'excellent' },
      dependencies: ['backend-server', 'performance-api'],
      category: 'api'
    });

    this.addTestComplexity({
      name: 'integration/performance-metrics-dashboard.spec.ts',
      estimatedDuration: 120000, // 2 minutes
      resourceIntensity: 'medium',
      networkDependency: 'high',
      browserCompatibility: { chromium: 'excellent', firefox: 'fair', webkit: 'good' },
      dependencies: ['frontend-server', 'backend-server', 'performance-api'],
      category: 'performance'
    });

    this.addTestComplexity({
      name: 'integration/performance-metrics-dashboard/load-dashboard',
      estimatedDuration: 25000,
      resourceIntensity: 'medium',
      networkDependency: 'medium',
      browserCompatibility: { chromium: 'excellent', firefox: 'fair', webkit: 'good' },
      dependencies: ['frontend-server'],
      category: 'performance'
    });

    this.addTestComplexity({
      name: 'integration/performance-metrics-dashboard/tab-navigation',
      estimatedDuration: 30000,
      resourceIntensity: 'low',
      networkDependency: 'low',
      browserCompatibility: { chromium: 'excellent', firefox: 'fair', webkit: 'good' },
      dependencies: ['frontend-server'],
      category: 'performance'
    });

    this.addTestComplexity({
      name: 'integration/performance-metrics-dashboard/model-comparison',
      estimatedDuration: 40000,
      resourceIntensity: 'medium',
      networkDependency: 'high',
      browserCompatibility: { chromium: 'excellent', firefox: 'good', webkit: 'fair' },
      dependencies: ['frontend-server', 'backend-server', 'performance-api'],
      category: 'performance'
    });
  }

  addTestComplexity(complexity: TestComplexity): void {
    this.testComplexities.set(complexity.name, complexity);
  }

  getTestComplexity(testName: string): TestComplexity | undefined {
    // Try exact match first
    let complexity = this.testComplexities.get(testName);
    if (complexity) return complexity;

    // Try partial match
    for (const [name, comp] of this.testComplexities.entries()) {
      if (testName.includes(name) || name.includes(testName)) {
        return comp;
      }
    }

    // Default complexity for unknown tests
    return {
      name: testName,
      estimatedDuration: 30000,
      resourceIntensity: 'medium',
      networkDependency: 'medium',
      browserCompatibility: { chromium: 'good', firefox: 'good', webkit: 'good' },
      dependencies: [],
      category: 'integration'
    };
  }

  createOptimalBatches(
    testNames: string[], 
    maxBatchDuration: number = 300000, // 5 minutes
    maxParallelism: number = 3
  ): TestBatch[] {
    const batches: TestBatch[] = [];
    const remainingTests = [...testNames];

    // Sort tests by priority and complexity
    remainingTests.sort((a, b) => {
      const complexityA = this.getTestComplexity(a)!;
      const complexityB = this.getTestComplexity(b)!;
      
      // Prioritize by category (api -> frontend -> integration)
      const categoryPriority = { api: 3, frontend: 2, integration: 1, performance: 0 };
      const priorityDiff = categoryPriority[complexityA.category] - categoryPriority[complexityB.category];
      if (priorityDiff !== 0) return priorityDiff;
      
      // Then by estimated duration (shorter first)
      return complexityA.estimatedDuration - complexityB.estimatedDuration;
    });

    let batchId = 1;
    while (remainingTests.length > 0) {
      const batch = this.createSingleBatch(
        remainingTests, 
        maxBatchDuration, 
        maxParallelism, 
        `batch-${batchId++}`
      );
      
      batches.push(batch);
      
      // Remove batched tests from remaining tests
      batch.tests.forEach(testName => {
        const index = remainingTests.indexOf(testName);
        if (index > -1) {
          remainingTests.splice(index, 1);
        }
      });
    }

    return batches;
  }

  private createSingleBatch(
    availableTests: string[], 
    maxDuration: number, 
    maxParallelism: number,
    batchId: string
  ): TestBatch {
    const batchTests: string[] = [];
    let totalDuration = 0;
    let maxResourceIntensity: 'low' | 'medium' | 'high' = 'low';
    let priority: 'high' | 'medium' | 'low' = 'low';

    for (const testName of availableTests) {
      const complexity = this.getTestComplexity(testName)!;
      
      // Check if adding this test would exceed batch limits
      if (totalDuration + complexity.estimatedDuration > maxDuration) {
        continue;
      }
      
      // Check parallelism constraints
      if (batchTests.length >= maxParallelism) {
        continue;
      }

      batchTests.push(testName);
      totalDuration += complexity.estimatedDuration;
      
      // Update batch characteristics
      if (complexity.resourceIntensity === 'high' || maxResourceIntensity === 'high') {
        maxResourceIntensity = 'high';
      } else if (complexity.resourceIntensity === 'medium' || maxResourceIntensity === 'medium') {
        maxResourceIntensity = 'medium';
      }

      // Set priority based on test category
      if (complexity.category === 'api') {
        priority = 'high';
      } else if (complexity.category === 'frontend' && priority !== 'high') {
        priority = 'medium';
      }
    }

    // Adjust parallelism based on resource intensity
    const adjustedParallelism = maxResourceIntensity === 'high' ? 
      Math.min(maxParallelism, 2) : maxParallelism;

    return {
      id: batchId,
      tests: batchTests,
      estimatedDuration: totalDuration,
      maxParallelism: adjustedParallelism,
      priority,
      resourceRequirement: maxResourceIntensity
    };
  }

  getBrowserRecommendation(testName: string): string {
    const complexity = this.getTestComplexity(testName);
    if (!complexity) return 'chromium';

    const compatibility = complexity.browserCompatibility;
    
    // Find the browser with the best compatibility
    const browsers = Object.entries(compatibility) as [string, string][];
    browsers.sort((a, b) => {
      const scoreMap = { excellent: 4, good: 3, fair: 2, poor: 1 };
      return scoreMap[b[1] as keyof typeof scoreMap] - scoreMap[a[1] as keyof typeof scoreMap];
    });

    return browsers[0][0];
  }

  shouldSkipTest(testName: string, browser: string): boolean {
    const complexity = this.getTestComplexity(testName);
    if (!complexity) return false;

    const compatibility = complexity.browserCompatibility[browser as keyof typeof complexity.browserCompatibility];
    return compatibility === 'poor';
  }

  generateBatchingReport(batches: TestBatch[]): string {
    let report = '\n📦 Test Batching Strategy\n';
    report += '==========================\n\n';
    
    const totalTests = batches.reduce((sum, batch) => sum + batch.tests.length, 0);
    const totalEstimatedTime = batches.reduce((sum, batch) => sum + batch.estimatedDuration, 0);
    
    report += `Total Tests: ${totalTests}\n`;
    report += `Total Batches: ${batches.length}\n`;
    report += `Estimated Total Time: ${(totalEstimatedTime / 1000 / 60).toFixed(1)} minutes\n\n`;
    
    batches.forEach((batch, index) => {
      report += `Batch ${index + 1} (${batch.id}):\n`;
      report += `  Priority: ${batch.priority}\n`;
      report += `  Tests: ${batch.tests.length}\n`;
      report += `  Estimated Duration: ${(batch.estimatedDuration / 1000).toFixed(1)}s\n`;
      report += `  Max Parallelism: ${batch.maxParallelism}\n`;
      report += `  Resource Requirement: ${batch.resourceRequirement}\n`;
      report += `  Tests: ${batch.tests.join(', ')}\n\n`;
    });
    
    return report;
  }

  updateTestComplexity(testName: string, actualDuration: number, success: boolean): void {
    const complexity = this.getTestComplexity(testName);
    if (complexity) {
      // Update estimated duration based on actual performance
      const weight = 0.3; // How much to weight the new measurement
      complexity.estimatedDuration = Math.round(
        complexity.estimatedDuration * (1 - weight) + actualDuration * weight
      );
      
      this.testComplexities.set(testName, complexity);
    }
  }
}
