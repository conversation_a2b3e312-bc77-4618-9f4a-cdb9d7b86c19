import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';

test.describe('Application Loading', () => {
  test('should load the main application at root path', async ({ page }) => {
    const helpers = new TestHelpers(page);

    try {
      await helpers.navigateToPage('/');

      // Check that the page loads
      await expect(page).toHaveTitle(/Cabinet Insight Pro/i);

      // Check for main navigation/header (use more specific selector)
      await helpers.expectElementToBeVisible('header');

      // Check for main content area
      await helpers.expectElementToBeVisible('main, .min-h-screen');

      // Check for hero section or main content
      await expect(page.locator('h1, h2').first()).toBeVisible();
    } catch (error) {
      if (error instanceof Error && error.message.includes('Frontend server not available')) {
        test.skip(true, 'Frontend server not available');
      }
      throw error;
    }
  });

  test('should navigate to analysis page', async ({ page }) => {
    const helpers = new TestHelpers(page);

    try {
      await helpers.navigateToPage('/');

      // Look for analysis link/button
      const analysisLink = page.locator('a[href="/analysis"], button:has-text("Analysis"), a:has-text("Analysis")').first();

      if (await analysisLink.isVisible()) {
        await analysisLink.click();
        await helpers.waitForAppLoad();

        // Should be on analysis page
        expect(page.url()).toContain('/analysis');
      } else {
        // Navigate directly if no link found
        await helpers.navigateToPage('/analysis');
      }

      // Check for analysis-specific content (check for upload area or analysis content)
      const analysisContentVisible = await page.locator('[data-testid="analysis-dashboard"], .analysis, [class*="analysis"], input[type="file"]').first().isVisible().catch(() => false);
      expect(analysisContentVisible).toBeTruthy();
    } catch (error) {
      if (error instanceof Error && error.message.includes('Frontend server not available')) {
        test.skip(true, 'Frontend server not available');
      }
      throw error;
    }
  });

  test('should display upload section', async ({ page }) => {
    const helpers = new TestHelpers(page);
    
    await helpers.navigateToPage('/analysis');
    
    // Check for upload area
    const uploadSelectors = [
      '[data-testid="upload-area"]',
      '[class*="upload"]',
      'input[type="file"]'
    ];

    let uploadFound = false;
    for (const selector of uploadSelectors) {
      if (await page.locator(selector).first().isVisible().catch(() => false)) {
        uploadFound = true;
        break;
      }
    }

    expect(uploadFound).toBeTruthy();
    
    // Check for drag and drop text
    await expect(page.locator('text=/drag.*drop|drop.*file|upload/i').first()).toBeVisible();
  });

  test('should have responsive design', async ({ page }) => {
    const helpers = new TestHelpers(page);

    await helpers.navigateToPage('/');

    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 });
    await helpers.expectElementToBeVisible('h1'); // Check for main heading
    await helpers.expectElementToBeVisible('button:has-text("Start Free Analysis")'); // Check for main CTA

    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await helpers.expectElementToBeVisible('h1'); // Check for main heading
    await helpers.expectElementToBeVisible('button:has-text("Start Free Analysis")'); // Check for main CTA

    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await helpers.expectElementToBeVisible('h1'); // Check for main heading
    await helpers.expectElementToBeVisible('button:has-text("Start Free Analysis")'); // Check for main CTA
  });

  test('should handle 404 pages gracefully', async ({ page }) => {
    const helpers = new TestHelpers(page);
    
    await helpers.navigateToPage('/non-existent-page');
    
    // Should show 404 page or redirect
    const is404Text = await page.locator('text=/404/i').first().isVisible().catch(() => false);
    const isNotFoundText = await page.locator('text=/not found/i').first().isVisible().catch(() => false);
    const isPageNotFoundText = await page.locator('text=/page not found/i').first().isVisible().catch(() => false);
    const isRedirected = page.url().includes('/') && !page.url().includes('/non-existent-page');

    const is404 = is404Text || isNotFoundText || isPageNotFoundText;
    expect(is404 || isRedirected).toBeTruthy();
  });
});
