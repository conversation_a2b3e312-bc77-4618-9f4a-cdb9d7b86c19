import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

test.describe('File Upload UI', () => {
  const testPdfPath = path.join(__dirname, '../fixtures/kitchen-design-test.pdf');
  const testPngPath = path.join(__dirname, '../fixtures/kitchen-design-test.png');

  test.beforeEach(async ({ page }) => {
    const helpers = new TestHelpers(page);
    await helpers.navigateToPage('/analysis');
  });

  test('should display file upload interface', async ({ page }) => {
    const helpers = new TestHelpers(page);
    
    // Check for upload area
    await helpers.expectElementToBeVisible('input[type="file"], [data-testid="upload-area"]');
    
    // Check for upload instructions
    await expect(page.locator('text=/drag.*drop|browse|upload/i').first()).toBeVisible();
    
    // Check for file type information
    await expect(page.locator('text=/pdf|png|jpg|jpeg/i').first()).toBeVisible();
  });

  test('should upload PDF file via file input', async ({ page }) => {
    const helpers = new TestHelpers(page);
    
    // Find file input
    const fileInput = page.locator('input[type="file"]').first();
    await fileInput.setInputFiles(testPdfPath);
    
    // Check that file is selected
    await expect(page.locator('text=/kitchen-design-test\.pdf|selected|uploaded/i').first()).toBeVisible({ timeout: 10000 });
    
    // Check for analysis button or next step
    await expect(page.locator('button:has-text("Analyze"), button:has-text("Start"), [data-testid="analyze-button"]').first()).toBeVisible();
  });

  test('should upload PNG file via file input', async ({ page }) => {
    const helpers = new TestHelpers(page);
    
    const fileInput = page.locator('input[type="file"]').first();
    await fileInput.setInputFiles(testPngPath);
    
    // Check that file is selected
    await expect(page.locator('text=/kitchen-design-test\.png|selected|uploaded/i').first()).toBeVisible({ timeout: 10000 });
  });

  test('should show analysis configuration options', async ({ page }) => {
    const helpers = new TestHelpers(page);
    
    // Upload a file first
    const fileInput = page.locator('input[type="file"]').first();
    await fileInput.setInputFiles(testPdfPath);
    
    // Wait for file to be processed
    await page.waitForTimeout(2000);
    
    // Look for configuration options
    const configOptions = [
      'GPT-4o',
      'reasoning',
      'materials',
      'hardware',
      'multi-view'
    ];
    
    for (const option of configOptions) {
      // Check multiple selector types separately to avoid regex issues
      const textSelector = page.locator(`text=/${option}/i`).first();
      const testIdSelector = page.locator(`[data-testid*="${option}"]`).first();
      const inputSelector = page.locator(`input[name*="${option}"]`).first();

      const isVisible = await textSelector.isVisible().catch(() => false) ||
                       await testIdSelector.isVisible().catch(() => false) ||
                       await inputSelector.isVisible().catch(() => false);

      if (isVisible) {
        console.log(`Found configuration option: ${option}`);
      }
    }
  });

  test('should handle file size validation', async ({ page }) => {
    const helpers = new TestHelpers(page);
    
    // Try to upload the large test file
    const largePdfPath = path.join(__dirname, '../fixtures/kitchen-design-large.pdf');
    const fileInput = page.locator('input[type="file"]').first();
    
    await fileInput.setInputFiles(largePdfPath);
    
    // Should either accept it or show size warning
    await page.waitForTimeout(2000);
    
    // Check if there's any error message or if file was accepted
    const hasError = await page.locator('text=/too large|size limit|error/i').isVisible();
    const hasSuccess = await page.locator('text=/selected|uploaded|ready/i').isVisible();
    const hasFileInfo = await page.locator('text=/\.pdf|KB|MB/i').first().isVisible(); // Look for file info like "kitchen-design-large.pdf" or size info
    const hasRemoveButton = await page.locator('button:has-text("Remove")').isVisible(); // File uploaded if remove button is present

    expect(hasError || hasSuccess || hasFileInfo || hasRemoveButton).toBeTruthy();
  });

  test('should start analysis when analyze button is clicked', async ({ page }) => {
    const helpers = new TestHelpers(page);

    // Upload a file
    const fileInput = page.locator('input[type="file"]').first();
    await fileInput.setInputFiles(testPdfPath);

    // Wait for file to be processed
    await page.waitForTimeout(2000);

    // Find and click analyze button
    const analyzeButton = page.locator('button:has-text("Analyze"), button:has-text("Start"), [data-testid="analyze-button"]').first();

    if (await analyzeButton.isVisible()) {
      await analyzeButton.click();

      // Check for various indicators that analysis has started
      const analysisStarted = await Promise.race([
        // Look for progress indicators
        page.locator('text=/analyzing|processing|progress/i').first().isVisible({ timeout: 5000 }).catch(() => false),
        // Look for Results tab becoming active or enabled
        page.locator('tab[aria-selected="true"]:has-text("Results")').isVisible({ timeout: 5000 }).catch(() => false),
        // Look for disabled state change on Results tab
        page.locator('tab:has-text("Results"):not([disabled])').isVisible({ timeout: 5000 }).catch(() => false),
        // Look for any loading or spinner indicators
        page.locator('[data-testid*="loading"], .loading, .spinner').first().isVisible({ timeout: 5000 }).catch(() => false),
        // Look for URL change (navigation to results)
        page.waitForURL(/.*results.*|.*analysis.*/, { timeout: 5000 }).then(() => true).catch(() => false)
      ]);

      expect(analysisStarted).toBeTruthy();
    }
  });

  test('should display progress during analysis', async ({ page }) => {
    const helpers = new TestHelpers(page);
    
    // Upload and start analysis
    const fileInput = page.locator('input[type="file"]').first();
    await fileInput.setInputFiles(testPdfPath);
    await page.waitForTimeout(2000);
    
    const analyzeButton = page.locator('button:has-text("Analyze"), button:has-text("Start")').first();
    if (await analyzeButton.isVisible()) {
      await analyzeButton.click();
      
      // Check for progress indicators
      const progressIndicators = [
        '[data-testid="progress"]',
        '.progress',
        'progress',
        'text=/progress|%|analyzing/i'
      ];
      
      let foundProgress = false;
      for (const indicator of progressIndicators) {
        if (await page.locator(indicator).first().isVisible({ timeout: 5000 })) {
          foundProgress = true;
          break;
        }
      }
      
      if (foundProgress) {
        console.log('Progress indicator found during analysis');
      }
    }
  });
});
