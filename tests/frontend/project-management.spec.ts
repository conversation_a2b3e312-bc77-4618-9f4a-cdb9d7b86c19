import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';

test.describe('Project Management Dashboard', () => {
  let testHelpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    testHelpers = new TestHelpers(page);
    await testHelpers.setupTestEnvironment();
  });

  test.describe('Project Dashboard Page', () => {
    test('should display project dashboard correctly', async ({ page }) => {
      await testHelpers.navigateToPage('/projects');
      
      // Check for main dashboard elements
      await expect(page.locator('h1')).toContainText('Projects');
      
      // Check for project creation button
      const createButton = page.locator('button:has-text("Create"), button:has-text("New Project")');
      await expect(createButton.first()).toBeVisible();
      
      console.log('✅ Project dashboard displayed correctly');
    });

    test('should handle project creation flow', async ({ page }) => {
      await testHelpers.navigateToPage('/projects');
      
      // Click create project button
      const createButton = page.locator('button:has-text("Create"), button:has-text("New Project")');
      await createButton.first().click();
      
      // Wait for create project modal or form
      await page.waitForTimeout(1000);
      
      // Check for project creation form
      const nameInput = page.locator('input[name="name"], input[placeholder*="name" i]');
      const descriptionInput = page.locator('textarea[name="description"], input[name="description"]');
      
      if (await nameInput.count() > 0) {
        // Fill project details
        const timestamp = Date.now();
        await nameInput.fill(`Test Project ${timestamp}`);
        
        if (await descriptionInput.count() > 0) {
          await descriptionInput.fill('Test project description');
        }
        
        // Submit form
        const submitButton = page.locator('button[type="submit"], button:has-text("Create"), button:has-text("Save")');
        await submitButton.first().click();
        
        // Wait for response
        await page.waitForTimeout(2000);
        
        console.log('✅ Project creation flow completed');
      } else {
        console.log('ℹ️ Project creation form not found (may require authentication)');
      }
    });

    test('should display project list', async ({ page }) => {
      await testHelpers.navigateToPage('/projects');
      
      // Wait for projects to load
      await page.waitForTimeout(2000);
      
      // Check for project list or empty state
      const projectList = page.locator('[data-testid="project-list"], .project-list');
      const emptyState = page.locator('[data-testid="empty-projects"], .empty-state');
      const projectCards = page.locator('[data-testid="project-card"], .project-card');
      
      const hasProjects = await projectCards.count() > 0;
      const hasEmptyState = await emptyState.count() > 0;
      const hasProjectList = await projectList.count() > 0;
      
      expect(hasProjects || hasEmptyState || hasProjectList).toBeTruthy();
      
      if (hasProjects) {
        console.log(`✅ Found ${await projectCards.count()} projects`);
      } else {
        console.log('✅ Empty state or project list displayed');
      }
    });

    test('should handle project search and filtering', async ({ page }) => {
      await testHelpers.navigateToPage('/projects');
      
      // Look for search input
      const searchInput = page.locator('input[placeholder*="search" i], input[name="search"]');
      
      if (await searchInput.count() > 0) {
        // Test search functionality
        await searchInput.fill('test');
        await page.waitForTimeout(1000);
        
        // Check if search results are filtered
        console.log('✅ Search functionality available');
      } else {
        console.log('ℹ️ Search functionality not found');
      }
      
      // Look for filter options
      const filterButtons = page.locator('button:has-text("Filter"), select[name*="filter"]');
      
      if (await filterButtons.count() > 0) {
        console.log('✅ Filter functionality available');
      } else {
        console.log('ℹ️ Filter functionality not found');
      }
    });
  });

  test.describe('Project Details and Management', () => {
    test('should handle project selection and viewing', async ({ page }) => {
      await testHelpers.navigateToPage('/projects');
      
      // Wait for projects to load
      await page.waitForTimeout(2000);
      
      // Look for project cards or list items
      const projectCards = page.locator('[data-testid="project-card"], .project-card');
      const projectLinks = page.locator('a[href*="/project/"], a[href*="/projects/"]');
      
      if (await projectCards.count() > 0) {
        // Click on first project
        await projectCards.first().click();
        await page.waitForTimeout(1000);
        console.log('✅ Project card clicked');
      } else if (await projectLinks.count() > 0) {
        // Click on first project link
        await projectLinks.first().click();
        await page.waitForTimeout(1000);
        console.log('✅ Project link clicked');
      } else {
        console.log('ℹ️ No projects available to select');
      }
    });

    test('should display project settings and options', async ({ page }) => {
      await testHelpers.navigateToPage('/projects');
      
      // Look for project settings or options menu
      const settingsButton = page.locator('button:has-text("Settings"), [data-testid="project-settings"]');
      const optionsMenu = page.locator('button:has-text("Options"), [data-testid="project-options"]');
      const moreButton = page.locator('button:has-text("More"), button[aria-label*="more" i]');
      
      if (await settingsButton.count() > 0) {
        await settingsButton.first().click();
        await page.waitForTimeout(500);
        console.log('✅ Project settings accessible');
      } else if (await optionsMenu.count() > 0) {
        await optionsMenu.first().click();
        await page.waitForTimeout(500);
        console.log('✅ Project options menu accessible');
      } else if (await moreButton.count() > 0) {
        await moreButton.first().click();
        await page.waitForTimeout(500);
        console.log('✅ Project more menu accessible');
      } else {
        console.log('ℹ️ Project settings/options not found');
      }
    });

    test('should handle project sharing functionality', async ({ page }) => {
      await testHelpers.navigateToPage('/projects');
      
      // Look for share button
      const shareButton = page.locator('button:has-text("Share"), [data-testid="share-project"]');
      
      if (await shareButton.count() > 0) {
        await shareButton.first().click();
        await page.waitForTimeout(1000);
        
        // Check for share modal or form
        const shareModal = page.locator('[data-testid="share-modal"], .share-modal');
        const emailInput = page.locator('input[type="email"], input[placeholder*="email" i]');
        
        if (await shareModal.count() > 0 || await emailInput.count() > 0) {
          console.log('✅ Project sharing functionality available');
        }
      } else {
        console.log('ℹ️ Project sharing functionality not found');
      }
    });
  });

  test.describe('Collaboration Features', () => {
    test('should display collaboration tools', async ({ page }) => {
      await testHelpers.navigateToPage('/projects');
      
      // Look for collaboration indicators
      const collaboratorsList = page.locator('[data-testid="collaborators"], .collaborators');
      const commentsSection = page.locator('[data-testid="comments"], .comments');
      const activityFeed = page.locator('[data-testid="activity"], .activity');
      
      const hasCollaborators = await collaboratorsList.count() > 0;
      const hasComments = await commentsSection.count() > 0;
      const hasActivity = await activityFeed.count() > 0;
      
      if (hasCollaborators || hasComments || hasActivity) {
        console.log('✅ Collaboration tools available');
      } else {
        console.log('ℹ️ Collaboration tools not visible (may require project selection)');
      }
    });

    test('should handle comment creation', async ({ page }) => {
      await testHelpers.navigateToPage('/projects');
      
      // Look for comment input or add comment button
      const commentInput = page.locator('textarea[placeholder*="comment" i], input[placeholder*="comment" i]');
      const addCommentButton = page.locator('button:has-text("Add Comment"), button:has-text("Comment")');
      
      if (await commentInput.count() > 0) {
        // Test comment creation
        await commentInput.fill('Test comment for collaboration');
        
        // Look for submit button
        const submitButton = page.locator('button[type="submit"], button:has-text("Post"), button:has-text("Send")');
        if (await submitButton.count() > 0) {
          await submitButton.click();
          await page.waitForTimeout(1000);
          console.log('✅ Comment creation functionality available');
        }
      } else if (await addCommentButton.count() > 0) {
        await addCommentButton.first().click();
        await page.waitForTimeout(500);
        console.log('✅ Add comment functionality available');
      } else {
        console.log('ℹ️ Comment functionality not found');
      }
    });

    test('should display real-time collaboration status', async ({ page }) => {
      await testHelpers.navigateToPage('/projects');
      
      // Look for online users or presence indicators
      const onlineUsers = page.locator('[data-testid="online-users"], .online-users');
      const presenceIndicators = page.locator('.presence-indicator, [data-testid="presence"]');
      const collaborationStatus = page.locator('[data-testid="collaboration-status"]');
      
      const hasOnlineUsers = await onlineUsers.count() > 0;
      const hasPresence = await presenceIndicators.count() > 0;
      const hasStatus = await collaborationStatus.count() > 0;
      
      if (hasOnlineUsers || hasPresence || hasStatus) {
        console.log('✅ Real-time collaboration status available');
      } else {
        console.log('ℹ️ Real-time collaboration status not visible');
      }
    });
  });

  test.describe('Mobile Project Management', () => {
    test('should display mobile-optimized project dashboard', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      await testHelpers.navigateToPage('/projects');
      
      // Check mobile-specific elements
      await expect(page.locator('.mobile-viewport')).toBeVisible();
      
      // Check that project list is mobile-optimized
      const projectCards = page.locator('[data-testid="project-card"], .project-card');
      if (await projectCards.count() > 0) {
        const cardWidth = await projectCards.first().boundingBox();
        expect(cardWidth?.width).toBeLessThanOrEqual(375);
      }
      
      console.log('✅ Mobile project dashboard displayed correctly');
    });

    test('should handle mobile project creation', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      await testHelpers.navigateToPage('/projects');
      
      // Look for mobile-optimized create button
      const createButton = page.locator('button:has-text("Create"), button:has-text("New"), [data-testid="create-project"]');
      
      if (await createButton.count() > 0) {
        await createButton.first().click();
        await page.waitForTimeout(1000);
        
        // Check that modal or form is mobile-optimized
        const modal = page.locator('[role="dialog"], .modal');
        if (await modal.count() > 0) {
          const modalWidth = await modal.boundingBox();
          expect(modalWidth?.width).toBeLessThanOrEqual(375);
        }
        
        console.log('✅ Mobile project creation available');
      } else {
        console.log('ℹ️ Mobile project creation not found');
      }
    });
  });
});
