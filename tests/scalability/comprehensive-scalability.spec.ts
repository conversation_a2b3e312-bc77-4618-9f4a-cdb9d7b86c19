import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';

test.describe('Comprehensive Scalability Testing', () => {
  let testHelpers: TestHelpers;
  const API_BASE_URL = 'http://localhost:3001';

  test.beforeEach(async ({ page }) => {
    testHelpers = new TestHelpers(page);
    await testHelpers.setupTestEnvironment();
  });

  test.describe('Horizontal Scaling Infrastructure', () => {
    test('should handle multiple concurrent API requests', async ({ request }) => {
      console.log('🧪 Testing concurrent API request handling');

      const concurrentRequests = 10;
      const requests = [];

      // Create multiple concurrent health check requests
      for (let i = 0; i < concurrentRequests; i++) {
        requests.push(
          request.get(`${API_BASE_URL}/api/health`).then(response => ({
            index: i,
            status: response.status(),
            ok: response.ok(),
            responseTime: Date.now()
          }))
        );
      }

      const startTime = Date.now();
      const results = await Promise.all(requests);
      const totalTime = Date.now() - startTime;

      // Verify all requests succeeded
      const successfulRequests = results.filter(r => r.ok).length;
      const successRate = (successfulRequests / concurrentRequests) * 100;

      expect(successRate).toBeGreaterThanOrEqual(90); // 90% success rate minimum
      expect(totalTime).toBeLessThan(10000); // Should complete within 10 seconds

      console.log(`✅ Concurrent requests: ${successfulRequests}/${concurrentRequests} successful (${successRate}%)`);
      console.log(`⏱️ Total time: ${totalTime}ms`);
    });

    test('should maintain performance under load', async ({ request }) => {
      console.log('🧪 Testing performance under load');

      const loadTestRequests = 50;
      const batchSize = 10;
      const results = [];

      // Run requests in batches to simulate realistic load
      for (let batch = 0; batch < loadTestRequests / batchSize; batch++) {
        const batchRequests = [];
        const batchStartTime = Date.now();

        for (let i = 0; i < batchSize; i++) {
          batchRequests.push(
            request.get(`${API_BASE_URL}/api/health/detailed`).then(response => ({
              batchIndex: batch,
              requestIndex: i,
              status: response.status(),
              responseTime: Date.now() - batchStartTime
            }))
          );
        }

        const batchResults = await Promise.all(batchRequests);
        results.push(...batchResults);

        // Small delay between batches
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Analyze performance metrics
      const responseTimes = results.map(r => r.responseTime);
      const averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const maxResponseTime = Math.max(...responseTimes);
      const successfulRequests = results.filter(r => r.status === 200).length;

      expect(averageResponseTime).toBeLessThan(5000); // Average under 5 seconds
      expect(maxResponseTime).toBeLessThan(15000); // Max under 15 seconds
      expect(successfulRequests).toBeGreaterThanOrEqual(loadTestRequests * 0.9); // 90% success rate

      console.log(`📊 Load test results:`);
      console.log(`  - Average response time: ${averageResponseTime.toFixed(2)}ms`);
      console.log(`  - Max response time: ${maxResponseTime}ms`);
      console.log(`  - Success rate: ${(successfulRequests / loadTestRequests * 100).toFixed(1)}%`);
    });

    test('should handle Redis cluster operations', async ({ request }) => {
      console.log('🧪 Testing Redis cluster operations');

      // Test cache operations across cluster
      const cacheOperations = [
        { operation: 'set', key: 'test-key-1', value: 'test-value-1' },
        { operation: 'set', key: 'test-key-2', value: 'test-value-2' },
        { operation: 'get', key: 'test-key-1' },
        { operation: 'get', key: 'test-key-2' },
        { operation: 'delete', key: 'test-key-1' }
      ];

      for (const op of cacheOperations) {
        const response = await request.post(`${API_BASE_URL}/api/cache/operation`, {
          data: op
        });

        if (response.ok()) {
          const data = await response.json();
          expect(data.success).toBe(true);
          console.log(`✅ Cache ${op.operation} operation successful`);
        } else {
          console.log(`ℹ️ Cache operation endpoint not available`);
          break;
        }
      }
    });

    test('should validate load balancer configuration', async ({ request }) => {
      console.log('🧪 Testing load balancer configuration');

      // Test multiple requests to check if they're distributed
      const requests = 20;
      const serverResponses = new Map();

      for (let i = 0; i < requests; i++) {
        const response = await request.get(`${API_BASE_URL}/api/health/server-info`);
        
        if (response.ok()) {
          const data = await response.json();
          const serverId = data.data?.serverId || 'unknown';
          
          serverResponses.set(serverId, (serverResponses.get(serverId) || 0) + 1);
        }
      }

      const uniqueServers = serverResponses.size;
      console.log(`📊 Load distribution across ${uniqueServers} server(s):`);
      
      for (const [serverId, count] of serverResponses.entries()) {
        console.log(`  - Server ${serverId}: ${count} requests (${(count/requests*100).toFixed(1)}%)`);
      }

      // If load balancer is configured, we should see distribution
      if (uniqueServers > 1) {
        console.log('✅ Load balancer distributing requests across multiple servers');
      } else {
        console.log('ℹ️ Single server configuration detected');
      }
    });
  });

  test.describe('Database Optimization and Clustering', () => {
    test('should handle database connection pooling', async ({ request }) => {
      console.log('🧪 Testing database connection pooling');

      // Test multiple concurrent database operations
      const dbOperations = [];
      const operationCount = 15;

      for (let i = 0; i < operationCount; i++) {
        dbOperations.push(
          request.get(`${API_BASE_URL}/api/quotation/pricing/materials`).then(response => ({
            index: i,
            status: response.status(),
            responseTime: Date.now()
          }))
        );
      }

      const startTime = Date.now();
      const results = await Promise.all(dbOperations);
      const totalTime = Date.now() - startTime;

      const successfulOps = results.filter(r => r.status === 200).length;
      const successRate = (successfulOps / operationCount) * 100;

      expect(successRate).toBeGreaterThanOrEqual(90);
      expect(totalTime).toBeLessThan(20000); // Should complete within 20 seconds

      console.log(`✅ Database operations: ${successfulOps}/${operationCount} successful`);
      console.log(`⏱️ Total time: ${totalTime}ms`);
    });

    test('should validate read replica functionality', async ({ request }) => {
      console.log('🧪 Testing read replica functionality');

      // Test read operations that should use replicas
      const readOperations = [
        '/api/quotation/pricing/materials',
        '/api/collaboration/projects',
        '/api/performance/overview',
        '/api/health/detailed'
      ];

      const results = [];

      for (const endpoint of readOperations) {
        const response = await request.get(`${API_BASE_URL}${endpoint}`);
        
        results.push({
          endpoint,
          status: response.status(),
          ok: response.ok(),
          headers: response.headers()
        });
      }

      const successfulReads = results.filter(r => r.ok).length;
      const readSuccessRate = (successfulReads / readOperations.length) * 100;

      expect(readSuccessRate).toBeGreaterThanOrEqual(80);

      console.log(`✅ Read operations: ${successfulReads}/${readOperations.length} successful`);
      
      // Check for read replica indicators in headers
      const replicaIndicators = results.filter(r => 
        r.headers['x-read-replica'] || 
        r.headers['x-database-role'] === 'replica'
      ).length;

      if (replicaIndicators > 0) {
        console.log(`📊 ${replicaIndicators} operations used read replicas`);
      } else {
        console.log('ℹ️ Read replica headers not detected');
      }
    });

    test('should handle database failover scenarios', async ({ request }) => {
      console.log('🧪 Testing database failover resilience');

      // Test database operations with retry logic
      const criticalOperations = [
        '/api/health',
        '/api/quotation/pricing/materials'
      ];

      for (const endpoint of criticalOperations) {
        let attempts = 0;
        let success = false;
        const maxAttempts = 3;

        while (attempts < maxAttempts && !success) {
          attempts++;
          
          try {
            const response = await request.get(`${API_BASE_URL}${endpoint}`);
            
            if (response.ok()) {
              success = true;
              console.log(`✅ ${endpoint} succeeded on attempt ${attempts}`);
            } else {
              console.log(`⚠️ ${endpoint} failed on attempt ${attempts}: ${response.status()}`);
            }
          } catch (error) {
            console.log(`❌ ${endpoint} error on attempt ${attempts}: ${error}`);
          }

          if (!success && attempts < maxAttempts) {
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        expect(success).toBe(true);
      }
    });
  });

  test.describe('WebSocket Scaling', () => {
    test('should handle multiple WebSocket connections', async ({ browser }) => {
      console.log('🧪 Testing multiple WebSocket connections');

      const connectionCount = 5;
      const connections = [];

      // Create multiple browser contexts for concurrent connections
      for (let i = 0; i < connectionCount; i++) {
        const context = await browser.newContext();
        const page = await context.newPage();
        const helpers = new TestHelpers(page);
        
        connections.push({ context, page, helpers, id: i });
      }

      // Connect all WebSocket clients
      const connectionResults = [];
      
      for (const conn of connections) {
        try {
          await conn.helpers.navigateToPage('/');
          const connected = await conn.helpers.waitForWebSocketConnection(2);
          
          connectionResults.push({
            id: conn.id,
            connected,
            timestamp: Date.now()
          });
          
          console.log(`${connected ? '✅' : '❌'} WebSocket connection ${conn.id}: ${connected ? 'SUCCESS' : 'FAILED'}`);
        } catch (error) {
          connectionResults.push({
            id: conn.id,
            connected: false,
            error: error.message
          });
        }
      }

      // Cleanup connections
      for (const conn of connections) {
        await conn.context.close();
      }

      const successfulConnections = connectionResults.filter(r => r.connected).length;
      const connectionSuccessRate = (successfulConnections / connectionCount) * 100;

      expect(connectionSuccessRate).toBeGreaterThanOrEqual(80);

      console.log(`📊 WebSocket connections: ${successfulConnections}/${connectionCount} successful (${connectionSuccessRate}%)`);
    });

    test('should handle WebSocket message broadcasting', async ({ page }) => {
      await testHelpers.navigateToPage('/');
      
      const wsConnected = await testHelpers.waitForWebSocketConnection();
      
      if (wsConnected) {
        // Test message broadcasting
        const messageTest = await page.evaluate(() => {
          return new Promise((resolve) => {
            const messages: any[] = [];
            
            if (window.io && window.io.connected) {
              // Listen for broadcast messages
              window.io.on('broadcast-test', (data: any) => {
                messages.push(data);
              });
              
              // Send a test message
              window.io.emit('test-broadcast', { 
                message: 'Test broadcast message',
                timestamp: Date.now()
              });
              
              // Resolve after 3 seconds
              setTimeout(() => resolve(messages), 3000);
            } else {
              resolve([]);
            }
          });
        });
        
        console.log(`📡 Broadcast messages received: ${(messageTest as any[]).length}`);
      } else {
        console.log('ℹ️ WebSocket connection not established for broadcast test');
      }
    });

    test('should maintain WebSocket performance under load', async ({ page }) => {
      await testHelpers.navigateToPage('/');
      
      const wsConnected = await testHelpers.waitForWebSocketConnection();
      
      if (wsConnected) {
        // Test WebSocket performance with multiple messages
        const performanceTest = await page.evaluate(() => {
          return new Promise((resolve) => {
            const messageCount = 50;
            const sentMessages = [];
            const receivedMessages = [];
            const startTime = Date.now();
            
            if (window.io && window.io.connected) {
              // Listen for echo responses
              window.io.on('echo-response', (data: any) => {
                receivedMessages.push({
                  ...data,
                  receivedAt: Date.now()
                });
                
                if (receivedMessages.length >= messageCount) {
                  const endTime = Date.now();
                  resolve({
                    sent: sentMessages.length,
                    received: receivedMessages.length,
                    totalTime: endTime - startTime,
                    averageLatency: receivedMessages.reduce((sum, msg) => 
                      sum + (msg.receivedAt - msg.sentAt), 0) / receivedMessages.length
                  });
                }
              });
              
              // Send multiple messages
              for (let i = 0; i < messageCount; i++) {
                const message = {
                  id: i,
                  content: `Test message ${i}`,
                  sentAt: Date.now()
                };
                
                sentMessages.push(message);
                window.io.emit('echo-test', message);
              }
              
              // Timeout after 30 seconds
              setTimeout(() => resolve({
                sent: sentMessages.length,
                received: receivedMessages.length,
                totalTime: Date.now() - startTime,
                timeout: true
              }), 30000);
            } else {
              resolve({ error: 'WebSocket not connected' });
            }
          });
        });
        
        const results = performanceTest as any;
        
        if (results.error) {
          console.log(`❌ WebSocket performance test failed: ${results.error}`);
        } else {
          console.log(`📊 WebSocket performance results:`);
          console.log(`  - Messages sent: ${results.sent}`);
          console.log(`  - Messages received: ${results.received}`);
          console.log(`  - Total time: ${results.totalTime}ms`);
          
          if (results.averageLatency) {
            console.log(`  - Average latency: ${results.averageLatency.toFixed(2)}ms`);
          }
          
          if (results.timeout) {
            console.log(`  - ⚠️ Test timed out`);
          }
          
          // Expect at least 80% message delivery
          const deliveryRate = (results.received / results.sent) * 100;
          expect(deliveryRate).toBeGreaterThanOrEqual(80);
        }
      }
    });
  });

  test.describe('System Resource Monitoring', () => {
    test('should monitor system resource usage', async ({ request }) => {
      console.log('🧪 Testing system resource monitoring');

      const response = await request.get(`${API_BASE_URL}/api/health/system-resources`);
      
      if (response.ok()) {
        const data = await response.json();
        
        expect(data.success).toBe(true);
        expect(data.data.memory).toBeDefined();
        expect(data.data.cpu).toBeDefined();
        expect(data.data.disk).toBeDefined();
        
        console.log(`📊 System resources:`);
        console.log(`  - Memory usage: ${data.data.memory.usedPercentage}%`);
        console.log(`  - CPU usage: ${data.data.cpu.usagePercentage}%`);
        console.log(`  - Disk usage: ${data.data.disk.usedPercentage}%`);
        
        // Verify resources are within acceptable limits
        expect(data.data.memory.usedPercentage).toBeLessThan(90);
        expect(data.data.cpu.usagePercentage).toBeLessThan(80);
        expect(data.data.disk.usedPercentage).toBeLessThan(85);
      } else {
        console.log('ℹ️ System resource monitoring endpoint not available');
      }
    });

    test('should handle resource threshold alerts', async ({ request }) => {
      console.log('🧪 Testing resource threshold alerts');

      const response = await request.get(`${API_BASE_URL}/api/health/resource-alerts`);
      
      if (response.ok()) {
        const data = await response.json();
        
        expect(data.success).toBe(true);
        expect(data.data.alerts).toBeInstanceOf(Array);
        
        const activeAlerts = data.data.alerts.filter((alert: any) => alert.active);
        
        console.log(`🚨 Active resource alerts: ${activeAlerts.length}`);
        
        activeAlerts.forEach((alert: any) => {
          console.log(`  - ${alert.type}: ${alert.message} (${alert.severity})`);
        });
        
        // System should not have critical alerts under normal load
        const criticalAlerts = activeAlerts.filter((alert: any) => alert.severity === 'critical');
        expect(criticalAlerts.length).toBe(0);
      } else {
        console.log('ℹ️ Resource alerts endpoint not available');
      }
    });
  });
});
