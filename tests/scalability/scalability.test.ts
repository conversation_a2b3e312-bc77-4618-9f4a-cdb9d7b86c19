import { test, expect } from '@playwright/test';

/**
 * Cabinet Insight Pro - Scalability Test Suite
 * 
 * Tests the scalability infrastructure components:
 * - Redis cluster functionality
 * - Enhanced caching service
 * - Advanced OpenAI service with queuing
 * - Load balancing and clustering
 * - Performance under concurrent load
 */

test.describe('Scalability Infrastructure Tests', () => {
  
  test.describe('Redis Cluster Tests', () => {
    test('should connect to Redis cluster successfully', async ({ page }) => {
      const response = await page.request.get('/api/performance/cache');
      expect(response.status()).toBe(200);
      
      const cacheStats = await response.json();
      expect(cacheStats.connected).toBe(true);
      expect(cacheStats.redisInfo).toBeDefined();
    });

    test('should distribute cache across cluster nodes', async ({ page }) => {
      // Test cache distribution by setting multiple keys
      const testKeys = ['test1', 'test2', 'test3', 'test4', 'test5'];
      
      for (const key of testKeys) {
        const response = await page.request.post('/api/cache/set', {
          data: { key, value: `test-value-${key}`, ttl: 300 }
        });
        expect(response.status()).toBe(200);
      }

      // Verify all keys can be retrieved
      for (const key of testKeys) {
        const response = await page.request.get(`/api/cache/get/${key}`);
        expect(response.status()).toBe(200);
        
        const result = await response.json();
        expect(result.value).toBe(`test-value-${key}`);
      }
    });

    test('should handle cache failover gracefully', async ({ page }) => {
      // Set a test value
      await page.request.post('/api/cache/set', {
        data: { key: 'failover-test', value: 'failover-value', ttl: 600 }
      });

      // Verify cache statistics show healthy cluster
      const response = await page.request.get('/api/performance/cache');
      const stats = await response.json();
      
      expect(stats.connected).toBe(true);
      expect(stats.hitRate).toBeGreaterThanOrEqual(0);
    });
  });

  test.describe('Enhanced Caching Service Tests', () => {
    test('should achieve target cache hit rate', async ({ page }) => {
      // Warm up cache with repeated requests
      const testPrompt = 'Analyze this kitchen layout for cabinet optimization';
      
      for (let i = 0; i < 5; i++) {
        await page.request.post('/api/analysis/quick', {
          data: { prompt: testPrompt, model: 'gpt-4o' }
        });
      }

      // Check cache statistics
      const response = await page.request.get('/api/performance/cache');
      const stats = await response.json();
      
      expect(stats.hitRate).toBeGreaterThan(0.6); // Target 60%+ hit rate
    });

    test('should implement L1/L2 caching strategy', async ({ page }) => {
      const response = await page.request.get('/api/performance/cache');
      const stats = await response.json();
      
      expect(stats.localCacheSize).toBeGreaterThanOrEqual(0);
      expect(stats.memoryUsage).toBeGreaterThanOrEqual(0);
      expect(stats.totalHits).toBeGreaterThanOrEqual(0);
    });

    test('should handle cache invalidation correctly', async ({ page }) => {
      // Set test data
      await page.request.post('/api/cache/set', {
        data: { key: 'invalidation-test', value: 'original-value', ttl: 300 }
      });

      // Invalidate cache pattern
      const invalidateResponse = await page.request.post('/api/cache/invalidate', {
        data: { pattern: 'invalidation-*' }
      });
      expect(invalidateResponse.status()).toBe(200);

      // Verify key is no longer in cache
      const getResponse = await page.request.get('/api/cache/get/invalidation-test');
      const result = await getResponse.json();
      expect(result.value).toBeNull();
    });
  });

  test.describe('Advanced OpenAI Service Tests', () => {
    test('should queue requests efficiently', async ({ page }) => {
      // Submit multiple concurrent requests
      const requests = [];
      for (let i = 0; i < 10; i++) {
        requests.push(
          page.request.post('/api/analysis/queue', {
            data: {
              prompt: `Test analysis request ${i}`,
              model: 'gpt-4o',
              priority: i % 3 // Vary priority
            }
          })
        );
      }

      const responses = await Promise.all(requests);
      
      // All requests should be accepted
      responses.forEach(response => {
        expect(response.status()).toBe(202); // Accepted for processing
      });

      // Check queue statistics
      const queueStats = await page.request.get('/api/performance/queue');
      const stats = await queueStats.json();
      
      expect(stats.waiting + stats.active + stats.completed).toBeGreaterThanOrEqual(10);
    });

    test('should respect rate limits', async ({ page }) => {
      const response = await page.request.get('/api/performance/queue');
      const stats = await response.json();
      
      expect(stats.rateLimitState).toBeDefined();
      expect(stats.rateLimitState.requestsPerMinute).toBeGreaterThan(0);
      expect(stats.rateLimitState.currentRequests).toBeGreaterThanOrEqual(0);
    });

    test('should achieve target API call reduction', async ({ page }) => {
      const response = await page.request.get('/api/performance/overview');
      const stats = await response.json();
      
      // Target 60-80% API call reduction through caching
      if (stats.cacheHitRate > 0) {
        expect(stats.cacheHitRate).toBeGreaterThan(0.6);
        expect(stats.estimatedCostSavings).toBeGreaterThan(0.4);
      }
    });
  });

  test.describe('Load Balancing Tests', () => {
    test('should distribute requests across multiple instances', async ({ page }) => {
      // Make multiple requests to test load distribution
      const responses = [];
      for (let i = 0; i < 20; i++) {
        const response = await page.request.get('/api/health');
        responses.push(response);
      }

      // All requests should succeed
      responses.forEach(response => {
        expect(response.status()).toBe(200);
      });

      // Check if requests are being distributed (via server headers if available)
      const serverHeaders = responses.map(r => r.headers()['x-server-id']).filter(Boolean);
      if (serverHeaders.length > 0) {
        const uniqueServers = new Set(serverHeaders);
        expect(uniqueServers.size).toBeGreaterThan(1); // Multiple servers handling requests
      }
    });

    test('should handle server failures gracefully', async ({ page }) => {
      // Test health endpoint multiple times to ensure availability
      for (let i = 0; i < 10; i++) {
        const response = await page.request.get('/api/health');
        expect(response.status()).toBe(200);
        
        const health = await response.json();
        expect(health.status).toBe('healthy');
      }
    });
  });

  test.describe('Performance Under Load Tests', () => {
    test('should maintain response times under concurrent load', async ({ page }) => {
      const startTime = Date.now();
      
      // Simulate concurrent requests
      const concurrentRequests = [];
      for (let i = 0; i < 50; i++) {
        concurrentRequests.push(
          page.request.get('/api/performance/overview')
        );
      }

      const responses = await Promise.all(concurrentRequests);
      const endTime = Date.now();
      
      // All requests should succeed
      responses.forEach(response => {
        expect(response.status()).toBe(200);
      });

      // Average response time should be reasonable
      const averageResponseTime = (endTime - startTime) / responses.length;
      expect(averageResponseTime).toBeLessThan(5000); // Less than 5 seconds average
    });

    test('should scale WebSocket connections', async ({ page }) => {
      // Test WebSocket connection establishment
      const wsResponse = await page.request.get('/api/websocket/status');
      expect(wsResponse.status()).toBe(200);
      
      const wsStats = await wsResponse.json();
      expect(wsStats.connected).toBeDefined();
      expect(wsStats.totalConnections).toBeGreaterThanOrEqual(0);
    });

    test('should maintain 91.7% success rate under load', async ({ page }) => {
      const testRequests = 100;
      const requests = [];
      
      // Generate mixed workload
      for (let i = 0; i < testRequests; i++) {
        if (i % 3 === 0) {
          requests.push(page.request.get('/api/health'));
        } else if (i % 3 === 1) {
          requests.push(page.request.get('/api/performance/overview'));
        } else {
          requests.push(page.request.get('/api/performance/cache'));
        }
      }

      const responses = await Promise.all(requests);
      
      // Calculate success rate
      const successfulResponses = responses.filter(r => r.status() >= 200 && r.status() < 300);
      const successRate = (successfulResponses.length / testRequests) * 100;
      
      expect(successRate).toBeGreaterThanOrEqual(91.7); // Target success rate
    });
  });

  test.describe('Database Scalability Tests', () => {
    test('should handle concurrent database operations', async ({ page }) => {
      // Test concurrent read operations
      const readRequests = [];
      for (let i = 0; i < 20; i++) {
        readRequests.push(
          page.request.get('/api/analyses?limit=10')
        );
      }

      const responses = await Promise.all(readRequests);
      
      responses.forEach(response => {
        expect(response.status()).toBe(200);
      });
    });

    test('should utilize read replicas for queries', async ({ page }) => {
      const response = await page.request.get('/api/performance/database');
      expect(response.status()).toBe(200);
      
      const dbStats = await response.json();
      expect(dbStats.primaryConnections).toBeDefined();
      expect(dbStats.replicaConnections).toBeDefined();
    });
  });

  test.describe('Monitoring and Metrics Tests', () => {
    test('should provide comprehensive performance metrics', async ({ page }) => {
      const response = await page.request.get('/api/performance/overview');
      expect(response.status()).toBe(200);
      
      const metrics = await response.json();
      
      // Verify key metrics are present
      expect(metrics.cacheHitRate).toBeDefined();
      expect(metrics.averageResponseTime).toBeDefined();
      expect(metrics.totalRequests).toBeDefined();
      expect(metrics.errorRate).toBeDefined();
      expect(metrics.queueDepth).toBeDefined();
    });

    test('should track resource utilization', async ({ page }) => {
      const response = await page.request.get('/api/performance/system');
      expect(response.status()).toBe(200);
      
      const systemStats = await response.json();
      
      expect(systemStats.memoryUsage).toBeDefined();
      expect(systemStats.cpuUsage).toBeDefined();
      expect(systemStats.activeConnections).toBeDefined();
    });

    test('should provide real-time metrics updates', async ({ page }) => {
      // Get initial metrics
      const initialResponse = await page.request.get('/api/performance/overview');
      const initialMetrics = await initialResponse.json();
      
      // Wait and get updated metrics
      await page.waitForTimeout(2000);
      
      const updatedResponse = await page.request.get('/api/performance/overview');
      const updatedMetrics = await updatedResponse.json();
      
      // Metrics should be updated (timestamps should differ)
      expect(updatedMetrics.timestamp).not.toBe(initialMetrics.timestamp);
    });
  });
});

test.describe('Scalability Integration Tests', () => {
  test('should handle end-to-end analysis workflow under load', async ({ page }) => {
    // Test complete analysis workflow with scalability features
    const analysisRequests = [];
    
    for (let i = 0; i < 10; i++) {
      analysisRequests.push(
        page.request.post('/api/analysis/upload', {
          multipart: {
            file: {
              name: `test-kitchen-${i}.pdf`,
              mimeType: 'application/pdf',
              buffer: Buffer.from('mock pdf content')
            },
            analysisType: 'standard'
          }
        })
      );
    }

    const responses = await Promise.all(analysisRequests);
    
    // All uploads should be accepted
    responses.forEach(response => {
      expect(response.status()).toBe(202);
    });

    // Verify queue is processing requests
    const queueStats = await page.request.get('/api/performance/queue');
    const stats = await queueStats.json();
    
    expect(stats.waiting + stats.active + stats.completed).toBeGreaterThanOrEqual(10);
  });

  test('should maintain data consistency across cluster', async ({ page }) => {
    // Test data consistency by creating and retrieving data
    const testData = {
      name: 'Scalability Test Project',
      description: 'Testing data consistency across cluster'
    };

    const createResponse = await page.request.post('/api/projects', {
      data: testData
    });
    expect(createResponse.status()).toBe(201);
    
    const project = await createResponse.json();
    
    // Retrieve from different endpoints to test consistency
    const getResponse = await page.request.get(`/api/projects/${project.id}`);
    expect(getResponse.status()).toBe(200);
    
    const retrievedProject = await getResponse.json();
    expect(retrievedProject.name).toBe(testData.name);
  });
});
