import { test, expect } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';

test.describe('Progressive Web App Features', () => {
  let testHelpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    testHelpers = new TestHelpers(page);
    await testHelpers.setupTestEnvironment();
  });

  test.describe('PWA Installation', () => {
    test('should have valid web app manifest', async ({ page }) => {
      await testHelpers.navigateToPage('/');
      
      // Check for manifest link
      const manifestLink = await page.locator('link[rel="manifest"]').getAttribute('href');
      expect(manifestLink).toBeTruthy();
      
      // Fetch and validate manifest
      const manifestResponse = await page.request.get(`http://localhost:8080${manifestLink}`);
      expect(manifestResponse.ok()).toBeTruthy();
      
      const manifest = await manifestResponse.json();
      expect(manifest.name).toBeDefined();
      expect(manifest.short_name).toBeDefined();
      expect(manifest.start_url).toBeDefined();
      expect(manifest.display).toBeDefined();
      expect(manifest.icons).toBeInstanceOf(Array);
      expect(manifest.icons.length).toBeGreaterThan(0);
      
      console.log(`✅ Valid PWA manifest found: ${manifest.name}`);
    });

    test('should display PWA install prompt', async ({ page }) => {
      // Set mobile viewport to trigger PWA behavior
      await page.setViewportSize({ width: 375, height: 667 });
      await testHelpers.navigateToPage('/');
      
      // Wait for PWA install prompt to appear
      await page.waitForTimeout(2000);
      
      // Look for install prompt elements
      const installPrompt = page.locator('[data-testid="pwa-install-prompt"], .pwa-install-prompt');
      const installButton = page.locator('button:has-text("Install"), button:has-text("Add to Home")');
      
      const hasInstallPrompt = await installPrompt.count() > 0;
      const hasInstallButton = await installButton.count() > 0;
      
      if (hasInstallPrompt || hasInstallButton) {
        console.log('✅ PWA install prompt displayed');
        
        // Test install button interaction
        if (hasInstallButton) {
          await installButton.first().click();
          await page.waitForTimeout(1000);
          console.log('✅ Install button interaction working');
        }
      } else {
        console.log('ℹ️ PWA install prompt not shown (may require HTTPS or specific conditions)');
      }
    });

    test('should handle PWA install banner dismissal', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await testHelpers.navigateToPage('/');
      
      // Look for dismiss button
      const dismissButton = page.locator('button:has-text("Dismiss"), button:has-text("Close"), [data-testid="dismiss-install"]');
      
      if (await dismissButton.count() > 0) {
        await dismissButton.first().click();
        await page.waitForTimeout(500);
        
        // Check that prompt is hidden
        const installPrompt = page.locator('[data-testid="pwa-install-prompt"], .pwa-install-prompt');
        await expect(installPrompt).toBeHidden();
        
        console.log('✅ PWA install prompt dismissal working');
      } else {
        console.log('ℹ️ PWA install prompt dismiss button not found');
      }
    });
  });

  test.describe('Service Worker and Offline Functionality', () => {
    test('should register service worker', async ({ page }) => {
      await testHelpers.navigateToPage('/');
      
      // Check for service worker registration
      const swRegistered = await page.evaluate(async () => {
        if ('serviceWorker' in navigator) {
          try {
            const registration = await navigator.serviceWorker.getRegistration();
            return !!registration;
          } catch (error) {
            return false;
          }
        }
        return false;
      });
      
      if (swRegistered) {
        console.log('✅ Service worker registered successfully');
      } else {
        console.log('ℹ️ Service worker not registered (may require HTTPS)');
      }
    });

    test('should handle offline mode gracefully', async ({ page }) => {
      await testHelpers.navigateToPage('/');
      
      // Simulate offline mode
      await page.context().setOffline(true);
      
      // Try to navigate to a cached page
      await page.reload();
      await page.waitForTimeout(2000);
      
      // Check if page loads or shows offline message
      const pageTitle = await page.title();
      const offlineMessage = page.locator('text=/offline|no connection|network error/i');
      
      const hasOfflineMessage = await offlineMessage.count() > 0;
      const pageLoaded = pageTitle.length > 0;
      
      if (pageLoaded || hasOfflineMessage) {
        console.log('✅ Offline mode handled gracefully');
      } else {
        console.log('ℹ️ Offline handling not implemented');
      }
      
      // Restore online mode
      await page.context().setOffline(false);
    });

    test('should cache critical resources', async ({ page }) => {
      await testHelpers.navigateToPage('/');
      
      // Check for cached resources
      const cachedResources = await page.evaluate(async () => {
        if ('caches' in window) {
          try {
            const cacheNames = await caches.keys();
            let totalCached = 0;
            
            for (const cacheName of cacheNames) {
              const cache = await caches.open(cacheName);
              const keys = await cache.keys();
              totalCached += keys.length;
            }
            
            return { cacheNames, totalCached };
          } catch (error) {
            return { cacheNames: [], totalCached: 0 };
          }
        }
        return { cacheNames: [], totalCached: 0 };
      });
      
      if (cachedResources.totalCached > 0) {
        console.log(`✅ ${cachedResources.totalCached} resources cached across ${cachedResources.cacheNames.length} caches`);
      } else {
        console.log('ℹ️ No cached resources found');
      }
    });
  });

  test.describe('Mobile Performance Optimization', () => {
    test('should meet mobile performance targets', async ({ page }) => {
      // Set mobile viewport and simulate 3G network
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Simulate slower network
      await page.route('**/*', async (route) => {
        await new Promise(resolve => setTimeout(resolve, 100)); // Add 100ms delay
        await route.continue();
      });
      
      const startTime = Date.now();
      await testHelpers.navigateToPage('/');
      
      // Wait for First Contentful Paint
      await page.waitForSelector('h1, .hero-section, header', { timeout: 5000 });
      const fcpTime = Date.now() - startTime;
      
      // Should load within 3 seconds on simulated 3G
      expect(fcpTime).toBeLessThan(3000);
      
      console.log(`✅ Mobile FCP: ${fcpTime}ms (target: <3000ms)`);
    });

    test('should optimize images for mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await testHelpers.navigateToPage('/');
      
      // Check for responsive images
      const images = page.locator('img');
      const imageCount = await images.count();
      
      if (imageCount > 0) {
        // Check first few images for optimization
        for (let i = 0; i < Math.min(3, imageCount); i++) {
          const img = images.nth(i);
          const src = await img.getAttribute('src');
          const srcset = await img.getAttribute('srcset');
          const loading = await img.getAttribute('loading');
          
          if (srcset) {
            console.log(`✅ Image ${i + 1} has responsive srcset`);
          }
          
          if (loading === 'lazy') {
            console.log(`✅ Image ${i + 1} has lazy loading`);
          }
          
          if (src && (src.includes('.webp') || src.includes('w_375') || src.includes('mobile'))) {
            console.log(`✅ Image ${i + 1} appears optimized for mobile`);
          }
        }
      } else {
        console.log('ℹ️ No images found to check optimization');
      }
    });

    test('should minimize bundle size for mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Monitor network requests
      const responses: any[] = [];
      page.on('response', (response) => {
        if (response.url().includes('.js') || response.url().includes('.css')) {
          responses.push({
            url: response.url(),
            size: response.headers()['content-length'],
            status: response.status()
          });
        }
      });
      
      await testHelpers.navigateToPage('/');
      await page.waitForLoadState('networkidle');
      
      // Calculate total bundle size
      let totalSize = 0;
      responses.forEach(response => {
        if (response.size) {
          totalSize += parseInt(response.size);
        }
      });
      
      if (totalSize > 0) {
        console.log(`📦 Total bundle size: ${(totalSize / 1024).toFixed(2)} KB`);
        
        // Mobile bundle should be reasonable (under 1MB for initial load)
        expect(totalSize).toBeLessThan(1024 * 1024);
      } else {
        console.log('ℹ️ Bundle size information not available');
      }
    });
  });

  test.describe('Mobile Touch Interactions', () => {
    test('should handle touch gestures for 3D visualization', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await testHelpers.navigateToPage('/');
      
      // Look for 3D visualization elements
      const canvas3D = page.locator('canvas, [data-testid="3d-viewer"]');
      
      if (await canvas3D.count() > 0) {
        const canvas = canvas3D.first();
        
        // Test touch interactions
        const boundingBox = await canvas.boundingBox();
        if (boundingBox) {
          // Simulate pinch gesture
          await page.touchscreen.tap(boundingBox.x + 100, boundingBox.y + 100);
          await page.waitForTimeout(500);
          
          // Simulate swipe gesture
          await page.touchscreen.tap(boundingBox.x + 50, boundingBox.y + 50);
          await page.touchscreen.tap(boundingBox.x + 150, boundingBox.y + 50);
          
          console.log('✅ Touch gestures for 3D visualization tested');
        }
      } else {
        console.log('ℹ️ 3D visualization not found for touch testing');
      }
    });

    test('should handle mobile navigation gestures', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await testHelpers.navigateToPage('/');
      
      // Test mobile menu if present
      const mobileMenuButton = page.locator('button[aria-label*="menu" i], .mobile-menu-button, [data-testid="mobile-menu"]');
      
      if (await mobileMenuButton.count() > 0) {
        await mobileMenuButton.tap();
        await page.waitForTimeout(500);
        
        // Check if menu opened
        const mobileMenu = page.locator('.mobile-menu, [data-testid="mobile-menu-content"]');
        if (await mobileMenu.count() > 0) {
          console.log('✅ Mobile menu touch interaction working');
        }
      } else {
        console.log('ℹ️ Mobile menu not found');
      }
      
      // Test swipe navigation if implemented
      const swipeArea = page.locator('.swipe-area, [data-testid="swipe-navigation"]');
      if (await swipeArea.count() > 0) {
        const boundingBox = await swipeArea.boundingBox();
        if (boundingBox) {
          // Simulate swipe left
          await page.touchscreen.tap(boundingBox.x + boundingBox.width - 50, boundingBox.y + 50);
          await page.touchscreen.tap(boundingBox.x + 50, boundingBox.y + 50);
          
          console.log('✅ Swipe navigation tested');
        }
      }
    });

    test('should optimize form inputs for mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await testHelpers.navigateToPage('/');
      
      // Look for form inputs
      const inputs = page.locator('input, textarea, select');
      const inputCount = await inputs.count();
      
      if (inputCount > 0) {
        // Check first few inputs for mobile optimization
        for (let i = 0; i < Math.min(3, inputCount); i++) {
          const input = inputs.nth(i);
          const type = await input.getAttribute('type');
          const inputmode = await input.getAttribute('inputmode');
          const autocomplete = await input.getAttribute('autocomplete');
          
          if (type === 'email' || inputmode === 'email') {
            console.log(`✅ Input ${i + 1} optimized for email entry`);
          }
          
          if (type === 'tel' || inputmode === 'tel') {
            console.log(`✅ Input ${i + 1} optimized for phone entry`);
          }
          
          if (autocomplete) {
            console.log(`✅ Input ${i + 1} has autocomplete: ${autocomplete}`);
          }
        }
      } else {
        console.log('ℹ️ No form inputs found to check mobile optimization');
      }
    });
  });

  test.describe('Mobile Accessibility', () => {
    test('should meet mobile accessibility standards', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await testHelpers.navigateToPage('/');

      // Check for proper heading structure
      const headings = page.locator('h1, h2, h3, h4, h5, h6');
      const headingCount = await headings.count();

      if (headingCount > 0) {
        console.log(`✅ Found ${headingCount} headings for screen readers`);
      }

      // Check for alt text on images
      const images = page.locator('img');
      const imageCount = await images.count();
      let imagesWithAlt = 0;

      for (let i = 0; i < imageCount; i++) {
        const alt = await images.nth(i).getAttribute('alt');
        if (alt && alt.trim().length > 0) {
          imagesWithAlt++;
        }
      }

      if (imageCount > 0) {
        const altPercentage = (imagesWithAlt / imageCount) * 100;
        console.log(`✅ ${altPercentage.toFixed(1)}% of images have alt text`);
      }

      // Check for proper button labels
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();
      let buttonsWithLabels = 0;

      for (let i = 0; i < buttonCount; i++) {
        const button = buttons.nth(i);
        const text = await button.textContent();
        const ariaLabel = await button.getAttribute('aria-label');

        if ((text && text.trim().length > 0) || (ariaLabel && ariaLabel.trim().length > 0)) {
          buttonsWithLabels++;
        }
      }

      if (buttonCount > 0) {
        const labelPercentage = (buttonsWithLabels / buttonCount) * 100;
        console.log(`✅ ${labelPercentage.toFixed(1)}% of buttons have proper labels`);
      }
    });

    test('should support mobile screen readers', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await testHelpers.navigateToPage('/');

      // Check for ARIA landmarks
      const landmarks = page.locator('[role="main"], [role="navigation"], [role="banner"], [role="contentinfo"]');
      const landmarkCount = await landmarks.count();

      if (landmarkCount > 0) {
        console.log(`✅ Found ${landmarkCount} ARIA landmarks`);
      }

      // Check for skip links
      const skipLinks = page.locator('a[href="#main"], a[href="#content"], a:has-text("Skip to")');
      if (await skipLinks.count() > 0) {
        console.log('✅ Skip links available for screen readers');
      }

      // Check for live regions
      const liveRegions = page.locator('[aria-live], [role="status"], [role="alert"]');
      if (await liveRegions.count() > 0) {
        console.log('✅ Live regions available for dynamic content');
      }
    });

    test('should handle mobile keyboard navigation', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await testHelpers.navigateToPage('/');

      // Test tab navigation
      await page.keyboard.press('Tab');
      const focusedElement = await page.evaluate(() => document.activeElement?.tagName);

      if (focusedElement) {
        console.log(`✅ Keyboard navigation working, focused on: ${focusedElement}`);
      }

      // Test skip to main content
      await page.keyboard.press('Tab');
      const skipLink = page.locator('a:has-text("Skip to")');
      if (await skipLink.count() > 0) {
        await page.keyboard.press('Enter');
        console.log('✅ Skip to main content working');
      }
    });
  });
});
