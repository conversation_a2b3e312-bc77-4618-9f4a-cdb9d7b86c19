import { test, expect, devices } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';

test.describe('Mobile Optimization - Priority 3 Feature 3', () => {
  let testHelpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    testHelpers = new TestHelpers(page);
    // Initialize test environment
    await testHelpers.initializeTestEnvironment('mobile-optimization');
  });

  test.afterEach(async ({ page }) => {
    // Record test completion metrics
    testHelpers.recordTestMetrics('mobile-optimization', Date.now() - 30000, true);
  });

  test.describe('Mobile Responsive Design', () => {
    test('should display mobile-optimized layout on small screens', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/');

      // Check mobile-specific elements
      await expect(page.locator('.mobile-viewport')).toBeVisible();
      await expect(page.locator('.mobile-safe-area')).toBeVisible();

      // Verify header is mobile-optimized
      const header = page.locator('header');
      await expect(header).toBeVisible();
      
      // Check that company button is hidden on mobile
      await expect(page.locator('button:has-text("Acme Design Co.")')).toBeHidden();
      
      // Check that analyze button shows shortened text
      await expect(page.locator('button:has-text("Analyze")')).toBeVisible();
    });

    test('should handle touch interactions properly', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/analysis');

      // Check touch-manipulation class is applied
      const touchElements = page.locator('.touch-manipulation');
      const count = await touchElements.count();
      expect(count).toBeGreaterThan(0);

      // Test touch-friendly button sizes
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();
      
      for (let i = 0; i < Math.min(buttonCount, 5); i++) {
        const button = buttons.nth(i);
        const boundingBox = await button.boundingBox();
        if (boundingBox) {
          // Touch targets should be at least 44px (iOS guidelines)
          expect(boundingBox.height).toBeGreaterThanOrEqual(32); // Allowing some flexibility
        }
      }
    });

    test('should display mobile upload interface correctly', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/analysis');

      // Wait for mobile upload interface to load
      await page.waitForSelector('[data-testid="mobile-upload-interface"], .mobile-upload-interface, .upload-zone-idle', { timeout: 10000 });

      // Check for mobile-specific upload options
      const cameraButton = page.locator('button:has-text("Take Photo")');
      const browseButton = page.locator('button:has-text("Browse Files")');
      
      // These should be visible on mobile
      if (await cameraButton.count() > 0) {
        await expect(cameraButton).toBeVisible();
      }
      if (await browseButton.count() > 0) {
        await expect(browseButton).toBeVisible();
      }

      // Check upload zone is touch-friendly
      const uploadZone = page.locator('.upload-zone-idle, [role="button"]').first();
      await expect(uploadZone).toBeVisible();
    });
  });

  test.describe('Progressive Web App (PWA)', () => {
    test('should load PWA manifest correctly', async ({ page }) => {
      await page.goto('/');
      
      // Check manifest link
      const manifestLink = page.locator('link[rel="manifest"]');
      await expect(manifestLink).toHaveAttribute('href', '/manifest.json');

      // Check PWA meta tags
      await expect(page.locator('meta[name="theme-color"]')).toHaveAttribute('content', '#3b82f6');
      await expect(page.locator('meta[name="apple-mobile-web-app-capable"]')).toHaveAttribute('content', 'yes');
    });

    test('should register service worker', async ({ page }) => {
      await page.goto('/');
      
      // Wait for service worker registration
      await page.waitForFunction(() => {
        return 'serviceWorker' in navigator;
      });

      // Check if service worker is registered
      const swRegistered = await page.evaluate(async () => {
        if ('serviceWorker' in navigator) {
          try {
            const registration = await navigator.serviceWorker.getRegistration();
            return !!registration;
          } catch (error) {
            return false;
          }
        }
        return false;
      });

      // Service worker should be registered (may take time)
      expect(typeof swRegistered).toBe('boolean');
    });

    test('should show PWA install prompt when available', async ({ page }) => {
      await page.goto('/');
      
      // Look for PWA install prompt component
      const installPrompt = page.locator('[data-testid="pwa-install-prompt"], .pwa-install-prompt');
      
      // The prompt may or may not be visible depending on browser support
      // Just check that the component exists in the DOM
      const promptExists = await installPrompt.count() > 0;
      expect(typeof promptExists).toBe('boolean');
    });

    test('should handle offline functionality', async ({ page, context }) => {
      await page.goto('/');
      
      // Wait for page to load completely
      await page.waitForLoadState('networkidle');
      
      // Simulate offline mode
      await context.setOffline(true);
      
      // Try to navigate to a cached page
      await page.goto('/analysis');
      
      // Should either load from cache or show offline page
      const pageContent = await page.textContent('body');
      const isOfflinePage = pageContent?.includes('offline') || pageContent?.includes('Offline');
      const isLoadedFromCache = pageContent?.includes('Analysis') || pageContent?.includes('Upload');
      
      expect(isOfflinePage || isLoadedFromCache).toBe(true);
      
      // Restore online mode
      await context.setOffline(false);
    });
  });

  test.describe('Mobile 3D Viewer', () => {
    test('should load mobile-optimized 3D viewer', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Upload a test file and wait for analysis
      await testHelpers.navigateToPage('/analysis');

      // Upload test file
      const testFilePath = 'tests/fixtures/test-kitchen.pdf';
      await testHelpers.uploadFile(testFilePath);
      await testHelpers.waitForFileValidation();

      // Start analysis
      const analyzeButton = page.locator('button:has-text("Analyze"), button:has-text("Start")');
      if (await analyzeButton.count() > 0) {
        await analyzeButton.first().click();
        await testHelpers.waitForAnalysisComplete();
      }

      // Navigate to 3D view or check if 3D reconstruction is available
      const threeDTab = page.locator('button:has-text("3D View"), [data-value="3d"]');
      if (await threeDTab.count() > 0) {
        await threeDTab.click();
        
        // Wait for 3D viewer to load
        await page.waitForSelector('canvas, .mobile-3d-viewer', { timeout: 15000 });
        
        // Check for mobile-specific 3D controls
        const touchInstructions = page.locator('text=Touch Controls, text=One finger');
        if (await touchInstructions.count() > 0) {
          await expect(touchInstructions.first()).toBeVisible();
        }
        
        // Check for mobile optimization badge
        const mobileOptimized = page.locator('text=Mobile Optimized');
        if (await mobileOptimized.count() > 0) {
          await expect(mobileOptimized.first()).toBeVisible();
        }
      }
    });

    test('should handle touch gestures in 3D viewer', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      
      await testHelpers.navigateToPage('/analysis');

      // Upload test file
      const testFilePath = 'tests/fixtures/test-kitchen.pdf';
      await testHelpers.uploadFile(testFilePath);
      await testHelpers.waitForFileValidation();

      // Start analysis
      const analyzeButton = page.locator('button:has-text("Analyze"), button:has-text("Start")');
      if (await analyzeButton.count() > 0) {
        await analyzeButton.first().click();
        await testHelpers.waitForAnalysisComplete();
      }

      const threeDTab = page.locator('button:has-text("3D View"), [data-value="3d"]');
      if (await threeDTab.count() > 0) {
        await threeDTab.click();
        
        const canvas = page.locator('canvas').first();
        if (await canvas.count() > 0) {
          // Test touch interaction
          await canvas.tap();
          
          // Check that canvas has touch-action: none style
          const touchAction = await canvas.evaluate((el) => {
            return window.getComputedStyle(el).touchAction;
          });
          
          expect(touchAction).toBe('none');
        }
      }
    });
  });

  test.describe('Mobile Performance', () => {
    test('should meet mobile performance targets', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Simulate 3G network conditions
      await page.route('**/*', async (route) => {
        await new Promise(resolve => setTimeout(resolve, 100)); // Add 100ms delay
        await route.continue();
      });

      const startTime = Date.now();
      await page.goto('/');
      
      // Wait for First Contentful Paint
      await page.waitForSelector('h1, .hero-section, header', { timeout: 5000 });
      const fcpTime = Date.now() - startTime;
      
      // Should load within 3 seconds on simulated 3G
      expect(fcpTime).toBeLessThan(3000);
    });

    test('should lazy load images and components', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/');
      
      // Check for lazy loading attributes
      const images = page.locator('img');
      const imageCount = await images.count();
      
      if (imageCount > 0) {
        for (let i = 0; i < Math.min(imageCount, 3); i++) {
          const img = images.nth(i);
          const loading = await img.getAttribute('loading');
          // Images should have lazy loading (except above-the-fold)
          if (loading) {
            expect(['lazy', 'eager']).toContain(loading);
          }
        }
      }
    });

    test('should optimize bundle size for mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Monitor network requests
      const responses: any[] = [];
      page.on('response', (response) => {
        if (response.url().includes('.js') || response.url().includes('.css')) {
          responses.push({
            url: response.url(),
            size: response.headers()['content-length'],
            status: response.status()
          });
        }
      });

      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // Check that main bundle loaded successfully
      const mainBundle = responses.find(r => r.url.includes('main') || r.url.includes('index'));
      if (mainBundle) {
        expect(mainBundle.status).toBe(200);
      }
      
      expect(responses.length).toBeGreaterThan(0);
    });
  });

  test.describe('Mobile Analysis Workflow', () => {
    test('should complete full analysis workflow on mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/analysis');

      // Upload file using mobile interface
      const testFilePath = 'tests/fixtures/test-kitchen.pdf';
      await testHelpers.uploadFile(testFilePath);
      await testHelpers.waitForFileValidation();

      // Start analysis
      const analyzeButton = page.locator('button:has-text("Analyze"), button:has-text("Start")');
      if (await analyzeButton.count() > 0) {
        await analyzeButton.first().click();
        await testHelpers.waitForAnalysisComplete();
      }
      
      // Check mobile analysis viewer
      const cabinetCount = page.locator('text=Total Cabinets Detected').first();
      if (await cabinetCount.count() > 0) {
        await expect(cabinetCount).toBeVisible();
      }
      
      // Check mobile tabs are working
      const tabs = page.locator('[role="tablist"] button, .tabs-trigger');
      const tabCount = await tabs.count();
      
      if (tabCount > 0) {
        // Test switching between tabs
        for (let i = 0; i < Math.min(tabCount, 3); i++) {
          await tabs.nth(i).click();
          await page.waitForTimeout(500); // Allow tab content to load
        }
      }
    });

    test('should generate and view reports on mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      
      const testFilePath = 'tests/fixtures/test-kitchen.pdf';
      await testHelpers.uploadFile(testFilePath);
      await testHelpers.waitForFileValidation();

      // Start analysis
      const analyzeButton = page.locator('button:has-text("Analyze"), button:has-text("Start")');
      if (await analyzeButton.count() > 0) {
        await analyzeButton.first().click();
        await testHelpers.waitForAnalysisComplete();
      }
      
      // Look for report generation button
      const generateButton = page.locator('button:has-text("Generate"), button:has-text("Report"), button:has-text("Basic Report")');
      
      if (await generateButton.count() > 0) {
        await generateButton.first().click();
        
        // Wait for report generation
        await page.waitForSelector('text=Report, text=PDF, text=Download', { timeout: 30000 });
        
        // Check mobile report viewer
        const reportViewer = page.locator('.mobile-report-viewer, [data-testid="mobile-report-viewer"]');
        if (await reportViewer.count() > 0) {
          await expect(reportViewer).toBeVisible();
        }
      }
    });
  });

  test.describe('PWA Installation and Features', () => {
    test('should validate PWA manifest', async ({ page }) => {
      // Test manifest.json directly
      const manifestResponse = await page.request.get('/manifest.json');
      expect(manifestResponse.status()).toBe(200);

      const manifest = await manifestResponse.json();
      expect(manifest.name).toBe('Cabinet Insight Pro');
      expect(manifest.short_name).toBe('Cabinet Pro');
      expect(manifest.display).toBe('standalone');
      expect(manifest.theme_color).toBe('#3b82f6');
      expect(manifest.icons).toBeDefined();
      expect(manifest.icons.length).toBeGreaterThan(0);
    });

    test('should load service worker script', async ({ page }) => {
      const swResponse = await page.request.get('/sw.js');
      expect(swResponse.status()).toBe(200);

      const swContent = await swResponse.text();
      expect(swContent).toContain('Cabinet Insight Pro');
      expect(swContent).toContain('CACHE_NAME');
      expect(swContent).toContain('install');
      expect(swContent).toContain('fetch');
    });

    test('should load offline page', async ({ page }) => {
      const offlineResponse = await page.request.get('/offline.html');
      expect(offlineResponse.status()).toBe(200);

      const offlineContent = await offlineResponse.text();
      expect(offlineContent).toContain('offline');
      expect(offlineContent).toContain('Cabinet Insight Pro');
    });
  });

  test.describe('Mobile Performance Metrics', () => {
    test('should achieve target performance scores', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });

      const startTime = Date.now();
      await page.goto('/');

      // Wait for First Contentful Paint
      await page.waitForSelector('h1, header', { timeout: 3000 });
      const fcpTime = Date.now() - startTime;

      // Should load within 3 seconds target
      expect(fcpTime).toBeLessThan(3000);
    });

    test('should optimize resource loading', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });

      const resourceSizes: number[] = [];

      page.on('response', async (response) => {
        if (response.url().includes('.js') || response.url().includes('.css')) {
          try {
            const buffer = await response.body();
            resourceSizes.push(buffer.length);
          } catch (error) {
            // Ignore errors for resources we can't access
          }
        }
      });

      await page.goto('/');
      await page.waitForLoadState('networkidle');

      // Check that we're loading resources
      expect(resourceSizes.length).toBeGreaterThan(0);
    });
  });
});
