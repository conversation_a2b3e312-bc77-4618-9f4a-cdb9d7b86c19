const fs = require('fs');
const path = require('path');

// Create a simple test PDF (minimal PDF structure)
const createTestPDF = () => {
  const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Kitchen Design Test) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF`;

  return Buffer.from(pdfContent);
};

// Create a simple test image (1x1 PNG)
const createTestPNG = () => {
  // Minimal 1x1 transparent PNG
  const pngData = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
    0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
    0x49, 0x48, 0x44, 0x52, // IHDR
    0x00, 0x00, 0x00, 0x01, // width: 1
    0x00, 0x00, 0x00, 0x01, // height: 1
    0x08, 0x06, 0x00, 0x00, 0x00, // bit depth, color type, compression, filter, interlace
    0x1F, 0x15, 0xC4, 0x89, // CRC
    0x00, 0x00, 0x00, 0x0A, // IDAT chunk length
    0x49, 0x44, 0x41, 0x54, // IDAT
    0x78, 0x9C, 0x62, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, // compressed data
    0xE2, 0x21, 0xBC, 0x33, // CRC
    0x00, 0x00, 0x00, 0x00, // IEND chunk length
    0x49, 0x45, 0x4E, 0x44, // IEND
    0xAE, 0x42, 0x60, 0x82  // CRC
  ]);
  
  return pngData;
};

// Create test files
const fixturesDir = __dirname;

// Create kitchen design PDF
fs.writeFileSync(path.join(fixturesDir, 'kitchen-design-test.pdf'), createTestPDF());
console.log('Created kitchen-design-test.pdf');

// Create kitchen design PNG
fs.writeFileSync(path.join(fixturesDir, 'kitchen-design-test.png'), createTestPNG());
console.log('Created kitchen-design-test.png');

// Create a larger test file for size testing
const largePdfContent = createTestPDF().toString() + '\n'.repeat(1000);
fs.writeFileSync(path.join(fixturesDir, 'kitchen-design-large.pdf'), largePdfContent);
console.log('Created kitchen-design-large.pdf');

console.log('Test fixtures created successfully!');
