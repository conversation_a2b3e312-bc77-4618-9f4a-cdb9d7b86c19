import { test, expect } from '@playwright/test';
import { TestHelpers } from './utils/test-helpers';
import { randomUUID } from 'crypto';

test.describe('Performance Monitoring System', () => {
  let testHelpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    testHelpers = new TestHelpers(page);
  });

  test('should record test metrics via API', async ({ request }) => {
    const testMetrics = {
      testName: 'performance-monitoring-test',
      testCategory: 'api',
      browser: 'chromium',
      duration: 1500,
      success: true,
      retryCount: 0,
      featureVersion: 'v1.0.0-test',
      buildId: 'test-build-123',
      networkCondition: {
        quality: 'good',
        latency: 100,
        bandwidth: 1000,
        packetLoss: 0
      },
      resourceUsage: {
        memoryUsage: 50,
        cpuUsage: 25,
        diskUsage: 10
      },
      metadata: {
        testFile: 'performance-monitoring.spec.ts',
        testLine: 1,
        testColumn: 1
      }
    };

    const response = await request.post('http://localhost:3001/api/performance-monitoring/test-metrics', {
      data: testMetrics
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.testId).toBeDefined();
    expect(data.data.recorded).toBe(true);

    console.log('✅ Test metrics recorded successfully');
  });

  test('should record test suite results via API', async ({ request }) => {
    const testSuiteResult = {
      suiteId: randomUUID(),
      suiteName: 'Performance Monitoring Tests',
      totalTests: 10,
      passedTests: 9,
      failedTests: 1,
      skippedTests: 0,
      successRate: 90.0,
      duration: 15000,
      featureVersion: 'v1.0.0-test',
      buildId: 'test-build-123',
      browser: 'chromium',
      testCategory: 'integration'
    };

    const response = await request.post('http://localhost:3001/api/performance-monitoring/test-suite-results', {
      data: testSuiteResult
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.suiteResultId).toBeDefined();
    expect(data.data.recorded).toBe(true);

    console.log('✅ Test suite results recorded successfully');
  });

  test('should fetch dashboard data', async ({ request }) => {
    const response = await request.get('http://localhost:3001/api/performance-monitoring/dashboard');

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toBeDefined();
    expect(data.data.thresholds).toBeDefined();
    expect(data.data.thresholds.successRate).toBe(91.7);

    console.log('✅ Dashboard data fetched successfully');
    console.log(`📊 Current success rate threshold: ${data.data.thresholds.successRate}%`);
  });

  test('should create feature baseline', async ({ request }) => {
    const featureVersion = 'v1.0.0-baseline-test';

    const response = await request.post('http://localhost:3001/api/performance-monitoring/feature-baseline', {
      data: { featureVersion }
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.featureVersion).toBe(featureVersion);
    expect(data.data.baselineCreated).toBe(true);

    console.log(`✅ Feature baseline created for version: ${featureVersion}`);
  });

  test('should analyze feature impact', async ({ request }) => {
    const featureVersion = 'v1.0.0-impact-test';

    // First create a baseline
    await request.post('http://localhost:3001/api/performance-monitoring/feature-baseline', {
      data: { featureVersion }
    });

    // Then analyze impact
    const response = await request.post('http://localhost:3001/api/performance-monitoring/analyze-feature-impact', {
      data: { featureVersion }
    });

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.featureVersion).toBe(featureVersion);
    expect(data.data.recommendation).toBeDefined();
    expect(['proceed', 'investigate', 'rollback']).toContain(data.data.recommendation);

    console.log(`✅ Feature impact analyzed for version: ${featureVersion}`);
    console.log(`📊 Recommendation: ${data.data.recommendation}`);
  });

  test('should access performance monitoring dashboard page', async ({ page }) => {
    await page.goto('/performance-monitoring');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check for main dashboard elements
    await expect(page.locator('h1')).toContainText('Performance Monitoring');
    
    // Check for success rate card
    await expect(page.locator('text=Success Rate')).toBeVisible();
    
    // Check for threshold information
    await expect(page.locator('text=91.7%')).toBeVisible();
    
    console.log('✅ Performance monitoring dashboard page loaded successfully');
  });

  test('should display real-time metrics on dashboard', async ({ page }) => {
    await page.goto('/performance-monitoring');
    await page.waitForLoadState('networkidle');
    
    // Wait for dashboard data to load
    await page.waitForSelector('[data-testid="success-rate-card"], .text-2xl', { timeout: 10000 });
    
    // Check for metric cards
    const successRateCard = page.locator('text=Success Rate').first();
    await expect(successRateCard).toBeVisible();
    
    const totalTestsCard = page.locator('text=Total Tests').first();
    await expect(totalTestsCard).toBeVisible();
    
    const passedTestsCard = page.locator('text=Passed Tests').first();
    await expect(passedTestsCard).toBeVisible();
    
    const alertsCard = page.locator('text=Active Alerts').first();
    await expect(alertsCard).toBeVisible();
    
    console.log('✅ Real-time metrics displayed on dashboard');
  });

  test('should handle performance monitoring service health check', async ({ request }) => {
    const response = await request.get('http://localhost:3001/api/performance-monitoring/health');

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.status).toBe('healthy');
    expect(data.data.service).toBe('performance-monitoring');

    console.log('✅ Performance monitoring service health check passed');
  });

  test('should validate 91.7% success rate threshold enforcement', async ({ request }) => {
    // Get current dashboard data to check threshold
    const dashboardResponse = await request.get('http://localhost:3001/api/performance-monitoring/dashboard');
    const dashboardData = await dashboardResponse.json();
    
    expect(dashboardData.data.thresholds.successRate).toBe(91.7);
    
    // Record a test that would trigger an alert if success rate drops
    const failingTestMetrics = {
      testName: 'threshold-test-failing',
      testCategory: 'api',
      browser: 'chromium',
      duration: 2000,
      success: false,
      retryCount: 1,
      errorType: 'AssertionError',
      errorMessage: 'Test failed for threshold validation',
      featureVersion: 'v1.0.0-threshold-test'
    };

    const response = await request.post('http://localhost:3001/api/performance-monitoring/test-metrics', {
      data: failingTestMetrics
    });

    expect(response.ok()).toBeTruthy();
    
    console.log('✅ 91.7% success rate threshold properly configured');
    console.log('📊 Threshold enforcement validated');
  });

  test('should demonstrate feature impact correlation', async ({ request }) => {
    const featureVersion = 'v1.0.0-correlation-test';
    
    // Create baseline
    await request.post('http://localhost:3001/api/performance-monitoring/feature-baseline', {
      data: { featureVersion }
    });

    // Record some test metrics for this feature
    const testMetrics = [
      {
        testName: 'correlation-test-1',
        testCategory: 'api',
        browser: 'chromium',
        duration: 1200,
        success: true,
        featureVersion
      },
      {
        testName: 'correlation-test-2',
        testCategory: 'ui',
        browser: 'chromium',
        duration: 1800,
        success: true,
        featureVersion
      },
      {
        testName: 'correlation-test-3',
        testCategory: 'integration',
        browser: 'chromium',
        duration: 2500,
        success: false,
        errorType: 'TimeoutError',
        featureVersion
      }
    ];

    // Record all test metrics
    for (const metrics of testMetrics) {
      await request.post('http://localhost:3001/api/performance-monitoring/test-metrics', {
        data: metrics
      });
    }

    // Analyze feature impact
    const impactResponse = await request.post('http://localhost:3001/api/performance-monitoring/analyze-feature-impact', {
      data: { featureVersion }
    });

    expect(impactResponse.ok()).toBeTruthy();
    
    const impactData = await impactResponse.json();
    expect(impactData.data.impact).toBeDefined();
    expect(impactData.data.impact.newFailures).toBeDefined();
    
    console.log('✅ Feature impact correlation demonstrated');
    console.log(`📊 New failures detected: ${impactData.data.impact.newFailures.length}`);
  });
});
