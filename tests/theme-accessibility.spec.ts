import { test, expect } from '@playwright/test';

test.describe('Advanced Theme System - Phase 4: Accessibility Features', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForSelector('[data-testid="theme-toggle"]', { timeout: 10000 });
  });

  test('should display accessibility options in theme dropdown', async ({ page }) => {
    // Open theme toggle dropdown
    await page.click('[data-testid="theme-toggle"]');
    
    // Wait for dropdown to open
    await page.waitForSelector('[role="menu"]');
    
    // Check that accessibility options are present
    await expect(page.locator('text=High Contrast')).toBeVisible();
    await expect(page.locator('text=Focus Enhancement')).toBeVisible();
  });

  test('should enable high contrast mode and apply enhanced styling', async ({ page }) => {
    // Open theme toggle and enable high contrast
    await page.click('[data-testid="theme-toggle"]');
    await page.click('text=High Contrast');
    
    // Wait for theme application
    await page.waitForTimeout(500);
    
    // Check that high contrast class is applied
    const html = page.locator('html');
    await expect(html).toHaveClass(/high-contrast/);
    
    // Verify enhanced border styling is applied
    const cardBorder = await page.evaluate(() => {
      const card = document.querySelector('.aone-card');
      if (card) {
        return getComputedStyle(card).borderWidth;
      }
      return null;
    });
    
    expect(cardBorder).toBe('2px');
  });

  test('should enable focus enhancement mode and apply stronger focus rings', async ({ page }) => {
    // Open theme toggle and enable focus enhancement
    await page.click('[data-testid="theme-toggle"]');
    await page.click('text=Focus Enhancement');
    
    // Wait for theme application
    await page.waitForTimeout(500);
    
    // Check that focus enhanced class is applied
    const html = page.locator('html');
    await expect(html).toHaveClass(/focus-enhanced/);
    
    // Test focus ring on a button
    const button = page.locator('button').first();
    await button.focus();
    
    // Verify enhanced focus styling
    const focusOutline = await button.evaluate((el) => {
      return getComputedStyle(el).outline;
    });
    
    expect(focusOutline).toContain('3px');
  });

  test('should respect prefers-reduced-motion system preference', async ({ page }) => {
    // Set reduced motion preference
    await page.emulateMedia({ reducedMotion: 'reduce' });
    
    // Reload to apply preference
    await page.reload();
    await page.waitForSelector('[data-testid="theme-toggle"]');
    
    // Check that reduced motion class is applied
    const html = page.locator('html');
    await expect(html).toHaveClass(/reduced-motion/);
    
    // Verify transitions are disabled
    const transitionDuration = await page.evaluate(() => {
      const element = document.querySelector('*');
      if (element) {
        return getComputedStyle(element).transitionDuration;
      }
      return null;
    });
    
    expect(transitionDuration).toBe('0s');
  });

  test('should auto-detect system preferences when enabled', async ({ page }) => {
    // Test with dark mode preference
    await page.emulateMedia({ colorScheme: 'dark' });
    
    // Reload to apply preference
    await page.reload();
    await page.waitForSelector('[data-testid="theme-toggle"]');
    
    // Check that dark theme is auto-applied
    const html = page.locator('html');
    await expect(html).toHaveClass(/dark/);
  });

  test('should persist accessibility settings in localStorage', async ({ page }) => {
    // Enable high contrast and focus enhancement
    await page.click('[data-testid="theme-toggle"]');
    await page.click('text=High Contrast');
    await page.click('[data-testid="theme-toggle"]');
    await page.click('text=Focus Enhancement');
    
    // Wait for settings to be applied
    await page.waitForTimeout(500);
    
    // Reload page
    await page.reload();
    await page.waitForSelector('[data-testid="theme-toggle"]');
    
    // Check that settings are still active
    const html = page.locator('html');
    await expect(html).toHaveClass(/high-contrast/);
    await expect(html).toHaveClass(/focus-enhanced/);
    
    // Verify localStorage contains the settings
    const storedSettings = await page.evaluate(() => {
      return localStorage.getItem('blackveil-design-mind-accessibility');
    });
    
    expect(storedSettings).toContain('highContrast');
    expect(storedSettings).toContain('focusEnhancement');
  });

  test('should work with all theme presets in high contrast mode', async ({ page }) => {
    const presets = ['Professional', 'Creative', 'Minimal', 'Enterprise'];
    
    // Enable high contrast first
    await page.click('[data-testid="theme-toggle"]');
    await page.click('text=High Contrast');
    
    for (const preset of presets) {
      // Select preset
      await page.click('[data-testid="theme-toggle"]');
      await page.click(`text=${preset}`);
      await page.waitForTimeout(500);
      
      // Check that both high contrast and preset classes are applied
      const html = page.locator('html');
      await expect(html).toHaveClass(/high-contrast/);
      await expect(html).toHaveClass(new RegExp(`preset-${preset.toLowerCase()}`));
      
      // Verify enhanced styling is maintained
      const cardBorder = await page.evaluate(() => {
        const card = document.querySelector('.aone-card');
        if (card) {
          return getComputedStyle(card).borderWidth;
        }
        return null;
      });
      
      expect(cardBorder).toBe('2px');
    }
  });

  test('should provide keyboard navigation indicators in focus enhanced mode', async ({ page }) => {
    // Enable focus enhancement
    await page.click('[data-testid="theme-toggle"]');
    await page.click('text=Focus Enhancement');
    
    await page.waitForTimeout(500);
    
    // Navigate using keyboard
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // Check for keyboard navigation indicator
    const keyboardIndicator = await page.evaluate(() => {
      const focusedElement = document.activeElement;
      if (focusedElement) {
        const beforePseudo = getComputedStyle(focusedElement, '::before');
        return beforePseudo.content;
      }
      return null;
    });
    
    // The keyboard indicator should be present (⌨️ emoji)
    expect(keyboardIndicator).toContain('⌨️');
  });

  test('should maintain WCAG AAA compliance in high contrast mode', async ({ page }) => {
    // Enable high contrast mode
    await page.click('[data-testid="theme-toggle"]');
    await page.click('text=High Contrast');
    
    await page.waitForTimeout(500);
    
    // Test contrast ratios for key color combinations
    const contrastResults = await page.evaluate(() => {
      const sage = getComputedStyle(document.documentElement).getPropertyValue('--aone-sage').trim();
      const background = getComputedStyle(document.documentElement).getPropertyValue('--aone-warm-white').trim();
      const charcoal = getComputedStyle(document.documentElement).getPropertyValue('--aone-charcoal').trim();
      
      // Simple validation that colors are different and enhanced
      return {
        sageBackground: sage !== background,
        charcoalBackground: charcoal !== background,
        sageCharcoal: sage !== charcoal,
      };
    });
    
    expect(contrastResults.sageBackground).toBe(true);
    expect(contrastResults.charcoalBackground).toBe(true);
    expect(contrastResults.sageCharcoal).toBe(true);
  });
});
