# Cabinet Insight Pro - End-to-End Tests

This directory contains comprehensive Playwright end-to-end tests for the Cabinet Insight Pro application, specifically designed to verify the Azure OpenAI integration and overall application functionality with advanced network monitoring and test optimization features.

## Test Structure

```
tests/
├── api/                    # API integration tests
│   ├── health.spec.ts     # Health endpoint tests
│   └── analysis.spec.ts   # Analysis API tests
├── frontend/              # Frontend UI tests
│   ├── app-loading.spec.ts # Application loading tests
│   └── file-upload.spec.ts # File upload UI tests
├── integration/           # Full integration tests
│   ├── azure-openai.spec.ts # Azure OpenAI functionality tests
│   └── websocket.spec.ts   # WebSocket real-time tests
├── fixtures/              # Test data files
│   ├── kitchen-design-test.pdf
│   ├── kitchen-design-test.png
│   └── kitchen-design-large.pdf
├── utils/                 # Test utilities
│   └── test-helpers.ts    # Common test helper functions
├── global-setup.ts        # Global test setup
├── global-teardown.ts     # Global test teardown
└── README.md              # This file
```

## Prerequisites

1. **Servers Running**: Both frontend (port 8080) and backend (port 3001) must be running
2. **Azure OpenAI Configuration**: Valid Azure OpenAI credentials must be configured in `server/.env`
3. **Dependencies**: Playwright and test dependencies installed

## Running Tests

### Install Dependencies
```bash
npm install
npx playwright install
```

### Run All Tests
```bash
npm run test
```

### Run Specific Test Suites
```bash
# API tests only
npm run test:api

# Frontend tests only
npm run test:frontend

# Integration tests only
npm run test:integration

# Azure OpenAI tests specifically
npm run test:azure

# Advanced monitoring and optimization
npm run test:monitoring   # Enhanced monitoring demo tests
npm run test:performance  # Performance analysis with metrics
npm run test:compatibility # Cross-browser compatibility testing
```

### Interactive Testing
```bash
# Run tests with browser visible
npm run test:headed

# Run tests with Playwright UI
npm run test:ui

# Debug mode (step through tests)
npm run test:debug
```

### View Test Reports
```bash
npm run test:report
```

## Test Categories

### 1. API Integration Tests (`tests/api/`)

**Health Tests (`health.spec.ts`)**:
- ✅ Basic health endpoint functionality
- ✅ Detailed health with Azure OpenAI configuration verification
- ✅ Readiness and liveness checks

**Analysis Tests (`analysis.spec.ts`)**:
- ✅ File upload functionality (PDF/PNG)
- ✅ Analysis configuration options
- ✅ Queue status monitoring
- ✅ Prompt management
- ✅ File validation and error handling

### 2. Frontend Integration Tests (`tests/frontend/`)

**App Loading Tests (`app-loading.spec.ts`)**:
- ✅ Main application loading
- ✅ Navigation between pages
- ✅ Responsive design verification
- ✅ 404 error handling

**File Upload Tests (`file-upload.spec.ts`)**:
- ✅ File upload UI components
- ✅ Drag and drop functionality
- ✅ File type validation
- ✅ Analysis configuration options
- ✅ Progress indicators

### 3. Integration Tests (`tests/integration/`)

**Azure OpenAI Tests (`azure-openai.spec.ts`)**:
- ✅ Azure OpenAI configuration verification
- ✅ Real API calls (not mock responses)
- ✅ GPT-4o model testing
- ✅ GPT-4o-mini model testing
- ✅ Dual-model reasoning workflow

**WebSocket Tests (`websocket.spec.ts`)**:
- ✅ WebSocket connection establishment
- ✅ Real-time progress updates
- ✅ Analysis completion notifications
- ✅ Connection resilience testing

**Enhanced Monitoring Demo (`enhanced-monitoring-demo.spec.ts`)**:
- ✅ Network condition detection and adaptive timeouts
- ✅ Environment validation and pre-test health checks
- ✅ Intelligent retry strategies with browser-specific handling
- ✅ Performance metrics collection and analysis
- ✅ Cross-browser compatibility matrix validation
- ✅ Test batching recommendations and optimization

## Key Test Features

### Azure OpenAI Verification
The tests specifically verify that:
- Azure OpenAI configuration is properly detected
- Real API calls are made (not mock responses)
- Both GPT-4o and GPT-4o-mini models are accessible
- Token usage statistics are real (not mock data)
- Dual-model workflows function correctly

### Real-time Testing
- WebSocket connections are established
- Progress updates are received in real-time
- UI updates reflect real analysis progress
- Connection resilience is maintained

### Comprehensive Coverage
- API endpoints are thoroughly tested
- Frontend components are validated
- File upload and processing workflows
- Error handling and edge cases
- Cross-browser compatibility

## Test Configuration

### Timeouts
- Individual tests: 2 minutes (for AI operations)
- Assertions: 30 seconds
- Analysis completion: Up to 2 minutes

### Browsers
Tests run on:
- Chromium (Desktop)
- Firefox (Desktop)
- WebKit/Safari (Desktop)
- Mobile Chrome (Pixel 5)
- Mobile Safari (iPhone 12)

### Environment
- Frontend: http://localhost:8080
- Backend: http://localhost:3001
- Test data: Sample kitchen design files

## Troubleshooting

### Common Issues

1. **Servers Not Running**
   ```
   Error: connect ECONNREFUSED ::1:8080
   ```
   Solution: Start both frontend and backend servers

2. **Azure OpenAI Not Configured**
   ```
   OpenAI service is not configured
   ```
   Solution: Set Azure OpenAI environment variables in `server/.env`

3. **Test Timeouts**
   ```
   Test timeout of 120000ms exceeded
   ```
   Solution: Check Azure OpenAI API connectivity and quotas

4. **File Upload Failures**
   ```
   File not found: kitchen-design-test.pdf
   ```
   Solution: Run `node tests/fixtures/create-test-files.cjs` to create test files

### Debug Mode
Use debug mode to step through tests:
```bash
npm run test:debug
```

### Screenshots and Videos
Failed tests automatically capture:
- Screenshots on failure
- Videos of test execution
- Browser traces for debugging

## Contributing

When adding new tests:
1. Follow the existing test structure
2. Use the TestHelpers utility class
3. Add appropriate timeouts for AI operations
4. Include both positive and negative test cases
5. Document any new test fixtures or utilities

## CI/CD Integration

These tests are designed to run in CI/CD pipelines:
- Headless mode by default
- Retry logic for flaky tests
- Multiple output formats (HTML, JSON, JUnit)
- Screenshot/video capture on failures
