import { chromium, FullConfig } from '@playwright/test';
import { NetworkMonitor } from './utils/network-monitor';
import { EnvironmentValidator } from './utils/environment-validator';
import { CompatibilityMatrix } from './utils/compatibility-matrix';
import { TestBatchingManager } from './utils/test-batching';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global test setup with advanced monitoring...');

  // Create required directories
  const fs = await import('fs');
  const path = await import('path');
  const requiredDirs = ['test-results', 'uploads', 'temp'];

  for (const dir of requiredDirs) {
    const dirPath = path.resolve(dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`📁 Created directory: ${dir}`);
    }
  }

  // Initialize monitoring services
  const networkMonitor = NetworkMonitor.getInstance();
  const environmentValidator = EnvironmentValidator.getInstance();
  const compatibilityMatrix = CompatibilityMatrix.getInstance();
  const batchingManager = TestBatchingManager.getInstance();

  // Wait for servers to be ready
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // Check frontend server with retry logic
    console.log('⏳ Waiting for frontend server...');
    let frontendReady = false;
    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        await page.goto('http://localhost:8080', {
          waitUntil: 'domcontentloaded',
          timeout: 30000
        });
        await page.waitForLoadState('networkidle', { timeout: 15000 });
        frontendReady = true;
        break;
      } catch (error) {
        console.log(`Frontend server attempt ${attempt}/3 failed:`, error);
        if (attempt < 3) {
          await page.waitForTimeout(2000);
        }
      }
    }

    if (!frontendReady) {
      throw new Error('Frontend server failed to start after 3 attempts');
    }
    console.log('✅ Frontend server is ready');
    
    // Check backend server health
    console.log('⏳ Checking backend server health...');
    const response = await page.request.get('http://localhost:3001/api/health');
    if (!response.ok()) {
      throw new Error(`Backend health check failed: ${response.status()}`);
    }
    console.log('✅ Backend server is healthy');
    
    // Check Azure OpenAI configuration
    console.log('⏳ Verifying Azure OpenAI configuration...');
    const detailedResponse = await page.request.get('http://localhost:3001/api/health/detailed');
    const healthData = await detailedResponse.json();
    
    if (!healthData.success) {
      throw new Error('Health check returned unsuccessful status');
    }
    
    const openaiService = healthData.data.services.openai;
    if (!openaiService.configured) {
      throw new Error('OpenAI service is not configured');
    }
    
    if (openaiService.type !== 'azure') {
      console.warn(`⚠️  OpenAI service type is '${openaiService.type}', expected 'azure'`);
    } else {
      console.log('✅ Azure OpenAI configuration verified');
    }
    
    console.log(`📊 OpenAI Status: ${openaiService.status} (${openaiService.type})`);
    if (openaiService.endpoint) {
      console.log(`🔗 Azure Endpoint: ${openaiService.endpoint}`);
    }

    // Perform comprehensive environment validation
    console.log('🔍 Performing comprehensive environment validation...');
    const envValidation = await environmentValidator.validateEnvironment(page);
    console.log(environmentValidator.generateValidationReport(envValidation));

    if (envValidation.overall === 'fail') {
      console.error('❌ Environment validation failed. Please fix issues before running tests.');
      throw new Error('Environment validation failed');
    }

    // Detect initial network conditions
    console.log('📡 Detecting network conditions...');
    const networkCondition = await networkMonitor.detectNetworkCondition(page);
    console.log(`📊 Network Quality: ${networkCondition.quality}`);
    console.log(`⏱️ Recommended Timeout: ${networkCondition.recommendedTimeout}ms`);

    // Enhanced WebSocket connectivity verification
    console.log('🔌 Verifying WebSocket connectivity...');
    try {
      // Wait for Socket.IO to be available
      await page.waitForFunction(() => {
        return typeof window.io !== 'undefined';
      }, { timeout: 10000 });

      // Test WebSocket connection
      const wsConnected = await page.evaluate(async () => {
        try {
          if (window.io) {
            const socket = window.io('http://localhost:3001');
            return new Promise((resolve) => {
              socket.on('connect', () => {
                socket.disconnect();
                resolve(true);
              });
              socket.on('connect_error', () => {
                resolve(false);
              });
              setTimeout(() => resolve(false), 5000);
            });
          }
          return false;
        } catch (error) {
          return false;
        }
      });

      if (wsConnected) {
        console.log('✅ WebSocket connectivity verified');
      } else {
        console.warn('⚠️ WebSocket connectivity test failed - tests may experience issues');
      }
    } catch (error) {
      console.warn('⚠️ WebSocket verification failed:', error.message);
    }

    // Generate compatibility report
    console.log('🔄 Generating compatibility matrix...');
    console.log(compatibilityMatrix.generateCompatibilityReport());

  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }

  console.log('✅ Global test setup completed successfully with advanced monitoring enabled');
}

export default globalSetup;
