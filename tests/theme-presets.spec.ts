import { test, expect } from '@playwright/test';

test.describe('Advanced Theme System - Phase 1: Theme Presets', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    // Wait for the application to load - try multiple selectors
    try {
      await page.waitForSelector('[data-testid="theme-toggle"]', { timeout: 5000 });
    } catch {
      // Fallback to aria-label selector
      await page.waitForSelector('[aria-label*="theme"]', { timeout: 5000 });
    }
  });

  test('should display all four theme presets in dropdown', async ({ page }) => {
    // Open theme toggle dropdown - try multiple selectors
    const themeToggle = page.locator('[data-testid="theme-toggle"]').or(page.locator('[aria-label*="theme"]')).first();
    await themeToggle.click();

    // Wait for dropdown to open
    await page.waitForSelector('[role="menu"]');

    // Check that all presets are present within the dropdown menu
    const dropdownMenu = page.locator('[role="menu"]');
    const presets = ['Professional', 'Creative', 'Minimal', 'Enterprise'];

    for (const preset of presets) {
      await expect(dropdownMenu.locator(`text=${preset}`).first()).toBeVisible();
    }
  });

  test('should switch to Professional preset and apply correct colors', async ({ page }) => {
    // Open theme toggle and select Professional preset
    const themeToggle = page.locator('[data-testid="theme-toggle"]').or(page.locator('[aria-label*="theme"]')).first();
    await themeToggle.click();

    // Wait for dropdown and click Professional preset within the menu
    const dropdownMenu = page.locator('[role="menu"]');
    await dropdownMenu.locator('text=Professional').first().click();
    
    // Wait for theme application
    await page.waitForTimeout(500);
    
    // Check that professional preset class is applied
    const html = page.locator('html');
    await expect(html).toHaveClass(/preset-professional/);
    
    // Verify CSS custom properties are set correctly
    const sageColor = await page.evaluate(() => {
      return getComputedStyle(document.documentElement).getPropertyValue('--aone-sage').trim();
    });
    
    expect(sageColor).toBe('107 122 79');
  });

  test('should switch to Creative preset and apply warm accent colors', async ({ page }) => {
    // Open theme toggle and select Creative preset
    const themeToggle = page.locator('[data-testid="theme-toggle"]').or(page.locator('[aria-label*="theme"]')).first();
    await themeToggle.click();

    const dropdownMenu = page.locator('[role="menu"]');
    await dropdownMenu.locator('text=Creative').first().click();
    
    // Wait for theme application
    await page.waitForTimeout(500);
    
    // Check that creative preset class is applied
    const html = page.locator('html');
    await expect(html).toHaveClass(/preset-creative/);
    
    // Verify accent color is warm orange
    const accentColor = await page.evaluate(() => {
      return getComputedStyle(document.documentElement).getPropertyValue('--preset-accent').trim();
    });
    
    expect(accentColor).toBe('251 146 60');
  });

  test('should switch to Minimal preset and apply high contrast colors', async ({ page }) => {
    // Open theme toggle and select Minimal preset
    const themeToggle = page.locator('[data-testid="theme-toggle"]').or(page.locator('[aria-label*="theme"]')).first();
    await themeToggle.click();

    const dropdownMenu = page.locator('[role="menu"]');
    await dropdownMenu.locator('text=Minimal').first().click();
    
    // Wait for theme application and transitions to complete
    await page.waitForTimeout(1500);

    // Wait for page to be stable (no navigation events)
    await page.waitForLoadState('networkidle');

    // Check that minimal preset class is applied
    const html = page.locator('html');
    await expect(html).toHaveClass(/preset-minimal/);

    // Verify accent color is black for high contrast with retry logic
    let accentColor;
    try {
      accentColor = await page.evaluate(() => {
        return getComputedStyle(document.documentElement).getPropertyValue('--preset-accent').trim();
      });
    } catch (error) {
      // If execution context was destroyed, wait and retry
      await page.waitForTimeout(1000);
      accentColor = await page.evaluate(() => {
        return getComputedStyle(document.documentElement).getPropertyValue('--preset-accent').trim();
      });
    }

    expect(accentColor).toBe('0 0 0');
  });

  test('should switch to Enterprise preset and apply corporate blue colors', async ({ page }) => {
    // Open theme toggle and select Enterprise preset
    const themeToggle = page.locator('[data-testid="theme-toggle"]').or(page.locator('[aria-label*="theme"]')).first();
    await themeToggle.click();

    const dropdownMenu = page.locator('[role="menu"]');
    await dropdownMenu.locator('text=Enterprise').first().click();
    
    // Wait for theme application
    await page.waitForTimeout(500);
    
    // Check that enterprise preset class is applied
    const html = page.locator('html');
    await expect(html).toHaveClass(/preset-enterprise/);
    
    // Verify accent color is corporate blue
    const accentColor = await page.evaluate(() => {
      return getComputedStyle(document.documentElement).getPropertyValue('--preset-accent').trim();
    });
    
    expect(accentColor).toBe('37 99 235');
  });

  test('should persist preset selection in localStorage', async ({ page }) => {
    // Select Creative preset
    const themeToggle = page.locator('[data-testid="theme-toggle"]').or(page.locator('[aria-label*="theme"]')).first();
    await themeToggle.click();

    const dropdownMenu = page.locator('[role="menu"]');
    await dropdownMenu.locator('text=Creative').first().click();

    // Wait for theme application and transitions to complete
    await page.waitForTimeout(1500);

    // Wait for page to be stable
    await page.waitForLoadState('networkidle');

    // Reload page
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('[data-testid="theme-toggle"]');
    
    // Check that Creative preset is still active
    const html = page.locator('html');
    await expect(html).toHaveClass(/preset-creative/);
    
    // Verify localStorage contains the preset
    const storedPreset = await page.evaluate(() => {
      return localStorage.getItem('blackveil-design-mind-theme-preset');
    });
    
    expect(storedPreset).toBe('creative');
  });

  test('should work correctly with both light and dark themes', async ({ page }) => {
    // Test with light theme
    const themeToggle = page.locator('[data-testid="theme-toggle"]').or(page.locator('[aria-label*="theme"]')).first();
    await themeToggle.click();

    let dropdownMenu = page.locator('[role="menu"]');
    await dropdownMenu.locator('text=Light').first().click();

    await themeToggle.click();
    dropdownMenu = page.locator('[role="menu"]');
    await dropdownMenu.locator('text=Minimal').first().click();
    
    await page.waitForTimeout(500);
    
    // Check light theme with minimal preset
    let html = page.locator('html');
    await expect(html).toHaveClass(/light/);
    await expect(html).toHaveClass(/preset-minimal/);
    
    // Switch to dark theme
    await themeToggle.click();
    dropdownMenu = page.locator('[role="menu"]');
    await dropdownMenu.locator('text=Dark').first().click();

    // Wait for theme application and transitions to complete
    await page.waitForTimeout(1500);

    // Wait for page to be stable
    await page.waitForLoadState('networkidle');
    
    // Check dark theme with minimal preset (should have different accent colors)
    html = page.locator('html');
    await expect(html).toHaveClass(/dark/);
    await expect(html).toHaveClass(/preset-minimal/);
    
    // Verify accent color changes for dark mode in minimal preset with retry logic
    let accentColor;
    try {
      accentColor = await page.evaluate(() => {
        return getComputedStyle(document.documentElement).getPropertyValue('--preset-accent').trim();
      });
    } catch (error) {
      // If execution context was destroyed, wait and retry
      await page.waitForTimeout(1000);
      accentColor = await page.evaluate(() => {
        return getComputedStyle(document.documentElement).getPropertyValue('--preset-accent').trim();
      });
    }

    expect(accentColor).toBe('255 255 255'); // White for dark mode minimal
  });

  test('should maintain WCAG accessibility compliance across all presets', async ({ page }) => {
    const presets = ['Professional', 'Creative', 'Minimal', 'Enterprise'];
    
    for (const preset of presets) {
      // Select preset
      const themeToggle = page.locator('[data-testid="theme-toggle"]').or(page.locator('[aria-label*="theme"]')).first();
      await themeToggle.click();

      const dropdownMenu = page.locator('[role="menu"]');
      await dropdownMenu.locator(`text=${preset}`).first().click();
      await page.waitForTimeout(500);
      
      // Check contrast ratios for key elements
      const contrastRatio = await page.evaluate(() => {
        const sage = getComputedStyle(document.documentElement).getPropertyValue('--aone-sage').trim();
        const background = getComputedStyle(document.documentElement).getPropertyValue('--aone-warm-white').trim();
        
        // Simple contrast check (actual implementation would use proper contrast calculation)
        return sage !== background; // Basic check that colors are different
      });
      
      expect(contrastRatio).toBe(true);
    }
  });
});
