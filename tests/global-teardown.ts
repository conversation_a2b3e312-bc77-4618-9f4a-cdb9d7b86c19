import { FullConfig } from '@playwright/test';
import { NetworkMonitor } from './utils/network-monitor';
import fs from 'fs';
import path from 'path';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global test teardown with performance reporting...');

  try {
    // Generate comprehensive performance report
    const networkMonitor = NetworkMonitor.getInstance();
    const performanceReport = networkMonitor.generatePerformanceReport();
    console.log(performanceReport);

    // Export metrics to file for analysis
    const metricsData = networkMonitor.exportMetrics();
    const resultsDir = 'test-results';

    // Ensure results directory exists
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }

    // Save performance metrics
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const metricsFile = path.join(resultsDir, `performance-metrics-${timestamp}.json`);
    fs.writeFileSync(metricsFile, metricsData);
    console.log(`📊 Performance metrics saved to: ${metricsFile}`);

    // Generate summary statistics
    const metrics = networkMonitor.getMetrics();
    if (metrics.length > 0) {
      const totalTests = metrics.length;
      const successfulTests = metrics.filter(m => m.success).length;
      const failedTests = totalTests - successfulTests;
      const avgDuration = metrics.reduce((sum, m) => sum + m.duration, 0) / totalTests;
      const totalRetries = metrics.reduce((sum, m) => sum + m.retryCount, 0);

      console.log('\n📈 Test Session Summary:');
      console.log(`  Total Tests: ${totalTests}`);
      console.log(`  Successful: ${successfulTests} (${((successfulTests/totalTests)*100).toFixed(1)}%)`);
      console.log(`  Failed: ${failedTests}`);
      console.log(`  Average Duration: ${avgDuration.toFixed(2)}ms`);
      console.log(`  Total Retries: ${totalRetries}`);

      // Network quality distribution
      const networkQualities = metrics.map(m => m.networkCondition.quality);
      const qualityDistribution = networkQualities.reduce((acc, quality) => {
        acc[quality] = (acc[quality] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      console.log('\n🌐 Network Quality Distribution:');
      Object.entries(qualityDistribution).forEach(([quality, count]) => {
        const percentage = ((count / totalTests) * 100).toFixed(1);
        console.log(`  ${quality}: ${count} tests (${percentage}%)`);
      });
    }

  } catch (error) {
    console.error('⚠️ Error during teardown reporting:', error);
  }

  console.log('✅ Global test teardown completed with performance analysis');
}

export default globalTeardown;
