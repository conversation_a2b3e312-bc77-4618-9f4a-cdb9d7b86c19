import { test, expect, Page, BrowserContext } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';

test.describe('Real-time Collaboration Features', () => {
  let testHelpers: TestHelpers;
  let context1: BrowserContext;
  let context2: BrowserContext;
  let page1: Page;
  let page2: Page;

  test.beforeAll(async ({ browser }) => {
    // Create two browser contexts to simulate multiple users
    context1 = await browser.newContext();
    context2 = await browser.newContext();
    
    page1 = await context1.newPage();
    page2 = await context2.newPage();
    
    testHelpers = new TestHelpers(page1);
  });

  test.afterAll(async () => {
    await context1.close();
    await context2.close();
  });

  test.describe('Live Cursor Tracking', () => {
    test('should display live cursors from multiple users', async () => {
      // Navigate both users to collaboration tab
      await page1.goto('/');
      await page2.goto('/');
      
      await testHelpers.navigateToTab('collaboration');
      await page2.click('[data-testid="tab-collaboration"], .tab-collaboration, [role="tab"]:has-text("Team")');
      
      // Wait for collaboration dashboard to load
      await expect(page1.locator('[data-testid="collaboration-dashboard"], .collaboration-dashboard')).toBeVisible({ timeout: 10000 });
      await expect(page2.locator('[data-testid="collaboration-dashboard"], .collaboration-dashboard')).toBeVisible({ timeout: 10000 });
      
      // Simulate mouse movement on page1
      const canvas1 = page1.locator('[data-project-canvas], .project-canvas, .analysis-results').first();
      await canvas1.hover();
      
      // Check if cursor appears on page2
      const cursorIndicator = page2.locator('[data-testid="cursor-indicator"], .cursor-indicator, .live-cursor');
      await expect(cursorIndicator).toBeVisible({ timeout: 5000 });
      
      // Verify cursor has user identification
      const userLabel = page2.locator('[data-testid="cursor-user-label"], .cursor-user-label');
      await expect(userLabel).toBeVisible({ timeout: 3000 });
    });

    test('should show cursor trails when enabled', async () => {
      await page1.goto('/');
      await testHelpers.navigateToTab('collaboration');
      
      // Enable cursor trails if there's a setting
      const trailsToggle = page1.locator('[data-testid="cursor-trails-toggle"], .cursor-trails-toggle');
      if (await trailsToggle.isVisible()) {
        await trailsToggle.click();
      }
      
      // Move cursor to create trail
      const canvas = page1.locator('[data-project-canvas], .project-canvas').first();
      await canvas.hover({ position: { x: 100, y: 100 } });
      await page1.mouse.move(200, 200);
      await page1.mouse.move(300, 300);
      
      // Check for trail elements on page2
      const trailElements = page2.locator('[data-testid="cursor-trail"], .cursor-trail');
      await expect(trailElements.first()).toBeVisible({ timeout: 3000 });
    });
  });

  test.describe('User Presence System', () => {
    test('should display online users in presence indicator', async () => {
      await page1.goto('/');
      await page2.goto('/');
      
      await testHelpers.navigateToTab('collaboration');
      await page2.click('[data-testid="tab-collaboration"], .tab-collaboration, [role="tab"]:has-text("Team")');
      
      // Check presence indicators
      const presenceIndicator = page1.locator('[data-testid="user-presence"], .user-presence, .online-users');
      await expect(presenceIndicator).toBeVisible({ timeout: 10000 });
      
      // Should show at least 1 user online (the current user)
      const userAvatars = page1.locator('[data-testid="user-avatar"], .user-avatar, .presence-avatar');
      await expect(userAvatars.first()).toBeVisible({ timeout: 5000 });
      
      // Check online count
      const onlineCount = page1.locator('[data-testid="online-count"], .online-count');
      if (await onlineCount.isVisible()) {
        const countText = await onlineCount.textContent();
        expect(countText).toMatch(/\d+\s+online/);
      }
    });

    test('should show user activity status', async () => {
      await page1.goto('/');
      await testHelpers.navigateToTab('collaboration');
      
      // Look for activity indicators
      const activityIndicator = page1.locator('[data-testid="activity-indicator"], .activity-indicator');
      if (await activityIndicator.isVisible()) {
        await expect(activityIndicator).toBeVisible();
      }
      
      // Check for status badges
      const statusBadge = page1.locator('[data-testid="user-status"], .user-status, .status-badge');
      if (await statusBadge.isVisible()) {
        const statusText = await statusBadge.textContent();
        expect(statusText).toMatch(/(online|away|viewing|editing)/i);
      }
    });
  });

  test.describe('Real-time Annotations', () => {
    test('should create and sync annotations across users', async () => {
      await page1.goto('/');
      await page2.goto('/');
      
      // Upload a test file first
      await testHelpers.uploadTestFile();
      await testHelpers.waitForAnalysisCompletion();
      
      await testHelpers.navigateToTab('collaboration');
      await page2.click('[data-testid="tab-collaboration"], .tab-collaboration, [role="tab"]:has-text("Team")');
      
      // Switch to annotation mode on page1
      const annotateMode = page1.locator('[data-testid="mode-annotate"], .mode-annotate, [data-mode="annotate"]');
      if (await annotateMode.isVisible()) {
        await annotateMode.click();
      }
      
      // Create annotation on page1
      const canvas = page1.locator('[data-project-canvas], .project-canvas, .analysis-results').first();
      await canvas.click({ position: { x: 200, y: 150 } });
      
      // Fill annotation form if it appears
      const annotationInput = page1.locator('[data-testid="annotation-input"], .annotation-input, textarea[placeholder*="annotation"]');
      if (await annotationInput.isVisible({ timeout: 3000 })) {
        await annotationInput.fill('Test annotation for real-time sync');
        
        const createButton = page1.locator('[data-testid="create-annotation"], .create-annotation, button:has-text("Create")');
        await createButton.click();
      }
      
      // Check if annotation appears on page2
      const annotationMarker = page2.locator('[data-testid="annotation-marker"], .annotation-marker, .annotation-pin');
      await expect(annotationMarker.first()).toBeVisible({ timeout: 10000 });
    });

    test('should support annotation threading and replies', async () => {
      await page1.goto('/');
      await testHelpers.navigateToTab('collaboration');
      
      // Look for existing annotations
      const annotationMarker = page1.locator('[data-testid="annotation-marker"], .annotation-marker').first();
      if (await annotationMarker.isVisible()) {
        await annotationMarker.click();
        
        // Look for reply option
        const replyButton = page1.locator('[data-testid="reply-annotation"], .reply-annotation, button:has-text("Reply")');
        if (await replyButton.isVisible()) {
          await replyButton.click();
          
          const replyInput = page1.locator('[data-testid="reply-input"], .reply-input, textarea[placeholder*="reply"]');
          if (await replyInput.isVisible()) {
            await replyInput.fill('This is a reply to the annotation');
            
            const submitReply = page1.locator('[data-testid="submit-reply"], .submit-reply, button:has-text("Reply")');
            await submitReply.click();
            
            // Verify reply appears
            const replyContent = page1.locator('[data-testid="annotation-reply"], .annotation-reply');
            await expect(replyContent.first()).toBeVisible({ timeout: 5000 });
          }
        }
      }
    });
  });

  test.describe('Version Control System', () => {
    test('should create and display version snapshots', async () => {
      await page1.goto('/');
      await testHelpers.uploadTestFile();
      await testHelpers.waitForAnalysisCompletion();
      await testHelpers.navigateToTab('collaboration');
      
      // Look for version control section
      const versionControl = page1.locator('[data-testid="version-control"], .version-control');
      if (await versionControl.isVisible()) {
        // Try to create a snapshot
        const createSnapshot = page1.locator('[data-testid="create-snapshot"], .create-snapshot, button:has-text("Snapshot")');
        if (await createSnapshot.isVisible()) {
          await createSnapshot.click();
          
          // Fill snapshot form
          const titleInput = page1.locator('[data-testid="snapshot-title"], .snapshot-title, input[placeholder*="title"]');
          if (await titleInput.isVisible()) {
            await titleInput.fill('Test Snapshot');
            
            const createButton = page1.locator('[data-testid="create-version"], .create-version, button:has-text("Create")');
            await createButton.click();
            
            // Verify snapshot appears in history
            const versionItem = page1.locator('[data-testid="version-item"], .version-item');
            await expect(versionItem.first()).toBeVisible({ timeout: 10000 });
          }
        }
      }
    });

    test('should support version comparison', async () => {
      await page1.goto('/');
      await testHelpers.navigateToTab('collaboration');
      
      // Look for version history
      const historyTab = page1.locator('[data-testid="history-tab"], .history-tab, [role="tab"]:has-text("History")');
      if (await historyTab.isVisible()) {
        await historyTab.click();
        
        // Select versions for comparison
        const versionItems = page1.locator('[data-testid="version-item"], .version-item');
        const versionCount = await versionItems.count();
        
        if (versionCount >= 2) {
          await versionItems.nth(0).click();
          await versionItems.nth(1).click();
          
          // Look for compare button
          const compareButton = page1.locator('[data-testid="compare-versions"], .compare-versions, button:has-text("Compare")');
          if (await compareButton.isVisible()) {
            await compareButton.click();
            
            // Verify comparison view
            const comparisonView = page1.locator('[data-testid="version-comparison"], .version-comparison');
            await expect(comparisonView).toBeVisible({ timeout: 5000 });
          }
        }
      }
    });
  });

  test.describe('Real-time Comments', () => {
    test('should sync comments across multiple users', async () => {
      await page1.goto('/');
      await page2.goto('/');
      
      await testHelpers.uploadTestFile();
      await testHelpers.waitForAnalysisCompletion();
      
      await testHelpers.navigateToTab('collaboration');
      await page2.click('[data-testid="tab-collaboration"], .tab-collaboration, [role="tab"]:has-text("Team")');
      
      // Switch to comments tab
      const commentsTab = page1.locator('[data-testid="comments-tab"], .comments-tab, [role="tab"]:has-text("Comments")');
      if (await commentsTab.isVisible()) {
        await commentsTab.click();
        
        // Add a comment
        const commentInput = page1.locator('[data-testid="comment-input"], .comment-input, textarea[placeholder*="comment"]');
        if (await commentInput.isVisible()) {
          await commentInput.fill('Real-time comment test');
          
          const submitComment = page1.locator('[data-testid="submit-comment"], .submit-comment, button:has-text("Comment")');
          await submitComment.click();
          
          // Check if comment appears on page2
          await page2.click('[data-testid="comments-tab"], .comments-tab, [role="tab"]:has-text("Comments")');
          const commentContent = page2.locator('[data-testid="comment-content"], .comment-content');
          await expect(commentContent.first()).toBeVisible({ timeout: 10000 });
        }
      }
    });

    test('should support @mentions in comments', async () => {
      await page1.goto('/');
      await testHelpers.navigateToTab('collaboration');
      
      const commentsTab = page1.locator('[data-testid="comments-tab"], .comments-tab, [role="tab"]:has-text("Comments")');
      if (await commentsTab.isVisible()) {
        await commentsTab.click();
        
        const commentInput = page1.locator('[data-testid="comment-input"], .comment-input, textarea');
        if (await commentInput.isVisible()) {
          await commentInput.fill('@testuser This is a mention test');
          
          const submitComment = page1.locator('[data-testid="submit-comment"], .submit-comment, button:has-text("Comment")');
          await submitComment.click();
          
          // Verify mention highlighting
          const mentionHighlight = page1.locator('[data-testid="mention-highlight"], .mention-highlight, .mention');
          await expect(mentionHighlight.first()).toBeVisible({ timeout: 5000 });
        }
      }
    });
  });

  test.describe('Collaboration Performance', () => {
    test('should maintain good performance with multiple users', async () => {
      const startTime = Date.now();
      
      await page1.goto('/');
      await page2.goto('/');
      
      await testHelpers.navigateToTab('collaboration');
      await page2.click('[data-testid="tab-collaboration"], .tab-collaboration, [role="tab"]:has-text("Team")');
      
      // Wait for collaboration features to load
      await expect(page1.locator('[data-testid="collaboration-dashboard"], .collaboration-dashboard')).toBeVisible({ timeout: 10000 });
      await expect(page2.locator('[data-testid="collaboration-dashboard"], .collaboration-dashboard')).toBeVisible({ timeout: 10000 });
      
      const loadTime = Date.now() - startTime;
      expect(loadTime).toBeLessThan(15000); // Should load within 15 seconds
      
      // Test cursor update performance
      const cursorStartTime = Date.now();
      const canvas = page1.locator('[data-project-canvas], .project-canvas').first();
      await canvas.hover();
      
      // Check if cursor appears on page2 quickly
      const cursorIndicator = page2.locator('[data-testid="cursor-indicator"], .cursor-indicator, .live-cursor');
      await expect(cursorIndicator).toBeVisible({ timeout: 3000 });
      
      const cursorUpdateTime = Date.now() - cursorStartTime;
      expect(cursorUpdateTime).toBeLessThan(3000); // Cursor should appear within 3 seconds
    });

    test('should handle connection interruptions gracefully', async () => {
      await page1.goto('/');
      await testHelpers.navigateToTab('collaboration');
      
      // Check initial connection status
      const connectionStatus = page1.locator('[data-testid="connection-status"], .connection-status');
      if (await connectionStatus.isVisible()) {
        await expect(connectionStatus).toContainText(/connected/i);
      }
      
      // Simulate network interruption by going offline
      await page1.context().setOffline(true);
      
      // Wait a moment
      await page1.waitForTimeout(2000);
      
      // Check disconnected status
      if (await connectionStatus.isVisible()) {
        await expect(connectionStatus).toContainText(/disconnect/i, { timeout: 5000 });
      }
      
      // Restore connection
      await page1.context().setOffline(false);
      
      // Check reconnection
      if (await connectionStatus.isVisible()) {
        await expect(connectionStatus).toContainText(/connect/i, { timeout: 10000 });
      }
    });
  });
});
