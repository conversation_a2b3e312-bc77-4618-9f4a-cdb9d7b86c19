import { test, expect, Page, BrowserContext } from '@playwright/test';
import { v4 as uuidv4 } from 'uuid';

// Test configuration for mesh networking
const MESH_TEST_CONFIG = {
  maxPeers: 6,
  connectionTimeout: 10000,
  qualityCheckInterval: 5000,
  testProjectId: `mesh-test-${uuidv4()}`,
  baseUrl: process.env.VITE_API_BASE_URL || 'http://localhost:3001'
};

// Helper function to create authenticated user session
async function createAuthenticatedSession(context: BrowserContext, userIndex: number): Promise<Page> {
  const page = await context.newPage();
  
  // Navigate to login page
  await page.goto('/login');
  
  // Login with test user
  await page.fill('[data-testid="email-input"]', `meshuser${userIndex}@test.com`);
  await page.fill('[data-testid="password-input"]', 'testpassword123');
  await page.click('[data-testid="login-button"]');
  
  // Wait for authentication
  await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });
  
  return page;
}

// Helper function to join mesh network
async function joinMeshNetwork(page: Page, projectId: string): Promise<void> {
  // Navigate to project
  await page.goto(`/projects/${projectId}`);
  
  // Wait for collaboration components to load
  await page.waitForSelector('[data-testid="collaboration-panel"]', { timeout: 10000 });
  
  // Enable mesh networking
  await page.click('[data-testid="enable-mesh-networking"]');
  
  // Wait for mesh connection
  await page.waitForSelector('[data-testid="mesh-status-connected"]', { timeout: 15000 });
}

test.describe('Advanced P2P Mesh Networking', () => {
  test.describe.configure({ mode: 'parallel' });

  test('should initialize mesh network manager on server startup', async ({ request }) => {
    const response = await request.get(`${MESH_TEST_CONFIG.baseUrl}/api/health`);
    expect(response.ok()).toBeTruthy();
    
    const healthData = await response.json();
    expect(healthData.services).toHaveProperty('meshNetworkManager');
    expect(healthData.services.meshNetworkManager.status).toBe('operational');
  });

  test('should handle mesh network join and leave operations', async ({ browser }) => {
    const context = await browser.newContext();
    const page = await createAuthenticatedSession(context, 1);
    
    try {
      // Join mesh network
      await joinMeshNetwork(page, MESH_TEST_CONFIG.testProjectId);
      
      // Verify mesh status
      const meshStatus = await page.textContent('[data-testid="mesh-peer-count"]');
      expect(parseInt(meshStatus || '0')).toBeGreaterThanOrEqual(1);
      
      // Leave mesh network
      await page.click('[data-testid="leave-mesh-network"]');
      await page.waitForSelector('[data-testid="mesh-status-disconnected"]', { timeout: 10000 });
      
      // Verify disconnection
      const disconnectedStatus = await page.textContent('[data-testid="mesh-status"]');
      expect(disconnectedStatus).toContain('Disconnected');
      
    } finally {
      await context.close();
    }
  });

  test('should establish P2P connections between multiple peers', async ({ browser }) => {
    const contexts = await Promise.all([
      browser.newContext(),
      browser.newContext(),
      browser.newContext()
    ]);
    
    const pages = await Promise.all([
      createAuthenticatedSession(contexts[0], 1),
      createAuthenticatedSession(contexts[1], 2),
      createAuthenticatedSession(contexts[2], 3)
    ]);
    
    try {
      // All peers join the same mesh network
      await Promise.all(pages.map(page => 
        joinMeshNetwork(page, MESH_TEST_CONFIG.testProjectId)
      ));
      
      // Wait for mesh formation
      await Promise.all(pages.map(page => 
        page.waitForFunction(() => {
          const peerCount = document.querySelector('[data-testid="mesh-peer-count"]')?.textContent;
          return parseInt(peerCount || '0') >= 3;
        }, { timeout: 20000 })
      ));
      
      // Verify each peer sees the others
      for (const page of pages) {
        const peerCount = await page.textContent('[data-testid="mesh-peer-count"]');
        expect(parseInt(peerCount || '0')).toBe(3);
        
        const connectionQuality = await page.textContent('[data-testid="mesh-connection-quality"]');
        expect(parseFloat(connectionQuality || '0')).toBeGreaterThan(0.5);
      }
      
    } finally {
      await Promise.all(contexts.map(context => context.close()));
    }
  });

  test('should synchronize cursor movements across mesh peers', async ({ browser }) => {
    const contexts = await Promise.all([
      browser.newContext(),
      browser.newContext()
    ]);
    
    const [page1, page2] = await Promise.all([
      createAuthenticatedSession(contexts[0], 1),
      createAuthenticatedSession(contexts[1], 2)
    ]);
    
    try {
      // Both peers join mesh network
      await Promise.all([
        joinMeshNetwork(page1, MESH_TEST_CONFIG.testProjectId),
        joinMeshNetwork(page2, MESH_TEST_CONFIG.testProjectId)
      ]);
      
      // Wait for mesh connection
      await Promise.all([
        page1.waitForSelector('[data-testid="mesh-peer-count"]:has-text("2")'),
        page2.waitForSelector('[data-testid="mesh-peer-count"]:has-text("2")')
      ]);
      
      // Move cursor on page1
      await page1.mouse.move(300, 200);
      await page1.mouse.move(400, 300);
      
      // Verify cursor position is synchronized on page2
      await page2.waitForSelector('[data-testid="remote-cursor"]', { timeout: 5000 });
      
      const cursorPosition = await page2.evaluate(() => {
        const cursor = document.querySelector('[data-testid="remote-cursor"]') as HTMLElement;
        return cursor ? {
          x: parseInt(cursor.style.left || '0'),
          y: parseInt(cursor.style.top || '0')
        } : null;
      });
      
      expect(cursorPosition).toBeTruthy();
      expect(cursorPosition!.x).toBeCloseTo(400, 50);
      expect(cursorPosition!.y).toBeCloseTo(300, 50);
      
    } finally {
      await Promise.all(contexts.map(context => context.close()));
    }
  });

  test('should handle mesh topology optimization', async ({ browser }) => {
    const context = await browser.newContext();
    const page = await createAuthenticatedSession(context, 1);
    
    try {
      await joinMeshNetwork(page, MESH_TEST_CONFIG.testProjectId);
      
      // Trigger topology optimization
      await page.click('[data-testid="optimize-mesh-topology"]');
      
      // Wait for optimization completion
      await page.waitForSelector('[data-testid="optimization-completed"]', { timeout: 15000 });
      
      // Verify optimization results
      const optimizationResult = await page.textContent('[data-testid="optimization-result"]');
      expect(optimizationResult).toContain('optimizations applied');
      
      // Check that network health improved or maintained
      const networkHealth = await page.textContent('[data-testid="mesh-network-health"]');
      expect(parseFloat(networkHealth || '0')).toBeGreaterThan(0.6);
      
    } finally {
      await context.close();
    }
  });

  test('should maintain connection quality monitoring', async ({ browser }) => {
    const context = await browser.newContext();
    const page = await createAuthenticatedSession(context, 1);
    
    try {
      await joinMeshNetwork(page, MESH_TEST_CONFIG.testProjectId);
      
      // Wait for quality monitoring to start
      await page.waitForSelector('[data-testid="connection-quality-metrics"]', { timeout: 10000 });
      
      // Monitor quality updates over time
      const qualityUpdates: number[] = [];
      
      for (let i = 0; i < 3; i++) {
        await page.waitForTimeout(MESH_TEST_CONFIG.qualityCheckInterval);
        
        const quality = await page.textContent('[data-testid="mesh-connection-quality"]');
        qualityUpdates.push(parseFloat(quality || '0'));
      }
      
      // Verify quality monitoring is active
      expect(qualityUpdates.length).toBe(3);
      expect(qualityUpdates.every(q => q >= 0 && q <= 1)).toBeTruthy();
      
      // Check latency monitoring
      const latency = await page.textContent('[data-testid="mesh-latency"]');
      expect(parseInt(latency || '0')).toBeGreaterThan(0);
      expect(parseInt(latency || '0')).toBeLessThan(1000); // Should be under 1 second
      
    } finally {
      await context.close();
    }
  });

  test('should handle peer disconnection gracefully', async ({ browser }) => {
    const contexts = await Promise.all([
      browser.newContext(),
      browser.newContext(),
      browser.newContext()
    ]);
    
    const pages = await Promise.all([
      createAuthenticatedSession(contexts[0], 1),
      createAuthenticatedSession(contexts[1], 2),
      createAuthenticatedSession(contexts[2], 3)
    ]);
    
    try {
      // All peers join mesh network
      await Promise.all(pages.map(page => 
        joinMeshNetwork(page, MESH_TEST_CONFIG.testProjectId)
      ));
      
      // Wait for full mesh formation
      await Promise.all(pages.map(page => 
        page.waitForSelector('[data-testid="mesh-peer-count"]:has-text("3")')
      ));
      
      // Disconnect one peer
      await contexts[2].close();
      
      // Verify remaining peers detect disconnection
      await Promise.all([
        pages[0].waitForSelector('[data-testid="mesh-peer-count"]:has-text("2")'),
        pages[1].waitForSelector('[data-testid="mesh-peer-count"]:has-text("2")')
      ]);
      
      // Verify mesh rebalancing
      await Promise.all([
        pages[0].waitForSelector('[data-testid="mesh-rebalanced"]'),
        pages[1].waitForSelector('[data-testid="mesh-rebalanced"]')
      ]);
      
      // Check that remaining connections are still healthy
      for (const page of pages.slice(0, 2)) {
        const health = await page.textContent('[data-testid="mesh-network-health"]');
        expect(parseFloat(health || '0')).toBeGreaterThan(0.5);
      }
      
    } finally {
      await Promise.all(contexts.slice(0, 2).map(context => context.close()));
    }
  });

  test('should integrate with 3D visualization for collaborative viewing', async ({ browser }) => {
    const contexts = await Promise.all([
      browser.newContext(),
      browser.newContext()
    ]);
    
    const [page1, page2] = await Promise.all([
      createAuthenticatedSession(contexts[0], 1),
      createAuthenticatedSession(contexts[1], 2)
    ]);
    
    try {
      // Both peers join mesh network
      await Promise.all([
        joinMeshNetwork(page1, MESH_TEST_CONFIG.testProjectId),
        joinMeshNetwork(page2, MESH_TEST_CONFIG.testProjectId)
      ]);
      
      // Navigate to 3D visualization
      await Promise.all([
        page1.click('[data-testid="3d-visualization-tab"]'),
        page2.click('[data-testid="3d-visualization-tab"]')
      ]);
      
      // Wait for 3D scene to load
      await Promise.all([
        page1.waitForSelector('[data-testid="3d-scene-loaded"]'),
        page2.waitForSelector('[data-testid="3d-scene-loaded"]')
      ]);
      
      // Interact with 3D scene on page1
      await page1.click('[data-testid="cabinet-model-1"]');
      
      // Verify selection is synchronized on page2
      await page2.waitForSelector('[data-testid="cabinet-model-1"][data-selected="true"]', { timeout: 5000 });
      
      // Verify scene update was transmitted via mesh
      const sceneUpdateReceived = await page2.evaluate(() => {
        return window.meshCollaboration?.lastSceneUpdate?.type === 'cabinet-selection';
      });
      
      expect(sceneUpdateReceived).toBeTruthy();
      
    } finally {
      await Promise.all(contexts.map(context => context.close()));
    }
  });

  test('should support collaborative quotation development', async ({ browser }) => {
    const contexts = await Promise.all([
      browser.newContext(),
      browser.newContext()
    ]);
    
    const [page1, page2] = await Promise.all([
      createAuthenticatedSession(contexts[0], 1),
      createAuthenticatedSession(contexts[1], 2)
    ]);
    
    try {
      // Both peers join mesh network
      await Promise.all([
        joinMeshNetwork(page1, MESH_TEST_CONFIG.testProjectId),
        joinMeshNetwork(page2, MESH_TEST_CONFIG.testProjectId)
      ]);
      
      // Navigate to quotation section
      await Promise.all([
        page1.click('[data-testid="quotation-tab"]'),
        page2.click('[data-testid="quotation-tab"]')
      ]);
      
      // Modify quote on page1
      await page1.click('[data-testid="edit-quote-tier-premium"]');
      await page1.fill('[data-testid="quote-custom-price"]', '15000');
      await page1.click('[data-testid="save-quote-changes"]');
      
      // Verify quote update is synchronized on page2
      await page2.waitForSelector('[data-testid="quote-tier-premium"]:has-text("$15,000")', { timeout: 5000 });
      
      // Verify quote update notification
      await page2.waitForSelector('[data-testid="quote-update-notification"]');
      
      const notificationText = await page2.textContent('[data-testid="quote-update-notification"]');
      expect(notificationText).toContain('Quote updated by');
      
    } finally {
      await Promise.all(contexts.map(context => context.close()));
    }
  });

  test('should maintain ~97-99% success rate under mesh network load', async ({ browser }) => {
    const testRuns = 20;
    const successfulRuns: boolean[] = [];
    
    for (let i = 0; i < testRuns; i++) {
      const context = await browser.newContext();
      
      try {
        const page = await createAuthenticatedSession(context, 1);
        await joinMeshNetwork(page, `${MESH_TEST_CONFIG.testProjectId}-${i}`);
        
        // Verify mesh connection within timeout
        await page.waitForSelector('[data-testid="mesh-status-connected"]', { timeout: 10000 });
        
        // Perform basic mesh operations
        await page.mouse.move(100 + i * 10, 100 + i * 10);
        await page.waitForTimeout(1000);
        
        // Verify mesh is still healthy
        const health = await page.textContent('[data-testid="mesh-network-health"]');
        const isHealthy = parseFloat(health || '0') > 0.5;
        
        successfulRuns.push(isHealthy);
        
      } catch (error) {
        console.error(`Test run ${i + 1} failed:`, error);
        successfulRuns.push(false);
      } finally {
        await context.close();
      }
    }
    
    const successRate = successfulRuns.filter(Boolean).length / testRuns;
    console.log(`Mesh networking success rate: ${(successRate * 100).toFixed(1)}%`);
    
    // Verify success rate meets requirement
    expect(successRate).toBeGreaterThanOrEqual(0.97);
  });
});
