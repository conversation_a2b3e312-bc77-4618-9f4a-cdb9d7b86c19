import { test, expect } from '@playwright/test';
import { TestHelpers } from './utils/test-helpers';

test.describe('Comprehensive Dark Mode Enhancement', () => {
  let testHelpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    testHelpers = new TestHelpers(page);
    await page.goto('/');
    await testHelpers.waitForAppLoad();
  });

  test.describe('Theme Detection and System Preferences', () => {
    test('should detect system dark mode preference', async ({ page }) => {
      // Emulate dark mode system preference
      await page.emulateMedia({ colorScheme: 'dark' });
      await page.reload();
      await testHelpers.waitForAppLoad();

      // Check if dark mode is applied
      const htmlElement = page.locator('html');
      await expect(htmlElement).toHaveClass(/dark/);
    });

    test('should detect system light mode preference', async ({ page }) => {
      // Emulate light mode system preference
      await page.emulateMedia({ colorScheme: 'light' });
      await page.reload();
      await testHelpers.waitForAppLoad();

      // Check if light mode is applied (no dark class)
      const htmlElement = page.locator('html');
      await expect(htmlElement).not.toHaveClass(/dark/);
    });

    test('should persist theme preference in localStorage', async ({ page }) => {
      // Set dark mode manually
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();
      
      const darkModeOption = page.locator('text=Dark Mode');
      await darkModeOption.click();

      // Reload page and check if preference is persisted
      await page.reload();
      await testHelpers.waitForAppLoad();

      const htmlElement = page.locator('html');
      await expect(htmlElement).toHaveClass(/dark/);
    });
  });

  test.describe('Theme Toggle Component', () => {
    test('should display theme toggle in header', async ({ page }) => {
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await expect(themeToggle).toBeVisible();
    });

    test('should open theme dropdown menu', async ({ page }) => {
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();

      // Check for theme options
      await expect(page.locator('text=Light Mode')).toBeVisible();
      await expect(page.locator('text=Dark Mode')).toBeVisible();
      await expect(page.locator('text=System Theme')).toBeVisible();
    });

    test('should switch to dark mode', async ({ page }) => {
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();
      
      const darkModeOption = page.locator('text=Dark Mode');
      await darkModeOption.click();

      // Verify dark mode is applied
      const htmlElement = page.locator('html');
      await expect(htmlElement).toHaveClass(/dark/);
    });

    test('should switch to light mode', async ({ page }) => {
      // First set to dark mode
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();
      await page.locator('text=Dark Mode').click();

      // Then switch to light mode
      await themeToggle.click();
      await page.locator('text=Light Mode').click();

      // Verify light mode is applied
      const htmlElement = page.locator('html');
      await expect(htmlElement).not.toHaveClass(/dark/);
    });

    test('should handle system theme option', async ({ page }) => {
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();
      
      const systemOption = page.locator('text=System Theme');
      await systemOption.click();

      // Theme should follow system preference
      await page.emulateMedia({ colorScheme: 'dark' });
      await page.waitForTimeout(100); // Allow theme to update

      const htmlElement = page.locator('html');
      await expect(htmlElement).toHaveClass(/dark/);
    });
  });

  test.describe('Dark Mode Visual Components', () => {
    test('should apply dark mode to header component', async ({ page }) => {
      // Switch to dark mode
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();
      await page.locator('text=Dark Mode').click();

      // Check header dark mode styling
      const header = page.locator('header');
      await expect(header).toBeVisible();
      
      // Verify dark mode classes are applied
      const htmlElement = page.locator('html');
      await expect(htmlElement).toHaveClass(/dark/);
    });

    test('should apply dark mode to hero section', async ({ page }) => {
      // Switch to dark mode
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();
      await page.locator('text=Dark Mode').click();

      // Check hero section elements
      const heroSection = page.locator('text=Transform Kitchen Drawings into').locator('..');
      await expect(heroSection).toBeVisible();

      // Verify dark mode is applied
      const htmlElement = page.locator('html');
      await expect(htmlElement).toHaveClass(/dark/);
    });

    test('should apply dark mode to enterprise cards', async ({ page }) => {
      // Switch to dark mode
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();
      await page.locator('text=Dark Mode').click();

      // Check for enterprise cards
      const enterpriseCards = page.locator('.aone-card-enterprise');
      if (await enterpriseCards.count() > 0) {
        await expect(enterpriseCards.first()).toBeVisible();
      }

      // Verify dark mode is applied
      const htmlElement = page.locator('html');
      await expect(htmlElement).toHaveClass(/dark/);
    });

    test('should apply dark mode to navigation elements', async ({ page }) => {
      // Switch to dark mode
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();
      await page.locator('text=Dark Mode').click();

      // Check navigation elements
      const navLinks = page.locator('.aone-nav-enterprise');
      if (await navLinks.count() > 0) {
        await expect(navLinks.first()).toBeVisible();
      }

      // Verify dark mode is applied
      const htmlElement = page.locator('html');
      await expect(htmlElement).toHaveClass(/dark/);
    });
  });

  test.describe('Dark Mode Glass Effects and Animations', () => {
    test('should apply dark mode glass effects', async ({ page }) => {
      // Switch to dark mode
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();
      await page.locator('text=Dark Mode').click();

      // Check for glass effects
      const glassElements = page.locator('.aone-glass');
      if (await glassElements.count() > 0) {
        await expect(glassElements.first()).toBeVisible();
      }

      // Verify dark mode is applied
      const htmlElement = page.locator('html');
      await expect(htmlElement).toHaveClass(/dark/);
    });

    test('should maintain animations in dark mode', async ({ page }) => {
      // Switch to dark mode
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();
      await page.locator('text=Dark Mode').click();

      // Check for animated elements
      const animatedElements = page.locator('[class*="animate-"]');
      await expect(animatedElements.first()).toBeVisible();

      // Verify animations work in dark mode
      const floatingElements = page.locator('.animate-float-gentle');
      if (await floatingElements.count() > 0) {
        await expect(floatingElements.first()).toBeVisible();
      }
    });
  });

  test.describe('Theme Transitions', () => {
    test('should have smooth transitions when switching themes', async ({ page }) => {
      // Check for transition classes
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();
      await page.locator('text=Dark Mode').click();

      // Allow transition to complete
      await page.waitForTimeout(400);

      // Verify theme was applied
      const htmlElement = page.locator('html');
      await expect(htmlElement).toHaveClass(/dark/);
    });

    test('should respect reduced motion preferences', async ({ page }) => {
      // Emulate reduced motion preference
      await page.emulateMedia({ reducedMotion: 'reduce' });
      
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();
      await page.locator('text=Dark Mode').click();

      // Theme should still switch but without transitions
      const htmlElement = page.locator('html');
      await expect(htmlElement).toHaveClass(/dark/);
    });
  });

  test.describe('Dark Mode Form Elements', () => {
    test('should apply dark mode to form inputs', async ({ page, browserName }) => {
      // Navigate to analysis page which has forms
      await page.goto('/analysis');
      await testHelpers.waitForAppLoad();

      // Switch to dark mode
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();
      await page.locator('text=Dark Mode').click();

      // Check for form elements
      const inputs = page.locator('input, textarea, select');
      if (await inputs.count() > 0) {
        await expect(inputs.first()).toBeVisible();
      }

      // Verify dark mode is applied
      const htmlElement = page.locator('html');
      await expect(htmlElement).toHaveClass(/dark/);
    });
  });

  test.describe('Cross-Browser Dark Mode Compatibility', () => {
    test('should work consistently across browsers', async ({ page, browserName }) => {
      // Switch to dark mode
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();
      await page.locator('text=Dark Mode').click();

      // Verify dark mode works regardless of browser
      const htmlElement = page.locator('html');
      await expect(htmlElement).toHaveClass(/dark/);

      // Browser-specific checks
      if (browserName === 'webkit') {
        // Safari-specific dark mode checks
        const header = page.locator('header');
        await expect(header).toBeVisible();
      }
    });
  });

  test.describe('Dark Mode Accessibility', () => {
    test('should maintain proper contrast ratios in dark mode', async ({ page }) => {
      // Switch to dark mode
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();
      await page.locator('text=Dark Mode').click();

      // Check for proper text visibility
      const headings = page.locator('h1, h2, h3');
      await expect(headings.first()).toBeVisible();

      // Verify focus states work in dark mode
      await page.keyboard.press('Tab');
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
    });

    test('should support keyboard navigation in dark mode', async ({ page }) => {
      // Switch to dark mode
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();
      await page.locator('text=Dark Mode').click();

      // Test keyboard navigation
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
    });
  });

  test.describe('Dark Mode Performance', () => {
    test('should maintain good performance with dark mode', async ({ page }) => {
      // Measure performance with theme switching
      const startTime = Date.now();
      
      const themeToggle = page.locator('[aria-label*="Theme"]');
      await themeToggle.click();
      await page.locator('text=Dark Mode').click();
      
      await page.waitForLoadState('networkidle');
      const switchTime = Date.now() - startTime;

      // Theme switch should be fast (under 1 second)
      expect(switchTime).toBeLessThan(1000);

      // Verify theme was applied
      const htmlElement = page.locator('html');
      await expect(htmlElement).toHaveClass(/dark/);
    });
  });
});
