# Blackveil Design Mind - Development Docker Compose
# ================================================
# Simple development setup with PostgreSQL and Redis

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: blackveil-postgres-dev
    restart: unless-stopped
    ports:
      - "5433:5432"
    environment:
      POSTGRES_DB: blackveil_design_mind
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: blackveil_dev_2024
    volumes:
      - postgres-dev-data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    networks:
      - blackveil-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: blackveil-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-dev-data:/data
    networks:
      - blackveil-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  # pgAdmin (Optional - for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: blackveil-pgadmin-dev
    restart: unless-stopped
    ports:
      - "8081:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: blackveil_admin_2024
    volumes:
      - pgadmin-dev-data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - blackveil-dev-network
    profiles:
      - with-pgadmin

volumes:
  postgres-dev-data:
    driver: local
  redis-dev-data:
    driver: local
  pgadmin-dev-data:
    driver: local

networks:
  blackveil-dev-network:
    driver: bridge
