import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				
					// Chart and Visualization Colors
					chart: {
						primary: 'rgb(var(--chart-primary))',
						secondary: 'rgb(var(--chart-secondary))',
						success: 'hsl(var(--chart-success))',
						warning: 'hsl(var(--chart-warning))',
						error: 'hsl(var(--chart-error))',
						info: 'hsl(var(--chart-info))'
					},
					// Collaboration Colors
					collab: {
						user1: 'hsl(var(--collab-user-1))',
						user2: 'hsl(var(--collab-user-2))',
						user3: 'hsl(var(--collab-user-3))',
						user4: 'hsl(var(--collab-user-4))',
						user5: 'rgb(var(--collab-user-5))',
						user6: 'rgb(var(--collab-user-6))',
						user7: 'rgb(var(--collab-user-7))',
						user8: 'rgb(var(--collab-user-8))'
					},
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				// A.ONE Inspired Design System Colors - Enhanced with preset support
				'aone-sage': 'rgb(var(--aone-sage))',
				'aone-sage-light': 'rgb(var(--aone-sage-light))',
				'aone-sage-dark': 'rgb(var(--aone-sage-dark))',
				'aone-cream': 'rgb(var(--aone-cream))',
				'aone-warm-white': 'rgb(var(--aone-warm-white))',
				'aone-charcoal': 'rgb(var(--aone-charcoal))',
				'aone-soft-gray': 'rgb(var(--aone-soft-gray))',
				// Preset-specific accent colors
				'preset-accent': 'rgb(var(--preset-accent))',
				'preset-accent-light': 'rgb(var(--preset-accent-light))',
				'preset-accent-dark': 'rgb(var(--preset-accent-dark))',
				// Semantic Status Colors
				status: {
					success: 'hsl(var(--status-success))',
					'success-foreground': 'hsl(var(--status-success-foreground))',
					warning: 'hsl(var(--status-warning))',
					'warning-foreground': 'hsl(var(--status-warning-foreground))',
					error: 'hsl(var(--status-error))',
					'error-foreground': 'hsl(var(--status-error-foreground))',
					info: 'hsl(var(--status-info))',
					'info-foreground': 'hsl(var(--status-info-foreground))'
				},
				// Confidence Score Colors
				confidence: {
					high: 'hsl(var(--confidence-high))',
					medium: 'hsl(var(--confidence-medium))',
					low: 'hsl(var(--confidence-low))'
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)',
				// A.ONE Design Token Border Radius Scale
				'aone-sm': 'var(--radius-sm)',
				'aone-md': 'var(--radius-md)',
				'aone-lg': 'var(--radius-lg)',
				'aone-xl': 'var(--radius-xl)'
			},
			spacing: {
				// A.ONE Design Token Spacing Scale
				'aone-xs': 'var(--spacing-xs)',
				'aone-sm': 'var(--spacing-sm)',
				'aone-md': 'var(--spacing-md)',
				'aone-lg': 'var(--spacing-lg)',
				'aone-xl': 'var(--spacing-xl)',
				'aone-2xl': 'var(--spacing-2xl)',
				'aone-3xl': 'var(--spacing-3xl)'
			},
			fontSize: {
				// A.ONE Design Token Typography Scale
				'aone-xs': ['var(--font-size-xs)', { lineHeight: 'var(--line-height-tight)' }],
				'aone-sm': ['var(--font-size-sm)', { lineHeight: 'var(--line-height-normal)' }],
				'aone-base': ['var(--font-size-base)', { lineHeight: 'var(--line-height-normal)' }],
				'aone-lg': ['var(--font-size-lg)', { lineHeight: 'var(--line-height-normal)' }],
				'aone-xl': ['var(--font-size-xl)', { lineHeight: 'var(--line-height-tight)' }],
				'aone-2xl': ['var(--font-size-2xl)', { lineHeight: 'var(--line-height-tight)' }],
				'aone-3xl': ['var(--font-size-3xl)', { lineHeight: 'var(--line-height-tight)' }],
				'aone-4xl': ['var(--font-size-4xl)', { lineHeight: 'var(--line-height-tight)' }]
			},
			fontWeight: {
				// A.ONE Design Token Font Weight Scale
				'aone-light': 'var(--font-weight-light)',
				'aone-normal': 'var(--font-weight-normal)',
				'aone-medium': 'var(--font-weight-medium)',
				'aone-semibold': 'var(--font-weight-semibold)',
				'aone-bold': 'var(--font-weight-bold)'
			},
			boxShadow: {
				// A.ONE Design Token Shadow System
				'aone-sm': 'var(--shadow-sm)',
				'aone-md': 'var(--shadow-md)',
				'aone-lg': 'var(--shadow-lg)',
				'aone-xl': 'var(--shadow-xl)'
			},
			fontSize: {
				// A.ONE Design Token Typography Scale
				'aone-xs': 'var(--font-size-xs)',
				'aone-sm': 'var(--font-size-sm)',
				'aone-base': 'var(--font-size-base)',
				'aone-lg': 'var(--font-size-lg)',
				'aone-xl': 'var(--font-size-xl)',
				'aone-2xl': 'var(--font-size-2xl)',
				'aone-3xl': 'var(--font-size-3xl)',
				'aone-4xl': 'var(--font-size-4xl)'
			},
			transitionDuration: {
				// A.ONE Design Token Animation Timing
				'aone-fast': 'var(--transition-fast)',
				'aone-normal': 'var(--transition-normal)',
				'aone-slow': 'var(--transition-slow)'
			},
			transitionTimingFunction: {
				// A.ONE Design Token Easing Functions
				'aone-ease-in-out': 'var(--ease-in-out)',
				'aone-ease-out': 'var(--ease-out)',
				'aone-ease-in': 'var(--ease-in)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				// Enhanced animations
				'fade-in': {
					'0%': { opacity: '0' },
					'100%': { opacity: '1' }
				},
				'slide-up': {
					'0%': { opacity: '0', transform: 'translateY(10px)' },
					'100%': { opacity: '1', transform: 'translateY(0)' }
				},
				'pulse-subtle': {
					'0%, 100%': { opacity: '1' },
					'50%': { opacity: '0.8' }
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				// Enhanced animations
				'fade-in': 'fade-in 0.3s ease-in-out',
				'slide-up': 'slide-up 0.3s ease-out',
				'pulse-subtle': 'pulse-subtle 2s infinite'
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
