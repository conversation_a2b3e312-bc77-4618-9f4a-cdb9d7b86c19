import { defineConfig, devices } from '@playwright/test';

/**
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './tests',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Enhanced retry logic for better reliability */
  retries: process.env.CI ? 3 : 2,
  /* Optimized workers for better resource management and stability */
  workers: process.env.CI ? 1 : 2,
  /* Enhanced test metadata for intelligent batching */
  metadata: {
    'network-monitoring': 'enabled',
    'adaptive-timeouts': 'enabled',
    'compatibility-matrix': 'enabled',
    'performance-tracking': 'enabled'
  },
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/results.xml' }],
    ['line'], // Add line reporter for better CI output
    ['./tests/utils/performance-reporter.ts', {
      apiBaseUrl: 'http://localhost:3001/api/performance-monitoring',
      featureVersion: process.env.FEATURE_VERSION,
      buildId: process.env.BUILD_ID
    }]
  ],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: 'http://localhost:8081',
    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',
    /* Take screenshot on failure */
    screenshot: 'only-on-failure',
    /* Record video on failure */
    video: 'retain-on-failure',
    /* Extra HTTP headers to be sent with every request */
    extraHTTPHeaders: {
      'Accept': 'application/json',
    },
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
        // Enhanced Chromium-specific optimizations for network stability
        navigationTimeout: 90000,
        actionTimeout: 45000,
        launchOptions: {
          args: [
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding'
          ]
        }
      },
    },

    {
      name: 'firefox',
      use: {
        ...devices['Desktop Firefox'],
        // Enhanced Firefox-specific network handling
        navigationTimeout: 120000, // Increased for Firefox stability
        actionTimeout: 60000,
        launchOptions: {
          firefoxUserPrefs: {
            'network.http.connection-timeout': 90,
            'network.http.response.timeout': 90,
            'network.http.connection-retry-timeout': 45,
            'dom.max_script_run_time': 90,
            'dom.max_chrome_script_run_time': 90,
            'network.http.max-connections': 256,
            'network.http.max-connections-per-server': 32
          }
        }
      },
    },

    {
      name: 'webkit',
      use: {
        ...devices['Desktop Safari'],
        // Enhanced WebKit-specific network handling
        navigationTimeout: 120000, // Increased for WebKit stability
        actionTimeout: 60000,
        launchOptions: {
          args: ['--disable-web-security']
        }
      },
    },

    /* Test against mobile viewports with enhanced stability */
    {
      name: 'Mobile Chrome',
      use: {
        ...devices['Pixel 5'],
        navigationTimeout: 90000, // Increased for mobile stability
        actionTimeout: 45000,
        launchOptions: {
          args: ['--disable-web-security', '--disable-background-timer-throttling']
        }
      },
    },
    {
      name: 'Mobile Safari',
      use: {
        ...devices['iPhone 12'],
        navigationTimeout: 90000, // Increased for mobile stability
        actionTimeout: 45000,
      },
    },
  ],

  /* Global setup and teardown */
  globalSetup: './tests/global-setup.ts',
  globalTeardown: './tests/global-teardown.ts',

  /* Enhanced timeouts for AI operations and network stability */
  timeout: 180000, // 3 minutes for individual tests (increased for reliability)
  expect: {
    timeout: 45000, // 45 seconds for assertions (increased for network stability)
  },

  /* Configure test environment */
  webServer: [
    {
      command: 'npm run dev',
      port: 8081,
      reuseExistingServer: !process.env.CI,
      timeout: 30000,
    },
    {
      command: 'cd server && NODE_ENV=test ENABLE_TEST_AUTH_BYPASS=true npm run dev',
      port: 3001,
      reuseExistingServer: !process.env.CI,
      timeout: 30000,
      env: {
        NODE_ENV: 'test',
        ENABLE_TEST_AUTH_BYPASS: 'true'
      }
    }
  ],
});
