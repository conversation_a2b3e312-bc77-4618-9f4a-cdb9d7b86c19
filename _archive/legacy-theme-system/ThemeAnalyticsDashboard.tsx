/**
 * Theme Analytics Dashboard - Blackveil Design Mind
 * 
 * Advanced dashboard for monitoring theme performance and usage analytics
 * Provides real-time insights into theme switching performance and user behavior
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useTheme } from '@/hooks/useTheme';
import { 
  Activity, 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  RefreshCw,
  Download,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';
import type { ThemePerformanceReport } from '@/utils/themePerformanceMonitor';

interface ThemeAnalyticsDashboardProps {
  className?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export const ThemeAnalyticsDashboard: React.FC<ThemeAnalyticsDashboardProps> = ({
  className = '',
  autoRefresh = true,
  refreshInterval = 5000
}) => {
  const { getPerformanceReport, clearPerformanceHistory, resolvedTheme } = useTheme();
  const [report, setReport] = useState<ThemePerformanceReport | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Refresh performance data
  const refreshData = async () => {
    setIsRefreshing(true);
    try {
      const newReport = getPerformanceReport();
      setReport(newReport);
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Failed to refresh theme analytics:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(refreshData, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  // Initial data load
  useEffect(() => {
    refreshData();
  }, []);

  // Export performance data
  const exportData = () => {
    if (!report) return;
    
    const dataStr = JSON.stringify({
      exportDate: new Date().toISOString(),
      report,
      metadata: {
        currentTheme: resolvedTheme,
        userAgent: navigator.userAgent,
        timestamp: Date.now()
      }
    }, null, 2);
    
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `theme-analytics-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  // Clear all data
  const clearData = () => {
    clearPerformanceHistory();
    refreshData();
  };

  // Get performance grade color
  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A': return 'text-green-600 bg-green-50 border-green-200';
      case 'B': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'C': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'D': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'F': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  // Get performance grade icon
  const getGradeIcon = (grade: string) => {
    switch (grade) {
      case 'A': return <CheckCircle className="h-4 w-4" />;
      case 'B': return <CheckCircle className="h-4 w-4" />;
      case 'C': return <Info className="h-4 w-4" />;
      case 'D': return <AlertTriangle className="h-4 w-4" />;
      case 'F': return <AlertTriangle className="h-4 w-4" />;
      default: return <Info className="h-4 w-4" />;
    }
  };

  if (!report) {
    return (
      <Card className={`aone-card-elegant ${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-aone-sage" />
            Theme Analytics Dashboard
          </CardTitle>
          <CardDescription>Loading performance data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-aone-sage" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card className="aone-card-elegant">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-aone-sage" />
                Theme Analytics Dashboard
              </CardTitle>
              <CardDescription>
                Real-time theme performance monitoring and analytics
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={refreshData}
                disabled={isRefreshing}
                className="aone-micro-interaction"
              >
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={exportData}
                className="aone-micro-interaction"
              >
                <Download className="h-4 w-4" />
                Export
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={clearData}
                className="aone-micro-interaction text-red-600 hover:text-red-700"
              >
                Clear Data
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Performance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Performance Grade */}
        <Card className="aone-card-elegant">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Performance Grade</p>
                <div className="flex items-center gap-2 mt-2">
                  <Badge className={`${getGradeColor(report.performanceGrade)} border`}>
                    {getGradeIcon(report.performanceGrade)}
                    Grade {report.performanceGrade}
                  </Badge>
                </div>
              </div>
              <BarChart3 className="h-8 w-8 text-aone-sage/60" />
            </div>
          </CardContent>
        </Card>

        {/* Average Switch Time */}
        <Card className="aone-card-elegant">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Switch Time</p>
                <p className="text-2xl font-bold text-aone-charcoal">
                  {report.averageSwitchTime.toFixed(1)}ms
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Target: &lt;100ms
                </p>
              </div>
              <Clock className="h-8 w-8 text-aone-sage/60" />
            </div>
          </CardContent>
        </Card>

        {/* Total Switches */}
        <Card className="aone-card-elegant">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Switches</p>
                <p className="text-2xl font-bold text-aone-charcoal">
                  {report.totalSwitches}
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Since last clear
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-aone-sage/60" />
            </div>
          </CardContent>
        </Card>

        {/* Performance Range */}
        <Card className="aone-card-elegant">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Performance Range</p>
                <div className="flex items-center gap-2 mt-2">
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-3 w-3 text-green-600" />
                    <span className="text-sm font-medium">{report.fastestSwitch.toFixed(1)}ms</span>
                  </div>
                  <span className="text-muted-foreground">-</span>
                  <div className="flex items-center gap-1">
                    <TrendingDown className="h-3 w-3 text-red-600" />
                    <span className="text-sm font-medium">{report.slowestSwitch.toFixed(1)}ms</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Progress */}
      <Card className="aone-card-elegant">
        <CardHeader>
          <CardTitle className="text-lg">Performance Metrics</CardTitle>
          <CardDescription>
            Theme switching performance compared to optimal targets
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span>Average Switch Time</span>
              <span>{report.averageSwitchTime.toFixed(1)}ms / 100ms target</span>
            </div>
            <Progress 
              value={Math.min((100 - report.averageSwitchTime) / 100 * 100, 100)} 
              className="h-2"
            />
          </div>
          
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span>Performance Grade</span>
              <span>Grade {report.performanceGrade}</span>
            </div>
            <Progress 
              value={report.performanceGrade === 'A' ? 100 : 
                     report.performanceGrade === 'B' ? 80 :
                     report.performanceGrade === 'C' ? 60 :
                     report.performanceGrade === 'D' ? 40 : 20} 
              className="h-2"
            />
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      {report.recommendations.length > 0 && (
        <Card className="aone-card-elegant">
          <CardHeader>
            <CardTitle className="text-lg">Performance Recommendations</CardTitle>
            <CardDescription>
              Suggestions to improve theme switching performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {report.recommendations.map((recommendation, index) => (
                <li key={index} className="flex items-start gap-2">
                  <Info className="h-4 w-4 text-aone-sage mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-aone-charcoal">{recommendation}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Footer */}
      <Card className="aone-card-elegant">
        <CardContent className="p-4">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>Last updated: {lastRefresh.toLocaleTimeString()}</span>
            <span>Current theme: {resolvedTheme}</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ThemeAnalyticsDashboard;
