/**
 * Theme Debugging Utility for Blackveil Design Mind
 * Comprehensive theme validation and debugging tools
 */

export interface ThemeDebugInfo {
  timestamp: string;
  documentReady: string;
  styleSheetsLoaded: number;
  themeClass: string[];
  cssProperties: Record<string, string>;
  computedStyles: Record<string, string>;
  errors: string[];
  warnings: string[];
}

/**
 * Comprehensive theme debugging and validation
 */
export const debugTheme = (): ThemeDebugInfo => {
  const root = document.documentElement;
  const computedStyle = getComputedStyle(root);
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check required A.ONE CSS properties
  const requiredProperties = [
    '--aone-sage',
    '--aone-sage-foreground',
    '--aone-sage-light',
    '--aone-sage-dark',
    '--aone-cream',
    '--aone-cream-foreground',
    '--aone-charcoal',
    '--aone-charcoal-foreground',
    '--aone-warm-white',
    '--aone-warm-white-foreground'
  ];

  const cssProperties: Record<string, string> = {};
  const computedStyles: Record<string, string> = {};

  // Validate each required property
  requiredProperties.forEach(prop => {
    const value = computedStyle.getPropertyValue(prop).trim();
    cssProperties[prop] = value || 'NOT FOUND';
    
    if (!value) {
      errors.push(`Missing CSS property: ${prop}`);
    }

    // Get computed color values
    if (value) {
      const testElement = document.createElement('div');
      testElement.style.color = `hsl(${value})`;
      document.body.appendChild(testElement);
      const computedColor = getComputedStyle(testElement).color;
      document.body.removeChild(testElement);
      computedStyles[prop] = computedColor;
    }
  });

  // Check theme class application
  const themeClass = Array.from(root.classList);
  const hasThemeClass = themeClass.includes('light') || themeClass.includes('dark');
  
  if (!hasThemeClass) {
    errors.push('No theme class (light/dark) applied to document root');
  }

  // Check for CSS loading issues
  if (document.styleSheets.length === 0) {
    errors.push('No stylesheets loaded');
  }

  // Check for A.ONE sage color specifically
  const sageColor = cssProperties['--aone-sage'];
  if (!sageColor || sageColor === 'NOT FOUND') {
    errors.push('A.ONE sage color not found - primary brand color missing');
  } else if (!sageColor.includes('82')) {
    warnings.push(`A.ONE sage color may be incorrect: ${sageColor} (expected hue ~82)`);
  }

  return {
    timestamp: new Date().toISOString(),
    documentReady: document.readyState,
    styleSheetsLoaded: document.styleSheets.length,
    themeClass,
    cssProperties,
    computedStyles,
    errors,
    warnings
  };
};

/**
 * Force theme refresh and validation
 */
export const forceThemeRefresh = (): void => {
  const root = document.documentElement;
  
  console.log('🔄 Forcing theme refresh...');
  
  // Force style recalculation
  root.style.display = 'none';
  root.offsetHeight; // Trigger reflow
  root.style.display = '';
  
  // Re-apply theme class
  const savedTheme = localStorage.getItem('blackveil-design-mind-theme') || 'system';
  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  const resolvedTheme = savedTheme === 'system' ? (systemPrefersDark ? 'dark' : 'light') : savedTheme;
  
  root.classList.remove('light', 'dark');
  root.classList.add(resolvedTheme);
  
  console.log(`🎨 Theme refreshed: ${resolvedTheme}`);
  
  // Validate after refresh
  setTimeout(() => {
    const debugInfo = debugTheme();
    console.log('🔍 Post-refresh validation:', debugInfo);
  }, 100);
};

/**
 * Comprehensive theme validation with detailed logging
 */
export const validateThemeComprehensive = (): boolean => {
  const debugInfo = debugTheme();
  
  console.group('🎨 Comprehensive Theme Validation');
  console.log('📊 Debug Info:', debugInfo);
  
  if (debugInfo.errors.length > 0) {
    console.group('❌ Errors Found:');
    debugInfo.errors.forEach(error => console.error(`  • ${error}`));
    console.groupEnd();
  }
  
  if (debugInfo.warnings.length > 0) {
    console.group('⚠️ Warnings:');
    debugInfo.warnings.forEach(warning => console.warn(`  • ${warning}`));
    console.groupEnd();
  }
  
  if (debugInfo.errors.length === 0) {
    console.log('✅ All theme validation checks passed!');
    console.log('🎨 A.ONE Design System successfully loaded');
  }
  
  console.groupEnd();
  
  return debugInfo.errors.length === 0;
};

/**
 * Auto-fix common theme issues
 */
export const autoFixThemeIssues = (): void => {
  console.log('🔧 Attempting to auto-fix theme issues...');
  
  const debugInfo = debugTheme();
  
  // Fix 1: Ensure theme class is applied
  if (!debugInfo.themeClass.includes('light') && !debugInfo.themeClass.includes('dark')) {
    console.log('🔧 Fixing missing theme class...');
    const savedTheme = localStorage.getItem('blackveil-design-mind-theme') || 'system';
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const resolvedTheme = savedTheme === 'system' ? (systemPrefersDark ? 'dark' : 'light') : savedTheme;
    
    document.documentElement.classList.add(resolvedTheme);
  }
  
  // Fix 2: Force CSS reload if properties are missing
  if (debugInfo.errors.some(error => error.includes('Missing CSS property'))) {
    console.log('🔧 Forcing CSS reload...');
    forceThemeRefresh();
  }
  
  // Fix 3: Add cache busting if needed
  if (debugInfo.styleSheetsLoaded === 0) {
    console.log('🔧 No stylesheets detected, this may be a loading issue');
    console.log('💡 Try refreshing the page or checking network tab');
  }
  
  console.log('🔧 Auto-fix complete');
};

/**
 * Development helper: Log theme state every 5 seconds
 */
export const startThemeMonitoring = (): () => void => {
  console.log('👁️ Starting theme monitoring (development mode)...');
  
  const interval = setInterval(() => {
    const debugInfo = debugTheme();
    if (debugInfo.errors.length > 0 || debugInfo.warnings.length > 0) {
      console.log('🔍 Theme monitoring detected issues:', {
        errors: debugInfo.errors,
        warnings: debugInfo.warnings,
        timestamp: debugInfo.timestamp
      });
    }
  }, 5000);
  
  return () => {
    clearInterval(interval);
    console.log('👁️ Theme monitoring stopped');
  };
};

// Export for global access in development
if (import.meta.env.DEV) {
  (window as any).themeDebugger = {
    debug: debugTheme,
    validate: validateThemeComprehensive,
    refresh: forceThemeRefresh,
    autoFix: autoFixThemeIssues,
    monitor: startThemeMonitoring
  };
  
  console.log('🛠️ Theme debugger available globally as window.themeDebugger');
}
