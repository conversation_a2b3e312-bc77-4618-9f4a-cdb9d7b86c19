/**
 * Theme Performance Monitor - Blackveil Design Mind
 * 
 * Advanced performance monitoring for theme switching operations
 * Tracks metrics, identifies bottlenecks, and provides optimization insights
 */

export interface ThemePerformanceMetrics {
  switchDuration: number;
  cssRecalculationTime: number;
  repaintTime: number;
  layoutShiftCount: number;
  memoryUsage: number;
  timestamp: number;
  fromTheme: string;
  toTheme: string;
  userAgent: string;
}

export interface ThemePerformanceReport {
  averageSwitchTime: number;
  fastestSwitch: number;
  slowestSwitch: number;
  totalSwitches: number;
  performanceGrade: 'A' | 'B' | 'C' | 'D' | 'F';
  recommendations: string[];
  metrics: ThemePerformanceMetrics[];
}

export class ThemePerformanceMonitor {
  private metrics: ThemePerformanceMetrics[] = [];
  private maxMetrics = 100; // Keep last 100 measurements
  private performanceObserver: PerformanceObserver | null = null;
  private isMonitoring = false;

  constructor() {
    this.initializePerformanceObserver();
  }

  /**
   * Initialize Performance Observer for advanced metrics
   */
  private initializePerformanceObserver(): void {
    if (typeof window === 'undefined' || !window.PerformanceObserver) {
      console.warn('PerformanceObserver not available');
      return;
    }

    try {
      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name.includes('theme-switch')) {
            this.processPerformanceEntry(entry);
          }
        });
      });

      this.performanceObserver.observe({ 
        entryTypes: ['measure', 'paint', 'layout-shift'] 
      });
      
      this.isMonitoring = true;
    } catch (error) {
      console.warn('Failed to initialize PerformanceObserver:', error);
    }
  }

  /**
   * Start monitoring a theme switch operation
   */
  startThemeSwitch(fromTheme: string, toTheme: string): string {
    const operationId = `theme-switch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Mark the start of theme switch
    if (typeof window !== 'undefined' && window.performance) {
      window.performance.mark(`${operationId}-start`);
    }

    return operationId;
  }

  /**
   * End monitoring and record metrics
   */
  endThemeSwitch(
    operationId: string, 
    fromTheme: string, 
    toTheme: string
  ): ThemePerformanceMetrics {
    const endTime = performance.now();
    
    // Mark the end and measure duration
    if (typeof window !== 'undefined' && window.performance) {
      window.performance.mark(`${operationId}-end`);
      window.performance.measure(
        operationId,
        `${operationId}-start`,
        `${operationId}-end`
      );
    }

    const metrics = this.collectMetrics(operationId, fromTheme, toTheme, endTime);
    this.addMetrics(metrics);
    
    return metrics;
  }

  /**
   * Collect comprehensive performance metrics
   */
  private collectMetrics(
    operationId: string,
    fromTheme: string,
    toTheme: string,
    endTime: number
  ): ThemePerformanceMetrics {
    const startMark = performance.getEntriesByName(`${operationId}-start`)[0];
    const endMark = performance.getEntriesByName(`${operationId}-end`)[0];
    
    const switchDuration = endMark ? endMark.startTime - startMark.startTime : 0;
    
    // Estimate CSS recalculation time (simplified)
    const cssRecalculationTime = this.estimateCSSRecalculationTime();
    
    // Estimate repaint time
    const repaintTime = this.estimateRepaintTime();
    
    // Get layout shift count
    const layoutShiftCount = this.getLayoutShiftCount();
    
    // Get memory usage
    const memoryUsage = this.getMemoryUsage();

    return {
      switchDuration,
      cssRecalculationTime,
      repaintTime,
      layoutShiftCount,
      memoryUsage,
      timestamp: Date.now(),
      fromTheme,
      toTheme,
      userAgent: navigator.userAgent
    };
  }

  /**
   * Estimate CSS recalculation time
   */
  private estimateCSSRecalculationTime(): number {
    // This is a simplified estimation
    // In a real implementation, you might use more sophisticated timing
    const startTime = performance.now();
    
    // Force style recalculation
    const testElement = document.createElement('div');
    testElement.style.cssText = 'position: absolute; visibility: hidden;';
    document.body.appendChild(testElement);
    
    // Trigger recalculation
    testElement.offsetHeight;
    
    document.body.removeChild(testElement);
    
    return performance.now() - startTime;
  }

  /**
   * Estimate repaint time
   */
  private estimateRepaintTime(): number {
    // Simplified repaint time estimation
    const paintEntries = performance.getEntriesByType('paint');
    const lastPaint = paintEntries[paintEntries.length - 1];
    
    return lastPaint ? lastPaint.duration || 0 : 0;
  }

  /**
   * Get layout shift count
   */
  private getLayoutShiftCount(): number {
    const layoutShiftEntries = performance.getEntriesByType('layout-shift');
    return layoutShiftEntries.length;
  }

  /**
   * Get memory usage
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return memory.usedJSHeapSize || 0;
    }
    return 0;
  }

  /**
   * Process performance observer entries
   */
  private processPerformanceEntry(entry: PerformanceEntry): void {
    // Additional processing for performance entries
    console.debug('Theme Performance Entry:', {
      name: entry.name,
      duration: entry.duration,
      startTime: entry.startTime
    });
  }

  /**
   * Add metrics to collection
   */
  private addMetrics(metrics: ThemePerformanceMetrics): void {
    this.metrics.push(metrics);
    
    // Keep only the most recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  /**
   * Generate comprehensive performance report
   */
  getPerformanceReport(): ThemePerformanceReport {
    if (this.metrics.length === 0) {
      return {
        averageSwitchTime: 0,
        fastestSwitch: 0,
        slowestSwitch: 0,
        totalSwitches: 0,
        performanceGrade: 'F',
        recommendations: ['No theme switch data available'],
        metrics: []
      };
    }

    const switchTimes = this.metrics.map(m => m.switchDuration);
    const averageSwitchTime = switchTimes.reduce((a, b) => a + b, 0) / switchTimes.length;
    const fastestSwitch = Math.min(...switchTimes);
    const slowestSwitch = Math.max(...switchTimes);

    const performanceGrade = this.calculatePerformanceGrade(averageSwitchTime);
    const recommendations = this.generateRecommendations(averageSwitchTime, this.metrics);

    return {
      averageSwitchTime,
      fastestSwitch,
      slowestSwitch,
      totalSwitches: this.metrics.length,
      performanceGrade,
      recommendations,
      metrics: [...this.metrics]
    };
  }

  /**
   * Calculate performance grade based on average switch time
   */
  private calculatePerformanceGrade(averageTime: number): 'A' | 'B' | 'C' | 'D' | 'F' {
    if (averageTime <= 50) return 'A';      // Excellent: ≤50ms
    if (averageTime <= 100) return 'B';     // Good: ≤100ms
    if (averageTime <= 200) return 'C';     // Fair: ≤200ms
    if (averageTime <= 500) return 'D';     // Poor: ≤500ms
    return 'F';                             // Very Poor: >500ms
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(
    averageTime: number, 
    metrics: ThemePerformanceMetrics[]
  ): string[] {
    const recommendations: string[] = [];

    if (averageTime > 100) {
      recommendations.push('Theme switching is slower than optimal (>100ms). Consider optimizing CSS custom properties.');
    }

    const highMemoryUsage = metrics.some(m => m.memoryUsage > 50 * 1024 * 1024); // 50MB
    if (highMemoryUsage) {
      recommendations.push('High memory usage detected during theme switches. Check for memory leaks.');
    }

    const highLayoutShifts = metrics.some(m => m.layoutShiftCount > 5);
    if (highLayoutShifts) {
      recommendations.push('Multiple layout shifts detected. Ensure theme changes don\'t affect element positioning.');
    }

    const slowCSSRecalculation = metrics.some(m => m.cssRecalculationTime > 50);
    if (slowCSSRecalculation) {
      recommendations.push('CSS recalculation is slow. Consider reducing CSS complexity or using more specific selectors.');
    }

    if (recommendations.length === 0) {
      recommendations.push('Theme performance is optimal! No improvements needed.');
    }

    return recommendations;
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Get current monitoring status
   */
  isMonitoringActive(): boolean {
    return this.isMonitoring;
  }

  /**
   * Stop monitoring
   */
  stopMonitoring(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.isMonitoring = false;
    }
  }

  /**
   * Export metrics for external analysis
   */
  exportMetrics(): string {
    return JSON.stringify({
      exportDate: new Date().toISOString(),
      metricsCount: this.metrics.length,
      performanceReport: this.getPerformanceReport(),
      rawMetrics: this.metrics
    }, null, 2);
  }
}

// Singleton instance for global use
export const themePerformanceMonitor = new ThemePerformanceMonitor();

// Helper function for easy integration with useTheme hook
export const withThemePerformanceMonitoring = <T extends (...args: any[]) => any>(
  fn: T,
  fromTheme: string,
  toTheme: string
): T => {
  return ((...args: any[]) => {
    const operationId = themePerformanceMonitor.startThemeSwitch(fromTheme, toTheme);
    
    try {
      const result = fn(...args);
      
      // Handle both sync and async functions
      if (result instanceof Promise) {
        return result.finally(() => {
          themePerformanceMonitor.endThemeSwitch(operationId, fromTheme, toTheme);
        });
      } else {
        themePerformanceMonitor.endThemeSwitch(operationId, fromTheme, toTheme);
        return result;
      }
    } catch (error) {
      themePerformanceMonitor.endThemeSwitch(operationId, fromTheme, toTheme);
      throw error;
    }
  }) as T;
};
