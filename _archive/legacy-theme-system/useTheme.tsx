import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { themePerformanceMonitor, ThemePerformanceReport } from '@/utils/themePerformanceMonitor';

// Theme types
export type Theme = 'light' | 'dark' | 'system';
export type ResolvedTheme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  resolvedTheme: ResolvedTheme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  systemTheme: ResolvedTheme;
  isTransitioning: boolean;
  // Performance monitoring
  getPerformanceReport: () => ThemePerformanceReport;
  clearPerformanceHistory: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
  enableTransitions?: boolean;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultTheme = 'system',
  storageKey = 'blackveil-design-mind-theme',
  enableTransitions = true
}) => {
  const [theme, setThemeState] = useState<Theme>(defaultTheme);
  const [systemTheme, setSystemTheme] = useState<ResolvedTheme>('light');
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Get resolved theme based on current theme setting
  const resolvedTheme: ResolvedTheme = theme === 'system' ? systemTheme : theme;

  // Detect system theme preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light');
    };

    // Set initial system theme
    setSystemTheme(mediaQuery.matches ? 'dark' : 'light');
    
    // Listen for changes
    mediaQuery.addEventListener('change', handleChange);
    
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Load theme from localStorage on mount
  useEffect(() => {
    try {
      const storedTheme = localStorage.getItem(storageKey) as Theme;
      if (storedTheme && ['light', 'dark', 'system'].includes(storedTheme)) {
        setThemeState(storedTheme);
      }
    } catch (error) {
      console.warn('Failed to load theme from localStorage:', error);
    }
  }, [storageKey]);

  // Apply theme to document with enhanced validation and force application
  useEffect(() => {
    const root = document.documentElement;

    if (enableTransitions) {
      setIsTransitioning(true);

      // Add transition class for smooth theme switching
      root.classList.add('theme-transitioning');

      // Remove transition class after animation completes
      const timeout = setTimeout(() => {
        root.classList.remove('theme-transitioning');
        setIsTransitioning(false);
      }, 300);

      return () => clearTimeout(timeout);
    }

    // Apply theme class with validation
    root.classList.remove('light', 'dark');
    root.classList.add(resolvedTheme);

    // Set color-scheme for better browser integration
    root.style.colorScheme = resolvedTheme;

    // Force style recalculation to ensure theme is applied
    root.style.setProperty('--theme-applied', resolvedTheme);

    // Validate theme application and log for debugging
    const appliedTheme = root.classList.contains(resolvedTheme);
    const sageColor = getComputedStyle(root).getPropertyValue('--aone-sage').trim();

    console.log(`🎨 ThemeProvider: Applied ${resolvedTheme}`, {
      success: appliedTheme,
      classList: Array.from(root.classList),
      sageColor: sageColor || 'NOT FOUND',
      colorScheme: root.style.colorScheme,
      timestamp: new Date().toISOString()
    });

    // If sage color is not found, force a style refresh
    if (!sageColor) {
      console.warn('⚠️ A.ONE sage color not found, forcing style refresh...');
      setTimeout(() => {
        root.style.display = 'none';
        root.offsetHeight; // Trigger reflow
        root.style.display = '';
      }, 100);
    }

  }, [resolvedTheme, enableTransitions]);

  // Set theme function with performance monitoring
  const setTheme = (newTheme: Theme) => {
    const currentResolvedTheme = resolvedTheme;
    const newResolvedTheme = newTheme === 'system' ? systemTheme : newTheme;

    // Start performance monitoring
    const operationId = themePerformanceMonitor.startThemeSwitch(
      currentResolvedTheme,
      newResolvedTheme
    );

    try {
      setThemeState(newTheme);
      localStorage.setItem(storageKey, newTheme);

      // End performance monitoring after state update
      setTimeout(() => {
        themePerformanceMonitor.endThemeSwitch(
          operationId,
          currentResolvedTheme,
          newResolvedTheme
        );
      }, 50); // Small delay to capture theme application

    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error);
      setThemeState(newTheme);

      // Still end monitoring even on error
      themePerformanceMonitor.endThemeSwitch(
        operationId,
        currentResolvedTheme,
        newResolvedTheme
      );
    }
  };

  // Toggle between light and dark (ignores system)
  const toggleTheme = () => {
    if (theme === 'system') {
      // If currently system, toggle to opposite of system preference
      setTheme(systemTheme === 'dark' ? 'light' : 'dark');
    } else {
      // Toggle between light and dark
      setTheme(theme === 'dark' ? 'light' : 'dark');
    }
  };

  // Performance monitoring functions
  const getPerformanceReport = () => themePerformanceMonitor.getPerformanceReport();
  const clearPerformanceHistory = () => themePerformanceMonitor.clearMetrics();

  const value: ThemeContextType = {
    theme,
    resolvedTheme,
    setTheme,
    toggleTheme,
    systemTheme,
    isTransitioning,
    getPerformanceReport,
    clearPerformanceHistory
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook for theme-aware styling
export const useThemeAwareStyle = () => {
  const { resolvedTheme } = useTheme();
  
  return {
    isDark: resolvedTheme === 'dark',
    isLight: resolvedTheme === 'light',
    themeClass: resolvedTheme,
    getThemeValue: <T,>(lightValue: T, darkValue: T): T =>
      resolvedTheme === 'dark' ? darkValue : lightValue
  };
};

// Utility function for conditional theme classes
export const themeClasses = (lightClasses: string, darkClasses: string): string => {
  return `${lightClasses} dark:${darkClasses}`;
};

// Theme transition CSS (to be added to index.css)
export const themeTransitionCSS = `
  .theme-transitioning,
  .theme-transitioning *,
  .theme-transitioning *:before,
  .theme-transitioning *:after {
    transition: background-color 0.3s ease-in-out, 
                border-color 0.3s ease-in-out, 
                color 0.3s ease-in-out,
                box-shadow 0.3s ease-in-out !important;
  }
`;
