/**
 * Theme Test Panel - Visual validation of A.ONE Design System
 * This component helps verify that all theme colors are loading correctly
 */

import React from 'react';
import { useTheme } from '@/hooks/useTheme';
import { validateThemeComprehensive, autoFixThemeIssues } from '@/utils/themeDebugger';

interface ColorSwatchProps {
  name: string;
  cssVar: string;
  description: string;
}

const ColorSwatch: React.FC<ColorSwatchProps> = ({ name, cssVar, description }) => {
  const style = {
    backgroundColor: `hsl(var(${cssVar}))`,
    color: `hsl(var(${cssVar}-foreground))`,
  };

  return (
    <div className="p-4 rounded-lg border" style={style}>
      <div className="font-semibold text-sm">{name}</div>
      <div className="text-xs opacity-80">{cssVar}</div>
      <div className="text-xs mt-1">{description}</div>
    </div>
  );
};

const ThemeTestPanel: React.FC = () => {
  const { resolvedTheme, toggleTheme } = useTheme();

  const handleValidateTheme = () => {
    validateThemeComprehensive();
  };

  const handleAutoFix = () => {
    autoFixThemeIssues();
  };

  // Test if CSS custom properties are loaded
  const testCSSLoading = () => {
    const root = document.documentElement;
    const sageColor = getComputedStyle(root).getPropertyValue('--aone-sage').trim();
    console.log('🔍 Direct CSS Test:', {
      sageColor: sageColor || 'NOT FOUND',
      documentClasses: Array.from(root.classList),
      timestamp: new Date().toISOString()
    });
    return sageColor;
  };

  const aoneColors = [
    {
      name: 'Sage Green',
      cssVar: '--aone-sage',
      description: 'Primary brand color'
    },
    {
      name: 'Sage Light',
      cssVar: '--aone-sage-light',
      description: 'Lighter sage variant'
    },
    {
      name: 'Sage Dark',
      cssVar: '--aone-sage-dark',
      description: 'Darker sage variant'
    },
    {
      name: 'Cream',
      cssVar: '--aone-cream',
      description: 'Light cream/beige'
    },
    {
      name: 'Charcoal',
      cssVar: '--aone-charcoal',
      description: 'Dark charcoal text'
    },
    {
      name: 'Warm White',
      cssVar: '--aone-warm-white',
      description: 'Warm white background'
    }
  ];

  return (
    <div className="fixed bottom-4 right-4 w-80 bg-card border rounded-lg shadow-lg p-4 z-50">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-sm">A.ONE Theme Test</h3>
        <div className="flex gap-2">
          <button
            onClick={toggleTheme}
            className="px-2 py-1 text-xs bg-aone-sage text-aone-sage-foreground rounded"
          >
            {resolvedTheme === 'dark' ? '☀️' : '🌙'}
          </button>
          <button
            onClick={handleValidateTheme}
            className="px-2 py-1 text-xs bg-blue-500 text-white rounded"
          >
            Validate
          </button>
          <button
            onClick={handleAutoFix}
            className="px-2 py-1 text-xs bg-green-500 text-white rounded"
          >
            Fix
          </button>
          <button
            onClick={testCSSLoading}
            className="px-2 py-1 text-xs bg-purple-500 text-white rounded"
          >
            Test
          </button>
        </div>
      </div>

      <div className="text-xs mb-3 p-2 bg-muted rounded">
        <div>Theme: <strong>{resolvedTheme}</strong></div>
        <div>Classes: <strong>{document.documentElement.className}</strong></div>
      </div>

      <div className="grid grid-cols-2 gap-2 mb-4">
        {aoneColors.map((color) => (
          <ColorSwatch
            key={color.cssVar}
            name={color.name}
            cssVar={color.cssVar}
            description={color.description}
          />
        ))}
      </div>

      <div className="text-xs text-muted-foreground">
        Open browser console for detailed theme validation logs.
      </div>
    </div>
  );
};

export default ThemeTestPanel;
