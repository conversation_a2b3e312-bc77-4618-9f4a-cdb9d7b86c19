/**
 * Comprehensive Theme Loading Diagnostic Script
 * 
 * This script performs deep analysis of theme loading issues in Blackveil Design Mind
 * Run this in the browser console to get detailed diagnostic information
 */

(function() {
    'use strict';
    
    console.log('🔍 THEME DIAGNOSTIC SCRIPT STARTING...');
    console.log('=====================================');
    
    // 1. Document Ready State Analysis
    console.log('\n📄 DOCUMENT STATE ANALYSIS:');
    console.log('Document ready state:', document.readyState);
    console.log('DOM content loaded:', document.readyState !== 'loading');
    
    // 2. HTML Element Analysis
    console.log('\n🏷️ HTML ELEMENT ANALYSIS:');
    const htmlElement = document.documentElement;
    console.log('HTML element classes:', Array.from(htmlElement.classList));
    console.log('HTML element has dark class:', htmlElement.classList.contains('dark'));
    console.log('HTML element has light class:', htmlElement.classList.contains('light'));
    console.log('HTML element has theme-transitioning class:', htmlElement.classList.contains('theme-transitioning'));
    
    // 3. CSS Custom Properties Analysis
    console.log('\n🎨 CSS CUSTOM PROPERTIES ANALYSIS:');
    const computedStyle = getComputedStyle(htmlElement);
    
    const criticalProperties = [
        '--aone-sage',
        '--aone-sage-foreground', 
        '--aone-charcoal',
        '--background',
        '--foreground',
        '--test-validation'
    ];
    
    criticalProperties.forEach(prop => {
        const value = computedStyle.getPropertyValue(prop);
        console.log(`${prop}:`, value ? `"${value.trim()}"` : 'NOT FOUND');
    });
    
    // 4. Stylesheet Analysis
    console.log('\n📋 STYLESHEET ANALYSIS:');
    console.log('Total stylesheets:', document.styleSheets.length);
    
    Array.from(document.styleSheets).forEach((sheet, index) => {
        try {
            console.log(`Stylesheet ${index}:`, {
                href: sheet.href,
                title: sheet.title,
                disabled: sheet.disabled,
                media: sheet.media.mediaText,
                rulesCount: sheet.cssRules ? sheet.cssRules.length : 'Cannot access'
            });
        } catch (e) {
            console.log(`Stylesheet ${index}: Cannot access (CORS)`, sheet.href);
        }
    });
    
    // 5. CSS Rule Analysis (for accessible stylesheets)
    console.log('\n📝 CSS RULES ANALYSIS:');
    let aoneRulesFound = 0;
    let darkModeRulesFound = 0;
    
    Array.from(document.styleSheets).forEach((sheet, sheetIndex) => {
        try {
            if (sheet.cssRules) {
                Array.from(sheet.cssRules).forEach((rule, ruleIndex) => {
                    if (rule.cssText && rule.cssText.includes('--aone-sage')) {
                        aoneRulesFound++;
                        console.log(`Found A.ONE rule in sheet ${sheetIndex}, rule ${ruleIndex}:`, rule.cssText.substring(0, 100) + '...');
                    }
                    if (rule.cssText && rule.cssText.includes('.dark')) {
                        darkModeRulesFound++;
                        console.log(`Found dark mode rule in sheet ${sheetIndex}, rule ${ruleIndex}:`, rule.cssText.substring(0, 100) + '...');
                    }
                });
            }
        } catch (e) {
            console.log(`Cannot analyze rules in stylesheet ${sheetIndex}:`, e.message);
        }
    });
    
    console.log(`Total A.ONE rules found: ${aoneRulesFound}`);
    console.log(`Total dark mode rules found: ${darkModeRulesFound}`);
    
    // 6. Element Style Testing
    console.log('\n🧪 ELEMENT STYLE TESTING:');
    
    // Create test element
    const testElement = document.createElement('div');
    testElement.style.cssText = `
        background: hsl(var(--aone-sage));
        color: hsl(var(--aone-sage-foreground));
        padding: 10px;
        position: absolute;
        top: -1000px;
        left: -1000px;
    `;
    document.body.appendChild(testElement);
    
    const testComputedStyle = getComputedStyle(testElement);
    console.log('Test element background color:', testComputedStyle.backgroundColor);
    console.log('Test element color:', testComputedStyle.color);
    console.log('Test element computed --aone-sage:', testComputedStyle.getPropertyValue('--aone-sage'));
    
    document.body.removeChild(testElement);
    
    // 7. Theme Context Analysis (if React is loaded)
    console.log('\n⚛️ REACT THEME CONTEXT ANALYSIS:');
    if (window.React) {
        console.log('React is loaded');
        // Try to find theme context in React DevTools
        if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
            console.log('React DevTools detected');
        }
    } else {
        console.log('React not yet loaded or not accessible');
    }
    
    // 8. Local Storage Analysis
    console.log('\n💾 LOCAL STORAGE ANALYSIS:');
    const themeKeys = ['blackveil-design-mind-theme', 'theme', 'dark-mode'];
    themeKeys.forEach(key => {
        const value = localStorage.getItem(key);
        console.log(`localStorage["${key}"]:`, value);
    });
    
    // 9. Media Query Analysis
    console.log('\n📱 MEDIA QUERY ANALYSIS:');
    const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
    console.log('System prefers dark mode:', darkModeQuery.matches);
    console.log('Media query object:', darkModeQuery);
    
    // 10. Network Analysis (check for CSS loading)
    console.log('\n🌐 NETWORK ANALYSIS:');
    if (performance.getEntriesByType) {
        const resources = performance.getEntriesByType('resource');
        const cssResources = resources.filter(r => r.name.includes('.css') || r.name.includes('index.css'));
        console.log('CSS resources loaded:', cssResources.length);
        cssResources.forEach(resource => {
            console.log('CSS Resource:', {
                name: resource.name,
                duration: resource.duration,
                transferSize: resource.transferSize,
                responseEnd: resource.responseEnd
            });
        });
    }
    
    // 11. Timing Analysis
    console.log('\n⏱️ TIMING ANALYSIS:');
    console.log('Performance timing:', {
        domContentLoaded: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
        loadComplete: performance.timing.loadEventEnd - performance.timing.navigationStart,
        currentTime: performance.now()
    });
    
    // 12. CSS Validation Test
    console.log('\n✅ CSS VALIDATION TEST:');
    const validationElement = document.createElement('div');
    validationElement.className = 'test-css-properties';
    document.body.appendChild(validationElement);
    
    const validationStyle = getComputedStyle(validationElement);
    const testValidation = validationStyle.getPropertyValue('--test-validation');
    console.log('CSS validation test result:', testValidation);
    console.log('CSS properties loaded correctly:', testValidation.includes('loaded'));
    
    document.body.removeChild(validationElement);
    
    // 13. Final Summary
    console.log('\n📊 DIAGNOSTIC SUMMARY:');
    console.log('=====================================');
    
    const issues = [];
    const successes = [];
    
    if (!computedStyle.getPropertyValue('--aone-sage').trim()) {
        issues.push('❌ A.ONE sage color not found in CSS custom properties');
    } else {
        successes.push('✅ A.ONE sage color found in CSS custom properties');
    }
    
    if (!testValidation.includes('loaded')) {
        issues.push('❌ CSS validation test failed - CSS may not be fully loaded');
    } else {
        successes.push('✅ CSS validation test passed');
    }
    
    if (aoneRulesFound === 0) {
        issues.push('❌ No A.ONE CSS rules found in stylesheets');
    } else {
        successes.push(`✅ Found ${aoneRulesFound} A.ONE CSS rules`);
    }
    
    if (darkModeRulesFound === 0) {
        issues.push('❌ No dark mode CSS rules found in stylesheets');
    } else {
        successes.push(`✅ Found ${darkModeRulesFound} dark mode CSS rules`);
    }
    
    console.log('\n🎉 SUCCESSES:');
    successes.forEach(success => console.log(success));
    
    console.log('\n🚨 ISSUES FOUND:');
    issues.forEach(issue => console.log(issue));
    
    // 14. Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    if (issues.length > 0) {
        console.log('1. Check if CSS is loading before JavaScript execution');
        console.log('2. Verify CSS custom properties are defined correctly');
        console.log('3. Check for CSS loading race conditions');
        console.log('4. Verify Vite CSS processing is working correctly');
        console.log('5. Check browser console for CSS parsing errors');
    } else {
        console.log('✅ No major issues detected. Theme system appears to be working correctly.');
    }
    
    console.log('\n🔍 THEME DIAGNOSTIC SCRIPT COMPLETED');
    console.log('=====================================');
    
    // Return diagnostic data for programmatic access
    return {
        documentReady: document.readyState !== 'loading',
        htmlClasses: Array.from(htmlElement.classList),
        cssProperties: Object.fromEntries(
            criticalProperties.map(prop => [prop, computedStyle.getPropertyValue(prop).trim()])
        ),
        stylesheetCount: document.styleSheets.length,
        aoneRulesFound,
        darkModeRulesFound,
        cssValidationPassed: testValidation.includes('loaded'),
        systemDarkMode: darkModeQuery.matches,
        localStorage: Object.fromEntries(
            themeKeys.map(key => [key, localStorage.getItem(key)])
        ),
        issues,
        successes
    };
})();
