/**
 * Theme Validation Utility for Blackveil Design Mind
 * Validates CSS custom properties and A.ONE design system loading
 */

export interface ThemeValidationResult {
  isValid: boolean;
  missingProperties: string[];
  loadedProperties: string[];
  errors: string[];
}

/**
 * A.ONE Design System Required CSS Custom Properties - Complete List
 */
const REQUIRED_CSS_PROPERTIES = [
  // Base Shadcn Colors (17 properties)
  '--background',
  '--foreground',
  '--card',
  '--card-foreground',
  '--popover',
  '--popover-foreground',
  '--primary',
  '--primary-foreground',
  '--secondary',
  '--secondary-foreground',
  '--muted',
  '--muted-foreground',
  '--accent',
  '--accent-foreground',
  '--destructive',
  '--destructive-foreground',
  '--border',
  '--input',
  '--ring',

  // A.ONE Inspired Design System Colors (10 properties)
  '--aone-sage',
  '--aone-sage-foreground',
  '--aone-sage-light',
  '--aone-sage-dark',
  '--aone-cream',
  '--aone-cream-foreground',
  '--aone-charcoal',
  '--aone-charcoal-foreground',
  '--aone-warm-white',
  '--aone-warm-white-foreground',

  // Semantic Status Colors (8 properties)
  '--status-success',
  '--status-success-foreground',
  '--status-warning',
  '--status-warning-foreground',
  '--status-error',
  '--status-error-foreground',
  '--status-info',
  '--status-info-foreground',

  // Confidence Score Colors (3 properties)
  '--confidence-high',
  '--confidence-medium',
  '--confidence-low',

  // Enhanced Design Tokens - Spacing Scale (7 properties)
  '--spacing-xs',
  '--spacing-sm',
  '--spacing-md',
  '--spacing-lg',
  '--spacing-xl',
  '--spacing-2xl',
  '--spacing-3xl',

  // Enhanced Design Tokens - Border Radius Scale (6 properties)
  '--radius',
  '--radius-sm',
  '--radius-md',
  '--radius-lg',
  '--radius-xl',
  '--radius-2xl',

  // Enhanced Design Tokens - Typography Scale (8 properties)
  '--font-size-xs',
  '--font-size-sm',
  '--font-size-base',
  '--font-size-lg',
  '--font-size-xl',
  '--font-size-2xl',
  '--font-size-3xl',
  '--font-size-4xl',

  // Enhanced Design Tokens - Shadow Scale (4 properties)
  '--shadow-sm',
  '--shadow-md',
  '--shadow-lg',
  '--shadow-xl',

  // Enhanced Design Tokens - Animation Timing (6 properties)
  '--transition-fast',
  '--transition-normal',
  '--transition-slow',
  '--ease-in-out',
  '--ease-out',
  '--ease-in',

  // Enhanced Design Tokens - Sidebar Colors (8 properties)
  '--sidebar-background',
  '--sidebar-foreground',
  '--sidebar-primary',
  '--sidebar-primary-foreground',
  '--sidebar-accent',
  '--sidebar-accent-foreground',
  '--sidebar-border',
  '--sidebar-ring'
];

/**
 * Validates that all required CSS custom properties are loaded
 */
export function validateThemeProperties(): ThemeValidationResult {
  const result: ThemeValidationResult = {
    isValid: true,
    missingProperties: [],
    loadedProperties: [],
    errors: []
  };

  try {
    const computedStyle = getComputedStyle(document.documentElement);

    for (const property of REQUIRED_CSS_PROPERTIES) {
      const value = computedStyle.getPropertyValue(property).trim();
      
      if (value) {
        result.loadedProperties.push(property);
      } else {
        result.missingProperties.push(property);
        result.isValid = false;
      }
    }

    // Additional validation for A.ONE sage color
    const sageColor = computedStyle.getPropertyValue('--aone-sage').trim();
    if (!sageColor) {
      result.errors.push('A.ONE sage color variable (--aone-sage) not found');
      result.isValid = false;
    } else if (!sageColor.includes('82 25% 45%') && !sageColor.includes('82 30% 60%')) {
      result.errors.push(`A.ONE sage color has unexpected value: "${sageColor}" (expected: "82 25% 45%" for light or "82 30% 60%" for dark)`);
      result.isValid = false;
    }

    // Validate critical A.ONE brand colors
    const criticalColors = [
      { prop: '--aone-sage', expected: ['82 25% 45%', '82 30% 60%'] },
      { prop: '--aone-charcoal', expected: ['0 0% 18%', '0 0% 88%'] },
      { prop: '--aone-cream', expected: ['48 20% 95%', '48 15% 12%'] }
    ];

    for (const { prop, expected } of criticalColors) {
      const value = computedStyle.getPropertyValue(prop).trim();
      if (value && !expected.some(exp => value.includes(exp))) {
        result.errors.push(`${prop} has unexpected value: "${value}"`);
      }
    }

  } catch (error) {
    result.errors.push(`Theme validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    result.isValid = false;
  }

  return result;
}

/**
 * Validates dark mode theme properties
 */
export function validateDarkModeProperties(): ThemeValidationResult {
  const result: ThemeValidationResult = {
    isValid: true,
    missingProperties: [],
    loadedProperties: [],
    errors: []
  };

  try {
    // Temporarily add dark class to test dark mode properties
    const originalClass = document.documentElement.className;
    document.documentElement.className = `${originalClass} dark`.trim();

    const computedStyle = getComputedStyle(document.documentElement);

    // Check key dark mode properties
    const darkModeProperties = [
      '--background',
      '--foreground',
      '--aone-sage',
      '--status-success',
      '--status-warning',
      '--status-error'
    ];

    for (const property of darkModeProperties) {
      const value = computedStyle.getPropertyValue(property).trim();
      
      if (value) {
        result.loadedProperties.push(property);
      } else {
        result.missingProperties.push(property);
        result.isValid = false;
      }
    }

    // Restore original class
    document.documentElement.className = originalClass;

  } catch (error) {
    result.errors.push(`Dark mode validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    result.isValid = false;
  }

  return result;
}

/**
 * Logs theme validation results to console with detailed breakdown
 */
export function logThemeValidation(): void {
  console.group('🎨 Blackveil Design Mind - A.ONE Design System Validation');

  const lightTheme = validateThemeProperties();
  const darkTheme = validateDarkModeProperties();

  // Light Theme Results
  console.log('Light Theme Validation:', lightTheme.isValid ? '✅ PASS' : '❌ FAIL');
  console.log(`📊 Properties Loaded: ${lightTheme.loadedProperties.length}/${REQUIRED_CSS_PROPERTIES.length} (${Math.round((lightTheme.loadedProperties.length / REQUIRED_CSS_PROPERTIES.length) * 100)}%)`);

  if (lightTheme.missingProperties.length > 0) {
    console.group('⚠️ Missing Light Theme Properties:');
    lightTheme.missingProperties.forEach(prop => console.warn(`  • ${prop}`));
    console.groupEnd();
  }

  if (lightTheme.errors.length > 0) {
    console.group('❌ Light Theme Errors:');
    lightTheme.errors.forEach(error => console.error(`  • ${error}`));
    console.groupEnd();
  }

  // Dark Theme Results
  console.log('Dark Theme Validation:', darkTheme.isValid ? '✅ PASS' : '❌ FAIL');

  if (darkTheme.missingProperties.length > 0) {
    console.group('⚠️ Missing Dark Theme Properties:');
    darkTheme.missingProperties.forEach(prop => console.warn(`  • ${prop}`));
    console.groupEnd();
  }

  if (darkTheme.errors.length > 0) {
    console.group('❌ Dark Theme Errors:');
    darkTheme.errors.forEach(error => console.error(`  • ${error}`));
    console.groupEnd();
  }

  // Overall Status
  const overallStatus = lightTheme.isValid && darkTheme.isValid;
  console.log('🎯 A.ONE Design System Status:', overallStatus ? '🟢 Fully Loaded' : '🟡 Issues Detected');

  if (overallStatus) {
    console.log('✨ All A.ONE design system CSS custom properties loaded successfully!');
    console.log('🎨 Sage color (#6B7A4F) and brand colors are properly configured');
  } else {
    console.log('🔧 Some CSS custom properties need attention for optimal A.ONE design rendering');
  }

  console.groupEnd();
}

/**
 * Validates theme loading and reports issues
 */
export function validateAndReportTheme(): boolean {
  const validation = validateThemeProperties();
  
  if (!validation.isValid) {
    console.error('🚨 Theme Loading Issues Detected:');
    console.error('Missing Properties:', validation.missingProperties);
    console.error('Errors:', validation.errors);
    return false;
  }
  
  console.log('✅ A.ONE Design System loaded successfully');
  return true;
}
