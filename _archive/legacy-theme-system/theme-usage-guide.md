# Theme Usage Guide - Blackveil Design Mind

## 🎨 Quick Start

### Basic Theme Usage

```tsx
import { useTheme } from '@/hooks/useTheme';

const MyComponent = () => {
  const { theme, resolvedTheme, setTheme, toggleTheme } = useTheme();
  
  return (
    <div className="bg-background text-foreground">
      <h1 className="text-aone-sage">Welcome to Blackveil Design Mind</h1>
      <button 
        onClick={toggleTheme}
        className="bg-aone-sage text-aone-sage-foreground hover:bg-aone-sage-dark"
      >
        Toggle Theme
      </button>
    </div>
  );
};
```

### Theme Toggle Components

```tsx
import { ThemeToggle } from '@/components/ui/theme-toggle';

// Dropdown variant (default)
<ThemeToggle variant="dropdown" size="md" />

// Button variant
<ThemeToggle variant="button" size="lg" />

// Switch variant
<ThemeToggle variant="switch" showLabel={true} />

// Compact variant for mobile
import { CompactThemeToggle } from '@/components/ui/theme-toggle';
<CompactThemeToggle />
```

## 🎯 Design Tokens

### A.ONE Color Palette

```css
/* Primary Colors */
--aone-sage: 82 25% 45%;           /* #6B7A4F */
--aone-sage-foreground: 0 0% 100%;
--aone-sage-light: 82 25% 65%;
--aone-sage-dark: 82 25% 35%;

/* Supporting Colors */
--aone-cream: 48 20% 95%;
--aone-charcoal: 0 0% 18%;
--aone-warm-white: 48 10% 98%;
```

### Tailwind Classes

```tsx
// A.ONE Colors
className="bg-aone-sage text-aone-sage-foreground"
className="border-aone-sage/20 hover:border-aone-sage/30"
className="text-aone-charcoal dark:text-aone-charcoal"

// Status Colors
className="text-status-success bg-status-success/10"
className="text-status-warning bg-status-warning/10"
className="text-status-error bg-status-error/10"

// Confidence Indicators
className="text-confidence-high"
className="text-confidence-medium"
className="text-confidence-low"
```

## 🔧 Advanced Usage

### Theme-Aware Styling Hook

```tsx
import { useThemeAwareStyle } from '@/hooks/useTheme';

const MyComponent = () => {
  const { isDark, isLight, themeClass, getThemeValue } = useThemeAwareStyle();
  
  const backgroundColor = getThemeValue('#ffffff', '#1a1a1a');
  const textColor = getThemeValue('#333333', '#ffffff');
  
  return (
    <div 
      className={`component ${themeClass}`}
      style={{ backgroundColor, color: textColor }}
    >
      {isDark ? 'Dark mode content' : 'Light mode content'}
    </div>
  );
};
```

### Conditional Theme Classes

```tsx
import { themeClasses } from '@/hooks/useTheme';

// Utility function for conditional classes
const buttonClasses = themeClasses(
  'bg-white text-gray-900 border-gray-300', // light mode
  'bg-gray-800 text-white border-gray-600'  // dark mode
);

<button className={buttonClasses}>
  Theme-aware button
</button>
```

## 🛠️ Development Tools

### Theme Debugging

```tsx
import { debugTheme, validateTheme } from '@/utils/themeDebugger';

// Debug current theme state
const themeInfo = debugTheme();
console.log('Theme Debug Info:', themeInfo);

// Validate theme implementation
const isValid = validateTheme();
if (!isValid) {
  console.warn('Theme validation failed');
}
```

### Auto-Fix Theme Issues

```tsx
import { autoFixThemeIssues } from '@/utils/themeDebugger';

// Automatically fix common theme issues
autoFixThemeIssues();
```

### Theme Validation

```tsx
import { validateThemeImplementation } from '@/utils/themeValidation';

// Comprehensive theme validation
const validation = validateThemeImplementation();
console.log('Validation Results:', validation);
```

## 📱 Responsive Design

### Mobile-First Approach

```tsx
// Mobile-optimized theme toggle
<CompactThemeToggle className="md:hidden" />

// Desktop theme toggle
<ThemeToggle variant="dropdown" className="hidden md:block" />
```

### Responsive Theme Classes

```css
/* Mobile-first responsive design */
.aone-responsive-card {
  @apply bg-card text-card-foreground rounded-lg p-4;
  @apply md:p-6 lg:p-8;
  @apply shadow-sm hover:shadow-md transition-shadow;
}
```

## 🎨 Custom Styling Patterns

### A.ONE Component Classes

```css
/* Professional banner */
.aone-banner {
  @apply bg-gradient-to-r from-aone-sage to-aone-sage-light;
  @apply text-white text-center py-3 px-4 text-sm font-medium;
}

/* Enterprise header */
.aone-header {
  @apply bg-white/95 backdrop-blur-md shadow-sm;
  @apply border-b border-aone-sage/10;
  @apply dark:bg-aone-warm-white/95 dark:border-aone-sage/20;
}

/* Elegant cards */
.aone-card-elegant {
  @apply bg-card text-card-foreground rounded-lg shadow-sm;
  @apply border border-border hover:shadow-md;
  @apply transition-all duration-200;
}
```

### Micro-Interactions

```css
/* Subtle animations */
.aone-micro-interaction {
  @apply transition-all duration-200 ease-in-out;
  @apply hover:scale-105 active:scale-95;
}

/* Focus states */
.aone-focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-aone-sage/50;
  @apply focus:ring-offset-2 focus:ring-offset-background;
}
```

## 🔍 Performance Optimization

### Theme Switching Performance

```tsx
// Optimized theme switching with transitions
const { setTheme, isTransitioning } = useTheme();

const handleThemeChange = (newTheme: Theme) => {
  // Theme switching is automatically optimized
  setTheme(newTheme);
};

// Show loading state during transitions
{isTransitioning && <div className="animate-pulse">Switching theme...</div>}
```

### CSS Custom Properties Best Practices

```css
/* Use semantic naming */
:root {
  --primary-color: hsl(var(--aone-sage));
  --primary-foreground: hsl(var(--aone-sage-foreground));
}

/* Avoid hardcoded values */
.component {
  background-color: hsl(var(--aone-sage)); /* ✅ Good */
  /* background-color: #6B7A4F; ❌ Avoid */
}
```

## 🧪 Testing Theme Implementation

### Playwright Theme Tests

```typescript
// Test theme switching functionality
test('theme switching works correctly', async ({ page }) => {
  await page.goto('/');
  
  // Test light to dark theme switch
  await page.click('[data-testid="theme-toggle"]');
  await expect(page.locator('html')).toHaveClass(/dark/);
  
  // Verify theme persistence
  await page.reload();
  await expect(page.locator('html')).toHaveClass(/dark/);
});
```

### Visual Regression Testing

```typescript
// Test theme visual consistency
test('theme visual consistency', async ({ page }) => {
  // Light theme screenshot
  await page.goto('/');
  await expect(page).toHaveScreenshot('light-theme.png');
  
  // Dark theme screenshot
  await page.click('[data-testid="theme-toggle"]');
  await expect(page).toHaveScreenshot('dark-theme.png');
});
```

## 🚀 Production Deployment

### Environment Configuration

```typescript
// Theme configuration for production
const themeConfig = {
  defaultTheme: 'system' as const,
  enableTransitions: true,
  storageKey: 'blackveil-design-mind-theme',
  enableDebugMode: process.env.NODE_ENV === 'development'
};
```

### Performance Monitoring

```typescript
// Monitor theme switching performance
import { PerformanceTracker } from '@/utils/performanceTracker';

const tracker = new PerformanceTracker();
tracker.trackOperation('theme_switch', () => {
  setTheme(newTheme);
});
```

## 📚 Best Practices

### Do's ✅

- Use semantic design tokens (`--aone-sage` instead of `#6B7A4F`)
- Test theme switching across all components
- Maintain WCAG 2.1 AA contrast ratios
- Use the `useTheme` hook for theme-aware logic
- Implement smooth transitions for theme changes

### Don'ts ❌

- Don't hardcode color values in components
- Don't skip accessibility testing for dark mode
- Don't forget to test theme persistence
- Don't override theme CSS custom properties directly
- Don't ignore mobile theme experience

## 🔗 Related Documentation

- [A.ONE Design System](./aone-design-system.md)
- [Dark Mode Implementation](./dark-mode-implementation.md)
- [Theme Fix Implementation](./theme-fix-implementation.md)
- [Critical Theme Fix Summary](./critical-theme-fix-summary.md)
