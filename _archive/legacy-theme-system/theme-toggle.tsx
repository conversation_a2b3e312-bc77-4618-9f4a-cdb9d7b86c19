import React from 'react';
import { <PERSON>, <PERSON>, <PERSON>, Pa<PERSON> } from 'lucide-react';
import { Button } from './button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from './dropdown-menu';
import { useTheme, Theme } from '@/hooks/useTheme';
import { cn } from '@/lib/utils';

interface ThemeToggleProps {
  variant?: 'button' | 'dropdown' | 'switch';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showLabel?: boolean;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  variant = 'dropdown',
  size = 'md',
  className,
  showLabel = false
}) => {
  const { theme, resolvedTheme, setTheme, toggleTheme, isTransitioning } = useTheme();

  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-9 w-9',
    lg: 'h-10 w-10'
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  const getThemeIcon = (themeType: Theme | 'resolved') => {
    const iconSize = iconSizes[size];
    
    switch (themeType) {
      case 'light':
        return <Sun className={cn(iconSize, 'text-amber-500')} />;
      case 'dark':
        return <Moon className={cn(iconSize, 'text-blue-400')} />;
      case 'system':
        return <Monitor className={cn(iconSize, 'text-aone-sage')} />;
      case 'resolved':
        return resolvedTheme === 'dark' 
          ? <Moon className={cn(iconSize, 'text-blue-400')} />
          : <Sun className={cn(iconSize, 'text-amber-500')} />;
      default:
        return <Palette className={cn(iconSize, 'text-aone-sage')} />;
    }
  };

  const getThemeLabel = (themeType: Theme) => {
    switch (themeType) {
      case 'light':
        return 'Light Mode';
      case 'dark':
        return 'Dark Mode';
      case 'system':
        return 'System Theme';
      default:
        return 'Theme';
    }
  };

  if (variant === 'button') {
    return (
      <Button
        variant="ghost"
        size="icon"
        onClick={toggleTheme}
        className={cn(
          sizeClasses[size],
          'aone-micro-interaction relative overflow-hidden',
          isTransitioning && 'animate-pulse-elegant',
          className
        )}
        aria-label={`Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} mode`}
      >
        <div className={cn(
          'absolute inset-0 flex items-center justify-center transition-transform duration-300',
          resolvedTheme === 'dark' ? 'rotate-0' : 'rotate-180'
        )}>
          {getThemeIcon('resolved')}
        </div>
      </Button>
    );
  }

  if (variant === 'switch') {
    return (
      <div className={cn('flex items-center space-x-3', className)}>
        {showLabel && (
          <span className="aone-body-enterprise text-sm">
            {getThemeLabel(theme)}
          </span>
        )}
        <button
          onClick={toggleTheme}
          className={cn(
            'relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-300',
            'aone-focus-ring aone-micro-interaction',
            resolvedTheme === 'dark' 
              ? 'bg-aone-sage' 
              : 'bg-gray-200 dark:bg-gray-700',
            isTransitioning && 'animate-pulse-elegant'
          )}
          aria-label={`Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} mode`}
          role="switch"
          aria-checked={resolvedTheme === 'dark'}
        >
          <span
            className={cn(
              'inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-300 shadow-lg',
              resolvedTheme === 'dark' ? 'translate-x-6' : 'translate-x-1'
            )}
          >
            <span className="flex h-full w-full items-center justify-center">
              {resolvedTheme === 'dark' 
                ? <Moon className="h-2 w-2 text-aone-sage" />
                : <Sun className="h-2 w-2 text-amber-500" />
              }
            </span>
          </span>
        </button>
      </div>
    );
  }

  // Default dropdown variant
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            sizeClasses[size],
            'aone-micro-interaction relative',
            isTransitioning && 'animate-pulse-elegant',
            className
          )}
          aria-label="Theme options"
        >
          <div className="relative">
            {getThemeIcon(theme)}
            {theme === 'system' && (
              <div className="absolute -bottom-1 -right-1">
                <div className="h-2 w-2 rounded-full bg-aone-sage border border-background" />
              </div>
            )}
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        align="end" 
        className="aone-glass border-aone-sage/20 min-w-[160px]"
      >
        <DropdownMenuLabel className="aone-subheading-enterprise text-xs">
          Theme Preference
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-aone-sage/10" />
        
        <DropdownMenuItem
          onClick={() => setTheme('light')}
          className={cn(
            'aone-micro-interaction cursor-pointer',
            theme === 'light' && 'bg-aone-sage/10 text-aone-sage'
          )}
        >
          <Sun className="mr-2 h-4 w-4 text-amber-500" />
          <span>Light Mode</span>
          {theme === 'light' && (
            <div className="ml-auto h-2 w-2 rounded-full bg-aone-sage" />
          )}
        </DropdownMenuItem>
        
        <DropdownMenuItem
          onClick={() => setTheme('dark')}
          className={cn(
            'aone-micro-interaction cursor-pointer',
            theme === 'dark' && 'bg-aone-sage/10 text-aone-sage'
          )}
        >
          <Moon className="mr-2 h-4 w-4 text-blue-400" />
          <span>Dark Mode</span>
          {theme === 'dark' && (
            <div className="ml-auto h-2 w-2 rounded-full bg-aone-sage" />
          )}
        </DropdownMenuItem>
        
        <DropdownMenuItem
          onClick={() => setTheme('system')}
          className={cn(
            'aone-micro-interaction cursor-pointer',
            theme === 'system' && 'bg-aone-sage/10 text-aone-sage'
          )}
        >
          <Monitor className="mr-2 h-4 w-4 text-aone-sage" />
          <span>System Theme</span>
          {theme === 'system' && (
            <div className="ml-auto h-2 w-2 rounded-full bg-aone-sage" />
          )}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

// Compact theme toggle for mobile/small spaces
export const CompactThemeToggle: React.FC<{ className?: string }> = ({ className }) => {
  const { resolvedTheme, toggleTheme, isTransitioning } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className={cn(
        'flex h-8 w-8 items-center justify-center rounded-lg',
        'aone-micro-interaction aone-focus-ring',
        'bg-aone-sage/10 hover:bg-aone-sage/20',
        'border border-aone-sage/20 hover:border-aone-sage/30',
        isTransitioning && 'animate-pulse-elegant',
        className
      )}
      aria-label={`Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} mode`}
    >
      {resolvedTheme === 'dark' 
        ? <Sun className="h-4 w-4 text-amber-500" />
        : <Moon className="h-4 w-4 text-blue-400" />
      }
    </button>
  );
};

export default ThemeToggle;
