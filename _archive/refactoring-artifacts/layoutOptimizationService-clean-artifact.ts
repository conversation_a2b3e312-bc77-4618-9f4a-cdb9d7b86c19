/**
 * Layout Optimization Service - Backward Compatibility Layer
 *
 * This file maintains backward compatibility with the existing Layout Optimization service API
 * while delegating to the new modular architecture. All new functionality should
 * use the modular services directly from './layout/'.
 *
 * @deprecated Use the modular services from './layout/' for new development
 */

import { createModuleLogger } from '@/utils/logger';
import {
  layoutOptimizationService as newLayoutOptimizationService,
  LayoutOptimizationConfig,
  LayoutOptimizationResult,
  WorkflowOptimization,
  SpaceUtilization,
  ErgonomicAssessment,
  TrafficFlowAnalysis,
  CostBenefitAnalysis,
  LayoutChange,
  SpaceOpportunity,
  ErgonomicRecommendation,
  FlowPath,
  CongestionPoint,
  ClearanceIssue
} from './layout';

const logger = createModuleLogger('LayoutOptimizationService-Legacy');

// Re-export types for backward compatibility
export { 
  LayoutOptimizationConfig,
  LayoutOptimizationResult,
  WorkflowOptimization,
  SpaceUtilization,
  ErgonomicAssessment,
  TrafficFlowAnalysis,
  CostBenefitAnalysis,
  LayoutChange,
  SpaceOpportunity,
  ErgonomicRecommendation,
  FlowPath,
  CongestionPoint,
  ClearanceIssue
};

/**
 * Layout Optimization Service - Backward Compatibility Wrapper
 * 
 * @deprecated This class is maintained for backward compatibility only.
 * Use the modular services from './layout/' for new development.
 */
export class LayoutOptimizationService {
  constructor() {
    logger.warn('Using legacy Layout Optimization service - consider migrating to modular services');
  }

  /**
   * Perform comprehensive layout optimization analysis (main method)
   */
  async optimizeLayout(
    imagePaths: string[],
    analysisId: string,
    config?: Partial<LayoutOptimizationConfig>
  ): Promise<LayoutOptimizationResult> {
    return await newLayoutOptimizationService.optimizeLayout(imagePaths, analysisId, config);
  }

  /**
   * Analyze workflow optimization only
   */
  async analyzeWorkflow(
    imagePaths: string[],
    analysisId: string,
    config?: Partial<LayoutOptimizationConfig>
  ): Promise<WorkflowOptimization> {
    return await newLayoutOptimizationService.analyzeWorkflow(imagePaths, analysisId, config);
  }

  /**
   * Analyze space utilization only
   */
  async analyzeSpaceUtilization(
    imagePaths: string[],
    analysisId: string,
    config?: Partial<LayoutOptimizationConfig>
  ): Promise<SpaceUtilization> {
    return await newLayoutOptimizationService.analyzeSpaceUtilization(imagePaths, analysisId, config);
  }

  /**
   * Perform ergonomic assessment only
   */
  async performErgonomicAssessment(
    imagePaths: string[],
    analysisId: string,
    config?: Partial<LayoutOptimizationConfig>
  ): Promise<ErgonomicAssessment> {
    return await newLayoutOptimizationService.performErgonomicAssessment(imagePaths, analysisId, config);
  }

  /**
   * Analyze traffic flow only
   */
  async analyzeTrafficFlow(
    imagePaths: string[],
    analysisId: string,
    config?: Partial<LayoutOptimizationConfig>
  ): Promise<TrafficFlowAnalysis> {
    return await newLayoutOptimizationService.analyzeTrafficFlow(imagePaths, analysisId, config);
  }

  /**
   * Perform cost-benefit analysis (requires other analysis results)
   */
  async performCostBenefitAnalysis(
    workflowOptimization: WorkflowOptimization,
    spaceUtilization: SpaceUtilization,
    ergonomicAssessment: ErgonomicAssessment,
    trafficFlowAnalysis: TrafficFlowAnalysis,
    analysisId: string,
    config?: Partial<LayoutOptimizationConfig>
  ): Promise<CostBenefitAnalysis> {
    return await newLayoutOptimizationService.performCostBenefitAnalysis(
      workflowOptimization,
      spaceUtilization,
      ergonomicAssessment,
      trafficFlowAnalysis,
      analysisId,
      config
    );
  }

  /**
   * Get default configuration
   */
  getDefaultConfig(): LayoutOptimizationConfig {
    return newLayoutOptimizationService.getDefaultConfig();
  }

  /**
   * Get configuration for specific optimization level
   */
  getConfigForLevel(level: 'BASIC' | 'DETAILED' | 'COMPREHENSIVE'): LayoutOptimizationConfig {
    return newLayoutOptimizationService.getConfigForLevel(level);
  }

  /**
   * Get accessibility-focused configuration
   */
  getAccessibilityConfig(): LayoutOptimizationConfig {
    return newLayoutOptimizationService.getAccessibilityConfig();
  }

  /**
   * Get cost-focused configuration
   */
  getCostFocusedConfig(): LayoutOptimizationConfig {
    return newLayoutOptimizationService.getCostFocusedConfig();
  }

  /**
   * Validate configuration
   */
  validateConfig(config: LayoutOptimizationConfig) {
    return newLayoutOptimizationService.validateConfig(config);
  }

  /**
   * Merge configuration with defaults
   */
  mergeWithDefaults(partialConfig: Partial<LayoutOptimizationConfig>): LayoutOptimizationConfig {
    return newLayoutOptimizationService.mergeWithDefaults(partialConfig);
  }

  /**
   * Get service health status
   */
  async getHealthStatus() {
    return await newLayoutOptimizationService.getHealthStatus();
  }

  /**
   * Get available analyzers
   */
  getAvailableAnalyzers(): string[] {
    return newLayoutOptimizationService.getAvailableAnalyzers();
  }

  // Backward compatibility methods

  /**
   * @deprecated Use optimizeLayout instead
   */
  async performLayoutOptimization(
    imagePaths: string[],
    analysisId: string,
    config?: Partial<LayoutOptimizationConfig>
  ): Promise<LayoutOptimizationResult> {
    logger.warn('performLayoutOptimization is deprecated, use optimizeLayout instead');
    return this.optimizeLayout(imagePaths, analysisId, config);
  }

  /**
   * @deprecated Use getHealthStatus instead
   */
  async checkServiceHealth(): Promise<boolean> {
    logger.warn('checkServiceHealth is deprecated, use getHealthStatus instead');
    const health = await this.getHealthStatus();
    return health.status !== 'unhealthy';
  }

  /**
   * @deprecated Use getAvailableAnalyzers instead
   */
  isServiceAvailable(): boolean {
    logger.warn('isServiceAvailable is deprecated, use getAvailableAnalyzers instead');
    return this.getAvailableAnalyzers().length > 0;
  }
}

// Export singleton instance for backward compatibility
export const layoutOptimizationService = new LayoutOptimizationService();

// Default export for backward compatibility
export default layoutOptimizationService;
