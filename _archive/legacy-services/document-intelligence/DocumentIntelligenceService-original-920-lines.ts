import { createModuleLogger } from '@/utils/logger';
import { DocumentIntelligenceConfig, DocumentAnalysisResult, DocumentProcessingOptions } from './types';
import { DocumentFileSystemManager } from './utils/DocumentFileSystemManager';
import { DocumentInitializationManager } from './utils/DocumentInitializationManager';

// OpenAI Service Interface for dependency injection
export interface IOpenAIService {
  analyzeImages(imagePaths: string[], prompt: string, config: any): Promise<any>;
}

// Default OpenAI Service import for backward compatibility
import { openaiService } from '../openai/OpenAIService';
import { InitializationState } from './utils/DocumentInitializationManager';

const logger = createModuleLogger('DocumentIntelligenceService');

/**
 * Document Intelligence Service - Refactored Modular Implementation
 *
 * Provides document intelligence capabilities using open-source libraries as an alternative
 * to Azure Document Intelligence. Integrates with existing PDF processing pipeline to extract
 * structured data from kitchen design documents, technical drawings, and specification sheets.
 *
 * Features:
 * - Advanced OCR using Tesseract.js with spatial information
 * - Table detection and extraction using pattern recognition
 * - Layout analysis using PDF parsing and image processing
 * - Kitchen-specific document processing enhanced with GPT-4o
 * - Identical API interface to Azure Document Intelligence for seamless integration
 * - Modular architecture with specialized utilities for maintainability
 * - 100% backward compatibility facade
 */
export class DocumentIntelligenceService {
  private fileSystemManager: DocumentFileSystemManager;
  private initializationManager: DocumentInitializationManager;
  private openAIService: IOpenAIService;
  private config: DocumentIntelligenceConfig;

  constructor(
    openAIService: IOpenAIService = openaiService,
    tempDir?: string,
    config?: DocumentIntelligenceConfig
  ) {
    // Dependency injection with defaults for backward compatibility
    this.openAIService = openAIService;
    this.config = config || this.loadConfiguration();

    // Initialize utility managers
    const resolvedTempDir = tempDir || this.getDefaultTempDir();
    this.fileSystemManager = new DocumentFileSystemManager(resolvedTempDir);
    this.initializationManager = new DocumentInitializationManager({
      timeout: 60000,
      maxRetries: 3,
      retryDelayBase: 1000,
      maxRetryDelay: 10000
    });
  }

  /**
   * Get default temp directory path
   */
  private getDefaultTempDir(): string {
    const path = require('path');
    return path.join(process.cwd(), 'temp', 'document-intelligence');
  }

  /**
   * Ensure service is initialized (Phase 2: Fixed state management)
   */
  private async ensureInitialized(): Promise<void> {
    // Initialize file system first
    await this.fileSystemManager.initialize();

    // Then initialize Tesseract worker
    await this.initializationManager.initialize();
  }

  /**
   * Check if service is available (Phase 2: Separated from initialization trigger)
   */
  async isAvailable(): Promise<boolean> {
    return this.initializationManager.isAvailable();
  }

  /**
   * Analyze document using open-source document intelligence
   */
  async analyzeDocument(
    documentPath: string,
    options: DocumentProcessingOptions = {}
  ): Promise<DocumentAnalysisResult> {
    // Ensure service is initialized before processing
    await this.ensureInitialized();

    if (!this.initializationManager.isAvailable()) {
      throw new Error('Document Intelligence service is not available');
    }

    const startTime = Date.now();
    logger.info('Starting document analysis', {
      documentPath,
      options: this.sanitizeOptionsForLogging(options)
    });

    try {
      // Determine file format from mimetype or original filename
      const fileExtension = this.determineFileExtension(documentPath, options);

      let imagePaths: string[] = [];

      // Convert PDF to images if needed using FileSystemManager
      if (fileExtension === '.pdf') {
        imagePaths = await this.fileSystemManager.convertPDFToImages(documentPath);
      } else if (['.jpg', '.jpeg', '.png', '.tiff', '.bmp'].includes(fileExtension)) {
        imagePaths = [documentPath];
      } else {
        throw new Error(`Unsupported file format: ${fileExtension}`);
      }

      // Process each image with OCR
      const ocrResults = await this.performOCRAnalysis(imagePaths, options);

      // Extract tables if requested
      const tables = options.extractTables ? await this.extractTablesFromImages(imagePaths) : [];

      // Extract key-value pairs if requested
      const keyValuePairs = options.extractKeyValuePairs ? await this.extractKeyValuePairsFromOCR(ocrResults) : [];

      // Enhance with GPT-4o analysis if kitchen analysis is enabled
      const enhancedResult = options.enableKitchenAnalysis
        ? await this.enhanceWithGPTAnalysis(ocrResults, imagePaths, options)
        : ocrResults;

      const processingTime = Date.now() - startTime;

      // Structure the results in Azure Document Intelligence format
      const analysisResult = this.formatAnalysisResult(
        enhancedResult,
        tables,
        keyValuePairs,
        processingTime,
        options
      );

      logger.info('Document analysis completed', {
        processingTime,
        pagesAnalyzed: imagePaths.length,
        tablesFound: tables.length,
        confidence: analysisResult.confidence
      });

      // Cleanup temporary files using FileSystemManager
      await this.fileSystemManager.cleanupTempFiles(imagePaths.filter(p => p !== documentPath));

      return analysisResult;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error('Document analysis failed', {
        error: (error as Error).message,
        processingTime,
        documentPath
      });
      throw error;
    }
  }

  /**
   * Determine file extension from various sources
   */
  private determineFileExtension(documentPath: string, options: DocumentProcessingOptions): string {
    const path = require('path');

    let fileExtension = '';
    if (options.mimetype) {
      // Use mimetype to determine file format
      const mimeToExt: { [key: string]: string } = {
        'application/pdf': '.pdf',
        'image/jpeg': '.jpg',
        'image/jpg': '.jpg',
        'image/png': '.png',
        'image/tiff': '.tiff',
        'image/bmp': '.bmp'
      };
      fileExtension = mimeToExt[options.mimetype] || '';
    } else if (options.originalFilename) {
      // Fallback to original filename extension
      fileExtension = path.extname(options.originalFilename).toLowerCase();
    } else {
      // Last resort: try to get extension from saved file path
      fileExtension = path.extname(documentPath).toLowerCase();
    }

    if (!fileExtension) {
      throw new Error(`Unable to determine file format. Mimetype: ${options.mimetype}, Original filename: ${options.originalFilename}`);
    }

    return fileExtension;
  }

  /**
   * Batch analyze multiple documents
   */
  async analyzeDocuments(
    documentPaths: string[],
    options: DocumentProcessingOptions = {}
  ): Promise<DocumentAnalysisResult[]> {
    logger.info('Starting batch document analysis', {
      documentCount: documentPaths.length,
      options: this.sanitizeOptionsForLogging(options)
    });

    const results: DocumentAnalysisResult[] = [];
    const errors: Array<{ path: string; error: string }> = [];

    for (const documentPath of documentPaths) {
      try {
        const result = await this.analyzeDocument(documentPath, options);
        results.push(result);
      } catch (error) {
        logger.error(`Failed to analyze document: ${documentPath}`, error);
        errors.push({
          path: documentPath,
          error: error.message
        });
      }
    }

    logger.info('Batch document analysis completed', {
      successful: results.length,
      failed: errors.length,
      totalDocuments: documentPaths.length
    });

    if (errors.length > 0) {
      logger.warn('Some documents failed analysis', { errors });
    }

    return results;
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details: {
      clientInitialized: boolean;
      configurationValid: boolean;
      lastChecked: string;
      errorMessage?: string;
    };
  }> {
    const lastChecked = new Date().toISOString();

    try {
      const isAvailable = await this.isAvailable();

      if (!isAvailable) {
        return {
          status: 'unhealthy',
          details: {
            clientInitialized: false,
            configurationValid: false,
            lastChecked,
            errorMessage: 'Service initialization failed'
          }
        };
      }

      // Test service connectivity with a minimal operation
      return {
        status: 'healthy',
        details: {
          clientInitialized: true,
          configurationValid: true,
          lastChecked
        }
      };
    } catch (error) {
      logger.error('Document Intelligence health check failed:', error);
      return {
        status: 'degraded',
        details: {
          clientInitialized: false,
          configurationValid: true,
          lastChecked,
          errorMessage: (error as Error).message
        }
      };
    }
  }



  /**
   * Perform OCR analysis on images using Tesseract
   */
  private async performOCRAnalysis(imagePaths: string[], options: DocumentProcessingOptions): Promise<any> {
    const sharp = require('sharp');
    const worker = this.initializationManager.getWorker();
    const pages = [];

    for (let i = 0; i < imagePaths.length; i++) {
      const imagePath = imagePaths[i];

      // Get image metadata
      const metadata = await sharp(imagePath).metadata();

      // Perform OCR using the worker from initialization manager
      const { data } = await worker.recognize(imagePath);

      // Debug: Log the structure of the Tesseract data
      logger.info('Tesseract OCR data structure', {
        imagePath,
        hasData: !!data,
        dataKeys: data ? Object.keys(data) : [],
        hasLines: !!(data && data.lines),
        linesLength: data && data.lines ? data.lines.length : 0,
        hasText: !!(data && data.text),
        textLength: data && data.text ? data.text.length : 0
      });

      // Process OCR results with spatial information
      let lines = [];
      if (data && data.lines && Array.isArray(data.lines)) {
        lines = data.lines.map((line: any) => ({
          content: line.text || '',
          boundingBox: line.bbox ? [line.bbox.x0, line.bbox.y0, line.bbox.x1, line.bbox.y1] : [0, 0, 0, 0],
          confidence: (line.confidence || 0) / 100 // Convert to 0-1 scale
        }));
      } else {
        // Fallback: create a single line from the text if lines are not available
        logger.warn('Tesseract lines not available, creating fallback line structure', {
          imagePath,
          hasText: !!(data && data.text)
        });
        if (data && data.text) {
          lines = [{
            content: data.text,
            boundingBox: [0, 0, metadata.width || 0, metadata.height || 0],
            confidence: 0.8 // Default confidence
          }];
        }
      }

      pages.push({
        pageNumber: i + 1,
        width: metadata.width || 0,
        height: metadata.height || 0,
        unit: 'pixel',
        lines,
        text: data.text
      });
    }

    return {
      content: pages.map(p => p.text).join('\n'),
      pages,
      confidence: pages.reduce((sum, p) => sum + p.lines.reduce((lineSum: number, l: any) => lineSum + l.confidence, 0) / p.lines.length, 0) / pages.length
    };
  }

  /**
   * Extract tables from images using pattern recognition
   */
  private async extractTablesFromImages(imagePaths: string[]): Promise<any[]> {
    const worker = this.initializationManager.getWorker();
    const tables = [];

    for (const imagePath of imagePaths) {
      try {
        // Simple table detection using OCR data analysis
        const { data } = await worker.recognize(imagePath);

        // Look for table-like patterns in the text
        const lines = data.text.split('\n').filter((line: string) => line.trim().length > 0);
        const potentialTableLines = lines.filter((line: string) => {
          // Simple heuristic: lines with multiple spaces or tabs might be table rows
          return line.includes('\t') || line.match(/\s{3,}/);
        });

        if (potentialTableLines.length >= 2) {
          // Create a simple table structure
          const cells = potentialTableLines.map((line: string, rowIndex: number) => {
            const columns = line.split(/\s{3,}|\t/).filter(col => col.trim().length > 0);
            return columns.map((content: string, colIndex: number) => ({
              content: content.trim(),
              rowIndex,
              columnIndex: colIndex,
              confidence: 0.8 // Default confidence for pattern-based detection
            }));
          }).flat();

          if (cells.length > 0) {
            const maxColumns = Math.max(...potentialTableLines.map(line =>
              line.split(/\s{3,}|\t/).filter(col => col.trim().length > 0).length
            ));

            tables.push({
              rowCount: potentialTableLines.length,
              columnCount: maxColumns,
              cells
            });
          }
        }
      } catch (error) {
        logger.warn(`Failed to extract tables from ${imagePath}:`, error);
      }
    }

    return tables;
  }



  /**
   * Enhance analysis with GPT-4o for kitchen-specific understanding
   */
  private async enhanceWithGPTAnalysis(ocrResults: any, imagePaths: string[], options: DocumentProcessingOptions): Promise<any> {
    try {
      const prompt = `Analyze this kitchen design document text and enhance the extracted information:

Text Content:
${ocrResults.content}

Please provide enhanced analysis focusing on:
1. Kitchen cabinet specifications and counts
2. Appliance details and specifications
3. Material types and finishes
4. Dimensions and measurements
5. Hardware specifications

Return the analysis in a structured format that improves upon the OCR results.`;

      const gptAnalysis = await this.openAIService.analyzeImages(imagePaths, prompt, {
        useGPT4o: true,
        useReasoning: false,
        focusOnMaterials: true,
        focusOnHardware: true,
        enableMultiView: false
      });

      // Combine OCR results with GPT analysis
      return {
        ...ocrResults,
        enhancedContent: gptAnalysis?.analysis || '',
        gptConfidence: gptAnalysis?.confidence || 0.8,
        kitchenSpecificAnalysis: this.extractKitchenSpecificData(gptAnalysis?.analysis || '')
      };

    } catch (error) {
      logger.warn('GPT enhancement failed, using OCR results only:', error);
      return ocrResults;
    }
  }

  /**
   * Extract kitchen-specific data from GPT analysis
   */
  private extractKitchenSpecificData(analysis: string): any {
    const kitchenData = {
      cabinets: [] as Array<{ description: string; confidence: number }>,
      appliances: [] as Array<{ description: string; confidence: number }>,
      materials: [] as Array<{ description: string; confidence: number }>,
      dimensions: [] as Array<{ measurement: string; confidence: number }>
    };

    // Simple extraction patterns for kitchen elements
    const cabinetMatches = analysis.match(/cabinet[s]?[^.]*\d+/gi) || [];
    const applianceMatches = analysis.match(/(refrigerator|oven|dishwasher|microwave|range)[^.]*\d*/gi) || [];
    const materialMatches = analysis.match(/(wood|granite|quartz|marble|steel|laminate)[^.]*\d*/gi) || [];
    const dimensionMatches = analysis.match(/\d+\s*(mm|cm|m|in|ft|'|")/gi) || [];

    kitchenData.cabinets = cabinetMatches.map(match => ({ description: match, confidence: 0.8 }));
    kitchenData.appliances = applianceMatches.map(match => ({ description: match, confidence: 0.8 }));
    kitchenData.materials = materialMatches.map(match => ({ description: match, confidence: 0.8 }));
    kitchenData.dimensions = dimensionMatches.map(match => ({ measurement: match, confidence: 0.8 }));

    return kitchenData;
  }

  /**
   * Format analysis results in Azure Document Intelligence compatible format
   */
  private formatAnalysisResult(
    ocrResults: any,
    tables: any[],
    keyValuePairs: any[],
    processingTime: number,
    options: DocumentProcessingOptions
  ): DocumentAnalysisResult {
    return {
      documentType: this.detectDocumentType(ocrResults),
      extractedText: ocrResults.content,
      tables,
      layout: {
        pages: ocrResults.pages || []
      },
      keyValuePairs,
      confidence: ocrResults.confidence || 0.8,
      processingTime,
      metadata: {
        modelUsed: options.modelType || 'tesseract-ocr',
        apiVersion: 'open-source-v1.0',
        pagesProcessed: ocrResults.pages?.length || 0,
        timestamp: new Date().toISOString()
      }
    };
  }



  /**
   * Load configuration from environment variables
   */
  private loadConfiguration(): DocumentIntelligenceConfig {
    return {
      endpoint: 'open-source-implementation',
      apiKey: 'not-required',
      apiVersion: 'open-source-v1.0',
      defaultModel: 'tesseract-ocr',
      maxRetries: 3,
      retryDelay: 1000
    };
  }

  /**
   * Extract key-value pairs from OCR results (overloaded method)
   */
  private async extractKeyValuePairsFromOCR(ocrResults: any): Promise<any[]> {
    const keyValuePairs = [];
    const text = ocrResults.content;

    // Simple pattern matching for key-value pairs
    const patterns = [
      /([A-Za-z\s]+):\s*([^\n\r]+)/g, // "Key: Value" pattern
      /([A-Za-z\s]+)\s*=\s*([^\n\r]+)/g, // "Key = Value" pattern
      /([A-Za-z\s]+)\s*-\s*([^\n\r]+)/g  // "Key - Value" pattern
    ];

    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const key = match[1]?.trim();
        const value = match[2]?.trim();

        if (key && value && key.length > 2 && value.length > 0) {
          keyValuePairs.push({
            key,
            value,
            confidence: 0.7 // Default confidence for pattern-based extraction
          });
        }
      }
    }

    return keyValuePairs;
  }

  /**
   * Detect document type based on content analysis
   */
  private detectDocumentType(result: any): string {
    const content = result.content?.toLowerCase() || '';

    // Kitchen-specific document type detection
    if (content.includes('cabinet') || content.includes('kitchen') || content.includes('countertop')) {
      if (content.includes('specification') || content.includes('spec')) {
        return 'kitchen_specification';
      }
      if (content.includes('drawing') || content.includes('plan') || content.includes('layout')) {
        return 'kitchen_plan';
      }
      if (content.includes('price') || content.includes('cost') || content.includes('quote')) {
        return 'pricing_document';
      }
      return 'kitchen_document';
    }

    // General document types
    if (content.includes('table') && result.tables?.length > 0) {
      return 'tabular_document';
    }

    return 'general_document';
  }

  /**
   * Calculate overall confidence score
   */
  private calculateOverallConfidence(result: any): number {
    const confidenceScores: number[] = [];

    // Collect confidence scores from various elements
    if (result.pages) {
      result.pages.forEach((page: any) => {
        if (page.lines) {
          page.lines.forEach((line: any) => {
            if (line.confidence) {
              confidenceScores.push(line.confidence);
            }
          });
        }
      });
    }

    if (result.tables) {
      result.tables.forEach((table: any) => {
        if (table.cells) {
          table.cells.forEach((cell: any) => {
            if (cell.confidence) {
              confidenceScores.push(cell.confidence);
            }
          });
        }
      });
    }

    if (confidenceScores.length === 0) {
      return 0.8; // Default confidence if no scores available
    }

    // Calculate weighted average confidence
    return confidenceScores.reduce((sum, score) => sum + score, 0) / confidenceScores.length;
  }

  /**
   * Sanitize options for logging (remove sensitive data)
   */
  private sanitizeOptionsForLogging(options: DocumentProcessingOptions): any {
    return {
      modelType: options.modelType,
      extractTables: options.extractTables,
      extractKeyValuePairs: options.extractKeyValuePairs,
      enableKitchenAnalysis: options.enableKitchenAnalysis
    };
  }

  /**
   * Cleanup resources and terminate Tesseract worker
   */
  async cleanup(): Promise<void> {
    try {
      // Cleanup initialization manager (handles Tesseract worker termination)
      await this.initializationManager.cleanup();

      logger.info('Document Intelligence service cleaned up successfully');
    } catch (error) {
      logger.warn('Error during cleanup:', error);
    }
  }
}