<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Loading Detection Test - Blackveil Design Mind</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .metric-label {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>CSS Loading Detection Test</h1>
        <p>This page tests the CSS loading detection mechanism implemented in Blackveil Design Mind.</p>
        
        <div id="status" class="status info">
            🔍 Initializing CSS loading detection test...
        </div>

        <div class="metrics">
            <div class="metric">
                <div class="metric-value" id="detection-time">--</div>
                <div class="metric-label">Detection Time (ms)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="attempts-count">--</div>
                <div class="metric-label">Attempts Made</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="success-rate">--</div>
                <div class="metric-label">Success Rate (%)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="css-properties">--</div>
                <div class="metric-label">CSS Properties Found</div>
            </div>
        </div>

        <div>
            <button onclick="runSingleTest()">Run Single Test</button>
            <button onclick="runMultipleTests()">Run 10 Tests</button>
            <button onclick="simulateSlowCSS()">Simulate Slow CSS</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>

        <div id="log" class="log">Test log will appear here...</div>
    </div>

    <script>
        let testResults = [];
        let logElement = document.getElementById('log');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(`%c${logMessage}`, 
                type === 'error' ? 'color: red' : 
                type === 'success' ? 'color: green' : 
                type === 'warning' ? 'color: orange' : 'color: blue'
            );
        }

        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function updateMetrics() {
            if (testResults.length === 0) return;

            const detectionTimes = testResults.map(r => r.duration);
            const attempts = testResults.map(r => r.attempts);
            const successCount = testResults.filter(r => r.success).length;

            document.getElementById('detection-time').textContent = 
                Math.round(detectionTimes.reduce((a, b) => a + b, 0) / detectionTimes.length);
            document.getElementById('attempts-count').textContent = 
                Math.round(attempts.reduce((a, b) => a + b, 0) / attempts.length);
            document.getElementById('success-rate').textContent = 
                Math.round((successCount / testResults.length) * 100);
            document.getElementById('css-properties').textContent = 
                testResults[testResults.length - 1]?.cssPropertiesFound || 0;
        }

        // CSS Loading Detection Implementation (from main.tsx)
        function waitForCSSLoading() {
            return new Promise((resolve, reject) => {
                const startTime = performance.now();
                const timeout = 5000; // 5 second timeout
                const checkInterval = 50; // Check every 50ms
                let attemptCount = 0;

                log('🔍 Starting CSS loading detection...');

                const checkCSSProperties = () => {
                    attemptCount++;
                    const currentTime = performance.now();
                    const elapsed = currentTime - startTime;

                    // Create test element to check CSS custom properties
                    const testElement = document.createElement('div');
                    testElement.className = 'test-css-properties';
                    testElement.style.position = 'absolute';
                    testElement.style.top = '-9999px';
                    testElement.style.left = '-9999px';
                    document.body.appendChild(testElement);

                    const computedStyle = getComputedStyle(testElement);
                    
                    // Check for CSS validation property (primary indicator)
                    const testValidation = computedStyle.getPropertyValue('--test-validation').trim();
                    
                    // Check for critical A.ONE properties (secondary indicators)
                    const sageColor = computedStyle.getPropertyValue('--aone-sage').trim();
                    const backgroundColor = computedStyle.getPropertyValue('--background').trim();
                    
                    document.body.removeChild(testElement);

                    // Log detailed progress
                    log(`🔍 CSS Detection Attempt ${attemptCount} (${elapsed.toFixed(0)}ms): testValidation="${testValidation}", sageColor="${sageColor}", backgroundColor="${backgroundColor}"`);

                    // Success condition: CSS validation property indicates loaded
                    if (testValidation.includes('loaded') && sageColor && backgroundColor) {
                        log(`✅ CSS loading detection successful after ${elapsed.toFixed(0)}ms (${attemptCount} attempts)`, 'success');
                        resolve({
                            success: true,
                            duration: elapsed,
                            attempts: attemptCount,
                            cssPropertiesFound: [testValidation, sageColor, backgroundColor].filter(Boolean).length
                        });
                        return;
                    }

                    // Timeout condition
                    if (elapsed >= timeout) {
                        log(`❌ CSS loading detection timeout after ${elapsed.toFixed(0)}ms (${attemptCount} attempts)`, 'error');
                        reject({
                            success: false,
                            duration: elapsed,
                            attempts: attemptCount,
                            cssPropertiesFound: [testValidation, sageColor, backgroundColor].filter(Boolean).length,
                            error: `CSS loading timeout after ${timeout}ms`
                        });
                        return;
                    }

                    // Continue checking
                    setTimeout(checkCSSProperties, checkInterval);
                };

                // Start checking immediately
                checkCSSProperties();
            });
        }

        async function runSingleTest() {
            updateStatus('🧪 Running single CSS loading detection test...', 'info');
            log('🧪 Starting single test...');

            try {
                const result = await waitForCSSLoading();
                testResults.push(result);
                updateStatus(`✅ Test completed successfully in ${result.duration.toFixed(0)}ms`, 'success');
                log(`✅ Single test completed: ${JSON.stringify(result)}`, 'success');
            } catch (error) {
                testResults.push(error);
                updateStatus(`❌ Test failed: ${error.error}`, 'error');
                log(`❌ Single test failed: ${JSON.stringify(error)}`, 'error');
            }

            updateMetrics();
        }

        async function runMultipleTests() {
            updateStatus('🧪 Running 10 CSS loading detection tests...', 'info');
            log('🧪 Starting multiple tests (10 iterations)...');

            const results = [];
            for (let i = 1; i <= 10; i++) {
                log(`🔄 Running test ${i}/10...`);
                try {
                    const result = await waitForCSSLoading();
                    results.push(result);
                    log(`✅ Test ${i} completed in ${result.duration.toFixed(0)}ms`, 'success');
                } catch (error) {
                    results.push(error);
                    log(`❌ Test ${i} failed: ${error.error}`, 'error');
                }

                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            testResults.push(...results);
            const successCount = results.filter(r => r.success).length;
            const avgTime = results.reduce((sum, r) => sum + r.duration, 0) / results.length;

            updateStatus(`📊 Multiple tests completed: ${successCount}/10 successful, avg ${avgTime.toFixed(0)}ms`, 
                successCount >= 8 ? 'success' : successCount >= 5 ? 'warning' : 'error');
            
            log(`📊 Multiple tests summary: ${successCount}/10 successful, average time: ${avgTime.toFixed(0)}ms`);
            updateMetrics();
        }

        function simulateSlowCSS() {
            updateStatus('🐌 Simulating slow CSS loading...', 'warning');
            log('🐌 Simulating slow CSS by temporarily removing CSS properties...');

            // Temporarily remove CSS custom properties to simulate slow loading
            const style = document.createElement('style');
            style.id = 'slow-css-simulation';
            style.textContent = `
                .test-css-properties {
                    --test-validation: '';
                    --aone-sage: '';
                    --background: '';
                }
            `;
            document.head.appendChild(style);

            // Remove the simulation after 2 seconds
            setTimeout(() => {
                const simulationStyle = document.getElementById('slow-css-simulation');
                if (simulationStyle) {
                    simulationStyle.remove();
                    log('🔄 CSS simulation removed, properties should be available again');
                }
            }, 2000);

            // Run test during simulation
            setTimeout(() => runSingleTest(), 500);
        }

        function clearLog() {
            logElement.textContent = '';
            testResults = [];
            updateStatus('🔍 Log cleared, ready for new tests', 'info');
            updateMetrics();
        }

        // Add CSS properties for testing (simulating the actual CSS from index.css)
        const testCSS = document.createElement('style');
        testCSS.textContent = `
            .test-css-properties {
                --test-validation: 'loaded' !important;
                --aone-sage: 82 25% 45% !important;
                --background: 0 0% 100% !important;
                --aone-charcoal: 0 0% 18% !important;
            }
        `;
        document.head.appendChild(testCSS);

        // Initialize
        log('🚀 CSS Loading Detection Test initialized');
        log('📋 Available tests: Single Test, Multiple Tests (10x), Slow CSS Simulation');
        updateStatus('✅ Test environment ready', 'success');
    </script>
</body>
</html>
