# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
dist-ssr/
build/
out/
.next/
.nuxt/
.cache/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# Logs
logs/
*.log
server/logs/
server/*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output/

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache/

# Vite cache
.vite/

# Rollup cache
.rollup.cache/

# Temporary folders
tmp/
temp/
server/temp/
server/tmp/

# File uploads (keep structure but ignore content)
uploads/
server/uploads/
!server/uploads/.gitkeep

# AI/ML temporary files
*.pkl
*.h5
*.model
*.weights

# Test results and artifacts
test-results/
playwright-report/
coverage/
.nyc_output/
junit.xml
test-output/

# Playwright browsers (optional - comment out if you want to cache browsers)
# /ms-playwright/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Windows
*.exe
*.msi
*.msm
*.msp

# Linux
*~

# Project specific
# AI service data (keep structure but ignore dynamic content)
server/data/ab_tests/tests/
server/data/ab_tests/results/
server/data/optimization/
server/data/reasoning/
!server/data/**/.gitkeep

# PDF processing temporary files
*.pdf.tmp
*.png.tmp
*.jpg.tmp
*.jpeg.tmp

# Lock files (keep one, ignore others)
package-lock.json
!server/package-lock.json
yarn.lock
pnpm-lock.yaml

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Certificate files
*.pem
*.key
*.crt
*.cert

# Database files
*.db
*.sqlite
*.sqlite3

# Redis dump
dump.rdb

# Monitoring and metrics
metrics/
monitoring/

# Documentation build
docs/build/
docs/.vuepress/dist/

# Storybook build outputs
storybook-static/

# Temporary files created by editors
.tmp/
.temp/
