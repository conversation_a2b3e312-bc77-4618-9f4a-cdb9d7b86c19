# Testing Guide - Cabinet Insight Pro

This document provides comprehensive information about testing the Cabinet Insight Pro application, including both manual testing procedures and automated test execution.

## Overview

The Cabinet Insight Pro application includes a comprehensive test suite built with <PERSON><PERSON> that verifies:

- ✅ **Azure OpenAI Integration**: Real API calls vs mock responses
- ✅ **API Functionality**: All backend endpoints and services
- ✅ **Frontend Components**: UI interactions and user workflows
- ✅ **Real-time Features**: WebSocket connections and live updates
- ✅ **Cross-browser Compatibility**: Multiple browsers and devices
- ✅ **Advanced Network Monitoring**: Dynamic timeout adjustment based on connection quality
- ✅ **Intelligent Test Optimization**: Automated batching and performance analysis
- ✅ **Environment Validation**: Pre-test system health checks
- ✅ **Enhanced Error Recovery**: Browser-specific retry strategies

## Advanced Test Optimization Features

### Network Condition Detection
The test suite automatically detects network conditions and adjusts timeouts dynamically:
- **Excellent**: < 100ms latency, < 5% packet loss → 30s timeout
- **Good**: < 300ms latency, < 15% packet loss → 60s timeout
- **Fair**: < 1000ms latency, < 30% packet loss → 120s timeout
- **Poor**: > 1000ms latency, > 30% packet loss → 180s timeout

### Environment Validation
Pre-test checks ensure optimal testing conditions:
- Frontend/backend server availability
- Azure OpenAI configuration validation
- File system dependencies verification
- Browser capabilities assessment
- WebSocket connectivity testing

### Intelligent Test Batching
Tests are automatically grouped for optimal resource usage:
- **API Tests**: Low resource, high priority batches
- **Frontend Tests**: Medium resource, parallel execution
- **Integration Tests**: High resource, sequential execution
- **Performance Tests**: Resource-aware scheduling

### Cross-Browser Compatibility Matrix
Automatic handling of browser-specific issues:
- **Firefox**: Enhanced retry logic for navigation timeouts
- **WebKit**: Increased timeouts for file upload operations
- **Mobile Browsers**: Reduced parallelism, extended timeouts
- **Known Issues**: Automatic test skipping for unsupported features

### Performance Metrics Collection
Comprehensive monitoring and reporting:
- Test execution duration tracking
- Network quality distribution analysis
- Browser-specific performance statistics
- Retry count and failure pattern analysis
- Automated performance recommendations

## Test Architecture

### Test Structure

```
tests/
├── api/                    # API integration tests
│   ├── health.spec.ts     # Health endpoint verification
│   └── analysis.spec.ts   # Analysis API functionality
├── frontend/              # Frontend UI tests
│   ├── app-loading.spec.ts # Application loading and navigation
│   └── file-upload.spec.ts # File upload UI components
├── integration/           # Full integration tests
│   ├── azure-openai.spec.ts       # Azure OpenAI functionality
│   ├── azure-openai-simple.spec.ts # Quick Azure OpenAI verification
│   └── websocket.spec.ts          # WebSocket real-time features
├── fixtures/              # Test data files
├── utils/                 # Test utilities and helpers
└── README.md              # Test documentation
```

### Test Categories

1. **API Integration Tests**: Verify backend API functionality
2. **Frontend Integration Tests**: Test UI components and user interactions
3. **Azure OpenAI Integration Tests**: Confirm real AI API integration
4. **WebSocket Tests**: Validate real-time communication
5. **Cross-browser Tests**: Ensure compatibility across browsers

## Prerequisites

### System Requirements

- Node.js 18+
- npm package manager
- Both frontend and backend servers running
- Valid Azure OpenAI configuration

### Installation

```bash
# Install test dependencies
npm install
npx playwright install

# Install browsers for testing
npx playwright install chromium firefox webkit
```

### Environment Setup

1. **Start Backend Server**:
   ```bash
   cd server && npm run dev
   ```

2. **Start Frontend Server**:
   ```bash
   npm run dev
   ```

3. **Verify Azure OpenAI Configuration** in `server/.env`:
   ```bash
   AZURE_OPENAI_API_KEY=your_azure_openai_api_key
   AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
   AZURE_OPENAI_DEPLOYMENT_GPT4O=gpt-4o
   AZURE_OPENAI_DEPLOYMENT_GPT4O_MINI=gpt-4o-mini
   ```

## Running Tests

### Quick Test Commands

```bash
# Run all tests
npm run test

# Run specific test suites
npm run test:api          # API integration tests only
npm run test:frontend     # Frontend UI tests only
npm run test:integration  # Integration tests only
npm run test:azure        # Azure OpenAI specific tests

# Advanced monitoring and optimization
npm run test:monitoring   # Enhanced monitoring demo tests
npm run test:performance  # Performance analysis with metrics
npm run test:compatibility # Cross-browser compatibility testing

# Interactive testing modes
npm run test:headed       # Run with browser visible
npm run test:ui          # Run with Playwright UI
npm run test:debug       # Debug mode with step-through

# Test reporting
npm run test:report       # View HTML test report
```

### Advanced Test Execution

```bash
# Run tests on specific browser
npx playwright test --project=chromium
npx playwright test --project=firefox
npx playwright test --project=webkit

# Run specific test file
npx playwright test tests/api/health.spec.ts

# Run tests with custom timeout
npx playwright test --timeout=180000

# Run tests in parallel
npx playwright test --workers=4

# Generate test artifacts
npx playwright test --trace=on --video=on --screenshot=on
```

## Test Verification

### Azure OpenAI Integration Verification

The tests specifically verify that the application is using real Azure OpenAI integration:

1. **Configuration Detection**: Confirms Azure OpenAI endpoint is configured
2. **Real API Calls**: Verifies actual API requests (not mock responses)
3. **Model Accessibility**: Tests both GPT-4o and GPT-4o-mini models
4. **Token Usage**: Validates real token consumption statistics
5. **Processing Pipeline**: Confirms end-to-end AI analysis workflow

### Expected Test Results

When tests pass successfully, you should see:

```
✅ Azure OpenAI Endpoint: https://your-resource.openai.azure.com/
✅ Configuration Type: azure
✅ Analysis started with ID: [UUID]
✅ Analysis is actively processing - confirms real Azure OpenAI integration
✅ Real Azure OpenAI usage: [X] tokens
✅ Model used: gpt-4o or gpt-4o-mini
```

## Test Coverage Details

### API Integration Tests (tests/api/)

**Health Endpoints (`health.spec.ts`)**:
- Basic health check functionality
- Detailed health status with Azure OpenAI configuration
- Service status verification
- System readiness and liveness checks

**Analysis Endpoints (`analysis.spec.ts`)**:
- File upload functionality (PDF/PNG)
- Analysis configuration options
- Queue status monitoring
- Prompt management
- Default configuration retrieval
- Error handling and validation

### Frontend Integration Tests (tests/frontend/)

**Application Loading (`app-loading.spec.ts`)**:
- Main application loading verification
- Navigation between pages
- Responsive design testing
- Error page handling

**File Upload UI (`file-upload.spec.ts`)**:
- File upload interface functionality
- Drag and drop support
- File type validation
- Analysis configuration UI
- Progress indicators

### Integration Tests (tests/integration/)

**Azure OpenAI Integration (`azure-openai.spec.ts`)**:
- Azure OpenAI configuration verification
- Real API call testing (not mock responses)
- GPT-4o model functionality
- GPT-4o-mini model functionality
- Dual-model reasoning workflow

**WebSocket Real-time (`websocket.spec.ts`)**:
- WebSocket connection establishment
- Real-time progress updates
- Analysis completion notifications
- Connection resilience testing

**Enhanced Monitoring Demo (`enhanced-monitoring-demo.spec.ts`)**:
- Adaptive timeout demonstration
- Network condition detection
- Intelligent retry strategies
- Performance metrics collection
- Cross-browser compatibility handling
- Test batching recommendations

## Troubleshooting

### Common Issues

1. **Servers Not Running**:
   ```
   Error: connect ECONNREFUSED ::1:8080
   ```
   **Solution**: Start both frontend and backend servers

2. **Azure OpenAI Not Configured**:
   ```
   OpenAI service is not configured
   ```
   **Solution**: Set Azure OpenAI environment variables in `server/.env`

3. **Test Timeouts**:
   ```
   Test timeout of 120000ms exceeded
   ```
   **Solution**: Check Azure OpenAI API connectivity and increase timeout

4. **File Upload Failures**:
   ```
   File not found: kitchen-design-test.pdf
   ```
   **Solution**: Run `node tests/fixtures/create-test-files.cjs`

### Debug Mode

Use debug mode to step through tests interactively:

```bash
npm run test:debug
```

This opens the Playwright Inspector where you can:
- Step through test actions
- Inspect page elements
- View network requests
- Debug test failures

### Test Artifacts

Failed tests automatically generate:
- **Screenshots**: Visual state at failure point
- **Videos**: Recording of test execution
- **Traces**: Detailed execution timeline
- **Network logs**: API request/response data

Artifacts are saved in `test-results/` directory.

## Continuous Integration

### CI/CD Configuration

The tests are designed for CI/CD environments:

```yaml
# Example GitHub Actions workflow
- name: Run Playwright Tests
  run: |
    npm ci
    npx playwright install --with-deps
    npm run test
  env:
    AZURE_OPENAI_API_KEY: ${{ secrets.AZURE_OPENAI_API_KEY }}
    AZURE_OPENAI_ENDPOINT: ${{ secrets.AZURE_OPENAI_ENDPOINT }}
```

### Test Reporting

Multiple report formats are generated:
- **HTML Report**: Interactive test results viewer
- **JSON Report**: Machine-readable test data
- **JUnit Report**: CI/CD integration format

## Best Practices

### Writing New Tests

1. **Use Test Helpers**: Leverage `TestHelpers` class for common operations
2. **Proper Timeouts**: Set appropriate timeouts for AI operations
3. **Real Data**: Use actual test files, not mock data
4. **Error Handling**: Include both positive and negative test cases
5. **Documentation**: Document test purpose and expected behavior

### Test Maintenance

1. **Regular Updates**: Keep tests updated with application changes
2. **Flaky Test Management**: Identify and fix unstable tests
3. **Performance Monitoring**: Track test execution times
4. **Coverage Analysis**: Ensure comprehensive test coverage

## Support

For testing-related questions or issues:

- **Documentation**: See `tests/README.md` for detailed test information
- **GitHub Issues**: Report test failures or enhancement requests
- **Debug Logs**: Use `npm run test:debug` for interactive debugging

---

*This testing guide ensures comprehensive verification of the Cabinet Insight Pro application's Azure OpenAI integration and overall functionality.*
