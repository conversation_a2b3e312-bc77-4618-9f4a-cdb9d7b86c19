# Enhanced API Endpoints Documentation

This document describes the new API endpoints added for the advanced AI services in Cabinet Insight Pro.

## Base URL

```
http://localhost:3001/api
```

## Authentication

All endpoints use the same authentication mechanism as the existing API. No additional authentication is required for the enhanced services.

## Enhanced Analysis Endpoints

### POST /analysis/enhanced

Performs enhanced analysis with integrated optimization and reasoning.

**Request:**
```json
{
  "files": ["file1.pdf", "file2.png"],
  "config": {
    "useGPT4o": true,
    "useReasoning": true,
    "focusOnMaterials": false,
    "focusOnHardware": true,
    "enableMultiView": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "analysisId": "analysis_123456789",
    "content": "{ ... analysis results ... }",
    "model": "gpt-4o",
    "usage": {
      "prompt_tokens": 1500,
      "completion_tokens": 800,
      "total_tokens": 2300
    },
    "confidence": 0.92,
    "processingTime": 25000,
    "optimizationApplied": true,
    "reasoningChainId": "chain_analysis_123456789_1703123456789",
    "abTestVariant": "optimized_v2"
  }
}
```

## Prompt Optimization Endpoints

### POST /optimization/optimize

Optimizes a prompt using heuristic-based algorithms.

**Request:**
```json
{
  "prompt": "Analyze this kitchen design...",
  "context": {
    "analysisType": "kitchen_analysis",
    "targetMetrics": {
      "minAccuracy": 0.85,
      "maxResponseTime": 30000
    },
    "previousPerformance": {
      "accuracy": 0.78,
      "confidence": 0.82,
      "responseTime": 25000
    }
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "originalPrompt": "Analyze this kitchen design...",
    "optimizedPrompt": "CRITICAL INSTRUCTION: Be extremely precise...",
    "appliedHeuristics": ["clarity_enhancement", "specificity_enhancement"],
    "estimatedImprovement": {
      "accuracy": 0.12,
      "confidence": 0.08,
      "responseTime": 0.05
    },
    "reasoning": [
      "Applied Clarity Enhancement: Improve prompt clarity and reduce ambiguity",
      "Applied Specificity Enhancement: Add specific examples and detailed requirements"
    ]
  }
}
```

### GET /optimization/history/:analysisType

Retrieves optimization history for a specific analysis type.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "originalPrompt": "...",
      "optimizedPrompt": "...",
      "appliedHeuristics": ["clarity_enhancement"],
      "estimatedImprovement": {
        "accuracy": 0.08,
        "confidence": 0.05,
        "responseTime": 0.02
      },
      "reasoning": ["..."]
    }
  ]
}
```

### GET /optimization/heuristics

Lists available optimization heuristics.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "clarity_enhancement",
      "name": "Clarity Enhancement",
      "description": "Improve prompt clarity and reduce ambiguity",
      "category": "clarity",
      "weight": 0.8
    },
    {
      "id": "specificity_enhancement",
      "name": "Specificity Enhancement",
      "description": "Add specific examples and detailed requirements",
      "category": "specificity",
      "weight": 0.9
    }
  ]
}
```

## A/B Testing Endpoints

### POST /ab-tests

Creates a new A/B test.

**Request:**
```json
{
  "name": "Kitchen Analysis Prompt Optimization",
  "description": "Testing optimized vs standard prompts",
  "variants": [
    {
      "id": "control",
      "name": "Standard Prompt",
      "prompt": "Analyze this kitchen design...",
      "weight": 0.5,
      "metadata": {
        "description": "Current production prompt",
        "hypothesis": "Baseline performance",
        "expectedImprovement": "N/A"
      }
    },
    {
      "id": "optimized",
      "name": "Optimized Prompt",
      "prompt": "CRITICAL INSTRUCTION: Be extremely precise...",
      "weight": 0.5,
      "metadata": {
        "description": "Heuristic-optimized prompt",
        "hypothesis": "Improved accuracy and confidence",
        "expectedImprovement": "15% accuracy increase"
      }
    }
  ],
  "trafficAllocation": {
    "control": 0.5,
    "optimized": 0.5
  },
  "targetMetrics": {
    "primary": "accuracy",
    "secondary": ["confidence", "responseTime"]
  },
  "minimumSampleSize": 50,
  "confidenceLevel": 0.95
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "testId": "test_1703123456789_abc123def",
    "status": "draft"
  }
}
```

### POST /ab-tests/:testId/start

Starts an A/B test.

**Response:**
```json
{
  "success": true,
  "data": {
    "testId": "test_1703123456789_abc123def",
    "status": "running",
    "startDate": "2024-01-15T10:30:00.000Z"
  }
}
```

### POST /ab-tests/:testId/stop

Stops an A/B test.

**Response:**
```json
{
  "success": true,
  "data": {
    "testId": "test_1703123456789_abc123def",
    "status": "completed",
    "endDate": "2024-01-20T15:45:00.000Z"
  }
}
```

### GET /ab-tests

Lists all A/B tests.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "test_1703123456789_abc123def",
      "name": "Kitchen Analysis Prompt Optimization",
      "status": "running",
      "variants": 2,
      "startDate": "2024-01-15T10:30:00.000Z"
    }
  ]
}
```

### GET /ab-tests/:testId/analysis

Retrieves A/B test analysis results.

**Response:**
```json
{
  "success": true,
  "data": {
    "testId": "test_1703123456789_abc123def",
    "status": "significant_difference",
    "results": {
      "control": {
        "sampleSize": 75,
        "metrics": {
          "accuracy": {
            "mean": 0.78,
            "stdDev": 0.12,
            "confidence": [0.75, 0.81]
          }
        },
        "significance": {
          "pValue": 0.032,
          "isSignificant": true,
          "effectSize": -0.45
        }
      },
      "optimized": {
        "sampleSize": 73,
        "metrics": {
          "accuracy": {
            "mean": 0.85,
            "stdDev": 0.10,
            "confidence": [0.83, 0.87]
          }
        },
        "significance": {
          "pValue": 0.032,
          "isSignificant": true,
          "effectSize": 0.45
        }
      }
    },
    "recommendation": {
      "winningVariant": "optimized",
      "confidence": 0.968,
      "reasoning": [
        "Variant optimized shows statistically significant improvement in accuracy"
      ],
      "nextSteps": [
        "Deploy optimized variant to production",
        "Monitor performance for 2 weeks",
        "Consider further optimization"
      ]
    }
  }
}
```

## Reasoning Chain Endpoints

### POST /reasoning/chains

Starts a new reasoning chain.

**Request:**
```json
{
  "analysisId": "analysis_123456789",
  "context": {
    "analysisType": "kitchen_analysis",
    "inputData": {
      "imagePaths": ["image1.png", "image2.png"],
      "config": { "useGPT4o": true }
    },
    "constraints": ["accurate_counting", "realistic_measurements"],
    "objectives": ["cabinet_identification", "hardware_calculation"],
    "qualityThresholds": {
      "minConfidence": 0.7,
      "maxUncertainty": 0.3
    }
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "chainId": "chain_analysis_123456789_1703123456789",
    "status": "in_progress",
    "currentStep": 0,
    "totalSteps": 7
  }
}
```

### POST /reasoning/chains/:chainId/execute

Executes the next step in a reasoning chain.

**Request:**
```json
{
  "evidence": ["Cabinet count: 12", "Base cabinets: 8"],
  "confidence": 0.85,
  "observations": ["Clear cabinet visibility", "Standard kitchen layout"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "completed": false,
    "nextStep": {
      "id": "measurement_analysis",
      "type": "analysis",
      "description": "Extract and validate dimensional measurements",
      "dependencies": ["cabinet_identification"]
    }
  }
}
```

### GET /reasoning/chains/:chainId

Retrieves reasoning chain status.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "chain_analysis_123456789_1703123456789",
    "analysisId": "analysis_123456789",
    "status": "in_progress",
    "currentStep": 2,
    "metadata": {
      "startTime": "2024-01-15T10:30:00.000Z",
      "totalSteps": 7,
      "completedSteps": 2
    }
  }
}
```

### GET /reasoning/chains/active

Lists active reasoning chains.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "chain_analysis_123456789_1703123456789",
      "analysisId": "analysis_123456789",
      "status": "in_progress",
      "currentStep": 2,
      "totalSteps": 7
    }
  ]
}
```

## Enhanced PDF Processing Endpoints

### POST /pdf/process-enhanced

Processes PDF with enhanced capabilities.

**Request:**
```json
{
  "pdfPath": "/uploads/kitchen-design.pdf",
  "options": {
    "outputFormat": "png",
    "quality": 95,
    "density": 300,
    "optimizeForAnalysis": true,
    "extractTextLayer": true,
    "generateThumbnails": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "pages": [
      {
        "pageNumber": 1,
        "imagePath": "/temp/page-001.png",
        "thumbnailPath": "/temp/thumb-001.jpeg",
        "metadata": {
          "width": 2048,
          "height": 1536,
          "fileSize": 1024000,
          "format": "png",
          "density": 300
        }
      }
    ],
    "textContent": "Kitchen Design Plan\nCabinet Schedule...",
    "dimensions": [
      {
        "value": 600,
        "unit": "mm",
        "context": "Base cabinet width: 600mm",
        "confidence": 0.9
      }
    ],
    "kitchenAnalysis": {
      "hasFloorPlan": true,
      "hasElevations": true,
      "hasDimensions": true,
      "hasSpecifications": true,
      "confidence": 0.85,
      "detectedElements": ["Floor Plan", "Elevations", "Dimensions (15 found)"]
    }
  }
}
```

### POST /pdf/extract-text

Extracts text content from PDF.

**Request:**
```json
{
  "pdfPath": "/uploads/kitchen-design.pdf"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "textContent": "Kitchen Design Plan\nCabinet Schedule...",
    "extractionMethod": "direct",
    "confidence": 0.95
  }
}
```

### POST /pdf/detect-dimensions

Detects dimensions from PDF text content.

**Request:**
```json
{
  "textContent": "Base cabinet width: 600mm, height: 720mm..."
}
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "value": 600,
      "unit": "mm",
      "context": "Base cabinet width: 600mm",
      "confidence": 0.9
    },
    {
      "value": 720,
      "unit": "mm",
      "context": "height: 720mm",
      "confidence": 0.85
    }
  ]
}
```

## Health Check Endpoints

### GET /health/advanced-services

Comprehensive health check for all advanced services.

**Response:**
```json
{
  "success": true,
  "data": {
    "promptOptimization": {
      "status": "healthy",
      "heuristics": 5,
      "lastOptimization": "2024-01-15T10:30:00.000Z"
    },
    "abTesting": {
      "status": "healthy",
      "activeTests": 2,
      "totalTests": 15
    },
    "reasoning": {
      "status": "healthy",
      "activeChains": 3,
      "templates": 2
    },
    "pdfProcessing": {
      "status": "healthy",
      "ocrAvailable": true,
      "tesseractVersion": "5.3.0"
    }
  }
}
```

### GET /health/prompt-optimization

Prompt optimization service health check.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "heuristics": 5,
    "optimizationHistory": 127,
    "lastOptimization": "2024-01-15T10:30:00.000Z"
  }
}
```

### GET /health/ab-testing

A/B testing framework health check.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "activeTests": 2,
    "totalTests": 15,
    "dataPath": "./data/ab_tests",
    "diskUsage": "45.2 MB"
  }
}
```

### GET /health/reasoning

Reasoning manager health check.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "activeChains": 3,
    "templates": 2,
    "completedChains": 89
  }
}
```

### GET /health/pdf-processing

Enhanced PDF processor health check.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "ocrAvailable": true,
    "tesseractVersion": "5.3.0",
    "sharpVersion": "0.32.6",
    "supportedFormats": ["pdf", "png", "jpeg", "webp"]
  }
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": {
    "code": "OPTIMIZATION_FAILED",
    "message": "Prompt optimization failed: Invalid context provided",
    "details": {
      "analysisType": "Required field missing",
      "targetMetrics": "Must include minAccuracy"
    }
  }
}
```

## Rate Limiting

Enhanced endpoints follow the same rate limiting as existing API endpoints:

- **Standard requests**: 100 requests per minute
- **Analysis requests**: 10 requests per minute
- **A/B test management**: 20 requests per minute

## WebSocket Events

Enhanced services emit additional WebSocket events:

### optimization-complete
```json
{
  "event": "optimization-complete",
  "data": {
    "analysisId": "analysis_123456789",
    "appliedHeuristics": ["clarity_enhancement"],
    "estimatedImprovement": 0.12
  }
}
```

### reasoning-step-complete
```json
{
  "event": "reasoning-step-complete",
  "data": {
    "chainId": "chain_analysis_123456789_1703123456789",
    "stepId": "cabinet_identification",
    "confidence": 0.85,
    "progress": 0.4
  }
}
```

### ab-test-result-recorded
```json
{
  "event": "ab-test-result-recorded",
  "data": {
    "testId": "test_1703123456789_abc123def",
    "variantId": "optimized",
    "sampleSize": 75
  }
}
```
