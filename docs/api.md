# Frontend Architecture & Patterns

## Overview

The A.One Kitchen Design Analysis System is built as a modern frontend application using React, TypeScript, and Vite. This document outlines the component architecture, patterns, and data flow used in the application.

## Component Architecture

### Component Structure

```typescript
// Standard component structure
interface ComponentProps {
  // Props interface
}

const Component: React.FC<ComponentProps> = ({ ...props }) => {
  // Component logic
  return (
    // JSX
  );
};

export default Component;
```

## UI Component Patterns

### Shadcn UI Integration

The application uses Shadcn UI components as the foundation:

```typescript
// Example button component usage
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const ExampleComponent = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen Analysis</CardTitle>
      </CardHeader>
      <CardContent>
        <Button variant="primary" onClick={handleAnalysis}>
          Start Analysis
        </Button>
      </CardContent>
    </Card>
  );
};
```

### Form Handling

Forms use React Hook Form with Zod validation:

```typescript
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

const formSchema = z.object({
  projectName: z.string().min(1, "Project name is required"),
  description: z.string().optional(),
});

type FormData = z.infer<typeof formSchema>;

const ProjectForm = () => {
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
  });

  const onSubmit = (data: FormData) => {
    // Handle form submission
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)}>
      {/* Form fields */}
    </form>
  );
};
```

## State Management

### TanStack Query Integration

Server state is managed with TanStack Query:

```typescript
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

// Query for fetching data
const useProjects = () => {
  return useQuery({
    queryKey: ['projects'],
    queryFn: async () => {
      // Fetch projects from API
      const response = await fetch('/api/projects');
      return response.json();
    },
  });
};

// Mutation for creating data
const useCreateProject = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (projectData: ProjectData) => {
      // Create project via API
      const response = await fetch('/api/projects', {
        method: 'POST',
        body: JSON.stringify(projectData),
      });
      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch projects
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
  });
};
```

### Local State Management

Component-level state uses React hooks:

```typescript
import { useState, useEffect } from "react";

const UploadComponent = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  const handleFileUpload = async (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    setIsUploading(true);

    // Upload logic here

    setIsUploading(false);
  };

  return (
    // Upload UI
  );
};
```

## Routing Patterns

### React Router Setup

The application uses React Router for client-side routing:

```typescript
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";

const App = () => (
  <BrowserRouter>
    <Routes>
      <Route path="/" element={<Index />} />
      <Route path="*" element={<NotFound />} />
    </Routes>
  </BrowserRouter>
);
```

### Page Components

Page components follow a consistent structure:

```typescript
// src/pages/Dashboard.tsx
import { useQuery } from "@tanstack/react-query";
import Header from "@/components/Header";
import ProjectList from "@/components/ProjectList";

const Dashboard = () => {
  const { data: projects, isLoading } = useQuery({
    queryKey: ['projects'],
    queryFn: fetchProjects,
  });

  if (isLoading) return <div>Loading...</div>;

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">Dashboard</h1>
        <ProjectList projects={projects} />
      </main>
    </div>
  );
};

export default Dashboard;
```

## Styling Patterns

### Tailwind CSS Usage

Consistent styling patterns using Tailwind CSS:

```typescript
// Layout patterns
const containerClasses = "container mx-auto px-4";
const cardClasses = "bg-white rounded-lg shadow-md p-6";
const buttonPrimaryClasses = "bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700";

// Component with styling
const ProjectCard = ({ project }: { project: Project }) => {
  return (
    <div className={cardClasses}>
      <h3 className="text-xl font-semibold mb-2">{project.name}</h3>
      <p className="text-gray-600 mb-4">{project.description}</p>
      <button className={buttonPrimaryClasses}>
        View Project
      </button>
    </div>
  );
};
```

### Design System

The application follows a consistent design system:

```typescript
// Color palette
const colors = {
  primary: {
    50: '#eff6ff',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
  },
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    600: '#4b5563',
    900: '#111827',
  },
};

// Typography scale
const typography = {
  h1: 'text-4xl font-bold',
  h2: 'text-3xl font-bold',
  h3: 'text-2xl font-semibold',
  body: 'text-base',
  small: 'text-sm text-gray-600',
};
```

## Error Handling

### Error Boundaries

React Error Boundaries for graceful error handling:

```typescript
import { Component, ErrorInfo, ReactNode } from "react";

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(_: Error): State {
    return { hasError: true };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Uncaught error:', error, errorInfo);
  }

  public render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Something went wrong
            </h1>
            <button
              onClick={() => this.setState({ hasError: false })}
              className="bg-blue-600 text-white px-4 py-2 rounded-md"
            >
              Try again
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
```

### Form Validation

Error handling in forms:

```typescript
const ContactForm = () => {
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = (data: FormData) => {
    const newErrors: Record<string, string> = {};

    if (!data.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(data.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!data.message) {
      newErrors.message = 'Message is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  return (
    <form>
      <input
        type="email"
        className={`border rounded-md px-3 py-2 ${
          errors.email ? 'border-red-500' : 'border-gray-300'
        }`}
      />
      {errors.email && (
        <p className="text-red-500 text-sm mt-1">{errors.email}</p>
      )}
    </form>
  );
};
```

## Performance Optimization

### Code Splitting

Lazy loading for optimal performance:

```typescript
import { lazy, Suspense } from "react";

const Dashboard = lazy(() => import("./pages/Dashboard"));
const Projects = lazy(() => import("./pages/Projects"));

const App = () => (
  <BrowserRouter>
    <Suspense fallback={<div>Loading...</div>}>
      <Routes>
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/projects" element={<Projects />} />
      </Routes>
    </Suspense>
  </BrowserRouter>
);
```

### Image Optimization

Optimized image loading:

```typescript
const OptimizedImage = ({ src, alt, className }: ImageProps) => {
  const [isLoaded, setIsLoaded] = useState(false);

  return (
    <div className={`relative ${className}`}>
      {!isLoaded && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse" />
      )}
      <img
        src={src}
        alt={alt}
        className={`transition-opacity duration-300 ${
          isLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        onLoad={() => setIsLoaded(true)}
        loading="lazy"
      />
    </div>
  );
};
```

---

*This frontend architecture documentation reflects the current Vite + React implementation and will be updated as new patterns and components are added.*
