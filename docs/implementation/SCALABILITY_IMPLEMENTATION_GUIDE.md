# 🚀 **CABINET INSIGHT PRO - SCALABILITY IMPLEMENTATION GUIDE**

## **📋 EXECUTIVE SUMMARY**

This implementation guide provides step-by-step instructions for scaling Cabinet Insight Pro from its current single-instance architecture to support **1000+ concurrent users** across New Zealand's kitchen industry. The implementation maintains the existing **Vite + React 18 + TypeScript** architecture while introducing horizontal scaling, advanced caching, and cost optimization strategies.

### **🎯 Key Objectives**
- **Scale to 1000+ concurrent users** with 99.5% uptime
- **Reduce Azure OpenAI costs by 60-80%** through intelligent caching
- **Maintain 91.7% test success rate** throughout implementation
- **Achieve <3s response times** for analysis completion
- **Cost-effective scaling** using open-source technologies

### **💰 Expected Outcomes**
- **Infrastructure Cost**: $2.20-4.50 NZD per user per month
- **Azure OpenAI Savings**: $8,064-16,128 NZD annually
- **Total Implementation Cost**: $95,000-115,000 NZD over 16 weeks
- **ROI**: Break-even at 400+ active users

---

## **🔧 PREREQUISITES**

### **Required Tools and Dependencies**
```bash
# Development Environment
Node.js >= 18.0.0
Docker >= 20.10.0
Docker Compose >= 2.0.0
Redis >= 7.0
PostgreSQL >= 15
Nginx >= 1.20

# Monitoring Tools
Prometheus
Grafana
PM2 or similar process manager

# Testing Tools
Playwright (existing)
Artillery (for load testing)
```

### **Environment Setup Checklist**
- [ ] Docker and Docker Compose installed
- [ ] Redis cluster configuration tested
- [ ] PostgreSQL replication setup verified
- [ ] Azure OpenAI API keys configured
- [ ] SSL certificates prepared for production
- [ ] Monitoring infrastructure ready
- [ ] Backup systems configured

### **Current Architecture Validation**
Before starting, verify your current setup:
```bash
# Verify current test success rate
npm run test
# Should show 91.7% success rate

# Check current performance metrics
npm run test:performance-api
# Should show 2-10ms API response times

# Validate Azure OpenAI integration
npm run test:azure
# Should confirm real API integration
```

---

## **📅 PHASE-BY-PHASE IMPLEMENTATION**

## **PHASE 1: FOUNDATION SCALING (Weeks 1-4)**
**Target**: 200-300 concurrent users | **Budget**: $15,000-20,000 NZD

### **Week 1-2: Infrastructure Setup**

#### **Step 1.1: Redis Cluster Configuration**
Create Redis cluster for distributed caching:

```bash
# Create Redis cluster directory
mkdir -p infrastructure/redis-cluster
cd infrastructure/redis-cluster
```

Create `docker-compose.redis-cluster.yml`:
```yaml
version: '3.8'
services:
  redis-node-1:
    image: redis:7-alpine
    command: redis-server --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --port 7001
    ports: ["7001:7001"]
    volumes: ["redis-1:/data"]
    networks: ["redis-cluster"]
    
  redis-node-2:
    image: redis:7-alpine
    command: redis-server --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --port 7002
    ports: ["7002:7002"]
    volumes: ["redis-2:/data"]
    networks: ["redis-cluster"]
    
  redis-node-3:
    image: redis:7-alpine
    command: redis-server --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --port 7003
    ports: ["7003:7003"]
    volumes: ["redis-3:/data"]
    networks: ["redis-cluster"]

volumes:
  redis-1:
  redis-2:
  redis-3:

networks:
  redis-cluster:
    driver: bridge
```

Deploy and initialize cluster:
```bash
# Start Redis nodes
docker-compose -f docker-compose.redis-cluster.yml up -d

# Initialize cluster
docker exec -it redis-node-1 redis-cli --cluster create \
  redis-node-1:7001 redis-node-2:7002 redis-node-3:7003 \
  --cluster-replicas 0 --cluster-yes

# Verify cluster status
docker exec -it redis-node-1 redis-cli -p 7001 cluster nodes
```

#### **Step 1.2: PostgreSQL Read Replica Setup**
Create `docker-compose.postgres-cluster.yml`:
```yaml
version: '3.8'
services:
  postgres-primary:
    image: postgres:15
    environment:
      POSTGRES_DB: cabinet_pricing
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: ${REPLICATION_PASSWORD}
    volumes:
      - postgres-primary:/var/lib/postgresql/data
      - ./postgres-primary.conf:/etc/postgresql/postgresql.conf
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    ports: ["5432:5432"]
    networks: ["postgres-cluster"]

  postgres-replica-1:
    image: postgres:15
    environment:
      PGUSER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_PRIMARY_HOST: postgres-primary
      POSTGRES_PRIMARY_PORT: 5432
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: ${REPLICATION_PASSWORD}
    volumes:
      - postgres-replica-1:/var/lib/postgresql/data
      - ./setup-replica.sh:/docker-entrypoint-initdb.d/setup-replica.sh
    ports: ["5433:5432"]
    depends_on: [postgres-primary]
    networks: ["postgres-cluster"]

volumes:
  postgres-primary:
  postgres-replica-1:

networks:
  postgres-cluster:
    driver: bridge
```

Create PostgreSQL configuration files:
```bash
# postgres-primary.conf
wal_level = replica
max_wal_senders = 3
max_replication_slots = 3
synchronous_commit = off
archive_mode = on
archive_command = 'test ! -f /var/lib/postgresql/archive/%f && cp %p /var/lib/postgresql/archive/%f'
```

#### **Step 1.3: Nginx Load Balancer Configuration**
Create `nginx-lb.conf`:
```nginx
upstream cabinet_insight_backend {
    least_conn;
    server api-1:3001 max_fails=3 fail_timeout=30s;
    server api-2:3001 max_fails=3 fail_timeout=30s;
    server api-3:3001 max_fails=3 fail_timeout=30s;
}

upstream cabinet_insight_websocket {
    ip_hash; # Sticky sessions for WebSocket
    server api-1:3001;
    server api-2:3001;
    server api-3:3001;
}

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=upload:10m rate=2r/s;

server {
    listen 80;
    listen 443 ssl http2;
    
    # SSL configuration
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # API endpoints
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://cabinet_insight_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Health check
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
    }
    
    # WebSocket connections
    location /socket.io/ {
        proxy_pass http://cabinet_insight_websocket;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
    
    # File uploads with larger limits
    location /api/upload {
        limit_req zone=upload burst=5 nodelay;
        client_max_body_size 100M;
        proxy_pass http://cabinet_insight_backend;
        proxy_request_buffering off;
        proxy_read_timeout 300s;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

### **Week 3-4: Application Optimization**

#### **Step 1.4: Node.js Clustering Implementation**
Create `server/src/cluster.ts`:
```typescript
import cluster from 'cluster';
import os from 'os';
import { createModuleLogger } from './utils/logger';

const logger = createModuleLogger('Cluster');
const numCPUs = os.cpus().length;
const maxWorkers = parseInt(process.env.MAX_WORKERS || '4');
const workers = Math.min(numCPUs, maxWorkers);

if (cluster.isPrimary) {
  logger.info(`Primary ${process.pid} is running`);
  logger.info(`Starting ${workers} workers`);

  // Fork workers
  for (let i = 0; i < workers; i++) {
    cluster.fork();
  }

  // Handle worker exit and restart
  cluster.on('exit', (worker, code, signal) => {
    logger.warn(`Worker ${worker.process.pid} died with code ${code} and signal ${signal}`);
    logger.info('Starting a new worker');
    cluster.fork();
  });

  // Graceful shutdown
  process.on('SIGTERM', () => {
    logger.info('Primary received SIGTERM, shutting down gracefully');
    for (const id in cluster.workers) {
      cluster.workers[id]?.kill();
    }
    process.exit(0);
  });

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught exception in primary:', error);
    process.exit(1);
  });

} else {
  // Worker process
  require('./index');
  logger.info(`Worker ${process.pid} started`);
  
  // Worker-specific error handling
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught exception in worker:', error);
    process.exit(1);
  });
}
```

Update `package.json` scripts:
```json
{
  "scripts": {
    "start:cluster": "node dist/cluster.js",
    "dev:cluster": "tsx src/cluster.ts",
    "build": "tsc && npm run validate:production"
  }
}
```

#### **Step 1.5: Enhanced Caching Service**
Create `server/src/services/enhancedCacheService.ts`:
```typescript
import { Cluster } from 'ioredis';
import { createModuleLogger } from '../utils/logger';

const logger = createModuleLogger('EnhancedCacheService');

export class EnhancedCacheService {
  private cluster: Cluster;
  private localCache: Map<string, any> = new Map();
  private maxLocalCacheSize = 1000;
  private isConnected = false;

  constructor() {
    this.initializeCluster();
  }

  private initializeCluster(): void {
    this.cluster = new Cluster([
      { host: process.env.REDIS_NODE_1_HOST || 'redis-node-1', port: 7001 },
      { host: process.env.REDIS_NODE_2_HOST || 'redis-node-2', port: 7002 },
      { host: process.env.REDIS_NODE_3_HOST || 'redis-node-3', port: 7003 }
    ], {
      redisOptions: {
        password: process.env.REDIS_PASSWORD,
      },
      enableOfflineQueue: false,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true
    });

    this.cluster.on('connect', () => {
      this.isConnected = true;
      logger.info('Redis cluster connected successfully');
    });

    this.cluster.on('error', (error) => {
      this.isConnected = false;
      logger.error('Redis cluster connection error:', error);
    });

    this.cluster.on('close', () => {
      this.isConnected = false;
      logger.warn('Redis cluster connection closed');
    });
  }

  async get(key: string): Promise<any> {
    try {
      // Check local cache first (L1)
      if (this.localCache.has(key)) {
        logger.debug(`L1 cache hit: ${key}`);
        return this.localCache.get(key);
      }

      // Check Redis cluster (L2) if connected
      if (this.isConnected) {
        const value = await this.cluster.get(key);
        if (value) {
          const parsed = JSON.parse(value);
          this.setLocalCache(key, parsed);
          logger.debug(`L2 cache hit: ${key}`);
          return parsed;
        }
      }

      return null;
    } catch (error) {
      logger.error('Cache get error:', error);
      return null;
    }
  }

  async set(key: string, value: any, ttl: number = 3600): Promise<boolean> {
    try {
      const serialized = JSON.stringify(value);
      
      // Set in Redis cluster if connected
      if (this.isConnected) {
        await this.cluster.setex(key, ttl, serialized);
      }
      
      // Always set in local cache
      this.setLocalCache(key, value);
      
      return true;
    } catch (error) {
      logger.error('Cache set error:', error);
      return false;
    }
  }

  private setLocalCache(key: string, value: any): void {
    if (this.localCache.size >= this.maxLocalCacheSize) {
      // Remove oldest entry (simple LRU)
      const firstKey = this.localCache.keys().next().value;
      this.localCache.delete(firstKey);
    }
    this.localCache.set(key, value);
  }

  async invalidatePattern(pattern: string): Promise<void> {
    try {
      if (this.isConnected) {
        const keys = await this.cluster.keys(pattern);
        if (keys.length > 0) {
          await this.cluster.del(...keys);
        }
      }
      
      // Clear local cache entries matching pattern
      for (const key of this.localCache.keys()) {
        if (key.includes(pattern.replace('*', ''))) {
          this.localCache.delete(key);
        }
      }
    } catch (error) {
      logger.error('Cache invalidation error:', error);
    }
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  async getStats(): Promise<any> {
    try {
      if (!this.isConnected) {
        return { connected: false, localCacheSize: this.localCache.size };
      }

      const info = await this.cluster.info();
      return {
        connected: true,
        localCacheSize: this.localCache.size,
        redisInfo: info
      };
    } catch (error) {
      logger.error('Failed to get cache stats:', error);
      return { connected: false, error: error.message };
    }
  }
}
```

### **Phase 1 Validation and Testing**
After completing Phase 1, run these validation tests:

```bash
# Test Redis cluster
npm run test:redis-cluster

# Test PostgreSQL replication
npm run test:postgres-replication

# Test load balancer
npm run test:load-balancer

# Run full test suite to maintain 91.7% success rate
npm run test
```

**Expected Phase 1 Outcomes:**
- ✅ Redis cluster operational with 3 nodes
- ✅ PostgreSQL primary + 1 read replica
- ✅ Nginx load balancer distributing traffic
- ✅ Node.js clustering with 4 workers
- ✅ Enhanced caching with L1/L2 strategy
- ✅ Support for 200-300 concurrent users
- ✅ Maintained 91.7% test success rate

---

## **PHASE 2: ADVANCED OPTIMIZATION (Weeks 5-8)**
**Target**: 500-800 concurrent users | **Budget**: $25,000-30,000 NZD

### **Week 5-6: Azure OpenAI Optimization**

#### **Step 2.1: Advanced Rate Limiting Service**
Create `server/src/services/advancedOpenAIService.ts`:

```typescript
import { OpenAI } from 'openai';
import { createModuleLogger } from '../utils/logger';
import { EnhancedCacheService } from './enhancedCacheService';
import Bull from 'bull';

const logger = createModuleLogger('AdvancedOpenAIService');

interface OpenAIRequest {
  id: string;
  prompt: string;
  model: string;
  options: any;
  resolve: (result: any) => void;
  reject: (error: any) => void;
  priority: number;
  timestamp: number;
}

export class AdvancedOpenAIService {
  private client: OpenAI;
  private cacheService: EnhancedCacheService;
  private requestQueue: Bull.Queue;
  private rateLimitState = {
    requestsPerMinute: 60,
    tokensPerMinute: 150000,
    currentRequests: 0,
    currentTokens: 0,
    resetTime: Date.now() + 60000
  };

  constructor() {
    this.cacheService = new EnhancedCacheService();
    this.initializeClient();
    this.initializeQueue();
    this.startRateLimitReset();
  }

  private initializeClient(): void {
    this.client = new OpenAI({
      apiKey: process.env.AZURE_OPENAI_API_KEY,
      baseURL: `https://${process.env.AZURE_OPENAI_ENDPOINT}/openai/deployments`,
      defaultQuery: { 'api-version': '2024-12-01-preview' },
      defaultHeaders: {
        'api-key': process.env.AZURE_OPENAI_API_KEY,
      },
    });
  }

  private initializeQueue(): void {
    this.requestQueue = new Bull('openai requests', {
      redis: {
        host: process.env.REDIS_HOST || 'redis-node-1',
        port: parseInt(process.env.REDIS_PORT || '7001'),
      },
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    });

    // Process requests with concurrency control
    this.requestQueue.process('openai-request', 5, this.processRequest.bind(this));
  }

  private async processRequest(job: Bull.Job): Promise<any> {
    const { prompt, model, options, requestId } = job.data;

    try {
      // Check cache first
      const cacheKey = this.generateCacheKey(prompt, model, options);
      const cached = await this.cacheService.get(cacheKey);

      if (cached) {
        logger.debug(`Cache hit for request: ${requestId}`);
        return cached;
      }

      // Check semantic similarity cache
      const similarResult = await this.findSimilarCachedResult(prompt);
      if (similarResult && similarResult.similarity > 0.85) {
        logger.debug(`Semantic cache hit for request: ${requestId}`);
        return similarResult.result;
      }

      // Wait for rate limit availability
      await this.waitForRateLimit();

      // Make API request
      const result = await this.makeOpenAIRequest(prompt, model, options);

      // Cache result with semantic embedding
      await this.cacheWithSemantics(cacheKey, result, prompt);

      return result;

    } catch (error) {
      logger.error(`Request failed: ${requestId}`, error);
      throw error;
    }
  }

  private async waitForRateLimit(): Promise<void> {
    while (!this.canMakeRequest()) {
      const waitTime = Math.min(1000, this.rateLimitState.resetTime - Date.now());
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  private canMakeRequest(): boolean {
    const now = Date.now();

    if (now >= this.rateLimitState.resetTime) {
      this.resetRateLimits();
    }

    return this.rateLimitState.currentRequests < this.rateLimitState.requestsPerMinute &&
           this.rateLimitState.currentTokens < this.rateLimitState.tokensPerMinute;
  }

  private resetRateLimits(): void {
    this.rateLimitState.currentRequests = 0;
    this.rateLimitState.currentTokens = 0;
    this.rateLimitState.resetTime = Date.now() + 60000;
  }

  private startRateLimitReset(): void {
    setInterval(() => {
      this.resetRateLimits();
    }, 60000); // Reset every minute
  }

  async queueRequest(prompt: string, model: string, options: any = {}): Promise<any> {
    const requestId = this.generateRequestId();

    const job = await this.requestQueue.add('openai-request', {
      prompt,
      model,
      options,
      requestId,
      timestamp: Date.now()
    }, {
      priority: this.calculatePriority(prompt, options),
      delay: 0
    });

    return new Promise((resolve, reject) => {
      job.finished().then(resolve).catch(reject);
    });
  }

  private calculatePriority(prompt: string, options: any): number {
    let priority = 0;

    // Higher priority for shorter prompts (faster processing)
    if (prompt.length < 1000) priority += 10;
    else if (prompt.length < 5000) priority += 5;

    // Premium analysis types get higher priority
    if (options.analysisType === 'premium') priority += 15;
    else if (options.analysisType === 'standard') priority += 10;

    return priority;
  }

  private async makeOpenAIRequest(prompt: string, model: string, options: any): Promise<any> {
    const startTime = Date.now();

    try {
      const response = await this.client.chat.completions.create({
        model,
        messages: [{ role: 'user', content: prompt }],
        ...options
      });

      // Update rate limit counters
      this.rateLimitState.currentRequests++;
      this.rateLimitState.currentTokens += response.usage?.total_tokens || 0;

      const processingTime = Date.now() - startTime;

      return {
        content: response.choices[0]?.message?.content,
        model: response.model,
        usage: response.usage,
        processingTime,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      if (error.status === 429) {
        this.handleRateLimitError(error);
      }
      throw error;
    }
  }

  private handleRateLimitError(error: any): void {
    const retryAfter = error.headers?.['retry-after'];
    if (retryAfter) {
      this.rateLimitState.resetTime = Date.now() + (parseInt(retryAfter) * 1000);
    }

    logger.warn('Rate limit hit, adjusting strategy', {
      retryAfter,
      queueLength: await this.requestQueue.waiting()
    });
  }

  private generateCacheKey(prompt: string, model: string, options: any): string {
    const crypto = require('crypto');
    const data = `${prompt}:${model}:${JSON.stringify(options)}`;
    return `openai:${crypto.createHash('md5').update(data).digest('hex')}`;
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async findSimilarCachedResult(prompt: string): Promise<any> {
    // Implement semantic similarity search using embeddings
    // This is a simplified version - in production, use proper vector similarity
    try {
      const promptHash = require('crypto').createHash('md5').update(prompt).digest('hex');
      const similarKey = `semantic:${promptHash.substring(0, 8)}*`;

      // This would be replaced with proper vector similarity search
      return null;
    } catch (error) {
      logger.error('Semantic search error:', error);
      return null;
    }
  }

  private async cacheWithSemantics(key: string, result: any, prompt: string): Promise<void> {
    try {
      // Cache the result
      await this.cacheService.set(key, result, 24 * 60 * 60); // 24 hours

      // Store semantic information for future similarity matching
      const promptHash = require('crypto').createHash('md5').update(prompt).digest('hex');
      const semanticKey = `semantic:${promptHash.substring(0, 8)}`;
      await this.cacheService.set(semanticKey, { result, prompt }, 24 * 60 * 60);

    } catch (error) {
      logger.error('Semantic caching error:', error);
    }
  }

  async getQueueStats(): Promise<any> {
    try {
      const waiting = await this.requestQueue.waiting();
      const active = await this.requestQueue.active();
      const completed = await this.requestQueue.completed();
      const failed = await this.requestQueue.failed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        rateLimitState: this.rateLimitState
      };
    } catch (error) {
      logger.error('Failed to get queue stats:', error);
      return { error: error.message };
    }
  }
}
```

#### **Step 2.2: Semantic Caching Implementation**
Create `server/src/services/semanticCacheService.ts`:

```typescript
import { EnhancedCacheService } from './enhancedCacheService';
import { createModuleLogger } from '../utils/logger';

const logger = createModuleLogger('SemanticCacheService');

interface SemanticCacheEntry {
  content: string;
  embedding: number[];
  result: any;
  timestamp: number;
  hitCount: number;
}

export class SemanticCacheService {
  private cacheService: EnhancedCacheService;
  private similarityThreshold = 0.85;

  constructor() {
    this.cacheService = new EnhancedCacheService();
  }

  async findSimilarContent(content: string): Promise<any> {
    try {
      // Generate embedding for input content
      const inputEmbedding = await this.generateEmbedding(content);

      // Search for similar cached content
      const cacheKeys = await this.getCacheKeys('semantic:*');

      let bestMatch = null;
      let bestSimilarity = 0;

      for (const key of cacheKeys) {
        const entry = await this.cacheService.get(key) as SemanticCacheEntry;
        if (!entry || !entry.embedding) continue;

        const similarity = this.calculateCosineSimilarity(inputEmbedding, entry.embedding);

        if (similarity > this.similarityThreshold && similarity > bestSimilarity) {
          bestMatch = entry;
          bestSimilarity = similarity;
        }
      }

      if (bestMatch) {
        // Update hit count
        bestMatch.hitCount++;
        await this.cacheService.set(`semantic:${this.generateContentHash(bestMatch.content)}`, bestMatch);

        logger.debug(`Semantic cache hit with similarity: ${bestSimilarity}`);
        return {
          result: bestMatch.result,
          similarity: bestSimilarity,
          hitCount: bestMatch.hitCount
        };
      }

      return null;
    } catch (error) {
      logger.error('Semantic search error:', error);
      return null;
    }
  }

  async cacheContent(content: string, result: any): Promise<void> {
    try {
      const embedding = await this.generateEmbedding(content);
      const contentHash = this.generateContentHash(content);

      const entry: SemanticCacheEntry = {
        content,
        embedding,
        result,
        timestamp: Date.now(),
        hitCount: 0
      };

      await this.cacheService.set(`semantic:${contentHash}`, entry, 7 * 24 * 60 * 60); // 7 days

    } catch (error) {
      logger.error('Semantic caching error:', error);
    }
  }

  private async generateEmbedding(content: string): Promise<number[]> {
    // Simplified embedding generation - in production, use OpenAI embeddings API
    // or a local embedding model
    const words = content.toLowerCase().split(/\s+/);
    const embedding = new Array(384).fill(0); // 384-dimensional embedding

    // Simple hash-based embedding (replace with proper embedding model)
    for (let i = 0; i < words.length; i++) {
      const hash = this.simpleHash(words[i]);
      const index = Math.abs(hash) % embedding.length;
      embedding[index] += 1 / words.length;
    }

    // Normalize
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => val / magnitude);
  }

  private calculateCosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) return 0;

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash;
  }

  private generateContentHash(content: string): string {
    const crypto = require('crypto');
    return crypto.createHash('md5').update(content).digest('hex');
  }

  private async getCacheKeys(pattern: string): Promise<string[]> {
    // This would need to be implemented based on your Redis setup
    // For now, return empty array
    return [];
  }

  async getStats(): Promise<any> {
    try {
      const keys = await this.getCacheKeys('semantic:*');
      let totalHits = 0;
      let totalEntries = 0;

      for (const key of keys) {
        const entry = await this.cacheService.get(key) as SemanticCacheEntry;
        if (entry) {
          totalEntries++;
          totalHits += entry.hitCount;
        }
      }

      return {
        totalEntries,
        totalHits,
        averageHitsPerEntry: totalEntries > 0 ? totalHits / totalEntries : 0,
        similarityThreshold: this.similarityThreshold
      };
    } catch (error) {
      logger.error('Failed to get semantic cache stats:', error);
      return { error: error.message };
    }
  }
}
```

### **Week 7-8: 3D Visualization & File Handling**

#### **Step 2.3: Optimized 3D Visualization Service**
Create `src/services/optimized3DVisualizationService.ts`:

```typescript
import * as THREE from 'three';
import { createModuleLogger } from '../utils/logger';

const logger = createModuleLogger('Optimized3DVisualizationService');

interface SceneConfig {
  maxPolygons: number;
  textureQuality: 'low' | 'medium' | 'high';
  enableShadows: boolean;
  enableAntialiasing: boolean;
}

interface SceneMetadata {
  analysisId: string;
  timestamp: number;
  polygonCount: number;
  memoryUsage: number;
}

export class Optimized3DVisualizationService {
  private scenePool: Map<string, THREE.Scene> = new Map();
  private sceneMetadata: Map<string, SceneMetadata> = new Map();
  private rendererPool: THREE.WebGLRenderer[] = [];
  private maxScenes = 10;
  private memoryThreshold = 512 * 1024 * 1024; // 512MB
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    this.initializeRendererPool();
    this.startMemoryMonitoring();
    this.startPeriodicCleanup();
  }

  private initializeRendererPool(): void {
    // Pre-create renderer pool for better performance
    for (let i = 0; i < 3; i++) {
      const renderer = new THREE.WebGLRenderer({
        antialias: false, // Disable for performance
        alpha: true,
        powerPreference: 'high-performance',
        preserveDrawingBuffer: false
      });

      renderer.setSize(800, 600);
      renderer.shadowMap.enabled = false; // Disable shadows by default
      renderer.outputColorSpace = THREE.SRGBColorSpace;
      renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // Limit pixel ratio

      this.rendererPool.push(renderer);
    }
  }

  async create3DScene(analysisId: string, cabinetData: any, config: SceneConfig): Promise<string> {
    try {
      // Check memory usage before creating new scene
      if (this.getMemoryUsage() > this.memoryThreshold) {
        await this.cleanupOldScenes();
      }

      // Remove existing scene if it exists
      if (this.scenePool.has(analysisId)) {
        await this.disposeScene(analysisId);
      }

      const scene = new THREE.Scene();
      scene.background = new THREE.Color(0xf0f0f0);

      // Add optimized lighting
      this.addOptimizedLighting(scene);

      // Create cabinet models with LOD (Level of Detail)
      const cabinetGroup = await this.createOptimizedCabinetModels(cabinetData, config);
      scene.add(cabinetGroup);

      // Calculate polygon count
      const polygonCount = this.calculatePolygonCount(scene);

      // Store scene with metadata
      this.scenePool.set(analysisId, scene);
      this.sceneMetadata.set(analysisId, {
        analysisId,
        timestamp: Date.now(),
        polygonCount,
        memoryUsage: this.estimateSceneMemoryUsage(scene)
      });

      logger.info(`3D scene created for analysis: ${analysisId}, polygons: ${polygonCount}`);
      return analysisId;

    } catch (error) {
      logger.error(`Failed to create 3D scene: ${analysisId}`, error);
      throw error;
    }
  }

  private addOptimizedLighting(scene: THREE.Scene): void {
    // Use minimal lighting for performance
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = false; // Disable shadows for performance
    scene.add(directionalLight);
  }

  private async createOptimizedCabinetModels(cabinetData: any, config: SceneConfig): Promise<THREE.Group> {
    const group = new THREE.Group();

    for (const cabinet of cabinetData.cabinets || []) {
      const cabinetMesh = await this.createCabinetMesh(cabinet, config);
      group.add(cabinetMesh);
    }

    return group;
  }

  private async createCabinetMesh(cabinet: any, config: SceneConfig): Promise<THREE.Object3D> {
    // Create simplified geometry based on performance config
    const geometry = this.createOptimizedGeometry(cabinet, config);
    const material = this.createOptimizedMaterial(cabinet, config);

    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.set(
      cabinet.position?.x || 0,
      cabinet.position?.y || 0,
      cabinet.position?.z || 0
    );

    // Add LOD for complex models
    if (config.maxPolygons < this.getGeometryPolygonCount(geometry)) {
      return this.createLODMesh(geometry, material, cabinet);
    }

    return mesh;
  }

  private createOptimizedGeometry(cabinet: any, config: SceneConfig): THREE.BufferGeometry {
    const { width = 1, height = 1, depth = 1 } = cabinet.dimensions || {};

    // Use box geometry for performance, can be enhanced later
    const geometry = new THREE.BoxGeometry(width, height, depth);

    // Reduce polygon count if needed
    if (config.maxPolygons < 1000) {
      geometry.deleteAttribute('normal');
      geometry.deleteAttribute('uv');
    }

    return geometry;
  }

  private createOptimizedMaterial(cabinet: any, config: SceneConfig): THREE.Material {
    const materialConfig: any = {
      color: cabinet.color || 0x8B4513,
      transparent: false,
      opacity: 1.0
    };

    // Use different material types based on quality setting
    switch (config.textureQuality) {
      case 'low':
        return new THREE.MeshBasicMaterial(materialConfig);
      case 'medium':
        return new THREE.MeshLambertMaterial(materialConfig);
      case 'high':
        return new THREE.MeshPhongMaterial(materialConfig);
      default:
        return new THREE.MeshBasicMaterial(materialConfig);
    }
  }

  private createLODMesh(geometry: THREE.BufferGeometry, material: THREE.Material, cabinet: any): THREE.LOD {
    const lod = new THREE.LOD();

    // High detail (close view)
    lod.addLevel(new THREE.Mesh(geometry, material), 0);

    // Medium detail
    const mediumGeometry = geometry.clone();
    mediumGeometry.scale(0.8, 0.8, 0.8);
    lod.addLevel(new THREE.Mesh(mediumGeometry, material), 50);

    // Low detail (far view)
    const { width = 1, height = 1, depth = 1 } = cabinet.dimensions || {};
    const lowGeometry = new THREE.BoxGeometry(width * 0.5, height * 0.5, depth * 0.5);
    lod.addLevel(new THREE.Mesh(lowGeometry, material), 100);

    return lod;
  }

  private startMemoryMonitoring(): void {
    setInterval(() => {
      const memoryUsage = this.getMemoryUsage();

      if (memoryUsage > this.memoryThreshold) {
        logger.warn(`High memory usage detected: ${memoryUsage / 1024 / 1024}MB`);
        this.cleanupOldScenes();
      }
    }, 30000); // Check every 30 seconds
  }

  private startPeriodicCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldScenes();
    }, 5 * 60 * 1000); // Cleanup every 5 minutes
  }

  private getMemoryUsage(): number {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in window.performance) {
      return (window.performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  private async cleanupOldScenes(): Promise<void> {
    const scenesToRemove: string[] = [];
    const now = Date.now();
    const maxAge = 10 * 60 * 1000; // 10 minutes

    // Remove scenes older than maxAge
    for (const [analysisId, metadata] of this.sceneMetadata.entries()) {
      const sceneAge = now - metadata.timestamp;
      if (sceneAge > maxAge) {
        scenesToRemove.push(analysisId);
      }
    }

    // If still over memory threshold, remove largest scenes
    if (this.getMemoryUsage() > this.memoryThreshold) {
      const sortedScenes = Array.from(this.sceneMetadata.entries())
        .sort((a, b) => b[1].memoryUsage - a[1].memoryUsage);

      for (const [analysisId] of sortedScenes.slice(0, 3)) {
        if (!scenesToRemove.includes(analysisId)) {
          scenesToRemove.push(analysisId);
        }
      }
    }

    for (const analysisId of scenesToRemove) {
      await this.disposeScene(analysisId);
    }

    if (scenesToRemove.length > 0) {
      logger.info(`Cleaned up ${scenesToRemove.length} old 3D scenes`);
    }
  }

  async disposeScene(analysisId: string): Promise<void> {
    const scene = this.scenePool.get(analysisId);
    if (!scene) return;

    // Dispose of all geometries and materials
    scene.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        if (object.geometry) {
          object.geometry.dispose();
        }
        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose());
          } else {
            object.material.dispose();
          }
        }
      }
    });

    // Remove from pools
    this.scenePool.delete(analysisId);
    this.sceneMetadata.delete(analysisId);

    logger.debug(`3D scene disposed: ${analysisId}`);
  }

  private calculatePolygonCount(scene: THREE.Scene): number {
    let polygonCount = 0;

    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.geometry) {
        const geometry = object.geometry;
        if (geometry.index) {
          polygonCount += geometry.index.count / 3;
        } else {
          polygonCount += geometry.attributes.position.count / 3;
        }
      }
    });

    return Math.floor(polygonCount);
  }

  private getGeometryPolygonCount(geometry: THREE.BufferGeometry): number {
    if (geometry.index) {
      return geometry.index.count / 3;
    } else {
      return geometry.attributes.position.count / 3;
    }
  }

  private estimateSceneMemoryUsage(scene: THREE.Scene): number {
    let memoryUsage = 0;

    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.geometry) {
        const geometry = object.geometry;

        // Estimate geometry memory usage
        for (const attributeName in geometry.attributes) {
          const attribute = geometry.attributes[attributeName];
          memoryUsage += attribute.array.byteLength;
        }

        if (geometry.index) {
          memoryUsage += geometry.index.array.byteLength;
        }
      }
    });

    return memoryUsage;
  }

  getAvailableRenderer(): THREE.WebGLRenderer | null {
    return this.rendererPool.length > 0 ? this.rendererPool.pop() || null : null;
  }

  returnRenderer(renderer: THREE.WebGLRenderer): void {
    if (!this.rendererPool.includes(renderer)) {
      this.rendererPool.push(renderer);
    }
  }

  getSceneStats(): any {
    const stats = {
      totalScenes: this.scenePool.size,
      totalMemoryUsage: 0,
      totalPolygons: 0,
      scenes: []
    };

    for (const [analysisId, metadata] of this.sceneMetadata.entries()) {
      stats.totalMemoryUsage += metadata.memoryUsage;
      stats.totalPolygons += metadata.polygonCount;
      stats.scenes.push({
        analysisId,
        age: Date.now() - metadata.timestamp,
        polygonCount: metadata.polygonCount,
        memoryUsage: metadata.memoryUsage
      });
    }

    return stats;
  }

  destroy(): void {
    // Cleanup all scenes
    for (const analysisId of this.scenePool.keys()) {
      this.disposeScene(analysisId);
    }

    // Dispose renderers
    for (const renderer of this.rendererPool) {
      renderer.dispose();
    }

    // Clear intervals
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
  }
}
```

#### **Step 2.4: Concurrent File Upload Service**
Create `server/src/services/optimizedUploadService.ts`:

```typescript
import multer from 'multer';
import { Request, Response } from 'express';
import { createModuleLogger } from '../utils/logger';
import { EnhancedCacheService } from './enhancedCacheService';
import Bull from 'bull';

const logger = createModuleLogger('OptimizedUploadService');

interface UploadJob {
  fileId: string;
  filePath: string;
  originalName: string;
  size: number;
  userId: string;
  analysisType: string;
}

export class OptimizedUploadService {
  private uploadQueue: Bull.Queue;
  private cacheService: EnhancedCacheService;
  private maxConcurrentUploads = 10;
  private maxFileSize = 100 * 1024 * 1024; // 100MB
  private allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];

  constructor() {
    this.cacheService = new EnhancedCacheService();
    this.initializeQueue();
  }

  private initializeQueue(): void {
    this.uploadQueue = new Bull('file upload processing', {
      redis: {
        host: process.env.REDIS_HOST || 'redis-node-1',
        port: parseInt(process.env.REDIS_PORT || '7001'),
      },
      defaultJobOptions: {
        removeOnComplete: 50,
        removeOnFail: 25,
        attempts: 2,
        backoff: {
          type: 'exponential',
          delay: 1000,
        },
      },
    });

    // Process uploads with concurrency control
    this.uploadQueue.process('process-upload', this.maxConcurrentUploads, this.processUploadJob.bind(this));
  }

  setupMulter(): multer.Multer {
    const storage = multer.diskStorage({
      destination: (req, file, cb) => {
        const uploadDir = process.env.UPLOAD_DIR || './uploads';
        cb(null, uploadDir);
      },
      filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const fileId = `upload_${uniqueSuffix}_${file.originalname}`;
        cb(null, fileId);
      }
    });

    return multer({
      storage,
      limits: {
        fileSize: this.maxFileSize,
        files: 5 // Max 5 files per request
      },
      fileFilter: (req, file, cb) => {
        if (this.allowedTypes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(new Error(`File type ${file.mimetype} not allowed`));
        }
      }
    });
  }

  async handleMultipleUploads(req: Request, res: Response): Promise<void> {
    try {
      const files = req.files as Express.Multer.File[];
      if (!files || files.length === 0) {
        res.status(400).json({ error: 'No files uploaded' });
        return;
      }

      const uploadPromises = files.map(file => this.queueFileProcessing(file, req.body));
      const jobIds = await Promise.all(uploadPromises);

      res.json({
        success: true,
        message: `${files.length} files queued for processing`,
        jobIds,
        estimatedProcessingTime: this.estimateProcessingTime(files)
      });

    } catch (error) {
      logger.error('Multiple upload handling failed:', error);
      res.status(500).json({ error: 'Upload processing failed' });
    }
  }

  private async queueFileProcessing(file: Express.Multer.File, metadata: any): Promise<string> {
    const uploadJob: UploadJob = {
      fileId: file.filename,
      filePath: file.path,
      originalName: file.originalname,
      size: file.size,
      userId: metadata.userId || 'anonymous',
      analysisType: metadata.analysisType || 'standard'
    };

    const job = await this.uploadQueue.add('process-upload', uploadJob, {
      priority: this.calculatePriority(file.size, metadata.analysisType),
      delay: 0
    });

    logger.info(`File queued for processing: ${file.originalname} (Job ID: ${job.id})`);
    return job.id.toString();
  }

  private async processUploadJob(job: Bull.Job<UploadJob>): Promise<any> {
    const { fileId, filePath, originalName, size, userId, analysisType } = job.data;

    try {
      // Update job progress
      await job.progress(10);

      // Validate file integrity
      const isValid = await this.validateFile(filePath);
      if (!isValid) {
        throw new Error('File validation failed');
      }

      await job.progress(30);

      // Optimize file if needed
      const optimizedPath = await this.optimizeFile(filePath, analysisType);
      await job.progress(60);

      // Generate file metadata
      const metadata = await this.generateFileMetadata(optimizedPath);
      await job.progress(80);

      // Cache file information
      await this.cacheFileInfo(fileId, {
        originalName,
        size,
        optimizedPath,
        metadata,
        userId,
        analysisType,
        processedAt: new Date().toISOString()
      });

      await job.progress(100);

      logger.info(`File processing completed: ${originalName}`);
      return {
        fileId,
        originalName,
        optimizedPath,
        metadata,
        processingTime: Date.now() - job.timestamp
      };

    } catch (error) {
      logger.error(`File processing failed: ${originalName}`, error);
      throw error;
    }
  }

  private calculatePriority(fileSize: number, analysisType: string): number {
    let priority = 0;

    // Smaller files get higher priority
    if (fileSize < 1024 * 1024) priority += 10; // < 1MB
    else if (fileSize < 10 * 1024 * 1024) priority += 5; // < 10MB

    // Premium analysis types get higher priority
    if (analysisType === 'premium') priority += 15;
    else if (analysisType === 'standard') priority += 10;

    return priority;
  }

  private async validateFile(filePath: string): Promise<boolean> {
    try {
      const fs = require('fs').promises;
      const stats = await fs.stat(filePath);

      // Check if file exists and has content
      if (!stats.isFile() || stats.size === 0) {
        return false;
      }

      // Additional validation based on file type
      return true;

    } catch (error) {
      logger.error('File validation error:', error);
      return false;
    }
  }

  private async optimizeFile(filePath: string, analysisType: string): Promise<string> {
    // File optimization logic (compression, format conversion, etc.)
    return filePath;
  }

  private async generateFileMetadata(filePath: string): Promise<any> {
    const fs = require('fs').promises;
    const path = require('path');

    try {
      const stats = await fs.stat(filePath);

      return {
        size: stats.size,
        lastModified: stats.mtime,
        extension: path.extname(filePath),
        checksum: await this.generateChecksum(filePath)
      };

    } catch (error) {
      logger.error('Metadata generation error:', error);
      return {};
    }
  }

  private async generateChecksum(filePath: string): Promise<string> {
    const crypto = require('crypto');
    const fs = require('fs');

    return new Promise((resolve, reject) => {
      const hash = crypto.createHash('md5');
      const stream = fs.createReadStream(filePath);

      stream.on('data', (data: Buffer) => hash.update(data));
      stream.on('end', () => resolve(hash.digest('hex')));
      stream.on('error', reject);
    });
  }

  private async cacheFileInfo(fileId: string, info: any): Promise<void> {
    await this.cacheService.set(`file:${fileId}`, info, 24 * 60 * 60); // 24 hours
  }

  private estimateProcessingTime(files: Express.Multer.File[]): number {
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    const baseTime = 2000; // 2 seconds base time
    const sizeMultiplier = totalSize / (1024 * 1024); // Per MB

    return Math.round(baseTime + (sizeMultiplier * 500)); // 500ms per MB
  }

  async getUploadStatus(jobId: string): Promise<any> {
    try {
      const job = await this.uploadQueue.getJob(jobId);
      if (!job) {
        return { status: 'not_found' };
      }

      const state = await job.getState();
      const progress = job.progress();

      return {
        status: state,
        progress,
        data: job.data,
        result: job.returnvalue,
        failedReason: job.failedReason
      };

    } catch (error) {
      logger.error('Status check error:', error);
      return { status: 'error', error: error.message };
    }
  }

  async getQueueStats(): Promise<any> {
    try {
      const waiting = await this.uploadQueue.waiting();
      const active = await this.uploadQueue.active();
      const completed = await this.uploadQueue.completed();
      const failed = await this.uploadQueue.failed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length
      };
    } catch (error) {
      logger.error('Failed to get upload queue stats:', error);
      return { error: error.message };
    }
  }
}
```

### **Phase 2 Validation and Testing**
After completing Phase 2, run these validation tests:

```bash
# Test advanced OpenAI service
npm run test:openai-advanced

# Test semantic caching
npm run test:semantic-cache

# Test 3D visualization optimization
npm run test:3d-optimization

# Test concurrent file uploads
npm run test:upload-concurrent

# Run full test suite to maintain 91.7% success rate
npm run test

# Load test with 500 concurrent users
npm run test:load-500
```

**Expected Phase 2 Outcomes:**
- ✅ 70% Azure OpenAI cost reduction through caching
- ✅ Semantic similarity matching for cache hits
- ✅ Optimized 3D visualization with memory management
- ✅ Concurrent file processing with queue management
- ✅ Support for 500-800 concurrent users
- ✅ Maintained 91.7% test success rate

---

## **PHASE 3: PRODUCTION HARDENING (Weeks 9-12)**
**Target**: 1000+ concurrent users | **Budget**: $35,000-40,000 NZD

### **Week 9-10: Auto-scaling & Resilience**

#### **Step 3.1: Auto-scaling Configuration**
Create `infrastructure/auto-scaling/docker-swarm-stack.yml`:

```yaml
version: '3.8'

services:
  nginx-lb:
    image: nginx:alpine
    ports: ["80:80", "443:443"]
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    deploy:
      replicas: 2
      placement:
        constraints: [node.role == manager]
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits: { cpus: '1.0', memory: 512M }
        reservations: { cpus: '0.5', memory: 256M }

  cabinet-api:
    image: cabinet-insight-pro:latest
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://redis-cluster:6379
      - POSTGRES_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres-primary:5432/cabinet_pricing
    volumes: [uploads:/app/uploads, logs:/app/logs]
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits: { cpus: '2.0', memory: 2G }
        reservations: { cpus: '1.0', memory: 1G }
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis-cluster:
    image: redis:7-alpine
    command: redis-server --cluster-enabled yes --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes: [redis-data:/data]
    deploy:
      replicas: 3
      placement:
        constraints: [node.labels.redis == true]
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits: { cpus: '1.0', memory: 2G }
        reservations: { cpus: '0.5', memory: 1G }

  postgres-primary:
    image: postgres:15
    environment:
      POSTGRES_DB: cabinet_pricing
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes: [postgres-primary:/var/lib/postgresql/data]
    deploy:
      replicas: 1
      placement:
        constraints: [node.labels.database == primary]
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
      resources:
        limits: { cpus: '2.0', memory: 4G }
        reservations: { cpus: '1.0', memory: 2G }

  postgres-replica:
    image: postgres:15
    environment:
      POSTGRES_PRIMARY_HOST: postgres-primary
    volumes: [postgres-replica:/var/lib/postgresql/data]
    deploy:
      replicas: 2
      placement:
        constraints: [node.labels.database == replica]
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
      resources:
        limits: { cpus: '1.0', memory: 2G }
        reservations: { cpus: '0.5', memory: 1G }

  worker-pdf:
    image: cabinet-insight-pro:latest
    command: npm run worker:pdf
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://redis-cluster:6379
    volumes: [uploads:/app/uploads]
    deploy:
      replicas: 4
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits: { cpus: '1.0', memory: 1G }
        reservations: { cpus: '0.5', memory: 512M }

  worker-ai:
    image: cabinet-insight-pro:latest
    command: npm run worker:ai
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://redis-cluster:6379
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
    deploy:
      replicas: 2
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits: { cpus: '0.5', memory: 512M }
        reservations: { cpus: '0.25', memory: 256M }

  prometheus:
    image: prom/prometheus:latest
    ports: ["9090:9090"]
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    deploy:
      replicas: 1
      placement:
        constraints: [node.role == manager]
      resources:
        limits: { cpus: '0.5', memory: 512M }

  grafana:
    image: grafana/grafana:latest
    ports: ["3000:3000"]
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes: [grafana-data:/var/lib/grafana]
    deploy:
      replicas: 1
      placement:
        constraints: [node.role == manager]
      resources:
        limits: { cpus: '0.5', memory: 512M }

volumes:
  uploads:
  logs:
  redis-data:
  postgres-primary:
  postgres-replica:
  prometheus-data:
  grafana-data:

networks:
  default:
    driver: overlay
    attachable: true
```

#### **Step 3.2: Circuit Breaker Implementation**
Create `server/src/utils/circuitBreaker.ts`:

```typescript
import { createModuleLogger } from './logger';

const logger = createModuleLogger('CircuitBreaker');

export enum CircuitState {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN'
}

interface CircuitBreakerOptions {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
  expectedErrors?: string[];
}

export class CircuitBreaker {
  private state: CircuitState = CircuitState.CLOSED;
  private failureCount = 0;
  private lastFailureTime = 0;
  private successCount = 0;
  private options: CircuitBreakerOptions;

  constructor(options: CircuitBreakerOptions) {
    this.options = {
      failureThreshold: 5,
      recoveryTimeout: 60000, // 1 minute
      monitoringPeriod: 10000, // 10 seconds
      expectedErrors: [],
      ...options
    };
  }

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.state = CircuitState.HALF_OPEN;
        logger.info('Circuit breaker moving to HALF_OPEN state');
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure(error);
      throw error;
    }
  }

  private onSuccess(): void {
    this.failureCount = 0;

    if (this.state === CircuitState.HALF_OPEN) {
      this.successCount++;
      if (this.successCount >= 3) { // Require 3 successes to close
        this.state = CircuitState.CLOSED;
        this.successCount = 0;
        logger.info('Circuit breaker moving to CLOSED state');
      }
    }
  }

  private onFailure(error: any): void {
    // Don't count expected errors as failures
    if (this.isExpectedError(error)) {
      return;
    }

    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.state === CircuitState.HALF_OPEN) {
      this.state = CircuitState.OPEN;
      this.successCount = 0;
      logger.warn('Circuit breaker moving to OPEN state from HALF_OPEN');
    } else if (this.failureCount >= this.options.failureThreshold) {
      this.state = CircuitState.OPEN;
      logger.warn(`Circuit breaker moving to OPEN state after ${this.failureCount} failures`);
    }
  }

  private shouldAttemptReset(): boolean {
    return Date.now() - this.lastFailureTime >= this.options.recoveryTimeout;
  }

  private isExpectedError(error: any): boolean {
    if (!this.options.expectedErrors || this.options.expectedErrors.length === 0) {
      return false;
    }

    const errorMessage = error.message || error.toString();
    return this.options.expectedErrors.some(expectedError =>
      errorMessage.includes(expectedError)
    );
  }

  getState(): CircuitState {
    return this.state;
  }

  getStats(): any {
    return {
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      lastFailureTime: this.lastFailureTime
    };
  }

  reset(): void {
    this.state = CircuitState.CLOSED;
    this.failureCount = 0;
    this.successCount = 0;
    this.lastFailureTime = 0;
    logger.info('Circuit breaker manually reset');
  }
}
```

### **Week 11-12: Performance Tuning & Testing**

#### **Step 3.3: Comprehensive Monitoring Setup**
Create `infrastructure/monitoring/prometheus.yml`:

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'cabinet-insight-api'
    static_configs:
      - targets: ['cabinet-api:3001']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'redis-cluster'
    static_configs:
      - targets: ['redis-cluster:6379']
    metrics_path: '/metrics'

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-primary:5432', 'postgres-replica:5432']

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-lb:80']
    metrics_path: '/nginx_status'

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
```

#### **Step 3.4: Load Testing Configuration**
Create `tests/load/artillery-config.yml`:

```yaml
config:
  target: 'https://cabinet-insight-pro.com'
  phases:
    # Warm-up phase
    - duration: 60
      arrivalRate: 10
      name: "Warm-up"

    # Ramp-up phase
    - duration: 300
      arrivalRate: 50
      rampTo: 200
      name: "Ramp-up to 200 users"

    # Sustained load
    - duration: 600
      arrivalRate: 200
      name: "Sustained 200 users"

    # Peak load test
    - duration: 300
      arrivalRate: 200
      rampTo: 1000
      name: "Peak load to 1000 users"

    # Cool-down
    - duration: 120
      arrivalRate: 1000
      rampTo: 0
      name: "Cool-down"

  processor: "./load-test-processor.js"

  defaults:
    headers:
      'Content-Type': 'application/json'
      'User-Agent': 'Artillery Load Test'

scenarios:
  - name: "API Health Check"
    weight: 10
    flow:
      - get:
          url: "/health"
          expect:
            - statusCode: 200

  - name: "File Upload and Analysis"
    weight: 40
    flow:
      - post:
          url: "/api/upload"
          formData:
            file: "@./test-files/sample-kitchen.pdf"
            analysisType: "standard"
          capture:
            - json: "$.jobId"
              as: "uploadJobId"
      - think: 2
      - get:
          url: "/api/upload/status/{{ uploadJobId }}"
          expect:
            - statusCode: 200

  - name: "Analysis Retrieval"
    weight: 30
    flow:
      - get:
          url: "/api/analysis/{{ $randomString() }}"
          expect:
            - statusCode: [200, 404]

  - name: "3D Visualization"
    weight: 15
    flow:
      - post:
          url: "/api/3d/generate"
          json:
            analysisId: "{{ $randomString() }}"
            config:
              maxPolygons: 5000
              textureQuality: "medium"
          expect:
            - statusCode: [200, 202]

  - name: "WebSocket Connection"
    weight: 5
    engine: ws
    flow:
      - connect:
          url: "wss://cabinet-insight-pro.com/socket.io/"
      - send: '{"type":"subscribe","analysisId":"test-{{ $randomString() }}"}'
      - think: 5
      - send: '{"type":"unsubscribe"}'
```

---

## **TESTING AND VALIDATION**

### **🧪 Comprehensive Testing Strategy**

#### **Phase-by-Phase Testing Approach**

**Phase 1 Testing:**
```bash
# Infrastructure validation
./scripts/validate-redis-cluster.sh
./scripts/validate-postgres-replication.sh
./scripts/validate-load-balancer.sh

# Application testing
npm run test:cluster
npm run test:cache-l1-l2
npm run test:database-read-write

# Load testing (200-300 users)
artillery run tests/load/phase1-load-test.yml
```

**Phase 2 Testing:**
```bash
# Advanced features testing
npm run test:openai-rate-limiting
npm run test:semantic-cache
npm run test:3d-memory-management
npm run test:concurrent-uploads

# Performance testing
npm run test:performance-benchmarks

# Load testing (500-800 users)
artillery run tests/load/phase2-load-test.yml
```

**Phase 3 Testing:**
```bash
# Production readiness testing
npm run test:circuit-breakers
npm run test:auto-scaling
npm run test:failover

# Stress testing (1000+ users)
artillery run tests/load/phase3-stress-test.yml

# Chaos engineering
npm run test:chaos-monkey
```

#### **Maintaining 91.7% Test Success Rate**

Create `scripts/test-success-monitor.js`:
```javascript
const { execSync } = require('child_process');

async function monitorTestSuccess() {
  const testResults = execSync('npm test -- --reporter=json', { encoding: 'utf8' });
  const results = JSON.parse(testResults);

  const totalTests = results.stats.tests;
  const passedTests = results.stats.passes;
  const successRate = (passedTests / totalTests) * 100;

  console.log(`Test Success Rate: ${successRate.toFixed(1)}%`);

  if (successRate < 91.7) {
    console.error(`❌ Test success rate ${successRate.toFixed(1)}% below required 91.7%`);
    process.exit(1);
  } else {
    console.log(`✅ Test success rate ${successRate.toFixed(1)}% meets requirement`);
  }
}

monitorTestSuccess();
```

---

## **COST MONITORING**

### **💰 Cost Tracking and Optimization**

#### **Real-time Cost Monitoring Dashboard**
Create `server/src/services/costMonitoringService.ts`:

```typescript
import { createModuleLogger } from '../utils/logger';
import { EnhancedCacheService } from './enhancedCacheService';

const logger = createModuleLogger('CostMonitoringService');

interface CostMetrics {
  azureOpenAI: {
    totalRequests: number;
    totalTokens: number;
    totalCost: number;
    cacheHitRate: number;
    savings: number;
  };
  infrastructure: {
    compute: number;
    storage: number;
    networking: number;
    database: number;
  };
  daily: number;
  monthly: number;
  projected: number;
}

export class CostMonitoringService {
  private cacheService: EnhancedCacheService;
  private costPerToken = 0.000015; // $0.000015 per token (GPT-4o pricing)

  constructor() {
    this.cacheService = new EnhancedCacheService();
  }

  async trackOpenAIUsage(tokens: number, cached: boolean): Promise<void> {
    const today = new Date().toISOString().split('T')[0];
    const key = `cost:openai:${today}`;

    const current = await this.cacheService.get(key) || {
      totalRequests: 0,
      totalTokens: 0,
      cachedRequests: 0,
      cost: 0
    };

    current.totalRequests++;
    current.totalTokens += tokens;

    if (cached) {
      current.cachedRequests++;
    } else {
      current.cost += tokens * this.costPerToken;
    }

    await this.cacheService.set(key, current, 24 * 60 * 60);
  }

  async getCostMetrics(): Promise<CostMetrics> {
    const today = new Date().toISOString().split('T')[0];
    const openAIData = await this.cacheService.get(`cost:openai:${today}`) || {
      totalRequests: 0,
      totalTokens: 0,
      cachedRequests: 0,
      cost: 0
    };

    const cacheHitRate = openAIData.totalRequests > 0
      ? (openAIData.cachedRequests / openAIData.totalRequests) * 100
      : 0;

    const potentialCost = openAIData.totalTokens * this.costPerToken;
    const savings = potentialCost - openAIData.cost;

    return {
      azureOpenAI: {
        totalRequests: openAIData.totalRequests,
        totalTokens: openAIData.totalTokens,
        totalCost: openAIData.cost,
        cacheHitRate,
        savings
      },
      infrastructure: {
        compute: await this.getInfrastructureCost('compute'),
        storage: await this.getInfrastructureCost('storage'),
        networking: await this.getInfrastructureCost('networking'),
        database: await this.getInfrastructureCost('database')
      },
      daily: openAIData.cost,
      monthly: openAIData.cost * 30,
      projected: this.projectMonthlyCost(openAIData.cost)
    };
  }

  private async getInfrastructureCost(type: string): Promise<number> {
    // Implement infrastructure cost tracking
    // This would integrate with cloud provider APIs
    return 0;
  }

  private projectMonthlyCost(dailyCost: number): number {
    // Project monthly cost based on current usage trends
    return dailyCost * 30 * 1.2; // 20% buffer for growth
  }

  async generateCostReport(): Promise<string> {
    const metrics = await this.getCostMetrics();

    return `
# Daily Cost Report - ${new Date().toISOString().split('T')[0]}

## Azure OpenAI Usage
- Total Requests: ${metrics.azureOpenAI.totalRequests}
- Total Tokens: ${metrics.azureOpenAI.totalTokens.toLocaleString()}
- Cache Hit Rate: ${metrics.azureOpenAI.cacheHitRate.toFixed(1)}%
- Actual Cost: $${metrics.azureOpenAI.totalCost.toFixed(4)}
- Savings: $${metrics.azureOpenAI.savings.toFixed(4)}

## Infrastructure Costs
- Compute: $${metrics.infrastructure.compute.toFixed(2)}
- Storage: $${metrics.infrastructure.storage.toFixed(2)}
- Networking: $${metrics.infrastructure.networking.toFixed(2)}
- Database: $${metrics.infrastructure.database.toFixed(2)}

## Projections
- Monthly Projected: $${metrics.projected.toFixed(2)}
- Annual Projected: $${(metrics.projected * 12).toFixed(2)}
    `;
  }
}
```

---

## **TROUBLESHOOTING GUIDE**

### **🔧 Common Issues and Solutions**

#### **Redis Cluster Issues**
```bash
# Check cluster status
docker exec redis-node-1 redis-cli -p 7001 cluster nodes

# Fix split-brain scenario
docker exec redis-node-1 redis-cli -p 7001 cluster reset

# Rebalance cluster
docker exec redis-node-1 redis-cli -p 7001 cluster rebalance
```

#### **PostgreSQL Replication Issues**
```bash
# Check replication status
docker exec postgres-primary psql -U postgres -c "SELECT * FROM pg_stat_replication;"

# Fix broken replication
docker exec postgres-replica pg_basebackup -h postgres-primary -D /var/lib/postgresql/data -U replicator -v -P
```

#### **Load Balancer Issues**
```bash
# Check upstream health
curl -f http://nginx-lb/health

# Reload configuration
docker exec nginx-lb nginx -s reload

# Check logs
docker logs nginx-lb --tail=100
```

#### **Performance Issues**
```bash
# Check memory usage
docker stats

# Monitor queue lengths
curl http://cabinet-api:3001/api/metrics/queues

# Check cache hit rates
curl http://cabinet-api:3001/api/metrics/cache
```

---

## **SUCCESS METRICS**

### **📊 Key Performance Indicators**

#### **Technical Metrics**
- **Uptime**: Target 99.5%, Monitor via Prometheus
- **Response Time**: Target <3s, Monitor via APM
- **Cache Hit Rate**: Target 70%+, Monitor via Redis metrics
- **Error Rate**: Target <2%, Monitor via application logs
- **Test Success Rate**: Maintain 91.7%+

#### **Business Metrics**
- **Concurrent Users**: Target 1000+
- **Cost per User**: Target <$3 NZD/month
- **Azure OpenAI Savings**: Target 60-80%
- **Customer Satisfaction**: Target 90%+

#### **Monitoring Commands**
```bash
# Check all metrics
npm run metrics:check

# Generate performance report
npm run metrics:report

# Validate SLA compliance
npm run metrics:sla-check
```

---

## **NEXT STEPS**

### **🚀 Immediate Action Items**

#### **Week 1 Priorities**
1. **Environment Setup**
   ```bash
   # Clone and setup development environment
   git clone https://github.com/your-org/cabinet-insight-pro.git
   cd cabinet-insight-pro
   npm install

   # Setup Docker environment
   docker-compose up -d

   # Validate current test success rate
   npm test
   ```

2. **Infrastructure Preparation**
   ```bash
   # Create infrastructure directories
   mkdir -p infrastructure/{redis-cluster,postgres-cluster,monitoring}

   # Setup SSL certificates
   ./scripts/setup-ssl-certificates.sh

   # Configure environment variables
   cp .env.example .env.production
   ```

3. **Team Preparation**
   - [ ] Assign 2 senior developers for Phase 1
   - [ ] Assign 1 DevOps engineer for infrastructure
   - [ ] Setup monitoring and alerting access
   - [ ] Schedule weekly progress reviews

#### **Success Criteria for Phase 1**
- [ ] Redis cluster operational with 3 nodes
- [ ] PostgreSQL primary + 1 read replica
- [ ] Nginx load balancer distributing traffic
- [ ] Node.js clustering with 4 workers
- [ ] Enhanced caching with L1/L2 strategy
- [ ] Support for 200-300 concurrent users
- [ ] Maintained 91.7% test success rate

#### **Risk Mitigation Checklist**
- [ ] Backup current production database
- [ ] Setup rollback procedures
- [ ] Test disaster recovery scenarios
- [ ] Document all configuration changes
- [ ] Setup monitoring alerts
- [ ] Prepare incident response procedures

### **📞 Support and Resources**

#### **Implementation Support**
- **Technical Lead**: Review architecture decisions
- **DevOps Engineer**: Infrastructure implementation
- **QA Engineer**: Test validation and monitoring
- **Project Manager**: Timeline and resource coordination

#### **External Resources**
- **Azure OpenAI Documentation**: Rate limiting and optimization
- **Redis Cluster Guide**: High availability setup
- **PostgreSQL Replication**: Master-slave configuration
- **Docker Swarm**: Production deployment patterns

This comprehensive implementation guide provides everything needed to begin scaling Cabinet Insight Pro immediately. Start with Phase 1 foundation scaling and progress through each phase systematically while maintaining the 91.7% test success rate and existing TypeScript/React architecture.
