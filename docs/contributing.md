# Contributing Guide

Welcome to the A.One Kitchen Design Analysis System! We appreciate your interest in contributing to our project.

## 🚀 Getting Started

### Prerequisites

- **Node.js** 18+ (use [nvm](https://github.com/nvm-sh/nvm) for version management)
- **npm** or **yarn** package manager
- **Git** for version control
- **VS Code** (recommended) with suggested extensions

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/YOUR_USERNAME/cabinet-insight-pro.git
   cd cabinet-insight-pro
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

4. **Open Browser**
   Navigate to `http://localhost:5173`

## 📋 Development Workflow

### Branch Strategy

We use **GitHub Flow** for our development workflow:

- `main` - Production-ready code
- `feature/feature-name` - New features
- `fix/bug-description` - Bug fixes
- `docs/documentation-update` - Documentation changes

### Making Changes

1. **Create a Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Your Changes**
   - Follow our coding standards (see below)
   - Write tests for new functionality
   - Update documentation as needed

3. **Test Your Changes**
   ```bash
   npm run lint          # Check code quality
   npm run build         # Ensure build works
   npm run test          # Run tests (when available)
   ```

4. **Commit Your Changes**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

5. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

## 📝 Coding Standards

### TypeScript Guidelines

- **Strict TypeScript** - Use strict mode, avoid `any`
- **Explicit Types** - Define interfaces for complex objects
- **Consistent Naming** - Use camelCase for variables, PascalCase for components

```typescript
// ✅ Good
interface UserProfile {
  id: string;
  name: string;
  email: string;
}

const UserCard: React.FC<{ user: UserProfile }> = ({ user }) => {
  return <div>{user.name}</div>;
};

// ❌ Avoid
const UserCard = ({ user }: any) => {
  return <div>{user.name}</div>;
};
```

### React Component Guidelines

- **Functional Components** - Use function components with hooks
- **Component Structure** - Props interface, component, export
- **File Naming** - PascalCase for components, camelCase for utilities

```typescript
// ✅ Component structure
interface ButtonProps {
  variant?: 'primary' | 'secondary';
  children: React.ReactNode;
  onClick?: () => void;
}

const Button: React.FC<ButtonProps> = ({ 
  variant = 'primary', 
  children, 
  onClick 
}) => {
  return (
    <button 
      className={cn(buttonVariants({ variant }))}
      onClick={onClick}
    >
      {children}
    </button>
  );
};

export default Button;
```

### CSS/Styling Guidelines

- **Tailwind CSS** - Use utility classes
- **Component Variants** - Use `class-variance-authority` for variants
- **Responsive Design** - Mobile-first approach

```typescript
// ✅ Using Tailwind with variants
const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md font-medium transition-colors",
  {
    variants: {
      variant: {
        primary: "bg-blue-600 text-white hover:bg-blue-700",
        secondary: "bg-gray-200 text-gray-900 hover:bg-gray-300",
      },
      size: {
        sm: "h-8 px-3 text-sm",
        md: "h-10 px-4",
        lg: "h-12 px-6 text-lg",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "md",
    },
  }
);
```

### File Organization

```text
src/
├── components/
│   ├── ui/              # Base UI components (Shadcn)
│   ├── forms/           # Form-specific components
│   ├── layout/          # Layout components
│   └── features/        # Feature-specific components
├── hooks/               # Custom React hooks
├── lib/                 # Utilities and helpers
├── pages/               # Route components
├── types/               # TypeScript type definitions
└── utils/               # Pure utility functions
```

## 🧪 Testing Guidelines

### Testing Strategy

- **Unit Tests** - Individual components and utilities
- **Integration Tests** - Component interactions
- **E2E Tests** - Full user workflows (planned)

### Writing Tests

```typescript
// Example component test
import { render, screen } from '@testing-library/react';
import { Button } from './Button';

describe('Button', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('applies correct variant class', () => {
    render(<Button variant="secondary">Test</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-gray-200');
  });
});
```

## 📖 Documentation Standards

### Code Documentation

- **JSDoc Comments** - For complex functions
- **README Updates** - For new features
- **Type Definitions** - Self-documenting interfaces

```typescript
/**
 * Calculates the total price for kitchen cabinets
 * @param cabinets - Array of cabinet specifications
 * @param options - Pricing options and modifiers
 * @returns Total price with breakdown
 */
function calculateCabinetPrice(
  cabinets: CabinetSpec[],
  options: PricingOptions
): PriceBreakdown {
  // Implementation
}
```

### Commit Message Format

We follow [Conventional Commits](https://www.conventionalcommits.org/):

```
type(scope): description

feat(auth): add multi-tenant authentication
fix(upload): resolve file validation issue
docs(readme): update installation instructions
style(ui): improve button component styling
refactor(api): restructure tRPC routers
test(components): add Button component tests
```

## 🔍 Code Review Process

### Before Submitting PR

- [ ] Code follows style guidelines
- [ ] Tests pass locally
- [ ] Documentation updated
- [ ] No console.log statements
- [ ] TypeScript errors resolved

### PR Requirements

- **Clear Description** - What and why
- **Screenshots** - For UI changes
- **Breaking Changes** - Clearly marked
- **Testing Notes** - How to test changes

### Review Criteria

- Code quality and maintainability
- Performance implications
- Security considerations
- Accessibility compliance
- Documentation completeness

## 🐛 Bug Reports

### Bug Report Template

```markdown
**Bug Description**
Clear description of the issue

**Steps to Reproduce**
1. Go to '...'
2. Click on '...'
3. See error

**Expected Behavior**
What should happen

**Screenshots**
If applicable

**Environment**
- OS: [e.g. macOS]
- Browser: [e.g. Chrome 91]
- Version: [e.g. 0.1.0]
```

## 💡 Feature Requests

### Feature Request Template

```markdown
**Feature Description**
Clear description of the proposed feature

**Problem Statement**
What problem does this solve?

**Proposed Solution**
How should this work?

**Alternatives Considered**
Other approaches considered

**Additional Context**
Screenshots, mockups, etc.
```

## 📞 Getting Help

- **GitHub Issues** - Bug reports and feature requests
- **GitHub Discussions** - Questions and general discussion
- **Email** - <<EMAIL>> for urgent matters

## 📄 License

By contributing, you agree that your contributions will be licensed under the same license as the project.

---

*Thank you for contributing to the A.One Kitchen Design Analysis System!*
