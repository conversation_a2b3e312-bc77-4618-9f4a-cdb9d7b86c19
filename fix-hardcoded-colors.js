#!/usr/bin/env node

/**
 * Automated Hardcoded Color Fix Script for Blackveil Design Mind
 * 
 * Systematically replaces hardcoded colors with theme variables
 * based on the comprehensive theme consistency audit results.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Color mapping based on audit results
const COLOR_MIGRATION_MAP = {
  // Status Colors
  '#3b82f6': 'hsl(var(--status-info))',
  '#2563eb': 'hsl(var(--status-info))',
  '#1e40af': 'hsl(var(--status-info))',
  '#10b981': 'hsl(var(--status-success))',
  '#f59e0b': 'hsl(var(--status-warning))',
  '#ef4444': 'hsl(var(--status-error))',
  '#dc2626': 'hsl(var(--status-error))',
  
  // Preset Accent Colors
  '#8b5cf6': 'rgb(var(--preset-accent))',
  '#6366f1': 'rgb(var(--preset-accent))',
  '#06b6d4': 'rgb(var(--preset-accent))',
  '#0ea5e9': 'rgb(var(--preset-accent))',
  '#84cc16': 'rgb(var(--preset-accent))',
  '#f97316': 'rgb(var(--preset-accent))',
  '#ec4899': 'rgb(var(--preset-accent))',
  
  // Gray Scale
  '#6b7280': 'rgb(var(--aone-soft-gray))',
  '#64748b': 'rgb(var(--aone-soft-gray))',
  '#374151': 'rgb(var(--aone-charcoal))',
  '#9ca3af': 'rgb(var(--aone-soft-gray))',
  '#94a3b8': 'rgb(var(--aone-soft-gray))',
  '#d1d5db': 'hsl(var(--muted))',
  '#e5e7eb': 'hsl(var(--muted))',
  '#f3f4f6': 'hsl(var(--muted))',
  
  // Chart/Visualization specific
  '#8884d8': 'rgb(var(--preset-accent))',
  '#60a5fa': 'hsl(var(--status-info))'
};

// Tailwind class replacements
const TAILWIND_CLASS_REPLACEMENTS = {
  'text-blue-500': 'text-status-info',
  'text-blue-600': 'text-status-info',
  'bg-blue-500': 'bg-status-info',
  'bg-blue-600': 'bg-status-info',
  'border-blue-500': 'border-status-info',
  
  'text-green-500': 'text-status-success',
  'text-green-600': 'text-status-success',
  'bg-green-500': 'bg-status-success',
  'bg-green-600': 'bg-status-success',
  'border-green-500': 'border-status-success',
  
  'text-yellow-500': 'text-status-warning',
  'text-orange-500': 'text-status-warning',
  'bg-yellow-500': 'bg-status-warning',
  'bg-orange-500': 'bg-status-warning',
  'border-yellow-500': 'border-status-warning',
  
  'text-red-500': 'text-status-error',
  'text-red-600': 'text-status-error',
  'bg-red-500': 'bg-status-error',
  'bg-red-600': 'bg-status-error',
  'border-red-500': 'border-status-error',
  
  'text-purple-500': 'text-preset-accent',
  'text-purple-600': 'text-preset-accent',
  'bg-purple-500': 'bg-preset-accent',
  'bg-purple-600': 'bg-preset-accent',
  'border-purple-500': 'border-preset-accent',
  
  'text-gray-500': 'text-aone-soft-gray',
  'text-gray-600': 'text-aone-charcoal',
  'text-gray-700': 'text-aone-charcoal',
  'bg-gray-100': 'bg-aone-cream',
  'bg-gray-200': 'bg-muted',
  'border-gray-300': 'border-aone-sage/20'
};

// Files identified in audit with hardcoded colors
const PRIORITY_FILES = [
  'src/components/CabinetReconstructionViewer.tsx',
  'src/components/MeshNetworkVisualization.tsx',
  'src/components/PerformanceMetricsDashboard.tsx',
  'src/components/PerformanceMonitoringDashboard.tsx',
  'src/components/QuoteTemplateForm.tsx',
  'src/components/ReasoningChainVisualization.tsx',
  'src/components/ReportGenerator.tsx',
  'src/components/collaboration/EnhancedCursorTracker.tsx',
  'src/components/collaboration/LiveCursorTracker.tsx',
  'src/components/mobile/Mobile3DViewer.tsx'
];

class HardcodedColorFixer {
  constructor() {
    this.fixedFiles = [];
    this.totalReplacements = 0;
    this.errors = [];
  }

  async fixAllFiles() {
    console.log('🎨 Starting Hardcoded Color Fix for Blackveil Design Mind\n');
    
    for (const filePath of PRIORITY_FILES) {
      await this.fixFile(filePath);
    }
    
    this.generateReport();
  }

  async fixFile(filePath) {
    const fullPath = path.join(process.cwd(), filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return;
    }

    console.log(`🔧 Processing: ${filePath}`);
    
    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      const originalContent = content;
      let replacements = 0;

      // Fix hardcoded hex colors
      for (const [hardcodedColor, themeVariable] of Object.entries(COLOR_MIGRATION_MAP)) {
        const regex = new RegExp(hardcodedColor.replace('#', '#'), 'gi');
        const matches = content.match(regex);
        if (matches) {
          content = content.replace(regex, themeVariable);
          replacements += matches.length;
          console.log(`   ✓ Replaced ${matches.length} instances of ${hardcodedColor}`);
        }
      }

      // Fix Tailwind classes
      for (const [oldClass, newClass] of Object.entries(TAILWIND_CLASS_REPLACEMENTS)) {
        const regex = new RegExp(`\\b${oldClass}\\b`, 'g');
        const matches = content.match(regex);
        if (matches) {
          content = content.replace(regex, newClass);
          replacements += matches.length;
          console.log(`   ✓ Replaced ${matches.length} instances of ${oldClass} → ${newClass}`);
        }
      }

      // Only write if changes were made
      if (content !== originalContent) {
        // Create backup
        const backupPath = `${fullPath}.backup`;
        fs.writeFileSync(backupPath, originalContent);
        
        // Write fixed content
        fs.writeFileSync(fullPath, content);
        
        this.fixedFiles.push({
          path: filePath,
          replacements,
          backupPath
        });
        
        this.totalReplacements += replacements;
        console.log(`   ✅ Fixed ${replacements} color issues in ${filePath}\n`);
      } else {
        console.log(`   ℹ️  No hardcoded colors found in ${filePath}\n`);
      }

    } catch (error) {
      console.error(`   ❌ Error processing ${filePath}:`, error.message);
      this.errors.push({
        file: filePath,
        error: error.message
      });
    }
  }

  generateReport() {
    console.log('📋 HARDCODED COLOR FIX REPORT');
    console.log('='.repeat(60));
    console.log(`Files Processed: ${PRIORITY_FILES.length}`);
    console.log(`Files Fixed: ${this.fixedFiles.length}`);
    console.log(`Total Replacements: ${this.totalReplacements}`);
    console.log(`Errors: ${this.errors.length}`);
    console.log('');

    if (this.fixedFiles.length > 0) {
      console.log('✅ Successfully Fixed Files:');
      this.fixedFiles.forEach(file => {
        console.log(`   • ${file.path} (${file.replacements} replacements)`);
      });
      console.log('');
    }

    if (this.errors.length > 0) {
      console.log('❌ Errors:');
      this.errors.forEach(error => {
        console.log(`   • ${error.file}: ${error.error}`);
      });
      console.log('');
    }

    console.log('📝 Next Steps:');
    console.log('1. Test the application to ensure all components render correctly');
    console.log('2. Verify dark mode compatibility');
    console.log('3. Run the theme consistency audit again to measure improvement');
    console.log('4. Remove backup files once testing is complete');
    console.log('');

    if (this.fixedFiles.length > 0) {
      console.log('🔄 To restore from backups if needed:');
      this.fixedFiles.forEach(file => {
        console.log(`   mv "${file.backupPath}" "${path.join(process.cwd(), file.path)}"`);
      });
    }

    console.log('='.repeat(60));
  }
}

// Additional utility functions
function addMissingThemeVariables() {
  console.log('🎨 Adding missing theme variables to CSS...');
  
  const indexCssPath = path.join(process.cwd(), 'src/index.css');
  if (!fs.existsSync(indexCssPath)) {
    console.log('❌ src/index.css not found');
    return;
  }

  let cssContent = fs.readFileSync(indexCssPath, 'utf8');
  
  // Check if chart colors are already defined
  if (!cssContent.includes('--chart-primary')) {
    const additionalVariables = `
  /* Chart and Visualization Colors */
  --chart-primary: var(--aone-sage);
  --chart-secondary: var(--preset-accent);
  --chart-success: var(--status-success);
  --chart-warning: var(--status-warning);
  --chart-error: var(--status-error);
  --chart-info: var(--status-info);
  
  /* Collaboration User Colors */
  --collab-user-1: var(--status-info);
  --collab-user-2: var(--status-success);
  --collab-user-3: var(--status-warning);
  --collab-user-4: var(--status-error);
  --collab-user-5: var(--preset-accent);
  --collab-user-6: 139 69 19; /* Brown */
  --collab-user-7: 168 85 247; /* Purple */
  --collab-user-8: 236 72 153; /* Pink */`;

    // Find the :root section and add variables
    const rootMatch = cssContent.match(/:root\s*{([^}]*)}/);
    if (rootMatch) {
      const newRootContent = rootMatch[0].slice(0, -1) + additionalVariables + '\n}';
      cssContent = cssContent.replace(rootMatch[0], newRootContent);
      
      fs.writeFileSync(indexCssPath, cssContent);
      console.log('✅ Added missing theme variables to src/index.css');
    }
  } else {
    console.log('ℹ️  Chart colors already defined in CSS');
  }
}

function updateTailwindConfig() {
  console.log('🎨 Updating Tailwind configuration...');
  
  const tailwindConfigPath = path.join(process.cwd(), 'tailwind.config.ts');
  if (!fs.existsSync(tailwindConfigPath)) {
    console.log('❌ tailwind.config.ts not found');
    return;
  }

  let configContent = fs.readFileSync(tailwindConfigPath, 'utf8');
  
  // Check if chart colors are already defined
  if (!configContent.includes('chart-primary')) {
    const chartColors = `
					// Chart and Visualization Colors
					chart: {
						primary: 'rgb(var(--chart-primary))',
						secondary: 'rgb(var(--chart-secondary))',
						success: 'hsl(var(--chart-success))',
						warning: 'hsl(var(--chart-warning))',
						error: 'hsl(var(--chart-error))',
						info: 'hsl(var(--chart-info))'
					},
					// Collaboration Colors
					collab: {
						user1: 'hsl(var(--collab-user-1))',
						user2: 'hsl(var(--collab-user-2))',
						user3: 'hsl(var(--collab-user-3))',
						user4: 'hsl(var(--collab-user-4))',
						user5: 'rgb(var(--collab-user-5))',
						user6: 'rgb(var(--collab-user-6))',
						user7: 'rgb(var(--collab-user-7))',
						user8: 'rgb(var(--collab-user-8))'
					},`;

    // Find the colors section and add chart colors
    const colorsMatch = configContent.match(/(colors:\s*{[^}]*)(})/);
    if (colorsMatch) {
      const newColorsContent = colorsMatch[1] + chartColors + '\n\t\t\t\t' + colorsMatch[2];
      configContent = configContent.replace(colorsMatch[0], newColorsContent);
      
      fs.writeFileSync(tailwindConfigPath, configContent);
      console.log('✅ Added chart and collaboration colors to Tailwind config');
    }
  } else {
    console.log('ℹ️  Chart colors already defined in Tailwind config');
  }
}

// Run the fix
async function main() {
  const fixer = new HardcodedColorFixer();
  
  // Add missing theme variables first
  addMissingThemeVariables();
  updateTailwindConfig();
  
  // Fix hardcoded colors in components
  await fixer.fixAllFiles();
  
  console.log('🎉 Hardcoded color fix complete!');
  console.log('');
  console.log('Run the theme consistency audit again to see the improvement:');
  console.log('node theme-consistency-audit.js');
}

main().catch(console.error);
