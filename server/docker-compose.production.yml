# Cabinet Insight Pro - Production Docker Compose
# ===============================================
# Complete production setup with all dependencies

version: '3.8'

services:
  # Backend API Server
  cabinet-insight-api:
    build:
      context: .
      dockerfile: Dockerfile.production
    container_name: cabinet-insight-api
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - REDIS_URL=redis://redis:6379
      - UPLOAD_DIR=/app/uploads
      - MAX_FILE_SIZE=52428800
      # Azure OpenAI Configuration (set in .env file)
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      - AZURE_OPENAI_API_VERSION=${AZURE_OPENAI_API_VERSION}
      - AZURE_OPENAI_GPT4O_DEPLOYMENT_NAME=${AZURE_OPENAI_GPT4O_DEPLOYMENT_NAME}
      - AZURE_OPENAI_GPT4O_MINI_DEPLOYMENT_NAME=${AZURE_OPENAI_GPT4O_MINI_DEPLOYMENT_NAME}
      # CORS Configuration
      - CORS_ORIGIN=${CORS_ORIGIN:-http://localhost:3000}
      # Sharp Configuration
      - SHARP_CACHE_SIZE=50
    volumes:
      - uploads:/app/uploads
      - logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - cabinet-insight-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis Cache and Session Store
  redis:
    image: redis:7-alpine
    container_name: cabinet-insight-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - cabinet-insight-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: cabinet-insight-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - uploads:/var/www/uploads:ro
    depends_on:
      - cabinet-insight-api
    networks:
      - cabinet-insight-network
    profiles:
      - with-nginx

volumes:
  uploads:
    driver: local
  logs:
    driver: local
  redis-data:
    driver: local

networks:
  cabinet-insight-network:
    driver: bridge
