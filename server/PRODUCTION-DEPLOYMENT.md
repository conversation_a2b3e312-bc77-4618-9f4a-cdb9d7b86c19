# Cabinet Insight Pro - Production Deployment Guide

## 🚀 Quick Start

### 1. System Dependencies Installation
```bash
# Run the automated setup script
./scripts/setup-system-deps.sh

# Or install manually based on your OS:
# Ubuntu/Debian: sudo apt-get install poppler-utils tesseract-ocr libvips-dev build-essential
# CentOS/RHEL:   sudo yum install poppler-utils tesseract vips-devel
# macOS:         brew install poppler tesseract vips
```

### 2. Node.js Dependencies
```bash
npm install
```

### 3. Environment Configuration
```bash
# Copy and configure environment variables
cp .env.production.example .env
# Edit .env with your production values
```

### 4. Production Validation
```bash
# Validate all dependencies and configuration
npm run validate:production
```

### 5. Build and Deploy
```bash
# Build the application
npm run build

# Start in production mode
npm start
```

## 📋 System Requirements

### Required System Dependencies
- **Node.js**: >= 18.0.0
- **poppler-utils**: For PDF processing (pdftoppm, pdftotext)
- **tesseract-ocr**: For OCR capabilities
- **libvips-dev**: For Sharp image processing
- **Redis**: For caching and session storage

### Required Environment Variables
```bash
# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com/
AZURE_OPENAI_GPT4O_DEPLOYMENT_NAME=gpt-4o
AZURE_OPENAI_GPT4O_MINI_DEPLOYMENT_NAME=gpt-4o-mini

# Application Configuration
NODE_ENV=production
PORT=3001
CORS_ORIGIN=https://your-frontend-domain.com
REDIS_URL=redis://localhost:6379
```

## 🐳 Docker Deployment

### Build and Run with Docker
```bash
# Build production image
npm run docker:build

# Run with Docker Compose
npm run docker:run

# Stop services
npm run docker:stop
```

### Manual Docker Commands
```bash
# Build image
docker build -f Dockerfile.production -t cabinet-insight-pro .

# Run with compose
docker-compose -f docker-compose.production.yml up -d
```

## 🔍 Production Validation

The production validation script checks:
- ✅ System dependencies (poppler-utils, tesseract, libvips)
- ✅ Node.js dependencies
- ✅ Environment variables
- ✅ File system permissions
- ✅ Redis connectivity
- ✅ Azure OpenAI API connectivity

```bash
npm run validate:production
```

## 📊 Monitoring and Health Checks

### Health Check Endpoints
- `GET /api/health` - Basic health check
- `GET /api/health/detailed` - Detailed system status

### Key Metrics to Monitor
- PDF processing queue length
- Azure OpenAI API response times
- Redis connection status
- File upload directory disk usage
- Memory and CPU usage

## 🔧 Production Configuration

### Performance Tuning
```bash
# Environment variables for optimization
SHARP_CACHE_SIZE=50
UV_THREADPOOL_SIZE=16
NODE_MAX_OLD_SPACE_SIZE=4096
MAX_CONCURRENT_JOBS=5
```

### Security Configuration
```bash
# Rate limiting
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=60000

# File upload limits
MAX_FILE_SIZE=52428800  # 50MB
```

## 🚨 Troubleshooting

### Common Issues

#### 1. "pdftoppm not found"
```bash
# Solution: Install poppler-utils
./scripts/setup-system-deps.sh
# Or manually: sudo apt-get install poppler-utils
```

#### 2. "Sharp installation failed"
```bash
# Solution: Install libvips development libraries
sudo apt-get install libvips-dev  # Ubuntu/Debian
sudo yum install vips-devel       # CentOS/RHEL
brew install vips                 # macOS
```

#### 3. "PDF processing hangs"
- Check system resources (CPU, memory)
- Verify poppler-utils installation
- Check file permissions on upload directory
- Monitor timeout settings

#### 4. "Azure OpenAI timeout"
- Verify API key and endpoint configuration
- Check network connectivity
- Monitor API rate limits
- Increase timeout values if needed

#### 5. "Redis connection failed"
- Verify Redis server is running
- Check REDIS_URL configuration
- Verify network connectivity
- Check Redis authentication

### Validation Failures
If `npm run validate:production` fails:
1. Check the detailed error messages
2. Install missing system dependencies
3. Configure missing environment variables
4. Fix file permission issues
5. Re-run validation

## 📈 Performance Optimization

### PDF Processing
- Install poppler-utils for optimal performance
- Configure appropriate DPI settings (150 recommended)
- Set reasonable timeout values
- Monitor queue processing

### Image Processing
- Configure Sharp cache size based on available memory
- Use appropriate image quality settings
- Enable progressive JPEG for web delivery

### AI Processing
- Use GPT-4o-mini for faster responses when appropriate
- Implement request batching for multiple files
- Configure appropriate timeout values
- Monitor API usage and costs

## 🔐 Security Best Practices

### File Upload Security
- Validate file types and sizes
- Sanitize file names
- Scan uploaded files for malware (optional)
- Implement proper access controls

### API Security
- Use HTTPS in production
- Implement proper CORS configuration
- Use rate limiting
- Secure environment variable storage
- Regular security updates

### Data Protection
- Encrypt sensitive data at rest
- Use secure Redis configuration
- Implement proper logging without sensitive data
- Regular backup procedures

## 📝 Deployment Checklist

### Pre-Deployment
- [ ] All system dependencies installed
- [ ] Environment variables configured
- [ ] Production validation passes
- [ ] SSL certificates configured
- [ ] Redis server configured
- [ ] Monitoring setup complete

### Deployment
- [ ] Build application: `npm run build`
- [ ] Run final validation: `npm run validate:production`
- [ ] Deploy to production environment
- [ ] Verify health checks pass
- [ ] Test PDF processing with sample files
- [ ] Verify Azure OpenAI connectivity

### Post-Deployment
- [ ] Monitor application logs
- [ ] Verify Playwright tests pass (91.7% success rate)
- [ ] Set up log rotation
- [ ] Configure automated backups
- [ ] Set up alerting for critical issues

## 📞 Support

For production deployment issues:
1. Check this deployment guide
2. Run production validation script
3. Review application logs
4. Check system resource usage
5. Verify all dependencies are installed

## 🔄 Updates and Maintenance

### Regular Maintenance
- Update system dependencies monthly
- Update Node.js dependencies regularly
- Monitor and rotate logs
- Clean up old temporary files
- Review and update security configurations

### Monitoring
- Set up alerts for failed health checks
- Monitor PDF processing queue
- Track Azure OpenAI API usage
- Monitor system resources
- Review error logs regularly
