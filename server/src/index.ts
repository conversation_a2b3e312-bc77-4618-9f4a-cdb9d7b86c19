// Load environment variables FIRST before any other imports
import dotenv from 'dotenv';
dotenv.config();

import express from 'express';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import path from 'path';
import fs from 'fs';

import { logger } from '@/utils/logger';
import { errorHandler } from '@/middleware/errorHandler';
import { analysisRoutes } from '@/routes/analysis';
import { healthRoutes } from '@/routes/health';
import { metricsRoutes } from '@/routes/metrics';
import optimizationRoutes from '@/routes/optimization';
import abTestRoutes from '@/routes/abTests';
import reasoningRoutes from '@/routes/reasoning';
import reasoningVisualizationRoutes from '@/routes/reasoningVisualization';
import pdfRoutes from '@/routes/pdf';
import intelligentMeasurementRoutes from '@/routes/intelligentMeasurement';
import enhancedHardwareRoutes from '@/routes/enhancedHardware';
import materialRecognitionRoutes from '@/routes/materialRecognition';
import layoutOptimizationRoutes from '@/routes/layoutOptimization';
import enhancedReportingRoutes from '@/routes/enhancedReporting';
import authRoutes from '@/routes/auth';
import collaborationRoutes from '@/routes/collaboration';
import cacheMetricsRoutes from '@/routes/cacheMetrics';
import { performanceMetricsRoutes } from '@/routes/performanceMetrics';
import quotationRoutes from '@/routes/quotation';
import quoteTemplateRoutes from '@/routes/quoteTemplates';
import performanceMonitoringRoutes from '@/routes/performanceMonitoring';
import documentIntelligenceRoutes from '@/routes/documentIntelligence';
import { enterpriseScalabilityRoutes } from '@/routes/enterpriseScalability';
import { SocketManager } from '@/services/socketManager';
import { AnalysisQueue } from '@/services/analysisQueue';
import { initializeReasoningManagerWithSocket } from '@/services/reasoningManager';
import { DatabaseService } from '@/services/databaseService';
import { PerformanceMonitoringService } from '@/services/performanceMonitoringService';

const app = express();
const server = createServer(app);
const io = new SocketIOServer(server, {
  cors: {
    origin: process.env.CORS_ORIGIN || "http://localhost:8080",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3001;
const NODE_ENV = process.env.NODE_ENV || 'development';

// Create necessary directories
const createDirectories = () => {
  const dirs = [
    process.env.UPLOAD_DIR || './uploads',
    process.env.TEMP_DIR || './temp',
    './logs'
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      logger.info(`Created directory: ${dir}`);
    }
  });
};

// Initialize directories
createDirectories();

// Initialize database
const database = DatabaseService.getInstance();
logger.info('Database initialized');

// Initialize performance monitoring
const performanceMonitoring = PerformanceMonitoringService.getInstance();
logger.info('Performance monitoring initialized');

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS configuration with multiple origins support
const corsOrigins = (process.env.CORS_ORIGIN || "http://localhost:8080").split(',').map(origin => origin.trim());

app.use(cors({
  origin: (origin, callback) => {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    if (corsOrigins.includes(origin)) {
      return callback(null, true);
    } else {
      return callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
  preflightContinue: false,
  optionsSuccessStatus: 204
}));

// Additional explicit OPTIONS handling for problematic routes
app.options('*', (req, res) => {
  const origin = req.headers.origin;
  if (!origin || corsOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin || corsOrigins[0]);
    res.header('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-Requested-With,Accept,Origin');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.status(204).send();
  } else {
    res.status(403).send('CORS not allowed');
  }
});

// Rate limiting
if (process.env.ENABLE_RATE_LIMITING === 'true') {
  const limiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // limit each IP to 100 requests per windowMs
    message: {
      error: 'Too many requests from this IP, please try again later.',
      retryAfter: Math.ceil(parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000') / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  
  app.use('/api/', limiter);
  logger.info('Rate limiting enabled');
}

// Body parsing middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Static file serving for uploads
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Initialize services
const socketManager = new SocketManager(io);
const analysisQueue = new AnalysisQueue(socketManager);

// Initialize reasoning manager with socket manager for real-time updates
initializeReasoningManagerWithSocket(socketManager);

// Set SocketManager for performance monitoring
performanceMonitoring.setSocketManager(socketManager);

// Start real-time metrics broadcasting (5-second intervals)
socketManager.startRealTimeMetricsBroadcast(5000);

// Start mesh network monitoring (10-second intervals)
socketManager.startMeshNetworkMonitoring(10000);

// Make services available to routes
app.locals.socketManager = socketManager;
app.locals.analysisQueue = analysisQueue;

// Routes
app.use('/api/health', healthRoutes);
app.use('/api/metrics', metricsRoutes);
app.use('/api/cache', cacheMetricsRoutes);
app.use('/api/performance', performanceMetricsRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/collaboration', collaborationRoutes);
app.use('/api/analysis', analysisRoutes);
app.use('/api/optimization', optimizationRoutes);
app.use('/api/ab-tests', abTestRoutes);
app.use('/api/reasoning', reasoningRoutes);
app.use('/api/reasoning', reasoningVisualizationRoutes);
app.use('/api/pdf', pdfRoutes);
app.use('/api/analysis', intelligentMeasurementRoutes);
app.use('/api/analysis', enhancedHardwareRoutes);
app.use('/api/analysis', materialRecognitionRoutes);
app.use('/api/analysis', layoutOptimizationRoutes);
app.use('/api/reports', enhancedReportingRoutes);
app.use('/api/quotation', quotationRoutes);
app.use('/api/quotation/templates', quoteTemplateRoutes);
app.use('/api/performance-monitoring', performanceMonitoringRoutes);
app.use('/api/document-intelligence', documentIntelligenceRoutes);
app.use('/api/enterprise/scalability', enterpriseScalabilityRoutes);

// Error handling middleware
app.use(errorHandler);

// Socket.IO connection handling
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);
  
  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`);
    socketManager.removeClient(socket.id);
  });
  
  socket.on('join-analysis', (analysisId: string) => {
    socket.join(`analysis-${analysisId}`);
    logger.info(`Client ${socket.id} joined analysis room: ${analysisId}`);
  });
  
  socket.on('leave-analysis', (analysisId: string) => {
    socket.leave(`analysis-${analysisId}`);
    logger.info(`Client ${socket.id} left analysis room: ${analysisId}`);
  });
});

// Graceful shutdown
const gracefulShutdown = (signal: string) => {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);
  
  server.close(() => {
    logger.info('HTTP server closed');
    
    // Close database connections, cleanup resources, etc.
    analysisQueue.shutdown();
    
    process.exit(0);
  });
  
  // Force close after 30 seconds
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 30000);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start server
server.listen(PORT, () => {
  logger.info(`🚀 Blackveil Design Mind Server running on port ${PORT}`);
  logger.info(`📊 Environment: ${NODE_ENV}`);
  logger.info(`🔗 CORS Origin: ${process.env.CORS_ORIGIN || "http://localhost:8080"}`);
  
  // Check for API configuration
  const hasAzureConfig = process.env.AZURE_OPENAI_API_KEY && process.env.AZURE_OPENAI_ENDPOINT;
  const hasOpenAIConfig = process.env.OPENAI_API_KEY;

  if (hasAzureConfig) {
    logger.info('✅ Azure OpenAI configuration detected');
    logger.info(`🔗 Azure Endpoint: ${process.env.AZURE_OPENAI_ENDPOINT}`);
    logger.info(`📋 API Version: ${process.env.AZURE_OPENAI_API_VERSION || '2025-01-01-preview'}`);
  } else if (hasOpenAIConfig) {
    logger.info('✅ Standard OpenAI API key configured');
  } else {
    logger.warn('⚠️  No OpenAI configuration found - API calls will use mock data');
    logger.warn('💡 Set AZURE_OPENAI_API_KEY and AZURE_OPENAI_ENDPOINT for Azure OpenAI');
    logger.warn('💡 Or set OPENAI_API_KEY for standard OpenAI');
  }
});

export { app, server, io };
