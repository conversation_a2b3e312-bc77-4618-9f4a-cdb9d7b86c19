// Quick test to verify asyncHandler import
const { asyncHandler } = require('./middleware/errorHandler');

console.log('asyncHandler type:', typeof asyncHandler);
console.log('asyncHandler:', asyncHandler);

if (typeof asyncHandler === 'function') {
  console.log('✅ asyncHandler is a function');
  
  // Test creating a wrapped function
  const testHandler = asyncHandler(async (req, res, next) => {
    res.json({ success: true });
  });
  
  console.log('✅ asyncHandler wrapper created successfully');
  console.log('Wrapped handler type:', typeof testHandler);
} else {
  console.log('❌ asyncHandler is not a function');
}
