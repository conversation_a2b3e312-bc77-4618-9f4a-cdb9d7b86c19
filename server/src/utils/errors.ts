/**
 * Custom Error Classes
 * 
 * Provides custom error types for better error handling and HTTP status codes
 */

export class ValidationError extends Error {
  public statusCode: number = 400;
  
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends Error {
  public statusCode: number = 404;
  
  constructor(message: string) {
    super(message);
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends Error {
  public statusCode: number = 401;
  
  constructor(message: string) {
    super(message);
    this.name = 'UnauthorizedError';
  }
}

export class ForbiddenError extends Error {
  public statusCode: number = 403;
  
  constructor(message: string) {
    super(message);
    this.name = 'ForbiddenError';
  }
}

export class ConflictError extends Error {
  public statusCode: number = 409;
  
  constructor(message: string) {
    super(message);
    this.name = 'ConflictError';
  }
}

export class InternalServerError extends Error {
  public statusCode: number = 500;
  
  constructor(message: string) {
    super(message);
    this.name = 'InternalServerError';
  }
}
