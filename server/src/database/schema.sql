-- Cabinet Insight Pro - Collaboration System Database Schema
-- SQLite Database Schema for Advanced Collaboration Tools

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- Users table for authentication and user management
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'viewer', -- 'admin', 'designer', 'collaborator', 'viewer'
    avatar_url TEXT,
    is_active BOOLEAN DEFAULT true,
    last_login DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Organizations/Workspaces for team management
CREATE TABLE IF NOT EXISTS organizations (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    owner_id TEXT NOT NULL,
    settings TEXT, -- J<PERSON><PERSON> settings
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (owner_id) REFERENCES users (id)
);

-- User-Organization relationships
CREATE TABLE IF NOT EXISTS organization_members (
    id TEXT PRIMARY KEY,
    organization_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'member', -- 'owner', 'admin', 'member'
    permissions TEXT, -- JSON permissions
    joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (organization_id) REFERENCES organizations (id),
    FOREIGN KEY (user_id) REFERENCES users (id),
    UNIQUE(organization_id, user_id)
);

-- Projects for organizing analysis results
CREATE TABLE IF NOT EXISTS projects (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    organization_id TEXT,
    owner_id TEXT NOT NULL,
    visibility TEXT DEFAULT 'private', -- 'public', 'private', 'organization'
    status TEXT DEFAULT 'active', -- 'active', 'archived', 'deleted'
    tags TEXT, -- JSON array of tags
    folder_path TEXT, -- For organization
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (organization_id) REFERENCES organizations (id),
    FOREIGN KEY (owner_id) REFERENCES users (id)
);

-- Analysis results table (enhanced from existing system)
CREATE TABLE IF NOT EXISTS analysis_results (
    id TEXT PRIMARY KEY,
    project_id TEXT,
    filename TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER,
    analysis_data TEXT, -- JSON analysis results
    status TEXT DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
    confidence_score REAL,
    processing_time_ms INTEGER,
    created_by TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    version INTEGER DEFAULT 1,
    FOREIGN KEY (project_id) REFERENCES projects (id),
    FOREIGN KEY (created_by) REFERENCES users (id)
);

-- Enhanced Analysis Engine results (from existing system)
CREATE TABLE IF NOT EXISTS enhanced_analysis (
    id TEXT PRIMARY KEY,
    analysis_id TEXT NOT NULL,
    feature_type TEXT NOT NULL, -- '3d_reconstruction', 'intelligent_measurement', 'smart_hardware', 'material_recognition', 'layout_optimization', 'enhanced_reporting'
    result_data TEXT NOT NULL, -- JSON data
    confidence_score REAL,
    processing_time_ms INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT, -- User ID who last updated
    version INTEGER DEFAULT 1,
    FOREIGN KEY (analysis_id) REFERENCES analysis_results (id),
    FOREIGN KEY (updated_by) REFERENCES users (id)
);

-- Project-Analysis relationships
CREATE TABLE IF NOT EXISTS project_analyses (
    id TEXT PRIMARY KEY,
    project_id TEXT NOT NULL,
    analysis_id TEXT NOT NULL,
    added_by TEXT NOT NULL,
    added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects (id),
    FOREIGN KEY (analysis_id) REFERENCES analysis_results (id),
    FOREIGN KEY (added_by) REFERENCES users (id),
    UNIQUE(project_id, analysis_id)
);

-- Project permissions
CREATE TABLE IF NOT EXISTS project_permissions (
    id TEXT PRIMARY KEY,
    project_id TEXT NOT NULL,
    user_id TEXT,
    email TEXT, -- For external sharing
    permission_level TEXT NOT NULL, -- 'view', 'comment', 'edit', 'admin'
    granted_by TEXT NOT NULL,
    expires_at DATETIME,
    password_hash TEXT, -- For password-protected shares
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects (id),
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (granted_by) REFERENCES users (id)
);

-- Comments system for real-time collaboration
CREATE TABLE IF NOT EXISTS comments (
    id TEXT PRIMARY KEY,
    project_id TEXT,
    analysis_id TEXT, -- Optional: can be NULL for general project comments
    parent_comment_id TEXT, -- For threading
    content TEXT NOT NULL,
    author_id TEXT NOT NULL,
    status TEXT DEFAULT 'open', -- 'open', 'in_progress', 'resolved'
    position_data TEXT, -- JSON for annotation positioning
    attachments TEXT, -- JSON array of attachment URLs
    mentions TEXT, -- JSON array of mentioned user IDs
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects (id),
    -- Note: analysis_id foreign key removed to allow general project comments
    FOREIGN KEY (parent_comment_id) REFERENCES comments (id),
    FOREIGN KEY (author_id) REFERENCES users (id)
);

-- Visual annotations for marking specific areas
CREATE TABLE IF NOT EXISTS annotations (
    id TEXT PRIMARY KEY,
    comment_id TEXT,
    analysis_id TEXT NOT NULL,
    annotation_type TEXT NOT NULL, -- 'point', 'rectangle', 'circle', 'polygon', 'arrow'
    coordinates TEXT NOT NULL, -- JSON coordinates data
    style_data TEXT, -- JSON styling information
    created_by TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (comment_id) REFERENCES comments (id),
    FOREIGN KEY (analysis_id) REFERENCES analysis_results (id),
    FOREIGN KEY (created_by) REFERENCES users (id)
);

-- Version control for analysis results
CREATE TABLE IF NOT EXISTS analysis_versions (
    id TEXT PRIMARY KEY,
    analysis_id TEXT NOT NULL,
    version_number INTEGER NOT NULL,
    changes_summary TEXT,
    analysis_data TEXT NOT NULL, -- JSON snapshot of analysis data
    created_by TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (analysis_id) REFERENCES analysis_results (id),
    FOREIGN KEY (created_by) REFERENCES users (id),
    UNIQUE(analysis_id, version_number)
);

-- Activity log for audit trail
CREATE TABLE IF NOT EXISTS activity_log (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    action_type TEXT NOT NULL, -- 'create', 'update', 'delete', 'share', 'comment', 'annotate'
    resource_type TEXT NOT NULL, -- 'project', 'analysis', 'comment', 'annotation'
    resource_id TEXT NOT NULL,
    details TEXT, -- JSON additional details
    ip_address TEXT,
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);

-- Real-time presence tracking
CREATE TABLE IF NOT EXISTS user_presence (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    project_id TEXT,
    analysis_id TEXT,
    status TEXT NOT NULL, -- 'online', 'away', 'offline'
    current_view TEXT, -- JSON data about what user is viewing
    last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (project_id) REFERENCES projects (id),
    FOREIGN KEY (analysis_id) REFERENCES analysis_results (id),
    UNIQUE(user_id, project_id)
);

-- Notification system
CREATE TABLE IF NOT EXISTS notifications (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    type TEXT NOT NULL, -- 'mention', 'comment', 'share', 'project_update'
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data TEXT, -- JSON additional data
    read_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);

-- Indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_organization_members_org_id ON organization_members(organization_id);
CREATE INDEX IF NOT EXISTS idx_organization_members_user_id ON organization_members(user_id);
CREATE INDEX IF NOT EXISTS idx_projects_owner_id ON projects(owner_id);
CREATE INDEX IF NOT EXISTS idx_projects_organization_id ON projects(organization_id);
CREATE INDEX IF NOT EXISTS idx_analysis_results_project_id ON analysis_results(project_id);
CREATE INDEX IF NOT EXISTS idx_analysis_results_created_by ON analysis_results(created_by);
CREATE INDEX IF NOT EXISTS idx_enhanced_analysis_analysis_id ON enhanced_analysis(analysis_id);
CREATE INDEX IF NOT EXISTS idx_comments_project_id ON comments(project_id);
CREATE INDEX IF NOT EXISTS idx_comments_analysis_id ON comments(analysis_id);
CREATE INDEX IF NOT EXISTS idx_comments_author_id ON comments(author_id);
CREATE INDEX IF NOT EXISTS idx_annotations_analysis_id ON annotations(analysis_id);
CREATE INDEX IF NOT EXISTS idx_activity_log_user_id ON activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_log_resource ON activity_log(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_user_presence_user_id ON user_presence(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
