import cluster from 'cluster';
import os from 'os';
import { createModuleLogger } from './utils/logger';

const logger = createModuleLogger('Cluster');

interface ClusterConfig {
  maxWorkers: number;
  gracefulShutdownTimeout: number;
  workerRestartDelay: number;
  enableGracefulRestart: boolean;
  enableHealthCheck: boolean;
  healthCheckInterval: number;
}

interface WorkerStats {
  id: number;
  pid: number;
  startTime: number;
  restartCount: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: NodeJS.CpuUsage;
  isHealthy: boolean;
  lastHealthCheck: number;
}

/**
 * Node.js Cluster Manager for Cabinet Insight Pro Scalability
 * 
 * Implements intelligent worker process management with:
 * - Automatic worker scaling based on CPU cores
 * - Graceful shutdown and restart capabilities
 * - Health monitoring and automatic recovery
 * - Performance metrics collection
 * - Zero-downtime deployments
 */
class ClusterManager {
  private config: ClusterConfig;
  private workers: Map<number, WorkerStats> = new Map();
  private isShuttingDown = false;
  private healthCheckInterval?: NodeJS.Timeout;

  constructor() {
    const numCPUs = os.cpus().length;
    const maxWorkers = parseInt(process.env.MAX_WORKERS || '0');
    
    this.config = {
      maxWorkers: maxWorkers > 0 ? Math.min(maxWorkers, numCPUs) : Math.min(numCPUs, 4),
      gracefulShutdownTimeout: parseInt(process.env.GRACEFUL_SHUTDOWN_TIMEOUT || '30000'),
      workerRestartDelay: parseInt(process.env.WORKER_RESTART_DELAY || '1000'),
      enableGracefulRestart: process.env.ENABLE_GRACEFUL_RESTART !== 'false',
      enableHealthCheck: process.env.ENABLE_HEALTH_CHECK !== 'false',
      healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000')
    };

    logger.info('Cluster configuration:', this.config);
  }

  /**
   * Start the cluster manager
   */
  start(): void {
    if (cluster.isPrimary) {
      this.startPrimary();
    } else {
      this.startWorker();
    }
  }

  private startPrimary(): void {
    logger.info(`Primary process ${process.pid} is running`);
    logger.info(`Starting ${this.config.maxWorkers} workers`);

    // Fork initial workers
    for (let i = 0; i < this.config.maxWorkers; i++) {
      this.forkWorker();
    }

    // Set up event handlers
    this.setupPrimaryEventHandlers();

    // Start health monitoring
    if (this.config.enableHealthCheck) {
      this.startHealthMonitoring();
    }

    // Handle process signals
    this.setupSignalHandlers();

    logger.info(`Cluster started with ${this.workers.size} workers`);
  }

  private startWorker(): void {
    // Import and start the main application
    require('./index');
    
    const workerId = cluster.worker?.id || 0;
    logger.info(`Worker ${workerId} (PID: ${process.pid}) started`);

    // Worker-specific error handling
    process.on('uncaughtException', (error) => {
      logger.error(`Uncaught exception in worker ${workerId}:`, error);
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error(`Unhandled rejection in worker ${workerId}:`, reason);
      process.exit(1);
    });

    // Graceful shutdown handling
    process.on('SIGTERM', () => {
      logger.info(`Worker ${workerId} received SIGTERM, shutting down gracefully`);
      this.gracefulWorkerShutdown();
    });

    process.on('SIGINT', () => {
      logger.info(`Worker ${workerId} received SIGINT, shutting down gracefully`);
      this.gracefulWorkerShutdown();
    });
  }

  private forkWorker(): void {
    const worker = cluster.fork();
    const workerId = worker.id;

    const workerStats: WorkerStats = {
      id: workerId,
      pid: worker.process.pid!,
      startTime: Date.now(),
      restartCount: this.workers.get(workerId)?.restartCount || 0,
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      isHealthy: true,
      lastHealthCheck: Date.now()
    };

    this.workers.set(workerId, workerStats);

    logger.info(`Worker ${workerId} (PID: ${worker.process.pid}) forked`);

    // Set up worker-specific event handlers
    worker.on('message', (message) => {
      this.handleWorkerMessage(workerId, message);
    });

    worker.on('error', (error) => {
      logger.error(`Worker ${workerId} error:`, error);
      this.markWorkerUnhealthy(workerId);
    });

    worker.on('disconnect', () => {
      logger.warn(`Worker ${workerId} disconnected`);
    });
  }

  private setupPrimaryEventHandlers(): void {
    cluster.on('exit', (worker, code, signal) => {
      const workerId = worker.id;
      const workerStats = this.workers.get(workerId);

      logger.warn(`Worker ${workerId} (PID: ${worker.process.pid}) died with code ${code} and signal ${signal}`);

      if (workerStats) {
        workerStats.restartCount++;
        this.workers.delete(workerId);
      }

      // Restart worker if not shutting down
      if (!this.isShuttingDown) {
        logger.info(`Restarting worker ${workerId} after ${this.config.workerRestartDelay}ms delay`);
        
        setTimeout(() => {
          if (!this.isShuttingDown) {
            this.forkWorker();
          }
        }, this.config.workerRestartDelay);
      }
    });

    cluster.on('listening', (worker, address) => {
      logger.info(`Worker ${worker.id} is listening on ${address.address}:${address.port}`);
    });

    cluster.on('online', (worker) => {
      logger.info(`Worker ${worker.id} is online`);
    });

    cluster.on('disconnect', (worker) => {
      logger.warn(`Worker ${worker.id} disconnected`);
    });
  }

  private setupSignalHandlers(): void {
    // Graceful shutdown
    process.on('SIGTERM', () => {
      logger.info('Primary received SIGTERM, initiating graceful shutdown');
      this.gracefulShutdown();
    });

    process.on('SIGINT', () => {
      logger.info('Primary received SIGINT, initiating graceful shutdown');
      this.gracefulShutdown();
    });

    // Graceful restart (zero-downtime deployment)
    process.on('SIGUSR2', () => {
      if (this.config.enableGracefulRestart) {
        logger.info('Primary received SIGUSR2, initiating graceful restart');
        this.gracefulRestart();
      }
    });

    // Handle uncaught exceptions in primary
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception in primary:', error);
      this.gracefulShutdown();
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection in primary:', reason);
      this.gracefulShutdown();
    });
  }

  private startHealthMonitoring(): void {
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, this.config.healthCheckInterval);

    logger.info(`Health monitoring started (interval: ${this.config.healthCheckInterval}ms)`);
  }

  private performHealthCheck(): void {
    for (const [workerId, stats] of this.workers.entries()) {
      const worker = cluster.workers[workerId];
      
      if (!worker || worker.isDead()) {
        this.markWorkerUnhealthy(workerId);
        continue;
      }

      // Send health check message to worker
      worker.send({ type: 'health-check', timestamp: Date.now() });

      // Check if worker responded to previous health check
      const timeSinceLastCheck = Date.now() - stats.lastHealthCheck;
      if (timeSinceLastCheck > this.config.healthCheckInterval * 2) {
        logger.warn(`Worker ${workerId} failed health check (no response for ${timeSinceLastCheck}ms)`);
        this.markWorkerUnhealthy(workerId);
        
        // Restart unhealthy worker
        if (worker && !worker.isDead()) {
          logger.info(`Restarting unhealthy worker ${workerId}`);
          worker.kill('SIGTERM');
        }
      }
    }
  }

  private handleWorkerMessage(workerId: number, message: any): void {
    const workerStats = this.workers.get(workerId);
    if (!workerStats) return;

    switch (message.type) {
      case 'health-check-response':
        workerStats.isHealthy = true;
        workerStats.lastHealthCheck = Date.now();
        workerStats.memoryUsage = message.memoryUsage;
        workerStats.cpuUsage = message.cpuUsage;
        break;

      case 'worker-stats':
        Object.assign(workerStats, message.stats);
        break;

      default:
        logger.debug(`Unknown message from worker ${workerId}:`, message);
    }
  }

  private markWorkerUnhealthy(workerId: number): void {
    const workerStats = this.workers.get(workerId);
    if (workerStats) {
      workerStats.isHealthy = false;
    }
  }

  private async gracefulShutdown(): Promise<void> {
    if (this.isShuttingDown) return;
    
    this.isShuttingDown = true;
    logger.info('Starting graceful shutdown...');

    // Stop health monitoring
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    // Send shutdown signal to all workers
    const shutdownPromises: Promise<void>[] = [];
    
    for (const workerId of Object.keys(cluster.workers)) {
      const worker = cluster.workers[workerId];
      if (worker && !worker.isDead()) {
        shutdownPromises.push(this.shutdownWorker(worker));
      }
    }

    // Wait for all workers to shutdown gracefully
    try {
      await Promise.all(shutdownPromises);
      logger.info('All workers shut down gracefully');
    } catch (error) {
      logger.error('Error during graceful shutdown:', error);
    }

    process.exit(0);
  }

  private async shutdownWorker(worker: cluster.Worker): Promise<void> {
    return new Promise<void>((resolve) => {
      const timeout = setTimeout(() => {
        logger.warn(`Worker ${worker.id} did not shut down gracefully, killing forcefully`);
        worker.kill('SIGKILL');
        resolve();
      }, this.config.gracefulShutdownTimeout);

      worker.on('exit', () => {
        clearTimeout(timeout);
        resolve();
      });

      worker.send({ type: 'shutdown' });
      worker.disconnect();
    });
  }

  private async gracefulRestart(): Promise<void> {
    logger.info('Starting graceful restart (zero-downtime deployment)...');

    const workerIds = Object.keys(cluster.workers);
    
    // Restart workers one by one to maintain availability
    for (const workerId of workerIds) {
      const worker = cluster.workers[workerId];
      if (worker && !worker.isDead()) {
        logger.info(`Restarting worker ${workerId} for zero-downtime deployment`);
        
        // Fork new worker first
        this.forkWorker();
        
        // Wait a bit for new worker to be ready
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Shutdown old worker
        await this.shutdownWorker(worker);
        
        // Wait before restarting next worker
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    logger.info('Graceful restart completed');
  }

  private gracefulWorkerShutdown(): void {
    // Implement graceful shutdown logic for worker
    // This should close servers, database connections, etc.
    
    setTimeout(() => {
      logger.warn('Graceful shutdown timeout, forcing exit');
      process.exit(1);
    }, this.config.gracefulShutdownTimeout);

    // Close HTTP server gracefully if available
    if (global.server) {
      global.server.close(() => {
        logger.info('HTTP server closed');
        process.exit(0);
      });
    } else {
      process.exit(0);
    }
  }

  /**
   * Get cluster statistics
   */
  getStats(): any {
    if (!cluster.isPrimary) {
      return null;
    }

    const stats = {
      primary: {
        pid: process.pid,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage()
      },
      workers: Array.from(this.workers.values()),
      cluster: {
        totalWorkers: this.workers.size,
        healthyWorkers: Array.from(this.workers.values()).filter(w => w.isHealthy).length,
        totalRestarts: Array.from(this.workers.values()).reduce((sum, w) => sum + w.restartCount, 0)
      }
    };

    return stats;
  }
}

// Start cluster if this file is run directly
if (require.main === module) {
  const clusterManager = new ClusterManager();
  clusterManager.start();
}

export { ClusterManager };
