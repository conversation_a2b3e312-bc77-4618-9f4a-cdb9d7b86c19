import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { DatabaseService } from './databaseService';
import { logger } from '@/utils/logger';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'designer' | 'collaborator' | 'viewer';
  avatarUrl?: string;
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: string;
}

export class AuthService {
  private db: DatabaseService;
  private jwtSecret: string;
  private jwtRefreshSecret: string;
  private accessTokenExpiry: string = '1h';
  private refreshTokenExpiry: string = '7d';

  constructor() {
    this.db = DatabaseService.getInstance();
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    this.jwtRefreshSecret = process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key';
    
    if (!process.env.JWT_SECRET) {
      logger.warn('JWT_SECRET not set in environment variables, using default (not secure for production)');
    }
  }

  /**
   * Register a new user
   */
  public async register(userData: RegisterData): Promise<{ user: User; tokens: AuthTokens }> {
    try {
      // Check if user already exists
      const existingUser = this.db.getUserByEmail(userData.email);
      if (existingUser) {
        throw new Error('User with this email already exists');
      }

      // Hash password
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(userData.password, saltRounds);

      // Create user
      const userId = uuidv4();
      this.db.createUser({
        id: userId,
        email: userData.email.toLowerCase(),
        passwordHash,
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: userData.role || 'viewer'
      });

      // Get created user
      const user = this.db.getUserById(userId);
      if (!user) {
        throw new Error('Failed to create user');
      }

      // Generate tokens
      const tokens = this.generateTokens(user);

      // Update last login
      this.db.updateUserLastLogin(userId);

      logger.info('User registered successfully', {
        userId,
        email: userData.email,
        role: userData.role || 'viewer'
      });

      return {
        user: this.formatUser(user),
        tokens
      };
    } catch (error) {
      logger.error('User registration failed', { error, email: userData.email });
      throw error;
    }
  }

  /**
   * Login user with email and password
   */
  public async login(credentials: LoginCredentials): Promise<{ user: User; tokens: AuthTokens }> {
    try {
      // Get user by email
      const user = this.db.getUserByEmail(credentials.email.toLowerCase());
      if (!user) {
        throw new Error('Invalid email or password');
      }

      // Check if user is active
      if (!user.is_active) {
        throw new Error('Account is deactivated');
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(credentials.password, user.password_hash);
      if (!isPasswordValid) {
        throw new Error('Invalid email or password');
      }

      // Generate tokens
      const tokens = this.generateTokens(user);

      // Update last login
      this.db.updateUserLastLogin(user.id);

      logger.info('User logged in successfully', {
        userId: user.id,
        email: user.email
      });

      return {
        user: this.formatUser(user),
        tokens
      };
    } catch (error) {
      logger.error('User login failed', { error, email: credentials.email });
      throw error;
    }
  }

  /**
   * Refresh access token using refresh token
   */
  public async refreshToken(refreshToken: string): Promise<AuthTokens> {
    try {
      // Verify refresh token
      const decoded = jwt.verify(refreshToken, this.jwtRefreshSecret) as any;
      
      // Get user
      const user = this.db.getUserById(decoded.userId);
      if (!user || !user.is_active) {
        throw new Error('Invalid refresh token');
      }

      // Generate new tokens
      const tokens = this.generateTokens(user);

      logger.info('Token refreshed successfully', { userId: user.id });

      return tokens;
    } catch (error) {
      logger.error('Token refresh failed', { error });
      throw new Error('Invalid refresh token');
    }
  }

  /**
   * Verify access token and return user
   */
  public async verifyToken(token: string): Promise<User> {
    try {
      // Verify token
      const decoded = jwt.verify(token, this.jwtSecret) as any;
      
      // Get user
      const user = this.db.getUserById(decoded.userId);
      if (!user || !user.is_active) {
        throw new Error('Invalid token');
      }

      return this.formatUser(user);
    } catch (error) {
      logger.error('Token verification failed', { error });
      throw new Error('Invalid token');
    }
  }

  /**
   * Generate access and refresh tokens
   */
  private generateTokens(user: any): AuthTokens {
    const payload = {
      userId: user.id,
      email: user.email,
      role: user.role
    };

    const accessToken = jwt.sign(payload, this.jwtSecret, {
      expiresIn: this.accessTokenExpiry
    });

    const refreshToken = jwt.sign(
      { userId: user.id },
      this.jwtRefreshSecret,
      { expiresIn: this.refreshTokenExpiry }
    );

    return {
      accessToken,
      refreshToken,
      expiresIn: 3600 // 1 hour in seconds
    };
  }

  /**
   * Format user data for API response
   */
  private formatUser(user: any): User {
    return {
      id: user.id,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      role: user.role,
      avatarUrl: user.avatar_url,
      isActive: user.is_active,
      lastLogin: user.last_login ? new Date(user.last_login) : undefined,
      createdAt: new Date(user.created_at),
      updatedAt: new Date(user.updated_at)
    };
  }

  /**
   * Get user by ID
   */
  public async getUserById(id: string): Promise<User | null> {
    try {
      const user = this.db.getUserById(id);
      return user ? this.formatUser(user) : null;
    } catch (error) {
      logger.error('Failed to get user by ID', { error, userId: id });
      return null;
    }
  }

  /**
   * Update user profile
   */
  public async updateUserProfile(userId: string, updates: {
    firstName?: string;
    lastName?: string;
    avatarUrl?: string;
  }): Promise<User> {
    try {
      const user = this.db.getUserById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Update user in database (would need to add this method to DatabaseService)
      // For now, just return the existing user
      return this.formatUser(user);
    } catch (error) {
      logger.error('Failed to update user profile', { error, userId });
      throw error;
    }
  }

  /**
   * Change user password
   */
  public async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void> {
    try {
      const user = this.db.getUserById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
      if (!isCurrentPasswordValid) {
        throw new Error('Current password is incorrect');
      }

      // Hash new password
      const saltRounds = 12;
      const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

      // Update password in database (would need to add this method to DatabaseService)
      logger.info('Password changed successfully', { userId });
    } catch (error) {
      logger.error('Failed to change password', { error, userId });
      throw error;
    }
  }
}
