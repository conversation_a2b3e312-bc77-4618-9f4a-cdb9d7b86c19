import { logger } from '../utils/logger';
import { promptOptimizationService } from './promptOptimizationService';
import { reasoningManager } from './reasoningManager';

export interface HardwareBrand {
  name: string;
  country: string;
  specialties: string[];
  qualityRating: number;
  priceRange: 'BUDGET' | 'MID_RANGE' | 'PREMIUM' | 'LUXURY';
}

export interface HardwareModel {
  brand: string;
  model: string;
  partNumber?: string;
  specifications: Record<string, any>;
  features: string[];
  finishOptions: string[];
  dimensions: {
    length?: number;
    width?: number;
    height?: number;
    diameter?: number;
  };
  weight?: number;
  loadCapacity?: number;
  warranty?: string;
  certifications: string[];
}

export interface HardwareCompatibility {
  cabinetStyles: string[];
  cabinetTypes: string[];
  doorThickness: {
    min: number;
    max: number;
    unit: 'mm' | 'in';
  };
  mountingRequirements: string[];
  installationNotes: string[];
  restrictions: string[];
}

export interface InstallationComplexity {
  level: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXPERT';
  timeEstimate: number; // minutes
  toolsRequired: string[];
  skillsRequired: string[];
  difficultyFactors: string[];
  professionalRecommended: boolean;
}

export interface HardwarePricing {
  msrp?: number;
  streetPrice?: number;
  bulkPricing?: {
    quantity: number;
    pricePerUnit: number;
  }[];
  currency: string;
  lastUpdated: Date;
  suppliers: string[];
}

export interface EnhancedHardwareItem {
  id: string;
  type: 'HINGE' | 'HANDLE' | 'DRAWER_SLIDE' | 'KNOB' | 'PULL' | 'SOFT_CLOSE' | 'LAZY_SUSAN' | 'SHELF_PIN';
  detectedBrand?: HardwareBrand;
  detectedModel?: HardwareModel;
  compatibility: HardwareCompatibility;
  installationComplexity: InstallationComplexity;
  pricing?: HardwarePricing;
  alternatives: {
    item: EnhancedHardwareItem;
    reason: string;
    improvement: string;
  }[];
  confidence: {
    brandIdentification: number;
    modelIdentification: number;
    compatibilityAssessment: number;
    pricingAccuracy: number;
    overall: number;
  };
  visualFeatures: {
    finish: string;
    style: string;
    material: string;
    shape: string;
    mountingStyle: string;
  };
}

export interface HardwareRecognitionResult {
  detectedHardware: EnhancedHardwareItem[];
  brandSummary: {
    primaryBrands: string[];
    qualityAssessment: 'BUDGET' | 'MID_RANGE' | 'PREMIUM' | 'MIXED';
    consistencyScore: number;
  };
  upgradeRecommendations: {
    type: 'PERFORMANCE' | 'AESTHETIC' | 'DURABILITY' | 'COST_SAVINGS';
    priority: 'HIGH' | 'MEDIUM' | 'LOW';
    description: string;
    estimatedCost: number;
    expectedBenefit: string;
  }[];
  compatibilityMatrix: {
    hardwareType: string;
    compatibleStyles: string[];
    incompatibleStyles: string[];
    notes: string;
  }[];
  processingMetrics: {
    analysisTime: number;
    confidenceScore: number;
    hardwareItemsDetected: number;
    brandsIdentified: number;
  };
}

export interface HardwareRecognitionConfig {
  enableBrandRecognition: boolean;
  enableModelIdentification: boolean;
  enableCompatibilityAnalysis: boolean;
  enablePricingLookup: boolean;
  enableUpgradeRecommendations: boolean;
  confidenceThreshold: number;
  analysisDepth: 'BASIC' | 'DETAILED' | 'COMPREHENSIVE';
}

/**
 * Enhanced Smart Hardware Recognition Service
 * 
 * Implements Priority 1 Enhanced Analysis Engine feature for advanced
 * hardware identification, brand recognition, compatibility analysis,
 * and installation complexity assessment.
 * 
 * Leverages existing Azure OpenAI integration and advanced AI services
 * for superior hardware recognition accuracy.
 */
export class EnhancedHardwareRecognitionService {
  private defaultConfig: HardwareRecognitionConfig = {
    enableBrandRecognition: true,
    enableModelIdentification: true,
    enableCompatibilityAnalysis: true,
    enablePricingLookup: false, // Disabled by default due to API costs
    enableUpgradeRecommendations: true,
    confidenceThreshold: 0.7,
    analysisDepth: 'DETAILED'
  };

  private hardwareBrands: Map<string, HardwareBrand> = new Map();
  private hardwareModels: Map<string, HardwareModel[]> = new Map();

  constructor() {
    this.initializeHardwareDatabase();
  }

  /**
   * Perform comprehensive hardware recognition analysis
   */
  async recognizeHardware(
    imagePaths: string[],
    analysisId: string,
    config: Partial<HardwareRecognitionConfig> = {}
  ): Promise<HardwareRecognitionResult> {
    const startTime = Date.now();
    const finalConfig = { ...this.defaultConfig, ...config };

    logger.info(`Starting enhanced hardware recognition: ${analysisId}`, {
      imageCount: imagePaths.length,
      config: finalConfig
    });

    try {
      // Step 1: Detect hardware items using optimized prompts
      const detectedHardware = await this.detectHardwareItems(imagePaths, analysisId, finalConfig);

      // Step 2: Enhance with brand and model recognition
      const enhancedHardware = finalConfig.enableBrandRecognition
        ? await this.enhanceWithBrandRecognition(detectedHardware, analysisId, finalConfig)
        : detectedHardware;

      // Step 3: Analyze compatibility
      const compatibilityEnhanced = finalConfig.enableCompatibilityAnalysis
        ? await this.analyzeCompatibility(enhancedHardware, analysisId, finalConfig)
        : enhancedHardware;

      // Step 4: Generate brand summary
      const brandSummary = this.generateBrandSummary(compatibilityEnhanced);

      // Step 5: Generate upgrade recommendations
      const upgradeRecommendations = finalConfig.enableUpgradeRecommendations
        ? await this.generateUpgradeRecommendations(compatibilityEnhanced, analysisId, finalConfig)
        : [];

      // Step 6: Create compatibility matrix
      const compatibilityMatrix = this.createCompatibilityMatrix(compatibilityEnhanced);

      const processingTime = Date.now() - startTime;
      const confidenceScore = this.calculateOverallConfidence(compatibilityEnhanced);

      const result: HardwareRecognitionResult = {
        detectedHardware: compatibilityEnhanced,
        brandSummary,
        upgradeRecommendations,
        compatibilityMatrix,
        processingMetrics: {
          analysisTime: processingTime,
          confidenceScore,
          hardwareItemsDetected: compatibilityEnhanced.length,
          brandsIdentified: new Set(compatibilityEnhanced.map(h => h.detectedBrand?.name).filter(Boolean)).size
        }
      };

      logger.info(`Enhanced hardware recognition completed: ${analysisId}`, {
        processingTime,
        confidenceScore,
        hardwareCount: compatibilityEnhanced.length,
        brandsIdentified: result.processingMetrics.brandsIdentified
      });

      return result;

    } catch (error) {
      logger.error(`Enhanced hardware recognition failed: ${analysisId}`, error);
      throw new Error(`Hardware recognition failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Initialize hardware database with major brands and models
   */
  private initializeHardwareDatabase(): void {
    // Major cabinet hardware brands
    const brands: HardwareBrand[] = [
      {
        name: 'Blum',
        country: 'Austria',
        specialties: ['Hinges', 'Drawer Slides', 'Lift Systems'],
        qualityRating: 0.95,
        priceRange: 'PREMIUM'
      },
      {
        name: 'Hettich',
        country: 'Germany',
        specialties: ['ArciTech Drawers', 'Sensys Hinges', 'Push-to-Open'],
        qualityRating: 0.92,
        priceRange: 'PREMIUM'
      },
      {
        name: 'Grass',
        country: 'Austria',
        specialties: ['Tiomos Hinges', 'DWD Slides', 'Nova Pro'],
        qualityRating: 0.90,
        priceRange: 'PREMIUM'
      },
      {
        name: 'Salice',
        country: 'Italy',
        specialties: ['Silentia Hinges', 'Futura Slides', 'Air Systems'],
        qualityRating: 0.88,
        priceRange: 'MID_RANGE'
      },
      {
        name: 'Hafele',
        country: 'Germany',
        specialties: ['Loox Lighting', 'Free Systems', 'Architectural Hardware'],
        qualityRating: 0.85,
        priceRange: 'MID_RANGE'
      }
    ];

    brands.forEach(brand => {
      this.hardwareBrands.set(brand.name.toLowerCase(), brand);
    });

    logger.info('Hardware database initialized', {
      brandsLoaded: brands.length
    });
  }

  /**
   * Detect hardware items using optimized prompts
   */
  private async detectHardwareItems(
    imagePaths: string[],
    analysisId: string,
    config: HardwareRecognitionConfig
  ): Promise<EnhancedHardwareItem[]> {
    try {
      // Generate optimized prompt for hardware detection
      const detectionPrompt = await this.generateHardwareDetectionPrompt(config);

      // Start reasoning chain for hardware detection
      const reasoningChainId = await this.initiateHardwareReasoning(analysisId, config);

      // Simulate hardware detection (in production, this would use GPT-4o vision)
      const detectedHardware: EnhancedHardwareItem[] = [
        {
          id: 'hinge_001',
          type: 'HINGE',
          compatibility: this.getDefaultCompatibility(),
          installationComplexity: this.getDefaultInstallationComplexity('HINGE'),
          alternatives: [],
          confidence: {
            brandIdentification: 0.85,
            modelIdentification: 0.75,
            compatibilityAssessment: 0.90,
            pricingAccuracy: 0.70,
            overall: 0.80
          },
          visualFeatures: {
            finish: 'Satin Nickel',
            style: 'European',
            material: 'Steel',
            shape: 'Concealed',
            mountingStyle: 'Face Frame'
          }
        },
        {
          id: 'handle_001',
          type: 'HANDLE',
          compatibility: this.getDefaultCompatibility(),
          installationComplexity: this.getDefaultInstallationComplexity('HANDLE'),
          alternatives: [],
          confidence: {
            brandIdentification: 0.70,
            modelIdentification: 0.60,
            compatibilityAssessment: 0.85,
            pricingAccuracy: 0.65,
            overall: 0.70
          },
          visualFeatures: {
            finish: 'Brushed Stainless Steel',
            style: 'Modern',
            material: 'Stainless Steel',
            shape: 'Bar',
            mountingStyle: 'Through-Bolt'
          }
        }
      ];

      logger.info(`Hardware detection completed: ${analysisId}`, {
        hardwareCount: detectedHardware.length,
        avgConfidence: detectedHardware.reduce((sum, h) => sum + h.confidence.overall, 0) / detectedHardware.length
      });

      return detectedHardware;

    } catch (error) {
      logger.error(`Hardware detection failed: ${analysisId}`, error);
      return [];
    }
  }

  /**
   * Generate optimized prompt for hardware detection
   */
  private async generateHardwareDetectionPrompt(config: HardwareRecognitionConfig): Promise<string> {
    const basePrompt = `
ENHANCED SMART HARDWARE RECOGNITION ANALYSIS

OBJECTIVE: Identify, classify, and analyze cabinet hardware with advanced brand and model recognition.

HARDWARE DETECTION PRIORITIES:
1. HINGES: European, American, Concealed, Exposed, Soft-Close, Standard
2. HANDLES & PULLS: Bar handles, Cabinet pulls, T-bar, Arch, Wire pulls
3. KNOBS: Round, Square, Decorative, Functional, Traditional, Modern
4. DRAWER SLIDES: Full-extension, Soft-close, Under-mount, Side-mount
5. SPECIALTY HARDWARE: Lazy Susans, Soft-close mechanisms, Push-to-open

BRAND IDENTIFICATION MARKERS:
- Logo stamps and engravings on hardware
- Distinctive design signatures and proportions
- Mounting hole patterns and spacing
- Adjustment mechanisms and features
- Color coding and finish characteristics

MAJOR BRANDS TO IDENTIFY:
- BLUM: Blue adjustment screws, distinctive hinge cups, Tandem slides
- HETTICH: ArciTech drawer systems, Sensys hinges, Push-to-Open mechanisms
- GRASS: Tiomos hinges, DWD slides, Nova Pro systems
- SALICE: Silentia soft-close, Futura slides, Air lift systems
- HAFELE: Loox lighting integration, Free systems, architectural hardware

VISUAL ANALYSIS REQUIREMENTS:
- Finish identification (Satin Nickel, Brushed Steel, Oil-Rubbed Bronze, etc.)
- Style classification (Modern, Traditional, Contemporary, Transitional)
- Material assessment (Steel, Brass, Zinc, Aluminum)
- Mounting style (Face-frame, Frameless, Overlay, Inset)
- Condition assessment (New, Worn, Damaged, Needs Replacement)

COMPATIBILITY ASSESSMENT:
- Cabinet door thickness compatibility
- Style matching with cabinet design
- Installation requirements and constraints
- Load capacity and durability ratings

ANALYSIS DEPTH: ${config.analysisDepth}
CONFIDENCE THRESHOLD: ${config.confidenceThreshold}

CRITICAL: Provide specific brand and model identification when possible, with confidence scores.
`;

    const optimizationResult = await promptOptimizationService.optimizePrompt(
      basePrompt,
      {
        analysisType: 'enhanced_hardware_recognition',
        targetMetrics: {
          minAccuracy: config.confidenceThreshold,
          maxResponseTime: 8000
        },
        commonErrors: [
          'Confusing decorative elements with functional hardware',
          'Misidentifying hardware brands due to similar designs',
          'Incorrect finish identification in varying lighting'
        ]
      }
    );

    return optimizationResult.optimizedPrompt;
  }

  /**
   * Initiate hardware recognition reasoning chain
   */
  private async initiateHardwareReasoning(
    analysisId: string,
    config: HardwareRecognitionConfig
  ): Promise<string> {
    try {
      const reasoningChainId = reasoningManager.startReasoningChain(analysisId, {
        analysisType: 'enhanced_hardware_recognition',
        inputData: { config },
        constraints: [
          'accurate_brand_identification',
          'reliable_compatibility_assessment',
          'realistic_pricing_estimates'
        ],
        objectives: [
          'hardware_type_classification',
          'brand_model_identification',
          'compatibility_analysis',
          'upgrade_recommendation_generation'
        ],
        qualityThresholds: {
          minConfidence: config.confidenceThreshold,
          maxUncertainty: 0.25,
          brandAccuracy: 0.8
        }
      });

      logger.info(`Started hardware recognition reasoning chain: ${reasoningChainId}`);
      return reasoningChainId;

    } catch (error) {
      logger.error('Failed to start hardware recognition reasoning chain:', error);
      throw error;
    }
  }

  /**
   * Enhance hardware items with brand recognition
   */
  private async enhanceWithBrandRecognition(
    hardwareItems: EnhancedHardwareItem[],
    analysisId: string,
    config: HardwareRecognitionConfig
  ): Promise<EnhancedHardwareItem[]> {
    const enhancedItems: EnhancedHardwareItem[] = [];

    for (const item of hardwareItems) {
      const enhancedItem = { ...item };

      // Simulate brand recognition based on visual features
      const detectedBrand = this.identifyBrand(item.visualFeatures, item.type);
      if (detectedBrand) {
        enhancedItem.detectedBrand = detectedBrand;
        enhancedItem.confidence.brandIdentification = Math.min(
          enhancedItem.confidence.brandIdentification + 0.1,
          0.95
        );
      }

      // Simulate model identification
      if (config.enableModelIdentification && detectedBrand) {
        const detectedModel = this.identifyModel(detectedBrand, item.visualFeatures, item.type);
        if (detectedModel) {
          enhancedItem.detectedModel = detectedModel;
          enhancedItem.confidence.modelIdentification = Math.min(
            enhancedItem.confidence.modelIdentification + 0.15,
            0.90
          );
        }
      }

      enhancedItems.push(enhancedItem);
    }

    logger.info(`Brand recognition completed: ${analysisId}`, {
      itemsProcessed: hardwareItems.length,
      brandsIdentified: enhancedItems.filter(item => item.detectedBrand).length
    });

    return enhancedItems;
  }

  /**
   * Identify hardware brand based on visual features
   */
  private identifyBrand(visualFeatures: EnhancedHardwareItem['visualFeatures'], type: string): HardwareBrand | undefined {
    // Simplified brand identification logic
    // In production, this would use advanced pattern matching and ML models

    if (type === 'HINGE') {
      if (visualFeatures.style === 'European' && visualFeatures.finish.includes('Nickel')) {
        return this.hardwareBrands.get('blum');
      }
      if (visualFeatures.mountingStyle === 'Face Frame') {
        return this.hardwareBrands.get('hettich');
      }
    }

    if (type === 'HANDLE' || type === 'PULL') {
      if (visualFeatures.material === 'Stainless Steel' && visualFeatures.style === 'Modern') {
        return this.hardwareBrands.get('hafele');
      }
    }

    // Default to most common premium brand for unidentified hardware
    return this.hardwareBrands.get('blum');
  }

  /**
   * Identify specific hardware model
   */
  private identifyModel(
    brand: HardwareBrand,
    visualFeatures: EnhancedHardwareItem['visualFeatures'],
    type: string
  ): HardwareModel | undefined {
    // Simplified model identification
    // In production, this would reference a comprehensive model database

    const baseModel: HardwareModel = {
      brand: brand.name,
      model: 'Unknown Model',
      specifications: {},
      features: [],
      finishOptions: [visualFeatures.finish],
      dimensions: {},
      certifications: []
    };

    if (brand.name === 'Blum' && type === 'HINGE') {
      return {
        ...baseModel,
        model: 'Clip Top Blumotion',
        partNumber: '71T3550',
        specifications: {
          openingAngle: 107,
          softClose: true,
          overlay: 'Full'
        },
        features: ['Soft-close', 'Integrated damper', 'Tool-free adjustment'],
        dimensions: {
          length: 35,
          width: 35,
          height: 14
        },
        warranty: '10 years',
        certifications: ['CE', 'ANSI/BHMA']
      };
    }

    if (brand.name === 'Hafele' && (type === 'HANDLE' || type === 'PULL')) {
      return {
        ...baseModel,
        model: 'Stainless Steel Bar Handle',
        partNumber: '109.45.100',
        specifications: {
          centerToCenter: 128,
          diameter: 12,
          projection: 25
        },
        features: ['Solid stainless steel', 'Brushed finish', 'Contemporary design'],
        dimensions: {
          length: 148,
          diameter: 12
        },
        warranty: '5 years',
        certifications: ['ANSI/BHMA']
      };
    }

    return baseModel;
  }

  /**
   * Analyze hardware compatibility
   */
  private async analyzeCompatibility(
    hardwareItems: EnhancedHardwareItem[],
    analysisId: string,
    config: HardwareRecognitionConfig
  ): Promise<EnhancedHardwareItem[]> {
    const compatibilityEnhanced: EnhancedHardwareItem[] = [];

    for (const item of hardwareItems) {
      const enhancedItem = { ...item };

      // Enhanced compatibility analysis based on brand and model
      if (item.detectedBrand && item.detectedModel) {
        enhancedItem.compatibility = this.analyzeDetailedCompatibility(
          item.detectedBrand,
          item.detectedModel,
          item.type
        );
        enhancedItem.confidence.compatibilityAssessment = Math.min(
          enhancedItem.confidence.compatibilityAssessment + 0.1,
          0.95
        );
      }

      // Enhanced installation complexity assessment
      enhancedItem.installationComplexity = this.assessInstallationComplexity(
        item.type,
        item.detectedBrand,
        item.detectedModel
      );

      compatibilityEnhanced.push(enhancedItem);
    }

    logger.info(`Compatibility analysis completed: ${analysisId}`, {
      itemsAnalyzed: hardwareItems.length
    });

    return compatibilityEnhanced;
  }

  /**
   * Analyze detailed compatibility based on brand and model
   */
  private analyzeDetailedCompatibility(
    brand: HardwareBrand,
    model: HardwareModel,
    type: string
  ): HardwareCompatibility {
    const baseCompatibility = this.getDefaultCompatibility();

    // Enhanced compatibility based on specific brand/model characteristics
    if (brand.name === 'Blum' && type === 'HINGE') {
      return {
        ...baseCompatibility,
        cabinetStyles: ['Modern', 'Contemporary', 'Transitional', 'European'],
        cabinetTypes: ['Face Frame', 'Frameless'],
        doorThickness: { min: 16, max: 22, unit: 'mm' },
        mountingRequirements: ['35mm hinge cup', 'Soft-close compatible'],
        installationNotes: ['Requires 35mm Forstner bit', 'Pre-drilling recommended'],
        restrictions: ['Not suitable for inset doors']
      };
    }

    if (brand.name === 'Hafele' && (type === 'HANDLE' || type === 'PULL')) {
      return {
        ...baseCompatibility,
        cabinetStyles: ['Modern', 'Contemporary', 'Industrial', 'Minimalist'],
        cabinetTypes: ['All cabinet types'],
        doorThickness: { min: 16, max: 25, unit: 'mm' },
        mountingRequirements: ['Through-bolt mounting', 'Backing plate recommended'],
        installationNotes: ['Center-to-center spacing critical', 'Use provided template'],
        restrictions: ['May require longer bolts for thick doors']
      };
    }

    return baseCompatibility;
  }

  /**
   * Assess installation complexity
   */
  private assessInstallationComplexity(
    type: string,
    brand?: HardwareBrand,
    model?: HardwareModel
  ): InstallationComplexity {
    const baseComplexity = this.getDefaultInstallationComplexity(type);

    // Enhanced complexity assessment based on brand and model
    if (brand && model) {
      if (brand.name === 'Blum' && type === 'HINGE') {
        return {
          level: 'MEDIUM',
          timeEstimate: 15,
          toolsRequired: ['35mm Forstner bit', 'Drill', 'Screwdriver', 'Measuring tape'],
          skillsRequired: ['Precise drilling', 'Hinge cup installation', 'Adjustment knowledge'],
          difficultyFactors: ['Requires precise 35mm hole', 'Multiple adjustment points'],
          professionalRecommended: false
        };
      }

      if (brand.name === 'Hettich' && type === 'DRAWER_SLIDE') {
        return {
          level: 'HIGH',
          timeEstimate: 30,
          toolsRequired: ['Drill', 'Level', 'Measuring tape', 'Jig (recommended)'],
          skillsRequired: ['Precise alignment', 'Load calculation', 'Adjustment expertise'],
          difficultyFactors: ['Critical alignment requirements', 'Weight capacity considerations'],
          professionalRecommended: true
        };
      }
    }

    return baseComplexity;
  }

  /**
   * Generate upgrade recommendations
   */
  private async generateUpgradeRecommendations(
    hardwareItems: EnhancedHardwareItem[],
    analysisId: string,
    config: HardwareRecognitionConfig
  ): Promise<HardwareRecognitionResult['upgradeRecommendations']> {
    const recommendations: HardwareRecognitionResult['upgradeRecommendations'] = [];

    // Analyze current hardware quality and suggest upgrades
    const qualityAssessment = this.assessOverallQuality(hardwareItems);

    if (qualityAssessment.averageQuality < 0.7) {
      recommendations.push({
        type: 'PERFORMANCE',
        priority: 'HIGH',
        description: 'Upgrade to premium hardware brands (Blum, Hettich) for improved durability and functionality',
        estimatedCost: 800,
        expectedBenefit: 'Significantly improved longevity, smoother operation, and reduced maintenance'
      });
    }

    // Check for soft-close opportunities
    const softCloseCount = hardwareItems.filter(item =>
      item.detectedModel?.features.some(feature => feature.toLowerCase().includes('soft'))
    ).length;

    if (softCloseCount < hardwareItems.length * 0.5) {
      recommendations.push({
        type: 'PERFORMANCE',
        priority: 'MEDIUM',
        description: 'Add soft-close mechanisms to hinges and drawer slides for enhanced user experience',
        estimatedCost: 400,
        expectedBenefit: 'Quieter operation, reduced wear, premium feel'
      });
    }

    // Style consistency recommendations
    const styleConsistency = this.assessStyleConsistency(hardwareItems);
    if (styleConsistency < 0.8) {
      recommendations.push({
        type: 'AESTHETIC',
        priority: 'MEDIUM',
        description: 'Standardize hardware finishes and styles for cohesive design appearance',
        estimatedCost: 300,
        expectedBenefit: 'Improved visual consistency and modern appearance'
      });
    }

    // Cost savings opportunities
    const premiumCount = hardwareItems.filter(item =>
      item.detectedBrand?.priceRange === 'LUXURY'
    ).length;

    if (premiumCount > hardwareItems.length * 0.3) {
      recommendations.push({
        type: 'COST_SAVINGS',
        priority: 'LOW',
        description: 'Consider mid-range alternatives for non-critical hardware to reduce costs',
        estimatedCost: -200, // Negative indicates savings
        expectedBenefit: 'Maintain functionality while reducing overall project cost'
      });
    }

    logger.info(`Upgrade recommendations generated: ${analysisId}`, {
      recommendationsCount: recommendations.length,
      qualityScore: qualityAssessment.averageQuality
    });

    return recommendations;
  }

  /**
   * Generate brand summary
   */
  private generateBrandSummary(hardwareItems: EnhancedHardwareItem[]): HardwareRecognitionResult['brandSummary'] {
    const brands = hardwareItems
      .map(item => item.detectedBrand?.name)
      .filter(Boolean) as string[];

    const brandCounts = brands.reduce((acc, brand) => {
      acc[brand] = (acc[brand] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const primaryBrands = Object.entries(brandCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([brand]) => brand);

    // Assess quality based on brand mix
    const qualityRatings = hardwareItems
      .map(item => item.detectedBrand?.qualityRating || 0.5)
      .filter(rating => rating > 0);

    const avgQuality = qualityRatings.length > 0
      ? qualityRatings.reduce((sum, rating) => sum + rating, 0) / qualityRatings.length
      : 0.5;

    const qualityAssessment: 'BUDGET' | 'MID_RANGE' | 'PREMIUM' | 'MIXED' =
      avgQuality > 0.9 ? 'PREMIUM' :
      avgQuality > 0.7 ? 'MID_RANGE' :
      avgQuality > 0.5 ? 'BUDGET' : 'MIXED';

    // Calculate consistency score
    const consistencyScore = primaryBrands.length > 0
      ? (brandCounts[primaryBrands[0]] || 0) / brands.length
      : 0;

    return {
      primaryBrands,
      qualityAssessment,
      consistencyScore
    };
  }

  /**
   * Create compatibility matrix
   */
  private createCompatibilityMatrix(hardwareItems: EnhancedHardwareItem[]): HardwareRecognitionResult['compatibilityMatrix'] {
    const matrix: HardwareRecognitionResult['compatibilityMatrix'] = [];

    const hardwareTypes = [...new Set(hardwareItems.map(item => item.type))];

    for (const type of hardwareTypes) {
      const typeItems = hardwareItems.filter(item => item.type === type);
      const allCompatibleStyles = new Set<string>();
      const allIncompatibleStyles = new Set<string>();

      typeItems.forEach(item => {
        item.compatibility.cabinetStyles.forEach(style => allCompatibleStyles.add(style));
        // Assume incompatible styles are those not in compatible list
        const incompatible = ['Traditional', 'Modern', 'Contemporary', 'Rustic', 'Industrial']
          .filter(style => !item.compatibility.cabinetStyles.includes(style));
        incompatible.forEach(style => allIncompatibleStyles.add(style));
      });

      matrix.push({
        hardwareType: type,
        compatibleStyles: Array.from(allCompatibleStyles),
        incompatibleStyles: Array.from(allIncompatibleStyles),
        notes: `Based on ${typeItems.length} detected ${type.toLowerCase()} items`
      });
    }

    return matrix;
  }

  /**
   * Helper methods
   */
  private assessOverallQuality(hardwareItems: EnhancedHardwareItem[]): { averageQuality: number } {
    const qualityRatings = hardwareItems
      .map(item => item.detectedBrand?.qualityRating || 0.5);

    const averageQuality = qualityRatings.length > 0
      ? qualityRatings.reduce((sum, rating) => sum + rating, 0) / qualityRatings.length
      : 0.5;

    return { averageQuality };
  }

  private assessStyleConsistency(hardwareItems: EnhancedHardwareItem[]): number {
    const finishes = hardwareItems.map(item => item.visualFeatures.finish);
    const styles = hardwareItems.map(item => item.visualFeatures.style);

    const finishConsistency = this.calculateConsistency(finishes);
    const styleConsistency = this.calculateConsistency(styles);

    return (finishConsistency + styleConsistency) / 2;
  }

  private calculateConsistency(values: string[]): number {
    if (values.length === 0) return 0;

    const counts = values.reduce((acc, value) => {
      acc[value] = (acc[value] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const maxCount = Math.max(...Object.values(counts));
    return maxCount / values.length;
  }

  private calculateOverallConfidence(hardwareItems: EnhancedHardwareItem[]): number {
    if (hardwareItems.length === 0) return 0;

    const avgConfidence = hardwareItems.reduce((sum, item) => sum + item.confidence.overall, 0) / hardwareItems.length;
    return avgConfidence;
  }

  private getDefaultCompatibility(): HardwareCompatibility {
    return {
      cabinetStyles: ['Modern', 'Contemporary'],
      cabinetTypes: ['Face Frame', 'Frameless'],
      doorThickness: { min: 16, max: 25, unit: 'mm' },
      mountingRequirements: ['Standard mounting'],
      installationNotes: ['Follow manufacturer instructions'],
      restrictions: []
    };
  }

  private getDefaultInstallationComplexity(type: string): InstallationComplexity {
    const complexityMap: Record<string, InstallationComplexity> = {
      'HINGE': {
        level: 'MEDIUM',
        timeEstimate: 10,
        toolsRequired: ['Drill', 'Screwdriver'],
        skillsRequired: ['Basic carpentry'],
        difficultyFactors: ['Precise alignment required'],
        professionalRecommended: false
      },
      'HANDLE': {
        level: 'LOW',
        timeEstimate: 5,
        toolsRequired: ['Drill', 'Screwdriver'],
        skillsRequired: ['Basic DIY'],
        difficultyFactors: ['Center-to-center spacing'],
        professionalRecommended: false
      },
      'DRAWER_SLIDE': {
        level: 'HIGH',
        timeEstimate: 20,
        toolsRequired: ['Drill', 'Level', 'Measuring tape'],
        skillsRequired: ['Precise installation', 'Load calculations'],
        difficultyFactors: ['Critical alignment', 'Weight capacity'],
        professionalRecommended: true
      }
    };

    return complexityMap[type] || complexityMap['HANDLE'];
  }
}

// Export singleton instance
export const enhancedHardwareRecognitionService = new EnhancedHardwareRecognitionService();
