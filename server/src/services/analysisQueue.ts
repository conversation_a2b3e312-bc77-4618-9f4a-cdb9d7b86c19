import { v4 as uuidv4 } from 'uuid';
import { createModuleLogger } from '@/utils/logger';
import { SocketManager, AnalysisProgress } from './socketManager';
import { OpenAIService, AnalysisConfig } from './openaiService';
import { promptService } from './promptService';
import { pdfService, ProcessedImage } from './pdfService';
import { cabinetReconstructionService } from './cabinetReconstructionService';

const logger = createModuleLogger('AnalysisQueue');

export interface AnalysisJob {
  id: string;
  filePath: string;
  originalName: string;
  config: AnalysisConfig;
  status: 'QUEUED' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  progress: number;
  currentStep: string;
  results?: any;
  error?: string;
  processedImages?: ProcessedImage[];
}

export class AnalysisQueue {
  private queue: AnalysisJob[] = [];
  private processing: Map<string, AnalysisJob> = new Map();
  private completed: Map<string, AnalysisJob> = new Map();
  private socketManager: SocketManager;
  private openaiService: OpenAIService;
  private maxConcurrent: number;
  private isProcessing = false;

  constructor(socketManager: SocketManager) {
    this.socketManager = socketManager;
    this.openaiService = new OpenAIService();
    this.maxConcurrent = parseInt(process.env.MAX_CONCURRENT_ANALYSES || '3');
    
    logger.info('AnalysisQueue initialized', { maxConcurrent: this.maxConcurrent });
    
    // Start processing loop
    this.startProcessingLoop();
  }

  /**
   * Add new analysis job to queue
   */
  async addJob(filePath: string, originalName: string, config: AnalysisConfig): Promise<string> {
    const jobId = uuidv4();
    
    const job: AnalysisJob = {
      id: jobId,
      filePath,
      originalName,
      config,
      status: 'QUEUED',
      createdAt: new Date(),
      progress: 0,
      currentStep: 'queued'
    };

    this.queue.push(job);
    
    logger.info(`Job added to queue: ${jobId}`, {
      originalName,
      queueLength: this.queue.length,
      config
    });

    // Notify clients about queue status
    this.socketManager.sendQueueStatus(this.queue.length, this.processing.size);

    return jobId;
  }

  /**
   * Get job status
   */
  getJobStatus(jobId: string): AnalysisJob | null {
    // Check processing jobs first
    if (this.processing.has(jobId)) {
      return this.processing.get(jobId)!;
    }
    
    // Check completed jobs
    if (this.completed.has(jobId)) {
      return this.completed.get(jobId)!;
    }
    
    // Check queue
    return this.queue.find(job => job.id === jobId) || null;
  }

  /**
   * Start the processing loop
   */
  private startProcessingLoop(): void {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    
    const processLoop = async () => {
      while (this.isProcessing) {
        try {
          await this.processNextJob();
          await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay between checks
        } catch (error) {
          logger.error('Error in processing loop:', error);
          await new Promise(resolve => setTimeout(resolve, 5000)); // 5 second delay on error
        }
      }
    };

    processLoop();
    logger.info('Processing loop started');
  }

  /**
   * Process next job in queue
   */
  private async processNextJob(): Promise<void> {
    // Check if we can process more jobs
    if (this.processing.size >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }

    const job = this.queue.shift()!;
    this.processing.set(job.id, job);

    logger.info(`Starting job processing: ${job.id}`, {
      originalName: job.originalName,
      processingCount: this.processing.size
    });

    // Update job status
    job.status = 'PROCESSING';
    job.startedAt = new Date();
    job.currentStep = 'starting';
    job.progress = 5;

    // Send initial progress
    this.sendProgress(job, 'starting', 5, 'Analysis starting...');

    try {
      // Process the job
      const results = await this.executeAnalysis(job);
      
      // Job completed successfully
      job.status = 'COMPLETED';
      job.completedAt = new Date();
      job.progress = 100;
      job.currentStep = 'completed';
      job.results = results;

      this.socketManager.sendAnalysisComplete(job.id, results);
      
      logger.info(`Job completed: ${job.id}`, {
        processingTime: job.completedAt.getTime() - job.startedAt!.getTime(),
        resultKeys: Object.keys(results)
      });

    } catch (error) {
      // Job failed
      job.status = 'FAILED';
      job.completedAt = new Date();
      job.error = error instanceof Error ? error.message : 'Unknown error';

      this.socketManager.sendAnalysisError(job.id, job.error);
      
      logger.error(`Job failed: ${job.id}`, { error: job.error });
    } finally {
      // Move job from processing to completed
      this.processing.delete(job.id);
      this.completed.set(job.id, job);

      // Clean up processed images
      if (job.processedImages) {
        const imagePaths = job.processedImages.map(img => img.path);
        await pdfService.cleanupFiles(imagePaths).catch(err => 
          logger.warn('Failed to cleanup processed images:', err)
        );
      }

      // Update queue status
      this.socketManager.sendQueueStatus(this.queue.length, this.processing.size);
    }
  }

  /**
   * Execute the actual analysis
   */
  private async executeAnalysis(job: AnalysisJob): Promise<any> {
    // Check if this is enhanced analysis
    if (job.config.enhancedMode) {
      return this.executeEnhancedAnalysis(job);
    }

    // Regular analysis flow
    return this.executeRegularAnalysis(job);
  }

  /**
   * Execute enhanced analysis with all advanced AI services
   */
  private async executeEnhancedAnalysis(job: AnalysisJob): Promise<any> {
    // Step 1: File validation and preprocessing
    this.sendProgress(job, 'validation', 5, 'Validating file format...');
    pdfService.validateFile(job.filePath, job.originalName);

    // Step 2: Enhanced PDF processing (already done in route, use results)
    this.sendProgress(job, 'enhanced_pdf_processing', 15, 'Using enhanced PDF processing results...');

    // Use enhanced PDF results if available, otherwise process normally
    if (job.config.pdfProcessingResults) {
      logger.info('Using pre-processed enhanced PDF results');
    } else {
      job.processedImages = await pdfService.processFile(job.filePath, job.originalName, {
        quality: 95,
        maxWidth: 2048,
        maxHeight: 2048,
        format: 'png',
        dpi: 300
      });
    }

    // Step 3: Use enhanced style analysis prompt (Priority 1 Enhancement)
    this.sendProgress(job, 'enhanced_style_analysis', 25, 'Preparing enhanced style analysis prompt...');

    let analysisPrompt;
    if (job.config.optimizedPrompt) {
      // Extract the actual prompt string from the optimization result
      analysisPrompt = typeof job.config.optimizedPrompt === 'string'
        ? job.config.optimizedPrompt
        : job.config.optimizedPrompt.optimizedPrompt || job.config.optimizedPrompt.originalPrompt;

      logger.info('Using pre-optimized prompt from route');
    } else {
      // Generate enhanced style analysis prompt (Priority 1 Enhancement)
      const enhancedConfig = {
        ...job.config,
        integrateWithBase: true,
        commonErrors: [
          'Misidentifying modern cabinets as contemporary',
          'Confusing painted and laminate finishes',
          'Incorrect hardware style classification'
        ]
      };

      analysisPrompt = await promptService.getEnhancedStyleAnalysisPrompt(enhancedConfig);
      logger.info('Generated enhanced style analysis prompt', {
        promptLength: analysisPrompt.length,
        enhancedFeatures: ['style_classification', 'material_identification', 'hardware_recognition']
      });
    }

    // Step 4: Enhanced GPT-4o Vision Analysis with reasoning integration
    this.sendProgress(job, 'enhanced_analysis', 40, 'Performing enhanced AI analysis...');

    const imagePaths = job.processedImages?.map(img => img.path) || [];
    const primaryAnalysis = await this.openaiService.analyzeImages(
      imagePaths,
      analysisPrompt,
      job.config
    );

    // Step 5: Reasoning chain progression (if enabled)
    let reasoningResults = null;
    if (job.config.useReasoning && job.config.reasoningChainId) {
      this.sendProgress(job, 'reasoning_chain', 60, 'Executing reasoning chain...');

      try {
        // Progress the reasoning chain with analysis results
        const reasoningManager = require('./reasoningManager').reasoningManager;
        reasoningManager.progressChain(
          job.config.reasoningChainId,
          0,
          [primaryAnalysis.content],
          primaryAnalysis.confidence,
          'Primary analysis completed'
        );

        // Complete the reasoning chain
        reasoningResults = reasoningManager.completeChain(job.config.reasoningChainId);

        logger.info('Reasoning chain completed', {
          chainId: job.config.reasoningChainId,
          confidence: reasoningResults.confidence
        });
      } catch (error) {
        logger.warn('Reasoning chain execution failed:', error);
      }
    }

    // Step 6: A/B test result recording
    if (job.config.abTestVariant) {
      this.sendProgress(job, 'ab_test_recording', 75, 'Recording A/B test results...');

      try {
        const abTestManager = require('./abTestManager').abTestManager;
        await abTestManager.recordResult({
          variantId: job.config.abTestVariant,
          analysisId: job.id,
          metrics: {
            accuracy: primaryAnalysis.confidence,
            confidence: primaryAnalysis.confidence,
            responseTime: primaryAnalysis.processingTime
          },
          timestamp: new Date(),
          metadata: {
            analysisType: 'enhanced_kitchen_analysis',
            fileSize: job.processedImages?.length || 0,
            complexity: 'high'
          }
        });
      } catch (error) {
        logger.warn('A/B test result recording failed:', error);
      }
    }

    // Step 7: Enhanced result processing
    this.sendProgress(job, 'enhanced_result_processing', 85, 'Processing enhanced results...');

    const enhancedResults = await this.parseEnhancedAnalysisResults(
      primaryAnalysis,
      reasoningResults,
      job
    );

    // Step 8: Final validation and confidence scoring
    this.sendProgress(job, 'final_validation', 95, 'Finalizing enhanced analysis...');

    const finalResults = this.finalizeEnhancedResults(enhancedResults, job);

    return finalResults;
  }

  /**
   * Execute regular analysis (original flow)
   */
  private async executeRegularAnalysis(job: AnalysisJob): Promise<any> {
    // Step 1: File validation and preprocessing
    this.sendProgress(job, 'validation', 10, 'Validating file format...');

    pdfService.validateFile(job.filePath, job.originalName);

    // Step 2: Process file (PDF to images or image optimization)
    this.sendProgress(job, 'preprocessing', 20, 'Processing file and extracting images...');

    job.processedImages = await pdfService.processFile(job.filePath, job.originalName, {
      quality: 85,
      maxWidth: 2048,
      maxHeight: 2048,
      format: 'jpeg',
      dpi: 200
    });

    logger.info(`Processed ${job.processedImages.length} images for analysis`);

    // Step 3: Get analysis prompt
    this.sendProgress(job, 'prompt_preparation', 30, 'Preparing analysis prompt...');

    const analysisPrompt = promptService.getAnalysisPrompt(job.config);

    // Step 4: Primary GPT-4o Vision Analysis
    this.sendProgress(job, 'primary_analysis', 40, 'Analyzing design with GPT-4o Vision...');

    const imagePaths = job.processedImages.map(img => img.path);
    const primaryAnalysis = await this.openaiService.analyzeImages(
      imagePaths,
      analysisPrompt,
      job.config
    );

    // Step 5: Secondary analysis with reasoning (if enabled)
    let reasoningAnalysis = null;
    if (job.config.useReasoning) {
      this.sendProgress(job, 'reasoning_analysis', 70, 'Performing validation with GPT-4o-mini...');

      const validationPrompt = promptService.getValidationPrompt(primaryAnalysis.content, job.config);
      reasoningAnalysis = await this.openaiService.performReasoning(
        primaryAnalysis.content,
        validationPrompt,
        job.config
      );
    }

    // Step 6: Perform 3D reconstruction if enabled
    let reconstruction3D: any = undefined;
    if (job.config.enable3DReconstruction) {
      try {
        this.sendProgress(job, '3d_reconstruction', 75, 'Generating 3D cabinet models...');

        logger.info(`Starting 3D reconstruction: ${job.id}`);

        reconstruction3D = await cabinetReconstructionService.reconstructCabinets(
          imagePaths,
          job.id,
          {
            enableDepthEstimation: true,
            spatialResolution: job.config.spatialResolution || 'HIGH',
            includeHardwarePositioning: job.config.includeHardwarePositioning || false,
            optimizeForAccuracy: true,
            generateWireframe: false
          }
        );

        logger.info(`3D reconstruction completed: ${job.id}`, {
          cabinetCount: reconstruction3D.cabinets.length,
          confidence: reconstruction3D.confidence.overall,
          processingTime: reconstruction3D.reconstructionMetrics.reconstructionTime
        });
      } catch (error) {
        logger.warn(`3D reconstruction failed: ${job.id}`, error);
        // Continue without 3D reconstruction rather than failing the entire analysis
      }
    }

    // Step 7: Parse and structure results
    this.sendProgress(job, 'result_processing', 85, 'Processing and structuring results...');

    const structuredResults = await this.parseAnalysisResults(
      primaryAnalysis,
      reasoningAnalysis,
      job,
      reconstruction3D
    );

    // Step 7: Final validation and confidence scoring
    this.sendProgress(job, 'final_validation', 95, 'Finalizing analysis...');

    const finalResults = this.finalizeResults(structuredResults, job);

    return finalResults;
  }

  /**
   * Parse enhanced analysis results with advanced AI services integration
   */
  private async parseEnhancedAnalysisResults(
    primaryAnalysis: any,
    reasoningResults: any,
    job: AnalysisJob
  ): Promise<any> {
    try {
      // Parse primary analysis results
      let parsedResults;
      try {
        const jsonMatch = primaryAnalysis.content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          parsedResults = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('No JSON found in response');
        }
      } catch (parseError) {
        logger.warn('Failed to parse JSON from enhanced analysis, using fallback parsing');
        parsedResults = this.fallbackParsing(primaryAnalysis.content);
      }

      // Integrate reasoning results if available
      if (reasoningResults) {
        parsedResults.reasoning = {
          confidence: reasoningResults.confidence,
          qualityScore: reasoningResults.qualityScore,
          chainId: job.config.reasoningChainId,
          completedSteps: reasoningResults.completedSteps || 0
        };
      }

      // Add enhanced metadata
      return {
        ...parsedResults,
        enhanced: true,
        services: {
          promptOptimization: !!job.config.optimizedPrompt,
          reasoningChain: !!job.config.reasoningChainId,
          enhancedPdfProcessing: !!job.config.pdfProcessingResults,
          abTesting: !!job.config.abTestVariant
        },
        meta: {
          primaryAnalysis: {
            model: primaryAnalysis.model,
            usage: primaryAnalysis.usage,
            processingTime: primaryAnalysis.processingTime,
            confidence: primaryAnalysis.confidence
          },
          reasoningResults: reasoningResults ? {
            confidence: reasoningResults.confidence,
            qualityScore: reasoningResults.qualityScore,
            processingTime: reasoningResults.processingTime
          } : null,
          jobConfig: job.config,
          processedImages: job.processedImages?.length || 0
        }
      };

    } catch (error) {
      logger.error('Failed to parse enhanced analysis results:', error);
      throw new Error(`Enhanced result parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Finalize enhanced results with additional metadata
   */
  private finalizeEnhancedResults(results: any, job: AnalysisJob): any {
    const processingTime = job.startedAt ? Date.now() - job.startedAt.getTime() : 0;

    return {
      ...results,
      id: job.id,
      status: 'COMPLETED',
      timestamp: new Date().toISOString(),
      processing_time_ms: processingTime,
      enhanced_analysis: true,
      file_info: {
        original_name: job.originalName,
        processed_images: job.processedImages?.length || 0
      },
      advanced_services: {
        prompt_optimization: !!job.config.optimizedPrompt,
        reasoning_chain: !!job.config.reasoningChainId,
        enhanced_pdf_processing: !!job.config.pdfProcessingResults,
        ab_testing: !!job.config.abTestVariant
      }
    };
  }

  /**
   * Parse analysis results from OpenAI responses
   */
  private async parseAnalysisResults(
    primaryAnalysis: any,
    reasoningAnalysis: any,
    job: AnalysisJob,
    reconstruction3D?: any
  ): Promise<any> {
    try {
      // Try to parse JSON from primary analysis
      let parsedResults;
      try {
        // Look for JSON in the response
        const jsonMatch = primaryAnalysis.content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          parsedResults = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('No JSON found in response');
        }
      } catch (parseError) {
        logger.warn('Failed to parse JSON from primary analysis, using fallback parsing');
        parsedResults = this.fallbackParsing(primaryAnalysis.content);
      }

      // Enhance measurements with cabinet count data
      parsedResults = this.enhanceMeasurementsWithCabinetData(parsedResults);

      // Apply reasoning validation if available
      if (reasoningAnalysis) {
        parsedResults = this.applyReasoningValidation(parsedResults, reasoningAnalysis);
      }

      return {
        ...parsedResults,
        reconstruction3D: reconstruction3D,
        meta: {
          primaryAnalysis: {
            model: primaryAnalysis.model,
            usage: primaryAnalysis.usage,
            processingTime: primaryAnalysis.processingTime,
            confidence: primaryAnalysis.confidence
          },
          reasoningAnalysis: reasoningAnalysis ? {
            model: reasoningAnalysis.model,
            usage: reasoningAnalysis.usage,
            processingTime: reasoningAnalysis.processingTime,
            confidence: reasoningAnalysis.confidence
          } : null,
          reconstruction3D: reconstruction3D ? {
            confidence: reconstruction3D.confidence,
            cabinetCount: reconstruction3D.cabinets?.length || 0,
            processingTime: reconstruction3D.reconstructionMetrics?.reconstructionTime || 0
          } : null,
          jobConfig: job.config,
          processedImages: job.processedImages?.length || 0
        }
      };

    } catch (error) {
      logger.error('Failed to parse analysis results:', error);
      throw new Error(`Result parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Enhance measurements section with cabinet count data for frontend compatibility
   */
  private enhanceMeasurementsWithCabinetData(parsedResults: any): any {
    if (!parsedResults.cabinets) {
      return parsedResults;
    }

    const cabinets = parsedResults.cabinets;

    // Calculate cabinet counts by type
    const cabinetsByType: Record<string, number> = {
      'base_cabinets': cabinets.base_cabinets || 0,
      'wall_cabinets': cabinets.wall_cabinets || 0,
      'tall_cabinets': cabinets.tall_cabinets || 0,
      'pantry_cabinets': cabinets.pantry_cabinets || 0,
      'island_cabinets': cabinets.island_cabinets || 0
    };

    // Calculate total from individual counts (more reliable than trusting total_count)
    const calculatedTotal = Object.values(cabinetsByType).reduce((sum, count) => sum + count, 0);

    // Use the higher of calculated total or provided total_count
    const finalTotal = Math.max(calculatedTotal, cabinets.total_count || 0);

    // Enhance or create measurements section
    const enhancedMeasurements = {
      ...parsedResults.measurements,
      total_cabinets: finalTotal,
      totalCabinets: finalTotal, // Frontend compatibility
      cabinets_by_type: cabinetsByType,
      cabinetsByType: cabinetsByType // Frontend compatibility
    };

    logger.info('Enhanced measurements with cabinet data', {
      originalTotal: cabinets.total_count,
      calculatedTotal,
      finalTotal,
      cabinetsByType
    });

    return {
      ...parsedResults,
      measurements: enhancedMeasurements
    };
  }

  /**
   * Fallback parsing when JSON parsing fails
   */
  private fallbackParsing(content: string): any {
    // Basic fallback parsing - extract key information using regex
    const cabinetCountMatch = content.match(/total[_\s]*count[:\s]*(\d+)/i);
    const linearMetersMatch = content.match(/linear[_\s]*meters?[:\s]*(\d+\.?\d*)/i);
    const baseCabinetsMatch = content.match(/base[_\s]*cabinets?[:\s]*(\d+)/i);
    const wallCabinetsMatch = content.match(/wall[_\s]*cabinets?[:\s]*(\d+)/i);
    const tallCabinetsMatch = content.match(/tall[_\s]*cabinets?[:\s]*(\d+)/i);

    const totalCount = cabinetCountMatch && cabinetCountMatch[1] ? parseInt(cabinetCountMatch[1]) : 0;
    const baseCount = baseCabinetsMatch && baseCabinetsMatch[1] ? parseInt(baseCabinetsMatch[1]) : 0;
    const wallCount = wallCabinetsMatch && wallCabinetsMatch[1] ? parseInt(wallCabinetsMatch[1]) : 0;
    const tallCount = tallCabinetsMatch && tallCabinetsMatch[1] ? parseInt(tallCabinetsMatch[1]) : 0;

    return {
      analysis_type: 'kitchen_design_analysis',
      cabinets: {
        total_count: totalCount,
        base_cabinets: baseCount,
        wall_cabinets: wallCount,
        tall_cabinets: tallCount,
        pantry_cabinets: 0,
        island_cabinets: 0,
        details: []
      },
      hardware: {},
      measurements: {
        linear_meters: linearMetersMatch && linearMetersMatch[1] ? parseFloat(linearMetersMatch[1]) : 0,
        total_cabinets: totalCount,
        totalCabinets: totalCount,
        cabinets_by_type: {
          'base_cabinets': baseCount,
          'wall_cabinets': wallCount,
          'tall_cabinets': tallCount,
          'pantry_cabinets': 0,
          'island_cabinets': 0
        },
        cabinetsByType: {
          'base_cabinets': baseCount,
          'wall_cabinets': wallCount,
          'tall_cabinets': tallCount,
          'pantry_cabinets': 0,
          'island_cabinets': 0
        }
      },
      overall_confidence: 0.5,
      notes: 'Fallback parsing used - results may be incomplete',
      raw_content: content
    };
  }

  /**
   * Apply reasoning validation to results
   */
  private applyReasoningValidation(results: any, reasoningAnalysis: any): any {
    // Validate cabinet count consistency
    const cabinetValidation = this.validateCabinetCounts(results);

    // Extract validation insights from reasoning content
    const reasoningInsights = this.extractReasoningInsights(reasoningAnalysis.content);

    logger.info('Applied reasoning validation', {
      cabinetValidation,
      reasoningConfidence: reasoningAnalysis.confidence,
      hasReasoningContent: !!reasoningAnalysis.content
    });

    return {
      ...results,
      validation: {
        reasoning_content: reasoningAnalysis.content,
        validation_confidence: reasoningAnalysis.confidence,
        cabinet_count_validation: cabinetValidation,
        reasoning_insights: reasoningInsights
      }
    };
  }

  /**
   * Validate cabinet count consistency
   */
  private validateCabinetCounts(results: any): any {
    if (!results.cabinets || !results.measurements) {
      return { status: 'missing_data', confidence: 0.0 };
    }

    const cabinets = results.cabinets;
    const measurements = results.measurements;

    // Check if individual counts add up to total
    const individualSum = (cabinets.base_cabinets || 0) +
                         (cabinets.wall_cabinets || 0) +
                         (cabinets.tall_cabinets || 0) +
                         (cabinets.pantry_cabinets || 0) +
                         (cabinets.island_cabinets || 0);

    const totalCount = cabinets.total_count || 0;
    const measurementTotal = measurements.totalCabinets || measurements.total_cabinets || 0;

    const countsMatch = individualSum === totalCount && totalCount === measurementTotal;
    const confidence = countsMatch ? 0.95 : (Math.abs(individualSum - totalCount) <= 2 ? 0.7 : 0.3);

    return {
      status: countsMatch ? 'consistent' : 'inconsistent',
      confidence,
      individual_sum: individualSum,
      declared_total: totalCount,
      measurement_total: measurementTotal,
      discrepancy: Math.abs(individualSum - totalCount)
    };
  }

  /**
   * Extract insights from reasoning content
   */
  private extractReasoningInsights(content: string): any {
    if (!content) {
      return { status: 'no_content', insights: [] };
    }

    const insights = [];

    // Look for cabinet count mentions
    if (content.toLowerCase().includes('cabinet count')) {
      insights.push('cabinet_count_reviewed');
    }

    // Look for validation confirmations
    if (content.toLowerCase().includes('confirmed') || content.toLowerCase().includes('verified')) {
      insights.push('validation_confirmed');
    }

    // Look for discrepancy mentions
    if (content.toLowerCase().includes('discrepancy') || content.toLowerCase().includes('inconsistent')) {
      insights.push('discrepancy_noted');
    }

    return {
      status: insights.length > 0 ? 'insights_found' : 'no_insights',
      insights,
      content_length: content.length
    };
  }

  /**
   * Finalize results with additional metadata
   */
  private finalizeResults(results: any, job: AnalysisJob): any {
    const processingTime = job.startedAt ? Date.now() - job.startedAt.getTime() : 0;
    
    return {
      ...results,
      id: job.id,
      status: 'COMPLETED',
      timestamp: new Date().toISOString(),
      processing_time_ms: processingTime,
      file_info: {
        original_name: job.originalName,
        processed_images: job.processedImages?.length || 0
      }
    };
  }

  /**
   * Send progress update
   */
  private sendProgress(job: AnalysisJob, step: string, progress: number, message: string): void {
    job.currentStep = step;
    job.progress = progress;

    const progressUpdate: AnalysisProgress = {
      analysisId: job.id,
      step,
      progress,
      message,
      timestamp: new Date().toISOString()
    };

    this.socketManager.sendAnalysisProgress(progressUpdate);
  }

  /**
   * Get queue statistics
   */
  getQueueStats(): {
    queued: number;
    processing: number;
    completed: number;
    maxConcurrent: number;
  } {
    return {
      queued: this.queue.length,
      processing: this.processing.size,
      completed: this.completed.size,
      maxConcurrent: this.maxConcurrent
    };
  }

  /**
   * Shutdown the queue
   */
  shutdown(): void {
    logger.info('Shutting down AnalysisQueue');
    this.isProcessing = false;
  }
}
