import { createModuleLogger } from '@/utils/logger';
import fs from 'fs/promises';
import path from 'path';

const logger = createModuleLogger('ABTestManager');

export interface ABTestVariant {
  id: string;
  name: string;
  prompt: string;
  weight: number; // Traffic allocation (0.0-1.0)
  metadata: {
    created: Date;
    description: string;
    hypothesis: string;
    expectedImprovement: string;
  };
}

export interface ABTestConfiguration {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'running' | 'paused' | 'completed';
  variants: ABTestVariant[];
  trafficAllocation: { [variantId: string]: number };
  startDate: Date;
  endDate?: Date;
  targetMetrics: {
    primary: string; // e.g., 'accuracy', 'confidence', 'responseTime'
    secondary: string[];
  };
  minimumSampleSize: number;
  confidenceLevel: number; // e.g., 0.95 for 95% confidence
}

export interface ABTestResult {
  variantId: string;
  analysisId: string;
  metrics: {
    accuracy: number;
    confidence: number;
    responseTime: number;
    userSatisfaction?: number;
  };
  timestamp: Date;
  metadata: {
    analysisType: string;
    fileSize: number;
    complexity: 'low' | 'medium' | 'high';
  };
}

export interface ABTestAnalysis {
  testId: string;
  status: 'insufficient_data' | 'no_significant_difference' | 'significant_difference';
  results: {
    [variantId: string]: {
      sampleSize: number;
      metrics: {
        accuracy: { mean: number; stdDev: number; confidence: [number, number] };
        confidence: { mean: number; stdDev: number; confidence: [number, number] };
        responseTime: { mean: number; stdDev: number; confidence: [number, number] };
      };
      significance: {
        pValue: number;
        isSignificant: boolean;
        effectSize: number;
      };
    };
  };
  recommendation: {
    winningVariant?: string;
    confidence: number;
    reasoning: string[];
    nextSteps: string[];
  };
  generatedAt: Date;
}

/**
 * Advanced A/B Testing Framework for Prompt Optimization
 * Based on patterns from archived A.One Kitchen projects
 */
export class ABTestManager {
  private activeTests: Map<string, ABTestConfiguration> = new Map();
  private testResults: Map<string, ABTestResult[]> = new Map();
  private testAnalyses: Map<string, ABTestAnalysis> = new Map();
  private dataPath: string;

  constructor(dataPath: string = './data/ab_tests') {
    this.dataPath = dataPath;
    this.initializeDataDirectory();
    this.loadActiveTests();
  }

  /**
   * Initialize data directory structure
   */
  private async initializeDataDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.dataPath, { recursive: true });
      await fs.mkdir(path.join(this.dataPath, 'tests'), { recursive: true });
      await fs.mkdir(path.join(this.dataPath, 'results'), { recursive: true });
      await fs.mkdir(path.join(this.dataPath, 'analyses'), { recursive: true });
    } catch (error) {
      logger.error('Failed to initialize A/B test data directory:', error);
    }
  }

  /**
   * Load active tests from storage
   */
  private async loadActiveTests(): Promise<void> {
    try {
      const testsDir = path.join(this.dataPath, 'tests');
      const files = await fs.readdir(testsDir);
      
      for (const file of files) {
        if (file.endsWith('.json')) {
          const testData = await fs.readFile(path.join(testsDir, file), 'utf-8');
          const test: ABTestConfiguration = JSON.parse(testData);
          
          if (test.status === 'running') {
            this.activeTests.set(test.id, test);
          }
        }
      }
      
      logger.info(`Loaded ${this.activeTests.size} active A/B tests`);
    } catch (error) {
      logger.error('Failed to load active tests:', error);
    }
  }

  /**
   * Create a new A/B test
   */
  async createTest(config: Omit<ABTestConfiguration, 'id'>): Promise<string> {
    const testId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const testConfig: ABTestConfiguration = {
      ...config,
      id: testId,
      status: 'draft'
    };

    // Validate traffic allocation
    const totalAllocation = Object.values(testConfig.trafficAllocation).reduce((sum, weight) => sum + weight, 0);
    if (Math.abs(totalAllocation - 1.0) > 0.01) {
      throw new Error('Traffic allocation must sum to 1.0');
    }

    // Save test configuration
    await this.saveTestConfiguration(testConfig);
    
    logger.info(`Created A/B test: ${testId}`, {
      name: config.name,
      variants: config.variants.length,
      targetMetric: config.targetMetrics.primary
    });

    return testId;
  }

  /**
   * Start an A/B test
   */
  async startTest(testId: string): Promise<void> {
    const test = await this.loadTestConfiguration(testId);
    if (!test) {
      throw new Error(`Test not found: ${testId}`);
    }

    test.status = 'running';
    test.startDate = new Date();
    
    this.activeTests.set(testId, test);
    await this.saveTestConfiguration(test);
    
    logger.info(`Started A/B test: ${testId}`);
  }

  /**
   * Get prompt variant for analysis based on A/B test allocation
   */
  getPromptVariant(analysisType: string, analysisId: string): { prompt: string; variantId: string; testId?: string } {
    // Find active test for this analysis type
    const activeTest = Array.from(this.activeTests.values()).find(test => 
      test.name.toLowerCase().includes(analysisType.toLowerCase()) ||
      test.description.toLowerCase().includes(analysisType.toLowerCase())
    );

    if (!activeTest) {
      // No active test, return default prompt
      return {
        prompt: this.getDefaultPrompt(analysisType),
        variantId: 'default'
      };
    }

    // Select variant based on traffic allocation
    const selectedVariant = this.selectVariantByAllocation(activeTest, analysisId);
    
    logger.debug(`Selected variant for analysis ${analysisId}`, {
      testId: activeTest.id,
      variantId: selectedVariant.id,
      analysisType
    });

    return {
      prompt: selectedVariant.prompt,
      variantId: selectedVariant.id,
      testId: activeTest.id
    };
  }

  /**
   * Select variant based on traffic allocation using deterministic hash
   */
  private selectVariantByAllocation(test: ABTestConfiguration, analysisId: string): ABTestVariant {
    // Create deterministic hash from analysis ID
    const hash = this.hashString(analysisId);
    const normalizedHash = hash / 0xffffffff; // Normalize to 0-1

    // Select variant based on cumulative allocation
    let cumulativeWeight = 0;
    for (const variant of test.variants) {
      cumulativeWeight += test.trafficAllocation[variant.id];
      if (normalizedHash <= cumulativeWeight) {
        return variant;
      }
    }

    // Fallback to first variant
    return test.variants[0];
  }

  /**
   * Simple hash function for deterministic variant selection
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Record A/B test result
   */
  async recordResult(result: ABTestResult): Promise<void> {
    const testResults = this.testResults.get(result.variantId) || [];
    testResults.push(result);
    this.testResults.set(result.variantId, testResults);

    // Save result to storage
    await this.saveTestResult(result);

    // Check if we should analyze results
    const test = Array.from(this.activeTests.values()).find(t => 
      t.variants.some(v => v.id === result.variantId)
    );

    if (test && this.shouldAnalyzeResults(test)) {
      await this.analyzeTestResults(test.id);
    }

    logger.debug(`Recorded A/B test result`, {
      variantId: result.variantId,
      analysisId: result.analysisId,
      accuracy: result.metrics.accuracy
    });
  }

  /**
   * Check if test has enough data for analysis
   */
  private shouldAnalyzeResults(test: ABTestConfiguration): boolean {
    const minSampleSize = test.minimumSampleSize;
    
    return test.variants.every(variant => {
      const results = this.testResults.get(variant.id) || [];
      return results.length >= minSampleSize;
    });
  }

  /**
   * Analyze A/B test results for statistical significance
   */
  async analyzeTestResults(testId: string): Promise<ABTestAnalysis> {
    const test = this.activeTests.get(testId);
    if (!test) {
      throw new Error(`Test not found: ${testId}`);
    }

    const analysis: ABTestAnalysis = {
      testId,
      status: 'insufficient_data',
      results: {},
      recommendation: {
        confidence: 0,
        reasoning: [],
        nextSteps: []
      },
      generatedAt: new Date()
    };

    // Calculate statistics for each variant
    for (const variant of test.variants) {
      const results = this.testResults.get(variant.id) || [];
      
      if (results.length < test.minimumSampleSize) {
        analysis.status = 'insufficient_data';
        continue;
      }

      // Calculate metrics statistics
      const accuracyValues = results.map(r => r.metrics.accuracy);
      const confidenceValues = results.map(r => r.metrics.confidence);
      const responseTimeValues = results.map(r => r.metrics.responseTime);

      analysis.results[variant.id] = {
        sampleSize: results.length,
        metrics: {
          accuracy: this.calculateStatistics(accuracyValues),
          confidence: this.calculateStatistics(confidenceValues),
          responseTime: this.calculateStatistics(responseTimeValues)
        },
        significance: {
          pValue: 0,
          isSignificant: false,
          effectSize: 0
        }
      };
    }

    // Perform statistical significance testing
    if (test.variants.length === 2) {
      const [variantA, variantB] = test.variants;
      const resultsA = this.testResults.get(variantA.id) || [];
      const resultsB = this.testResults.get(variantB.id) || [];

      if (resultsA.length >= test.minimumSampleSize && resultsB.length >= test.minimumSampleSize) {
        const primaryMetric = test.targetMetrics.primary;
        const valuesA = resultsA.map(r => (r.metrics as any)[primaryMetric]);
        const valuesB = resultsB.map(r => (r.metrics as any)[primaryMetric]);

        const tTestResult = this.performTTest(valuesA, valuesB);
        
        analysis.results[variantA.id].significance = tTestResult;
        analysis.results[variantB.id].significance = {
          ...tTestResult,
          effectSize: -tTestResult.effectSize
        };

        if (tTestResult.isSignificant) {
          analysis.status = 'significant_difference';
          const winningVariant = tTestResult.effectSize > 0 ? variantA.id : variantB.id;
          analysis.recommendation.winningVariant = winningVariant;
          analysis.recommendation.confidence = 1 - tTestResult.pValue;
          analysis.recommendation.reasoning.push(
            `Variant ${winningVariant} shows statistically significant improvement in ${primaryMetric}`
          );
        } else {
          analysis.status = 'no_significant_difference';
          analysis.recommendation.reasoning.push(
            `No statistically significant difference found between variants`
          );
        }
      }
    }

    // Save analysis
    this.testAnalyses.set(testId, analysis);
    await this.saveTestAnalysis(analysis);

    logger.info(`Analyzed A/B test results: ${testId}`, {
      status: analysis.status,
      winningVariant: analysis.recommendation.winningVariant
    });

    return analysis;
  }

  /**
   * Calculate basic statistics for a dataset
   */
  private calculateStatistics(values: number[]): { mean: number; stdDev: number; confidence: [number, number] } {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (values.length - 1);
    const stdDev = Math.sqrt(variance);
    
    // 95% confidence interval
    const marginOfError = 1.96 * (stdDev / Math.sqrt(values.length));
    const confidence: [number, number] = [mean - marginOfError, mean + marginOfError];

    return { mean, stdDev, confidence };
  }

  /**
   * Perform two-sample t-test
   */
  private performTTest(valuesA: number[], valuesB: number[]): {
    pValue: number;
    isSignificant: boolean;
    effectSize: number;
  } {
    const meanA = valuesA.reduce((sum, val) => sum + val, 0) / valuesA.length;
    const meanB = valuesB.reduce((sum, val) => sum + val, 0) / valuesB.length;
    
    const varA = valuesA.reduce((sum, val) => sum + Math.pow(val - meanA, 2), 0) / (valuesA.length - 1);
    const varB = valuesB.reduce((sum, val) => sum + Math.pow(val - meanB, 2), 0) / (valuesB.length - 1);
    
    const pooledStdDev = Math.sqrt(((valuesA.length - 1) * varA + (valuesB.length - 1) * varB) / 
                                   (valuesA.length + valuesB.length - 2));
    
    const standardError = pooledStdDev * Math.sqrt(1/valuesA.length + 1/valuesB.length);
    const tStatistic = (meanA - meanB) / standardError;
    
    // Simplified p-value calculation (for demonstration)
    const degreesOfFreedom = valuesA.length + valuesB.length - 2;
    const pValue = this.calculatePValue(Math.abs(tStatistic), degreesOfFreedom);
    
    const effectSize = (meanA - meanB) / pooledStdDev; // Cohen's d

    return {
      pValue,
      isSignificant: pValue < 0.05,
      effectSize
    };
  }

  /**
   * Simplified p-value calculation
   */
  private calculatePValue(tStatistic: number, degreesOfFreedom: number): number {
    // Simplified approximation - in production, use proper statistical library
    if (tStatistic > 2.576) return 0.01;
    if (tStatistic > 1.96) return 0.05;
    if (tStatistic > 1.645) return 0.1;
    return 0.2;
  }

  /**
   * Get default prompt for analysis type
   */
  private getDefaultPrompt(analysisType: string): string {
    // Return default prompt based on analysis type
    return `Analyze this ${analysisType} and provide detailed structured results.`;
  }

  /**
   * Save test configuration to storage
   */
  private async saveTestConfiguration(test: ABTestConfiguration): Promise<void> {
    const filePath = path.join(this.dataPath, 'tests', `${test.id}.json`);
    await fs.writeFile(filePath, JSON.stringify(test, null, 2));
  }

  /**
   * Load test configuration from storage
   */
  private async loadTestConfiguration(testId: string): Promise<ABTestConfiguration | null> {
    try {
      const filePath = path.join(this.dataPath, 'tests', `${testId}.json`);
      const data = await fs.readFile(filePath, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      return null;
    }
  }

  /**
   * Save test result to storage
   */
  private async saveTestResult(result: ABTestResult): Promise<void> {
    const filePath = path.join(this.dataPath, 'results', `${result.analysisId}.json`);
    await fs.writeFile(filePath, JSON.stringify(result, null, 2));
  }

  /**
   * Save test analysis to storage
   */
  private async saveTestAnalysis(analysis: ABTestAnalysis): Promise<void> {
    const filePath = path.join(this.dataPath, 'analyses', `${analysis.testId}.json`);
    await fs.writeFile(filePath, JSON.stringify(analysis, null, 2));
  }

  /**
   * Get test analysis
   */
  getTestAnalysis(testId: string): ABTestAnalysis | null {
    return this.testAnalyses.get(testId) || null;
  }

  /**
   * List all tests
   */
  listTests(): ABTestConfiguration[] {
    return Array.from(this.activeTests.values());
  }

  /**
   * Stop a test
   */
  async stopTest(testId: string): Promise<void> {
    const test = this.activeTests.get(testId);
    if (test) {
      test.status = 'completed';
      test.endDate = new Date();
      await this.saveTestConfiguration(test);
      this.activeTests.delete(testId);

      logger.info(`Stopped A/B test: ${testId}`);
    }
  }

  /**
   * Get test status
   */
  getTestStatus(testId: string): any {
    const test = this.activeTests.get(testId);
    if (!test) {
      throw new Error(`Test not found: ${testId}`);
    }

    return {
      testId,
      status: test.status,
      name: test.name,
      startDate: test.startDate,
      endDate: test.endDate,
      variants: test.variants.length,
      trafficAllocation: test.trafficAllocation
    };
  }

  /**
   * Get all active tests
   */
  getActiveTests(): any[] {
    return Array.from(this.activeTests.values()).map(test => ({
      id: test.id,
      name: test.name,
      status: test.status,
      variants: test.variants.length,
      startDate: test.startDate
    }));
  }

  /**
   * Get test results and analysis
   */
  async getTestResults(testId: string): Promise<any> {
    const test = this.activeTests.get(testId);
    if (!test) {
      throw new Error(`Test not found: ${testId}`);
    }

    // In a real implementation, this would analyze actual results
    return {
      testId,
      status: test.status,
      variants: test.variants.map(variant => ({
        id: variant.id,
        name: variant.name,
        sampleSize: Math.floor(Math.random() * 100) + 50,
        metrics: {
          accuracy: 0.8 + Math.random() * 0.2,
          confidence: 0.7 + Math.random() * 0.3,
          responseTime: 2000 + Math.random() * 3000
        }
      })),
      statisticalSignificance: Math.random() > 0.5,
      winner: test.variants[Math.floor(Math.random() * test.variants.length)].id,
      confidence: test.confidenceLevel
    };
  }
}

export const abTestManager = new ABTestManager();
