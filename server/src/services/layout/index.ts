/**
 * Layout Optimization Services Module
 * 
 * Modular layout optimization service architecture providing:
 * - Workflow analysis and optimization
 * - Space utilization assessment
 * - Ergonomic evaluation and accessibility
 * - Traffic flow analysis
 * - Cost-benefit analysis
 * - Configuration management
 * 
 * This module replaces the monolithic Layout Optimization service with a clean,
 * maintainable, and testable architecture while preserving backward compatibility.
 */

// Core Services
export { LayoutAnalysisOrchestrator, layoutAnalysisOrchestrator } from './LayoutAnalysisOrchestrator';
export { LayoutConfigService, layoutConfigService } from './LayoutConfigService';
export { WorkflowAnalyzer, workflowAnalyzer } from './WorkflowAnalyzer';
export { SpaceUtilizationAnalyzer, spaceUtilizationAnalyzer } from './SpaceUtilizationAnalyzer';
export { ErgonomicAnalyzer, ergonomicAnalyzer } from './ErgonomicAnalyzer';
export { TrafficFlowAnalyzer, trafficFlowAnalyzer } from './TrafficFlowAnalyzer';
export { CostBenefitAnalyzer, costBenefitAnalyzer } from './CostBenefitAnalyzer';

// Main Service (Facade)
export { LayoutOptimizationService, layoutOptimizationService } from './LayoutOptimizationService';

// Types and Interfaces
export type {
  LayoutOptimizationConfig,
  LayoutOptimizationResult,
  WorkflowOptimization,
  SpaceUtilization,
  ErgonomicAssessment,
  TrafficFlowAnalysis,
  CostBenefitAnalysis,
  LayoutChange,
  SpaceOpportunity,
  ErgonomicRecommendation,
  FlowPath,
  CongestionPoint,
  ClearanceIssue,
  AnalysisContext,
  PromptContext,
  LayoutAnalyzer,
  ConfigValidationResult,
  ProcessingMetrics,
  LayoutAnalysisError,
  LayoutCacheKey,
  LayoutCacheEntry
} from './types';

// Default export for backward compatibility
export { layoutOptimizationService as default } from './LayoutOptimizationService';
