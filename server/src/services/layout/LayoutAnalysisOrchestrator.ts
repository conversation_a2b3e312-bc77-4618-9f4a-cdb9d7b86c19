import { createModuleLogger } from '@/utils/logger';
import { workflowAnalyzer } from './WorkflowAnalyzer';
import { spaceUtilizationAnalyzer } from './SpaceUtilizationAnalyzer';
import { ergonomicAnalyzer } from './ErgonomicAnalyzer';
import { trafficFlowAnalyzer } from './TrafficFlowAnalyzer';
import { costBenefitAnalyzer } from './CostBenefitAnalyzer';
import { layoutConfigService } from './LayoutConfigService';

const logger = createModuleLogger('LayoutAnalysisOrchestrator');

// Re-export types for backward compatibility
export {
  LayoutOptimizationConfig,
  LayoutOptimizationResult,
  WorkflowOptimization,
  SpaceUtilization,
  ErgonomicAssessment,
  TrafficFlowAnalysis,
  CostBenefitAnalysis,
  LayoutChange,
  SpaceOpportunity,
  ErgonomicRecommendation,
  FlowPath,
  CongestionPoint,
  ClearanceIssue
} from './types';

import {
  LayoutOptimizationConfig,
  LayoutOptimizationResult,
  WorkflowOptimization,
  SpaceUtilization,
  ErgonomicAssessment,
  TrafficFlowAnalysis,
  CostBenefitAnalysis
} from './types';

/**
 * Layout Analysis Orchestrator
 * 
 * Main coordination service for layout optimization analysis.
 * Orchestrates the execution of specialized analyzers and aggregates results.
 * 
 * Responsibilities:
 * - Coordinate analysis workflow
 * - Manage analyzer dependencies
 * - Aggregate and validate results
 * - Calculate overall confidence scores
 */
export class LayoutAnalysisOrchestrator {
  private defaultConfig: LayoutOptimizationConfig;

  constructor() {
    this.defaultConfig = layoutConfigService.getDefaultConfig();
  }

  /**
   * Get default configuration
   */
  getDefaultConfig(): LayoutOptimizationConfig {
    return layoutConfigService.getDefaultConfig();
  }

  /**
   * Perform comprehensive layout optimization analysis
   */
  async optimizeLayout(
    imagePaths: string[],
    analysisId: string,
    config?: Partial<LayoutOptimizationConfig>
  ): Promise<LayoutOptimizationResult> {
    const startTime = Date.now();
    const finalConfig = { ...this.defaultConfig, ...config };

    logger.info(`Starting layout optimization analysis: ${analysisId}`, {
      imageCount: imagePaths.length,
      config: this.sanitizeConfigForLogging(finalConfig)
    });

    try {
      // Validate configuration
      this.validateConfiguration(finalConfig);

      // Step 1: Workflow optimization analysis
      const workflowOptimization = await this.executeWorkflowAnalysis(
        imagePaths, 
        analysisId, 
        finalConfig
      );

      // Step 2: Space utilization analysis
      const spaceUtilization = await this.executeSpaceUtilizationAnalysis(
        imagePaths, 
        analysisId, 
        finalConfig
      );

      // Step 3: Ergonomic assessment
      const ergonomicAssessment = await this.executeErgonomicAnalysis(
        imagePaths, 
        analysisId, 
        finalConfig
      );

      // Step 4: Traffic flow analysis
      const trafficFlowAnalysis = await this.executeTrafficFlowAnalysis(
        imagePaths, 
        analysisId, 
        finalConfig
      );

      // Step 5: Cost-benefit analysis (depends on previous analyses)
      const costBenefitAnalysis = await this.executeCostBenefitAnalysis(
        workflowOptimization,
        spaceUtilization,
        ergonomicAssessment,
        trafficFlowAnalysis,
        analysisId,
        finalConfig
      );

      // Step 6: Calculate processing metrics
      const processingTime = Date.now() - startTime;
      const confidenceScore = this.calculateOverallConfidence(
        workflowOptimization,
        spaceUtilization,
        ergonomicAssessment,
        trafficFlowAnalysis,
        costBenefitAnalysis
      );

      const result: LayoutOptimizationResult = {
        workflowOptimization,
        spaceUtilization,
        ergonomicAssessment,
        trafficFlowAnalysis,
        costBenefitAnalysis,
        processingMetrics: {
          analysisTime: processingTime,
          confidenceScore,
          featuresAnalyzed: this.getAnalyzedFeatures(finalConfig)
        }
      };

      logger.info(`Layout optimization analysis completed: ${analysisId}`, {
        processingTime,
        confidenceScore,
        featuresAnalyzed: result.processingMetrics.featuresAnalyzed.length
      });

      return result;

    } catch (error) {
      logger.error(`Layout optimization analysis failed: ${analysisId}`, error);
      throw new Error(`Layout optimization analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute workflow analysis
   */
  private async executeWorkflowAnalysis(
    imagePaths: string[],
    analysisId: string,
    config: LayoutOptimizationConfig
  ): Promise<WorkflowOptimization> {
    if (!config.enableWorkflowAnalysis) {
      return workflowAnalyzer.getDefaultResult();
    }

    logger.info(`Executing workflow analysis: ${analysisId}`);
    return await workflowAnalyzer.analyzeWorkflow(imagePaths, analysisId, config);
  }

  /**
   * Execute space utilization analysis
   */
  private async executeSpaceUtilizationAnalysis(
    imagePaths: string[],
    analysisId: string,
    config: LayoutOptimizationConfig
  ): Promise<SpaceUtilization> {
    if (!config.enableSpaceUtilization) {
      return spaceUtilizationAnalyzer.getDefaultResult();
    }

    logger.info(`Executing space utilization analysis: ${analysisId}`);
    return await spaceUtilizationAnalyzer.analyzeSpaceUtilization(imagePaths, analysisId, config);
  }

  /**
   * Execute ergonomic analysis
   */
  private async executeErgonomicAnalysis(
    imagePaths: string[],
    analysisId: string,
    config: LayoutOptimizationConfig
  ): Promise<ErgonomicAssessment> {
    if (!config.enableErgonomicAssessment) {
      return ergonomicAnalyzer.getDefaultResult();
    }

    logger.info(`Executing ergonomic analysis: ${analysisId}`);
    return await ergonomicAnalyzer.performErgonomicAssessment(imagePaths, analysisId, config);
  }

  /**
   * Execute traffic flow analysis
   */
  private async executeTrafficFlowAnalysis(
    imagePaths: string[],
    analysisId: string,
    config: LayoutOptimizationConfig
  ): Promise<TrafficFlowAnalysis> {
    if (!config.enableTrafficFlowAnalysis) {
      return trafficFlowAnalyzer.getDefaultResult();
    }

    logger.info(`Executing traffic flow analysis: ${analysisId}`);
    return await trafficFlowAnalyzer.analyzeTrafficFlow(imagePaths, analysisId, config);
  }

  /**
   * Execute cost-benefit analysis
   */
  private async executeCostBenefitAnalysis(
    workflowOptimization: WorkflowOptimization,
    spaceUtilization: SpaceUtilization,
    ergonomicAssessment: ErgonomicAssessment,
    trafficFlowAnalysis: TrafficFlowAnalysis,
    analysisId: string,
    config: LayoutOptimizationConfig
  ): Promise<CostBenefitAnalysis> {
    if (!config.enableCostBenefitAnalysis) {
      return costBenefitAnalyzer.getDefaultResult();
    }

    logger.info(`Executing cost-benefit analysis: ${analysisId}`);
    return await costBenefitAnalyzer.performCostBenefitAnalysis(
      workflowOptimization,
      spaceUtilization,
      ergonomicAssessment,
      trafficFlowAnalysis,
      analysisId,
      config
    );
  }

  /**
   * Calculate overall confidence score
   */
  private calculateOverallConfidence(
    workflowOptimization: WorkflowOptimization,
    spaceUtilization: SpaceUtilization,
    ergonomicAssessment: ErgonomicAssessment,
    trafficFlowAnalysis: TrafficFlowAnalysis,
    costBenefitAnalysis: CostBenefitAnalysis
  ): number {
    const confidenceScores: number[] = [];

    // Collect confidence scores from each analysis
    if (workflowOptimization.confidence?.overall) {
      confidenceScores.push(workflowOptimization.confidence.overall);
    }

    // Space utilization doesn't have explicit confidence, estimate based on data completeness
    const spaceDataCompleteness = spaceUtilization.currentUtilization.totalSpace > 0 ? 0.85 : 0.3;
    confidenceScores.push(spaceDataCompleteness);

    // Ergonomic assessment confidence based on score completeness
    const ergonomicDataCompleteness = ergonomicAssessment.currentErgonomics.reachability > 0 ? 0.82 : 0.3;
    confidenceScores.push(ergonomicDataCompleteness);

    // Traffic flow confidence based on analysis completeness
    const trafficFlowCompleteness = trafficFlowAnalysis.currentFlow.overallRating > 0 ? 0.80 : 0.3;
    confidenceScores.push(trafficFlowCompleteness);

    // Cost-benefit confidence based on ROI calculation
    const costBenefitCompleteness = costBenefitAnalysis.roi.returnPercentage > 0 ? 0.78 : 0.3;
    confidenceScores.push(costBenefitCompleteness);

    // Calculate weighted average (workflow analysis has higher weight)
    const weights = [0.3, 0.2, 0.2, 0.15, 0.15]; // Workflow gets 30% weight
    let weightedSum = 0;
    let totalWeight = 0;

    confidenceScores.forEach((score, index) => {
      if (score > 0) {
        weightedSum += score * weights[index];
        totalWeight += weights[index];
      }
    });

    return totalWeight > 0 ? Math.round((weightedSum / totalWeight) * 100) / 100 : 0.5;
  }

  /**
   * Get analyzed features list
   */
  private getAnalyzedFeatures(config: LayoutOptimizationConfig): string[] {
    const features: string[] = [];

    if (config.enableWorkflowAnalysis) features.push('Workflow Optimization');
    if (config.enableSpaceUtilization) features.push('Space Utilization');
    if (config.enableErgonomicAssessment) features.push('Ergonomic Assessment');
    if (config.enableTrafficFlowAnalysis) features.push('Traffic Flow Analysis');
    if (config.enableCostBenefitAnalysis) features.push('Cost-Benefit Analysis');

    return features;
  }

  /**
   * Validate configuration
   */
  private validateConfiguration(config: LayoutOptimizationConfig): void {
    if (config.confidenceThreshold < 0 || config.confidenceThreshold > 1) {
      throw new Error('Confidence threshold must be between 0 and 1');
    }

    if (config.maxLayoutAlternatives < 1 || config.maxLayoutAlternatives > 10) {
      throw new Error('Max layout alternatives must be between 1 and 10');
    }

    if (!['BASIC', 'DETAILED', 'COMPREHENSIVE'].includes(config.optimizationLevel)) {
      throw new Error('Invalid optimization level');
    }
  }

  /**
   * Sanitize config for logging (remove sensitive data)
   */
  private sanitizeConfigForLogging(config: LayoutOptimizationConfig): Partial<LayoutOptimizationConfig> {
    // No sensitive data in layout config, return as-is
    return config;
  }
}

// Export singleton instance
export const layoutAnalysisOrchestrator = new LayoutAnalysisOrchestrator();
