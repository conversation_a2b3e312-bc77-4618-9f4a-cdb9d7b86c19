/**
 * Layout Optimization Types
 * 
 * Centralized type definitions for the layout optimization service architecture.
 * These types define the interfaces for all layout analysis components.
 */

// Configuration Interfaces
export interface LayoutOptimizationConfig {
  enableWorkflowAnalysis: boolean;
  enableSpaceUtilization: boolean;
  enableErgonomicAssessment: boolean;
  enableTrafficFlowAnalysis: boolean;
  enableCostBenefitAnalysis: boolean;
  optimizationLevel: 'BASIC' | 'DETAILED' | 'COMPREHENSIVE';
  confidenceThreshold: number;
  includeAccessibilityFeatures: boolean;
  maxLayoutAlternatives: number;
}

// Main Result Interface
export interface LayoutOptimizationResult {
  workflowOptimization: WorkflowOptimization;
  spaceUtilization: SpaceUtilization;
  ergonomicAssessment: ErgonomicAssessment;
  trafficFlowAnalysis: TrafficFlowAnalysis;
  costBenefitAnalysis: CostBenefitAnalysis;
  processingMetrics: {
    analysisTime: number;
    confidenceScore: number;
    featuresAnalyzed: string[];
  };
}

// Workflow Optimization Interfaces
export interface WorkflowOptimization {
  currentWorkflow: {
    efficiency: number;
    bottlenecks: string[];
    workTriangle: {
      perimeter: number;
      efficiency: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR';
      recommendations: string[];
    };
  };
  optimizedWorkflow: {
    suggestedChanges: LayoutChange[];
    expectedEfficiency: number;
    implementationComplexity: 'LOW' | 'MEDIUM' | 'HIGH';
    estimatedCost: number;
  };
  confidence: {
    workflowAnalysis: number;
    optimizationAccuracy: number;
    costEstimation: number;
    overall: number;
  };
}

// Space Utilization Interfaces
export interface SpaceUtilization {
  currentUtilization: {
    totalSpace: number;
    usedSpace: number;
    utilizationPercentage: number;
    wastedSpace: number;
    storageEfficiency: number;
  };
  optimizationOpportunities: {
    verticalSpace: SpaceOpportunity[];
    cornerUtilization: SpaceOpportunity[];
    cabinetConfiguration: SpaceOpportunity[];
    applianceLayout: SpaceOpportunity[];
  };
  recommendations: {
    priority: 'HIGH' | 'MEDIUM' | 'LOW';
    description: string;
    expectedGain: number;
    implementationCost: number;
    roi: number;
  }[];
}

// Ergonomic Assessment Interfaces
export interface ErgonomicAssessment {
  currentErgonomics: {
    reachability: number;
    heightOptimization: number;
    accessibilityScore: number;
    safetyRating: number;
  };
  improvements: {
    cabinetHeights: ErgonomicRecommendation[];
    handlePositions: ErgonomicRecommendation[];
    accessibilityFeatures: ErgonomicRecommendation[];
    safetyEnhancements: ErgonomicRecommendation[];
  };
  accessibilityCompliance: {
    adaCompliant: boolean;
    universalDesignScore: number;
    recommendations: string[];
  };
}

// Traffic Flow Analysis Interfaces
export interface TrafficFlowAnalysis {
  currentFlow: {
    primaryPaths: FlowPath[];
    congestionPoints: CongestionPoint[];
    clearanceIssues: ClearanceIssue[];
    overallRating: number;
  };
  optimizedFlow: {
    suggestedLayout: LayoutChange[];
    improvedPaths: FlowPath[];
    resolvedIssues: string[];
    expectedImprovement: number;
  };
}

// Cost-Benefit Analysis Interfaces
export interface CostBenefitAnalysis {
  totalInvestment: {
    materialCosts: number;
    laborCosts: number;
    totalCost: number;
    timeframe: string;
  };
  expectedBenefits: {
    efficiencyGains: number;
    spaceUtilization: number;
    propertyValue: number;
    energySavings: number;
  };
  roi: {
    paybackPeriod: number;
    netPresentValue: number;
    returnPercentage: number;
    riskAssessment: 'LOW' | 'MEDIUM' | 'HIGH';
  };
}

// Supporting Interfaces
export interface LayoutChange {
  id: string;
  type: 'CABINET_RELOCATION' | 'APPLIANCE_MOVE' | 'STORAGE_ADDITION' | 'WORKFLOW_OPTIMIZATION';
  description: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  estimatedCost: number;
  implementationTime: string;
  expectedBenefit: string;
  visualImpact: 'MAJOR' | 'MODERATE' | 'MINOR';
}

export interface SpaceOpportunity {
  location: string;
  currentState: string;
  opportunity: string;
  potentialGain: number;
  implementationCost: number;
  difficulty: 'EASY' | 'MODERATE' | 'DIFFICULT';
}

export interface ErgonomicRecommendation {
  area: string;
  currentIssue: string;
  recommendation: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  accessibilityImpact: boolean;
  estimatedCost: number;
}

export interface FlowPath {
  id: string;
  startPoint: string;
  endPoint: string;
  frequency: 'HIGH' | 'MEDIUM' | 'LOW';
  currentDistance: number;
  optimalDistance: number;
  efficiency: number;
  obstructions: string[];
}

export interface CongestionPoint {
  location: string;
  severity: 'HIGH' | 'MEDIUM' | 'LOW';
  causes: string[];
  suggestedSolutions: string[];
  estimatedImprovementCost: number;
}

export interface ClearanceIssue {
  location: string;
  currentClearance: number;
  requiredClearance: number;
  safetyRisk: 'HIGH' | 'MEDIUM' | 'LOW';
  suggestedFix: string;
  estimatedCost: number;
}

// Analysis Context Interfaces
export interface AnalysisContext {
  imagePaths: string[];
  analysisId: string;
  config: LayoutOptimizationConfig;
  startTime: number;
}

export interface PromptContext {
  analysisType: string;
  optimizationLevel: 'BASIC' | 'DETAILED' | 'COMPREHENSIVE';
  confidenceThreshold: number;
  includeAccessibilityFeatures: boolean;
  maxLayoutAlternatives: number;
}

// Analyzer Interface (for consistency across analyzers)
export interface LayoutAnalyzer<T> {
  analyze(imagePaths: string[], analysisId: string, config: LayoutOptimizationConfig): Promise<T>;
  getDefaultResult(): T;
  generatePrompt(config: LayoutOptimizationConfig): Promise<string>;
}

// Configuration Validation Interface
export interface ConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Processing Metrics Interface
export interface ProcessingMetrics {
  analysisTime: number;
  confidenceScore: number;
  featuresAnalyzed: string[];
  modelUsed?: string;
  cacheHit?: boolean;
  apiCalls?: number;
}

// Error Handling Interfaces
export interface LayoutAnalysisError {
  code: string;
  message: string;
  analysisId: string;
  analyzer?: string;
  timestamp: number;
  context?: any;
}

// Cache Interfaces
export interface LayoutCacheKey {
  imagePaths: string[];
  config: LayoutOptimizationConfig;
  analyzer: string;
  version: string;
}

export interface LayoutCacheEntry<T> {
  result: T;
  timestamp: number;
  confidence: number;
  metadata: {
    analysisId: string;
    processingTime: number;
    modelUsed: string;
  };
}
