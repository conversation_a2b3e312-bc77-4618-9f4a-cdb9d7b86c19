import { createModuleLogger } from '@/utils/logger';
import { LayoutOptimizationConfig, ConfigValidationResult } from './types';

const logger = createModuleLogger('LayoutConfigService');

/**
 * Layout Configuration Service
 * 
 * Centralizes configuration management for layout optimization services.
 * Handles default values, validation, and environment-specific settings.
 * 
 * Responsibilities:
 * - Default configuration management
 * - Configuration validation
 * - Environment-specific overrides
 * - Configuration health checks
 */
export class LayoutConfigService {
  private static instance: LayoutConfigService;
  private defaultConfig: LayoutOptimizationConfig;

  constructor() {
    this.defaultConfig = this.loadDefaultConfiguration();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): LayoutConfigService {
    if (!LayoutConfigService.instance) {
      LayoutConfigService.instance = new LayoutConfigService();
    }
    return LayoutConfigService.instance;
  }

  /**
   * Get default configuration
   */
  getDefaultConfig(): LayoutOptimizationConfig {
    return { ...this.defaultConfig };
  }

  /**
   * Validate configuration
   */
  validateConfig(config: LayoutOptimizationConfig): ConfigValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate confidence threshold
    if (config.confidenceThreshold < 0 || config.confidenceThreshold > 1) {
      errors.push('Confidence threshold must be between 0 and 1');
    }

    // Validate max layout alternatives
    if (config.maxLayoutAlternatives < 1 || config.maxLayoutAlternatives > 10) {
      errors.push('Max layout alternatives must be between 1 and 10');
    }

    // Validate optimization level
    if (!['BASIC', 'DETAILED', 'COMPREHENSIVE'].includes(config.optimizationLevel)) {
      errors.push('Invalid optimization level. Must be BASIC, DETAILED, or COMPREHENSIVE');
    }

    // Check for logical inconsistencies
    if (!config.enableWorkflowAnalysis && 
        !config.enableSpaceUtilization && 
        !config.enableErgonomicAssessment && 
        !config.enableTrafficFlowAnalysis && 
        !config.enableCostBenefitAnalysis) {
      errors.push('At least one analysis type must be enabled');
    }

    // Warnings for suboptimal configurations
    if (config.optimizationLevel === 'COMPREHENSIVE' && config.confidenceThreshold < 0.7) {
      warnings.push('Low confidence threshold with comprehensive analysis may produce unreliable results');
    }

    if (config.enableCostBenefitAnalysis && 
        (!config.enableWorkflowAnalysis || !config.enableSpaceUtilization)) {
      warnings.push('Cost-benefit analysis is most accurate when workflow and space utilization are also enabled');
    }

    if (config.includeAccessibilityFeatures && !config.enableErgonomicAssessment) {
      warnings.push('Accessibility features require ergonomic assessment to be enabled');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Merge configuration with defaults
   */
  mergeWithDefaults(partialConfig: Partial<LayoutOptimizationConfig>): LayoutOptimizationConfig {
    const merged = { ...this.defaultConfig, ...partialConfig };
    
    // Validate merged configuration
    const validation = this.validateConfig(merged);
    if (!validation.isValid) {
      logger.warn('Invalid configuration detected, using defaults for invalid fields', {
        errors: validation.errors
      });
      
      // Reset invalid fields to defaults
      if (merged.confidenceThreshold < 0 || merged.confidenceThreshold > 1) {
        merged.confidenceThreshold = this.defaultConfig.confidenceThreshold;
      }
      
      if (merged.maxLayoutAlternatives < 1 || merged.maxLayoutAlternatives > 10) {
        merged.maxLayoutAlternatives = this.defaultConfig.maxLayoutAlternatives;
      }
      
      if (!['BASIC', 'DETAILED', 'COMPREHENSIVE'].includes(merged.optimizationLevel)) {
        merged.optimizationLevel = this.defaultConfig.optimizationLevel;
      }
    }

    // Log warnings
    if (validation.warnings.length > 0) {
      logger.warn('Configuration warnings detected', {
        warnings: validation.warnings
      });
    }

    return merged;
  }

  /**
   * Get configuration for specific optimization level
   */
  getConfigForLevel(level: 'BASIC' | 'DETAILED' | 'COMPREHENSIVE'): LayoutOptimizationConfig {
    const baseConfig = this.getDefaultConfig();
    
    switch (level) {
      case 'BASIC':
        return {
          ...baseConfig,
          optimizationLevel: 'BASIC',
          enableWorkflowAnalysis: true,
          enableSpaceUtilization: true,
          enableErgonomicAssessment: false,
          enableTrafficFlowAnalysis: false,
          enableCostBenefitAnalysis: false,
          confidenceThreshold: 0.6,
          maxLayoutAlternatives: 2
        };

      case 'DETAILED':
        return {
          ...baseConfig,
          optimizationLevel: 'DETAILED',
          enableWorkflowAnalysis: true,
          enableSpaceUtilization: true,
          enableErgonomicAssessment: true,
          enableTrafficFlowAnalysis: true,
          enableCostBenefitAnalysis: false,
          confidenceThreshold: 0.75,
          maxLayoutAlternatives: 3
        };

      case 'COMPREHENSIVE':
        return {
          ...baseConfig,
          optimizationLevel: 'COMPREHENSIVE',
          enableWorkflowAnalysis: true,
          enableSpaceUtilization: true,
          enableErgonomicAssessment: true,
          enableTrafficFlowAnalysis: true,
          enableCostBenefitAnalysis: true,
          confidenceThreshold: 0.8,
          maxLayoutAlternatives: 5
        };

      default:
        return baseConfig;
    }
  }

  /**
   * Get accessibility-focused configuration
   */
  getAccessibilityConfig(): LayoutOptimizationConfig {
    return {
      ...this.getDefaultConfig(),
      enableErgonomicAssessment: true,
      enableTrafficFlowAnalysis: true,
      includeAccessibilityFeatures: true,
      confidenceThreshold: 0.85,
      optimizationLevel: 'DETAILED'
    };
  }

  /**
   * Get cost-focused configuration
   */
  getCostFocusedConfig(): LayoutOptimizationConfig {
    return {
      ...this.getDefaultConfig(),
      enableWorkflowAnalysis: true,
      enableSpaceUtilization: true,
      enableCostBenefitAnalysis: true,
      optimizationLevel: 'DETAILED',
      confidenceThreshold: 0.8,
      maxLayoutAlternatives: 4
    };
  }

  /**
   * Check configuration health
   */
  checkConfigHealth(config: LayoutOptimizationConfig): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    issues: string[];
    recommendations: string[];
  } {
    const validation = this.validateConfig(config);
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Add validation errors as issues
    issues.push(...validation.errors);

    // Add validation warnings as recommendations
    recommendations.push(...validation.warnings);

    // Additional health checks
    if (config.confidenceThreshold < 0.5) {
      issues.push('Very low confidence threshold may produce unreliable results');
    }

    if (config.maxLayoutAlternatives > 5 && config.optimizationLevel !== 'COMPREHENSIVE') {
      recommendations.push('High number of alternatives works best with comprehensive optimization');
    }

    // Determine overall health status
    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (issues.length > 0) {
      status = 'unhealthy';
    } else if (recommendations.length > 0) {
      status = 'degraded';
    } else {
      status = 'healthy';
    }

    return {
      status,
      issues,
      recommendations
    };
  }

  /**
   * Load default configuration
   */
  private loadDefaultConfiguration(): LayoutOptimizationConfig {
    return {
      enableWorkflowAnalysis: true,
      enableSpaceUtilization: true,
      enableErgonomicAssessment: true,
      enableTrafficFlowAnalysis: true,
      enableCostBenefitAnalysis: true,
      optimizationLevel: 'DETAILED',
      confidenceThreshold: 0.8,
      includeAccessibilityFeatures: true,
      maxLayoutAlternatives: 3
    };
  }

  /**
   * Update default configuration (for testing or environment-specific overrides)
   */
  updateDefaults(newDefaults: Partial<LayoutOptimizationConfig>): void {
    const merged = { ...this.defaultConfig, ...newDefaults };
    const validation = this.validateConfig(merged);
    
    if (validation.isValid) {
      this.defaultConfig = merged;
      logger.info('Default configuration updated', { newDefaults });
    } else {
      logger.error('Failed to update default configuration: invalid values', {
        errors: validation.errors
      });
      throw new Error(`Invalid default configuration: ${validation.errors.join(', ')}`);
    }
  }

  /**
   * Reset to factory defaults
   */
  resetToDefaults(): void {
    this.defaultConfig = this.loadDefaultConfiguration();
    logger.info('Configuration reset to factory defaults');
  }
}

// Export singleton instance
export const layoutConfigService = LayoutConfigService.getInstance();
