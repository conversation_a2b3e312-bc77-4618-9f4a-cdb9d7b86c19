import { createModuleLogger } from '@/utils/logger';
import { layoutAnalysisOrchestrator } from './LayoutAnalysisOrchestrator';
import { layoutConfigService } from './LayoutConfigService';
import { workflowAnalyzer } from './WorkflowAnalyzer';
import { spaceUtilizationAnalyzer } from './SpaceUtilizationAnalyzer';
import { ergonomicAnalyzer } from './ErgonomicAnalyzer';
import { trafficFlowAnalyzer } from './TrafficFlowAnalyzer';
import { costBenefitAnalyzer } from './CostBenefitAnalyzer';
import {
  LayoutOptimizationConfig,
  LayoutOptimizationResult,
  WorkflowOptimization,
  SpaceUtilization,
  ErgonomicAssessment,
  TrafficFlowAnalysis,
  CostBenefitAnalysis
} from './types';

const logger = createModuleLogger('LayoutOptimizationService');

/**
 * Layout Optimization Service - Main Facade
 * 
 * Provides a unified interface for all layout optimization functionality while maintaining
 * backward compatibility with the existing API. Orchestrates the modular services.
 * 
 * This service acts as a facade pattern, delegating to specialized services:
 * - LayoutAnalysisOrchestrator: Main coordination and result aggregation
 * - LayoutConfigService: Configuration management and validation
 * - WorkflowAnalyzer: Kitchen workflow optimization
 * - SpaceUtilizationAnalyzer: Storage and space efficiency
 * - ErgonomicAnalyzer: User comfort and accessibility
 * - TrafficFlowAnalyzer: Circulation and movement patterns
 * - CostBenefitAnalyzer: ROI and financial analysis
 */
export class LayoutOptimizationService {
  private static instance: LayoutOptimizationService;

  constructor() {
    this.validateInitialization();
  }

  /**
   * Get singleton instance (for backward compatibility)
   */
  static getInstance(): LayoutOptimizationService {
    if (!LayoutOptimizationService.instance) {
      LayoutOptimizationService.instance = new LayoutOptimizationService();
    }
    return LayoutOptimizationService.instance;
  }

  /**
   * Perform comprehensive layout optimization analysis (main method)
   */
  async optimizeLayout(
    imagePaths: string[],
    analysisId: string,
    config?: Partial<LayoutOptimizationConfig>
  ): Promise<LayoutOptimizationResult> {
    logger.info('Starting layout optimization analysis', {
      analysisId,
      imageCount: imagePaths.length,
      configProvided: !!config
    });

    return await layoutAnalysisOrchestrator.optimizeLayout(imagePaths, analysisId, config);
  }

  /**
   * Analyze workflow optimization only
   */
  async analyzeWorkflow(
    imagePaths: string[],
    analysisId: string,
    config?: Partial<LayoutOptimizationConfig>
  ): Promise<WorkflowOptimization> {
    const finalConfig = layoutConfigService.mergeWithDefaults(config || {});
    return await workflowAnalyzer.analyzeWorkflow(imagePaths, analysisId, finalConfig);
  }

  /**
   * Analyze space utilization only
   */
  async analyzeSpaceUtilization(
    imagePaths: string[],
    analysisId: string,
    config?: Partial<LayoutOptimizationConfig>
  ): Promise<SpaceUtilization> {
    const finalConfig = layoutConfigService.mergeWithDefaults(config || {});
    return await spaceUtilizationAnalyzer.analyzeSpaceUtilization(imagePaths, analysisId, finalConfig);
  }

  /**
   * Perform ergonomic assessment only
   */
  async performErgonomicAssessment(
    imagePaths: string[],
    analysisId: string,
    config?: Partial<LayoutOptimizationConfig>
  ): Promise<ErgonomicAssessment> {
    const finalConfig = layoutConfigService.mergeWithDefaults(config || {});
    return await ergonomicAnalyzer.performErgonomicAssessment(imagePaths, analysisId, finalConfig);
  }

  /**
   * Analyze traffic flow only
   */
  async analyzeTrafficFlow(
    imagePaths: string[],
    analysisId: string,
    config?: Partial<LayoutOptimizationConfig>
  ): Promise<TrafficFlowAnalysis> {
    const finalConfig = layoutConfigService.mergeWithDefaults(config || {});
    return await trafficFlowAnalyzer.analyzeTrafficFlow(imagePaths, analysisId, finalConfig);
  }

  /**
   * Perform cost-benefit analysis (requires other analysis results)
   */
  async performCostBenefitAnalysis(
    workflowOptimization: WorkflowOptimization,
    spaceUtilization: SpaceUtilization,
    ergonomicAssessment: ErgonomicAssessment,
    trafficFlowAnalysis: TrafficFlowAnalysis,
    analysisId: string,
    config?: Partial<LayoutOptimizationConfig>
  ): Promise<CostBenefitAnalysis> {
    const finalConfig = layoutConfigService.mergeWithDefaults(config || {});
    return await costBenefitAnalyzer.performCostBenefitAnalysis(
      workflowOptimization,
      spaceUtilization,
      ergonomicAssessment,
      trafficFlowAnalysis,
      analysisId,
      finalConfig
    );
  }

  /**
   * Get default configuration
   */
  getDefaultConfig(): LayoutOptimizationConfig {
    return layoutConfigService.getDefaultConfig();
  }

  /**
   * Get configuration for specific optimization level
   */
  getConfigForLevel(level: 'BASIC' | 'DETAILED' | 'COMPREHENSIVE'): LayoutOptimizationConfig {
    return layoutConfigService.getConfigForLevel(level);
  }

  /**
   * Get accessibility-focused configuration
   */
  getAccessibilityConfig(): LayoutOptimizationConfig {
    return layoutConfigService.getAccessibilityConfig();
  }

  /**
   * Get cost-focused configuration
   */
  getCostFocusedConfig(): LayoutOptimizationConfig {
    return layoutConfigService.getCostFocusedConfig();
  }

  /**
   * Validate configuration
   */
  validateConfig(config: LayoutOptimizationConfig) {
    return layoutConfigService.validateConfig(config);
  }

  /**
   * Merge configuration with defaults
   */
  mergeWithDefaults(partialConfig: Partial<LayoutOptimizationConfig>): LayoutOptimizationConfig {
    return layoutConfigService.mergeWithDefaults(partialConfig);
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    services: {
      orchestrator: boolean;
      configService: boolean;
      workflowAnalyzer: boolean;
      spaceUtilizationAnalyzer: boolean;
      ergonomicAnalyzer: boolean;
      trafficFlowAnalyzer: boolean;
      costBenefitAnalyzer: boolean;
    };
    configuration: any;
  }> {
    try {
      // Check configuration health
      const defaultConfig = layoutConfigService.getDefaultConfig();
      const configHealth = layoutConfigService.checkConfigHealth(defaultConfig);
      
      // Check service availability (all services are always available as they're local)
      const services = {
        orchestrator: true,
        configService: true,
        workflowAnalyzer: true,
        spaceUtilizationAnalyzer: true,
        ergonomicAnalyzer: true,
        trafficFlowAnalyzer: true,
        costBenefitAnalyzer: true
      };

      // Determine overall status based on configuration health
      let status: 'healthy' | 'degraded' | 'unhealthy';
      if (configHealth.status === 'unhealthy') {
        status = 'unhealthy';
      } else if (configHealth.status === 'degraded') {
        status = 'degraded';
      } else {
        status = 'healthy';
      }

      return {
        status,
        services,
        configuration: configHealth
      };
    } catch (error) {
      logger.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        services: {
          orchestrator: false,
          configService: false,
          workflowAnalyzer: false,
          spaceUtilizationAnalyzer: false,
          ergonomicAnalyzer: false,
          trafficFlowAnalyzer: false,
          costBenefitAnalyzer: false
        },
        configuration: { status: 'unhealthy', error: error.message }
      };
    }
  }

  /**
   * Get available analyzers
   */
  getAvailableAnalyzers(): string[] {
    return [
      'WorkflowAnalyzer',
      'SpaceUtilizationAnalyzer',
      'ErgonomicAnalyzer',
      'TrafficFlowAnalyzer',
      'CostBenefitAnalyzer'
    ];
  }

  /**
   * Validate service initialization
   */
  private validateInitialization(): void {
    try {
      // Test configuration service
      const defaultConfig = layoutConfigService.getDefaultConfig();
      const validation = layoutConfigService.validateConfig(defaultConfig);
      
      if (!validation.isValid) {
        logger.warn('Default configuration validation failed', {
          errors: validation.errors
        });
      } else {
        logger.info('Layout optimization service successfully initialized');
      }
    } catch (error) {
      logger.error('Layout optimization service initialization failed:', error);
    }
  }

  // Backward compatibility methods

  /**
   * @deprecated Use optimizeLayout instead
   */
  async performLayoutOptimization(
    imagePaths: string[],
    analysisId: string,
    config?: Partial<LayoutOptimizationConfig>
  ): Promise<LayoutOptimizationResult> {
    logger.warn('performLayoutOptimization is deprecated, use optimizeLayout instead');
    return this.optimizeLayout(imagePaths, analysisId, config);
  }

  /**
   * @deprecated Use getHealthStatus instead
   */
  async checkServiceHealth(): Promise<boolean> {
    logger.warn('checkServiceHealth is deprecated, use getHealthStatus instead');
    const health = await this.getHealthStatus();
    return health.status !== 'unhealthy';
  }

  /**
   * @deprecated Use getAvailableAnalyzers instead
   */
  isServiceAvailable(): boolean {
    logger.warn('isServiceAvailable is deprecated, use getAvailableAnalyzers instead');
    return this.getAvailableAnalyzers().length > 0;
  }
}

// Export interfaces for backward compatibility
export { 
  LayoutOptimizationConfig, 
  LayoutOptimizationResult,
  WorkflowOptimization,
  SpaceUtilization,
  ErgonomicAssessment,
  TrafficFlowAnalysis,
  CostBenefitAnalysis
};

// Export singleton instance for backward compatibility
export const layoutOptimizationService = LayoutOptimizationService.getInstance();

// Default export for backward compatibility
export default layoutOptimizationService;
