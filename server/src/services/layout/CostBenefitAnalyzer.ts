import { createModuleLogger } from '@/utils/logger';
import { promptOptimizationService } from '../promptOptimizationService';
import { 
  LayoutOptimizationConfig, 
  CostBenefitAnalysis, 
  LayoutAnalyzer,
  WorkflowOptimization,
  SpaceUtilization,
  ErgonomicAssessment,
  TrafficFlowAnalysis
} from './types';

const logger = createModuleLogger('CostBenefitAnalyzer');

/**
 * Cost Benefit Analyzer
 * 
 * Specialized analyzer for cost-benefit analysis of layout optimization recommendations.
 * Integrates data from all other analyzers to provide comprehensive ROI calculations.
 * 
 * Responsibilities:
 * - Investment cost calculation
 * - Benefit quantification
 * - ROI analysis and payback period
 * - Risk assessment
 */
export class CostBenefitAnalyzer implements LayoutAnalyzer<CostBenefitAnalysis> {
  /**
   * Perform cost-benefit analysis
   */
  async analyze(
    imagePaths: string[],
    analysisId: string,
    config: LayoutOptimizationConfig
  ): Promise<CostBenefitAnalysis> {
    // This analyzer requires data from other analyzers, so it's typically called
    // from the orchestrator with pre-computed analysis results
    throw new Error('CostBenefitAnalyzer.analyze() should not be called directly. Use performCostBenefitAnalysis() instead.');
  }

  /**
   * Perform comprehensive cost-benefit analysis
   */
  async performCostBenefitAnalysis(
    workflowOptimization: WorkflowOptimization,
    spaceUtilization: SpaceUtilization,
    ergonomicAssessment: ErgonomicAssessment,
    trafficFlowAnalysis: TrafficFlowAnalysis,
    analysisId: string,
    config: LayoutOptimizationConfig
  ): Promise<CostBenefitAnalysis> {
    logger.info(`Performing cost-benefit analysis: ${analysisId}`, {
      optimizationLevel: config.optimizationLevel
    });

    const prompt = await this.generatePrompt(config);

    try {
      const analysis: CostBenefitAnalysis = {
        totalInvestment: this.calculateTotalInvestment(
          workflowOptimization,
          spaceUtilization,
          ergonomicAssessment,
          trafficFlowAnalysis,
          config
        ),
        expectedBenefits: this.calculateExpectedBenefits(
          workflowOptimization,
          spaceUtilization,
          ergonomicAssessment,
          trafficFlowAnalysis,
          config
        ),
        roi: this.calculateROI(
          workflowOptimization,
          spaceUtilization,
          ergonomicAssessment,
          trafficFlowAnalysis,
          config
        )
      };

      logger.info(`Cost-benefit analysis completed: ${analysisId}`, {
        totalCost: analysis.totalInvestment.totalCost,
        returnPercentage: analysis.roi.returnPercentage,
        paybackPeriod: analysis.roi.paybackPeriod
      });

      return analysis;
    } catch (error) {
      logger.error(`Cost-benefit analysis failed: ${analysisId}`, error);
      return this.getDefaultResult();
    }
  }

  /**
   * Calculate total investment required
   */
  private calculateTotalInvestment(
    workflowOptimization: WorkflowOptimization,
    spaceUtilization: SpaceUtilization,
    ergonomicAssessment: ErgonomicAssessment,
    trafficFlowAnalysis: TrafficFlowAnalysis,
    config: LayoutOptimizationConfig
  ) {
    // Aggregate costs from all analysis components
    let materialCosts = 0;
    let laborCosts = 0;

    // Workflow optimization costs
    materialCosts += workflowOptimization.optimizedWorkflow.estimatedCost * 0.6; // 60% materials
    laborCosts += workflowOptimization.optimizedWorkflow.estimatedCost * 0.4; // 40% labor

    // Space utilization costs
    const spaceOptimizationCosts = spaceUtilization.recommendations.reduce(
      (total, rec) => total + rec.implementationCost, 0
    );
    materialCosts += spaceOptimizationCosts * 0.65; // 65% materials for storage solutions
    laborCosts += spaceOptimizationCosts * 0.35; // 35% labor

    // Ergonomic improvement costs
    const ergonomicCosts = [
      ...ergonomicAssessment.improvements.cabinetHeights,
      ...ergonomicAssessment.improvements.handlePositions,
      ...ergonomicAssessment.improvements.accessibilityFeatures,
      ...ergonomicAssessment.improvements.safetyEnhancements
    ].reduce((total, improvement) => total + improvement.estimatedCost, 0);
    materialCosts += ergonomicCosts * 0.55; // 55% materials
    laborCosts += ergonomicCosts * 0.45; // 45% labor (more labor-intensive)

    // Traffic flow optimization costs
    const trafficFlowCosts = trafficFlowAnalysis.optimizedFlow.suggestedLayout.reduce(
      (total, change) => total + change.estimatedCost, 0
    );
    materialCosts += trafficFlowCosts * 0.5; // 50% materials
    laborCosts += trafficFlowCosts * 0.5; // 50% labor

    const totalCost = materialCosts + laborCosts;

    // Determine timeframe based on complexity
    let timeframe = '2-3 weeks';
    if (totalCost > 10000) {
      timeframe = '4-6 weeks';
    } else if (totalCost > 5000) {
      timeframe = '3-4 weeks';
    } else if (totalCost < 2000) {
      timeframe = '1-2 weeks';
    }

    return {
      materialCosts: Math.round(materialCosts),
      laborCosts: Math.round(laborCosts),
      totalCost: Math.round(totalCost),
      timeframe
    };
  }

  /**
   * Calculate expected benefits
   */
  private calculateExpectedBenefits(
    workflowOptimization: WorkflowOptimization,
    spaceUtilization: SpaceUtilization,
    ergonomicAssessment: ErgonomicAssessment,
    trafficFlowAnalysis: TrafficFlowAnalysis,
    config: LayoutOptimizationConfig
  ) {
    // Efficiency gains from workflow optimization
    const workflowEfficiencyGain = (
      workflowOptimization.optimizedWorkflow.expectedEfficiency - 
      workflowOptimization.currentWorkflow.efficiency
    ) * 100;
    const efficiencyGains = Math.round(workflowEfficiencyGain * 50); // $50 per percentage point

    // Space utilization benefits
    const spaceGain = spaceUtilization.recommendations.reduce(
      (total, rec) => total + rec.expectedGain, 0
    );
    const spaceUtilizationValue = Math.round(spaceGain * 25); // $25 per sq ft gained

    // Property value increase (conservative estimate)
    const totalInvestment = this.calculateTotalInvestment(
      workflowOptimization, spaceUtilization, ergonomicAssessment, trafficFlowAnalysis, config
    ).totalCost;
    const propertyValue = Math.round(totalInvestment * 0.7); // 70% of investment adds to property value

    // Energy savings (from improved efficiency and lighting)
    const energySavings = config.optimizationLevel === 'COMPREHENSIVE' ? 180 : 120; // Annual savings

    return {
      efficiencyGains,
      spaceUtilization: spaceUtilizationValue,
      propertyValue,
      energySavings
    };
  }

  /**
   * Calculate ROI metrics
   */
  private calculateROI(
    workflowOptimization: WorkflowOptimization,
    spaceUtilization: SpaceUtilization,
    ergonomicAssessment: ErgonomicAssessment,
    trafficFlowAnalysis: TrafficFlowAnalysis,
    config: LayoutOptimizationConfig
  ) {
    const investment = this.calculateTotalInvestment(
      workflowOptimization, spaceUtilization, ergonomicAssessment, trafficFlowAnalysis, config
    );
    const benefits = this.calculateExpectedBenefits(
      workflowOptimization, spaceUtilization, ergonomicAssessment, trafficFlowAnalysis, config
    );

    // Calculate annual benefits (excluding one-time property value increase)
    const annualBenefits = benefits.efficiencyGains + benefits.energySavings;
    
    // Calculate payback period
    const paybackPeriod = investment.totalCost > 0 ? 
      Math.round((investment.totalCost / annualBenefits) * 10) / 10 : 0;

    // Calculate NPV (assuming 5% discount rate over 10 years)
    const discountRate = 0.05;
    const years = 10;
    let npv = -investment.totalCost; // Initial investment
    
    for (let year = 1; year <= years; year++) {
      npv += annualBenefits / Math.pow(1 + discountRate, year);
    }
    npv += benefits.propertyValue; // Add property value increase

    // Calculate return percentage
    const totalBenefits = (annualBenefits * years) + benefits.propertyValue;
    const returnPercentage = investment.totalCost > 0 ? 
      Math.round(((totalBenefits - investment.totalCost) / investment.totalCost) * 100) : 0;

    // Assess risk level
    let riskAssessment: 'LOW' | 'MEDIUM' | 'HIGH' = 'MEDIUM';
    if (investment.totalCost < 3000 && paybackPeriod < 3) {
      riskAssessment = 'LOW';
    } else if (investment.totalCost > 8000 || paybackPeriod > 6) {
      riskAssessment = 'HIGH';
    }

    return {
      paybackPeriod,
      netPresentValue: Math.round(npv),
      returnPercentage,
      riskAssessment
    };
  }

  /**
   * Generate cost-benefit analysis prompt
   */
  async generatePrompt(config: LayoutOptimizationConfig): Promise<string> {
    const basePrompt = `You are an expert kitchen renovation cost-benefit analyst specializing in ROI calculations and investment analysis.

ANALYSIS FOCUS: Kitchen Layout Optimization Cost-Benefit Analysis
OPTIMIZATION LEVEL: ${config.optimizationLevel}
CONFIDENCE THRESHOLD: ${config.confidenceThreshold}

COST-BENEFIT ANALYSIS REQUIREMENTS:
1. Investment Calculation: Aggregate all material and labor costs
2. Benefit Quantification: Measure efficiency gains, space improvements, and value additions
3. ROI Analysis: Calculate payback period, NPV, and return percentage
4. Risk Assessment: Evaluate implementation risks and market factors
5. Sensitivity Analysis: Consider variations in costs and benefits

COST CATEGORIES:
- Material costs: Cabinets, hardware, appliances, finishes
- Labor costs: Installation, electrical, plumbing, finishing
- Permit and design fees: Professional services and approvals
- Contingency: 10-15% buffer for unexpected costs

BENEFIT CATEGORIES:
- Efficiency gains: Time savings, improved workflow
- Space utilization: Additional storage and functional space
- Property value: Market value increase from improvements
- Energy savings: Reduced utility costs from efficiency improvements
- Quality of life: User satisfaction and comfort improvements

FINANCIAL METRICS:
- Payback period: Time to recover initial investment
- Net Present Value (NPV): Discounted future cash flows
- Return on Investment (ROI): Percentage return over time period
- Risk assessment: Implementation and market risk factors

${config.includeAccessibilityFeatures ? `
ACCESSIBILITY VALUE:
- Universal design premium: Additional property value
- Future-proofing benefits: Aging-in-place value
- Compliance value: ADA compliance market advantages
` : ''}

CRITICAL: Provide realistic, conservative estimates with clear assumptions and risk factors.
`;

    try {
      const optimizationResult = await promptOptimizationService.optimizePrompt(
        basePrompt,
        {
          analysisType: 'cost_benefit_analysis',
          targetMetrics: {
            minAccuracy: config.confidenceThreshold,
            maxResponseTime: 8000
          },
          commonErrors: [
            'Overly optimistic benefit projections',
            'Underestimating implementation costs',
            'Ignoring market risk factors'
          ]
        }
      );

      return optimizationResult.optimizedPrompt;
    } catch (error) {
      logger.warn('Prompt optimization failed, using base prompt', error);
      return basePrompt;
    }
  }

  /**
   * Get default cost-benefit analysis result
   */
  getDefaultResult(): CostBenefitAnalysis {
    return {
      totalInvestment: {
        materialCosts: 0,
        laborCosts: 0,
        totalCost: 0,
        timeframe: 'N/A'
      },
      expectedBenefits: {
        efficiencyGains: 0,
        spaceUtilization: 0,
        propertyValue: 0,
        energySavings: 0
      },
      roi: {
        paybackPeriod: 0,
        netPresentValue: 0,
        returnPercentage: 0,
        riskAssessment: 'HIGH'
      }
    };
  }
}

// Export singleton instance
export const costBenefitAnalyzer = new CostBenefitAnalyzer();
