import { <PERSON>A<PERSON> } from 'openai';
import { createModuleLogger } from '../utils/logger';
import { EnhancedCacheService } from './enhancedCacheService';
import Bull from 'bull';

// Phase 3.1: Async/Await Pattern Standardization
import { AsyncOperationManager, AsyncOperationConfig } from './documentIntelligence/utils/AsyncOperationManager';
import { PerformanceTracker } from './documentIntelligence/utils/PerformanceTracker';

const logger = createModuleLogger('AdvancedOpenAIService');

export interface OpenAIRequest {
  id: string;
  prompt: string;
  model: string;
  options: any;
  priority: number;
  timestamp: number;
  userId?: string;
  analysisType?: string;
}

export interface RateLimitState {
  requestsPerMinute: number;
  tokensPerMinute: number;
  currentRequests: number;
  currentTokens: number;
  resetTime: number;
  lastRequestTime: number;
}

export interface QueueStats {
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  rateLimitState: RateLimitState;
  cacheHitRate: number;
  averageProcessingTime: number;
}

/**
 * Advanced OpenAI Service for Cabinet Insight Pro Scalability
 * 
 * Implements intelligent request queuing, rate limiting, and caching
 * to achieve 60-80% API call reduction while maintaining performance.
 * 
 * Features:
 * - Bull queue for request management and prioritization
 * - Intelligent rate limiting with adaptive backoff
 * - Semantic caching for similar requests
 * - Request deduplication and batching
 * - Performance metrics and monitoring
 * - Automatic retry with exponential backoff
 */
export class AdvancedOpenAIService {
  private client: OpenAI;
  private cacheService: EnhancedCacheService;
  private requestQueue: Bull.Queue;
  private rateLimitState: RateLimitState;
  private processingStats = {
    totalRequests: 0,
    cacheHits: 0,
    totalProcessingTime: 0,
    averageProcessingTime: 0
  };

  // Phase 3.1: Async/Await Pattern Standardization
  private asyncManager: AsyncOperationManager;
  private performanceTracker: PerformanceTracker;
  private rateLimitResetInterval?: NodeJS.Timeout;

  constructor() {
    // Phase 3.1: Initialize async operation management
    this.asyncManager = new AsyncOperationManager({
      timeout: 30000, // 30 seconds for OpenAI requests
      maxRetries: 3,
      retryDelayBase: 1000,
      maxRetryDelay: 10000,
      concurrencyLimit: 5 // Match queue concurrency
    });

    this.performanceTracker = new PerformanceTracker(1000); // Track last 1000 operations

    this.initializeClient();
    this.cacheService = new EnhancedCacheService();
    this.initializeQueue();
    this.initializeRateLimiting();
    this.startRateLimitReset();
  }

  private initializeClient(): void {
    this.client = new OpenAI({
      apiKey: process.env.AZURE_OPENAI_API_KEY,
      baseURL: `https://${process.env.AZURE_OPENAI_ENDPOINT}/openai/deployments`,
      defaultQuery: { 'api-version': '2024-12-01-preview' },
      defaultHeaders: {
        'api-key': process.env.AZURE_OPENAI_API_KEY,
      },
    });

    logger.info('Advanced OpenAI client initialized');
  }

  private initializeQueue(): void {
    this.requestQueue = new Bull('openai-requests', {
      redis: {
        host: process.env.REDIS_NODE_1_HOST || 'redis-node-1',
        port: parseInt(process.env.REDIS_NODE_1_PORT || '7001'),
        password: process.env.REDIS_PASSWORD,
      },
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
      settings: {
        stalledInterval: 30000,
        maxStalledCount: 1,
      }
    });

    // Process requests with concurrency control
    this.requestQueue.process('openai-request', 5, this.processRequest.bind(this));

    // Set up queue event handlers
    this.setupQueueEventHandlers();

    logger.info('OpenAI request queue initialized');
  }

  private setupQueueEventHandlers(): void {
    this.requestQueue.on('completed', (job, result) => {
      logger.debug(`Request completed: ${job.data.id}`);
      this.updateProcessingStats(job, result);
    });

    this.requestQueue.on('failed', (job, error) => {
      logger.error(`Request failed: ${job.data.id}`, error);
    });

    this.requestQueue.on('stalled', (job) => {
      logger.warn(`Request stalled: ${job.data.id}`);
    });

    this.requestQueue.on('error', (error) => {
      logger.error('Queue error:', error);
    });
  }

  private initializeRateLimiting(): void {
    // Azure OpenAI rate limits (adjust based on your subscription)
    this.rateLimitState = {
      requestsPerMinute: parseInt(process.env.OPENAI_REQUESTS_PER_MINUTE || '60'),
      tokensPerMinute: parseInt(process.env.OPENAI_TOKENS_PER_MINUTE || '150000'),
      currentRequests: 0,
      currentTokens: 0,
      resetTime: Date.now() + 60000,
      lastRequestTime: 0
    };

    logger.info('Rate limiting initialized:', {
      requestsPerMinute: this.rateLimitState.requestsPerMinute,
      tokensPerMinute: this.rateLimitState.tokensPerMinute
    });
  }

  private startRateLimitReset(): void {
    // Phase 3.1: Store interval reference for proper cleanup
    this.rateLimitResetInterval = setInterval(() => {
      this.resetRateLimits();
    }, 60000); // Reset every minute
  }

  private resetRateLimits(): void {
    this.rateLimitState.currentRequests = 0;
    this.rateLimitState.currentTokens = 0;
    this.rateLimitState.resetTime = Date.now() + 60000;
    
    logger.debug('Rate limits reset');
  }

  /**
   * Queue an OpenAI request with intelligent prioritization
   * Phase 3.1: Enhanced with async operation management and performance tracking
   */
  async queueRequest(
    prompt: string,
    model: string,
    options: any = {},
    userId?: string,
    analysisType?: string
  ): Promise<any> {
    return await this.performanceTracker.trackOperation(
      'openai_request_queue',
      async () => {
        const requestId = this.generateRequestId();

        // Parallel cache operations for better performance
        const cacheOperations = [
          () => this.checkDirectCache(prompt, model, options),
          () => this.findSimilarCachedResult(prompt)
        ];

        const cacheResults = await this.asyncManager.executeInParallel(
          cacheOperations,
          'cache_lookup',
          false // Don't fail fast - continue even if one cache lookup fails
        );

        // Check direct cache hit
        if (cacheResults[0].success && cacheResults[0].data) {
          this.processingStats.cacheHits++;
          logger.debug(`Cache hit for request: ${requestId}`);
          return cacheResults[0].data;
        }

        // Check semantic similarity cache hit
        if (cacheResults[1].success && cacheResults[1].data && cacheResults[1].data.similarity > 0.85) {
          this.processingStats.cacheHits++;
          logger.debug(`Semantic cache hit for request: ${requestId} (similarity: ${cacheResults[1].data.similarity})`);
          return cacheResults[1].data.result;
        }

        const request: OpenAIRequest = {
          id: requestId,
          prompt,
          model,
          options,
          priority: this.calculatePriority(prompt, options, analysisType),
          timestamp: Date.now(),
          userId,
          analysisType
        };

        // Queue the request with async management
        const queueResult = await this.asyncManager.executeWithRetry(
          async () => {
            const job = await this.requestQueue.add('openai-request', request, {
              priority: request.priority,
              delay: 0
            });

            logger.info(`Request queued: ${requestId} (priority: ${request.priority})`);

            // Use async/await instead of Promise constructor
            return await job.finished();
          },
          'queue_job_creation'
        );

        if (!queueResult.success) {
          throw new Error(`Failed to queue request: ${queueResult.error?.message}`);
        }

        return queueResult.data;
      },
      {
        requestId,
        model,
        promptLength: prompt.length,
        analysisType,
        userId
      }
    ).then(result => result.result);
  }

  /**
   * Helper method for direct cache lookup
   */
  private async checkDirectCache(prompt: string, model: string, options: any): Promise<any> {
    const cacheKey = this.generateCacheKey(prompt, model, options);
    return await this.cacheService.get(cacheKey);
  }

  /**
   * Process OpenAI request with enhanced async patterns
   * Phase 3.1: Optimized with async operation management
   */
  private async processRequest(job: Bull.Job<OpenAIRequest>): Promise<any> {
    const { id, prompt, model, options } = job.data;

    return await this.performanceTracker.trackOperation(
      'openai_request_processing',
      async () => {
        // Update job progress
        await job.progress(10);

        // Wait for rate limit availability with async management
        const rateLimitResult = await this.asyncManager.executeWithRetry(
          () => this.waitForRateLimit(),
          'rate_limit_wait',
          { timeout: 60000 } // Extended timeout for rate limiting
        );

        if (!rateLimitResult.success) {
          throw new Error(`Rate limit wait failed: ${rateLimitResult.error?.message}`);
        }

        await job.progress(30);

        // Make API request with retry logic
        const apiResult = await this.asyncManager.executeWithRetry(
          () => this.makeOpenAIRequest(prompt, model, options),
          'openai_api_call',
          {
            timeout: 45000, // 45 second timeout for API calls
            maxRetries: 2 // Reduced retries for API calls to avoid long delays
          }
        );

        if (!apiResult.success) {
          throw new Error(`OpenAI API call failed: ${apiResult.error?.message}`);
        }

        const result = apiResult.data;
        await job.progress(80);

        // Cache result asynchronously (don't block on cache failures)
        const cacheKey = this.generateCacheKey(prompt, model, options);
        const cacheResult = await this.asyncManager.executeWithRetry(
          () => this.cacheWithSemantics(cacheKey, result, prompt),
          'result_caching',
          { maxRetries: 1 } // Single retry for caching
        );

        if (!cacheResult.success) {
          logger.warn(`Failed to cache result for ${id}: ${cacheResult.error?.message}`);
          // Don't throw - caching failure shouldn't fail the request
        }

        await job.progress(100);

        logger.info(`Request processed: ${id}`);
        return result;
      },
      {
        requestId: id,
        model,
        promptLength: prompt.length
      }
    ).then(result => result.result);
  }

  private async waitForRateLimit(): Promise<void> {
    while (!this.canMakeRequest()) {
      const waitTime = Math.min(1000, this.rateLimitState.resetTime - Date.now());
      logger.debug(`Rate limit reached, waiting ${waitTime}ms`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  private canMakeRequest(): boolean {
    const now = Date.now();

    if (now >= this.rateLimitState.resetTime) {
      this.resetRateLimits();
    }

    return this.rateLimitState.currentRequests < this.rateLimitState.requestsPerMinute &&
           this.rateLimitState.currentTokens < this.rateLimitState.tokensPerMinute;
  }

  private async makeOpenAIRequest(prompt: string, model: string, options: any): Promise<any> {
    const startTime = Date.now();

    try {
      const response = await this.client.chat.completions.create({
        model,
        messages: [{ role: 'user', content: prompt }],
        ...options
      });

      // Update rate limit counters
      this.rateLimitState.currentRequests++;
      this.rateLimitState.currentTokens += response.usage?.total_tokens || 0;
      this.rateLimitState.lastRequestTime = Date.now();

      const processingTime = Date.now() - startTime;

      return {
        content: response.choices[0]?.message?.content,
        model: response.model,
        usage: response.usage,
        processingTime,
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      };

    } catch (error: any) {
      if (error.status === 429) {
        this.handleRateLimitError(error);
      }
      throw error;
    }
  }

  private handleRateLimitError(error: any): void {
    const retryAfter = error.headers?.['retry-after'];
    if (retryAfter) {
      this.rateLimitState.resetTime = Date.now() + (parseInt(retryAfter) * 1000);
      logger.warn(`Rate limit hit, retry after ${retryAfter} seconds`);
    }
  }

  private calculatePriority(prompt: string, options: any, analysisType?: string): number {
    let priority = 0;

    // Higher priority for shorter prompts (faster processing)
    if (prompt.length < 1000) priority += 10;
    else if (prompt.length < 5000) priority += 5;

    // Premium analysis types get higher priority
    if (analysisType === 'premium') priority += 15;
    else if (analysisType === 'standard') priority += 10;
    else if (analysisType === 'basic') priority += 5;

    // Real-time requests get higher priority
    if (options.realTime) priority += 20;

    // 3D reconstruction gets high priority
    if (options.enable3DReconstruction) priority += 12;

    return priority;
  }

  private generateCacheKey(prompt: string, model: string, options: any): string {
    const crypto = require('crypto');
    const data = `${prompt}:${model}:${JSON.stringify(options)}`;
    return `openai:${crypto.createHash('md5').update(data).digest('hex')}`;
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async findSimilarCachedResult(prompt: string): Promise<any> {
    // Simplified semantic similarity - in production, use proper vector similarity
    try {
      const promptHash = require('crypto').createHash('md5').update(prompt).digest('hex');
      const similarKey = `semantic:${promptHash.substring(0, 8)}*`;
      
      // This would be replaced with proper vector similarity search
      return null;
    } catch (error) {
      logger.error('Semantic search error:', error);
      return null;
    }
  }

  private async cacheWithSemantics(key: string, result: any, prompt: string): Promise<void> {
    try {
      // Cache the result with extended TTL for AI responses
      await this.cacheService.set(key, result, 24 * 60 * 60); // 24 hours

      // Store semantic information for future similarity matching
      const promptHash = require('crypto').createHash('md5').update(prompt).digest('hex');
      const semanticKey = `semantic:${promptHash.substring(0, 8)}`;
      await this.cacheService.set(semanticKey, { result, prompt }, 24 * 60 * 60);

    } catch (error) {
      logger.error('Semantic caching error:', error);
    }
  }

  private updateProcessingStats(job: Bull.Job, result: any): void {
    this.processingStats.totalRequests++;
    
    if (result.processingTime) {
      this.processingStats.totalProcessingTime += result.processingTime;
      this.processingStats.averageProcessingTime = 
        this.processingStats.totalProcessingTime / this.processingStats.totalRequests;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<QueueStats> {
    try {
      const waiting = await this.requestQueue.waiting();
      const active = await this.requestQueue.active();
      const completed = await this.requestQueue.completed();
      const failed = await this.requestQueue.failed();

      const cacheStats = await this.cacheService.getStats();
      
      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        rateLimitState: this.rateLimitState,
        cacheHitRate: cacheStats.hitRate,
        averageProcessingTime: this.processingStats.averageProcessingTime
      };
    } catch (error) {
      logger.error('Failed to get queue stats:', error);
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        rateLimitState: this.rateLimitState,
        cacheHitRate: 0,
        averageProcessingTime: 0,
        error: error.message
      };
    }
  }

  /**
   * Get processing statistics
   * Phase 3.1: Enhanced with performance tracking data
   */
  getProcessingStats(): any {
    const cacheHitRate = this.processingStats.totalRequests > 0
      ? this.processingStats.cacheHits / this.processingStats.totalRequests
      : 0;

    const performanceReport = this.performanceTracker.getPerformanceReport();
    const asyncConfig = this.asyncManager.getConfig();

    return {
      ...this.processingStats,
      cacheHitRate: Math.round(cacheHitRate * 100) / 100,
      estimatedCostSavings: cacheHitRate * 0.7, // Estimate 70% cost savings from cache hits
      performance: {
        averageDuration: performanceReport.averageDuration,
        totalOperations: performanceReport.totalOperations,
        operationBreakdown: performanceReport.operationBreakdown
      },
      asyncConfiguration: {
        timeout: asyncConfig.timeout,
        maxRetries: asyncConfig.maxRetries,
        concurrencyLimit: asyncConfig.concurrencyLimit
      }
    };
  }

  /**
   * Get performance metrics for monitoring
   * Phase 3.1: New method for detailed performance analysis
   */
  getPerformanceMetrics(operationFilter?: string) {
    return this.performanceTracker.getPerformanceReport(operationFilter);
  }

  /**
   * Get async operation configuration
   */
  getAsyncConfiguration() {
    return this.asyncManager.getConfig();
  }

  /**
   * Update async operation configuration
   */
  updateAsyncConfiguration(config: Partial<AsyncOperationConfig>) {
    this.asyncManager.updateConfig(config);
    logger.info('Async operation configuration updated', config);
  }

  /**
   * Clear performance history
   */
  clearPerformanceHistory(): void {
    this.performanceTracker.clearHistory();
    logger.info('Performance history cleared');
  }

  /**
   * Pause the queue
   */
  async pauseQueue(): Promise<void> {
    await this.requestQueue.pause();
    logger.info('OpenAI request queue paused');
  }

  /**
   * Resume the queue
   */
  async resumeQueue(): Promise<void> {
    await this.requestQueue.resume();
    logger.info('OpenAI request queue resumed');
  }

  /**
   * Clean up completed and failed jobs
   */
  async cleanQueue(): Promise<void> {
    await this.requestQueue.clean(24 * 60 * 60 * 1000, 'completed'); // Clean completed jobs older than 24h
    await this.requestQueue.clean(24 * 60 * 60 * 1000, 'failed'); // Clean failed jobs older than 24h
    logger.info('OpenAI request queue cleaned');
  }

  /**
   * Shutdown the service gracefully
   * Phase 3.1: Enhanced with async operation management and proper cleanup
   */
  async shutdown(): Promise<void> {
    const shutdownResult = await this.asyncManager.executeWithRetry(
      async () => {
        // Clear rate limit reset interval
        if (this.rateLimitResetInterval) {
          clearInterval(this.rateLimitResetInterval);
          this.rateLimitResetInterval = undefined;
        }

        // Shutdown operations in parallel for faster cleanup
        const shutdownOperations = [
          () => this.requestQueue.close(),
          () => this.cacheService.disconnect()
        ];

        const shutdownResults = await this.asyncManager.executeInParallel(
          shutdownOperations,
          'service_shutdown',
          false // Don't fail fast - try to shutdown all components
        );

        // Log any shutdown failures but don't throw
        shutdownResults.forEach((result, index) => {
          const component = index === 0 ? 'requestQueue' : 'cacheService';
          if (!result.success) {
            logger.warn(`Failed to shutdown ${component}: ${result.error?.message}`);
          }
        });

        // Clear performance tracking history
        this.performanceTracker.clearHistory();

        logger.info('Advanced OpenAI service shut down gracefully');
      },
      'service_shutdown',
      { timeout: 15000, maxRetries: 1 }
    );

    if (!shutdownResult.success) {
      logger.error('Error during shutdown:', shutdownResult.error);
      throw shutdownResult.error;
    }
  }
}
