/**
 * Enhanced Reporting Service
 * 
 * Priority 2 Enhanced Analysis Engine Feature 3
 * Generates comprehensive PDF reports with 3D visualizations, cabinet counts,
 * measurements, material analysis, and cost estimates.
 * 
 * Integrates all Priority 1 and Priority 2 analysis features:
 * - 3D Cabinet Reconstruction
 * - Intelligent Measurement System  
 * - Enhanced Smart Hardware Recognition
 * - Advanced Material Recognition and Cost Estimation
 * - Smart Layout Optimization
 */

import fs from 'fs';
import path from 'path';
import puppeteer from 'puppeteer';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { logger } from '@/utils/logger';
import { AnalysisResults } from '@/services/aiAnalysisService';

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  sections: ReportSection[];
  customization: TemplateCustomization;
}

export interface ReportSection {
  id: string;
  title: string;
  type: 'summary' | 'analysis' | 'visualization' | 'recommendations' | 'appendix';
  required: boolean;
  order: number;
}

export interface TemplateCustomization {
  logo?: string;
  companyName?: string;
  brandColors?: {
    primary: string;
    secondary: string;
    accent: string;
  };
  fonts?: {
    heading: string;
    body: string;
  };
}

export interface ReportConfig {
  templateId: string;
  analysisId: string;
  customization?: TemplateCustomization;
  includeRawData?: boolean;
  includeCharts?: boolean;
  include3DVisualization?: boolean;
  format?: 'pdf' | 'html';
  quality?: 'draft' | 'standard' | 'high';
}

export interface ReportGenerationResult {
  reportId: string;
  filePath: string;
  fileSize: number;
  pageCount: number;
  generationTime: number;
  sections: GeneratedSection[];
  metadata: ReportMetadata;
}

export interface GeneratedSection {
  sectionId: string;
  title: string;
  pageStart: number;
  pageEnd: number;
  confidence: number;
}

export interface ReportMetadata {
  generatedAt: string;
  analysisData: {
    cabinetCount: number;
    confidence: number;
    processingTime: number;
  };
  reportVersion: string;
  templateVersion: string;
}

export interface ReportSchedule {
  id: string;
  analysisId: string;
  templateId: string;
  frequency: 'once' | 'daily' | 'weekly' | 'monthly';
  nextRun: Date;
  recipients: string[];
  active: boolean;
}

/**
 * Enhanced Reporting Service
 * 
 * Provides comprehensive PDF report generation with all Priority 1 & 2 analysis data
 */
export class EnhancedReportingService {
  private reportTemplates: Map<string, ReportTemplate> = new Map();
  private reportHistory: Map<string, ReportGenerationResult> = new Map();
  private scheduledReports: Map<string, ReportSchedule> = new Map();

  constructor() {
    this.initializeDefaultTemplates();
  }

  /**
   * Initialize default report templates
   */
  private initializeDefaultTemplates(): void {
    // Basic Template
    const basicTemplate: ReportTemplate = {
      id: 'basic',
      name: 'Basic Analysis Report',
      description: 'Essential analysis results with cabinet count and measurements',
      sections: [
        { id: 'summary', title: 'Executive Summary', type: 'summary', required: true, order: 1 },
        { id: 'cabinets', title: 'Cabinet Analysis', type: 'analysis', required: true, order: 2 },
        { id: 'measurements', title: 'Measurements', type: 'analysis', required: true, order: 3 }
      ],
      customization: {
        brandColors: {
          primary: '#2563eb',
          secondary: '#64748b',
          accent: '#0ea5e9'
        },
        fonts: {
          heading: 'Inter',
          body: 'Inter'
        }
      }
    };

    // Detailed Template
    const detailedTemplate: ReportTemplate = {
      id: 'detailed',
      name: 'Detailed Analysis Report',
      description: 'Comprehensive analysis with 3D visualization and material details',
      sections: [
        { id: 'summary', title: 'Executive Summary', type: 'summary', required: true, order: 1 },
        { id: 'cabinets', title: 'Cabinet Analysis', type: 'analysis', required: true, order: 2 },
        { id: '3d-visualization', title: '3D Reconstruction', type: 'visualization', required: false, order: 3 },
        { id: 'measurements', title: 'Intelligent Measurements', type: 'analysis', required: true, order: 4 },
        { id: 'hardware', title: 'Hardware Recognition', type: 'analysis', required: true, order: 5 },
        { id: 'materials', title: 'Material Analysis', type: 'analysis', required: false, order: 6 },
        { id: 'layout', title: 'Layout Optimization', type: 'recommendations', required: false, order: 7 },
        { id: 'appendix', title: 'Technical Details', type: 'appendix', required: false, order: 8 }
      ],
      customization: {
        brandColors: {
          primary: '#1e40af',
          secondary: '#475569',
          accent: '#0284c7'
        },
        fonts: {
          heading: 'Inter',
          body: 'Inter'
        }
      }
    };

    // Professional Template
    const professionalTemplate: ReportTemplate = {
      id: 'professional',
      name: 'Professional Analysis Report',
      description: 'Executive-level report with cost estimates and recommendations',
      sections: [
        { id: 'cover', title: 'Cover Page', type: 'summary', required: true, order: 1 },
        { id: 'executive-summary', title: 'Executive Summary', type: 'summary', required: true, order: 2 },
        { id: 'methodology', title: 'Analysis Methodology', type: 'analysis', required: true, order: 3 },
        { id: 'cabinets', title: 'Cabinet Analysis', type: 'analysis', required: true, order: 4 },
        { id: '3d-visualization', title: '3D Spatial Analysis', type: 'visualization', required: true, order: 5 },
        { id: 'measurements', title: 'Precision Measurements', type: 'analysis', required: true, order: 6 },
        { id: 'hardware', title: 'Hardware Assessment', type: 'analysis', required: true, order: 7 },
        { id: 'materials', title: 'Material & Cost Analysis', type: 'analysis', required: true, order: 8 },
        { id: 'layout', title: 'Layout Optimization', type: 'recommendations', required: true, order: 9 },
        { id: 'recommendations', title: 'Strategic Recommendations', type: 'recommendations', required: true, order: 10 },
        { id: 'appendix', title: 'Technical Appendix', type: 'appendix', required: true, order: 11 }
      ],
      customization: {
        brandColors: {
          primary: '#1e3a8a',
          secondary: '#374151',
          accent: '#059669'
        },
        fonts: {
          heading: 'Inter',
          body: 'Inter'
        }
      }
    };

    this.reportTemplates.set('basic', basicTemplate);
    this.reportTemplates.set('detailed', detailedTemplate);
    this.reportTemplates.set('professional', professionalTemplate);

    logger.info('Initialized default report templates', {
      templateCount: this.reportTemplates.size,
      templates: Array.from(this.reportTemplates.keys())
    });
  }

  /**
   * Generate comprehensive PDF report
   */
  async generateReport(
    analysisResults: AnalysisResults,
    config: ReportConfig
  ): Promise<ReportGenerationResult> {
    const startTime = Date.now();
    const reportId = `report_${config.analysisId}_${Date.now()}`;

    logger.info('Starting report generation', {
      reportId,
      analysisId: config.analysisId,
      templateId: config.templateId,
      quality: config.quality || 'standard'
    });

    try {
      // Get template
      const template = this.reportTemplates.get(config.templateId);
      if (!template) {
        throw new Error(`Template not found: ${config.templateId}`);
      }

      // Generate HTML content
      const htmlContent = await this.generateHTMLContent(analysisResults, template, config);

      // Generate PDF from HTML
      const pdfResult = await this.generatePDFFromHTML(htmlContent, reportId, config);

      // Calculate metadata
      const generationTime = Date.now() - startTime;
      const metadata: ReportMetadata = {
        generatedAt: new Date().toISOString(),
        analysisData: {
          cabinetCount: analysisResults.measurements.totalCabinets || 0,
          confidence: analysisResults.confidence.overall,
          processingTime: analysisResults.processingTime
        },
        reportVersion: '1.0.0',
        templateVersion: '1.0.0'
      };

      const result: ReportGenerationResult = {
        reportId,
        filePath: pdfResult.filePath,
        fileSize: pdfResult.fileSize,
        pageCount: pdfResult.pageCount,
        generationTime,
        sections: pdfResult.sections,
        metadata
      };

      // Store in history
      this.reportHistory.set(reportId, result);

      logger.info('Report generation completed', {
        reportId,
        generationTime,
        fileSize: pdfResult.fileSize,
        pageCount: pdfResult.pageCount
      });

      return result;

    } catch (error) {
      logger.error('Report generation failed', {
        reportId,
        analysisId: config.analysisId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Generate HTML content for report
   */
  private async generateHTMLContent(
    analysisResults: AnalysisResults,
    template: ReportTemplate,
    config: ReportConfig
  ): Promise<string> {
    const customization = { ...template.customization, ...config.customization };

    // Sort sections by order
    const sortedSections = template.sections.sort((a, b) => a.order - b.order);

    let htmlContent = this.generateHTMLHeader(customization);

    for (const section of sortedSections) {
      if (section.required || this.shouldIncludeSection(section, config)) {
        htmlContent += await this.generateSectionHTML(section, analysisResults, customization, config);
      }
    }

    htmlContent += this.generateHTMLFooter();

    return htmlContent;
  }

  /**
   * Generate HTML header with styles
   */
  private generateHTMLHeader(customization: TemplateCustomization): string {
    const colors = customization.brandColors || {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#0ea5e9'
    };

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cabinet Insight Pro - Analysis Report</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '${customization.fonts?.body || 'Inter'}', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #374151;
            background: #ffffff;
        }

        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: '${customization.fonts?.heading || 'Inter'}', sans-serif;
            color: ${colors.primary};
            margin-bottom: 1rem;
        }

        h1 { font-size: 2.5rem; font-weight: 700; }
        h2 { font-size: 2rem; font-weight: 600; }
        h3 { font-size: 1.5rem; font-weight: 600; }
        h4 { font-size: 1.25rem; font-weight: 500; }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 3px solid ${colors.primary};
        }

        .logo {
            max-width: 200px;
            margin-bottom: 1rem;
        }

        .company-name {
            font-size: 1.5rem;
            color: ${colors.secondary};
            margin-bottom: 0.5rem;
        }

        .report-title {
            color: ${colors.primary};
            margin-bottom: 0.5rem;
        }

        .report-date {
            color: ${colors.secondary};
            font-size: 1rem;
        }

        .section {
            margin-bottom: 3rem;
            page-break-inside: avoid;
        }

        .section-header {
            background: linear-gradient(135deg, ${colors.primary}, ${colors.accent});
            color: white;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            border-radius: 8px;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: ${colors.primary};
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: ${colors.secondary};
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .confidence-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 500;
            margin-left: 0.5rem;
        }

        .confidence-high { background: #dcfce7; color: #166534; }
        .confidence-medium { background: #fef3c7; color: #92400e; }
        .confidence-low { background: #fee2e2; color: #991b1b; }

        .chart-container {
            margin: 2rem 0;
            text-align: center;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5rem 0;
        }

        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .table th {
            background: ${colors.primary};
            color: white;
            font-weight: 600;
        }

        .table tr:nth-child(even) {
            background: #f8fafc;
        }

        .page-break {
            page-break-before: always;
        }

        .footer {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid #e2e8f0;
            text-align: center;
            color: ${colors.secondary};
            font-size: 0.875rem;
        }

        @media print {
            body { print-color-adjust: exact; }
            .container { margin: 0; padding: 15mm; }
            .page-break { page-break-before: always; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            ${customization.logo ? `<img src="${customization.logo}" alt="Logo" class="logo">` : ''}
            ${customization.companyName ? `<div class="company-name">${customization.companyName}</div>` : ''}
            <h1 class="report-title">Cabinet Insight Pro Analysis Report</h1>
            <div class="report-date">Generated on ${new Date().toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}</div>
        </div>
`;
  }

  /**
   * Generate HTML footer
   */
  private generateHTMLFooter(): string {
    return `
        <div class="footer">
            <p>Generated by Cabinet Insight Pro - Advanced AI-Powered Kitchen Design Analysis</p>
            <p>© ${new Date().getFullYear()} Cabinet Insight Pro. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
`;
  }

  /**
   * Check if section should be included based on config
   */
  private shouldIncludeSection(section: ReportSection, config: ReportConfig): boolean {
    switch (section.id) {
      case '3d-visualization':
        return config.include3DVisualization !== false;
      case 'materials':
      case 'layout':
        return true; // Always include Priority 2 features
      default:
        return true;
    }
  }

  /**
   * Generate HTML for a specific section
   */
  private async generateSectionHTML(
    section: ReportSection,
    analysisResults: AnalysisResults,
    customization: TemplateCustomization,
    config: ReportConfig
  ): Promise<string> {
    switch (section.id) {
      case 'summary':
      case 'executive-summary':
        return this.generateSummarySection(analysisResults);
      case 'cabinets':
        return this.generateCabinetSection(analysisResults);
      case 'measurements':
        return this.generateMeasurementSection(analysisResults);
      case 'hardware':
        return this.generateHardwareSection(analysisResults);
      case 'materials':
        return this.generateMaterialSection(analysisResults);
      case 'layout':
        return this.generateLayoutSection(analysisResults);
      case '3d-visualization':
        return await this.generate3DVisualizationSection(analysisResults, config);
      case 'recommendations':
        return this.generateRecommendationsSection(analysisResults);
      case 'appendix':
        return this.generateAppendixSection(analysisResults);
      default:
        return `<div class="section"><h2>${section.title}</h2><p>Section content not implemented.</p></div>`;
    }
  }

  /**
   * Generate summary section
   */
  private generateSummarySection(analysisResults: AnalysisResults): string {
    const confidence = this.getConfidenceClass(analysisResults.confidence.overall);

    return `
<div class="section">
    <div class="section-header">
        <h2>Executive Summary</h2>
    </div>

    <div class="metric-grid">
        <div class="metric-card">
            <div class="metric-value">${analysisResults.measurements.totalCabinets || 0}</div>
            <div class="metric-label">Total Cabinets</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${Math.round(analysisResults.confidence.overall * 100)}%</div>
            <div class="metric-label">Analysis Confidence</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${Math.round(analysisResults.processingTime / 1000)}s</div>
            <div class="metric-label">Processing Time</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${analysisResults.cabinets?.length || 0}</div>
            <div class="metric-label">Identified Units</div>
        </div>
    </div>

    <h3>Analysis Overview</h3>
    <p>This comprehensive analysis was performed using Cabinet Insight Pro's advanced AI-powered analysis engine,
    featuring GPT-4o and o4-mini models for superior accuracy and detailed insights.</p>

    <h4>Key Findings:</h4>
    <ul>
        <li><strong>Cabinet Count:</strong> ${analysisResults.measurements.totalCabinets || 0} total cabinets identified with ${confidence} confidence</li>
        <li><strong>Cabinet Types:</strong> ${Object.entries(analysisResults.measurements.cabinetsByType || {}).map(([type, count]) => `${count} ${type.toLowerCase()}`).join(', ')}</li>
        <li><strong>Style Analysis:</strong> ${analysisResults.style?.primary || 'Not determined'} style detected</li>
        <li><strong>Hardware Recognition:</strong> ${analysisResults.hardware?.length || 0} hardware items identified</li>
    </ul>
</div>
`;
  }

  /**
   * Generate cabinet analysis section
   */
  private generateCabinetSection(analysisResults: AnalysisResults): string {
    const cabinets = analysisResults.cabinets || [];

    let cabinetTableRows = '';
    cabinets.forEach((cabinet, index) => {
      cabinetTableRows += `
        <tr>
            <td>${index + 1}</td>
            <td>${cabinet.type}</td>
            <td>${cabinet.dimensions?.width || 'N/A'}"</td>
            <td>${cabinet.dimensions?.height || 'N/A'}"</td>
            <td>${cabinet.dimensions?.depth || 'N/A'}"</td>
            <td><span class="confidence-badge ${this.getConfidenceClass(cabinet.confidence)}">${Math.round(cabinet.confidence * 100)}%</span></td>
        </tr>
      `;
    });

    return `
<div class="section">
    <div class="section-header">
        <h2>Cabinet Analysis</h2>
    </div>

    <h3>Cabinet Inventory</h3>
    <table class="table">
        <thead>
            <tr>
                <th>#</th>
                <th>Type</th>
                <th>Width</th>
                <th>Height</th>
                <th>Depth</th>
                <th>Confidence</th>
            </tr>
        </thead>
        <tbody>
            ${cabinetTableRows}
        </tbody>
    </table>

    <h3>Cabinet Distribution</h3>
    <div class="metric-grid">
        ${Object.entries(analysisResults.measurements.cabinetsByType || {}).map(([type, count]) => `
            <div class="metric-card">
                <div class="metric-value">${count}</div>
                <div class="metric-label">${type.charAt(0).toUpperCase() + type.slice(1)} Cabinets</div>
            </div>
        `).join('')}
    </div>
</div>
`;
  }

  /**
   * Generate measurement section
   */
  private generateMeasurementSection(analysisResults: AnalysisResults): string {
    const measurements = analysisResults.measurements;

    return `
<div class="section">
    <div class="section-header">
        <h2>Intelligent Measurements</h2>
    </div>

    <h3>Overall Dimensions</h3>
    <div class="metric-grid">
        <div class="metric-card">
            <div class="metric-value">${measurements.overallWidth || 'N/A'}"</div>
            <div class="metric-label">Overall Width</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${measurements.overallHeight || 'N/A'}"</div>
            <div class="metric-label">Overall Height</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${measurements.overallDepth || 'N/A'}"</div>
            <div class="metric-label">Overall Depth</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${measurements.totalLinearFeet || 'N/A'}</div>
            <div class="metric-label">Linear Feet</div>
        </div>
    </div>

    <h3>Measurement Analysis</h3>
    <p>Our intelligent measurement system uses advanced AI to automatically detect and validate dimensions
    from your kitchen images. The system cross-references multiple measurement points to ensure accuracy.</p>

    ${measurements.notes ? `
    <h4>Measurement Notes:</h4>
    <p>${measurements.notes}</p>
    ` : ''}
</div>
`;
  }

  /**
   * Generate hardware section
   */
  private generateHardwareSection(analysisResults: AnalysisResults): string {
    const hardware = analysisResults.hardware || [];

    let hardwareTableRows = '';
    hardware.forEach((item, index) => {
      hardwareTableRows += `
        <tr>
            <td>${index + 1}</td>
            <td>${item.type}</td>
            <td>${item.brand || 'Unknown'}</td>
            <td>${item.model || 'N/A'}</td>
            <td><span class="confidence-badge ${this.getConfidenceClass(item.confidence)}">${Math.round(item.confidence * 100)}%</span></td>
        </tr>
      `;
    });

    return `
<div class="section">
    <div class="section-header">
        <h2>Enhanced Smart Hardware Recognition</h2>
    </div>

    <h3>Identified Hardware</h3>
    <table class="table">
        <thead>
            <tr>
                <th>#</th>
                <th>Type</th>
                <th>Brand</th>
                <th>Model</th>
                <th>Confidence</th>
            </tr>
        </thead>
        <tbody>
            ${hardwareTableRows}
        </tbody>
    </table>

    <h3>Hardware Summary</h3>
    <p>Our enhanced smart hardware recognition system identified ${hardware.length} hardware items using
    our comprehensive database of major brands including Blum, Hettich, Grass, Salice, and Hafele.</p>
</div>
`;
  }

  /**
   * Generate material section (Priority 2 Feature 1)
   */
  private generateMaterialSection(analysisResults: AnalysisResults): string {
    // Access Priority 2 material analysis data
    const materialAnalysis = (analysisResults as any).materialAnalysis;

    if (!materialAnalysis) {
      return `
<div class="section">
    <div class="section-header">
        <h2>Advanced Material Recognition & Cost Estimation</h2>
    </div>
    <p>Material analysis data not available for this report.</p>
</div>
`;
    }

    return `
<div class="section">
    <div class="section-header">
        <h2>Advanced Material Recognition & Cost Estimation</h2>
    </div>

    <h3>Material Analysis</h3>
    <div class="metric-grid">
        <div class="metric-card">
            <div class="metric-value">${materialAnalysis.primaryMaterial || 'Unknown'}</div>
            <div class="metric-label">Primary Material</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${materialAnalysis.qualityScore || 'N/A'}</div>
            <div class="metric-label">Quality Score</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">$${materialAnalysis.estimatedCost || 'N/A'}</div>
            <div class="metric-label">Estimated Cost</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${Math.round((materialAnalysis.confidence || 0) * 100)}%</div>
            <div class="metric-label">Analysis Confidence</div>
        </div>
    </div>

    <h3>Cost Breakdown by Region</h3>
    <p>Cost estimates are provided for major US markets with regional pricing variations.</p>

    <h3>Material Recommendations</h3>
    <p>Based on the analysis, we recommend considering alternative materials that may provide
    better value or enhanced durability for your specific application.</p>
</div>
`;
  }

  /**
   * Generate layout optimization section (Priority 2 Feature 2)
   */
  private generateLayoutSection(analysisResults: AnalysisResults): string {
    // Access Priority 2 layout optimization data
    const layoutOptimization = (analysisResults as any).layoutOptimization;

    if (!layoutOptimization) {
      return `
<div class="section">
    <div class="section-header">
        <h2>Smart Layout Optimization</h2>
    </div>
    <p>Layout optimization data not available for this report.</p>
</div>
`;
    }

    return `
<div class="section">
    <div class="section-header">
        <h2>Smart Layout Optimization</h2>
    </div>

    <h3>Workflow Analysis</h3>
    <div class="metric-grid">
        <div class="metric-card">
            <div class="metric-value">${layoutOptimization.workflowScore || 'N/A'}</div>
            <div class="metric-label">Workflow Efficiency</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${layoutOptimization.spaceUtilization || 'N/A'}%</div>
            <div class="metric-label">Space Utilization</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${layoutOptimization.ergonomicScore || 'N/A'}</div>
            <div class="metric-label">Ergonomic Score</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${layoutOptimization.trafficFlowRating || 'N/A'}</div>
            <div class="metric-label">Traffic Flow</div>
        </div>
    </div>

    <h3>Optimization Recommendations</h3>
    <p>Our smart layout optimization system has analyzed your current kitchen configuration and
    identified opportunities for improved workflow, space utilization, and ergonomic efficiency.</p>

    <h3>Cost-Benefit Analysis</h3>
    <p>Implementing the recommended layout optimizations could result in improved functionality
    and potential cost savings through more efficient space utilization.</p>
</div>
`;
  }

  /**
   * Generate 3D visualization section (Priority 1 Feature 1)
   */
  private async generate3DVisualizationSection(analysisResults: AnalysisResults, config: ReportConfig): Promise<string> {
    // Access Priority 1 3D reconstruction data
    const reconstruction3D = (analysisResults as any).reconstruction3D;

    if (!reconstruction3D) {
      return `
<div class="section">
    <div class="section-header">
        <h2>3D Cabinet Reconstruction</h2>
    </div>
    <p>3D reconstruction data not available for this report.</p>
</div>
`;
    }

    // Note: In a real implementation, we would capture a screenshot of the 3D visualization
    // For now, we'll include placeholder content
    return `
<div class="section page-break">
    <div class="section-header">
        <h2>3D Cabinet Reconstruction</h2>
    </div>

    <h3>Spatial Analysis</h3>
    <div class="metric-grid">
        <div class="metric-card">
            <div class="metric-value">${Math.round((reconstruction3D.confidence || 0) * 100)}%</div>
            <div class="metric-label">Reconstruction Confidence</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${reconstruction3D.cabinetCount || 0}</div>
            <div class="metric-label">3D Models Generated</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${reconstruction3D.spatialAccuracy || 'N/A'}%</div>
            <div class="metric-label">Spatial Accuracy</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${reconstruction3D.processingTime || 'N/A'}s</div>
            <div class="metric-label">Processing Time</div>
        </div>
    </div>

    <h3>3D Model Analysis</h3>
    <p>Our advanced 3D reconstruction system has generated detailed spatial models of your kitchen cabinets,
    providing accurate positioning and dimensional analysis for comprehensive design planning.</p>

    <div class="chart-container">
        <p><em>3D visualization screenshot would be embedded here in the full implementation.</em></p>
    </div>
</div>
`;
  }

  /**
   * Generate recommendations section
   */
  private generateRecommendationsSection(analysisResults: AnalysisResults): string {
    return `
<div class="section">
    <div class="section-header">
        <h2>Strategic Recommendations</h2>
    </div>

    <h3>Priority Recommendations</h3>
    <ol>
        <li><strong>Cabinet Optimization:</strong> Based on the analysis of ${analysisResults.measurements.totalCabinets || 0} cabinets,
        consider optimizing storage efficiency through better organization systems.</li>
        <li><strong>Hardware Upgrades:</strong> ${analysisResults.hardware?.length || 0} hardware items were identified.
        Consider upgrading to modern soft-close mechanisms for improved functionality.</li>
        <li><strong>Layout Improvements:</strong> Workflow analysis suggests potential improvements in traffic flow
        and work triangle efficiency.</li>
        <li><strong>Material Considerations:</strong> Material analysis indicates opportunities for enhanced
        durability and aesthetic appeal through strategic upgrades.</li>
    </ol>

    <h3>Implementation Timeline</h3>
    <p>We recommend implementing these improvements in phases to minimize disruption and optimize budget allocation.</p>
</div>
`;
  }

  /**
   * Generate appendix section
   */
  private generateAppendixSection(analysisResults: AnalysisResults): string {
    return `
<div class="section page-break">
    <div class="section-header">
        <h2>Technical Appendix</h2>
    </div>

    <h3>Analysis Methodology</h3>
    <p>This analysis was performed using Cabinet Insight Pro's advanced AI-powered analysis engine,
    featuring the following technologies:</p>

    <ul>
        <li><strong>Azure OpenAI GPT-4o:</strong> Primary analysis model for comprehensive kitchen design evaluation</li>
        <li><strong>Azure OpenAI o4-mini:</strong> Reasoning validation and quality assurance</li>
        <li><strong>3D Reconstruction Engine:</strong> Advanced spatial analysis and cabinet positioning</li>
        <li><strong>Intelligent Measurement System:</strong> Auto-scale detection and dimensional analysis</li>
        <li><strong>Smart Hardware Recognition:</strong> Comprehensive brand and model identification</li>
        <li><strong>Material Recognition System:</strong> Advanced material classification and cost estimation</li>
        <li><strong>Layout Optimization Engine:</strong> Workflow and ergonomic analysis</li>
    </ul>

    <h3>Confidence Scoring</h3>
    <p>All analysis results include confidence scores based on multiple validation factors:</p>
    <ul>
        <li><strong>High (80-100%):</strong> Multiple validation points confirm accuracy</li>
        <li><strong>Medium (60-79%):</strong> Good confidence with minor uncertainties</li>
        <li><strong>Low (0-59%):</strong> Limited data or conflicting indicators</li>
    </ul>

    <h3>Technical Specifications</h3>
    <table class="table">
        <tr><td>Analysis Engine Version</td><td>2.0.0</td></tr>
        <tr><td>Processing Time</td><td>${Math.round(analysisResults.processingTime / 1000)} seconds</td></tr>
        <tr><td>Overall Confidence</td><td>${Math.round(analysisResults.confidence.overall * 100)}%</td></tr>
        <tr><td>Generated</td><td>${new Date().toISOString()}</td></tr>
    </table>
</div>
`;
  }

  /**
   * Generate PDF from HTML content using Puppeteer
   */
  private async generatePDFFromHTML(
    htmlContent: string,
    reportId: string,
    config: ReportConfig
  ): Promise<{
    filePath: string;
    fileSize: number;
    pageCount: number;
    sections: GeneratedSection[];
  }> {
    const outputDir = path.join(process.cwd(), 'reports');
    await fs.promises.mkdir(outputDir, { recursive: true });

    const filePath = path.join(outputDir, `${reportId}.pdf`);

    let browser;
    try {
      // Launch Puppeteer browser
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });

      const page = await browser.newPage();

      // Set content and wait for any dynamic content to load
      await page.setContent(htmlContent, { waitUntil: 'networkidle0' });

      // Configure PDF options based on quality setting
      const pdfOptions: any = {
        path: filePath,
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20mm',
          right: '15mm',
          bottom: '20mm',
          left: '15mm'
        }
      };

      // Adjust quality settings
      if (config.quality === 'high') {
        pdfOptions.preferCSSPageSize = true;
        pdfOptions.displayHeaderFooter = true;
        pdfOptions.headerTemplate = '<div></div>';
        pdfOptions.footerTemplate = `
          <div style="font-size: 10px; text-align: center; width: 100%; color: #666;">
            Page <span class="pageNumber"></span> of <span class="totalPages"></span>
          </div>
        `;
      }

      // Generate PDF
      await page.pdf(pdfOptions);

      // Get file stats
      const stats = await fs.promises.stat(filePath);

      // Calculate page count (approximate based on content length)
      const pageCount = Math.max(1, Math.ceil(htmlContent.length / 5000));

      // Generate section information (simplified for now)
      const sections: GeneratedSection[] = [
        { sectionId: 'summary', title: 'Executive Summary', pageStart: 1, pageEnd: 1, confidence: 1.0 },
        { sectionId: 'cabinets', title: 'Cabinet Analysis', pageStart: 2, pageEnd: 3, confidence: 0.95 },
        { sectionId: 'measurements', title: 'Measurements', pageStart: 4, pageEnd: 4, confidence: 0.90 }
      ];

      return {
        filePath,
        fileSize: stats.size,
        pageCount,
        sections
      };

    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Get confidence CSS class
   */
  private getConfidenceClass(confidence: number): string {
    if (confidence >= 0.8) return 'confidence-high';
    if (confidence >= 0.6) return 'confidence-medium';
    return 'confidence-low';
  }

  /**
   * Get available report templates
   */
  getTemplates(): ReportTemplate[] {
    return Array.from(this.reportTemplates.values());
  }

  /**
   * Get report generation history
   */
  getReportHistory(): ReportGenerationResult[] {
    return Array.from(this.reportHistory.values());
  }

  /**
   * Schedule report generation
   */
  async scheduleReport(schedule: Omit<ReportSchedule, 'id'>): Promise<string> {
    const scheduleId = `schedule_${Date.now()}`;
    const fullSchedule: ReportSchedule = {
      id: scheduleId,
      ...schedule
    };

    this.scheduledReports.set(scheduleId, fullSchedule);

    logger.info('Report scheduled', {
      scheduleId,
      analysisId: schedule.analysisId,
      frequency: schedule.frequency,
      nextRun: schedule.nextRun
    });

    return scheduleId;
  }

  /**
   * Get scheduled reports
   */
  getScheduledReports(): ReportSchedule[] {
    return Array.from(this.scheduledReports.values());
  }

  /**
   * Cancel scheduled report
   */
  cancelScheduledReport(scheduleId: string): boolean {
    const deleted = this.scheduledReports.delete(scheduleId);
    if (deleted) {
      logger.info('Scheduled report cancelled', { scheduleId });
    }
    return deleted;
  }

  /**
   * Get report by ID
   */
  getReport(reportId: string): ReportGenerationResult | undefined {
    return this.reportHistory.get(reportId);
  }

  /**
   * Delete report
   */
  async deleteReport(reportId: string): Promise<boolean> {
    const report = this.reportHistory.get(reportId);
    if (!report) {
      return false;
    }

    try {
      // Delete file
      await fs.promises.unlink(report.filePath);

      // Remove from history
      this.reportHistory.delete(reportId);

      logger.info('Report deleted', { reportId, filePath: report.filePath });
      return true;

    } catch (error) {
      logger.error('Failed to delete report file', {
        reportId,
        filePath: report.filePath,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Get report statistics
   */
  getReportStatistics(): {
    totalReports: number;
    totalSize: number;
    averageGenerationTime: number;
    templateUsage: Record<string, number>;
  } {
    const reports = Array.from(this.reportHistory.values());

    const templateUsage: Record<string, number> = {};
    let totalSize = 0;
    let totalGenerationTime = 0;

    reports.forEach(report => {
      totalSize += report.fileSize;
      totalGenerationTime += report.generationTime;

      // Extract template from report metadata (simplified)
      const templateId = 'unknown'; // Would be stored in metadata
      templateUsage[templateId] = (templateUsage[templateId] || 0) + 1;
    });

    return {
      totalReports: reports.length,
      totalSize,
      averageGenerationTime: reports.length > 0 ? totalGenerationTime / reports.length : 0,
      templateUsage
    };
  }
}
