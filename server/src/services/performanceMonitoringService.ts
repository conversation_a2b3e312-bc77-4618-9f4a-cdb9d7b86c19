import { Pool } from 'pg';
import Redis from 'ioredis';
import { SocketManager } from './socketManager';
import { logger } from '../utils/logger';
import { EventEmitter } from 'events';

// Phase 3.1: Async/Await Pattern Standardization
import { AsyncOperationManager, AsyncOperationConfig } from './documentIntelligence/utils/AsyncOperationManager';
import { PerformanceTracker } from './documentIntelligence/utils/PerformanceTracker';

export interface TestMetrics {
  id: string;
  testName: string;
  testCategory: 'api' | 'ui' | 'integration' | 'cross-browser' | 'mobile' | 'performance';
  browser: string;
  duration: number;
  success: boolean;
  retryCount: number;
  errorType?: string;
  errorMessage?: string;
  timestamp: Date;
  featureVersion?: string;
  buildId?: string;
  networkCondition?: {
    quality: 'excellent' | 'good' | 'fair' | 'poor';
    latency: number;
    bandwidth: number;
    packetLoss: number;
  };
  resourceUsage?: {
    memoryUsage: number;
    cpuUsage: number;
    diskUsage: number;
  };
  metadata?: Record<string, any>;
}

export interface TestSuiteResult {
  id: string;
  suiteId: string;
  suiteName: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  successRate: number;
  duration: number;
  timestamp: Date;
  featureVersion?: string;
  buildId?: string;
  browser: string;
  testCategory: string;
}

export interface PerformanceAlert {
  id: string;
  type: 'success_rate_drop' | 'test_failure_spike' | 'performance_degradation' | 'feature_impact';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  currentValue: number;
  thresholdValue: number;
  affectedTests: string[];
  timestamp: Date;
  resolved: boolean;
  featureVersion?: string;
}

export interface FeatureImpactAnalysis {
  featureVersion: string;
  deploymentDate: Date;
  baselineMetrics: {
    successRate: number;
    averageDuration: number;
    totalTests: number;
  };
  currentMetrics: {
    successRate: number;
    averageDuration: number;
    totalTests: number;
  };
  impact: {
    successRateChange: number;
    durationChange: number;
    newFailures: string[];
    improvedTests: string[];
  };
  recommendation: 'proceed' | 'investigate' | 'rollback';
}

export class PerformanceMonitoringService extends EventEmitter {
  private static instance: PerformanceMonitoringService;
  private db: Pool | null = null;
  private redis: Redis | null = null;
  private wsManager: SocketManager | null = null;
  private alertThresholds = {
    successRate: 91.7, // Critical 91.7% threshold
    successRateWarning: 93.0, // Warning threshold
    maxFailureSpike: 5, // Max new failures in 1 hour
    maxDurationIncrease: 50, // Max 50% duration increase

    // Phase 3: Enterprise Scalability thresholds (backward compatible)
    enterpriseSuccessRate: 97.0, // Enterprise-grade 97% threshold
    scalabilityResponseTime: 5000, // Max 5 second response time under load
    concurrentUserThreshold: 1000, // Target concurrent user capacity
    apiCallReductionTarget: 70, // Target 70% API call reduction
    systemResourceThreshold: 85, // Max 85% system resource usage
  };

  // Phase 3.1: Async/Await Pattern Standardization
  private asyncManager: AsyncOperationManager;
  private performanceTracker: PerformanceTracker;
  private monitoringIntervals: NodeJS.Timeout[] = [];

  private constructor() {
    super();

    // Phase 3.1: Initialize async operation management
    this.asyncManager = new AsyncOperationManager({
      timeout: 15000, // 15 seconds for database operations
      maxRetries: 2,
      retryDelayBase: 1000,
      maxRetryDelay: 5000,
      concurrencyLimit: 10 // Allow multiple concurrent operations
    });

    this.performanceTracker = new PerformanceTracker(2000); // Track last 2000 operations

    this.initializeDatabase();
    this.initializeRedis();
    this.startPerformanceMonitoring();
  }

  public static getInstance(): PerformanceMonitoringService {
    if (!PerformanceMonitoringService.instance) {
      PerformanceMonitoringService.instance = new PerformanceMonitoringService();
    }
    return PerformanceMonitoringService.instance;
  }

  /**
   * Set the SocketManager instance for WebSocket communication
   */
  public setSocketManager(socketManager: SocketManager): void {
    this.wsManager = socketManager;
    logger.info('SocketManager set for performance monitoring');
  }

  private async initializeDatabase(): Promise<void> {
    try {
      this.db = new Pool({
        connectionString: process.env.DATABASE_URL || 'postgresql://postgres:cabinet_insight_secure_2024@localhost:5432/cabinet_insight_pro',
        max: 20,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
      });

      // Create performance monitoring tables
      await this.createPerformanceTables();
      logger.info('Performance monitoring database initialized');
    } catch (error) {
      logger.warn('Failed to initialize performance monitoring database, continuing without database persistence:', error.message);
      this.db = null; // Disable database operations
    }
  }

  private async initializeRedis(): Promise<void> {
    try {
      this.redis = new Redis({
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        keyPrefix: 'perf_monitor:',
        lazyConnect: true, // Don't connect immediately
      });

      // Test the connection
      await this.redis.ping();
      logger.info('Performance monitoring Redis initialized');
    } catch (error) {
      logger.warn('Failed to initialize performance monitoring Redis, continuing without Redis caching:', error.message);
      this.redis = null; // Disable Redis operations
    }
  }

  private async createPerformanceTables(): Promise<void> {
    const createTablesSQL = `
      -- Test metrics table
      CREATE TABLE IF NOT EXISTS test_metrics (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        test_name VARCHAR(255) NOT NULL,
        test_category VARCHAR(50) NOT NULL,
        browser VARCHAR(50) NOT NULL,
        duration INTEGER NOT NULL,
        success BOOLEAN NOT NULL,
        retry_count INTEGER DEFAULT 0,
        error_type VARCHAR(100),
        error_message TEXT,
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        feature_version VARCHAR(50),
        build_id VARCHAR(100),
        network_condition JSONB,
        resource_usage JSONB,
        metadata JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Test suite results table
      CREATE TABLE IF NOT EXISTS test_suite_results (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        suite_id VARCHAR(255) NOT NULL,
        suite_name VARCHAR(255) NOT NULL,
        total_tests INTEGER NOT NULL,
        passed_tests INTEGER NOT NULL,
        failed_tests INTEGER NOT NULL,
        skipped_tests INTEGER NOT NULL,
        success_rate DECIMAL(5,2) NOT NULL,
        duration INTEGER NOT NULL,
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        feature_version VARCHAR(50),
        build_id VARCHAR(100),
        browser VARCHAR(50) NOT NULL,
        test_category VARCHAR(50) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Performance alerts table
      CREATE TABLE IF NOT EXISTS performance_alerts (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        alert_type VARCHAR(50) NOT NULL,
        severity VARCHAR(20) NOT NULL,
        message TEXT NOT NULL,
        current_value DECIMAL(10,2) NOT NULL,
        threshold_value DECIMAL(10,2) NOT NULL,
        affected_tests TEXT[],
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        resolved BOOLEAN DEFAULT FALSE,
        resolved_at TIMESTAMP WITH TIME ZONE,
        feature_version VARCHAR(50),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Feature impact analysis table
      CREATE TABLE IF NOT EXISTS feature_impact_analysis (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        feature_version VARCHAR(50) NOT NULL,
        deployment_date TIMESTAMP WITH TIME ZONE NOT NULL,
        baseline_metrics JSONB NOT NULL,
        current_metrics JSONB NOT NULL,
        impact_analysis JSONB NOT NULL,
        recommendation VARCHAR(20) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Create indexes for performance
      CREATE INDEX IF NOT EXISTS idx_test_metrics_timestamp ON test_metrics(timestamp);
      CREATE INDEX IF NOT EXISTS idx_test_metrics_success ON test_metrics(success);
      CREATE INDEX IF NOT EXISTS idx_test_metrics_category ON test_metrics(test_category);
      CREATE INDEX IF NOT EXISTS idx_test_metrics_feature_version ON test_metrics(feature_version);
      CREATE INDEX IF NOT EXISTS idx_test_suite_results_timestamp ON test_suite_results(timestamp);
      CREATE INDEX IF NOT EXISTS idx_test_suite_results_success_rate ON test_suite_results(success_rate);
      CREATE INDEX IF NOT EXISTS idx_performance_alerts_resolved ON performance_alerts(resolved);
      CREATE INDEX IF NOT EXISTS idx_feature_impact_feature_version ON feature_impact_analysis(feature_version);
    `;

    await this.db.query(createTablesSQL);
    logger.info('Performance monitoring tables created successfully');
  }

  /**
   * Record test metrics from Playwright test execution
   * Phase 3.1: Enhanced with async operation management and performance tracking
   */
  public async recordTestMetrics(metrics: TestMetrics): Promise<void> {
    return await this.performanceTracker.trackOperation(
      'record_test_metrics',
      async () => {
        let testId = metrics.id;

        // Parallel operations for better performance
        const operations = [];

        // Database operation (if available)
        if (this.db) {
          operations.push(() => this.insertTestMetricsToDatabase(metrics));
        }

        // Cache operation (if Redis available)
        if (this.redis) {
          operations.push(() => this.cacheRecentMetrics(metrics));
        }

        // Execute database and cache operations in parallel
        const results = await this.asyncManager.executeInParallel(
          operations,
          'metrics_storage',
          false // Don't fail fast - continue even if one operation fails
        );

        // Extract database result if successful
        if (this.db && results[0]?.success && results[0].data) {
          testId = results[0].data;
        }

        // Log any storage failures but don't throw
        results.forEach((result, index) => {
          const operation = index === 0 ? 'database' : 'cache';
          if (!result.success) {
            logger.warn(`Failed to store metrics to ${operation}: ${result.error?.message}`);
          }
        });

        // Check for performance alerts (sequential after storage)
        const alertResult = await this.asyncManager.executeWithRetry(
          () => this.checkPerformanceAlerts(metrics),
          'performance_alerts_check',
          { maxRetries: 1 } // Single retry for alerts
        );

        if (!alertResult.success) {
          logger.warn(`Failed to check performance alerts: ${alertResult.error?.message}`);
          // Don't throw - alert checking failure shouldn't fail metrics recording
        }

        // Emit real-time update via SocketManager (non-blocking)
        this.emitToPerformanceMonitoring('test-metrics-update', {
          testId,
          metrics,
          timestamp: new Date()
        });

        logger.info(`Test metrics recorded: ${metrics.testName} - ${metrics.success ? 'PASS' : 'FAIL'}`);
      },
      {
        testName: metrics.testName,
        testCategory: metrics.testCategory,
        browser: metrics.browser,
        success: metrics.success
      }
    ).then(result => result.result);
  }

  /**
   * Helper method for database insertion
   */
  private async insertTestMetricsToDatabase(metrics: TestMetrics): Promise<string> {
    const query = `
      INSERT INTO test_metrics (
        test_name, test_category, browser, duration, success, retry_count,
        error_type, error_message, timestamp, feature_version, build_id,
        network_condition, resource_usage, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
      RETURNING id
    `;

    const values = [
      metrics.testName,
      metrics.testCategory,
      metrics.browser,
      metrics.duration,
      metrics.success,
      metrics.retryCount,
      metrics.errorType,
      metrics.errorMessage,
      metrics.timestamp,
      metrics.featureVersion,
      metrics.buildId,
      JSON.stringify(metrics.networkCondition),
      JSON.stringify(metrics.resourceUsage),
      JSON.stringify(metrics.metadata)
    ];

    const result = await this.db!.query(query, values);
    return result.rows[0].id;
  }

  private startPerformanceMonitoring(): void {
    // Phase 3.1: Store interval references for proper cleanup
    const periodicAnalysisInterval = setInterval(() => this.performPeriodicAnalysis(), 5 * 60 * 1000); // Every 5 minutes
    const cleanupInterval = setInterval(() => this.cleanupOldData(), 24 * 60 * 60 * 1000); // Daily cleanup

    this.monitoringIntervals.push(periodicAnalysisInterval, cleanupInterval);
    logger.info('Performance monitoring background tasks started');
  }

  /**
   * Helper method to emit events to performance monitoring clients
   */
  private emitToPerformanceMonitoring(event: string, data: any): void {
    try {
      if (this.wsManager && (this.wsManager as any).io) {
        // Use the SocketManager's io instance to emit to performance-monitoring room
        (this.wsManager as any).io.to('performance-monitoring').emit(event, data);
      } else {
        logger.debug('SocketManager not available for performance monitoring event:', event);
      }
    } catch (error) {
      logger.warn('Failed to emit to performance monitoring clients:', error);
    }
  }

  /**
   * Helper method to check if database is available
   */
  private isDatabaseAvailable(): boolean {
    return this.db !== null;
  }

  /**
   * Cache recent metrics in Redis for real-time dashboard
   */
  private async cacheRecentMetrics(metrics: TestMetrics): Promise<void> {
    try {
      if (!this.redis) {
        return; // Skip caching when Redis is not available
      }

      const key = `recent_metrics:${metrics.testCategory}:${metrics.browser}`;
      const data = {
        ...metrics,
        timestamp: metrics.timestamp.toISOString()
      };

      // Store in Redis with 24-hour expiration
      await this.redis.lpush(key, JSON.stringify(data));
      await this.redis.ltrim(key, 0, 99); // Keep last 100 entries
      await this.redis.expire(key, 24 * 60 * 60); // 24 hours

      // Update real-time success rate
      await this.updateRealTimeSuccessRate();
    } catch (error) {
      logger.error('Failed to cache recent metrics:', error);
    }
  }

  /**
   * Update real-time success rate in Redis
   */
  private async updateRealTimeSuccessRate(): Promise<void> {
    try {
      if (!this.redis) {
        return; // Skip when Redis is not available
      }

      if (!this.isDatabaseAvailable()) {
        // Fallback to default values when database is not available
        await this.redis.setex('real_time_success_rate', 300, JSON.stringify({
          successRate: 100.0, // Default to 100% when no data
          totalTests: 0,
          passedTests: 0,
          timestamp: new Date().toISOString()
        }));
        return;
      }

      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

      const query = `
        SELECT
          COUNT(*) as total_tests,
          COUNT(*) FILTER (WHERE success = true) as passed_tests,
          (COUNT(*) FILTER (WHERE success = true) * 100.0 / COUNT(*)) as success_rate
        FROM test_metrics
        WHERE timestamp >= $1
      `;

      const result = await this.db!.query(query, [oneHourAgo]);
      const stats = result.rows[0];

      await this.redis.setex('real_time_success_rate', 300, JSON.stringify({
        successRate: parseFloat(stats.success_rate) || 0,
        totalTests: parseInt(stats.total_tests) || 0,
        passedTests: parseInt(stats.passed_tests) || 0,
        timestamp: new Date().toISOString()
      }));
    } catch (error) {
      logger.error('Failed to update real-time success rate:', error);
    }
  }

  /**
   * Check for performance alerts based on new metrics
   */
  private async checkPerformanceAlerts(metrics: TestMetrics): Promise<void> {
    try {
      // Check success rate threshold
      await this.checkSuccessRateAlert();

      // Check for test failure spikes
      await this.checkFailureSpike(metrics);

      // Check for performance degradation
      await this.checkPerformanceDegradation(metrics);
    } catch (error) {
      logger.error('Failed to check performance alerts:', error);
    }
  }

  /**
   * Check if success rate has dropped below threshold
   */
  private async checkSuccessRateAlert(): Promise<void> {
    if (!this.isDatabaseAvailable()) {
      return; // Skip alert checking when database is not available
    }

    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

    const query = `
      SELECT
        (COUNT(*) FILTER (WHERE success = true) * 100.0 / COUNT(*)) as success_rate,
        COUNT(*) as total_tests
      FROM test_metrics
      WHERE timestamp >= $1
    `;

    const result = await this.db!.query(query, [oneHourAgo]);
    const stats = result.rows[0];
    const successRate = parseFloat(stats.success_rate) || 0;
    const totalTests = parseInt(stats.total_tests) || 0;

    if (totalTests >= 10 && successRate < this.alertThresholds.successRate) {
      await this.createAlert({
        type: 'success_rate_drop',
        severity: 'critical',
        message: `Test success rate dropped to ${successRate.toFixed(1)}% (below critical threshold of ${this.alertThresholds.successRate}%)`,
        currentValue: successRate,
        thresholdValue: this.alertThresholds.successRate,
        affectedTests: [],
        timestamp: new Date(),
        resolved: false
      });
    } else if (totalTests >= 10 && successRate < this.alertThresholds.successRateWarning) {
      await this.createAlert({
        type: 'success_rate_drop',
        severity: 'medium',
        message: `Test success rate dropped to ${successRate.toFixed(1)}% (below warning threshold of ${this.alertThresholds.successRateWarning}%)`,
        currentValue: successRate,
        thresholdValue: this.alertThresholds.successRateWarning,
        affectedTests: [],
        timestamp: new Date(),
        resolved: false
      });
    }
  }

  /**
   * Check for test failure spikes
   */
  private async checkFailureSpike(metrics: TestMetrics): Promise<void> {
    if (metrics.success) return;

    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

    const query = `
      SELECT COUNT(*) as failure_count
      FROM test_metrics
      WHERE timestamp >= $1 AND success = false AND test_name = $2
    `;

    const result = await this.db.query(query, [oneHourAgo, metrics.testName]);
    const failureCount = parseInt(result.rows[0].failure_count) || 0;

    if (failureCount >= this.alertThresholds.maxFailureSpike) {
      await this.createAlert({
        type: 'test_failure_spike',
        severity: 'high',
        message: `Test "${metrics.testName}" has failed ${failureCount} times in the last hour`,
        currentValue: failureCount,
        thresholdValue: this.alertThresholds.maxFailureSpike,
        affectedTests: [metrics.testName],
        timestamp: new Date(),
        resolved: false
      });
    }
  }

  /**
   * Check for performance degradation
   */
  private async checkPerformanceDegradation(metrics: TestMetrics): Promise<void> {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    const query = `
      SELECT AVG(duration) as avg_duration
      FROM test_metrics
      WHERE timestamp >= $1 AND test_name = $2 AND success = true
    `;

    const result = await this.db.query(query, [oneDayAgo, metrics.testName]);
    const avgDuration = parseFloat(result.rows[0].avg_duration) || 0;

    if (avgDuration > 0 && metrics.duration > avgDuration * (1 + this.alertThresholds.maxDurationIncrease / 100)) {
      const increasePercent = ((metrics.duration - avgDuration) / avgDuration) * 100;

      await this.createAlert({
        type: 'performance_degradation',
        severity: 'medium',
        message: `Test "${metrics.testName}" duration increased by ${increasePercent.toFixed(1)}% (${metrics.duration}ms vs ${avgDuration.toFixed(0)}ms average)`,
        currentValue: metrics.duration,
        thresholdValue: avgDuration,
        affectedTests: [metrics.testName],
        timestamp: new Date(),
        resolved: false
      });
    }
  }

  /**
   * Create a performance alert
   */
  private async createAlert(alert: Omit<PerformanceAlert, 'id'>): Promise<void> {
    try {
      let alertId = `alert-${Date.now()}`;

      // Only store in database if available
      if (this.isDatabaseAvailable()) {
        const query = `
          INSERT INTO performance_alerts (
            alert_type, severity, message, current_value, threshold_value,
            affected_tests, timestamp, resolved, feature_version
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          RETURNING id
        `;

        const values = [
          alert.type,
          alert.severity,
          alert.message,
          alert.currentValue,
          alert.thresholdValue,
          alert.affectedTests,
          alert.timestamp,
          alert.resolved,
          alert.featureVersion
        ];

        const result = await this.db!.query(query, values);
        alertId = result.rows[0].id;
      }

      // Emit alert via WebSocket
      this.emitToPerformanceMonitoring('performance-alert', {
        id: alertId,
        ...alert
      });

      // Send email/Slack notification for critical alerts
      if (alert.severity === 'critical') {
        await this.sendCriticalAlert(alert);
      }

      logger.warn(`Performance alert created: ${alert.type} - ${alert.message}`);
    } catch (error) {
      logger.error('Failed to create performance alert:', error);
    }
  }

  /**
   * Send critical alert notifications
   */
  private async sendCriticalAlert(alert: Omit<PerformanceAlert, 'id'>): Promise<void> {
    try {
      // This would integrate with email/Slack services
      // For now, we'll emit an event that can be handled by notification services
      this.emit('critical-alert', alert);

      logger.error(`CRITICAL ALERT: ${alert.message}`);
    } catch (error) {
      logger.error('Failed to send critical alert:', error);
    }
  }

  /**
   * Perform periodic analysis
   */
  private async performPeriodicAnalysis(): Promise<void> {
    try {
      await this.updateRealTimeSuccessRate();
      await this.analyzeTestTrends();
      await this.checkForAnomalies();
    } catch (error) {
      logger.error('Failed to perform periodic analysis:', error);
    }
  }

  /**
   * Analyze test trends
   */
  private async analyzeTestTrends(): Promise<void> {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    const query = `
      SELECT
        test_category,
        browser,
        COUNT(*) as total_tests,
        COUNT(*) FILTER (WHERE success = true) as passed_tests,
        (COUNT(*) FILTER (WHERE success = true) * 100.0 / COUNT(*)) as success_rate,
        AVG(duration) as avg_duration
      FROM test_metrics
      WHERE timestamp >= $1
      GROUP BY test_category, browser
      ORDER BY success_rate ASC
    `;

    const result = await this.db.query(query, [twentyFourHoursAgo]);

    // Cache trend data for dashboard
    await this.redis.setex('test_trends_24h', 300, JSON.stringify(result.rows));
  }

  /**
   * Check for anomalies in test patterns
   */
  private async checkForAnomalies(): Promise<void> {
    // This could include more sophisticated anomaly detection
    // For now, we'll check for unusual patterns in test execution

    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

    const query = `
      SELECT
        test_name,
        COUNT(*) as execution_count,
        AVG(duration) as avg_duration,
        STDDEV(duration) as duration_stddev
      FROM test_metrics
      WHERE timestamp >= $1
      GROUP BY test_name
      HAVING COUNT(*) > 5 AND STDDEV(duration) > AVG(duration) * 0.5
    `;

    const result = await this.db.query(query, [oneHourAgo]);

    for (const row of result.rows) {
      logger.info(`Anomaly detected in test "${row.test_name}": High duration variance (${row.duration_stddev}ms stddev)`);
    }
  }

  /**
   * Clean up old data
   */
  private async cleanupOldData(): Promise<void> {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      // Clean up old test metrics (keep 30 days)
      await this.db.query('DELETE FROM test_metrics WHERE timestamp < $1', [thirtyDaysAgo]);

      // Clean up resolved alerts older than 7 days
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      await this.db.query('DELETE FROM performance_alerts WHERE resolved = true AND resolved_at < $1', [sevenDaysAgo]);

      logger.info('Performance monitoring data cleanup completed');
    } catch (error) {
      logger.error('Failed to cleanup old data:', error);
    }
  }

  // Public API Methods

  /**
   * Record test suite results
   */
  public async recordTestSuiteResult(suiteResult: TestSuiteResult): Promise<void> {
    try {
      const query = `
        INSERT INTO test_suite_results (
          suite_id, suite_name, total_tests, passed_tests, failed_tests,
          skipped_tests, success_rate, duration, timestamp, feature_version,
          build_id, browser, test_category
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
        RETURNING id
      `;

      const values = [
        suiteResult.suiteId,
        suiteResult.suiteName,
        suiteResult.totalTests,
        suiteResult.passedTests,
        suiteResult.failedTests,
        suiteResult.skippedTests,
        suiteResult.successRate,
        suiteResult.duration,
        suiteResult.timestamp,
        suiteResult.featureVersion,
        suiteResult.buildId,
        suiteResult.browser,
        suiteResult.testCategory
      ];

      await this.db.query(query, values);

      // Emit real-time update
      this.emitToPerformanceMonitoring('test-suite-update', suiteResult);

      logger.info(`Test suite result recorded: ${suiteResult.suiteName} - ${suiteResult.successRate}% success rate`);
    } catch (error) {
      logger.error('Failed to record test suite result:', error);
      throw error;
    }
  }

  /**
   * Get current performance dashboard data
   */
  public async getDashboardData(): Promise<any> {
    try {
      // Get real-time success rate
      let realTimeStats = {
        successRate: 100.0,
        totalTests: 0,
        passedTests: 0,
        timestamp: new Date().toISOString()
      };

      if (this.redis) {
        const realTimeData = await this.redis.get('real_time_success_rate');
        if (realTimeData) {
          realTimeStats = JSON.parse(realTimeData);
        }
      }

      let alerts = [];
      let historicalData = [];

      // Get data from database if available
      if (this.isDatabaseAvailable()) {
        // Get recent alerts
        const alertsQuery = `
          SELECT * FROM performance_alerts
          WHERE resolved = false
          ORDER BY timestamp DESC
          LIMIT 10
        `;
        const alertsResult = await this.db!.query(alertsQuery);
        alerts = alertsResult.rows;

        // Get historical success rate (last 7 days)
        const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        const historicalQuery = `
          SELECT
            DATE_TRUNC('hour', timestamp) as hour,
            (COUNT(*) FILTER (WHERE success = true) * 100.0 / COUNT(*)) as success_rate,
            COUNT(*) as total_tests
          FROM test_metrics
          WHERE timestamp >= $1
          GROUP BY DATE_TRUNC('hour', timestamp)
          ORDER BY hour
        `;
        const historicalResult = await this.db!.query(historicalQuery, [sevenDaysAgo]);
        historicalData = historicalResult.rows;
      }

      // Get test trends
      let trends = [];
      if (this.redis) {
        const trendsData = await this.redis.get('test_trends_24h');
        trends = trendsData ? JSON.parse(trendsData) : [];
      }

      return {
        realTimeStats,
        alerts,
        trends,
        historicalData,
        thresholds: this.alertThresholds
      };
    } catch (error) {
      logger.error('Failed to get dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get feature impact analysis
   */
  public async getFeatureImpactAnalysis(featureVersion: string): Promise<FeatureImpactAnalysis | null> {
    try {
      const query = `
        SELECT * FROM feature_impact_analysis
        WHERE feature_version = $1
        ORDER BY created_at DESC
        LIMIT 1
      `;

      const result = await this.db.query(query, [featureVersion]);

      if (result.rows.length === 0) {
        return null;
      }

      const row = result.rows[0];
      return {
        featureVersion: row.feature_version,
        deploymentDate: row.deployment_date,
        baselineMetrics: row.baseline_metrics,
        currentMetrics: row.current_metrics,
        impact: row.impact_analysis,
        recommendation: row.recommendation
      };
    } catch (error) {
      logger.error('Failed to get feature impact analysis:', error);
      throw error;
    }
  }

  /**
   * Create baseline metrics for a new feature
   */
  public async createFeatureBaseline(featureVersion: string): Promise<void> {
    try {
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

      const query = `
        SELECT
          (COUNT(*) FILTER (WHERE success = true) * 100.0 / COUNT(*)) as success_rate,
          AVG(duration) as avg_duration,
          COUNT(*) as total_tests
        FROM test_metrics
        WHERE timestamp >= $1
      `;

      const result = await this.db.query(query, [twentyFourHoursAgo]);
      const stats = result.rows[0];

      const baselineMetrics = {
        successRate: parseFloat(stats.success_rate) || 0,
        averageDuration: parseFloat(stats.avg_duration) || 0,
        totalTests: parseInt(stats.total_tests) || 0
      };

      const insertQuery = `
        INSERT INTO feature_impact_analysis (
          feature_version, deployment_date, baseline_metrics,
          current_metrics, impact_analysis, recommendation
        ) VALUES ($1, $2, $3, $3, $4, $5)
      `;

      await this.db.query(insertQuery, [
        featureVersion,
        new Date(),
        JSON.stringify(baselineMetrics),
        JSON.stringify({ successRateChange: 0, durationChange: 0, newFailures: [], improvedTests: [] }),
        'proceed'
      ]);

      logger.info(`Feature baseline created for version: ${featureVersion}`);
    } catch (error) {
      logger.error('Failed to create feature baseline:', error);
      throw error;
    }
  }

  /**
   * Analyze feature impact after deployment
   */
  public async analyzeFeatureImpact(featureVersion: string): Promise<FeatureImpactAnalysis> {
    try {
      // Get baseline metrics
      const baselineQuery = `
        SELECT baseline_metrics FROM feature_impact_analysis
        WHERE feature_version = $1
        ORDER BY created_at DESC
        LIMIT 1
      `;
      const baselineResult = await this.db.query(baselineQuery, [featureVersion]);

      if (baselineResult.rows.length === 0) {
        throw new Error(`No baseline found for feature version: ${featureVersion}`);
      }

      const baselineMetrics = baselineResult.rows[0].baseline_metrics;

      // Get current metrics (last 24 hours)
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

      const currentQuery = `
        SELECT
          (COUNT(*) FILTER (WHERE success = true) * 100.0 / COUNT(*)) as success_rate,
          AVG(duration) as avg_duration,
          COUNT(*) as total_tests
        FROM test_metrics
        WHERE timestamp >= $1 AND feature_version = $2
      `;

      const currentResult = await this.db.query(currentQuery, [twentyFourHoursAgo, featureVersion]);
      const currentStats = currentResult.rows[0];

      const currentMetrics = {
        successRate: parseFloat(currentStats.success_rate) || 0,
        averageDuration: parseFloat(currentStats.avg_duration) || 0,
        totalTests: parseInt(currentStats.total_tests) || 0
      };

      // Calculate impact
      const successRateChange = currentMetrics.successRate - baselineMetrics.successRate;
      const durationChange = ((currentMetrics.averageDuration - baselineMetrics.averageDuration) / baselineMetrics.averageDuration) * 100;

      // Get new failures
      const failuresQuery = `
        SELECT DISTINCT test_name
        FROM test_metrics
        WHERE timestamp >= $1 AND feature_version = $2 AND success = false
        AND test_name NOT IN (
          SELECT DISTINCT test_name
          FROM test_metrics
          WHERE timestamp < $1 AND success = false
        )
      `;
      const failuresResult = await this.db.query(failuresQuery, [twentyFourHoursAgo, featureVersion]);
      const newFailures = failuresResult.rows.map(row => row.test_name);

      // Determine recommendation
      let recommendation: 'proceed' | 'investigate' | 'rollback' = 'proceed';
      if (successRateChange < -2 || newFailures.length > 3) {
        recommendation = 'rollback';
      } else if (successRateChange < -1 || durationChange > 25) {
        recommendation = 'investigate';
      }

      const impact = {
        successRateChange,
        durationChange,
        newFailures,
        improvedTests: [] // Could be calculated similarly
      };

      // Update the analysis
      const updateQuery = `
        UPDATE feature_impact_analysis
        SET current_metrics = $1, impact_analysis = $2, recommendation = $3, updated_at = NOW()
        WHERE feature_version = $4
      `;

      await this.db.query(updateQuery, [
        JSON.stringify(currentMetrics),
        JSON.stringify(impact),
        recommendation,
        featureVersion
      ]);

      const analysis: FeatureImpactAnalysis = {
        featureVersion,
        deploymentDate: new Date(), // This should come from the baseline
        baselineMetrics,
        currentMetrics,
        impact,
        recommendation
      };

      // Create alert if recommendation is not 'proceed'
      if (recommendation !== 'proceed') {
        await this.createAlert({
          type: 'feature_impact',
          severity: recommendation === 'rollback' ? 'critical' : 'high',
          message: `Feature ${featureVersion} impact analysis recommends: ${recommendation}. Success rate change: ${successRateChange.toFixed(1)}%`,
          currentValue: currentMetrics.successRate,
          thresholdValue: baselineMetrics.successRate,
          affectedTests: newFailures,
          timestamp: new Date(),
          resolved: false,
          featureVersion
        });
      }

      return analysis;
    } catch (error) {
      logger.error('Failed to analyze feature impact:', error);
      throw error;
    }
  }

  /**
   * Resolve a performance alert
   */
  public async resolveAlert(alertId: string): Promise<void> {
    try {
      const query = `
        UPDATE performance_alerts
        SET resolved = true, resolved_at = NOW()
        WHERE id = $1
      `;

      await this.db.query(query, [alertId]);

      // Emit real-time update
      this.emitToPerformanceMonitoring('alert-resolved', { alertId });

      logger.info(`Performance alert resolved: ${alertId}`);
    } catch (error) {
      logger.error('Failed to resolve alert:', error);
      throw error;
    }
  }

  /**
   * Get test execution history for a specific test
   */
  public async getTestHistory(testName: string, days: number = 7): Promise<any[]> {
    try {
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

      const query = `
        SELECT
          timestamp, success, duration, browser, retry_count, error_type
        FROM test_metrics
        WHERE test_name = $1 AND timestamp >= $2
        ORDER BY timestamp DESC
      `;

      const result = await this.db.query(query, [testName, startDate]);
      return result.rows;
    } catch (error) {
      logger.error('Failed to get test history:', error);
      throw error;
    }
  }

  /**
   * Get performance metrics for monitoring
   * Phase 3.1: New method for detailed performance analysis
   */
  public getPerformanceMetrics(operationFilter?: string) {
    return this.performanceTracker.getPerformanceReport(operationFilter);
  }

  /**
   * Get async operation configuration
   */
  public getAsyncConfiguration() {
    return this.asyncManager.getConfig();
  }

  /**
   * Update async operation configuration
   */
  public updateAsyncConfiguration(config: Partial<AsyncOperationConfig>) {
    this.asyncManager.updateConfig(config);
    logger.info('Performance monitoring async configuration updated', config);
  }

  /**
   * Clear performance history
   */
  public clearPerformanceHistory(): void {
    this.performanceTracker.clearHistory();
    logger.info('Performance monitoring history cleared');
  }

  /**
   * Get summary statistics for monitoring
   */
  public getSummaryStatistics() {
    const performanceStats = this.performanceTracker.getSummaryStats();
    const asyncConfig = this.asyncManager.getConfig();

    return {
      performance: performanceStats,
      configuration: {
        timeout: asyncConfig.timeout,
        maxRetries: asyncConfig.maxRetries,
        concurrencyLimit: asyncConfig.concurrencyLimit
      },
      serviceHealth: {
        databaseAvailable: this.isDatabaseAvailable(),
        redisAvailable: this.redis !== null,
        socketManagerAvailable: this.wsManager !== null
      },
      alertThresholds: this.alertThresholds
    };
  }

  /**
   * Phase 3: Enterprise Scalability - Get enterprise-grade monitoring metrics
   * Backward compatible - extends existing monitoring with scalability insights
   */
  public async getEnterpriseScalabilityMetrics(): Promise<{
    scalabilityHealth: {
      currentConcurrentUsers: number;
      maxConcurrentUsersSupported: number;
      scalabilityScore: number;
      resourceUtilization: {
        cpu: number;
        memory: number;
        network: number;
      };
    };
    performanceTargets: {
      successRateTarget: number;
      currentSuccessRate: number;
      responseTimeTarget: number;
      averageResponseTime: number;
      apiCallReductionTarget: number;
      currentApiCallReduction: number;
    };
    enterpriseReadiness: {
      isEnterpriseReady: boolean;
      readinessScore: number;
      criticalIssues: string[];
      recommendations: string[];
    };
  }> {
    try {
      // Get current system metrics
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();

      // Calculate resource utilization
      const resourceUtilization = {
        cpu: Math.min((cpuUsage.user + cpuUsage.system) / 1000000 / process.uptime() * 100, 100),
        memory: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100,
        network: 45 // Mock network utilization - would be calculated from actual metrics
      };

      // Get current performance metrics
      const dashboardData = await this.getDashboardData();
      const currentSuccessRate = dashboardData.realTimeStats.successRate;
      const performanceStats = this.performanceTracker.getSummaryStats();
      const averageResponseTime = performanceStats.averageOperationTime || 0;

      // Calculate scalability metrics
      const currentConcurrentUsers = this.wsManager?.getConnectedClientsCount() || 0;
      const maxConcurrentUsersSupported = this.calculateMaxConcurrentUsers(resourceUtilization);
      const scalabilityScore = this.calculateScalabilityScore(currentConcurrentUsers, maxConcurrentUsersSupported, resourceUtilization);

      // Calculate API call reduction (mock calculation - would integrate with cache service)
      const currentApiCallReduction = Math.min(currentSuccessRate * 0.7, 70); // Rough estimate

      // Assess enterprise readiness
      const enterpriseReadiness = this.assessEnterpriseReadiness(
        currentSuccessRate,
        averageResponseTime,
        resourceUtilization,
        scalabilityScore
      );

      return {
        scalabilityHealth: {
          currentConcurrentUsers,
          maxConcurrentUsersSupported,
          scalabilityScore,
          resourceUtilization
        },
        performanceTargets: {
          successRateTarget: this.alertThresholds.enterpriseSuccessRate,
          currentSuccessRate,
          responseTimeTarget: this.alertThresholds.scalabilityResponseTime,
          averageResponseTime,
          apiCallReductionTarget: this.alertThresholds.apiCallReductionTarget,
          currentApiCallReduction
        },
        enterpriseReadiness
      };
    } catch (error) {
      logger.error('Failed to get enterprise scalability metrics:', error);
      throw error;
    }
  }

  /**
   * Phase 3: Enterprise Scalability - Calculate maximum concurrent users supported
   */
  private calculateMaxConcurrentUsers(resourceUtilization: { cpu: number; memory: number; network: number }): number {
    // Conservative calculation based on current resource usage
    const cpuCapacity = Math.max(1, Math.floor((100 - resourceUtilization.cpu) / 10)); // Each 10% CPU supports ~100 users
    const memoryCapacity = Math.max(1, Math.floor((100 - resourceUtilization.memory) / 5)); // Each 5% memory supports ~100 users
    const networkCapacity = Math.max(1, Math.floor((100 - resourceUtilization.network) / 8)); // Each 8% network supports ~100 users

    // Take the most limiting factor
    const limitingFactor = Math.min(cpuCapacity, memoryCapacity, networkCapacity);
    return Math.min(limitingFactor * 100, this.alertThresholds.concurrentUserThreshold);
  }

  /**
   * Phase 3: Enterprise Scalability - Calculate overall scalability score
   */
  private calculateScalabilityScore(
    currentUsers: number,
    maxUsers: number,
    resourceUtilization: { cpu: number; memory: number; network: number }
  ): number {
    // Score based on capacity utilization (0-100)
    const capacityScore = Math.max(0, 100 - (currentUsers / maxUsers) * 100);

    // Score based on resource efficiency (0-100)
    const avgResourceUsage = (resourceUtilization.cpu + resourceUtilization.memory + resourceUtilization.network) / 3;
    const resourceScore = Math.max(0, 100 - avgResourceUsage);

    // Combined score with weights
    return Math.round((capacityScore * 0.6) + (resourceScore * 0.4));
  }

  /**
   * Phase 3: Enterprise Scalability - Assess enterprise readiness
   */
  private assessEnterpriseReadiness(
    successRate: number,
    responseTime: number,
    resourceUtilization: { cpu: number; memory: number; network: number },
    scalabilityScore: number
  ): {
    isEnterpriseReady: boolean;
    readinessScore: number;
    criticalIssues: string[];
    recommendations: string[];
  } {
    const criticalIssues: string[] = [];
    const recommendations: string[] = [];
    let readinessScore = 100;

    // Check success rate
    if (successRate < this.alertThresholds.enterpriseSuccessRate) {
      criticalIssues.push(`Success rate ${successRate.toFixed(1)}% below enterprise threshold ${this.alertThresholds.enterpriseSuccessRate}%`);
      readinessScore -= 25;
      recommendations.push('Improve test stability and error handling');
    }

    // Check response time
    if (responseTime > this.alertThresholds.scalabilityResponseTime) {
      criticalIssues.push(`Response time ${responseTime}ms exceeds enterprise threshold ${this.alertThresholds.scalabilityResponseTime}ms`);
      readinessScore -= 20;
      recommendations.push('Optimize API performance and implement caching');
    }

    // Check resource utilization
    const avgResourceUsage = (resourceUtilization.cpu + resourceUtilization.memory + resourceUtilization.network) / 3;
    if (avgResourceUsage > this.alertThresholds.systemResourceThreshold) {
      criticalIssues.push(`System resource usage ${avgResourceUsage.toFixed(1)}% exceeds threshold ${this.alertThresholds.systemResourceThreshold}%`);
      readinessScore -= 15;
      recommendations.push('Scale infrastructure or optimize resource usage');
    }

    // Check scalability score
    if (scalabilityScore < 70) {
      criticalIssues.push(`Scalability score ${scalabilityScore} below enterprise standard (70+)`);
      readinessScore -= 10;
      recommendations.push('Implement horizontal scaling and load balancing');
    }

    // Add positive recommendations
    if (criticalIssues.length === 0) {
      recommendations.push('System meets enterprise standards - consider advanced monitoring');
      recommendations.push('Implement predictive scaling for peak load handling');
    }

    return {
      isEnterpriseReady: criticalIssues.length === 0 && readinessScore >= 80,
      readinessScore: Math.max(0, readinessScore),
      criticalIssues,
      recommendations
    };
  }

  /**
   * Shutdown the service gracefully
   * Phase 3.1: Enhanced with async operation management and proper cleanup
   */
  public async shutdown(): Promise<void> {
    const shutdownResult = await this.asyncManager.executeWithRetry(
      async () => {
        // Clear monitoring intervals
        this.monitoringIntervals.forEach(interval => clearInterval(interval));
        this.monitoringIntervals = [];

        // Shutdown operations in parallel for faster cleanup
        const shutdownOperations = [];

        if (this.db) {
          shutdownOperations.push(() => this.db!.end());
        }

        if (this.redis) {
          shutdownOperations.push(() => this.redis!.disconnect());
        }

        if (shutdownOperations.length > 0) {
          const shutdownResults = await this.asyncManager.executeInParallel(
            shutdownOperations,
            'service_shutdown',
            false // Don't fail fast - try to shutdown all components
          );

          // Log any shutdown failures but don't throw
          shutdownResults.forEach((result, index) => {
            const component = index === 0 ? 'database' : 'redis';
            if (!result.success) {
              logger.warn(`Failed to shutdown ${component}: ${result.error?.message}`);
            }
          });
        }

        // Clear performance tracking history
        this.performanceTracker.clearHistory();

        logger.info('Performance monitoring service shut down gracefully');
      },
      'service_shutdown',
      { timeout: 15000, maxRetries: 1 }
    );

    if (!shutdownResult.success) {
      logger.error('Error during performance monitoring service shutdown:', shutdownResult.error);
      throw shutdownResult.error;
    }
  }
}
