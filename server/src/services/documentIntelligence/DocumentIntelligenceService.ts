import { createModuleLogger } from '@/utils/logger';
import {
  DocumentIntelligenceConfig,
  DocumentAnalysisResult,
  DocumentProcessingOptions,
  OCRAnalysisResult,
  OCRPageResult,
  OCRLineResult,
  TesseractLine
} from './types';
import { DocumentFileSystemManager } from './utils/DocumentFileSystemManager';
import { DocumentInitializationManager, InitializationState } from './utils/DocumentInitializationManager';
import path from 'path';
import sharp from 'sharp';

// Performance and async utilities
import { AsyncOperationManager, AsyncOperationConfig } from './utils/AsyncOperationManager';
import { PerformanceTracker } from './utils/PerformanceTracker';

// OpenAI Service Interface for dependency injection
export interface IOpenAIService {
  analyzeImages(imagePaths: string[], prompt: string, config: any): Promise<any>;
}

// Default OpenAI Service import for backward compatibility
import { openaiService } from '../openai/OpenAIService';

const logger = createModuleLogger('DocumentIntelligenceService');

/**
 * Document Intelligence Service - Refactored Modular Implementation
 *
 * Provides document intelligence capabilities using open-source libraries as an alternative
 * to Azure Document Intelligence. Integrates with existing PDF processing pipeline to extract
 * structured data from kitchen design documents, technical drawings, and specification sheets.
 *
 * Features:
 * - Advanced OCR using Tesseract.js with spatial information
 * - Table detection and extraction using pattern recognition
 * - Layout analysis using PDF parsing and image processing
 * - Kitchen-specific document processing enhanced with GPT-4o
 * - Identical API interface to Azure Document Intelligence for seamless integration
 * - Modular architecture with specialized utilities for maintainability
 * - 100% backward compatibility facade
 */
export class DocumentIntelligenceService {
  private fileSystemManager: DocumentFileSystemManager;
  private initializationManager: DocumentInitializationManager;
  private openAIService: IOpenAIService;
  private config: DocumentIntelligenceConfig;

  // Phase 3.1: Async/Await Pattern Standardization
  private asyncManager: AsyncOperationManager;
  private performanceTracker: PerformanceTracker;

  constructor(
    openAIService: IOpenAIService = openaiService,
    tempDir?: string,
    config?: DocumentIntelligenceConfig
  ) {
    // Dependency injection with defaults for backward compatibility
    this.openAIService = openAIService;
    this.config = config || this.loadConfiguration();

    // Initialize utility managers
    const resolvedTempDir = tempDir || this.getDefaultTempDir();
    this.fileSystemManager = new DocumentFileSystemManager(resolvedTempDir);
    this.initializationManager = new DocumentInitializationManager({
      timeout: 60000,
      maxRetries: 3,
      retryDelayBase: 1000,
      maxRetryDelay: 10000
    });

    // Phase 3.1: Initialize async operation management
    this.asyncManager = new AsyncOperationManager({
      timeout: 45000, // 45 seconds for document processing
      maxRetries: 2,
      retryDelayBase: 2000,
      maxRetryDelay: 8000,
      concurrencyLimit: 3 // Limit concurrent OCR operations
    });

    this.performanceTracker = new PerformanceTracker(500); // Track last 500 operations
  }

  /**
   * Get default temp directory path (Phase 2: Async consistency)
   */
  private getDefaultTempDir(): string {
    return path.join(process.cwd(), 'temp', 'document-intelligence');
  }

  /**
   * Ensure service is initialized (Phase 2: Fixed state management)
   */
  private async ensureInitialized(): Promise<void> {
    // Initialize file system first
    await this.fileSystemManager.initialize();

    // Then initialize Tesseract worker
    await this.initializationManager.initialize();
  }

  /**
   * Check if service is available (Phase 2: Separated from initialization trigger)
   */
  async isAvailable(): Promise<boolean> {
    return this.initializationManager.isAvailable();
  }

  /**
   * Analyze document using open-source document intelligence
   * Phase 3.1: Enhanced with async operation management and performance tracking
   */
  async analyzeDocument(
    documentPath: string,
    options: DocumentProcessingOptions = {}
  ): Promise<DocumentAnalysisResult> {
    return await this.performanceTracker.trackOperation(
      'document_analysis',
      async () => {
        // Ensure service is initialized before processing with retry logic
        const initResult = await this.asyncManager.executeWithRetry(
          () => this.ensureInitialized(),
          'service_initialization'
        );

        if (!initResult.success) {
          throw new Error(`Service initialization failed: ${initResult.error?.message}`);
        }

        if (!this.initializationManager.isAvailable()) {
          throw new Error('Document Intelligence service is not available');
        }

        logger.info('Starting document analysis', {
          documentPath,
          options: this.sanitizeOptionsForLogging(options)
        });

        // Determine file format from mimetype or original filename
        const fileExtension = this.determineFileExtension(documentPath, options);

        let imagePaths: string[] = [];

        // Convert PDF to images if needed using FileSystemManager with async management
        if (fileExtension === '.pdf') {
          const conversionResult = await this.asyncManager.executeWithRetry(
            () => this.fileSystemManager.convertPDFToImages(documentPath),
            'pdf_conversion',
            { timeout: 60000 } // Extended timeout for PDF conversion
          );

          if (!conversionResult.success) {
            throw new Error(`PDF conversion failed: ${conversionResult.error?.message}`);
          }

          imagePaths = conversionResult.data!;
        } else if (['.jpg', '.jpeg', '.png', '.tiff', '.bmp'].includes(fileExtension)) {
          imagePaths = [documentPath];
        } else {
          throw new Error(`Unsupported file format: ${fileExtension}`);
        }

        // Execute parallel processing operations with proper async management
        const parallelOperations = [
          // OCR analysis (required)
          () => this.performOCRAnalysis(imagePaths, options),
          // Table extraction (conditional)
          ...(options.extractTables ? [() => this.extractTablesFromImages(imagePaths)] : []),
        ];

        const parallelResults = await this.asyncManager.executeInParallel(
          parallelOperations,
          'parallel_document_processing',
          false // Don't fail fast - continue even if some operations fail
        );

        // Extract results with proper error handling
        const ocrResults = parallelResults[0].success ? parallelResults[0].data : null;
        if (!ocrResults) {
          throw new Error(`OCR analysis failed: ${parallelResults[0].error?.message}`);
        }

        const tables = options.extractTables && parallelResults[1]?.success
          ? parallelResults[1].data
          : [];

        // Extract key-value pairs if requested (sequential after OCR)
        let keyValuePairs: any[] = [];
        if (options.extractKeyValuePairs) {
          const kvResult = await this.asyncManager.executeWithRetry(
            () => this.extractKeyValuePairsFromOCR(ocrResults),
            'key_value_extraction'
          );
          keyValuePairs = kvResult.success ? kvResult.data! : [];
        }

        // Enhance with GPT-4o analysis if kitchen analysis is enabled
        let enhancedResult = ocrResults;
        if (options.enableKitchenAnalysis) {
          const gptResult = await this.asyncManager.executeWithRetry(
            () => this.enhanceWithGPTAnalysis(ocrResults, imagePaths, options),
            'gpt_enhancement',
            { timeout: 30000 } // 30 second timeout for GPT analysis
          );
          enhancedResult = gptResult.success ? gptResult.data! : ocrResults;
        }

        // Structure the results in Azure Document Intelligence format
        const analysisResult = this.formatAnalysisResult(
          enhancedResult,
          tables,
          keyValuePairs,
          0, // Processing time will be calculated by performance tracker
          options
        );

        logger.info('Document analysis completed', {
          pagesAnalyzed: imagePaths.length,
          tablesFound: tables.length,
          confidence: analysisResult.confidence
        });

        // Cleanup temporary files using FileSystemManager with async management
        await this.asyncManager.executeWithRetry(
          () => this.fileSystemManager.cleanupTempFiles(imagePaths.filter(p => p !== documentPath)),
          'cleanup_temp_files'
        );

        return analysisResult;
      },
      {
        documentPath,
        fileExtension: this.determineFileExtension(documentPath, options),
        optionsProvided: Object.keys(options).length > 0
      }
    ).then(result => result.result);
  }

  /**
   * Determine file extension from various sources (Phase 2: Enhanced error handling)
   */
  private determineFileExtension(documentPath: string, options: DocumentProcessingOptions): string {
    let fileExtension = '';

    try {
      if (options.mimetype) {
        // Use mimetype to determine file format
        const mimeToExt: { [key: string]: string } = {
          'application/pdf': '.pdf',
          'image/jpeg': '.jpg',
          'image/jpg': '.jpg',
          'image/png': '.png',
          'image/tiff': '.tiff',
          'image/bmp': '.bmp'
        };
        fileExtension = mimeToExt[options.mimetype] || '';
      } else if (options.originalFilename) {
        // Fallback to original filename extension
        fileExtension = path.extname(options.originalFilename).toLowerCase();
      } else {
        // Last resort: try to get extension from saved file path
        fileExtension = path.extname(documentPath).toLowerCase();
      }

      if (!fileExtension) {
        throw new Error(`Unable to determine file format. Mimetype: ${options.mimetype}, Original filename: ${options.originalFilename}`);
      }

      return fileExtension;
    } catch (error) {
      logger.error('Failed to determine file extension', {
        documentPath,
        mimetype: options.mimetype,
        originalFilename: options.originalFilename,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Batch analyze multiple documents
   * Phase 3.1: Enhanced with concurrency control and performance tracking
   */
  async analyzeDocuments(
    documentPaths: string[],
    options: DocumentProcessingOptions = {}
  ): Promise<DocumentAnalysisResult[]> {
    return await this.performanceTracker.trackOperation(
      'batch_document_analysis',
      async () => {
        logger.info('Starting batch document analysis', {
          documentCount: documentPaths.length,
          options: this.sanitizeOptionsForLogging(options)
        });

        // Create analysis operations for each document
        const analysisOperations = documentPaths.map(documentPath =>
          () => this.analyzeDocument(documentPath, options)
        );

        // Execute with concurrency control to prevent resource exhaustion
        const results = await this.asyncManager.executeWithConcurrencyControl(
          analysisOperations,
          'batch_document_analysis'
        );

        // Separate successful results from errors
        const successfulResults: DocumentAnalysisResult[] = [];
        const errors: Array<{ path: string; error: string }> = [];

        results.forEach((result, index) => {
          const documentPath = documentPaths[index];

          if (result.success && result.data) {
            successfulResults.push(result.data);
          } else {
            const errorMessage = result.error?.message || 'Unknown error';
            logger.error(`Failed to analyze document: ${documentPath}`, {
              error: errorMessage,
              duration: result.duration,
              retryCount: result.retryCount
            });
            errors.push({
              path: documentPath,
              error: errorMessage
            });
          }
        });

        logger.info('Batch document analysis completed', {
          successful: successfulResults.length,
          failed: errors.length,
          totalDocuments: documentPaths.length,
          averageProcessingTime: results.reduce((sum, r) => sum + r.duration, 0) / results.length
        });

        if (errors.length > 0) {
          logger.warn('Some documents failed analysis', { errors });
        }

        return successfulResults;
      },
      {
        documentCount: documentPaths.length,
        concurrencyLimit: this.asyncManager.getConfig().concurrencyLimit
      }
    ).then(result => result.result);
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details: {
      clientInitialized: boolean;
      configurationValid: boolean;
      lastChecked: string;
      errorMessage?: string;
    };
  }> {
    const lastChecked = new Date().toISOString();

    try {
      const isAvailable = await this.isAvailable();

      if (!isAvailable) {
        return {
          status: 'unhealthy',
          details: {
            clientInitialized: false,
            configurationValid: false,
            lastChecked,
            errorMessage: 'Service initialization failed'
          }
        };
      }

      // Test service connectivity with a minimal operation
      return {
        status: 'healthy',
        details: {
          clientInitialized: true,
          configurationValid: true,
          lastChecked
        }
      };
    } catch (error) {
      logger.error('Document Intelligence health check failed:', error);
      return {
        status: 'degraded',
        details: {
          clientInitialized: false,
          configurationValid: true,
          lastChecked,
          errorMessage: (error as Error).message
        }
      };
    }
  }



  /**
   * Perform OCR analysis on images using Tesseract (Phase 2: Enhanced error handling)
   */
  private async performOCRAnalysis(imagePaths: string[], _options: DocumentProcessingOptions): Promise<OCRAnalysisResult> {
    const worker = this.initializationManager.getWorker();
    const pages: OCRPageResult[] = [];

    for (let i = 0; i < imagePaths.length; i++) {
      const imagePath = imagePaths[i];

      try {
        // Get image metadata
        const metadata = await sharp(imagePath).metadata();

        // Perform OCR using the worker from initialization manager
        const { data } = await worker.recognize(imagePath);

        // Debug: Log the structure of the Tesseract data
        logger.info('Tesseract OCR data structure', {
          imagePath,
          hasData: !!data,
          dataKeys: data ? Object.keys(data) : [],
          hasLines: !!(data && data.lines),
          linesLength: data && data.lines ? data.lines.length : 0,
          hasText: !!(data && data.text),
          textLength: data && data.text ? data.text.length : 0
        });

        // Process OCR results with spatial information
        let lines: OCRLineResult[] = [];
        if (data && data.lines && Array.isArray(data.lines)) {
          lines = data.lines.map((line: TesseractLine) => ({
            content: line.text || '',
            boundingBox: line.bbox ? [line.bbox.x0, line.bbox.y0, line.bbox.x1, line.bbox.y1] : [0, 0, 0, 0],
            confidence: (line.confidence || 0) / 100 // Convert to 0-1 scale
          }));
        } else {
          // Fallback: create a single line from the text if lines are not available
          logger.warn('Tesseract lines not available, creating fallback line structure', {
            imagePath,
            hasText: !!(data && data.text)
          });
          if (data && data.text) {
            lines = [{
              content: data.text,
              boundingBox: [0, 0, metadata.width || 0, metadata.height || 0],
              confidence: 0.8 // Default confidence
            }];
          }
        }

        pages.push({
          pageNumber: i + 1,
          width: metadata.width || 0,
          height: metadata.height || 0,
          unit: 'pixel',
          lines,
          text: data.text || ''
        });

      } catch (error) {
        logger.error(`Failed to process OCR for image ${imagePath}:`, {
          error: error instanceof Error ? error.message : String(error),
          imagePath,
          pageNumber: i + 1
        });

        // Add empty page result for failed processing
        pages.push({
          pageNumber: i + 1,
          width: 0,
          height: 0,
          unit: 'pixel',
          lines: [],
          text: '',
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    // Calculate overall confidence
    const totalLines = pages.reduce((sum, p) => sum + p.lines.length, 0);
    const totalConfidence = pages.reduce((sum, p) =>
      sum + p.lines.reduce((lineSum, l) => lineSum + l.confidence, 0), 0
    );
    const overallConfidence = totalLines > 0 ? totalConfidence / totalLines : 0;

    return {
      content: pages.map(p => p.text).join('\n'),
      pages,
      confidence: overallConfidence
    };
  }

  /**
   * Extract tables from images using pattern recognition
   */
  private async extractTablesFromImages(imagePaths: string[]): Promise<any[]> {
    const worker = this.initializationManager.getWorker();
    const tables = [];

    for (const imagePath of imagePaths) {
      try {
        // Simple table detection using OCR data analysis
        const { data } = await worker.recognize(imagePath);

        // Look for table-like patterns in the text
        const lines = data.text.split('\n').filter((line: string) => line.trim().length > 0);
        const potentialTableLines = lines.filter((line: string) => {
          // Simple heuristic: lines with multiple spaces or tabs might be table rows
          return line.includes('\t') || line.match(/\s{3,}/);
        });

        if (potentialTableLines.length >= 2) {
          // Create a simple table structure
          const cells = potentialTableLines.map((line: string, rowIndex: number) => {
            const columns = line.split(/\s{3,}|\t/).filter(col => col.trim().length > 0);
            return columns.map((content: string, colIndex: number) => ({
              content: content.trim(),
              rowIndex,
              columnIndex: colIndex,
              confidence: 0.8 // Default confidence for pattern-based detection
            }));
          }).flat();

          if (cells.length > 0) {
            const maxColumns = Math.max(...potentialTableLines.map((line: string) =>
              line.split(/\s{3,}|\t/).filter((col: string) => col.trim().length > 0).length
            ));

            tables.push({
              rowCount: potentialTableLines.length,
              columnCount: maxColumns,
              cells
            });
          }
        }
      } catch (error) {
        logger.warn(`Failed to extract tables from ${imagePath}:`, error);
      }
    }

    return tables;
  }



  /**
   * Enhance analysis with GPT-4o for kitchen-specific understanding
   */
  private async enhanceWithGPTAnalysis(ocrResults: any, imagePaths: string[], options: DocumentProcessingOptions): Promise<any> {
    try {
      const prompt = `Analyze this kitchen design document text and enhance the extracted information:

Text Content:
${ocrResults.content}

Please provide enhanced analysis focusing on:
1. Kitchen cabinet specifications and counts
2. Appliance details and specifications
3. Material types and finishes
4. Dimensions and measurements
5. Hardware specifications

Return the analysis in a structured format that improves upon the OCR results.`;

      const gptAnalysis = await this.openAIService.analyzeImages(imagePaths, prompt, {
        useGPT4o: true,
        useReasoning: false,
        focusOnMaterials: true,
        focusOnHardware: true,
        enableMultiView: false
      });

      // Combine OCR results with GPT analysis
      return {
        ...ocrResults,
        enhancedContent: gptAnalysis?.analysis || '',
        gptConfidence: gptAnalysis?.confidence || 0.8,
        kitchenSpecificAnalysis: this.extractKitchenSpecificData(gptAnalysis?.analysis || '')
      };

    } catch (error) {
      logger.warn('GPT enhancement failed, using OCR results only:', error);
      return ocrResults;
    }
  }

  /**
   * Extract kitchen-specific data from GPT analysis
   */
  private extractKitchenSpecificData(analysis: string): any {
    const kitchenData = {
      cabinets: [] as Array<{ description: string; confidence: number }>,
      appliances: [] as Array<{ description: string; confidence: number }>,
      materials: [] as Array<{ description: string; confidence: number }>,
      dimensions: [] as Array<{ measurement: string; confidence: number }>
    };

    // Simple extraction patterns for kitchen elements
    const cabinetMatches = analysis.match(/cabinet[s]?[^.]*\d+/gi) || [];
    const applianceMatches = analysis.match(/(refrigerator|oven|dishwasher|microwave|range)[^.]*\d*/gi) || [];
    const materialMatches = analysis.match(/(wood|granite|quartz|marble|steel|laminate)[^.]*\d*/gi) || [];
    const dimensionMatches = analysis.match(/\d+\s*(mm|cm|m|in|ft|'|")/gi) || [];

    kitchenData.cabinets = cabinetMatches.map(match => ({ description: match, confidence: 0.8 }));
    kitchenData.appliances = applianceMatches.map(match => ({ description: match, confidence: 0.8 }));
    kitchenData.materials = materialMatches.map(match => ({ description: match, confidence: 0.8 }));
    kitchenData.dimensions = dimensionMatches.map(match => ({ measurement: match, confidence: 0.8 }));

    return kitchenData;
  }

  /**
   * Format analysis results in Azure Document Intelligence compatible format
   */
  private formatAnalysisResult(
    ocrResults: any,
    tables: any[],
    keyValuePairs: any[],
    processingTime: number,
    options: DocumentProcessingOptions
  ): DocumentAnalysisResult {
    return {
      documentType: this.detectDocumentType(ocrResults),
      extractedText: ocrResults.content,
      tables,
      layout: {
        pages: ocrResults.pages || []
      },
      keyValuePairs,
      confidence: ocrResults.confidence || 0.8,
      processingTime,
      metadata: {
        modelUsed: options.modelType || 'tesseract-ocr',
        apiVersion: 'open-source-v1.0',
        pagesProcessed: ocrResults.pages?.length || 0,
        timestamp: new Date().toISOString()
      }
    };
  }



  /**
   * Load configuration from environment variables
   */
  private loadConfiguration(): DocumentIntelligenceConfig {
    return {
      endpoint: 'open-source-implementation',
      apiKey: 'not-required',
      apiVersion: 'open-source-v1.0',
      defaultModel: 'tesseract-ocr',
      maxRetries: 3,
      retryDelay: 1000
    };
  }

  /**
   * Extract key-value pairs from OCR results (overloaded method)
   */
  private async extractKeyValuePairsFromOCR(ocrResults: any): Promise<any[]> {
    const keyValuePairs = [];
    const text = ocrResults.content;

    // Simple pattern matching for key-value pairs
    const patterns = [
      /([A-Za-z\s]+):\s*([^\n\r]+)/g, // "Key: Value" pattern
      /([A-Za-z\s]+)\s*=\s*([^\n\r]+)/g, // "Key = Value" pattern
      /([A-Za-z\s]+)\s*-\s*([^\n\r]+)/g  // "Key - Value" pattern
    ];

    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const key = match[1]?.trim();
        const value = match[2]?.trim();

        if (key && value && key.length > 2 && value.length > 0) {
          keyValuePairs.push({
            key,
            value,
            confidence: 0.7 // Default confidence for pattern-based extraction
          });
        }
      }
    }

    return keyValuePairs;
  }

  /**
   * Detect document type based on content analysis
   */
  private detectDocumentType(result: any): string {
    const content = result.content?.toLowerCase() || '';

    // Kitchen-specific document type detection
    if (content.includes('cabinet') || content.includes('kitchen') || content.includes('countertop')) {
      if (content.includes('specification') || content.includes('spec')) {
        return 'kitchen_specification';
      }
      if (content.includes('drawing') || content.includes('plan') || content.includes('layout')) {
        return 'kitchen_plan';
      }
      if (content.includes('price') || content.includes('cost') || content.includes('quote')) {
        return 'pricing_document';
      }
      return 'kitchen_document';
    }

    // General document types
    if (content.includes('table') && result.tables?.length > 0) {
      return 'tabular_document';
    }

    return 'general_document';
  }

  /**
   * Calculate overall confidence score
   */
  private calculateOverallConfidence(result: any): number {
    const confidenceScores: number[] = [];

    // Collect confidence scores from various elements
    if (result.pages) {
      result.pages.forEach((page: any) => {
        if (page.lines) {
          page.lines.forEach((line: any) => {
            if (line.confidence) {
              confidenceScores.push(line.confidence);
            }
          });
        }
      });
    }

    if (result.tables) {
      result.tables.forEach((table: any) => {
        if (table.cells) {
          table.cells.forEach((cell: any) => {
            if (cell.confidence) {
              confidenceScores.push(cell.confidence);
            }
          });
        }
      });
    }

    if (confidenceScores.length === 0) {
      return 0.8; // Default confidence if no scores available
    }

    // Calculate weighted average confidence
    return confidenceScores.reduce((sum, score) => sum + score, 0) / confidenceScores.length;
  }

  /**
   * Sanitize options for logging (remove sensitive data)
   */
  private sanitizeOptionsForLogging(options: DocumentProcessingOptions): any {
    return {
      modelType: options.modelType,
      extractTables: options.extractTables,
      extractKeyValuePairs: options.extractKeyValuePairs,
      enableKitchenAnalysis: options.enableKitchenAnalysis
    };
  }

  /**
   * Get performance metrics for the service
   * Phase 3.1: Performance monitoring and analytics
   */
  getPerformanceMetrics(operationFilter?: string) {
    return this.performanceTracker.getPerformanceReport(operationFilter);
  }

  /**
   * Get async operation configuration
   */
  getAsyncConfiguration() {
    return this.asyncManager.getConfig();
  }

  /**
   * Update async operation configuration
   */
  updateAsyncConfiguration(config: Partial<AsyncOperationConfig>) {
    this.asyncManager.updateConfig(config);
    logger.info('Async operation configuration updated', config);
  }

  /**
   * Get summary statistics for monitoring
   */
  getSummaryStatistics() {
    const performanceStats = this.performanceTracker.getSummaryStats();
    const asyncConfig = this.asyncManager.getConfig();

    return {
      performance: performanceStats,
      configuration: {
        timeout: asyncConfig.timeout,
        maxRetries: asyncConfig.maxRetries,
        concurrencyLimit: asyncConfig.concurrencyLimit
      },
      serviceHealth: {
        initialized: this.initializationManager.isAvailable(),
        fileSystemReady: true // FileSystemManager is always ready
      }
    };
  }

  /**
   * Clear performance history
   */
  clearPerformanceHistory(): void {
    this.performanceTracker.clearHistory();
    logger.info('Performance history cleared');
  }

  /**
   * Cleanup resources and terminate Tesseract worker
   * Phase 3.1: Enhanced with async operation management
   */
  async cleanup(): Promise<void> {
    const cleanupResult = await this.asyncManager.executeWithRetry(
      async () => {
        // Cleanup initialization manager (handles Tesseract worker termination)
        await this.initializationManager.cleanup();

        // Clear performance tracking history
        this.performanceTracker.clearHistory();

        logger.info('Document Intelligence service cleaned up successfully');
      },
      'service_cleanup',
      { timeout: 10000, maxRetries: 1 }
    );

    if (!cleanupResult.success) {
      logger.warn('Error during cleanup:', cleanupResult.error);
      throw cleanupResult.error;
    }
  }
}