/**
 * Document Intelligence Service Types
 * 
 * Type definitions for Azure AI Document Intelligence integration
 * following existing service patterns in Blackveil Design Mind.
 */

/**
 * Document Intelligence configuration
 */
export interface DocumentIntelligenceConfig {
  endpoint: string;
  apiKey: string;
  apiVersion: string;
  defaultModel: string;
  maxRetries: number;
  retryDelay: number;
}

/**
 * Document processing options
 */
export interface DocumentProcessingOptions {
  modelType?: 'prebuilt-layout' | 'prebuilt-read' | 'prebuilt-document' | 'prebuilt-invoice' | 'prebuilt-receipt';
  extractTables?: boolean;
  extractKeyValuePairs?: boolean;
  enableKitchenAnalysis?: boolean;
  confidenceThreshold?: number;
  locale?: string;
  // File metadata for proper format detection
  originalFilename?: string;
  mimetype?: string;
}

/**
 * Document analysis result
 */
export interface DocumentAnalysisResult {
  documentType: string;
  extractedText: string;
  tables: DocumentTable[];
  layout: DocumentLayout;
  keyValuePairs: KeyValuePair[];
  confidence: number;
  processingTime: number;
  metadata: DocumentMetadata;
}

/**
 * Document table structure
 */
export interface DocumentTable {
  rowCount: number;
  columnCount: number;
  cells: TableCell[];
}

/**
 * Table cell data
 */
export interface TableCell {
  content: string;
  rowIndex: number;
  columnIndex: number;
  confidence: number;
}

/**
 * Document layout information
 */
export interface DocumentLayout {
  pages: DocumentPage[];
}

/**
 * Document page structure
 */
export interface DocumentPage {
  pageNumber: number;
  width: number;
  height: number;
  unit: string;
  lines: DocumentLine[];
}

/**
 * Document line with spatial information
 */
export interface DocumentLine {
  content: string;
  boundingBox: number[];
  confidence: number;
}

/**
 * Key-value pair extracted from document
 */
export interface KeyValuePair {
  key: string;
  value: string;
  confidence: number;
}

/**
 * Document metadata
 */
export interface DocumentMetadata {
  modelUsed: string;
  apiVersion: string;
  pagesProcessed: number;
  timestamp: string;
}

/**
 * Enhanced analysis result combining Document Intelligence with existing analysis
 */
export interface EnhancedDocumentAnalysisResult {
  analysisId: string;
  documentIntelligence: DocumentAnalysisResult;
  visionAnalysis?: any; // Existing GPT-4o vision analysis
  combinedInsights: {
    structuredData: any;
    extractedDimensions: string[];
    identifiedComponents: string[];
    qualityScore: number;
  };
  processingMetrics: {
    documentProcessingTime: number;
    visionProcessingTime: number;
    totalProcessingTime: number;
  };
}

/**
 * Kitchen-specific document analysis
 */
export interface KitchenDocumentAnalysis {
  documentType: 'kitchen_specification' | 'kitchen_plan' | 'pricing_document' | 'kitchen_document';
  extractedComponents: {
    cabinets: CabinetSpecification[];
    appliances: ApplianceSpecification[];
    materials: MaterialSpecification[];
    dimensions: DimensionSpecification[];
  };
  specifications: {
    totalCabinets: number;
    estimatedCost?: number;
    roomDimensions?: {
      length: number;
      width: number;
      height: number;
      unit: string;
    };
  };
  confidence: number;
}

/**
 * Cabinet specification from document
 */
export interface CabinetSpecification {
  type: 'upper' | 'lower' | 'pantry' | 'island' | 'vanity';
  dimensions: {
    width: number;
    height: number;
    depth: number;
    unit: string;
  };
  material?: string;
  finish?: string;
  hardware?: string;
  quantity: number;
  confidence: number;
}

/**
 * Appliance specification from document
 */
export interface ApplianceSpecification {
  type: string;
  brand?: string;
  model?: string;
  dimensions?: {
    width: number;
    height: number;
    depth: number;
    unit: string;
  };
  specifications?: string[];
  confidence: number;
}

/**
 * Material specification from document
 */
export interface MaterialSpecification {
  type: 'countertop' | 'backsplash' | 'flooring' | 'cabinet_door' | 'hardware';
  material: string;
  finish?: string;
  color?: string;
  quantity?: number;
  unit?: string;
  confidence: number;
}

/**
 * Dimension specification from document
 */
export interface DimensionSpecification {
  element: string;
  measurement: number;
  unit: string;
  tolerance?: number;
  confidence: number;
}

/**
 * Document Intelligence service health status
 */
export interface DocumentIntelligenceHealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  details: {
    clientInitialized: boolean;
    configurationValid: boolean;
    lastChecked: string;
    errorMessage?: string;
  };
}

/**
 * Batch processing result
 */
export interface BatchDocumentAnalysisResult {
  successful: DocumentAnalysisResult[];
  failed: Array<{
    documentPath: string;
    error: string;
  }>;
  summary: {
    totalDocuments: number;
    successfulCount: number;
    failedCount: number;
    totalProcessingTime: number;
  };
}

/**
 * Document Intelligence API error
 */
export interface DocumentIntelligenceError extends Error {
  code?: string;
  statusCode?: number;
  details?: any;
}

/**
 * Progress callback for document analysis
 */
export interface DocumentAnalysisProgress {
  status: 'notStarted' | 'running' | 'succeeded' | 'failed';
  percentCompleted?: number;
  createdDateTime?: string;
  lastUpdatedDateTime?: string;
}

/**
 * Document Intelligence service metrics
 */
export interface DocumentIntelligenceMetrics {
  totalDocumentsProcessed: number;
  averageProcessingTime: number;
  successRate: number;
  errorRate: number;
  averageConfidence: number;
  modelUsageStats: {
    [modelType: string]: {
      usageCount: number;
      averageProcessingTime: number;
      averageConfidence: number;
    };
  };
}

// Phase 2: Enhanced type definitions for better error handling and type safety
/**
 * OCR line result with enhanced error handling
 */
export interface OCRLineResult {
  content: string;
  boundingBox: number[];
  confidence: number;
}

/**
 * OCR page result with error tracking
 */
export interface OCRPageResult {
  pageNumber: number;
  width: number;
  height: number;
  unit: string;
  lines: OCRLineResult[];
  text: string;
  error?: string;
}

/**
 * OCR analysis result with enhanced structure
 */
export interface OCRAnalysisResult {
  content: string;
  pages: OCRPageResult[];
  confidence: number;
}

/**
 * Tesseract line structure for type safety
 */
export interface TesseractLine {
  text: string;
  confidence: number;
  bbox: {
    x0: number;
    y0: number;
    x1: number;
    y1: number;
  };
}
