import { promises as fs } from 'fs';
import path from 'path';
import pdf2pic from 'pdf2pic';
import { spawn } from 'child_process';
import { createModuleLogger } from '@/utils/logger';

const logger = createModuleLogger('DocumentFileSystemManager');

/**
 * Document File System Manager
 * 
 * Handles all file system operations for document processing including:
 * - Async directory creation and cleanup
 * - Temp file management with proper error handling
 * - PDF-to-image conversion coordination
 * - Resource cleanup and error recovery
 * 
 * Part of the modular DocumentIntelligenceService architecture.
 */
export class DocumentFileSystemManager {
  private tempDir: string;

  constructor(tempDir: string) {
    this.tempDir = tempDir;
  }

  /**
   * Initialize file system resources
   */
  async initialize(): Promise<void> {
    await this.ensureDirectoryExists(this.tempDir);
    logger.info('Document file system manager initialized', { tempDir: this.tempDir });
  }

  /**
   * Ensure directory exists using async operations
   */
  async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
      logger.debug('Created directory', { dirPath });
    }
  }

  /**
   * Create unique temporary directory for processing
   */
  async createTempDirectory(prefix: string = 'doc_processing'): Promise<string> {
    const uniqueDir = path.join(this.tempDir, `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
    await this.ensureDirectoryExists(uniqueDir);
    return uniqueDir;
  }

  /**
   * Convert PDF to images using multiple strategies with enhanced error handling
   */
  async convertPDFToImages(pdfPath: string): Promise<string[]> {
    const outputDir = await this.createTempDirectory('pdf_conversion');

    try {
      // Verify PDF file exists and is readable
      await this.validatePDFFile(pdfPath);

      logger.info('Converting PDF to images', {
        pdfPath,
        outputDir
      });

      // Try multiple conversion strategies
      let imagePaths: string[] = [];
      let lastError: Error | null = null;

      // Strategy 1: Try pdftoppm directly (most reliable)
      try {
        imagePaths = await this.convertPDFWithPdftoppm(pdfPath, outputDir);
        if (imagePaths.length > 0) {
          logger.info('PDF conversion completed with pdftoppm', {
            pdfPath,
            imagesGenerated: imagePaths.length
          });
          return imagePaths;
        }
      } catch (error) {
        lastError = error as Error;
        logger.warn('pdftoppm conversion failed, trying pdf2pic', {
          error: lastError.message
        });
      }

      // Strategy 2: Try pdf2pic as fallback
      try {
        imagePaths = await this.convertPDFWithPdf2pic(pdfPath, outputDir);
        if (imagePaths.length > 0) {
          logger.info('PDF conversion completed with pdf2pic', {
            pdfPath,
            imagesGenerated: imagePaths.length
          });
          return imagePaths;
        }
      } catch (error) {
        lastError = error as Error;
        logger.warn('pdf2pic conversion failed', {
          error: lastError.message
        });
      }

      throw new Error(`All PDF conversion strategies failed. Last error: ${lastError?.message || 'Unknown error'}`);

    } catch (error) {
      logger.error('PDF conversion failed', {
        pdfPath,
        outputDir,
        error: (error as Error).message
      });

      // Clean up output directory on failure
      await this.cleanupDirectory(outputDir);
      throw new Error(`PDF conversion failed: ${(error as Error).message}`);
    }
  }

  /**
   * Validate PDF file exists and is readable
   */
  private async validatePDFFile(pdfPath: string): Promise<void> {
    try {
      const stats = await fs.stat(pdfPath);
      if (stats.size === 0) {
        throw new Error(`PDF file is empty: ${pdfPath}`);
      }
    } catch (error) {
      if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
        throw new Error(`PDF file not found: ${pdfPath}`);
      }
      throw error;
    }
  }

  /**
   * Convert PDF to images using pdftoppm directly
   */
  private async convertPDFWithPdftoppm(pdfPath: string, outputDir: string): Promise<string[]> {
    return new Promise((resolve, reject) => {
      const pdftoppmPath = '/opt/homebrew/bin/pdftoppm';
      const outputPrefix = path.join(outputDir, 'page');

      const args = [
        '-png',           // Output PNG format
        '-r', '300',      // 300 DPI resolution
        pdfPath,          // Input PDF file
        outputPrefix      // Output prefix
      ];

      logger.debug('Running pdftoppm conversion', {
        command: pdftoppmPath,
        args,
        pdfPath,
        outputDir
      });

      const childProcess = spawn(pdftoppmPath, args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          PATH: `/opt/homebrew/bin:${process.env.PATH}`
        }
      });

      let stdout = '';
      let stderr = '';

      childProcess.stdout?.on('data', (data: Buffer) => {
        stdout += data.toString();
      });

      childProcess.stderr?.on('data', (data: Buffer) => {
        stderr += data.toString();
      });

      childProcess.on('close', async (code: number | null) => {
        if (code !== 0) {
          logger.error('pdftoppm conversion failed', {
            code,
            stderr,
            stdout
          });
          reject(new Error(`pdftoppm failed with code ${code}: ${stderr}`));
          return;
        }

        try {
          const pngFiles = await this.findGeneratedPNGFiles(outputDir);
          if (pngFiles.length === 0) {
            reject(new Error('No PNG files were generated by pdftoppm'));
            return;
          }

          logger.debug('pdftoppm conversion successful', {
            filesGenerated: pngFiles.length,
            files: pngFiles
          });

          resolve(pngFiles);
        } catch (error) {
          reject(new Error(`Failed to read output directory: ${(error as Error).message}`));
        }
      });

      childProcess.on('error', (error: Error) => {
        logger.error('pdftoppm process error', { error: error.message });
        reject(new Error(`Failed to start pdftoppm: ${error.message}`));
      });
    });
  }

  /**
   * Convert PDF using pdf2pic library
   */
  private async convertPDFWithPdf2pic(pdfPath: string, outputDir: string): Promise<string[]> {
    const convert = pdf2pic.fromPath(pdfPath, {
      density: 300,
      saveFilename: 'page',
      savePath: outputDir,
      format: 'png',
      width: 2048,
      height: 2048
    });

    const results = await convert.bulk(-1);
    const imagePaths = results
      .map(result => result.path)
      .filter((path): path is string => path !== undefined);

    return imagePaths;
  }

  /**
   * Find generated PNG files in directory
   */
  private async findGeneratedPNGFiles(outputDir: string): Promise<string[]> {
    const files = await fs.readdir(outputDir);
    return files
      .filter(file => file.endsWith('.png'))
      .sort()
      .map(file => path.join(outputDir, file));
  }

  /**
   * Clean up temporary files with parallel execution
   */
  async cleanupTempFiles(filePaths: string[]): Promise<void> {
    const cleanupPromises = filePaths.map(async (filePath) => {
      try {
        await fs.unlink(filePath);
        
        // Check if parent directory is empty and remove it
        const dir = path.dirname(filePath);
        try {
          const files = await fs.readdir(dir);
          if (files.length === 0) {
            await fs.rmdir(dir);
          }
        } catch {
          // Directory not empty or already removed, ignore
        }
      } catch (error) {
        logger.warn(`Failed to cleanup temp file ${filePath}:`, error);
      }
    });

    await Promise.allSettled(cleanupPromises);
  }

  /**
   * Clean up entire directory
   */
  async cleanupDirectory(dirPath: string): Promise<void> {
    try {
      await fs.rm(dirPath, { recursive: true, force: true });
      logger.debug('Cleaned up directory', { dirPath });
    } catch (error) {
      logger.warn('Failed to cleanup directory:', { dirPath, error });
    }
  }

  /**
   * Get temp directory path
   */
  getTempDirectory(): string {
    return this.tempDir;
  }

  /**
   * Check if file exists
   */
  async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get file stats
   */
  async getFileStats(filePath: string): Promise<{ size: number; isFile: boolean }> {
    const stats = await fs.stat(filePath);
    return {
      size: stats.size,
      isFile: stats.isFile()
    };
  }
}
