import { createModuleLogger } from '@/utils/logger';

const logger = createModuleLogger('AsyncOperationManager');

export interface AsyncOperationConfig {
  timeout?: number;
  maxRetries?: number;
  retryDelayBase?: number;
  maxRetryDelay?: number;
  concurrencyLimit?: number;
}

export interface AsyncOperationResult<T> {
  success: boolean;
  data?: T;
  error?: Error;
  duration: number;
  retryCount: number;
}

/**
 * Async Operation Manager
 * 
 * Provides standardized async operation handling with timeout management,
 * retry logic, concurrency control, and performance tracking.
 * 
 * Phase 3.1: Async/Await Pattern Standardization
 */
export class AsyncOperationManager {
  private config: Required<AsyncOperationConfig>;
  private activeConcurrentOps = 0;
  private pendingOperations: Array<() => Promise<void>> = [];

  constructor(config: AsyncOperationConfig = {}) {
    this.config = {
      timeout: config.timeout || 30000, // 30 seconds default
      maxRetries: config.maxRetries || 3,
      retryDelayBase: config.retryDelayBase || 1000,
      maxRetryDelay: config.maxRetryDelay || 10000,
      concurrencyLimit: config.concurrencyLimit || 5
    };
  }

  /**
   * Execute async operation with timeout, retry, and error handling
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string,
    customConfig?: Partial<AsyncOperationConfig>
  ): Promise<AsyncOperationResult<T>> {
    const config = { ...this.config, ...customConfig };
    const startTime = Date.now();
    let lastError: Error | undefined;
    let retryCount = 0;

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        // Apply timeout wrapper
        const result = await this.withTimeout(operation(), config.timeout, operationName);
        
        const duration = Date.now() - startTime;
        logger.debug(`Operation "${operationName}" succeeded`, {
          attempt: attempt + 1,
          duration,
          retryCount: attempt
        });

        return {
          success: true,
          data: result,
          duration,
          retryCount: attempt
        };

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        retryCount = attempt;

        logger.warn(`Operation "${operationName}" failed on attempt ${attempt + 1}`, {
          error: lastError.message,
          attempt: attempt + 1,
          maxRetries: config.maxRetries
        });

        // Don't retry on the last attempt
        if (attempt < config.maxRetries) {
          const delay = this.calculateRetryDelay(attempt, config);
          await this.sleep(delay);
        }
      }
    }

    const duration = Date.now() - startTime;
    logger.error(`Operation "${operationName}" failed after all retries`, {
      retryCount,
      duration,
      finalError: lastError?.message
    });

    return {
      success: false,
      error: lastError,
      duration,
      retryCount
    };
  }

  /**
   * Execute multiple operations with concurrency control
   */
  async executeWithConcurrencyControl<T>(
    operations: Array<() => Promise<T>>,
    operationName: string
  ): Promise<AsyncOperationResult<T>[]> {
    const results: AsyncOperationResult<T>[] = [];
    const batches = this.createBatches(operations, this.config.concurrencyLimit);

    for (const batch of batches) {
      const batchPromises = batch.map(operation => 
        this.executeWithRetry(operation, `${operationName}-batch`)
      );

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return results;
  }

  /**
   * Execute operations in parallel with Promise.all and proper error handling
   */
  async executeInParallel<T>(
    operations: Array<() => Promise<T>>,
    operationName: string,
    failFast: boolean = false
  ): Promise<AsyncOperationResult<T>[]> {
    const startTime = Date.now();

    try {
      if (failFast) {
        // Use Promise.all - fails fast on first error
        const results = await Promise.all(
          operations.map(op => this.withTimeout(op(), this.config.timeout, operationName))
        );

        return results.map((data, index) => ({
          success: true,
          data,
          duration: Date.now() - startTime,
          retryCount: 0
        }));
      } else {
        // Use Promise.allSettled - continues even if some operations fail
        const results = await Promise.allSettled(
          operations.map(op => this.withTimeout(op(), this.config.timeout, operationName))
        );

        return results.map((result, index) => {
          const duration = Date.now() - startTime;
          
          if (result.status === 'fulfilled') {
            return {
              success: true,
              data: result.value,
              duration,
              retryCount: 0
            };
          } else {
            return {
              success: false,
              error: result.reason instanceof Error ? result.reason : new Error(String(result.reason)),
              duration,
              retryCount: 0
            };
          }
        });
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`Parallel execution failed for "${operationName}"`, {
        error: error instanceof Error ? error.message : String(error),
        duration
      });

      // Return error result for all operations
      return operations.map(() => ({
        success: false,
        error: error instanceof Error ? error : new Error(String(error)),
        duration,
        retryCount: 0
      }));
    }
  }

  /**
   * Add timeout wrapper to any async operation
   */
  private async withTimeout<T>(
    promise: Promise<T>,
    timeoutMs: number,
    operationName: string
  ): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Operation "${operationName}" timed out after ${timeoutMs}ms`));
      }, timeoutMs);
    });

    return Promise.race([promise, timeoutPromise]);
  }

  /**
   * Calculate exponential backoff delay with jitter
   */
  private calculateRetryDelay(attempt: number, config: Required<AsyncOperationConfig>): number {
    const exponentialDelay = config.retryDelayBase * Math.pow(2, attempt);
    const jitter = Math.random() * 0.1 * exponentialDelay; // 10% jitter
    const delay = Math.min(exponentialDelay + jitter, config.maxRetryDelay);
    
    return Math.floor(delay);
  }

  /**
   * Create batches for concurrency control
   */
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Sleep utility for retry delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get current configuration
   */
  getConfig(): Required<AsyncOperationConfig> {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<AsyncOperationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.debug('AsyncOperationManager configuration updated', this.config);
  }
}
