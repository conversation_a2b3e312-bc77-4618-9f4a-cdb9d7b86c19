import { createModuleLogger } from '@/utils/logger';

const logger = createModuleLogger('PerformanceTracker');

export interface PerformanceMetric {
  operationName: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  memoryUsage?: NodeJS.MemoryUsage;
  metadata?: Record<string, any>;
}

export interface PerformanceReport {
  totalOperations: number;
  averageDuration: number;
  minDuration: number;
  maxDuration: number;
  totalDuration: number;
  memoryStats?: {
    averageHeapUsed: number;
    maxHeapUsed: number;
    averageExternal: number;
    maxExternal: number;
  };
  operationBreakdown: Record<string, {
    count: number;
    averageDuration: number;
    totalDuration: number;
  }>;
}

/**
 * Performance Tracker
 * 
 * Tracks performance metrics for async operations including duration,
 * memory usage, and operation-specific statistics.
 * 
 * Phase 3.1: Async/Await Pattern Standardization
 */
export class PerformanceTracker {
  private metrics: PerformanceMetric[] = [];
  private activeOperations = new Map<string, PerformanceMetric>();
  private maxMetricsHistory: number;

  constructor(maxMetricsHistory: number = 1000) {
    this.maxMetricsHistory = maxMetricsHistory;
  }

  /**
   * Start tracking an operation
   */
  startOperation(operationName: string, metadata?: Record<string, any>): string {
    const operationId = `${operationName}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const metric: PerformanceMetric = {
      operationName,
      startTime: Date.now(),
      memoryUsage: process.memoryUsage(),
      metadata
    };

    this.activeOperations.set(operationId, metric);
    
    logger.debug(`Started tracking operation: ${operationName}`, {
      operationId,
      metadata
    });

    return operationId;
  }

  /**
   * End tracking an operation
   */
  endOperation(operationId: string, metadata?: Record<string, any>): PerformanceMetric | null {
    const metric = this.activeOperations.get(operationId);
    
    if (!metric) {
      logger.warn(`Attempted to end unknown operation: ${operationId}`);
      return null;
    }

    const endTime = Date.now();
    const duration = endTime - metric.startTime;
    const endMemoryUsage = process.memoryUsage();

    const completedMetric: PerformanceMetric = {
      ...metric,
      endTime,
      duration,
      metadata: { ...metric.metadata, ...metadata }
    };

    // Remove from active operations
    this.activeOperations.delete(operationId);

    // Add to metrics history
    this.addToHistory(completedMetric);

    logger.debug(`Completed tracking operation: ${metric.operationName}`, {
      operationId,
      duration,
      memoryDelta: {
        heapUsed: endMemoryUsage.heapUsed - (metric.memoryUsage?.heapUsed || 0),
        external: endMemoryUsage.external - (metric.memoryUsage?.external || 0)
      }
    });

    return completedMetric;
  }

  /**
   * Track a complete operation with automatic timing
   */
  async trackOperation<T>(
    operationName: string,
    operation: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<{ result: T; metric: PerformanceMetric }> {
    const operationId = this.startOperation(operationName, metadata);
    
    try {
      const result = await operation();
      const metric = this.endOperation(operationId, { success: true });
      
      return {
        result,
        metric: metric!
      };
    } catch (error) {
      const metric = this.endOperation(operationId, { 
        success: false, 
        error: error instanceof Error ? error.message : String(error)
      });
      
      throw error;
    }
  }

  /**
   * Get performance report for all tracked operations
   */
  getPerformanceReport(operationFilter?: string): PerformanceReport {
    const filteredMetrics = operationFilter 
      ? this.metrics.filter(m => m.operationName.includes(operationFilter))
      : this.metrics;

    if (filteredMetrics.length === 0) {
      return {
        totalOperations: 0,
        averageDuration: 0,
        minDuration: 0,
        maxDuration: 0,
        totalDuration: 0,
        operationBreakdown: {}
      };
    }

    const durations = filteredMetrics
      .map(m => m.duration)
      .filter((d): d is number => d !== undefined);

    const totalDuration = durations.reduce((sum, d) => sum + d, 0);
    const averageDuration = totalDuration / durations.length;
    const minDuration = Math.min(...durations);
    const maxDuration = Math.max(...durations);

    // Calculate memory stats if available
    const memoryMetrics = filteredMetrics
      .map(m => m.memoryUsage)
      .filter((m): m is NodeJS.MemoryUsage => m !== undefined);

    let memoryStats;
    if (memoryMetrics.length > 0) {
      const heapUsedValues = memoryMetrics.map(m => m.heapUsed);
      const externalValues = memoryMetrics.map(m => m.external);

      memoryStats = {
        averageHeapUsed: heapUsedValues.reduce((sum, v) => sum + v, 0) / heapUsedValues.length,
        maxHeapUsed: Math.max(...heapUsedValues),
        averageExternal: externalValues.reduce((sum, v) => sum + v, 0) / externalValues.length,
        maxExternal: Math.max(...externalValues)
      };
    }

    // Calculate operation breakdown
    const operationBreakdown: Record<string, { count: number; averageDuration: number; totalDuration: number }> = {};
    
    for (const metric of filteredMetrics) {
      if (metric.duration === undefined) continue;

      if (!operationBreakdown[metric.operationName]) {
        operationBreakdown[metric.operationName] = {
          count: 0,
          averageDuration: 0,
          totalDuration: 0
        };
      }

      const breakdown = operationBreakdown[metric.operationName];
      breakdown.count++;
      breakdown.totalDuration += metric.duration;
      breakdown.averageDuration = breakdown.totalDuration / breakdown.count;
    }

    return {
      totalOperations: filteredMetrics.length,
      averageDuration,
      minDuration,
      maxDuration,
      totalDuration,
      memoryStats,
      operationBreakdown
    };
  }

  /**
   * Get metrics for a specific operation type
   */
  getOperationMetrics(operationName: string): PerformanceMetric[] {
    return this.metrics.filter(m => m.operationName === operationName);
  }

  /**
   * Get currently active operations
   */
  getActiveOperations(): Array<{ id: string; metric: PerformanceMetric; duration: number }> {
    const now = Date.now();
    return Array.from(this.activeOperations.entries()).map(([id, metric]) => ({
      id,
      metric,
      duration: now - metric.startTime
    }));
  }

  /**
   * Clear all metrics history
   */
  clearHistory(): void {
    this.metrics = [];
    logger.info('Performance metrics history cleared');
  }

  /**
   * Get summary statistics
   */
  getSummaryStats(): {
    totalTrackedOperations: number;
    activeOperations: number;
    averageOperationDuration: number;
    memoryUsageTrend: 'increasing' | 'decreasing' | 'stable' | 'unknown';
  } {
    const report = this.getPerformanceReport();
    const activeOps = this.getActiveOperations();

    // Calculate memory trend from recent operations
    let memoryUsageTrend: 'increasing' | 'decreasing' | 'stable' | 'unknown' = 'unknown';
    const recentMetrics = this.metrics.slice(-10);
    if (recentMetrics.length >= 2) {
      const firstMemory = recentMetrics[0].memoryUsage?.heapUsed || 0;
      const lastMemory = recentMetrics[recentMetrics.length - 1].memoryUsage?.heapUsed || 0;
      const difference = lastMemory - firstMemory;
      const threshold = 1024 * 1024; // 1MB threshold

      if (Math.abs(difference) < threshold) {
        memoryUsageTrend = 'stable';
      } else if (difference > 0) {
        memoryUsageTrend = 'increasing';
      } else {
        memoryUsageTrend = 'decreasing';
      }
    }

    return {
      totalTrackedOperations: report.totalOperations,
      activeOperations: activeOps.length,
      averageOperationDuration: report.averageDuration,
      memoryUsageTrend
    };
  }

  /**
   * Add metric to history with size management
   */
  private addToHistory(metric: PerformanceMetric): void {
    this.metrics.push(metric);

    // Maintain maximum history size
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics = this.metrics.slice(-this.maxMetricsHistory);
    }
  }
}
