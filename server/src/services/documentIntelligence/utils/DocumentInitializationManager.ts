import { createWorker, PSM } from 'tesseract.js';
import { createModuleLogger } from '@/utils/logger';

const logger = createModuleLogger('DocumentInitializationManager');

/**
 * Initialization states for the service
 */
export enum InitializationState {
  IDLE = 'idle',
  INITIALIZING = 'initializing',
  INITIALIZED = 'initialized',
  FAILED = 'failed'
}

/**
 * Initialization configuration
 */
export interface InitializationConfig {
  timeout: number;
  maxRetries: number;
  retryDelayBase: number;
  maxRetryDelay: number;
}

/**
 * Document Initialization Manager
 * 
 * Manages Tesseract worker lifecycle with proper state management:
 * - Initialization state machine (idle → initializing → initialized → failed)
 * - Timeout and retry logic with exponential backoff
 * - Resource cleanup and error recovery
 * - Thread-safe initialization with promise chaining
 * 
 * Part of the modular DocumentIntelligenceService architecture.
 */
export class DocumentInitializationManager {
  private tesseractWorker: any = null;
  private state: InitializationState = InitializationState.IDLE;
  private initializationPromise: Promise<void> | null = null;
  private config: InitializationConfig;
  private abortController: AbortController | null = null;

  constructor(config: Partial<InitializationConfig> = {}) {
    this.config = {
      timeout: config.timeout || 60000, // 60 seconds
      maxRetries: config.maxRetries || 3,
      retryDelayBase: config.retryDelayBase || 1000, // 1 second
      maxRetryDelay: config.maxRetryDelay || 10000 // 10 seconds
    };
  }

  /**
   * Initialize Tesseract worker with state management and retry logic
   */
  async initialize(): Promise<void> {
    // Return existing promise if already initializing
    if (this.state === InitializationState.INITIALIZING && this.initializationPromise) {
      return this.initializationPromise;
    }

    // Return immediately if already initialized
    if (this.state === InitializationState.INITIALIZED) {
      return;
    }

    // Start new initialization
    this.state = InitializationState.INITIALIZING;
    this.initializationPromise = this.performInitializationWithRetry()
      .then(() => {
        this.state = InitializationState.INITIALIZED;
        logger.info('Document Intelligence service initialized successfully');
      })
      .catch((error) => {
        this.state = InitializationState.FAILED;
        this.initializationPromise = null;
        this.cleanup();
        throw error;
      });

    return this.initializationPromise;
  }

  /**
   * Perform initialization with retry logic and exponential backoff
   */
  private async performInitializationWithRetry(): Promise<void> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        logger.info(`Initializing Tesseract worker (attempt ${attempt}/${this.config.maxRetries})`);

        // Create abort controller for this attempt
        this.abortController = new AbortController();

        // Initialize worker with timeout
        this.tesseractWorker = await this.initializeWorkerWithTimeout();

        logger.info('Tesseract worker initialized successfully', {
          attempt,
          totalAttempts: this.config.maxRetries
        });

        return;

      } catch (error) {
        lastError = error as Error;
        logger.warn(`Initialization attempt ${attempt} failed:`, {
          error: lastError.message,
          attempt,
          maxRetries: this.config.maxRetries
        });

        // Clean up failed worker
        await this.cleanupWorker();

        // Wait before retry (exponential backoff)
        if (attempt < this.config.maxRetries) {
          const delay = Math.min(
            this.config.retryDelayBase * Math.pow(2, attempt - 1),
            this.config.maxRetryDelay
          );
          await this.delay(delay);
        }
      }
    }

    // All attempts failed
    const finalError = new Error(
      `Failed to initialize Tesseract worker after ${this.config.maxRetries} attempts: ${lastError?.message || 'Unknown error'}`
    );
    
    logger.error('Tesseract worker initialization failed permanently', {
      attempts: this.config.maxRetries,
      lastError: lastError?.message
    });

    throw finalError;
  }

  /**
   * Initialize Tesseract worker with timeout using AbortController
   */
  private async initializeWorkerWithTimeout(): Promise<any> {
    return new Promise<any>(async (resolve, reject) => {
      // Set up timeout
      const timeoutId = setTimeout(() => {
        if (this.abortController) {
          this.abortController.abort();
        }
        reject(new Error('Tesseract initialization timeout'));
      }, this.config.timeout);

      try {
        // Check if already aborted
        if (this.abortController?.signal.aborted) {
          throw new Error('Initialization aborted');
        }

        // Initialize worker
        const worker = await this.createTesseractWorker();

        // Clear timeout on success
        clearTimeout(timeoutId);
        resolve(worker);

      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  /**
   * Create and configure Tesseract worker
   */
  private async createTesseractWorker(): Promise<any> {
    // Set Tesseract environment variables
    process.env.TESSDATA_PREFIX = '/opt/homebrew/share/tessdata/';

    const worker = await createWorker('eng', 1, {
      logger: (m: any) => {
        if (m.status === 'recognizing text') {
          // Only log progress at 25% intervals to reduce noise
          if (m.progress && m.progress % 0.25 < 0.01) {
            logger.debug('OCR Progress', { progress: Math.round(m.progress * 100) });
          }
        }
      }
    });

    await worker.setParameters({
      tessedit_pageseg_mode: PSM.SINGLE_BLOCK, // Single uniform block of text (avoids OSD issues)
      tessedit_char_whitelist: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz .,;:!?-()[]{}"\'/\\@#$%^&*+=<>|~`',
    });

    return worker;
  }

  /**
   * Check if service is available
   */
  isAvailable(): boolean {
    return this.state === InitializationState.INITIALIZED && this.tesseractWorker !== null;
  }

  /**
   * Get current initialization state
   */
  getState(): InitializationState {
    return this.state;
  }

  /**
   * Get Tesseract worker instance
   */
  getWorker(): any {
    if (!this.isAvailable()) {
      throw new Error('Tesseract worker is not available. Call initialize() first.');
    }
    return this.tesseractWorker;
  }

  /**
   * Reset initialization state (for testing or recovery)
   */
  async reset(): Promise<void> {
    await this.cleanup();
    this.state = InitializationState.IDLE;
    this.initializationPromise = null;
  }

  /**
   * Clean up all resources
   */
  async cleanup(): Promise<void> {
    try {
      // Abort any ongoing initialization
      if (this.abortController) {
        this.abortController.abort();
        this.abortController = null;
      }

      // Clean up worker
      await this.cleanupWorker();

      logger.info('Document initialization manager cleaned up successfully');
    } catch (error) {
      logger.warn('Error during initialization manager cleanup:', error);
    }
  }

  /**
   * Clean up Tesseract worker
   */
  private async cleanupWorker(): Promise<void> {
    if (this.tesseractWorker) {
      try {
        await this.tesseractWorker.terminate();
        logger.debug('Tesseract worker terminated successfully');
      } catch (error) {
        logger.warn('Failed to terminate Tesseract worker:', error);
      }
      this.tesseractWorker = null;
    }
  }

  /**
   * Delay utility for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get initialization configuration
   */
  getConfig(): InitializationConfig {
    return { ...this.config };
  }

  /**
   * Update initialization configuration
   */
  updateConfig(newConfig: Partial<InitializationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get initialization metrics
   */
  getMetrics(): {
    state: InitializationState;
    isAvailable: boolean;
    hasWorker: boolean;
    config: InitializationConfig;
  } {
    return {
      state: this.state,
      isAvailable: this.isAvailable(),
      hasWorker: this.tesseractWorker !== null,
      config: this.getConfig()
    };
  }
}
