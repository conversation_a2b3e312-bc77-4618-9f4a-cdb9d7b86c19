/**
 * Document Intelligence Service Module
 * 
 * Azure AI Document Intelligence integration for Blackveil Design Mind
 * Provides enhanced document processing capabilities for kitchen design documents.
 * 
 * Part of Phase 1: Azure Document Intelligence Integration
 * Maintains ~97-99% test success rate and follows existing service patterns.
 */

export { DocumentIntelligenceService } from './DocumentIntelligenceService';

export type {
  DocumentIntelligenceConfig,
  DocumentProcessingOptions,
  DocumentAnalysisResult,
  DocumentTable,
  TableCell,
  DocumentLayout,
  DocumentPage,
  DocumentLine,
  KeyValuePair,
  DocumentMetadata,
  EnhancedDocumentAnalysisResult,
  KitchenDocumentAnalysis,
  CabinetSpecification,
  ApplianceSpecification,
  MaterialSpecification,
  DimensionSpecification,
  DocumentIntelligenceHealthStatus,
  BatchDocumentAnalysisResult,
  DocumentIntelligenceError,
  DocumentAnalysisProgress,
  DocumentIntelligenceMetrics
} from './types';

// Create singleton instance for use throughout the application
import { DocumentIntelligenceService } from './DocumentIntelligenceService';

export const documentIntelligenceService = new DocumentIntelligenceService();

// Export service health check for monitoring
export const checkDocumentIntelligenceHealth = async () => {
  return await documentIntelligenceService.getHealthStatus();
};
