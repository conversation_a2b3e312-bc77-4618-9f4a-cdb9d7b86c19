import { createModuleLogger } from '@/utils/logger';
import { documentIntelligenceService } from './index';
import { openAIService } from '../openai/OpenAIService';
import { socketManager } from '../socketManager';
import { 
  DocumentAnalysisResult, 
  EnhancedDocumentAnalysisResult, 
  KitchenDocumentAnalysis,
  DocumentProcessingOptions 
} from './types';
import { AnalysisConfig, VisionAnalysisResult } from '../openaiService';

const logger = createModuleLogger('EnhancedAnalysisIntegration');

/**
 * Enhanced Analysis Integration Service
 * 
 * Integrates Azure Document Intelligence with existing GPT-4o vision analysis
 * to provide superior document processing capabilities for kitchen design documents.
 * 
 * Features:
 * - Combines structured document extraction with AI vision analysis
 * - Maintains backward compatibility with existing analysis pipeline
 * - Provides enhanced insights through data fusion
 * - Real-time progress updates via WebSocket
 */
export class EnhancedAnalysisIntegration {
  
  /**
   * Perform enhanced document analysis combining Document Intelligence and GPT-4o vision
   */
  async analyzeDocumentEnhanced(
    documentPath: string,
    analysisId: string,
    config: AnalysisConfig & { documentOptions?: DocumentProcessingOptions }
  ): Promise<EnhancedDocumentAnalysisResult> {
    const startTime = Date.now();
    
    logger.info('Starting enhanced document analysis', {
      analysisId,
      documentPath,
      hasDocumentIntelligence: documentIntelligenceService.isAvailable()
    });

    // Send initial progress update
    this.sendProgressUpdate(analysisId, 'document_analysis_start', 0, 'Starting enhanced document analysis...');

    try {
      // Step 1: Document Intelligence analysis (if available)
      let documentIntelligenceResult: DocumentAnalysisResult | null = null;
      
      if (documentIntelligenceService.isAvailable()) {
        this.sendProgressUpdate(analysisId, 'document_intelligence', 20, 'Extracting document structure...');
        
        documentIntelligenceResult = await documentIntelligenceService.analyzeDocument(
          documentPath,
          config.documentOptions || {}
        );
        
        logger.info('Document Intelligence analysis completed', {
          analysisId,
          documentType: documentIntelligenceResult.documentType,
          confidence: documentIntelligenceResult.confidence,
          processingTime: documentIntelligenceResult.processingTime
        });
      } else {
        logger.warn('Document Intelligence service not available, proceeding with vision-only analysis');
      }

      // Step 2: Traditional vision analysis (existing pipeline)
      this.sendProgressUpdate(analysisId, 'vision_analysis', 50, 'Performing AI vision analysis...');
      
      const visionAnalysisResult = await this.performVisionAnalysis(documentPath, analysisId, config);
      
      // Step 3: Combine insights from both analyses
      this.sendProgressUpdate(analysisId, 'insight_fusion', 80, 'Combining document and vision insights...');
      
      const combinedInsights = this.combineAnalysisResults(
        documentIntelligenceResult,
        visionAnalysisResult
      );

      const totalProcessingTime = Date.now() - startTime;

      // Step 4: Create enhanced result
      const enhancedResult: EnhancedDocumentAnalysisResult = {
        analysisId,
        documentIntelligence: documentIntelligenceResult || this.createEmptyDocumentResult(),
        visionAnalysis: visionAnalysisResult,
        combinedInsights,
        processingMetrics: {
          documentProcessingTime: documentIntelligenceResult?.processingTime || 0,
          visionProcessingTime: visionAnalysisResult?.processingTime || 0,
          totalProcessingTime
        }
      };

      this.sendProgressUpdate(analysisId, 'analysis_complete', 100, 'Enhanced analysis completed successfully');

      logger.info('Enhanced document analysis completed', {
        analysisId,
        totalProcessingTime,
        qualityScore: combinedInsights.qualityScore
      });

      return enhancedResult;

    } catch (error) {
      logger.error('Enhanced document analysis failed', {
        analysisId,
        error: error.message,
        processingTime: Date.now() - startTime
      });

      this.sendProgressUpdate(analysisId, 'analysis_error', 0, `Analysis failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Analyze kitchen-specific documents with specialized processing
   */
  async analyzeKitchenDocument(
    documentPath: string,
    analysisId: string,
    config: AnalysisConfig & { documentOptions?: DocumentProcessingOptions }
  ): Promise<KitchenDocumentAnalysis> {
    logger.info('Starting kitchen-specific document analysis', { analysisId, documentPath });

    if (!documentIntelligenceService.isAvailable()) {
      throw new Error('Document Intelligence service required for kitchen document analysis');
    }

    // Configure for kitchen-specific analysis
    const kitchenOptions: DocumentProcessingOptions = {
      ...config.documentOptions,
      enableKitchenAnalysis: true,
      extractTables: true,
      extractKeyValuePairs: true
    };

    const documentResult = await documentIntelligenceService.analyzeDocument(
      documentPath,
      kitchenOptions
    );

    // Process kitchen-specific elements
    const kitchenAnalysis = this.extractKitchenSpecifications(documentResult);

    logger.info('Kitchen document analysis completed', {
      analysisId,
      documentType: kitchenAnalysis.documentType,
      totalCabinets: kitchenAnalysis.specifications.totalCabinets,
      confidence: kitchenAnalysis.confidence
    });

    return kitchenAnalysis;
  }

  /**
   * Perform traditional vision analysis using existing pipeline
   */
  private async performVisionAnalysis(
    documentPath: string,
    analysisId: string,
    config: AnalysisConfig
  ): Promise<VisionAnalysisResult | null> {
    try {
      // For PDF documents, we need to convert to images first
      // This integrates with the existing PDF processing pipeline
      const imagePaths = [documentPath];
      
      // Use existing OpenAI service for vision analysis
      const prompt = this.generateEnhancedPrompt(config);
      
      return await openAIService.analyzeImages(imagePaths, prompt, config);
      
    } catch (error) {
      logger.error('Vision analysis failed', { error: error.message, analysisId });
      return null;
    }
  }

  /**
   * Combine Document Intelligence and vision analysis results
   */
  private combineAnalysisResults(
    documentResult: DocumentAnalysisResult | null,
    visionResult: VisionAnalysisResult | null
  ): EnhancedDocumentAnalysisResult['combinedInsights'] {
    const structuredData: any = {};
    const extractedDimensions: string[] = [];
    const identifiedComponents: string[] = [];
    let qualityScore = 0.5; // Default score

    // Process Document Intelligence results
    if (documentResult) {
      structuredData.documentStructure = {
        type: documentResult.documentType,
        extractedText: documentResult.extractedText,
        tables: documentResult.tables,
        keyValuePairs: documentResult.keyValuePairs
      };

      // Extract dimensions from structured data
      extractedDimensions.push(...this.extractDimensionsFromDocument(documentResult));
      
      // Extract components from structured data
      identifiedComponents.push(...this.extractComponentsFromDocument(documentResult));
      
      qualityScore += documentResult.confidence * 0.3;
    }

    // Process vision analysis results
    if (visionResult) {
      structuredData.visionInsights = {
        analysis: visionResult.analysis,
        confidence: visionResult.confidence,
        processingTime: visionResult.processingTime
      };

      // Extract additional insights from vision analysis
      identifiedComponents.push(...this.extractComponentsFromVision(visionResult));
      
      qualityScore += (visionResult.confidence || 0.8) * 0.4;
    }

    // Calculate final quality score
    qualityScore = Math.min(qualityScore, 1.0);

    return {
      structuredData,
      extractedDimensions: [...new Set(extractedDimensions)], // Remove duplicates
      identifiedComponents: [...new Set(identifiedComponents)], // Remove duplicates
      qualityScore
    };
  }

  /**
   * Extract kitchen specifications from document analysis
   */
  private extractKitchenSpecifications(documentResult: DocumentAnalysisResult): KitchenDocumentAnalysis {
    const extractedText = documentResult.extractedText.toLowerCase();
    
    // Basic kitchen component extraction (can be enhanced with more sophisticated parsing)
    const cabinets = this.extractCabinetSpecifications(documentResult);
    const appliances = this.extractApplianceSpecifications(documentResult);
    const materials = this.extractMaterialSpecifications(documentResult);
    const dimensions = this.extractDimensionSpecifications(documentResult);

    return {
      documentType: documentResult.documentType as any,
      extractedComponents: {
        cabinets,
        appliances,
        materials,
        dimensions
      },
      specifications: {
        totalCabinets: cabinets.length,
        estimatedCost: this.calculateEstimatedCost(cabinets, materials),
        roomDimensions: this.extractRoomDimensions(documentResult)
      },
      confidence: documentResult.confidence
    };
  }

  /**
   * Generate enhanced prompt for vision analysis
   */
  private generateEnhancedPrompt(config: AnalysisConfig): string {
    return `Analyze this kitchen design document with focus on:
    1. Cabinet identification and counting
    2. Spatial layout and measurements
    3. Material specifications
    4. Hardware and appliance details
    5. Overall design quality and completeness
    
    Provide detailed analysis with confidence scores for each element identified.`;
  }

  /**
   * Send progress updates via WebSocket
   */
  private sendProgressUpdate(
    analysisId: string,
    step: string,
    progress: number,
    message: string
  ): void {
    socketManager.sendAnalysisProgress({
      analysisId,
      step,
      progress,
      message,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Create empty document result for fallback
   */
  private createEmptyDocumentResult(): DocumentAnalysisResult {
    return {
      documentType: 'unknown',
      extractedText: '',
      tables: [],
      layout: { pages: [] },
      keyValuePairs: [],
      confidence: 0,
      processingTime: 0,
      metadata: {
        modelUsed: 'none',
        apiVersion: 'none',
        pagesProcessed: 0,
        timestamp: new Date().toISOString()
      }
    };
  }

  // Helper methods for data extraction (simplified implementations)
  private extractDimensionsFromDocument(result: DocumentAnalysisResult): string[] {
    const dimensions: string[] = [];
    const text = result.extractedText;
    
    // Simple regex patterns for common dimension formats
    const dimensionPatterns = [
      /(\d+(?:\.\d+)?)\s*(?:mm|cm|m|in|ft|'|")/gi,
      /(\d+(?:\.\d+)?)\s*x\s*(\d+(?:\.\d+)?)/gi
    ];
    
    dimensionPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        dimensions.push(...matches);
      }
    });
    
    return dimensions;
  }

  private extractComponentsFromDocument(result: DocumentAnalysisResult): string[] {
    const components: string[] = [];
    const text = result.extractedText.toLowerCase();
    
    const componentKeywords = [
      'cabinet', 'drawer', 'door', 'shelf', 'countertop', 'backsplash',
      'sink', 'faucet', 'appliance', 'refrigerator', 'oven', 'dishwasher'
    ];
    
    componentKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        components.push(keyword);
      }
    });
    
    return components;
  }

  private extractComponentsFromVision(result: VisionAnalysisResult): string[] {
    // Extract components mentioned in vision analysis
    const analysis = result.analysis?.toLowerCase() || '';
    return this.extractComponentsFromDocument({ extractedText: analysis } as DocumentAnalysisResult);
  }

  // Placeholder implementations for kitchen-specific extraction methods
  private extractCabinetSpecifications(result: DocumentAnalysisResult): any[] {
    return []; // Implementation would parse cabinet specifications from document
  }

  private extractApplianceSpecifications(result: DocumentAnalysisResult): any[] {
    return []; // Implementation would parse appliance specifications from document
  }

  private extractMaterialSpecifications(result: DocumentAnalysisResult): any[] {
    return []; // Implementation would parse material specifications from document
  }

  private extractDimensionSpecifications(result: DocumentAnalysisResult): any[] {
    return []; // Implementation would parse dimension specifications from document
  }

  private calculateEstimatedCost(cabinets: any[], materials: any[]): number | undefined {
    return undefined; // Implementation would calculate cost based on specifications
  }

  private extractRoomDimensions(result: DocumentAnalysisResult): any {
    return undefined; // Implementation would extract room dimensions from document
  }
}

// Create singleton instance
export const enhancedAnalysisIntegration = new EnhancedAnalysisIntegration();
