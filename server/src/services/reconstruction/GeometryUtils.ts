import { Point3D } from './types';

/**
 * Geometry Utilities for 3D Cabinet Reconstruction
 * 
 * Provides reusable geometric calculations and 3D model generation utilities.
 * Shared across all reconstruction services for consistency and performance.
 */
export class GeometryUtils {
  private static instance: GeometryUtils;

  static getInstance(): GeometryUtils {
    if (!GeometryUtils.instance) {
      GeometryUtils.instance = new GeometryUtils();
    }
    return GeometryUtils.instance;
  }

  /**
   * Generate vertices for a cabinet box
   */
  generateCabinetVertices(
    x: number, y: number, z: number,
    width: number, height: number, depth: number
  ): Point3D[] {
    return [
      // Bottom face (y = y)
      { x, y, z },                           // 0: Bottom-front-left
      { x: x + width, y, z },                // 1: Bottom-front-right
      { x: x + width, y, z: z + depth },     // 2: Bottom-back-right
      { x, y, z: z + depth },                // 3: Bottom-back-left
      
      // Top face (y = y + height)
      { x, y: y + height, z },               // 4: Top-front-left
      { x: x + width, y: y + height, z },    // 5: Top-front-right
      { x: x + width, y: y + height, z: z + depth }, // 6: Top-back-right
      { x, y: y + height, z: z + depth }     // 7: Top-back-left
    ];
  }

  /**
   * Generate faces for a cabinet box (indices into vertices array)
   * Uses counter-clockwise winding order for proper normal calculation
   */
  generateCabinetFaces(): number[][] {
    return [
      [0, 1, 2, 3], // Bottom face
      [4, 7, 6, 5], // Top face
      [0, 4, 5, 1], // Front face
      [2, 6, 7, 3], // Back face
      [0, 3, 7, 4], // Left face
      [1, 5, 6, 2]  // Right face
    ];
  }

  /**
   * Calculate distance between two 3D points
   */
  calculateDistance(point1: Point3D, point2: Point3D): number {
    const dx = point2.x - point1.x;
    const dy = point2.y - point1.y;
    const dz = point2.z - point1.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }

  /**
   * Calculate volume of a cabinet from its dimensions
   */
  calculateVolume(width: number, height: number, depth: number): number {
    return width * height * depth;
  }

  /**
   * Calculate bounding box for a set of vertices
   */
  calculateBoundingBox(vertices: Point3D[]): {
    min: Point3D;
    max: Point3D;
    center: Point3D;
    dimensions: { width: number; height: number; depth: number };
  } {
    if (vertices.length === 0) {
      throw new Error('Cannot calculate bounding box for empty vertex array');
    }

    let minX = vertices[0]!.x, maxX = vertices[0]!.x;
    let minY = vertices[0]!.y, maxY = vertices[0]!.y;
    let minZ = vertices[0]!.z, maxZ = vertices[0]!.z;

    for (const vertex of vertices) {
      minX = Math.min(minX, vertex.x);
      maxX = Math.max(maxX, vertex.x);
      minY = Math.min(minY, vertex.y);
      maxY = Math.max(maxY, vertex.y);
      minZ = Math.min(minZ, vertex.z);
      maxZ = Math.max(maxZ, vertex.z);
    }

    const min = { x: minX, y: minY, z: minZ };
    const max = { x: maxX, y: maxY, z: maxZ };
    const center = {
      x: (minX + maxX) / 2,
      y: (minY + maxY) / 2,
      z: (minZ + maxZ) / 2
    };
    const dimensions = {
      width: maxX - minX,
      height: maxY - minY,
      depth: maxZ - minZ
    };

    return { min, max, center, dimensions };
  }

  /**
   * Check if two cabinet bounding boxes intersect (for collision detection)
   */
  checkBoundingBoxIntersection(
    box1: { min: Point3D; max: Point3D },
    box2: { min: Point3D; max: Point3D }
  ): boolean {
    return (
      box1.min.x <= box2.max.x && box1.max.x >= box2.min.x &&
      box1.min.y <= box2.max.y && box1.max.y >= box2.min.y &&
      box1.min.z <= box2.max.z && box1.max.z >= box2.min.z
    );
  }

  /**
   * Calculate normal vector for a face (for lighting calculations)
   */
  calculateFaceNormal(vertices: Point3D[], faceIndices: number[]): Point3D {
    if (faceIndices.length < 3) {
      throw new Error('Face must have at least 3 vertices to calculate normal');
    }

    const v1 = vertices[faceIndices[0]!];
    const v2 = vertices[faceIndices[1]!];
    const v3 = vertices[faceIndices[2]!];

    if (!v1 || !v2 || !v3) {
      throw new Error('Invalid vertex indices for face normal calculation');
    }

    // Calculate two edge vectors
    const edge1 = {
      x: v2.x - v1.x,
      y: v2.y - v1.y,
      z: v2.z - v1.z
    };

    const edge2 = {
      x: v3.x - v1.x,
      y: v3.y - v1.y,
      z: v3.z - v1.z
    };

    // Calculate cross product for normal
    const normal = {
      x: edge1.y * edge2.z - edge1.z * edge2.y,
      y: edge1.z * edge2.x - edge1.x * edge2.z,
      z: edge1.x * edge2.y - edge1.y * edge2.x
    };

    // Normalize the vector
    const length = Math.sqrt(normal.x * normal.x + normal.y * normal.y + normal.z * normal.z);
    
    if (length === 0) {
      return { x: 0, y: 1, z: 0 }; // Default up vector
    }

    return {
      x: normal.x / length,
      y: normal.y / length,
      z: normal.z / length
    };
  }

  /**
   * Transform vertices by rotation and translation
   */
  transformVertices(
    vertices: Point3D[],
    rotation: { x: number; y: number; z: number },
    translation: Point3D
  ): Point3D[] {
    return vertices.map(vertex => {
      // Apply rotation (simplified - in production would use proper rotation matrices)
      let transformed = { ...vertex };

      // Apply translation
      transformed.x += translation.x;
      transformed.y += translation.y;
      transformed.z += translation.z;

      return transformed;
    });
  }

  /**
   * Generate wireframe edges from faces
   */
  generateWireframeEdges(faces: number[][]): Array<[number, number]> {
    const edges = new Set<string>();
    
    for (const face of faces) {
      for (let i = 0; i < face.length; i++) {
        const v1 = face[i]!;
        const v2 = face[(i + 1) % face.length]!;
        
        // Create edge key (ensure consistent ordering)
        const edgeKey = v1 < v2 ? `${v1}-${v2}` : `${v2}-${v1}`;
        edges.add(edgeKey);
      }
    }

    return Array.from(edges).map(edgeKey => {
      const [v1, v2] = edgeKey.split('-').map(Number);
      return [v1!, v2!] as [number, number];
    });
  }

  /**
   * Validate vertex array for geometric consistency
   */
  validateVertices(vertices: Point3D[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (vertices.length === 0) {
      errors.push('Vertex array is empty');
    }

    if (vertices.length !== 8) {
      errors.push(`Expected 8 vertices for cabinet box, got ${vertices.length}`);
    }

    for (let i = 0; i < vertices.length; i++) {
      const vertex = vertices[i];
      if (!vertex) {
        errors.push(`Vertex at index ${i} is undefined`);
        continue;
      }

      if (typeof vertex.x !== 'number' || typeof vertex.y !== 'number' || typeof vertex.z !== 'number') {
        errors.push(`Vertex at index ${i} has invalid coordinates`);
      }

      if (!isFinite(vertex.x) || !isFinite(vertex.y) || !isFinite(vertex.z)) {
        errors.push(`Vertex at index ${i} has non-finite coordinates`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Calculate surface area of a cabinet
   */
  calculateSurfaceArea(vertices: Point3D[], faces: number[][]): number {
    let totalArea = 0;

    for (const face of faces) {
      if (face.length < 3) continue;

      // For rectangular faces, calculate area using cross product
      const v1 = vertices[face[0]!];
      const v2 = vertices[face[1]!];
      const v3 = vertices[face[2]!];

      if (!v1 || !v2 || !v3) continue;

      const edge1 = {
        x: v2.x - v1.x,
        y: v2.y - v1.y,
        z: v2.z - v1.z
      };

      const edge2 = {
        x: v3.x - v1.x,
        y: v3.y - v1.y,
        z: v3.z - v1.z
      };

      // Cross product magnitude gives twice the triangle area
      const crossProduct = {
        x: edge1.y * edge2.z - edge1.z * edge2.y,
        y: edge1.z * edge2.x - edge1.x * edge2.z,
        z: edge1.x * edge2.y - edge1.y * edge2.x
      };

      const magnitude = Math.sqrt(
        crossProduct.x * crossProduct.x +
        crossProduct.y * crossProduct.y +
        crossProduct.z * crossProduct.z
      );

      // For rectangular faces (4 vertices), area is twice the triangle area
      totalArea += face.length === 4 ? magnitude : magnitude / 2;
    }

    return totalArea;
  }
}

// Export singleton instance
export const geometryUtils = GeometryUtils.getInstance();
