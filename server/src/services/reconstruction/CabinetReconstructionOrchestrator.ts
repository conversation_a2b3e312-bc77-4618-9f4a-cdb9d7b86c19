import { createModuleLogger } from '@/utils/logger';
import { spatialAnalysisService } from './SpatialAnalysisService';
import { cabinetModelGenerator } from './CabinetModelGenerator';
import { geometryUtils } from './GeometryUtils';
import {
  ReconstructionResult,
  ReconstructionConfig,
  Cabinet3DModel,
  SpatialRelationship,
  SpatialMapping
} from './types';

const logger = createModuleLogger('CabinetReconstructionOrchestrator');

/**
 * Cabinet Reconstruction Orchestrator
 * 
 * Main coordination service for 3D cabinet reconstruction.
 * Orchestrates spatial analysis, model generation, and result compilation.
 */
export class CabinetReconstructionOrchestrator {
  private static instance: CabinetReconstructionOrchestrator;

  private defaultConfig: ReconstructionConfig = {
    enableDepthEstimation: true,
    spatialResolution: 'HIGH',
    includeHardwarePositioning: true,
    optimizeForAccuracy: true,
    generateWireframe: false
  };

  static getInstance(): CabinetReconstructionOrchestrator {
    if (!CabinetReconstructionOrchestrator.instance) {
      CabinetReconstructionOrchestrator.instance = new CabinetReconstructionOrchestrator();
    }
    return CabinetReconstructionOrchestrator.instance;
  }

  /**
   * Generate 3D reconstruction from kitchen images (main orchestration method)
   */
  async reconstructCabinets(
    imagePaths: string[],
    analysisId: string,
    config: Partial<ReconstructionConfig> = {}
  ): Promise<ReconstructionResult> {
    const startTime = Date.now();
    const reconstructionConfig = { ...this.defaultConfig, ...config };

    logger.info(`Starting 3D cabinet reconstruction orchestration: ${analysisId}`, {
      imageCount: imagePaths.length,
      config: reconstructionConfig
    });

    try {
      // Step 1: Generate optimized 3D analysis prompt
      const spatialPrompt = await spatialAnalysisService.generateSpatialAnalysisPrompt(reconstructionConfig);

      // Step 2: Start spatial reasoning chain
      const reasoningChainId = await spatialAnalysisService.initiateSpatialReasoning(analysisId, reconstructionConfig);

      // Step 3: Perform depth estimation and spatial mapping
      const spatialMapping = await spatialAnalysisService.performSpatialMapping(
        imagePaths,
        spatialPrompt,
        analysisId,
        reconstructionConfig
      );

      // Step 4: Generate 3D cabinet models
      const cabinetModels = await cabinetModelGenerator.generateCabinetModels(spatialMapping, reconstructionConfig);

      // Step 5: Validate generated models
      const validatedModels = this.validateCabinetModels(cabinetModels);

      // Step 6: Calculate spatial relationships
      const spatialRelationships = this.calculateSpatialRelationships(validatedModels);

      // Step 7: Estimate room dimensions
      const roomDimensions = this.estimateRoomDimensions(validatedModels, spatialMapping);

      // Step 8: Calculate reconstruction metrics
      const reconstructionMetrics = this.calculateReconstructionMetrics(
        validatedModels,
        spatialRelationships,
        roomDimensions,
        startTime
      );

      // Step 9: Calculate confidence scores
      const confidence = this.calculateConfidenceScores(
        validatedModels,
        spatialRelationships,
        spatialMapping
      );

      const result: ReconstructionResult = {
        cabinets: validatedModels,
        spatialRelationships,
        roomDimensions,
        reconstructionMetrics,
        confidence
      };

      logger.info(`3D reconstruction orchestration completed: ${analysisId}`, {
        cabinetCount: validatedModels.length,
        processingTime: reconstructionMetrics.reconstructionTime,
        overallConfidence: confidence.overall
      });

      return result;

    } catch (error) {
      logger.error(`3D reconstruction orchestration failed: ${analysisId}`, error);
      throw new Error(`3D reconstruction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate generated cabinet models
   */
  private validateCabinetModels(cabinets: Cabinet3DModel[]): Cabinet3DModel[] {
    const validatedCabinets: Cabinet3DModel[] = [];

    for (const cabinet of cabinets) {
      if (cabinetModelGenerator.validateCabinetModel(cabinet)) {
        validatedCabinets.push(cabinet);
      } else {
        logger.warn(`Removing invalid cabinet model: ${cabinet.id}`);
      }
    }

    logger.info(`Validated ${validatedCabinets.length} of ${cabinets.length} cabinet models`);
    return validatedCabinets;
  }

  /**
   * Calculate spatial relationships between cabinets
   */
  private calculateSpatialRelationships(cabinets: Cabinet3DModel[]): SpatialRelationship[] {
    const relationships: SpatialRelationship[] = [];

    for (let i = 0; i < cabinets.length; i++) {
      for (let j = i + 1; j < cabinets.length; j++) {
        const cabinet1 = cabinets[i];
        const cabinet2 = cabinets[j];

        if (!cabinet1 || !cabinet2) continue;

        const relationship = this.analyzeCabinetRelationship(cabinet1, cabinet2);
        if (relationship) {
          relationships.push(relationship);
        }
      }
    }

    logger.info(`Calculated ${relationships.length} spatial relationships`);
    return relationships;
  }

  /**
   * Analyze relationship between two cabinets
   */
  private analyzeCabinetRelationship(
    cabinet1: Cabinet3DModel,
    cabinet2: Cabinet3DModel
  ): SpatialRelationship | null {
    const pos1 = cabinet1.dimensions.position;
    const pos2 = cabinet2.dimensions.position;
    const dim1 = cabinet1.dimensions;
    const dim2 = cabinet2.dimensions;

    // Calculate distance between cabinet centers
    const distance = geometryUtils.calculateDistance(
      {
        x: pos1.x + dim1.width / 2,
        y: pos1.y + dim1.height / 2,
        z: pos1.z + dim1.depth / 2
      },
      {
        x: pos2.x + dim2.width / 2,
        y: pos2.y + dim2.height / 2,
        z: pos2.z + dim2.depth / 2
      }
    );

    // Determine relationship type based on positions
    let relationshipType: 'ADJACENT' | 'ABOVE' | 'BELOW' | 'CORNER' | 'OPPOSITE';
    let confidence = 0.8;

    // Check for vertical relationships
    if (Math.abs(pos1.y - pos2.y) > Math.max(dim1.height, dim2.height) * 0.5) {
      relationshipType = pos1.y > pos2.y ? 'ABOVE' : 'BELOW';
    }
    // Check for horizontal adjacency
    else if (Math.abs(pos1.x - pos2.x) <= Math.max(dim1.width, dim2.width) * 1.1) {
      relationshipType = 'ADJACENT';
    }
    // Check for corner relationship
    else if (distance <= Math.max(dim1.width, dim2.width) * 1.5) {
      relationshipType = 'CORNER';
    }
    // Default to opposite
    else {
      relationshipType = 'OPPOSITE';
      confidence = 0.6;
    }

    return {
      cabinetId1: cabinet1.id,
      cabinetId2: cabinet2.id,
      relationship: relationshipType,
      distance: Math.round(distance),
      confidence
    };
  }

  /**
   * Estimate room dimensions from cabinet positions
   */
  private estimateRoomDimensions(
    cabinets: Cabinet3DModel[],
    spatialMapping: SpatialMapping
  ): { width: number; height: number; depth: number } {
    if (cabinets.length === 0) {
      return { width: 3000, height: 2400, depth: 3000 }; // Default kitchen dimensions
    }

    // Calculate bounding box of all cabinets
    let minX = Infinity, maxX = -Infinity;
    let minY = Infinity, maxY = -Infinity;
    let minZ = Infinity, maxZ = -Infinity;

    for (const cabinet of cabinets) {
      const pos = cabinet.dimensions.position;
      const dim = cabinet.dimensions;

      minX = Math.min(minX, pos.x);
      maxX = Math.max(maxX, pos.x + dim.width);
      minY = Math.min(minY, pos.y);
      maxY = Math.max(maxY, pos.y + dim.height);
      minZ = Math.min(minZ, pos.z);
      maxZ = Math.max(maxZ, pos.z + dim.depth);
    }

    // Add margins for room boundaries
    const margin = 500; // 500mm margin
    const width = Math.max(3000, maxX - minX + margin * 2);
    const height = Math.max(2400, maxY - minY + margin);
    const depth = Math.max(3000, maxZ - minZ + margin * 2);

    return {
      width: Math.round(width),
      height: Math.round(height),
      depth: Math.round(depth)
    };
  }

  /**
   * Calculate reconstruction metrics
   */
  private calculateReconstructionMetrics(
    cabinets: Cabinet3DModel[],
    relationships: SpatialRelationship[],
    roomDimensions: { width: number; height: number; depth: number },
    startTime: number
  ) {
    const totalVolume = cabinets.reduce((sum, cabinet) => {
      return sum + geometryUtils.calculateVolume(
        cabinet.dimensions.width,
        cabinet.dimensions.height,
        cabinet.dimensions.depth
      );
    }, 0);

    const roomVolume = roomDimensions.width * roomDimensions.height * roomDimensions.depth;
    const cabinetDensity = totalVolume / roomVolume;

    // Calculate spatial accuracy based on relationship confidence
    const avgRelationshipConfidence = relationships.length > 0
      ? relationships.reduce((sum, rel) => sum + rel.confidence, 0) / relationships.length
      : 0.8;

    return {
      totalVolume: Math.round(totalVolume),
      cabinetDensity: Math.round(cabinetDensity * 1000) / 1000, // 3 decimal places
      spatialAccuracy: Math.round(avgRelationshipConfidence * 1000) / 1000,
      reconstructionTime: Date.now() - startTime
    };
  }

  /**
   * Calculate confidence scores for the reconstruction
   */
  private calculateConfidenceScores(
    cabinets: Cabinet3DModel[],
    relationships: SpatialRelationship[],
    spatialMapping: SpatialMapping
  ) {
    // Calculate average cabinet confidence
    const avgCabinetConfidence = cabinets.length > 0
      ? cabinets.reduce((sum, cabinet) => sum + cabinet.confidence, 0) / cabinets.length
      : 0.7;

    // Calculate spatial mapping confidence
    const spatialMappingConfidence = spatialMapping.spatialAccuracy || 0.8;

    // Calculate relationship confidence
    const avgRelationshipConfidence = relationships.length > 0
      ? relationships.reduce((sum, rel) => sum + rel.confidence, 0) / relationships.length
      : 0.8;

    // Calculate overall confidence as weighted average
    const overall = (
      avgCabinetConfidence * 0.4 +
      spatialMappingConfidence * 0.4 +
      avgRelationshipConfidence * 0.2
    );

    return {
      overall: Math.round(overall * 1000) / 1000,
      spatialMapping: Math.round(spatialMappingConfidence * 1000) / 1000,
      dimensionAccuracy: Math.round(avgCabinetConfidence * 1000) / 1000,
      cabinetPositioning: Math.round(avgRelationshipConfidence * 1000) / 1000
    };
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    services: Record<string, boolean>;
    configuration: any;
  }> {
    try {
      const services = {
        spatialAnalysisService: true,
        cabinetModelGenerator: true,
        geometryUtils: true,
        orchestrator: true
      };

      return {
        status: 'healthy',
        services,
        configuration: {
          defaultConfig: this.defaultConfig,
          status: 'healthy'
        }
      };
    } catch (error) {
      logger.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        services: {
          spatialAnalysisService: false,
          cabinetModelGenerator: false,
          geometryUtils: false,
          orchestrator: false
        },
        configuration: { status: 'unhealthy', error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }
}

// Export singleton instance
export const cabinetReconstructionOrchestrator = CabinetReconstructionOrchestrator.getInstance();
