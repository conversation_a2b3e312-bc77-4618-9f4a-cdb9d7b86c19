/**
 * Cabinet Reconstruction Services - Modular Architecture
 * 
 * Exports all reconstruction services and types for clean imports.
 * Provides backward compatibility with the original CabinetReconstructionService.
 */

// Core services
export { spatialAnalysisService, SpatialAnalysisService } from './SpatialAnalysisService';
export { cabinetModelGenerator, CabinetModelGenerator } from './CabinetModelGenerator';
export { geometryUtils, GeometryUtils } from './GeometryUtils';
export { cabinetReconstructionOrchestrator, CabinetReconstructionOrchestrator } from './CabinetReconstructionOrchestrator';

// Types
export type {
  Point3D,
  CabinetDimensions3D,
  Cabinet3DModel,
  SpatialRelationship,
  ReconstructionResult,
  ReconstructionConfig,
  SpatialMapping,
  GeometryUtils as GeometryUtilsInterface
} from './types';

// Main service facade (for backward compatibility)
export { CabinetReconstructionService, cabinetReconstructionService } from './CabinetReconstructionService';
