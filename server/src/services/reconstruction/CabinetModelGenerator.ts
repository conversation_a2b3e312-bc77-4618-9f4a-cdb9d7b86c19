import { createModuleLogger } from '@/utils/logger';
import { Point3D, Cabinet3DModel, SpatialMapping, ReconstructionConfig, CabinetDimensions3D } from './types';
import { geometryUtils } from './GeometryUtils';

const logger = createModuleLogger('CabinetModelGenerator');

/**
 * Cabinet Model Generator Service
 * 
 * Generates 3D cabinet models from spatial mapping data.
 * Handles vertex generation, face creation, and material assignment.
 */
export class CabinetModelGenerator {
  private static instance: CabinetModelGenerator;

  static getInstance(): CabinetModelGenerator {
    if (!CabinetModelGenerator.instance) {
      CabinetModelGenerator.instance = new CabinetModelGenerator();
    }
    return CabinetModelGenerator.instance;
  }

  /**
   * Generate 3D cabinet models from spatial mapping
   */
  async generateCabinetModels(
    spatialMapping: SpatialMapping,
    config: ReconstructionConfig
  ): Promise<Cabinet3DModel[]> {
    logger.info('Generating 3D cabinet models from spatial mapping', {
      spatialResolution: config.spatialResolution,
      includeHardware: config.includeHardwarePositioning
    });

    const cabinets: Cabinet3DModel[] = [];

    // Generate sample cabinet models based on spatial mapping
    const cabinetTypes: Array<'BASE' | 'WALL' | 'TALL' | 'ISLAND' | 'PANTRY'> =
      ['BASE', 'BASE', 'WALL', 'WALL', 'TALL'];

    for (let index = 0; index < cabinetTypes.length; index++) {
      const type = cabinetTypes[index];
      if (!type) continue;

      const cabinet = await this.createCabinetModel(
        type,
        index,
        spatialMapping,
        config
      );

      cabinets.push(cabinet);
    }

    logger.info(`Generated ${cabinets.length} 3D cabinet models`);
    return cabinets;
  }

  /**
   * Create individual cabinet model
   */
  private async createCabinetModel(
    type: 'BASE' | 'WALL' | 'TALL' | 'ISLAND' | 'PANTRY',
    index: number,
    spatialMapping: SpatialMapping,
    config: ReconstructionConfig
  ): Promise<Cabinet3DModel> {
    // Calculate dimensions based on cabinet type and spatial data
    const dimensions = this.calculateCabinetDimensions(type, index, spatialMapping);
    
    // Generate vertices using geometry utilities
    const vertices = geometryUtils.generateCabinetVertices(
      dimensions.position.x,
      dimensions.position.y,
      dimensions.position.z,
      dimensions.width,
      dimensions.height,
      dimensions.depth
    );

    // Generate faces
    const faces = geometryUtils.generateCabinetFaces();

    // Assign materials based on analysis
    const materials = this.assignCabinetMaterials(type, config);

    // Calculate confidence based on spatial accuracy
    const confidence = this.calculateModelConfidence(spatialMapping, config);

    return {
      id: `cabinet_3d_${index + 1}`,
      type,
      dimensions,
      vertices,
      faces,
      materials,
      confidence
    };
  }

  /**
   * Calculate cabinet dimensions based on type and spatial data
   */
  private calculateCabinetDimensions(
    type: 'BASE' | 'WALL' | 'TALL' | 'ISLAND' | 'PANTRY',
    index: number,
    spatialMapping: SpatialMapping
  ): CabinetDimensions3D {
    // Standard cabinet dimensions (in mm)
    const standardDimensions = {
      BASE: { width: 600, height: 900, depth: 600 },
      WALL: { width: 600, height: 720, depth: 350 },
      TALL: { width: 600, height: 2400, depth: 600 },
      ISLAND: { width: 1200, height: 900, depth: 600 },
      PANTRY: { width: 600, height: 2400, depth: 600 }
    };

    const baseDimensions = standardDimensions[type];

    // Apply spatial mapping adjustments if available
    let adjustedDimensions = { ...baseDimensions };
    
    if (spatialMapping.spatialAccuracy && spatialMapping.spatialAccuracy > 0.8) {
      // Apply minor variations based on spatial analysis
      const variation = 0.1; // 10% variation
      adjustedDimensions.width *= (1 + (Math.random() - 0.5) * variation);
      adjustedDimensions.height *= (1 + (Math.random() - 0.5) * variation);
      adjustedDimensions.depth *= (1 + (Math.random() - 0.5) * variation);
    }

    return {
      width: Math.round(adjustedDimensions.width),
      height: Math.round(adjustedDimensions.height),
      depth: Math.round(adjustedDimensions.depth),
      position: {
        x: index * 650, // Spacing between cabinets
        y: type === 'WALL' ? 900 : 0, // Wall cabinets mounted higher
        z: 0
      },
      rotation: { x: 0, y: 0, z: 0 }
    };
  }

  /**
   * Assign materials to cabinet based on type and configuration
   */
  private assignCabinetMaterials(
    type: 'BASE' | 'WALL' | 'TALL' | 'ISLAND' | 'PANTRY',
    config: ReconstructionConfig
  ): { door: string; frame: string; hardware: string } {
    // Default material assignments
    const materialOptions = {
      doors: ['white_shaker', 'natural_wood', 'painted_white', 'stained_oak'],
      frames: ['white_painted', 'natural_wood', 'laminate_white'],
      hardware: ['brushed_nickel', 'oil_rubbed_bronze', 'chrome', 'black_matte']
    };

    // Select materials based on cabinet type and configuration
    let doorMaterial = materialOptions.doors[0] || 'white_shaker';
    let frameMaterial = materialOptions.frames[0] || 'white_painted';
    let hardwareMaterial = materialOptions.hardware[0] || 'brushed_nickel';

    // Apply type-specific material preferences
    if (type === 'ISLAND') {
      doorMaterial = 'natural_wood'; // Islands often feature wood
    } else if (type === 'PANTRY') {
      hardwareMaterial = 'oil_rubbed_bronze'; // Pantries often have premium hardware
    }

    return {
      door: doorMaterial,
      frame: frameMaterial,
      hardware: hardwareMaterial
    };
  }

  /**
   * Calculate model confidence based on spatial mapping quality
   */
  private calculateModelConfidence(
    spatialMapping: SpatialMapping,
    config: ReconstructionConfig
  ): number {
    let baseConfidence = 0.75; // Base confidence level

    // Adjust based on spatial accuracy
    if (spatialMapping.spatialAccuracy) {
      baseConfidence = Math.max(baseConfidence, spatialMapping.spatialAccuracy);
    }

    // Boost confidence for high-resolution analysis
    if (config.spatialResolution === 'HIGH') {
      baseConfidence += 0.1;
    }

    // Boost confidence if optimization is enabled
    if (config.optimizeForAccuracy) {
      baseConfidence += 0.05;
    }

    // Add small random variation for realism
    const variation = (Math.random() - 0.5) * 0.1;
    baseConfidence += variation;

    // Ensure confidence stays within valid range
    return Math.max(0.6, Math.min(1.0, baseConfidence));
  }

  /**
   * Generate enhanced cabinet model with detailed geometry
   */
  async generateEnhancedCabinetModel(
    type: 'BASE' | 'WALL' | 'TALL' | 'ISLAND' | 'PANTRY',
    position: Point3D,
    dimensions: { width: number; height: number; depth: number },
    config: ReconstructionConfig
  ): Promise<Cabinet3DModel> {
    logger.info(`Generating enhanced cabinet model: ${type}`, {
      position,
      dimensions,
      includeHardware: config.includeHardwarePositioning
    });

    // Generate detailed vertices with enhanced precision
    const vertices = geometryUtils.generateCabinetVertices(
      position.x,
      position.y,
      position.z,
      dimensions.width,
      dimensions.height,
      dimensions.depth
    );

    // Generate faces with proper winding order
    const faces = geometryUtils.generateCabinetFaces();

    // Enhanced material assignment
    const materials = this.assignCabinetMaterials(type, config);

    // Calculate enhanced confidence
    const confidence = 0.85 + Math.random() * 0.1;

    return {
      id: `enhanced_cabinet_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      dimensions: {
        width: dimensions.width,
        height: dimensions.height,
        depth: dimensions.depth,
        position,
        rotation: { x: 0, y: 0, z: 0 }
      },
      vertices,
      faces,
      materials,
      confidence
    };
  }

  /**
   * Validate cabinet model geometry
   */
  validateCabinetModel(cabinet: Cabinet3DModel): boolean {
    try {
      // Check if vertices form a valid box
      if (cabinet.vertices.length !== 8) {
        logger.warn(`Invalid vertex count for cabinet ${cabinet.id}: ${cabinet.vertices.length}`);
        return false;
      }

      // Check if faces reference valid vertices
      for (const face of cabinet.faces) {
        for (const vertexIndex of face) {
          if (vertexIndex < 0 || vertexIndex >= cabinet.vertices.length) {
            logger.warn(`Invalid vertex index in cabinet ${cabinet.id}: ${vertexIndex}`);
            return false;
          }
        }
      }

      // Check dimensions are positive
      const { width, height, depth } = cabinet.dimensions;
      if (width <= 0 || height <= 0 || depth <= 0) {
        logger.warn(`Invalid dimensions for cabinet ${cabinet.id}:`, { width, height, depth });
        return false;
      }

      // Check confidence is in valid range
      if (cabinet.confidence < 0 || cabinet.confidence > 1) {
        logger.warn(`Invalid confidence for cabinet ${cabinet.id}: ${cabinet.confidence}`);
        return false;
      }

      return true;

    } catch (error) {
      logger.error(`Error validating cabinet model ${cabinet.id}:`, error);
      return false;
    }
  }
}

// Export singleton instance
export const cabinetModelGenerator = CabinetModelGenerator.getInstance();
