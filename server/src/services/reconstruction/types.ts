/**
 * Type definitions for Cabinet Reconstruction Services
 * 
 * Shared interfaces and types for the modular 3D cabinet reconstruction system.
 */

export interface Point3D {
  x: number;
  y: number;
  z: number;
}

export interface CabinetDimensions3D {
  width: number;
  height: number;
  depth: number;
  position: Point3D;
  rotation: {
    x: number;
    y: number;
    z: number;
  };
}

export interface Cabinet3DModel {
  id: string;
  type: 'BASE' | 'WALL' | 'TALL' | 'ISLAND' | 'PANTRY';
  dimensions: CabinetDimensions3D;
  vertices: Point3D[];
  faces: number[][];
  materials: {
    door: string;
    frame: string;
    hardware: string;
  };
  confidence: number;
}

export interface SpatialRelationship {
  cabinetId1: string;
  cabinetId2: string;
  relationship: 'ADJACENT' | 'ABOVE' | 'BELOW' | 'CORNER' | 'OPPOSITE';
  distance: number;
  confidence: number;
}

export interface ReconstructionResult {
  cabinets: Cabinet3DModel[];
  spatialRelationships: SpatialRelationship[];
  roomDimensions: {
    width: number;
    height: number;
    depth: number;
  };
  reconstructionMetrics: {
    totalVolume: number;
    cabinetDensity: number;
    spatialAccuracy: number;
    reconstructionTime: number;
  };
  confidence: {
    overall: number;
    spatialMapping: number;
    dimensionAccuracy: number;
    cabinetPositioning: number;
  };
}

export interface ReconstructionConfig {
  enableDepthEstimation: boolean;
  spatialResolution: 'LOW' | 'MEDIUM' | 'HIGH';
  includeHardwarePositioning: boolean;
  optimizeForAccuracy: boolean;
  generateWireframe: boolean;
}

export interface SpatialMapping {
  depthMap: Array<{
    x: number;
    y: number;
    depth: number;
    confidence?: number;
  }>;
  perspectivePoints: Point3D[];
  vanishingPoints: Array<{ x: number; y: number }>;
  spatialAccuracy?: number;
  reasoningSteps?: string[];
}

export interface GeometryUtils {
  generateCabinetVertices(
    x: number, y: number, z: number,
    width: number, height: number, depth: number
  ): Point3D[];
  
  generateCabinetFaces(): number[][];
  
  calculateDistance(point1: Point3D, point2: Point3D): number;
  
  calculateVolume(dimensions: CabinetDimensions3D): number;
}
