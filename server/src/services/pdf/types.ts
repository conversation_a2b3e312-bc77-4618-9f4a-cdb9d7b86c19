/**
 * Type definitions for PDF Processing Services
 * 
 * Shared interfaces and types for the modular PDF processing system.
 */

export interface ProcessedImage {
  path: string;
  originalName: string;
  width: number;
  height: number;
  size: number;
  format: string;
  page?: number; // For PDF pages
}

export interface PDFProcessingOptions {
  outputDir: string;
  quality: number; // 1-100
  maxWidth?: number;
  maxHeight?: number;
  format: 'jpeg' | 'png';
  dpi: number; // For PDF conversion
}

export interface ConversionStrategy {
  name: string;
  priority: number;
  isAvailable(): Promise<boolean>;
  convert(
    pdfPath: string,
    originalName: string,
    outputDir: string,
    options: PDFProcessingOptions
  ): Promise<string[]>; // Returns array of generated image paths
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  fileInfo?: {
    size: number;
    extension: string;
    exists: boolean;
  };
}

export interface ProcessingMetrics {
  startTime: number;
  endTime: number;
  processingTime: number;
  strategy: string;
  fileSize: number;
  pagesProcessed: number;
  outputSize: number;
}

export interface PDFProcessorConfig {
  defaultOptions: PDFProcessingOptions;
  maxFileSize: number;
  allowedExtensions: string[];
  cleanupMaxAge: number;
  enableFallback: boolean;
}

export interface ConversionResult {
  success: boolean;
  images: ProcessedImage[];
  metrics: ProcessingMetrics;
  strategy: string;
  errors?: string[];
}

export interface CleanupOptions {
  maxAgeHours: number;
  dryRun: boolean;
  includeDirectories: boolean;
}

export interface ErrorContext {
  operation: string;
  filePath: string;
  originalName: string;
  strategy?: string;
  options?: Partial<PDFProcessingOptions>;
  timestamp: number;
}
