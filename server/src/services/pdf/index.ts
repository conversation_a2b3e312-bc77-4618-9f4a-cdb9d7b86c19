/**
 * PDF Processing Services - Modular Architecture
 * 
 * Exports all PDF processing services and types for clean imports.
 * Provides backward compatibility with the original PDFService.
 */

// Core services
export { pdfProcessor, PDFProcessor } from './PDFProcessor';
export { pdfValidator, PDFValidator } from './PDFValidator';
export { pdfConverter, PDFConverter } from './PDFConverter';
export { pdfError<PERSON>andler, PDFErrorHandler } from './PDFErrorHandler';

// Types
export type {
  ProcessedImage,
  PDFProcessingOptions,
  ConversionStrategy,
  ValidationResult,
  ProcessingMetrics,
  PDFProcessorConfig,
  ConversionResult,
  CleanupOptions,
  ErrorContext
} from './types';

// Main service facade (for backward compatibility)
export { PDFService, pdfService } from './PDFService';
