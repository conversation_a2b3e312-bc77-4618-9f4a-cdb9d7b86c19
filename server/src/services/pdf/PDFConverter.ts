import fs from 'fs';
import path from 'path';
import { createModuleLogger } from '@/utils/logger';
import { ValidationError } from '@/middleware/errorHandler';
import { ConversionStrategy, PDFProcessingOptions, ProcessingMetrics } from './types';

const logger = createModuleLogger('PDFConverter');

/**
 * Direct pdftoppm Conversion Strategy
 * Most reliable method using system pdftoppm command
 */
export class DirectPDFConverter implements ConversionStrategy {
  name = 'direct_pdftoppm';
  priority = 1; // Highest priority

  async isAvailable(): Promise<boolean> {
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      const env = {
        ...process.env,
        PATH: `/opt/homebrew/bin:/usr/local/bin:${process.env.PATH}`
      };

      await execAsync('which pdftoppm', { env });
      await execAsync('pdftoppm -h', { env });
      
      logger.debug('Direct pdftoppm converter available');
      return true;
    } catch (error) {
      logger.debug('Direct pdftoppm converter not available:', error);
      return false;
    }
  }

  async convert(
    pdfPath: string,
    originalName: string,
    outputDir: string,
    options: PDFProcessingOptions
  ): Promise<string[]> {
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);

    const env = {
      ...process.env,
      PATH: `/opt/homebrew/bin:/usr/local/bin:${process.env.PATH}`
    };

    const outputPrefix = path.join(outputDir, 'page');
    const resolution = options.dpi || 200;

    // Calculate timeout based on file size
    const stats = await fs.promises.stat(pdfPath);
    const fileSizeMB = stats.size / (1024 * 1024);
    const timeoutMs = Math.max(60000, fileSizeMB * 10000);

    logger.info(`Converting PDF with direct pdftoppm`, {
      file: originalName,
      sizeMB: Math.round(fileSizeMB * 100) / 100,
      timeoutMs,
      resolution
    });

    try {
      const command = `pdftoppm -jpeg -r ${resolution} "${pdfPath}" "${outputPrefix}"`;

      await execAsync(command, {
        env,
        maxBuffer: 1024 * 1024 * 100, // 100MB buffer
        timeout: timeoutMs
      });

      // Get generated files
      const files = await fs.promises.readdir(outputDir);
      const imageFiles = files
        .filter(file => file.startsWith('page') && file.endsWith('.jpg'))
        .map(file => path.join(outputDir, file))
        .sort();

      if (imageFiles.length === 0) {
        throw new Error('No images generated by pdftoppm');
      }

      logger.info(`Direct conversion successful: ${imageFiles.length} pages`);
      return imageFiles;

    } catch (error) {
      logger.error('Direct pdftoppm conversion failed:', error);
      throw error;
    }
  }
}

/**
 * PDF-Poppler Library Conversion Strategy
 * Fallback method using pdf-poppler npm package
 */
export class PopplerLibraryConverter implements ConversionStrategy {
  name = 'pdf_poppler_library';
  priority = 2; // Second priority

  private pdfPoppler: any;

  constructor() {
    try {
      this.pdfPoppler = require('pdf-poppler');
    } catch (error) {
      logger.warn('pdf-poppler library not available');
    }
  }

  async isAvailable(): Promise<boolean> {
    return !!this.pdfPoppler;
  }

  async convert(
    pdfPath: string,
    originalName: string,
    outputDir: string,
    options: PDFProcessingOptions
  ): Promise<string[]> {
    if (!this.pdfPoppler) {
      throw new Error('pdf-poppler library not available');
    }

    const stats = await fs.promises.stat(pdfPath);
    const fileSizeMB = stats.size / (1024 * 1024);
    const timeoutMs = Math.max(120000, fileSizeMB * 15000);

    logger.info(`Converting PDF with pdf-poppler library`, {
      file: originalName,
      sizeMB: Math.round(fileSizeMB * 100) / 100,
      timeoutMs
    });

    const popplerOptions = {
      format: 'jpeg',
      out_dir: outputDir,
      out_prefix: 'page',
      page: null,
      scale: (options.dpi || 200) / 72,
      single_file: false,
      print_only_odd: false,
      print_only_even: false
    };

    try {
      await Promise.race([
        this.pdfPoppler.convert(pdfPath, popplerOptions),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error(`PDF conversion timeout after ${timeoutMs}ms`)), timeoutMs)
        )
      ]);

      // Get generated files
      const files = await fs.promises.readdir(outputDir);
      const imageFiles = files
        .filter(file => file.startsWith('page') && (file.endsWith('.jpg') || file.endsWith('.jpeg')))
        .map(file => path.join(outputDir, file))
        .sort();

      if (imageFiles.length === 0) {
        throw new Error('No images generated by pdf-poppler');
      }

      logger.info(`pdf-poppler conversion successful: ${imageFiles.length} pages`);
      return imageFiles;

    } catch (error) {
      logger.error('pdf-poppler conversion failed:', error);
      throw error;
    }
  }
}

/**
 * PDF Converter Service
 * 
 * Manages multiple conversion strategies and handles PDF to image conversion
 * with automatic fallback between different methods.
 */
export class PDFConverter {
  private static instance: PDFConverter;
  private strategies: ConversionStrategy[] = [];

  constructor() {
    this.initializeStrategies();
  }

  static getInstance(): PDFConverter {
    if (!PDFConverter.instance) {
      PDFConverter.instance = new PDFConverter();
    }
    return PDFConverter.instance;
  }

  /**
   * Initialize conversion strategies in priority order
   */
  private initializeStrategies(): void {
    this.strategies = [
      new DirectPDFConverter(),
      new PopplerLibraryConverter()
    ].sort((a, b) => a.priority - b.priority);

    logger.info(`Initialized ${this.strategies.length} PDF conversion strategies`);
  }

  /**
   * Convert PDF to images using best available strategy
   */
  async convertPDF(
    pdfPath: string,
    originalName: string,
    outputDir: string,
    options: PDFProcessingOptions
  ): Promise<{ images: string[]; strategy: string; metrics: ProcessingMetrics }> {
    const startTime = Date.now();
    
    // Ensure output directory exists
    await fs.promises.mkdir(outputDir, { recursive: true });

    // Try strategies in priority order
    for (const strategy of this.strategies) {
      if (await strategy.isAvailable()) {
        try {
          logger.info(`Attempting PDF conversion with strategy: ${strategy.name}`);
          
          const images = await strategy.convert(pdfPath, originalName, outputDir, options);
          const endTime = Date.now();

          // Calculate metrics
          const stats = await fs.promises.stat(pdfPath);
          const outputStats = await Promise.all(
            images.map(img => fs.promises.stat(img).catch(() => ({ size: 0 })))
          );
          const totalOutputSize = outputStats.reduce((sum, stat) => sum + stat.size, 0);

          const metrics: ProcessingMetrics = {
            startTime,
            endTime,
            processingTime: endTime - startTime,
            strategy: strategy.name,
            fileSize: stats.size,
            pagesProcessed: images.length,
            outputSize: totalOutputSize
          };

          logger.info(`PDF conversion successful with ${strategy.name}`, {
            pages: images.length,
            processingTime: metrics.processingTime,
            inputSize: Math.round(stats.size / 1024) + 'KB',
            outputSize: Math.round(totalOutputSize / 1024) + 'KB'
          });

          return { images, strategy: strategy.name, metrics };

        } catch (error) {
          logger.warn(`Strategy ${strategy.name} failed, trying next:`, error);
          continue;
        }
      } else {
        logger.debug(`Strategy ${strategy.name} not available, skipping`);
      }
    }

    // All strategies failed
    throw new ValidationError(
      'PDF conversion failed: No working conversion strategy available. Install poppler-utils for production-grade PDF processing.'
    );
  }

  /**
   * Get available conversion strategies
   */
  async getAvailableStrategies(): Promise<string[]> {
    const available: string[] = [];
    
    for (const strategy of this.strategies) {
      if (await strategy.isAvailable()) {
        available.push(strategy.name);
      }
    }
    
    return available;
  }

  /**
   * Check if any conversion strategy is available
   */
  async hasAvailableStrategy(): Promise<boolean> {
    for (const strategy of this.strategies) {
      if (await strategy.isAvailable()) {
        return true;
      }
    }
    return false;
  }

  /**
   * Get strategy by name
   */
  getStrategy(name: string): ConversionStrategy | undefined {
    return this.strategies.find(s => s.name === name);
  }

  /**
   * Test all strategies and return their availability
   */
  async testStrategies(): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};
    
    for (const strategy of this.strategies) {
      try {
        results[strategy.name] = await strategy.isAvailable();
      } catch (error) {
        results[strategy.name] = false;
      }
    }
    
    return results;
  }
}

// Export singleton instance
export const pdfConverter = PDFConverter.getInstance();
