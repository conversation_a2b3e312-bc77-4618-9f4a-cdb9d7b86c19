import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import { createModuleLogger } from '@/utils/logger';
import { ValidationError } from '@/middleware/errorHandler';
import { pdfValidator } from './PDFValidator';
import { pdfConverter } from './PDFConverter';
import { pdfErrorHandler } from './PDFErrorHandler';
import {
  ProcessedImage,
  PDFProcessingOptions,
  ConversionResult,
  ProcessingMetrics,
  PDFProcessorConfig
} from './types';

const logger = createModuleLogger('PDFProcessor');

/**
 * PDF Processor Service - Main Orchestrator
 * 
 * Coordinates PDF processing workflow using specialized services:
 * - PDFValidator for file validation
 * - PDFConverter for PDF to image conversion
 * - PDFErrorHandler for error management and cleanup
 * - Sharp for image optimization
 */
export class PDFProcessor {
  private static instance: PDFProcessor;

  private defaultOptions: PDFProcessingOptions = {
    outputDir: process.env.TEMP_DIR || './temp',
    quality: 85,
    maxWidth: 2048,
    maxHeight: 2048,
    format: 'jpeg',
    dpi: 200
  };

  constructor() {
    this.setupEnvironment();
    this.initializeDirectories();
  }

  static getInstance(): PDFProcessor {
    if (!PDFProcessor.instance) {
      PDFProcessor.instance = new PDFProcessor();
    }
    return PDFProcessor.instance;
  }

  /**
   * Setup environment for external tools
   */
  private setupEnvironment(): void {
    const currentPath = process.env.PATH || '';
    const homebrewPaths = '/opt/homebrew/bin:/usr/local/bin';

    if (!currentPath.includes('/opt/homebrew/bin')) {
      process.env.PATH = `${homebrewPaths}:${currentPath}`;
      logger.info('Updated PATH for external PDF tools', {
        newPath: process.env.PATH
      });
    }
  }

  /**
   * Initialize required directories
   */
  private async initializeDirectories(): Promise<void> {
    const directories = [
      this.defaultOptions.outputDir,
      process.env.UPLOAD_DIR || './uploads'
    ];

    const result = await pdfErrorHandler.ensureDirectories(directories);
    
    if (result.errors.length > 0) {
      logger.warn('Some directories could not be created:', result.errors);
    }
  }

  /**
   * Process uploaded file - main entry point
   */
  async processFile(
    filePath: string,
    originalName: string,
    options: Partial<PDFProcessingOptions> = {}
  ): Promise<ProcessedImage[]> {
    const processingOptions = { ...this.defaultOptions, ...options };
    const fileExt = path.extname(originalName).toLowerCase();
    
    logger.info(`Processing file: ${originalName}`, { fileExt, filePath });

    try {
      // Validate file and options
      pdfValidator.quickValidate(filePath, originalName);
      const optionsValidation = pdfValidator.validateProcessingOptions(options);
      
      if (!optionsValidation.isValid) {
        throw new ValidationError(optionsValidation.errors.join('; '));
      }

      // Process based on file type
      if (fileExt === '.pdf') {
        return await this.processPDF(filePath, originalName, processingOptions);
      } else if (['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(fileExt)) {
        return await this.processImage(filePath, originalName, processingOptions);
      } else {
        throw new ValidationError(`Unsupported file format: ${fileExt}`);
      }

    } catch (error) {
      return Promise.reject(pdfErrorHandler.handleError(error, {
        operation: 'file_processing',
        filePath,
        originalName,
        options: processingOptions
      }));
    }
  }

  /**
   * Convert PDF to images using modular converter
   */
  private async processPDF(
    pdfPath: string,
    originalName: string,
    options: PDFProcessingOptions
  ): Promise<ProcessedImage[]> {
    const outputDir = path.join(options.outputDir, `pdf_${Date.now()}`);

    try {
      logger.info(`Converting PDF to images: ${originalName}`);

      // Use modular PDF converter
      const conversionResult = await pdfConverter.convertPDF(
        pdfPath,
        originalName,
        outputDir,
        options
      );

      // Process generated images
      const processedImages = await this.processGeneratedImages(
        conversionResult.images,
        originalName,
        options
      );

      // Clean up original converted images
      await pdfErrorHandler.cleanupFiles(conversionResult.images);

      logger.info(`PDF processing completed: ${originalName}`, {
        pages: processedImages.length,
        strategy: conversionResult.strategy,
        processingTime: conversionResult.metrics.processingTime
      });

      return processedImages;

    } catch (error) {
      // Clean up on error
      await pdfErrorHandler.cleanupFiles([outputDir]).catch(() => {});

      return Promise.reject(pdfErrorHandler.handleError(error, {
        operation: 'pdf_conversion',
        filePath: pdfPath,
        originalName,
        options
      }));
    }
  }

  /**
   * Process generated images from PDF conversion
   */
  private async processGeneratedImages(
    imagePaths: string[],
    originalName: string,
    options: PDFProcessingOptions
  ): Promise<ProcessedImage[]> {
    if (imagePaths.length === 0) {
      throw new Error('No images were generated from PDF conversion');
    }

    logger.info(`Processing ${imagePaths.length} generated images from PDF: ${originalName}`);

    const processedImages: ProcessedImage[] = [];

    for (let i = 0; i < imagePaths.length; i++) {
      const imagePath = imagePaths[i];
      if (!imagePath) continue;

      try {
        const optimizedImage = await this.optimizeImage(
          imagePath,
          `${path.parse(originalName).name}_page_${i + 1}`,
          options
        );

        processedImages.push({
          ...optimizedImage,
          page: i + 1
        });

      } catch (error) {
        logger.warn(`Failed to process image ${i + 1}:`, error);
        // Continue with other images rather than failing completely
      }
    }

    if (processedImages.length === 0) {
      throw new Error('No images could be processed successfully');
    }

    return processedImages;
  }

  /**
   * Process and optimize image file
   */
  private async processImage(
    imagePath: string,
    originalName: string,
    options: PDFProcessingOptions
  ): Promise<ProcessedImage[]> {
    logger.info(`Processing image: ${originalName}`);

    try {
      const optimizedImage = await this.optimizeImage(imagePath, originalName, options);
      return [optimizedImage];

    } catch (error) {
      return Promise.reject(pdfErrorHandler.handleError(error, {
        operation: 'image_processing',
        filePath: imagePath,
        originalName,
        options
      }));
    }
  }

  /**
   * Optimize image using Sharp with comprehensive error handling
   */
  private async optimizeImage(
    inputPath: string,
    originalName: string,
    options: PDFProcessingOptions
  ): Promise<ProcessedImage> {
    const outputFileName = `optimized_${Date.now()}_${path.parse(originalName).name}.${options.format}`;
    const outputPath = path.join(options.outputDir, outputFileName);

    try {
      // Get original image metadata
      const metadata = await sharp(inputPath).metadata();
      
      logger.info(`Optimizing image: ${originalName}`, {
        originalSize: `${metadata.width}x${metadata.height}`,
        format: metadata.format
      });

      // Create Sharp pipeline
      let pipeline = sharp(inputPath);

      // Resize if needed
      if (options.maxWidth || options.maxHeight) {
        pipeline = pipeline.resize(options.maxWidth, options.maxHeight, {
          fit: 'inside',
          withoutEnlargement: true
        });
      }

      // Convert and optimize
      if (options.format === 'jpeg') {
        pipeline = pipeline.jpeg({ 
          quality: options.quality,
          progressive: true,
          mozjpeg: true
        });
      } else if (options.format === 'png') {
        pipeline = pipeline.png({ 
          quality: options.quality,
          progressive: true,
          compressionLevel: 9
        });
      }

      // Save optimized image
      await pipeline.toFile(outputPath);

      // Get final image info
      const finalMetadata = await sharp(outputPath).metadata();
      const stats = await fs.promises.stat(outputPath);

      logger.info(`Image optimized: ${originalName}`, {
        finalSize: `${finalMetadata.width}x${finalMetadata.height}`,
        fileSize: Math.round(stats.size / 1024) + 'KB'
      });

      return {
        path: outputPath,
        originalName,
        width: finalMetadata.width || 0,
        height: finalMetadata.height || 0,
        size: stats.size,
        format: finalMetadata.format || options.format
      };

    } catch (error) {
      return Promise.reject(pdfErrorHandler.handleError(error, {
        operation: 'image_optimization',
        filePath: inputPath,
        originalName,
        options
      }));
    }
  }

  /**
   * Clean up processed files
   */
  async cleanupFiles(filePaths: string[]): Promise<void> {
    const result = await pdfErrorHandler.cleanupFiles(filePaths);
    
    if (result.failed > 0) {
      logger.warn(`Cleanup partially failed: ${result.failed} files could not be deleted`);
    }
  }

  /**
   * Clean up old temporary files
   */
  async cleanupOldFiles(maxAgeHours: number = 24): Promise<void> {
    const result = await pdfErrorHandler.cleanupOldFiles(
      this.defaultOptions.outputDir,
      { maxAgeHours, dryRun: false, includeDirectories: true }
    );

    logger.info(`Cleanup completed: ${result.cleaned.length} files cleaned, ${result.errors.length} errors`);
  }

  /**
   * Validate file before processing
   */
  validateFile(filePath: string, originalName: string): void {
    pdfValidator.quickValidate(filePath, originalName);
  }

  /**
   * Get processing configuration
   */
  getConfig(): PDFProcessorConfig {
    return pdfValidator.getConfig();
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    services: Record<string, boolean>;
    strategies: string[];
    systemHealth: any;
  }> {
    try {
      const strategies = await pdfConverter.getAvailableStrategies();
      const systemHealth = await pdfErrorHandler.checkSystemHealth();
      
      const services = {
        validator: true,
        converter: strategies.length > 0,
        errorHandler: true,
        processor: true
      };

      const allServicesHealthy = Object.values(services).every(Boolean);
      const status = allServicesHealthy && systemHealth.status === 'healthy' 
        ? 'healthy' 
        : systemHealth.status === 'unhealthy' ? 'unhealthy' : 'degraded';

      return {
        status,
        services,
        strategies,
        systemHealth
      };

    } catch (error) {
      logger.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        services: {
          validator: false,
          converter: false,
          errorHandler: false,
          processor: false
        },
        strategies: [],
        systemHealth: { status: 'unhealthy', issues: ['Health check failed'], recommendations: [] }
      };
    }
  }
}

// Export singleton instance
export const pdfProcessor = PDFProcessor.getInstance();
