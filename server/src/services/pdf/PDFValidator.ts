import fs from 'fs';
import path from 'path';
import { createModuleLogger } from '@/utils/logger';
import { ValidationError } from '@/middleware/errorHandler';
import { ValidationResult, PDFProcessorConfig } from './types';

const logger = createModuleLogger('PDFValidator');

/**
 * PDF Validator Service
 * 
 * Handles file validation, format checking, and size limits
 * for PDF and image processing workflows.
 */
export class PDFValidator {
  private static instance: PDFValidator;

  private config: PDFProcessorConfig = {
    defaultOptions: {
      outputDir: process.env.TEMP_DIR || './temp',
      quality: 85,
      maxWidth: 2048,
      maxHeight: 2048,
      format: 'jpeg',
      dpi: 200
    },
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '52428800'), // 50MB default
    allowedExtensions: ['.pdf', '.jpg', '.jpeg', '.png', '.gif', '.webp'],
    cleanupMaxAge: 24,
    enableFallback: process.env.NODE_ENV !== 'production'
  };

  static getInstance(): PDFValidator {
    if (!PDFValidator.instance) {
      PDFValidator.instance = new PDFValidator();
    }
    return PDFValidator.instance;
  }

  /**
   * Comprehensive file validation
   */
  validateFile(filePath: string, originalName: string): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    try {
      // Basic file information
      const fileExt = path.extname(originalName).toLowerCase();
      const fileExists = fs.existsSync(filePath);
      let fileSize = 0;

      if (fileExists) {
        const stats = fs.statSync(filePath);
        fileSize = stats.size;
      }

      result.fileInfo = {
        size: fileSize,
        extension: fileExt,
        exists: fileExists
      };

      // Validate file existence
      if (!fileExists) {
        result.errors.push('File not found');
        result.isValid = false;
        return result;
      }

      // Validate file extension
      if (!this.config.allowedExtensions.includes(fileExt)) {
        result.errors.push(
          `Unsupported file format: ${fileExt}. Allowed formats: ${this.config.allowedExtensions.join(', ')}`
        );
        result.isValid = false;
      }

      // Validate file size
      if (fileSize > this.config.maxFileSize) {
        result.errors.push(
          `File too large. Maximum size: ${Math.round(this.config.maxFileSize / 1024 / 1024)}MB, actual: ${Math.round(fileSize / 1024 / 1024)}MB`
        );
        result.isValid = false;
      }

      // Add warnings for large files
      if (fileSize > this.config.maxFileSize * 0.8) {
        result.warnings.push('File is approaching size limit, processing may be slower');
      }

      // Validate file content (basic checks)
      if (fileExt === '.pdf') {
        const contentValidation = this.validatePDFContent(filePath);
        result.errors.push(...contentValidation.errors);
        result.warnings.push(...contentValidation.warnings);
        if (contentValidation.errors.length > 0) {
          result.isValid = false;
        }
      }

      logger.info(`File validation completed: ${originalName}`, {
        isValid: result.isValid,
        errors: result.errors.length,
        warnings: result.warnings.length,
        fileSize: Math.round(fileSize / 1024) + 'KB'
      });

      return result;

    } catch (error) {
      logger.error('File validation failed:', error);
      result.errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      result.isValid = false;
      return result;
    }
  }

  /**
   * Validate PDF file content
   */
  private validatePDFContent(filePath: string): { errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Read first few bytes to check PDF header
      const buffer = fs.readFileSync(filePath, { start: 0, end: 10 });
      const header = buffer.toString('ascii');

      if (!header.startsWith('%PDF-')) {
        errors.push('Invalid PDF file: Missing PDF header');
      } else {
        // Extract PDF version
        const versionMatch = header.match(/%PDF-(\d+\.\d+)/);
        if (versionMatch) {
          const version = parseFloat(versionMatch[1] || '1.4');
          if (version > 2.0) {
            warnings.push(`PDF version ${version} may not be fully supported`);
          }
        }
      }

      // Check for encrypted PDFs (basic check)
      const sampleBuffer = fs.readFileSync(filePath, { start: 0, end: 1024 });
      const content = sampleBuffer.toString('ascii');
      
      if (content.includes('/Encrypt')) {
        errors.push('Encrypted PDFs are not supported');
      }

    } catch (error) {
      warnings.push('Could not validate PDF content structure');
    }

    return { errors, warnings };
  }

  /**
   * Validate processing options
   */
  validateProcessingOptions(options: Partial<PDFProcessingOptions>): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // Validate quality
    if (options.quality !== undefined) {
      if (options.quality < 1 || options.quality > 100) {
        result.errors.push('Quality must be between 1 and 100');
        result.isValid = false;
      } else if (options.quality < 50) {
        result.warnings.push('Low quality setting may result in poor image quality');
      }
    }

    // Validate dimensions
    if (options.maxWidth !== undefined && options.maxWidth < 100) {
      result.errors.push('Maximum width must be at least 100 pixels');
      result.isValid = false;
    }

    if (options.maxHeight !== undefined && options.maxHeight < 100) {
      result.errors.push('Maximum height must be at least 100 pixels');
      result.isValid = false;
    }

    // Validate DPI
    if (options.dpi !== undefined) {
      if (options.dpi < 72 || options.dpi > 600) {
        result.errors.push('DPI must be between 72 and 600');
        result.isValid = false;
      } else if (options.dpi > 300) {
        result.warnings.push('High DPI setting may result in large file sizes and slower processing');
      }
    }

    // Validate format
    if (options.format !== undefined && !['jpeg', 'png'].includes(options.format)) {
      result.errors.push('Format must be either "jpeg" or "png"');
      result.isValid = false;
    }

    // Validate output directory
    if (options.outputDir !== undefined) {
      try {
        if (!fs.existsSync(options.outputDir)) {
          result.warnings.push('Output directory does not exist, will be created');
        } else {
          // Check write permissions
          fs.accessSync(options.outputDir, fs.constants.W_OK);
        }
      } catch (error) {
        result.errors.push('Output directory is not writable');
        result.isValid = false;
      }
    }

    return result;
  }

  /**
   * Quick validation for API endpoints
   */
  quickValidate(filePath: string, originalName: string): void {
    const result = this.validateFile(filePath, originalName);
    
    if (!result.isValid) {
      throw new ValidationError(result.errors.join('; '));
    }

    // Log warnings but don't throw
    if (result.warnings.length > 0) {
      logger.warn(`File validation warnings for ${originalName}:`, result.warnings);
    }
  }

  /**
   * Get validation configuration
   */
  getConfig(): PDFProcessorConfig {
    return { ...this.config };
  }

  /**
   * Update validation configuration
   */
  updateConfig(updates: Partial<PDFProcessorConfig>): void {
    this.config = { ...this.config, ...updates };
    logger.info('PDF validator configuration updated', updates);
  }

  /**
   * Get supported file extensions
   */
  getSupportedExtensions(): string[] {
    return [...this.config.allowedExtensions];
  }

  /**
   * Check if file extension is supported
   */
  isExtensionSupported(extension: string): boolean {
    return this.config.allowedExtensions.includes(extension.toLowerCase());
  }

  /**
   * Get maximum file size in bytes
   */
  getMaxFileSize(): number {
    return this.config.maxFileSize;
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${Math.round(size * 100) / 100} ${units[unitIndex]}`;
  }
}

// Export singleton instance
export const pdfValidator = PDFValidator.getInstance();
