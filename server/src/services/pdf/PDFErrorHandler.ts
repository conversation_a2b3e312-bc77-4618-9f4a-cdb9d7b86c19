import fs from 'fs';
import path from 'path';
import { createModuleLogger } from '@/utils/logger';
import { ValidationError } from '@/middleware/errorHandler';
import { ErrorContext, CleanupOptions } from './types';

const logger = createModuleLogger('PDFErrorHandler');

/**
 * PDF Error Handler Service
 * 
 * Handles error recovery, cleanup operations, and file management
 * for PDF processing workflows with comprehensive error tracking.
 */
export class PDFErrorHandler {
  private static instance: PDFErrorHandler;

  private errorHistory: ErrorContext[] = [];
  private maxErrorHistory = 100;

  static getInstance(): PDFErrorHandler {
    if (!PDFErrorHandler.instance) {
      PDFErrorHandler.instance = new PDFErrorHandler();
    }
    return PDFErrorHandler.instance;
  }

  /**
   * Handle and log processing errors with context
   */
  handleError(
    error: Error | unknown,
    context: Omit<ErrorContext, 'timestamp'>
  ): ValidationError {
    const errorContext: ErrorContext = {
      ...context,
      timestamp: Date.now()
    };

    // Add to error history
    this.errorHistory.push(errorContext);
    if (this.errorHistory.length > this.maxErrorHistory) {
      this.errorHistory.shift();
    }

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const enhancedMessage = this.enhanceErrorMessage(errorMessage, errorContext);

    logger.error('PDF processing error occurred:', {
      operation: context.operation,
      file: context.originalName,
      strategy: context.strategy,
      error: errorMessage,
      context: errorContext
    });

    // Attempt cleanup if file paths are involved
    if (context.filePath) {
      this.attemptCleanup(context.filePath).catch(cleanupError => {
        logger.warn('Cleanup failed after error:', cleanupError);
      });
    }

    return new ValidationError(enhancedMessage);
  }

  /**
   * Enhance error messages with helpful context and suggestions
   */
  private enhanceErrorMessage(originalMessage: string, context: ErrorContext): string {
    let enhancedMessage = originalMessage;

    // Add operation context
    enhancedMessage = `${context.operation} failed: ${enhancedMessage}`;

    // Add specific suggestions based on error patterns
    if (originalMessage.includes('pdftoppm')) {
      enhancedMessage += '. Install poppler-utils with: brew install poppler';
    } else if (originalMessage.includes('timeout')) {
      enhancedMessage += '. Try reducing DPI or file size for faster processing';
    } else if (originalMessage.includes('permission')) {
      enhancedMessage += '. Check file permissions and disk space';
    } else if (originalMessage.includes('not found')) {
      enhancedMessage += '. Verify file exists and is accessible';
    } else if (originalMessage.includes('format')) {
      enhancedMessage += '. Ensure file is a valid PDF or supported image format';
    }

    // Add environment context for production issues
    if (process.env.NODE_ENV === 'production') {
      enhancedMessage += ` [Production Environment - File: ${context.originalName}]`;
    }

    return enhancedMessage;
  }

  /**
   * Attempt cleanup of temporary files and directories
   */
  private async attemptCleanup(filePath: string): Promise<void> {
    try {
      const stats = await fs.promises.stat(filePath);
      
      if (stats.isDirectory()) {
        await fs.promises.rmdir(filePath, { recursive: true });
        logger.debug(`Cleaned up directory: ${filePath}`);
      } else {
        await fs.promises.unlink(filePath);
        logger.debug(`Cleaned up file: ${filePath}`);
      }
    } catch (error) {
      // Ignore cleanup errors - they're not critical
      logger.debug(`Cleanup attempt failed for ${filePath}:`, error);
    }
  }

  /**
   * Clean up processed files with error handling
   */
  async cleanupFiles(filePaths: string[]): Promise<{ success: number; failed: number; errors: string[] }> {
    logger.info(`Cleaning up ${filePaths.length} files`);
    
    let success = 0;
    let failed = 0;
    const errors: string[] = [];

    await Promise.all(
      filePaths.map(async (filePath) => {
        try {
          await fs.promises.unlink(filePath);
          success++;
          logger.debug(`Deleted file: ${filePath}`);
        } catch (error) {
          failed++;
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          errors.push(`Failed to delete ${filePath}: ${errorMessage}`);
          logger.warn(`Failed to delete file: ${filePath}`, error);
        }
      })
    );

    logger.info(`Cleanup completed: ${success} successful, ${failed} failed`);
    return { success, failed, errors };
  }

  /**
   * Clean up old temporary files with comprehensive options
   */
  async cleanupOldFiles(
    tempDir: string,
    options: CleanupOptions = { maxAgeHours: 24, dryRun: false, includeDirectories: true }
  ): Promise<{ cleaned: string[]; errors: string[]; totalSize: number }> {
    const maxAge = Date.now() - (options.maxAgeHours * 60 * 60 * 1000);
    const cleaned: string[] = [];
    const errors: string[] = [];
    let totalSize = 0;

    try {
      if (!fs.existsSync(tempDir)) {
        logger.warn(`Cleanup directory does not exist: ${tempDir}`);
        return { cleaned, errors, totalSize };
      }

      const files = await fs.promises.readdir(tempDir);
      
      for (const file of files) {
        const filePath = path.join(tempDir, file);
        
        try {
          const stats = await fs.promises.stat(filePath);
          
          if (stats.mtime.getTime() < maxAge) {
            totalSize += stats.size;
            
            if (!options.dryRun) {
              if (stats.isDirectory() && options.includeDirectories) {
                await fs.promises.rmdir(filePath, { recursive: true });
                cleaned.push(`${filePath} (directory)`);
              } else if (stats.isFile()) {
                await fs.promises.unlink(filePath);
                cleaned.push(filePath);
              }
            } else {
              cleaned.push(`${filePath} (would be deleted)`);
            }
            
            logger.debug(`${options.dryRun ? 'Would clean' : 'Cleaned'} old file: ${filePath}`);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          errors.push(`Failed to process ${filePath}: ${errorMessage}`);
          logger.warn(`Failed to process file during cleanup: ${filePath}`, error);
        }
      }
      
      logger.info(`Cleanup ${options.dryRun ? 'simulation' : 'completed'} for files older than ${options.maxAgeHours} hours`, {
        cleaned: cleaned.length,
        errors: errors.length,
        totalSize: Math.round(totalSize / 1024) + 'KB',
        dryRun: options.dryRun
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(`Cleanup failed: ${errorMessage}`);
      logger.error('Cleanup operation failed:', error);
    }

    return { cleaned, errors, totalSize };
  }

  /**
   * Ensure required directories exist with error handling
   */
  async ensureDirectories(directories: string[]): Promise<{ created: string[]; errors: string[] }> {
    const created: string[] = [];
    const errors: string[] = [];

    for (const dir of directories) {
      try {
        if (!fs.existsSync(dir)) {
          await fs.promises.mkdir(dir, { recursive: true });
          created.push(dir);
          logger.info(`Created directory: ${dir}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push(`Failed to create directory ${dir}: ${errorMessage}`);
        logger.error(`Failed to create directory: ${dir}`, error);
      }
    }

    return { created, errors };
  }

  /**
   * Get error statistics and history
   */
  getErrorStats(): {
    totalErrors: number;
    recentErrors: number;
    commonOperations: Record<string, number>;
    commonStrategies: Record<string, number>;
  } {
    const recentThreshold = Date.now() - (24 * 60 * 60 * 1000); // Last 24 hours
    const recentErrors = this.errorHistory.filter(e => e.timestamp > recentThreshold);

    const commonOperations: Record<string, number> = {};
    const commonStrategies: Record<string, number> = {};

    for (const error of this.errorHistory) {
      commonOperations[error.operation] = (commonOperations[error.operation] || 0) + 1;
      if (error.strategy) {
        commonStrategies[error.strategy] = (commonStrategies[error.strategy] || 0) + 1;
      }
    }

    return {
      totalErrors: this.errorHistory.length,
      recentErrors: recentErrors.length,
      commonOperations,
      commonStrategies
    };
  }

  /**
   * Clear error history
   */
  clearErrorHistory(): void {
    this.errorHistory = [];
    logger.info('Error history cleared');
  }

  /**
   * Get recent errors for debugging
   */
  getRecentErrors(limit: number = 10): ErrorContext[] {
    return this.errorHistory
      .slice(-limit)
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Check system health for PDF processing
   */
  async checkSystemHealth(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check recent error rate
    const recentErrors = this.getErrorStats().recentErrors;
    if (recentErrors > 10) {
      issues.push(`High error rate: ${recentErrors} errors in last 24 hours`);
      recommendations.push('Check system dependencies and file permissions');
    }

    // Check disk space
    try {
      const tempDir = process.env.TEMP_DIR || './temp';
      if (fs.existsSync(tempDir)) {
        const stats = await fs.promises.stat(tempDir);
        // This is a basic check - in production you'd want more sophisticated disk space monitoring
      }
    } catch (error) {
      issues.push('Cannot access temporary directory');
      recommendations.push('Check temporary directory permissions and disk space');
    }

    // Determine overall status
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    if (issues.length > 0) {
      status = issues.length > 2 ? 'unhealthy' : 'degraded';
    }

    return { status, issues, recommendations };
  }
}

// Export singleton instance
export const pdfErrorHandler = PDFErrorHandler.getInstance();
