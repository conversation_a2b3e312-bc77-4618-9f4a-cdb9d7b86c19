import { Cluster } from 'ioredis';
import { createModuleLogger } from '../utils/logger';

const logger = createModuleLogger('EnhancedCacheService');

export interface CacheConfig {
  redisNodes: Array<{ host: string; port: number }>;
  password?: string;
  maxRetries: number;
  retryDelayOnFailover: number;
  enableOfflineQueue: boolean;
  lazyConnect: boolean;
  maxLocalCacheSize: number;
  defaultTTL: number;
}

export interface CacheStats {
  connected: boolean;
  localCacheSize: number;
  redisInfo?: any;
  hitRate: number;
  totalHits: number;
  totalMisses: number;
  memoryUsage: number;
}

/**
 * Enhanced Cache Service for Cabinet Insight Pro Scalability
 * 
 * Implements L1 (local) + L2 (Redis cluster) caching strategy
 * with intelligent failover and performance optimization.
 * 
 * Features:
 * - Redis cluster support for horizontal scaling
 * - Local memory cache for ultra-fast access
 * - Automatic failover and retry logic
 * - Performance metrics and monitoring
 * - Memory management and cleanup
 */
export class EnhancedCacheService {
  private cluster: Cluster;
  private localCache: Map<string, { value: any; timestamp: number; ttl: number }> = new Map();
  private config: CacheConfig;
  private isConnected = false;
  private stats = {
    hits: 0,
    misses: 0,
    localHits: 0,
    redisHits: 0
  };

  constructor(config?: Partial<CacheConfig>) {
    this.config = {
      redisNodes: [
        { host: process.env.REDIS_NODE_1_HOST || 'redis-node-1', port: 7001 },
        { host: process.env.REDIS_NODE_2_HOST || 'redis-node-2', port: 7002 },
        { host: process.env.REDIS_NODE_3_HOST || 'redis-node-3', port: 7003 }
      ],
      password: process.env.REDIS_PASSWORD,
      maxRetries: 3,
      retryDelayOnFailover: 100,
      enableOfflineQueue: false,
      lazyConnect: true,
      maxLocalCacheSize: 1000,
      defaultTTL: 3600, // 1 hour
      ...config
    };

    this.initializeCluster();
    this.startLocalCacheCleanup();
  }

  private initializeCluster(): void {
    try {
      this.cluster = new Cluster(this.config.redisNodes, {
        redisOptions: {
          password: this.config.password,
        },
        enableOfflineQueue: this.config.enableOfflineQueue,
        retryDelayOnFailover: this.config.retryDelayOnFailover,
        maxRetriesPerRequest: this.config.maxRetries,
        lazyConnect: this.config.lazyConnect,
        scaleReads: 'slave', // Distribute reads to replica nodes
        readOnly: false,
        enableReadyCheck: true,
        maxRetriesPerRequest: 3,
        retryDelayOnClusterDown: 300,
        retryDelayOnFailover: 100,
        slotsRefreshTimeout: 10000,
        slotsRefreshInterval: 5000
      });

      this.setupClusterEventHandlers();
      
      // Connect if not lazy loading
      if (!this.config.lazyConnect) {
        this.cluster.connect().catch(error => {
          logger.error('Failed to connect to Redis cluster:', error);
        });
      }

    } catch (error) {
      logger.error('Failed to initialize Redis cluster:', error);
      this.isConnected = false;
    }
  }

  private setupClusterEventHandlers(): void {
    this.cluster.on('connect', () => {
      this.isConnected = true;
      logger.info('Redis cluster connected successfully');
    });

    this.cluster.on('ready', () => {
      this.isConnected = true;
      logger.info('Redis cluster is ready');
    });

    this.cluster.on('error', (error) => {
      this.isConnected = false;
      logger.error('Redis cluster connection error:', error);
    });

    this.cluster.on('close', () => {
      this.isConnected = false;
      logger.warn('Redis cluster connection closed');
    });

    this.cluster.on('reconnecting', () => {
      logger.info('Redis cluster reconnecting...');
    });

    this.cluster.on('end', () => {
      this.isConnected = false;
      logger.warn('Redis cluster connection ended');
    });

    this.cluster.on('+node', (node) => {
      logger.info(`Redis cluster node added: ${node.options.host}:${node.options.port}`);
    });

    this.cluster.on('-node', (node) => {
      logger.warn(`Redis cluster node removed: ${node.options.host}:${node.options.port}`);
    });

    this.cluster.on('node error', (error, node) => {
      logger.error(`Redis cluster node error on ${node.options.host}:${node.options.port}:`, error);
    });
  }

  /**
   * Get value from cache with L1/L2 strategy
   */
  async get(key: string): Promise<any> {
    try {
      // Check local cache first (L1)
      const localEntry = this.localCache.get(key);
      if (localEntry && this.isLocalEntryValid(localEntry)) {
        this.stats.hits++;
        this.stats.localHits++;
        logger.debug(`L1 cache hit: ${key}`);
        return localEntry.value;
      }

      // Remove expired local entry
      if (localEntry) {
        this.localCache.delete(key);
      }

      // Check Redis cluster (L2) if connected
      if (this.isConnected) {
        const value = await this.cluster.get(key);
        if (value) {
          const parsed = JSON.parse(value);
          this.setLocalCache(key, parsed, this.config.defaultTTL);
          this.stats.hits++;
          this.stats.redisHits++;
          logger.debug(`L2 cache hit: ${key}`);
          return parsed;
        }
      }

      this.stats.misses++;
      return null;
    } catch (error) {
      logger.error('Cache get error:', error);
      this.stats.misses++;
      return null;
    }
  }

  /**
   * Set value in cache with TTL
   */
  async set(key: string, value: any, ttl: number = this.config.defaultTTL): Promise<boolean> {
    try {
      const serialized = JSON.stringify(value);
      
      // Set in Redis cluster if connected
      if (this.isConnected) {
        await this.cluster.setex(key, ttl, serialized);
      }
      
      // Always set in local cache
      this.setLocalCache(key, value, ttl);
      
      logger.debug(`Cache set: ${key} (TTL: ${ttl}s)`);
      return true;
    } catch (error) {
      logger.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * Delete key from cache
   */
  async del(key: string): Promise<boolean> {
    try {
      // Remove from local cache
      this.localCache.delete(key);
      
      // Remove from Redis cluster if connected
      if (this.isConnected) {
        await this.cluster.del(key);
      }
      
      logger.debug(`Cache delete: ${key}`);
      return true;
    } catch (error) {
      logger.error('Cache delete error:', error);
      return false;
    }
  }

  /**
   * Invalidate keys matching pattern
   */
  async invalidatePattern(pattern: string): Promise<void> {
    try {
      if (this.isConnected) {
        const keys = await this.cluster.keys(pattern);
        if (keys.length > 0) {
          await this.cluster.del(...keys);
          logger.info(`Invalidated ${keys.length} keys matching pattern: ${pattern}`);
        }
      }
      
      // Clear local cache entries matching pattern
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      for (const key of this.localCache.keys()) {
        if (regex.test(key)) {
          this.localCache.delete(key);
        }
      }
    } catch (error) {
      logger.error('Cache invalidation error:', error);
    }
  }

  /**
   * Check if key exists in cache
   */
  async exists(key: string): Promise<boolean> {
    try {
      // Check local cache first
      const localEntry = this.localCache.get(key);
      if (localEntry && this.isLocalEntryValid(localEntry)) {
        return true;
      }

      // Check Redis cluster
      if (this.isConnected) {
        const exists = await this.cluster.exists(key);
        return exists === 1;
      }

      return false;
    } catch (error) {
      logger.error('Cache exists error:', error);
      return false;
    }
  }

  /**
   * Get multiple keys at once
   */
  async mget(keys: string[]): Promise<Array<any | null>> {
    try {
      const results: Array<any | null> = new Array(keys.length).fill(null);
      const missingKeys: number[] = [];

      // Check local cache first
      for (let i = 0; i < keys.length; i++) {
        const localEntry = this.localCache.get(keys[i]);
        if (localEntry && this.isLocalEntryValid(localEntry)) {
          results[i] = localEntry.value;
          this.stats.localHits++;
        } else {
          missingKeys.push(i);
        }
      }

      // Get missing keys from Redis
      if (missingKeys.length > 0 && this.isConnected) {
        const missingKeyNames = missingKeys.map(i => keys[i]);
        const redisResults = await this.cluster.mget(...missingKeyNames);
        
        for (let j = 0; j < missingKeys.length; j++) {
          const i = missingKeys[j];
          const redisValue = redisResults[j];
          
          if (redisValue) {
            const parsed = JSON.parse(redisValue);
            results[i] = parsed;
            this.setLocalCache(keys[i], parsed, this.config.defaultTTL);
            this.stats.redisHits++;
          }
        }
      }

      this.stats.hits += results.filter(r => r !== null).length;
      this.stats.misses += results.filter(r => r === null).length;

      return results;
    } catch (error) {
      logger.error('Cache mget error:', error);
      return new Array(keys.length).fill(null);
    }
  }

  private setLocalCache(key: string, value: any, ttl: number): void {
    // Implement LRU eviction if cache is full
    if (this.localCache.size >= this.config.maxLocalCacheSize) {
      const oldestKey = this.localCache.keys().next().value;
      this.localCache.delete(oldestKey);
    }

    this.localCache.set(key, {
      value,
      timestamp: Date.now(),
      ttl: ttl * 1000 // Convert to milliseconds
    });
  }

  private isLocalEntryValid(entry: { value: any; timestamp: number; ttl: number }): boolean {
    return Date.now() - entry.timestamp < entry.ttl;
  }

  private startLocalCacheCleanup(): void {
    setInterval(() => {
      const now = Date.now();
      let cleanedCount = 0;

      for (const [key, entry] of this.localCache.entries()) {
        if (now - entry.timestamp >= entry.ttl) {
          this.localCache.delete(key);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        logger.debug(`Cleaned up ${cleanedCount} expired local cache entries`);
      }
    }, 60000); // Clean up every minute
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<CacheStats> {
    try {
      const hitRate = this.stats.hits + this.stats.misses > 0 
        ? this.stats.hits / (this.stats.hits + this.stats.misses) 
        : 0;

      const stats: CacheStats = {
        connected: this.isConnected,
        localCacheSize: this.localCache.size,
        hitRate: Math.round(hitRate * 100) / 100,
        totalHits: this.stats.hits,
        totalMisses: this.stats.misses,
        memoryUsage: this.estimateMemoryUsage()
      };

      if (this.isConnected) {
        try {
          const info = await this.cluster.info();
          stats.redisInfo = this.parseRedisInfo(info);
        } catch (error) {
          logger.warn('Failed to get Redis info:', error);
        }
      }

      return stats;
    } catch (error) {
      logger.error('Failed to get cache stats:', error);
      return {
        connected: false,
        localCacheSize: this.localCache.size,
        hitRate: 0,
        totalHits: 0,
        totalMisses: 0,
        memoryUsage: 0,
        error: error.message
      };
    }
  }

  private estimateMemoryUsage(): number {
    let size = 0;
    for (const [key, entry] of this.localCache.entries()) {
      size += key.length * 2; // UTF-16 characters
      size += JSON.stringify(entry.value).length * 2;
      size += 24; // Approximate overhead for timestamp and ttl
    }
    return size;
  }

  private parseRedisInfo(info: string): any {
    const lines = info.split('\r\n');
    const result: any = {};
    
    for (const line of lines) {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        result[key] = isNaN(Number(value)) ? value : Number(value);
      }
    }
    
    return result;
  }

  /**
   * Flush all cache data
   */
  async flush(): Promise<void> {
    try {
      // Clear local cache
      this.localCache.clear();
      
      // Clear Redis cluster if connected
      if (this.isConnected) {
        await this.cluster.flushall();
      }
      
      // Reset stats
      this.stats = { hits: 0, misses: 0, localHits: 0, redisHits: 0 };
      
      logger.info('Cache flushed successfully');
    } catch (error) {
      logger.error('Cache flush error:', error);
    }
  }

  /**
   * Disconnect from Redis cluster
   */
  async disconnect(): Promise<void> {
    try {
      if (this.cluster) {
        await this.cluster.disconnect();
      }
      this.isConnected = false;
      logger.info('Disconnected from Redis cluster');
    } catch (error) {
      logger.error('Error disconnecting from Redis cluster:', error);
    }
  }
}
