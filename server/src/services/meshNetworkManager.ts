import { Server as SocketIOServer, Socket } from 'socket.io';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

export interface MeshPeer {
  id: string;
  userId: string;
  socketId: string;
  projectId: string;
  connectionState: 'connecting' | 'connected' | 'disconnected' | 'failed';
  connectionQuality: number; // 0-1 score
  latency: number;
  bandwidth: number;
  lastActivity: number;
  capabilities: {
    supportsWebRTC: boolean;
    supportsDataChannels: boolean;
    supportsAudio: boolean;
    supportsVideo: boolean;
  };
}

export interface MeshTopology {
  projectId: string;
  peers: Map<string, MeshPeer>;
  connections: Map<string, Set<string>>; // peerId -> Set of connected peerIds
  connectionMatrix: number[][]; // Quality matrix for connection optimization
  lastUpdated: number;
}

export interface MeshNetworkMetrics {
  totalPeers: number;
  activeConnections: number;
  averageLatency: number;
  averageBandwidth: number;
  networkHealth: number; // 0-1 score
  topologyEfficiency: number; // 0-1 score
  lastUpdated: number;
}

export interface ConnectionOptimizationConfig {
  maxPeersPerNode: number;
  minConnectionQuality: number;
  latencyThreshold: number;
  bandwidthThreshold: number;
  rebalanceInterval: number;
  enableAdaptiveTopology: boolean;
}

/**
 * Advanced P2P Mesh Network Manager
 * 
 * Manages dynamic mesh topology for 3-10 concurrent users in Cabinet Insight Pro
 * Provides intelligent connection management, bandwidth optimization, and automatic fallback
 * Integrates with existing WebRTC infrastructure and performance monitoring
 */
export class MeshNetworkManager {
  private io: SocketIOServer;
  private meshTopologies: Map<string, MeshTopology> = new Map(); // projectId -> topology
  private peerConnections: Map<string, Set<string>> = new Map(); // peerId -> connected peers
  private connectionMetrics: Map<string, MeshNetworkMetrics> = new Map();
  private optimizationConfig: ConnectionOptimizationConfig;
  private rebalanceTimer: NodeJS.Timeout | null = null;

  constructor(io: SocketIOServer, config?: Partial<ConnectionOptimizationConfig>) {
    this.io = io;
    this.optimizationConfig = {
      maxPeersPerNode: 6, // Optimal for 3-10 user mesh
      minConnectionQuality: 0.6,
      latencyThreshold: 200, // ms
      bandwidthThreshold: 1000, // kbps
      rebalanceInterval: 30000, // 30 seconds
      enableAdaptiveTopology: true,
      ...config
    };

    this.setupEventHandlers();
    this.startTopologyOptimization();
    logger.info('MeshNetworkManager initialized', { config: this.optimizationConfig });
  }

  /**
   * Setup Socket.IO event handlers for mesh networking
   */
  private setupEventHandlers(): void {
    this.io.on('connection', (socket) => {
      // Handle mesh network join
      socket.on('mesh-join-project', async (data: {
        projectId: string;
        userId: string;
        capabilities: MeshPeer['capabilities'];
      }) => {
        await this.handleMeshJoin(socket, data);
      });

      // Handle mesh network leave
      socket.on('mesh-leave-project', async (data: {
        projectId: string;
        userId: string;
      }) => {
        await this.handleMeshLeave(socket, data);
      });

      // Handle peer discovery request
      socket.on('mesh-discover-peers', async (data: {
        projectId: string;
      }) => {
        await this.handlePeerDiscovery(socket, data);
      });

      // Handle connection quality updates
      socket.on('mesh-connection-quality', (data: {
        projectId: string;
        peerId: string;
        targetPeerId: string;
        quality: number;
        latency: number;
        bandwidth: number;
      }) => {
        this.updateConnectionQuality(data);
      });

      // Handle mesh network metrics request
      socket.on('mesh-get-metrics', (data: {
        projectId: string;
      }) => {
        this.sendMeshMetrics(socket, data.projectId);
      });

      // Handle topology optimization request
      socket.on('mesh-optimize-topology', async (data: {
        projectId: string;
      }) => {
        await this.optimizeTopology(data.projectId);
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        this.handleSocketDisconnect(socket);
      });
    });
  }

  /**
   * Handle peer joining mesh network
   */
  private async handleMeshJoin(socket: Socket, data: {
    projectId: string;
    userId: string;
    capabilities: MeshPeer['capabilities'];
  }): Promise<void> {
    try {
      const { projectId, userId, capabilities } = data;
      const peerId = uuidv4();

      // Create or get mesh topology
      let topology = this.meshTopologies.get(projectId);
      if (!topology) {
        topology = {
          projectId,
          peers: new Map(),
          connections: new Map(),
          connectionMatrix: [],
          lastUpdated: Date.now()
        };
        this.meshTopologies.set(projectId, topology);
      }

      // Create peer entry
      const peer: MeshPeer = {
        id: peerId,
        userId,
        socketId: socket.id,
        projectId,
        connectionState: 'connecting',
        connectionQuality: 1.0,
        latency: 0,
        bandwidth: 0,
        lastActivity: Date.now(),
        capabilities
      };

      topology.peers.set(peerId, peer);
      topology.connections.set(peerId, new Set());
      topology.lastUpdated = Date.now();

      // Join socket to project room
      socket.join(`mesh:${projectId}`);

      // Notify peer of successful join
      socket.emit('mesh-joined', {
        peerId,
        topology: this.getTopologySnapshot(topology)
      });

      // Broadcast peer joined to other mesh members
      socket.to(`mesh:${projectId}`).emit('mesh-peer-joined', {
        peer: this.sanitizePeerForBroadcast(peer)
      });

      // Trigger peer discovery for new peer
      await this.initiatePeerDiscovery(projectId, peerId);

      logger.info('Peer joined mesh network', {
        projectId,
        userId,
        peerId,
        totalPeers: topology.peers.size
      });

    } catch (error) {
      logger.error('Failed to handle mesh join', { error, data });
      socket.emit('mesh-join-error', { error: 'Failed to join mesh network' });
    }
  }

  /**
   * Handle peer leaving mesh network
   */
  private async handleMeshLeave(socket: Socket, data: {
    projectId: string;
    userId: string;
  }): Promise<void> {
    try {
      const { projectId, userId } = data;
      const topology = this.meshTopologies.get(projectId);
      
      if (!topology) {
        return;
      }

      // Find peer by userId and socketId
      let peerToRemove: MeshPeer | null = null;
      for (const [peerId, peer] of topology.peers) {
        if (peer.userId === userId && peer.socketId === socket.id) {
          peerToRemove = peer;
          break;
        }
      }

      if (!peerToRemove) {
        return;
      }

      await this.removePeerFromMesh(topology, peerToRemove.id);

      logger.info('Peer left mesh network', {
        projectId,
        userId,
        peerId: peerToRemove.id,
        remainingPeers: topology.peers.size
      });

    } catch (error) {
      logger.error('Failed to handle mesh leave', { error, data });
    }
  }

  /**
   * Handle peer discovery for mesh formation
   */
  private async handlePeerDiscovery(socket: Socket, data: {
    projectId: string;
  }): Promise<void> {
    try {
      const { projectId } = data;
      const topology = this.meshTopologies.get(projectId);
      
      if (!topology) {
        socket.emit('mesh-discovery-error', { error: 'Project not found' });
        return;
      }

      // Find requesting peer
      let requestingPeer: MeshPeer | null = null;
      for (const peer of topology.peers.values()) {
        if (peer.socketId === socket.id) {
          requestingPeer = peer;
          break;
        }
      }

      if (!requestingPeer) {
        socket.emit('mesh-discovery-error', { error: 'Peer not found' });
        return;
      }

      // Get optimal peers for connection
      const optimalPeers = await this.getOptimalPeersForConnection(topology, requestingPeer.id);

      // Send discovery response
      socket.emit('mesh-peers-discovered', {
        peers: optimalPeers.map(peer => this.sanitizePeerForBroadcast(peer)),
        topology: this.getTopologySnapshot(topology)
      });

      logger.debug('Peer discovery completed', {
        projectId,
        requestingPeerId: requestingPeer.id,
        discoveredPeers: optimalPeers.length
      });

    } catch (error) {
      logger.error('Failed to handle peer discovery', { error, data });
      socket.emit('mesh-discovery-error', { error: 'Discovery failed' });
    }
  }

  /**
   * Update connection quality metrics
   */
  private updateConnectionQuality(data: {
    projectId: string;
    peerId: string;
    targetPeerId: string;
    quality: number;
    latency: number;
    bandwidth: number;
  }): void {
    const topology = this.meshTopologies.get(data.projectId);
    if (!topology) return;

    const peer = topology.peers.get(data.peerId);
    const targetPeer = topology.peers.get(data.targetPeerId);

    if (!peer || !targetPeer) return;

    // Update peer metrics
    peer.connectionQuality = Math.max(peer.connectionQuality * 0.8 + data.quality * 0.2, 0);
    peer.latency = Math.max(peer.latency * 0.8 + data.latency * 0.2, 0);
    peer.bandwidth = Math.max(peer.bandwidth * 0.8 + data.bandwidth * 0.2, 0);
    peer.lastActivity = Date.now();

    // Update connection matrix
    this.updateConnectionMatrix(topology, data.peerId, data.targetPeerId, data.quality);

    // Check if topology optimization is needed
    if (data.quality < this.optimizationConfig.minConnectionQuality) {
      this.scheduleTopologyOptimization(data.projectId);
    }

    logger.debug('Connection quality updated', {
      projectId: data.projectId,
      peerId: data.peerId,
      targetPeerId: data.targetPeerId,
      quality: data.quality,
      latency: data.latency,
      bandwidth: data.bandwidth
    });
  }

  /**
   * Send mesh network metrics to requesting client
   */
  private sendMeshMetrics(socket: Socket, projectId: string): void {
    const topology = this.meshTopologies.get(projectId);
    if (!topology) {
      socket.emit('mesh-metrics-error', { error: 'Project not found' });
      return;
    }

    const metrics = this.calculateMeshMetrics(topology);
    this.connectionMetrics.set(projectId, metrics);

    socket.emit('mesh-metrics', {
      projectId,
      metrics,
      topology: this.getTopologySnapshot(topology)
    });

    logger.debug('Mesh metrics sent', { projectId, metrics });
  }

  /**
   * Optimize mesh topology for better performance
   */
  private async optimizeTopology(projectId: string): Promise<void> {
    const topology = this.meshTopologies.get(projectId);
    if (!topology || !this.optimizationConfig.enableAdaptiveTopology) {
      return;
    }

    try {
      logger.info('Starting topology optimization', { projectId });

      // Analyze current topology efficiency
      const currentMetrics = this.calculateMeshMetrics(topology);

      // Identify suboptimal connections
      const optimizations = this.identifyOptimizations(topology);

      // Apply optimizations
      for (const optimization of optimizations) {
        await this.applyOptimization(topology, optimization);
      }

      // Recalculate metrics
      const newMetrics = this.calculateMeshMetrics(topology);

      // Broadcast topology update
      this.io.to(`mesh:${projectId}`).emit('mesh-topology-optimized', {
        oldMetrics: currentMetrics,
        newMetrics,
        optimizations: optimizations.length
      });

      logger.info('Topology optimization completed', {
        projectId,
        optimizations: optimizations.length,
        oldEfficiency: currentMetrics.topologyEfficiency,
        newEfficiency: newMetrics.topologyEfficiency
      });

    } catch (error) {
      logger.error('Failed to optimize topology', { error, projectId });
    }
  }

  /**
   * Handle socket disconnection
   */
  private handleSocketDisconnect(socket: Socket): void {
    // Find and remove peer from all topologies
    for (const [projectId, topology] of this.meshTopologies) {
      for (const [peerId, peer] of topology.peers) {
        if (peer.socketId === socket.id) {
          this.removePeerFromMesh(topology, peerId);
          logger.info('Peer removed due to socket disconnect', {
            projectId,
            peerId,
            userId: peer.userId
          });
          break;
        }
      }
    }
  }

  /**
   * Remove peer from mesh topology
   */
  private async removePeerFromMesh(topology: MeshTopology, peerId: string): Promise<void> {
    const peer = topology.peers.get(peerId);
    if (!peer) return;

    // Remove peer from topology
    topology.peers.delete(peerId);
    topology.connections.delete(peerId);

    // Remove connections to this peer from other peers
    for (const [otherPeerId, connections] of topology.connections) {
      connections.delete(peerId);
    }

    // Update connection matrix
    this.rebuildConnectionMatrix(topology);

    // Broadcast peer left to remaining mesh members
    this.io.to(`mesh:${topology.projectId}`).emit('mesh-peer-left', {
      peerId,
      userId: peer.userId
    });

    // Clean up empty topologies
    if (topology.peers.size === 0) {
      this.meshTopologies.delete(topology.projectId);
      this.connectionMetrics.delete(topology.projectId);
    } else {
      // Trigger rebalancing for remaining peers
      await this.rebalanceConnections(topology);
    }

    topology.lastUpdated = Date.now();
  }

  /**
   * Initiate peer discovery for new mesh member
   */
  private async initiatePeerDiscovery(projectId: string, newPeerId: string): Promise<void> {
    const topology = this.meshTopologies.get(projectId);
    if (!topology) return;

    const newPeer = topology.peers.get(newPeerId);
    if (!newPeer) return;

    // Get optimal peers for the new member to connect to
    const optimalPeers = await this.getOptimalPeersForConnection(topology, newPeerId);

    // Notify existing peers about new peer
    for (const peer of optimalPeers) {
      const socket = this.io.sockets.sockets.get(peer.socketId);
      if (socket) {
        socket.emit('mesh-new-peer-available', {
          peer: this.sanitizePeerForBroadcast(newPeer),
          shouldConnect: true
        });
      }
    }

    logger.debug('Peer discovery initiated', {
      projectId,
      newPeerId,
      targetPeers: optimalPeers.length
    });
  }

  /**
   * Get optimal peers for connection based on topology analysis
   */
  private async getOptimalPeersForConnection(
    topology: MeshTopology,
    peerId: string
  ): Promise<MeshPeer[]> {
    const peer = topology.peers.get(peerId);
    if (!peer) return [];

    const availablePeers = Array.from(topology.peers.values())
      .filter(p => p.id !== peerId && p.connectionState === 'connected');

    // If mesh is small, connect to all peers
    if (availablePeers.length <= 3) {
      return availablePeers;
    }

    // For larger meshes, use intelligent selection
    const scoredPeers = availablePeers.map(targetPeer => ({
      peer: targetPeer,
      score: this.calculateConnectionScore(topology, peerId, targetPeer.id)
    }));

    // Sort by score and select top peers
    scoredPeers.sort((a, b) => b.score - a.score);

    const maxConnections = Math.min(
      this.optimizationConfig.maxPeersPerNode,
      Math.ceil(Math.sqrt(topology.peers.size))
    );

    return scoredPeers
      .slice(0, maxConnections)
      .map(item => item.peer);
  }

  /**
   * Calculate connection score for peer selection
   */
  private calculateConnectionScore(topology: MeshTopology, fromPeerId: string, toPeerId: string): number {
    const fromPeer = topology.peers.get(fromPeerId);
    const toPeer = topology.peers.get(toPeerId);

    if (!fromPeer || !toPeer) return 0;

    let score = 0;

    // Connection quality factor (40%)
    score += toPeer.connectionQuality * 0.4;

    // Latency factor (30%) - lower is better
    const latencyScore = Math.max(0, 1 - (toPeer.latency / this.optimizationConfig.latencyThreshold));
    score += latencyScore * 0.3;

    // Bandwidth factor (20%) - higher is better
    const bandwidthScore = Math.min(1, toPeer.bandwidth / this.optimizationConfig.bandwidthThreshold);
    score += bandwidthScore * 0.2;

    // Connection diversity factor (10%) - prefer peers with fewer connections
    const currentConnections = topology.connections.get(toPeerId)?.size || 0;
    const diversityScore = Math.max(0, 1 - (currentConnections / this.optimizationConfig.maxPeersPerNode));
    score += diversityScore * 0.1;

    return score;
  }

  /**
   * Calculate mesh network metrics
   */
  private calculateMeshMetrics(topology: MeshTopology): MeshNetworkMetrics {
    const peers = Array.from(topology.peers.values());
    const totalPeers = peers.length;

    if (totalPeers === 0) {
      return {
        totalPeers: 0,
        activeConnections: 0,
        averageLatency: 0,
        averageBandwidth: 0,
        networkHealth: 0,
        topologyEfficiency: 0,
        lastUpdated: Date.now()
      };
    }

    // Calculate active connections
    let activeConnections = 0;
    for (const connections of topology.connections.values()) {
      activeConnections += connections.size;
    }
    activeConnections = activeConnections / 2; // Each connection counted twice

    // Calculate averages
    const connectedPeers = peers.filter(p => p.connectionState === 'connected');
    const averageLatency = connectedPeers.reduce((sum, p) => sum + p.latency, 0) / Math.max(connectedPeers.length, 1);
    const averageBandwidth = connectedPeers.reduce((sum, p) => sum + p.bandwidth, 0) / Math.max(connectedPeers.length, 1);

    // Calculate network health (0-1)
    const healthyPeers = peers.filter(p =>
      p.connectionState === 'connected' &&
      p.connectionQuality >= this.optimizationConfig.minConnectionQuality
    ).length;
    const networkHealth = healthyPeers / totalPeers;

    // Calculate topology efficiency (0-1)
    const optimalConnections = Math.min(totalPeers * (totalPeers - 1) / 2, totalPeers * this.optimizationConfig.maxPeersPerNode / 2);
    const topologyEfficiency = optimalConnections > 0 ? activeConnections / optimalConnections : 0;

    return {
      totalPeers,
      activeConnections,
      averageLatency,
      averageBandwidth,
      networkHealth,
      topologyEfficiency: Math.min(topologyEfficiency, 1),
      lastUpdated: Date.now()
    };
  }

  /**
   * Get topology snapshot for client consumption
   */
  private getTopologySnapshot(topology: MeshTopology): any {
    return {
      projectId: topology.projectId,
      totalPeers: topology.peers.size,
      connections: Array.from(topology.connections.entries()).map(([peerId, connections]) => ({
        peerId,
        connectedTo: Array.from(connections)
      })),
      lastUpdated: topology.lastUpdated
    };
  }

  /**
   * Sanitize peer data for broadcasting
   */
  private sanitizePeerForBroadcast(peer: MeshPeer): any {
    return {
      id: peer.id,
      userId: peer.userId,
      connectionState: peer.connectionState,
      connectionQuality: peer.connectionQuality,
      latency: peer.latency,
      capabilities: peer.capabilities,
      lastActivity: peer.lastActivity
    };
  }

  /**
   * Update connection matrix for topology optimization
   */
  private updateConnectionMatrix(topology: MeshTopology, fromPeerId: string, toPeerId: string, quality: number): void {
    const peerIds = Array.from(topology.peers.keys());
    const fromIndex = peerIds.indexOf(fromPeerId);
    const toIndex = peerIds.indexOf(toPeerId);

    if (fromIndex === -1 || toIndex === -1) return;

    // Ensure matrix is properly sized
    while (topology.connectionMatrix.length <= Math.max(fromIndex, toIndex)) {
      topology.connectionMatrix.push(new Array(topology.peers.size).fill(0));
    }

    for (let i = 0; i < topology.connectionMatrix.length; i++) {
      while (topology.connectionMatrix[i].length < topology.peers.size) {
        topology.connectionMatrix[i].push(0);
      }
    }

    // Update matrix (symmetric)
    topology.connectionMatrix[fromIndex][toIndex] = quality;
    topology.connectionMatrix[toIndex][fromIndex] = quality;
  }

  /**
   * Rebuild connection matrix after peer removal
   */
  private rebuildConnectionMatrix(topology: MeshTopology): void {
    const peerIds = Array.from(topology.peers.keys());
    const size = peerIds.length;

    topology.connectionMatrix = Array(size).fill(null).map(() => Array(size).fill(0));

    // Rebuild based on current connections
    for (const [peerId, connections] of topology.connections) {
      const peerIndex = peerIds.indexOf(peerId);
      if (peerIndex === -1) continue;

      for (const connectedPeerId of connections) {
        const connectedIndex = peerIds.indexOf(connectedPeerId);
        if (connectedIndex === -1) continue;

        const peer = topology.peers.get(peerId);
        const quality = peer?.connectionQuality || 0;

        topology.connectionMatrix[peerIndex][connectedIndex] = quality;
        topology.connectionMatrix[connectedIndex][peerIndex] = quality;
      }
    }
  }

  /**
   * Start topology optimization timer
   */
  private startTopologyOptimization(): void {
    if (this.rebalanceTimer) {
      clearInterval(this.rebalanceTimer);
    }

    this.rebalanceTimer = setInterval(() => {
      for (const projectId of this.meshTopologies.keys()) {
        this.optimizeTopology(projectId);
      }
    }, this.optimizationConfig.rebalanceInterval);

    logger.info('Topology optimization timer started', {
      interval: this.optimizationConfig.rebalanceInterval
    });
  }

  /**
   * Schedule topology optimization for specific project
   */
  private scheduleTopologyOptimization(projectId: string): void {
    // Debounce optimization requests
    setTimeout(() => {
      this.optimizeTopology(projectId);
    }, 5000);
  }

  /**
   * Identify optimization opportunities in mesh topology
   */
  private identifyOptimizations(topology: MeshTopology): Array<{
    type: 'disconnect' | 'connect' | 'rebalance';
    fromPeerId: string;
    toPeerId?: string;
    reason: string;
    priority: number;
  }> {
    const optimizations: Array<{
      type: 'disconnect' | 'connect' | 'rebalance';
      fromPeerId: string;
      toPeerId?: string;
      reason: string;
      priority: number;
    }> = [];

    const peers = Array.from(topology.peers.values());

    for (const peer of peers) {
      const connections = topology.connections.get(peer.id) || new Set();

      // Check for poor quality connections to disconnect
      for (const connectedPeerId of connections) {
        const connectedPeer = topology.peers.get(connectedPeerId);
        if (!connectedPeer) continue;

        if (connectedPeer.connectionQuality < this.optimizationConfig.minConnectionQuality ||
            connectedPeer.latency > this.optimizationConfig.latencyThreshold) {
          optimizations.push({
            type: 'disconnect',
            fromPeerId: peer.id,
            toPeerId: connectedPeerId,
            reason: 'Poor connection quality',
            priority: 0.8
          });
        }
      }

      // Check for beneficial new connections
      if (connections.size < this.optimizationConfig.maxPeersPerNode) {
        const potentialPeers = peers.filter(p =>
          p.id !== peer.id &&
          !connections.has(p.id) &&
          p.connectionState === 'connected'
        );

        for (const potentialPeer of potentialPeers) {
          const score = this.calculateConnectionScore(topology, peer.id, potentialPeer.id);
          if (score > 0.7) {
            optimizations.push({
              type: 'connect',
              fromPeerId: peer.id,
              toPeerId: potentialPeer.id,
              reason: 'High quality connection opportunity',
              priority: score
            });
          }
        }
      }
    }

    // Sort by priority (highest first)
    optimizations.sort((a, b) => b.priority - a.priority);

    return optimizations.slice(0, 5); // Limit to top 5 optimizations
  }

  /**
   * Apply topology optimization
   */
  private async applyOptimization(topology: MeshTopology, optimization: {
    type: 'disconnect' | 'connect' | 'rebalance';
    fromPeerId: string;
    toPeerId?: string;
    reason: string;
    priority: number;
  }): Promise<void> {
    const fromPeer = topology.peers.get(optimization.fromPeerId);
    if (!fromPeer) return;

    const fromSocket = this.io.sockets.sockets.get(fromPeer.socketId);
    if (!fromSocket) return;

    switch (optimization.type) {
      case 'disconnect':
        if (optimization.toPeerId) {
          fromSocket.emit('mesh-optimization-disconnect', {
            targetPeerId: optimization.toPeerId,
            reason: optimization.reason
          });

          // Update topology
          topology.connections.get(optimization.fromPeerId)?.delete(optimization.toPeerId);
          topology.connections.get(optimization.toPeerId)?.delete(optimization.fromPeerId);
        }
        break;

      case 'connect':
        if (optimization.toPeerId) {
          const toPeer = topology.peers.get(optimization.toPeerId);
          if (toPeer) {
            fromSocket.emit('mesh-optimization-connect', {
              targetPeer: this.sanitizePeerForBroadcast(toPeer),
              reason: optimization.reason
            });
          }
        }
        break;

      case 'rebalance':
        fromSocket.emit('mesh-optimization-rebalance', {
          reason: optimization.reason
        });
        break;
    }

    logger.debug('Applied mesh optimization', {
      projectId: topology.projectId,
      optimization
    });
  }

  /**
   * Rebalance connections after peer removal
   */
  private async rebalanceConnections(topology: MeshTopology): Promise<void> {
    const peers = Array.from(topology.peers.values());

    // Find peers with insufficient connections
    const underconnectedPeers = peers.filter(peer => {
      const connections = topology.connections.get(peer.id)?.size || 0;
      return connections < Math.min(this.optimizationConfig.maxPeersPerNode, peers.length - 1);
    });

    // Suggest new connections for underconnected peers
    for (const peer of underconnectedPeers) {
      const optimalPeers = await this.getOptimalPeersForConnection(topology, peer.id);
      const currentConnections = topology.connections.get(peer.id) || new Set();

      const newConnections = optimalPeers.filter(p => !currentConnections.has(p.id));

      if (newConnections.length > 0) {
        const socket = this.io.sockets.sockets.get(peer.socketId);
        if (socket) {
          socket.emit('mesh-rebalance-suggestions', {
            suggestedPeers: newConnections.map(p => this.sanitizePeerForBroadcast(p)),
            reason: 'Network rebalancing after peer departure'
          });
        }
      }
    }

    logger.debug('Mesh rebalancing completed', {
      projectId: topology.projectId,
      underconnectedPeers: underconnectedPeers.length
    });
  }

  /**
   * Public API methods
   */

  /**
   * Get mesh network metrics for a project
   */
  public getMeshMetrics(projectId: string): MeshNetworkMetrics | null {
    const topology = this.meshTopologies.get(projectId);
    if (!topology) return null;

    return this.calculateMeshMetrics(topology);
  }

  /**
   * Get all active mesh topologies
   */
  public getActiveMeshTopologies(): Array<{
    projectId: string;
    peerCount: number;
    connectionCount: number;
    health: number;
  }> {
    return Array.from(this.meshTopologies.entries()).map(([projectId, topology]) => {
      const metrics = this.calculateMeshMetrics(topology);
      return {
        projectId,
        peerCount: metrics.totalPeers,
        connectionCount: metrics.activeConnections,
        health: metrics.networkHealth
      };
    });
  }

  /**
   * Force topology optimization for a project
   */
  public async forceOptimization(projectId: string): Promise<boolean> {
    const topology = this.meshTopologies.get(projectId);
    if (!topology) return false;

    await this.optimizeTopology(projectId);
    return true;
  }

  /**
   * Cleanup and shutdown
   */
  public shutdown(): void {
    if (this.rebalanceTimer) {
      clearInterval(this.rebalanceTimer);
      this.rebalanceTimer = null;
    }

    // Notify all peers of shutdown
    for (const topology of this.meshTopologies.values()) {
      this.io.to(`mesh:${topology.projectId}`).emit('mesh-shutdown', {
        reason: 'Server shutdown'
      });
    }

    this.meshTopologies.clear();
    this.connectionMetrics.clear();
    this.peerConnections.clear();

    logger.info('MeshNetworkManager shutdown completed');
  }
}
