import { Pool, PoolClient } from 'pg';
import { createModuleLogger } from '../utils/logger';

const logger = createModuleLogger('PostgreSQLDatabase');

export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password?: string;
  ssl?: boolean;
  max?: number;
  idleTimeoutMillis?: number;
  connectionTimeoutMillis?: number;
}

export class PostgresDatabaseService {
  private static instance: PostgresDatabaseService;
  private pool: Pool;
  private config: DatabaseConfig;

  private constructor() {
    this.config = {
      host: process.env.POSTGRES_HOST || 'localhost',
      port: parseInt(process.env.POSTGRES_PORT || '5432'),
      database: process.env.POSTGRES_DB || 'cabinet_insight_pro',
      user: process.env.POSTGRES_USER || process.env.USER || 'postgres',
      password: process.env.POSTGRES_PASSWORD,
      ssl: process.env.POSTGRES_SSL === 'true',
      max: 20, // Maximum number of clients in the pool
      idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
      connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
    };

    this.pool = new Pool(this.config);
    
    // Handle pool errors
    this.pool.on('error', (err) => {
      logger.error('Unexpected error on idle client', { error: err });
    });

    // Test connection
    this.testConnection();
    
    logger.info('PostgreSQL database service initialized', {
      host: this.config.host,
      port: this.config.port,
      database: this.config.database,
      user: this.config.user
    });
  }

  public static getInstance(): PostgresDatabaseService {
    if (!PostgresDatabaseService.instance) {
      PostgresDatabaseService.instance = new PostgresDatabaseService();
    }
    return PostgresDatabaseService.instance;
  }

  private async testConnection(): Promise<void> {
    try {
      const client = await this.pool.connect();
      const result = await client.query('SELECT NOW()');
      client.release();
      logger.info('PostgreSQL connection test successful', { timestamp: result.rows[0].now });
    } catch (error) {
      logger.error('PostgreSQL connection test failed', { error });
      throw error;
    }
  }

  public async query(text: string, params?: any[]): Promise<any> {
    const start = Date.now();
    try {
      const result = await this.pool.query(text, params);
      const duration = Date.now() - start;
      logger.debug('Executed query', { text, duration, rows: result.rowCount });
      return result;
    } catch (error) {
      logger.error('Query execution failed', { text, params, error });
      throw error;
    }
  }

  public async getClient(): Promise<PoolClient> {
    return await this.pool.connect();
  }

  public async close(): Promise<void> {
    await this.pool.end();
    logger.info('PostgreSQL connection pool closed');
  }

  // User management methods
  public async createUser(userData: {
    id?: string;
    email: string;
    passwordHash: string;
    firstName: string;
    lastName: string;
    role?: string;
  }) {
    const query = `
      INSERT INTO users (email, password_hash, first_name, last_name, role)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `;
    
    const result = await this.query(query, [
      userData.email,
      userData.passwordHash,
      userData.firstName,
      userData.lastName,
      userData.role || 'viewer'
    ]);
    
    return result.rows[0];
  }

  public async getUserByEmail(email: string) {
    const query = 'SELECT * FROM users WHERE email = $1';
    const result = await this.query(query, [email]);
    return result.rows[0];
  }

  public async getUserById(id: string) {
    const query = 'SELECT * FROM users WHERE id = $1';
    const result = await this.query(query, [id]);
    return result.rows[0];
  }

  public async updateUserLastLogin(userId: string) {
    const query = 'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1';
    const result = await this.query(query, [userId]);
    return result.rowCount;
  }

  // Project management methods
  public async createProject(projectData: {
    id?: string;
    name: string;
    description?: string;
    organizationId?: string;
    ownerId: string;
    visibility?: string;
    tags?: string[];
  }) {
    const query = `
      INSERT INTO projects (name, description, organization_id, owner_id, visibility, tags)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `;
    
    const result = await this.query(query, [
      projectData.name,
      projectData.description || null,
      projectData.organizationId || null,
      projectData.ownerId,
      projectData.visibility || 'private',
      projectData.tags ? JSON.stringify(projectData.tags) : null
    ]);
    
    return result.rows[0];
  }

  public async getProjectById(id: string) {
    const query = 'SELECT * FROM projects WHERE id = $1';
    const result = await this.query(query, [id]);
    return result.rows[0];
  }

  public async getProjectsByOwner(ownerId: string) {
    const query = 'SELECT * FROM projects WHERE owner_id = $1 ORDER BY created_at DESC';
    const result = await this.query(query, [ownerId]);
    return result.rows;
  }

  // Analysis results methods
  public async createAnalysisResult(analysisData: {
    projectId?: string;
    filename: string;
    filePath: string;
    fileSize?: number;
    analysisData?: any;
    status?: string;
    confidenceScore?: number;
    processingTimeMs?: number;
    createdBy?: string;
  }) {
    const query = `
      INSERT INTO analysis_results (
        project_id, filename, file_path, file_size, analysis_data, 
        status, confidence_score, processing_time_ms, created_by
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `;
    
    const result = await this.query(query, [
      analysisData.projectId || null,
      analysisData.filename,
      analysisData.filePath,
      analysisData.fileSize || null,
      analysisData.analysisData ? JSON.stringify(analysisData.analysisData) : null,
      analysisData.status || 'pending',
      analysisData.confidenceScore || null,
      analysisData.processingTimeMs || null,
      analysisData.createdBy || null
    ]);
    
    return result.rows[0];
  }

  public async getAnalysisResultById(id: string) {
    const query = 'SELECT * FROM analysis_results WHERE id = $1';
    const result = await this.query(query, [id]);
    return result.rows[0];
  }

  public async updateAnalysisResult(id: string, updates: any) {
    const setClause = Object.keys(updates)
      .map((key, index) => `${key} = $${index + 2}`)
      .join(', ');
    
    const query = `UPDATE analysis_results SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING *`;
    const values = [id, ...Object.values(updates)];
    
    const result = await this.query(query, values);
    return result.rows[0];
  }

  // Performance metrics methods
  public async insertPerformanceMetric(metric: {
    metricType: string;
    metricName: string;
    value: number;
    metadata?: any;
  }) {
    const query = `
      INSERT INTO performance_metrics (metric_type, metric_name, value, metadata)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `;
    
    const result = await this.query(query, [
      metric.metricType,
      metric.metricName,
      metric.value,
      metric.metadata ? JSON.stringify(metric.metadata) : null
    ]);
    
    return result.rows[0];
  }

  public async getPerformanceMetrics(metricType?: string, limit: number = 100) {
    let query = 'SELECT * FROM performance_metrics';
    const params: any[] = [];
    
    if (metricType) {
      query += ' WHERE metric_type = $1';
      params.push(metricType);
    }
    
    query += ' ORDER BY timestamp DESC LIMIT $' + (params.length + 1);
    params.push(limit);
    
    const result = await this.query(query, params);
    return result.rows;
  }

  // Test execution tracking
  public async insertTestExecution(execution: {
    testSuite: string;
    testName: string;
    browser?: string;
    status: string;
    durationMs?: number;
    errorMessage?: string;
    metadata?: any;
  }) {
    const query = `
      INSERT INTO test_executions (test_suite, test_name, browser, status, duration_ms, error_message, metadata)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;
    
    const result = await this.query(query, [
      execution.testSuite,
      execution.testName,
      execution.browser || null,
      execution.status,
      execution.durationMs || null,
      execution.errorMessage || null,
      execution.metadata ? JSON.stringify(execution.metadata) : null
    ]);
    
    return result.rows[0];
  }

  public async getTestExecutions(testSuite?: string, limit: number = 100) {
    let query = 'SELECT * FROM test_executions';
    const params: any[] = [];
    
    if (testSuite) {
      query += ' WHERE test_suite = $1';
      params.push(testSuite);
    }
    
    query += ' ORDER BY executed_at DESC LIMIT $' + (params.length + 1);
    params.push(limit);
    
    const result = await this.query(query, params);
    return result.rows;
  }
}
