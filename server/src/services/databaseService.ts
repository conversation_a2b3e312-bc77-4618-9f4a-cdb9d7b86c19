import Database from 'better-sqlite3';
import fs from 'fs';
import path from 'path';
import { logger } from '@/utils/logger';

export class DatabaseService {
  private static instance: DatabaseService;
  private db: Database.Database;
  private dbPath: string;

  private constructor() {
    // Create database directory if it doesn't exist
    const dbDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }

    this.dbPath = path.join(dbDir, 'cabinet-insight-pro.db');
    this.db = new Database(this.dbPath);
    
    // Enable foreign key constraints
    this.db.pragma('foreign_keys = ON');
    
    // Initialize database schema
    this.initializeSchema();
    
    logger.info('Database service initialized', {
      dbPath: this.dbPath,
      version: this.db.pragma('user_version', { simple: true })
    });
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  private initializeSchema(): void {
    try {
      const schemaPath = path.join(__dirname, '../database/schema.sql');
      const schema = fs.readFileSync(schemaPath, 'utf8');
      
      // Execute schema in a transaction
      this.db.transaction(() => {
        this.db.exec(schema);
      })();
      
      logger.info('Database schema initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize database schema', { error });
      throw error;
    }
  }

  public getDatabase(): Database.Database {
    return this.db;
  }

  public close(): void {
    if (this.db) {
      this.db.close();
      logger.info('Database connection closed');
    }
  }

  // User management methods
  public createUser(userData: {
    id: string;
    email: string;
    passwordHash: string;
    firstName: string;
    lastName: string;
    role?: string;
  }) {
    const stmt = this.db.prepare(`
      INSERT INTO users (id, email, password_hash, first_name, last_name, role)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    
    return stmt.run(
      userData.id,
      userData.email,
      userData.passwordHash,
      userData.firstName,
      userData.lastName,
      userData.role || 'viewer'
    );
  }

  public getUserByEmail(email: string) {
    const stmt = this.db.prepare('SELECT * FROM users WHERE email = ?');
    return stmt.get(email);
  }

  public getUserById(id: string) {
    const stmt = this.db.prepare('SELECT * FROM users WHERE id = ?');
    return stmt.get(id);
  }

  public updateUserLastLogin(userId: string) {
    const stmt = this.db.prepare(`
      UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
    `);
    return stmt.run(userId);
  }

  // Project management methods
  public createProject(projectData: {
    id: string;
    name: string;
    description?: string;
    organizationId?: string;
    ownerId: string;
    visibility?: string;
    tags?: string[];
  }) {
    const stmt = this.db.prepare(`
      INSERT INTO projects (id, name, description, organization_id, owner_id, visibility, tags)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    
    return stmt.run(
      projectData.id,
      projectData.name,
      projectData.description || null,
      projectData.organizationId || null,
      projectData.ownerId,
      projectData.visibility || 'private',
      projectData.tags ? JSON.stringify(projectData.tags) : null
    );
  }

  public getProjectById(id: string) {
    const stmt = this.db.prepare(`
      SELECT p.*, u.first_name, u.last_name, u.email as owner_email
      FROM projects p
      JOIN users u ON p.owner_id = u.id
      WHERE p.id = ?
    `);
    return stmt.get(id);
  }

  public getProjectsByUser(userId: string) {
    const stmt = this.db.prepare(`
      SELECT DISTINCT p.*, u.first_name, u.last_name
      FROM projects p
      JOIN users u ON p.owner_id = u.id
      LEFT JOIN project_permissions pp ON p.id = pp.project_id
      WHERE p.owner_id = ? OR pp.user_id = ?
      ORDER BY p.updated_at DESC
    `);
    return stmt.all(userId, userId);
  }

  public updateProject(projectId: string, updates: {
    name?: string;
    description?: string;
    visibility?: string;
    status?: string;
    tags?: string[];
    folderPath?: string;
  }) {
    const fields: string[] = [];
    const values: any[] = [];

    if (updates.name !== undefined) {
      fields.push('name = ?');
      values.push(updates.name);
    }
    if (updates.description !== undefined) {
      fields.push('description = ?');
      values.push(updates.description);
    }
    if (updates.visibility !== undefined) {
      fields.push('visibility = ?');
      values.push(updates.visibility);
    }
    if (updates.status !== undefined) {
      fields.push('status = ?');
      values.push(updates.status);
    }
    if (updates.tags !== undefined) {
      fields.push('tags = ?');
      values.push(updates.tags ? JSON.stringify(updates.tags) : null);
    }
    if (updates.folderPath !== undefined) {
      fields.push('folder_path = ?');
      values.push(updates.folderPath);
    }

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    fields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(projectId);

    const stmt = this.db.prepare(`
      UPDATE projects SET ${fields.join(', ')} WHERE id = ?
    `);

    return stmt.run(...values);
  }

  public deleteProject(projectId: string) {
    // Soft delete by updating status
    const stmt = this.db.prepare(`
      UPDATE projects SET status = 'deleted', updated_at = CURRENT_TIMESTAMP WHERE id = ?
    `);
    return stmt.run(projectId);
  }

  public bulkUpdateProjects(projectIds: string[], updates: {
    status?: string;
    visibility?: string;
    tags?: string[];
  }) {
    const fields: string[] = [];
    const values: any[] = [];

    if (updates.status !== undefined) {
      fields.push('status = ?');
      values.push(updates.status);
    }
    if (updates.visibility !== undefined) {
      fields.push('visibility = ?');
      values.push(updates.visibility);
    }
    if (updates.tags !== undefined) {
      fields.push('tags = ?');
      values.push(updates.tags ? JSON.stringify(updates.tags) : null);
    }

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    fields.push('updated_at = CURRENT_TIMESTAMP');

    const placeholders = projectIds.map(() => '?').join(',');
    const stmt = this.db.prepare(`
      UPDATE projects SET ${fields.join(', ')} WHERE id IN (${placeholders})
    `);

    return stmt.run(...values, ...projectIds);
  }

  // Analysis results methods
  public createAnalysisResult(analysisData: {
    id: string;
    projectId?: string;
    filename: string;
    filePath: string;
    fileSize: number;
    analysisData?: any;
    createdBy?: string;
  }) {
    const stmt = this.db.prepare(`
      INSERT INTO analysis_results (id, project_id, filename, file_path, file_size, analysis_data, created_by)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    
    return stmt.run(
      analysisData.id,
      analysisData.projectId || null,
      analysisData.filename,
      analysisData.filePath,
      analysisData.fileSize,
      analysisData.analysisData ? JSON.stringify(analysisData.analysisData) : null,
      analysisData.createdBy || null
    );
  }

  public updateAnalysisResult(id: string, updates: {
    analysisData?: any;
    status?: string;
    confidenceScore?: number;
    processingTimeMs?: number;
  }) {
    const fields = [];
    const values = [];
    
    if (updates.analysisData !== undefined) {
      fields.push('analysis_data = ?');
      values.push(JSON.stringify(updates.analysisData));
    }
    if (updates.status !== undefined) {
      fields.push('status = ?');
      values.push(updates.status);
    }
    if (updates.confidenceScore !== undefined) {
      fields.push('confidence_score = ?');
      values.push(updates.confidenceScore);
    }
    if (updates.processingTimeMs !== undefined) {
      fields.push('processing_time_ms = ?');
      values.push(updates.processingTimeMs);
    }
    
    fields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(id);
    
    const stmt = this.db.prepare(`
      UPDATE analysis_results SET ${fields.join(', ')} WHERE id = ?
    `);
    
    return stmt.run(...values);
  }

  public getAnalysisById(id: string) {
    const stmt = this.db.prepare(`
      SELECT ar.*, p.name as project_name, u.first_name, u.last_name
      FROM analysis_results ar
      LEFT JOIN projects p ON ar.project_id = p.id
      LEFT JOIN users u ON ar.created_by = u.id
      WHERE ar.id = ?
    `);
    return stmt.get(id);
  }

  // Comment system methods
  public createComment(commentData: {
    id: string;
    projectId?: string;
    analysisId?: string;
    parentCommentId?: string;
    content: string;
    authorId: string;
    positionData?: any;
    mentions?: string[];
  }) {
    const stmt = this.db.prepare(`
      INSERT INTO comments (id, project_id, analysis_id, parent_comment_id, content, author_id, position_data, mentions)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    return stmt.run(
      commentData.id,
      commentData.projectId || null,
      commentData.analysisId || null,
      commentData.parentCommentId || null,
      commentData.content,
      commentData.authorId,
      commentData.positionData ? JSON.stringify(commentData.positionData) : null,
      commentData.mentions ? JSON.stringify(commentData.mentions) : null
    );
  }

  public getCommentsByAnalysis(analysisId: string) {
    const stmt = this.db.prepare(`
      SELECT c.*, u.first_name, u.last_name, u.avatar_url
      FROM comments c
      JOIN users u ON c.author_id = u.id
      WHERE c.analysis_id = ?
      ORDER BY c.created_at ASC
    `);
    return stmt.all(analysisId);
  }

  public updateCommentStatus(commentId: string, status: string) {
    const stmt = this.db.prepare(`
      UPDATE comments SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?
    `);
    return stmt.run(status, commentId);
  }

  public updateComment(commentId: string, content: string, mentions?: string[]) {
    const stmt = this.db.prepare(`
      UPDATE comments SET content = ?, mentions = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?
    `);
    return stmt.run(
      content,
      mentions ? JSON.stringify(mentions) : null,
      commentId
    );
  }

  public deleteComment(commentId: string) {
    const stmt = this.db.prepare(`
      DELETE FROM comments WHERE id = ?
    `);
    return stmt.run(commentId);
  }

  public getCommentById(commentId: string) {
    const stmt = this.db.prepare(`
      SELECT c.*, u.first_name, u.last_name, u.avatar_url
      FROM comments c
      JOIN users u ON c.author_id = u.id
      WHERE c.id = ?
    `);
    return stmt.get(commentId);
  }

  public getCommentsByProject(projectId: string) {
    const stmt = this.db.prepare(`
      SELECT c.*, u.first_name, u.last_name, u.avatar_url
      FROM comments c
      JOIN users u ON c.author_id = u.id
      WHERE c.project_id = ?
      ORDER BY c.created_at ASC
    `);
    return stmt.all(projectId);
  }

  // Activity logging
  public logActivity(activityData: {
    id: string;
    userId: string;
    actionType: string;
    resourceType: string;
    resourceId: string;
    details?: any;
    ipAddress?: string;
    userAgent?: string;
  }) {
    const stmt = this.db.prepare(`
      INSERT INTO activity_log (id, user_id, action_type, resource_type, resource_id, details, ip_address, user_agent)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    return stmt.run(
      activityData.id,
      activityData.userId,
      activityData.actionType,
      activityData.resourceType,
      activityData.resourceId,
      activityData.details ? JSON.stringify(activityData.details) : null,
      activityData.ipAddress || null,
      activityData.userAgent || null
    );
  }

  // Utility methods
  public beginTransaction() {
    return this.db.transaction(() => {});
  }

  public backup(backupPath: string): void {
    this.db.backup(backupPath);
    logger.info('Database backup created', { backupPath });
  }
}
