import { v4 as uuidv4 } from 'uuid';
import { DatabaseService } from './databaseService';
import { logger } from '@/utils/logger';
import { Server as SocketIOServer } from 'socket.io';

export interface Comment {
  id: string;
  projectId?: string;
  analysisId?: string;
  parentCommentId?: string;
  content: string;
  authorId: string;
  authorName: string;
  avatarUrl?: string;
  status: 'open' | 'in_progress' | 'resolved';
  positionData?: any;
  attachments?: string[];
  mentions?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Annotation {
  id: string;
  commentId?: string;
  analysisId: string;
  annotationType: 'point' | 'rectangle' | 'circle' | 'polygon' | 'arrow';
  coordinates: any;
  styleData?: any;
  createdBy: string;
  createdAt: Date;
}

export interface UserPresence {
  userId: string;
  userName: string;
  avatarUrl?: string;
  projectId?: string;
  analysisId?: string;
  status: 'online' | 'away' | 'offline';
  currentView?: any;
  lastActivity: Date;
}

export interface ProjectPermission {
  id: string;
  projectId: string;
  userId?: string;
  email?: string;
  permissionLevel: 'view' | 'comment' | 'edit' | 'admin';
  grantedBy: string;
  expiresAt?: Date;
  passwordHash?: string;
  createdAt: Date;
}

export class CollaborationService {
  private db: DatabaseService;
  private io?: SocketIOServer;
  private userPresence: Map<string, UserPresence> = new Map();

  constructor(io?: SocketIOServer) {
    this.db = DatabaseService.getInstance();
    this.io = io;
    this.setupSocketHandlers();
  }

  /**
   * Setup WebSocket event handlers for real-time collaboration
   */
  private setupSocketHandlers(): void {
    if (!this.io) return;

    this.io.on('connection', (socket) => {
      logger.info('User connected to collaboration service', { socketId: socket.id });

      // Handle user joining a project
      socket.on('join-project', async (data: { projectId: string; userId: string }) => {
        try {
          socket.join(`project:${data.projectId}`);
          await this.updateUserPresence(data.userId, {
            projectId: data.projectId,
            status: 'online'
          });
          
          // Broadcast user joined to other project members
          socket.to(`project:${data.projectId}`).emit('user-joined', {
            userId: data.userId,
            projectId: data.projectId
          });
        } catch (error) {
          logger.error('Failed to join project', { error, data });
        }
      });

      // Handle user leaving a project
      socket.on('leave-project', async (data: { projectId: string; userId: string }) => {
        try {
          socket.leave(`project:${data.projectId}`);
          await this.updateUserPresence(data.userId, {
            status: 'offline'
          });
          
          // Broadcast user left to other project members
          socket.to(`project:${data.projectId}`).emit('user-left', {
            userId: data.userId,
            projectId: data.projectId
          });
        } catch (error) {
          logger.error('Failed to leave project', { error, data });
        }
      });

      // Handle real-time commenting
      socket.on('new-comment', async (commentData: any) => {
        try {
          const comment = await this.createComment(commentData);
          
          // Broadcast to project members
          if (comment.projectId) {
            this.io?.to(`project:${comment.projectId}`).emit('comment-added', comment);
          }
          
          // Send notifications to mentioned users
          if (comment.mentions && comment.mentions.length > 0) {
            await this.sendMentionNotifications(comment);
          }
        } catch (error) {
          logger.error('Failed to create comment', { error, commentData });
        }
      });

      // Handle comment status updates
      socket.on('update-comment-status', async (data: { commentId: string; status: string; projectId: string }) => {
        try {
          await this.updateCommentStatus(data.commentId, data.status);
          
          // Broadcast status update
          this.io?.to(`project:${data.projectId}`).emit('comment-status-updated', {
            commentId: data.commentId,
            status: data.status
          });
        } catch (error) {
          logger.error('Failed to update comment status', { error, data });
        }
      });

      // Handle visual annotations
      socket.on('new-annotation', async (annotationData: any) => {
        try {
          const annotation = await this.createAnnotation(annotationData);
          
          // Broadcast to project members
          if (annotationData.projectId) {
            this.io?.to(`project:${annotationData.projectId}`).emit('annotation-added', annotation);
          }
        } catch (error) {
          logger.error('Failed to create annotation', { error, annotationData });
        }
      });

      // Handle user cursor/view updates
      socket.on('cursor-update', (data: { projectId: string; userId: string; position: any; viewData?: any }) => {
        socket.to(`project:${data.projectId}`).emit('cursor-moved', {
          ...data,
          user: socket.user,
          timestamp: new Date().toISOString()
        });
      });

      // Handle cursor leave
      socket.on('cursor-leave', (data: { projectId: string; userId: string }) => {
        socket.to(`project:${data.projectId}`).emit('cursor-left', data);
      });

      // Handle activity updates
      socket.on('activity-update', (data: {
        projectId: string;
        userId: string;
        activity: 'viewing' | 'editing' | 'commenting' | 'idle';
        currentView?: any;
      }) => {
        this.updateUserPresence(data.userId, {
          projectId: data.projectId,
          status: 'online',
          activity: data.activity,
          currentView: data.currentView
        });

        socket.to(`project:${data.projectId}`).emit('activity-updated', data);
      });

      // Handle version control operations
      socket.on('version-created', async (versionData: any) => {
        try {
          // Store version in database
          await this.createProjectVersion(versionData);

          // Broadcast to project members
          socket.to(`project:${versionData.projectId}`).emit('version-added', versionData);
        } catch (error) {
          logger.error('Failed to create version', { error, versionData });
        }
      });

      // Handle annotation operations
      socket.on('annotation-updated', (data: { id: string; updates: any }) => {
        socket.to(`project:${data.projectId || 'unknown'}`).emit('annotation-updated', data);
      });

      socket.on('annotation-resolved', (data: { id: string; resolvedBy: string }) => {
        socket.to(`project:${data.projectId || 'unknown'}`).emit('annotation-resolved', data);
      });

      socket.on('annotation-deleted', (data: { id: string }) => {
        socket.to(`project:${data.projectId || 'unknown'}`).emit('annotation-deleted', data);
      });

      // Handle synchronization operations
      socket.on('sync-operation', async (operation: any) => {
        try {
          // Process operation and broadcast to other clients
          const processedOperation = await this.processSyncOperation(operation);
          socket.to(`project:${operation.projectId}`).emit('sync-operation', processedOperation);
        } catch (error) {
          logger.error('Failed to process sync operation', { error, operation });
          socket.emit('sync-error', { error: 'Failed to process operation' });
        }
      });

      socket.on('sync-state', (state: any) => {
        // Broadcast state update to other clients
        socket.to(`project:${state.projectId}`).emit('sync-state-update', state);
      });

      // Handle disconnect
      socket.on('disconnect', () => {
        logger.info('User disconnected from collaboration service', { socketId: socket.id });
        // Update user presence to offline
        // Note: We'd need to track socket-to-user mapping for this
      });
    });
  }

  /**
   * Create a new comment
   */
  public async createComment(commentData: {
    projectId?: string;
    analysisId?: string;
    parentCommentId?: string;
    content: string;
    authorId: string;
    positionData?: any;
    mentions?: string[];
  }): Promise<Comment> {
    try {
      const commentId = uuidv4();
      
      this.db.createComment({
        id: commentId,
        projectId: commentData.projectId,
        analysisId: commentData.analysisId,
        parentCommentId: commentData.parentCommentId,
        content: commentData.content,
        authorId: commentData.authorId,
        positionData: commentData.positionData,
        mentions: commentData.mentions
      });

      // Log activity
      this.db.logActivity({
        id: uuidv4(),
        userId: commentData.authorId,
        actionType: 'create',
        resourceType: 'comment',
        resourceId: commentId,
        details: {
          projectId: commentData.projectId,
          analysisId: commentData.analysisId
        }
      });

      // Get the created comment with author info
      const comments = this.db.getCommentsByAnalysis(commentData.analysisId || '');
      const comment = comments.find(c => c.id === commentId);
      
      if (!comment) {
        throw new Error('Failed to retrieve created comment');
      }

      return this.formatComment(comment);
    } catch (error) {
      logger.error('Failed to create comment', { error, commentData });
      throw error;
    }
  }

  /**
   * Get comments for an analysis
   */
  public async getCommentsByAnalysis(analysisId: string): Promise<Comment[]> {
    try {
      const comments = this.db.getCommentsByAnalysis(analysisId);
      return comments.map(comment => this.formatComment(comment));
    } catch (error) {
      logger.error('Failed to get comments', { error, analysisId });
      throw error;
    }
  }

  /**
   * Update comment status
   */
  public async updateCommentStatus(commentId: string, status: string): Promise<void> {
    try {
      this.db.updateCommentStatus(commentId, status);

      logger.info('Comment status updated', { commentId, status });
    } catch (error) {
      logger.error('Failed to update comment status', { error, commentId, status });
      throw error;
    }
  }

  /**
   * Update comment content
   */
  public async updateComment(commentId: string, content: string, mentions?: string[]): Promise<Comment> {
    try {
      this.db.updateComment(commentId, content, mentions);

      // Get the updated comment
      const comment = this.db.getCommentById(commentId);
      if (!comment) {
        throw new Error('Comment not found after update');
      }

      // Log activity
      this.db.logActivity({
        id: uuidv4(),
        userId: comment.author_id,
        actionType: 'update',
        resourceType: 'comment',
        resourceId: commentId,
        details: { content, mentions }
      });

      logger.info('Comment updated successfully', { commentId });
      return this.formatComment(comment);
    } catch (error) {
      logger.error('Failed to update comment', { error, commentId, content });
      throw error;
    }
  }

  /**
   * Delete comment
   */
  public async deleteComment(commentId: string, userId: string): Promise<void> {
    try {
      // Get comment to check ownership
      const comment = this.db.getCommentById(commentId);
      if (!comment) {
        throw new Error('Comment not found');
      }

      // Check if user can delete (owner or admin)
      if (comment.author_id !== userId) {
        // TODO: Add admin check when role system is implemented
        throw new Error('Access denied');
      }

      this.db.deleteComment(commentId);

      // Log activity
      this.db.logActivity({
        id: uuidv4(),
        userId,
        actionType: 'delete',
        resourceType: 'comment',
        resourceId: commentId
      });

      logger.info('Comment deleted successfully', { commentId, userId });
    } catch (error) {
      logger.error('Failed to delete comment', { error, commentId, userId });
      throw error;
    }
  }

  /**
   * Get comment by ID
   */
  public async getCommentById(commentId: string): Promise<Comment | null> {
    try {
      const comment = this.db.getCommentById(commentId);
      if (!comment) {
        return null;
      }
      return this.formatComment(comment);
    } catch (error) {
      logger.error('Failed to get comment', { error, commentId });
      throw error;
    }
  }

  /**
   * Get comments for a project
   */
  public async getCommentsByProject(projectId: string): Promise<Comment[]> {
    try {
      const comments = this.db.getCommentsByProject(projectId);
      return comments.map(comment => this.formatComment(comment));
    } catch (error) {
      logger.error('Failed to get project comments', { error, projectId });
      throw error;
    }
  }

  /**
   * Create a visual annotation
   */
  public async createAnnotation(annotationData: {
    commentId?: string;
    analysisId: string;
    annotationType: string;
    coordinates: any;
    styleData?: any;
    createdBy: string;
  }): Promise<Annotation> {
    try {
      const annotationId = uuidv4();
      
      // Insert annotation into database (would need to add this method to DatabaseService)
      // For now, just return a formatted annotation
      
      const annotation: Annotation = {
        id: annotationId,
        commentId: annotationData.commentId,
        analysisId: annotationData.analysisId,
        annotationType: annotationData.annotationType as any,
        coordinates: annotationData.coordinates,
        styleData: annotationData.styleData,
        createdBy: annotationData.createdBy,
        createdAt: new Date()
      };

      logger.info('Annotation created', { annotationId, analysisId: annotationData.analysisId });
      
      return annotation;
    } catch (error) {
      logger.error('Failed to create annotation', { error, annotationData });
      throw error;
    }
  }

  /**
   * Update user presence
   */
  public async updateUserPresence(userId: string, updates: {
    projectId?: string;
    analysisId?: string;
    status?: 'online' | 'away' | 'offline';
    currentView?: any;
  }): Promise<void> {
    try {
      const existingPresence = this.userPresence.get(userId);
      
      const presence: UserPresence = {
        userId,
        userName: existingPresence?.userName || 'Unknown User',
        avatarUrl: existingPresence?.avatarUrl,
        projectId: updates.projectId || existingPresence?.projectId,
        analysisId: updates.analysisId || existingPresence?.analysisId,
        status: updates.status || existingPresence?.status || 'online',
        currentView: updates.currentView || existingPresence?.currentView,
        lastActivity: new Date()
      };

      this.userPresence.set(userId, presence);

      // Broadcast presence update to project members
      if (presence.projectId) {
        this.io?.to(`project:${presence.projectId}`).emit('presence-updated', presence);
      }
    } catch (error) {
      logger.error('Failed to update user presence', { error, userId, updates });
    }
  }

  /**
   * Get active users in a project
   */
  public getProjectPresence(projectId: string): UserPresence[] {
    const activeUsers: UserPresence[] = [];
    
    for (const presence of this.userPresence.values()) {
      if (presence.projectId === projectId && presence.status !== 'offline') {
        activeUsers.push(presence);
      }
    }
    
    return activeUsers;
  }

  /**
   * Send mention notifications
   */
  private async sendMentionNotifications(comment: Comment): Promise<void> {
    if (!comment.mentions || comment.mentions.length === 0) return;

    try {
      for (const mentionedUserId of comment.mentions) {
        // Create notification (would need to add this method to DatabaseService)
        logger.info('Mention notification sent', {
          mentionedUserId,
          commentId: comment.id,
          authorId: comment.authorId
        });
      }
    } catch (error) {
      logger.error('Failed to send mention notifications', { error, comment });
    }
  }

  /**
   * Format comment data for API response
   */
  private formatComment(comment: any): Comment {
    return {
      id: comment.id,
      projectId: comment.project_id,
      analysisId: comment.analysis_id,
      parentCommentId: comment.parent_comment_id,
      content: comment.content,
      authorId: comment.author_id,
      authorName: `${comment.first_name} ${comment.last_name}`,
      avatarUrl: comment.avatar_url,
      status: comment.status,
      positionData: comment.position_data ? JSON.parse(comment.position_data) : undefined,
      attachments: comment.attachments ? JSON.parse(comment.attachments) : undefined,
      mentions: comment.mentions ? JSON.parse(comment.mentions) : undefined,
      createdAt: new Date(comment.created_at),
      updatedAt: new Date(comment.updated_at)
    };
  }

  /**
   * Create project version
   */
  public async createProjectVersion(versionData: any): Promise<void> {
    try {
      // Store version data in database (would need to add this method to DatabaseService)
      logger.info('Project version created', { versionId: versionData.id, projectId: versionData.projectId });
    } catch (error) {
      logger.error('Failed to create project version', { error, versionData });
      throw error;
    }
  }

  /**
   * Process synchronization operation
   */
  public async processSyncOperation(operation: any): Promise<any> {
    try {
      // Add server timestamp and validation
      const processedOperation = {
        ...operation,
        serverTimestamp: new Date().toISOString(),
        processed: true
      };

      // Store operation in database for conflict resolution (would need to add this method to DatabaseService)
      logger.info('Sync operation processed', { operationId: operation.id, projectId: operation.projectId });

      return processedOperation;
    } catch (error) {
      logger.error('Failed to process sync operation', { error, operation });
      throw error;
    }
  }
}
