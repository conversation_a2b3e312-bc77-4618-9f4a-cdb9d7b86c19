import { createModuleLogger } from '@/utils/logger';
import { GPTCacheService } from './gptCacheService';
import { SocketManager } from './socketManager';

const logger = createModuleLogger('PerformanceMetricsService');

export interface ModelPerformanceMetrics {
  modelName: 'GPT4O' | 'GPT4O_MINI' | 'GPTO1';
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  averageTokensUsed: number;
  totalCost: number;
  averageConfidence: number;
  peakUsageTime: string;
  errorRate: number;
  lastUsed: string;
}

export interface PerformanceComparison {
  timeRange: string;
  models: ModelPerformanceMetrics[];
  totalRequests: number;
  totalCost: number;
  mostEfficientModel: string;
  costOptimizationOpportunities: string[];
}

export interface AlertThreshold {
  id: string;
  type: 'RESPONSE_TIME' | 'ERROR_RATE' | 'COST_THRESHOLD' | 'USAGE_SPIKE';
  threshold: number;
  enabled: boolean;
  lastTriggered?: string;
  description: string;
}

export interface PerformanceAlert {
  id: string;
  type: AlertThreshold['type'];
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  message: string;
  currentValue: number;
  threshold: number;
  timestamp: string;
  acknowledged: boolean;
}

export interface UsagePattern {
  hour: number;
  day: string;
  requests: number;
  averageResponseTime: number;
  modelDistribution: Record<string, number>;
}

export interface CostAnalysis {
  dailyCost: number;
  weeklyCost: number;
  monthlyCost: number;
  projectedMonthlyCost: number;
  costByModel: Record<string, number>;
  costTrend: 'INCREASING' | 'DECREASING' | 'STABLE';
  optimizationSuggestions: string[];
}

/**
 * Performance Metrics Service for GPT-o1 Integration
 * Provides comprehensive analytics and monitoring for AI model performance
 */
export class PerformanceMetricsService {
  private cacheService: GPTCacheService;
  private socketManager: SocketManager | null = null;
  private metricsHistory: Map<string, any[]> = new Map();
  private alertThresholds: Map<string, AlertThreshold> = new Map();
  private activeAlerts: Map<string, PerformanceAlert> = new Map();
  private usagePatterns: UsagePattern[] = [];

  constructor(socketManager?: SocketManager) {
    this.cacheService = new GPTCacheService();
    this.socketManager = socketManager || null;
    this.initializeDefaultThresholds();
    this.startMetricsCollection();
  }

  /**
   * Initialize default alert thresholds
   */
  private initializeDefaultThresholds(): void {
    const defaultThresholds: AlertThreshold[] = [
      {
        id: 'response_time_high',
        type: 'RESPONSE_TIME',
        threshold: 30000, // 30 seconds
        enabled: true,
        description: 'Average response time exceeds 30 seconds'
      },
      {
        id: 'error_rate_high',
        type: 'ERROR_RATE',
        threshold: 0.05, // 5%
        enabled: true,
        description: 'Error rate exceeds 5%'
      },
      {
        id: 'daily_cost_high',
        type: 'COST_THRESHOLD',
        threshold: 100, // $100 per day
        enabled: true,
        description: 'Daily cost exceeds $100'
      },
      {
        id: 'usage_spike',
        type: 'USAGE_SPIKE',
        threshold: 5, // 5x normal usage
        enabled: true,
        description: 'Usage spike detected (5x normal)'
      }
    ];

    defaultThresholds.forEach(threshold => {
      this.alertThresholds.set(threshold.id, threshold);
    });

    logger.info('Default alert thresholds initialized', {
      thresholdCount: defaultThresholds.length
    });
  }

  /**
   * Start periodic metrics collection
   */
  private startMetricsCollection(): void {
    // Collect metrics every 5 minutes
    setInterval(() => {
      this.collectCurrentMetrics();
    }, 5 * 60 * 1000);

    // Check alerts every minute
    setInterval(() => {
      this.checkAlertThresholds();
    }, 60 * 1000);

    logger.info('Performance metrics collection started');
  }

  /**
   * Collect current performance metrics
   */
  private async collectCurrentMetrics(): Promise<void> {
    try {
      const timestamp = new Date().toISOString();
      const cacheMetrics = this.cacheService.getMetrics();
      
      // Store historical data
      const currentMetrics = {
        timestamp,
        cacheMetrics,
        systemMetrics: {
          memoryUsage: process.memoryUsage(),
          uptime: process.uptime(),
          cpuUsage: process.cpuUsage()
        }
      };

      // Store in history (keep last 24 hours)
      const historyKey = 'performance_metrics';
      if (!this.metricsHistory.has(historyKey)) {
        this.metricsHistory.set(historyKey, []);
      }

      const history = this.metricsHistory.get(historyKey)!;
      history.push(currentMetrics);

      // Keep only last 24 hours (288 entries at 5-minute intervals)
      if (history.length > 288) {
        history.splice(0, history.length - 288);
      }

      logger.debug('Performance metrics collected', {
        timestamp,
        historyLength: history.length
      });

    } catch (error) {
      logger.error('Failed to collect performance metrics:', error);
    }
  }

  /**
   * Check alert thresholds and trigger alerts
   */
  private async checkAlertThresholds(): Promise<void> {
    try {
      const currentMetrics = await this.getCurrentPerformanceSnapshot();
      
      for (const [thresholdId, threshold] of this.alertThresholds) {
        if (!threshold.enabled) continue;

        let currentValue: number = 0;
        let shouldAlert = false;

        switch (threshold.type) {
          case 'RESPONSE_TIME':
            currentValue = currentMetrics.averageResponseTime;
            shouldAlert = currentValue > threshold.threshold;
            break;
          case 'ERROR_RATE':
            currentValue = currentMetrics.errorRate;
            shouldAlert = currentValue > threshold.threshold;
            break;
          case 'COST_THRESHOLD':
            currentValue = currentMetrics.dailyCost;
            shouldAlert = currentValue > threshold.threshold;
            break;
          case 'USAGE_SPIKE':
            currentValue = currentMetrics.currentUsageMultiplier;
            shouldAlert = currentValue > threshold.threshold;
            break;
        }

        if (shouldAlert && !this.activeAlerts.has(thresholdId)) {
          this.triggerAlert(threshold, currentValue);
        } else if (!shouldAlert && this.activeAlerts.has(thresholdId)) {
          this.resolveAlert(thresholdId);
        }
      }

    } catch (error) {
      logger.error('Failed to check alert thresholds:', error);
    }
  }

  /**
   * Trigger a performance alert
   */
  private triggerAlert(threshold: AlertThreshold, currentValue: number): void {
    const alert: PerformanceAlert = {
      id: `alert_${threshold.id}_${Date.now()}`,
      type: threshold.type,
      severity: this.calculateAlertSeverity(threshold.type, currentValue, threshold.threshold),
      message: `${threshold.description}. Current: ${currentValue}, Threshold: ${threshold.threshold}`,
      currentValue,
      threshold: threshold.threshold,
      timestamp: new Date().toISOString(),
      acknowledged: false
    };

    this.activeAlerts.set(threshold.id, alert);

    // Update threshold last triggered time
    threshold.lastTriggered = alert.timestamp;

    logger.warn('Performance alert triggered', {
      alertId: alert.id,
      type: alert.type,
      severity: alert.severity,
      currentValue,
      threshold: threshold.threshold
    });

    // Send real-time alert notification via WebSocket
    if (this.socketManager) {
      this.socketManager.sendPerformanceAlert({
        id: alert.id,
        type: alert.type,
        severity: alert.severity,
        message: alert.message,
        currentValue: alert.currentValue,
        threshold: alert.threshold
      });
    }
  }

  /**
   * Resolve an active alert
   */
  private resolveAlert(thresholdId: string): void {
    const alert = this.activeAlerts.get(thresholdId);
    if (alert) {
      this.activeAlerts.delete(thresholdId);
      logger.info('Performance alert resolved', {
        alertId: alert.id,
        type: alert.type
      });
    }
  }

  /**
   * Calculate alert severity based on how much threshold is exceeded
   */
  private calculateAlertSeverity(
    type: AlertThreshold['type'], 
    currentValue: number, 
    threshold: number
  ): PerformanceAlert['severity'] {
    const ratio = currentValue / threshold;
    
    if (ratio >= 3) return 'CRITICAL';
    if (ratio >= 2) return 'HIGH';
    if (ratio >= 1.5) return 'MEDIUM';
    return 'LOW';
  }

  /**
   * Get current performance snapshot
   */
  private async getCurrentPerformanceSnapshot(): Promise<{
    averageResponseTime: number;
    errorRate: number;
    dailyCost: number;
    currentUsageMultiplier: number;
  }> {
    // This would be implemented with actual metrics collection
    // For now, return mock data structure
    return {
      averageResponseTime: 5000, // 5 seconds
      errorRate: 0.02, // 2%
      dailyCost: 25.50, // $25.50
      currentUsageMultiplier: 1.2 // 1.2x normal usage
    };
  }

  /**
   * Get model performance comparison
   */
  async getModelPerformanceComparison(timeRange: string = '24h'): Promise<PerformanceComparison> {
    try {
      const endTime = new Date();
      const startTime = new Date();

      // Validate and normalize time range
      const validTimeRanges = ['1h', '24h', '7d', '30d'];
      const normalizedTimeRange = validTimeRanges.includes(timeRange) ? timeRange : '24h';

      // Calculate start time based on range
      switch (normalizedTimeRange) {
        case '1h':
          startTime.setHours(endTime.getHours() - 1);
          break;
        case '24h':
          startTime.setDate(endTime.getDate() - 1);
          break;
        case '7d':
          startTime.setDate(endTime.getDate() - 7);
          break;
        case '30d':
          startTime.setDate(endTime.getDate() - 30);
          break;
        default:
          startTime.setDate(endTime.getDate() - 1);
      }

      // Mock data for demonstration - would be replaced with actual metrics
      const models: ModelPerformanceMetrics[] = [
        {
          modelName: 'GPT4O',
          totalRequests: 245,
          successfulRequests: 238,
          failedRequests: 7,
          averageResponseTime: 4200,
          averageTokensUsed: 1850,
          totalCost: 45.60,
          averageConfidence: 0.87,
          peakUsageTime: '14:30',
          errorRate: 0.029,
          lastUsed: new Date(Date.now() - 300000).toISOString() // 5 minutes ago
        },
        {
          modelName: 'GPTO1',
          totalRequests: 89,
          successfulRequests: 87,
          failedRequests: 2,
          averageResponseTime: 8500,
          averageTokensUsed: 2400,
          totalCost: 32.10,
          averageConfidence: 0.94,
          peakUsageTime: '16:15',
          errorRate: 0.022,
          lastUsed: new Date(Date.now() - 180000).toISOString() // 3 minutes ago
        },
        {
          modelName: 'GPT4O_MINI',
          totalRequests: 412,
          successfulRequests: 405,
          failedRequests: 7,
          averageResponseTime: 2100,
          averageTokensUsed: 980,
          totalCost: 8.25,
          averageConfidence: 0.82,
          peakUsageTime: '11:45',
          errorRate: 0.017,
          lastUsed: new Date(Date.now() - 120000).toISOString() // 2 minutes ago
        }
      ];

      const totalRequests = models.reduce((sum, model) => sum + model.totalRequests, 0);
      const totalCost = models.reduce((sum, model) => sum + model.totalCost, 0);

      // Determine most efficient model (best cost per successful request)
      const mostEfficientModel = models.reduce((best, current) => {
        const currentEfficiency = current.totalCost / current.successfulRequests;
        const bestEfficiency = best.totalCost / best.successfulRequests;
        return currentEfficiency < bestEfficiency ? current : best;
      }).modelName;

      const costOptimizationOpportunities = this.generateCostOptimizationSuggestions(models);

      return {
        timeRange: normalizedTimeRange,
        models,
        totalRequests,
        totalCost,
        mostEfficientModel,
        costOptimizationOpportunities
      };

    } catch (error) {
      logger.error('Failed to get model performance comparison:', error);
      throw error;
    }
  }

  /**
   * Generate cost optimization suggestions
   */
  private generateCostOptimizationSuggestions(models: ModelPerformanceMetrics[]): string[] {
    const suggestions: string[] = [];

    // Find high-cost, low-confidence scenarios
    models.forEach(model => {
      const costPerRequest = model.totalCost / model.totalRequests;

      if (model.averageConfidence < 0.85 && costPerRequest > 0.15) {
        suggestions.push(`Consider using ${model.modelName === 'GPTO1' ? 'GPT-4o' : 'GPT-4o-mini'} for lower confidence tasks to reduce costs`);
      }

      if (model.errorRate > 0.03) {
        suggestions.push(`High error rate for ${model.modelName} - review prompt optimization to reduce retries`);
      }

      if (model.averageResponseTime > 10000) {
        suggestions.push(`${model.modelName} response times are high - consider caching or model switching for time-sensitive requests`);
      }
    });

    // Cache hit rate optimization
    const cacheMetrics = this.cacheService.getMetrics();
    if (cacheMetrics.hitRate < 0.6) {
      suggestions.push('Cache hit rate is below 60% - review caching strategy and similarity thresholds');
    }

    return suggestions;
  }

  /**
   * Get usage patterns analysis
   */
  async getUsagePatterns(days: number = 7): Promise<UsagePattern[]> {
    try {
      // Mock data for demonstration - would be replaced with actual historical data
      const patterns: UsagePattern[] = [];
      const now = new Date();

      for (let day = 0; day < days; day++) {
        const date = new Date(now);
        date.setDate(date.getDate() - day);

        for (let hour = 0; hour < 24; hour++) {
          // Simulate realistic usage patterns (higher during business hours)
          const baseRequests = hour >= 9 && hour <= 17 ? 15 : 5;
          const randomVariation = Math.random() * 10;
          const requests = Math.floor(baseRequests + randomVariation);

          patterns.push({
            hour,
            day: date.toISOString().split('T')[0],
            requests,
            averageResponseTime: 3000 + Math.random() * 2000,
            modelDistribution: {
              'GPT4O': Math.random() * 0.4 + 0.3, // 30-70%
              'GPTO1': Math.random() * 0.2 + 0.1, // 10-30%
              'GPT4O_MINI': Math.random() * 0.4 + 0.2 // 20-60%
            }
          });
        }
      }

      return patterns.reverse(); // Most recent first

    } catch (error) {
      logger.error('Failed to get usage patterns:', error);
      throw error;
    }
  }

  /**
   * Get cost analysis
   */
  async getCostAnalysis(): Promise<CostAnalysis> {
    try {
      const comparison = await this.getModelPerformanceComparison('30d');
      const dailyAverage = comparison.totalCost / 30;

      // Mock trend calculation - would use actual historical data
      const costTrend: CostAnalysis['costTrend'] = 'STABLE';

      const costByModel = comparison.models.reduce((acc, model) => {
        acc[model.modelName] = model.totalCost;
        return acc;
      }, {} as Record<string, number>);

      const optimizationSuggestions = this.generateCostOptimizationSuggestions(comparison.models);

      return {
        dailyCost: dailyAverage,
        weeklyCost: dailyAverage * 7,
        monthlyCost: comparison.totalCost,
        projectedMonthlyCost: dailyAverage * 30,
        costByModel,
        costTrend,
        optimizationSuggestions
      };

    } catch (error) {
      logger.error('Failed to get cost analysis:', error);
      throw error;
    }
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): PerformanceAlert[] {
    return Array.from(this.activeAlerts.values());
  }

  /**
   * Acknowledge an alert
   */
  acknowledgeAlert(alertId: string): boolean {
    for (const alert of this.activeAlerts.values()) {
      if (alert.id === alertId) {
        alert.acknowledged = true;
        logger.info('Alert acknowledged', { alertId });
        return true;
      }
    }
    return false;
  }

  /**
   * Update alert threshold
   */
  updateAlertThreshold(thresholdId: string, updates: Partial<AlertThreshold>): boolean {
    const threshold = this.alertThresholds.get(thresholdId);
    if (threshold) {
      Object.assign(threshold, updates);
      logger.info('Alert threshold updated', { thresholdId, updates });
      return true;
    }
    return false;
  }

  /**
   * Get alert thresholds
   */
  getAlertThresholds(): AlertThreshold[] {
    return Array.from(this.alertThresholds.values());
  }

  /**
   * Export metrics data
   */
  async exportMetricsData(format: 'CSV' | 'JSON', timeRange: string = '24h'): Promise<string> {
    try {
      const comparison = await this.getModelPerformanceComparison(timeRange);
      const usagePatterns = await this.getUsagePatterns(7);
      const costAnalysis = await this.getCostAnalysis();

      const exportData = {
        timestamp: new Date().toISOString(),
        timeRange,
        modelComparison: comparison,
        usagePatterns,
        costAnalysis,
        activeAlerts: this.getActiveAlerts()
      };

      if (format === 'JSON') {
        return JSON.stringify(exportData, null, 2);
      } else {
        // Convert to CSV format
        return this.convertToCSV(exportData);
      }

    } catch (error) {
      logger.error('Failed to export metrics data:', error);
      throw error;
    }
  }

  /**
   * Convert data to CSV format
   */
  private convertToCSV(data: any): string {
    const csvLines: string[] = [];

    // Model performance CSV
    csvLines.push('Model Performance');
    csvLines.push('Model,Total Requests,Successful,Failed,Avg Response Time,Avg Tokens,Total Cost,Avg Confidence,Error Rate');

    data.modelComparison.models.forEach((model: ModelPerformanceMetrics) => {
      csvLines.push([
        model.modelName,
        model.totalRequests,
        model.successfulRequests,
        model.failedRequests,
        model.averageResponseTime,
        model.averageTokensUsed,
        model.totalCost.toFixed(2),
        model.averageConfidence.toFixed(3),
        model.errorRate.toFixed(3)
      ].join(','));
    });

    csvLines.push(''); // Empty line separator

    // Cost analysis CSV
    csvLines.push('Cost Analysis');
    csvLines.push('Metric,Value');
    csvLines.push(`Daily Cost,${data.costAnalysis.dailyCost.toFixed(2)}`);
    csvLines.push(`Weekly Cost,${data.costAnalysis.weeklyCost.toFixed(2)}`);
    csvLines.push(`Monthly Cost,${data.costAnalysis.monthlyCost.toFixed(2)}`);
    csvLines.push(`Projected Monthly,${data.costAnalysis.projectedMonthlyCost.toFixed(2)}`);

    return csvLines.join('\n');
  }

  /**
   * Record model usage and send real-time updates
   */
  recordModelUsage(modelName: string, responseTime: number, tokensUsed: number, cost: number, success: boolean): void {
    try {
      // This would integrate with actual metrics storage
      // For now, just send real-time updates

      if (this.socketManager) {
        this.socketManager.sendModelPerformanceUpdate({
          modelName,
          totalRequests: 1, // Would be actual cumulative count
          averageResponseTime: responseTime,
          totalCost: cost,
          errorRate: success ? 0 : 1,
          lastUsed: new Date().toISOString()
        });
      }

      logger.debug('Model usage recorded', {
        modelName,
        responseTime,
        tokensUsed,
        cost,
        success
      });

    } catch (error) {
      logger.error('Failed to record model usage:', error);
    }
  }

  /**
   * Send real-time cost analysis update
   */
  sendCostUpdate(): void {
    if (!this.socketManager) return;

    this.getCostAnalysis().then(costAnalysis => {
      this.socketManager!.sendCostAnalysisUpdate({
        dailyCost: costAnalysis.dailyCost,
        weeklyCost: costAnalysis.weeklyCost,
        monthlyCost: costAnalysis.monthlyCost,
        costTrend: costAnalysis.costTrend,
        optimizationSuggestions: costAnalysis.optimizationSuggestions
      });
    }).catch(error => {
      logger.error('Failed to send cost update:', error);
    });
  }

  /**
   * Send real-time cache metrics update
   */
  sendCacheUpdate(): void {
    if (!this.socketManager) return;

    try {
      const cacheMetrics = this.cacheService.getMetrics();

      this.socketManager.sendCacheMetricsUpdate({
        hitRate: cacheMetrics.hitRate,
        totalRequests: cacheMetrics.totalRequests,
        costSavings: cacheMetrics.costSavings.estimatedCostSaved,
        memoryUsage: cacheMetrics.storageStats.memoryUsage
      });

    } catch (error) {
      logger.error('Failed to send cache update:', error);
    }
  }

  /**
   * Get performance overview data
   */
  async getPerformanceOverview(timeRange: string = '24h'): Promise<any> {
    try {
      const [
        modelComparison,
        usagePatterns,
        costAnalysis,
        activeAlerts
      ] = await Promise.all([
        this.getModelPerformanceComparison(timeRange),
        this.getUsagePatterns(7),
        this.getCostAnalysis(),
        this.getActiveAlerts()
      ]);

      return {
        modelComparison,
        usagePatterns,
        costAnalysis,
        activeAlerts,
        summary: {
          totalRequests: modelComparison.totalRequests,
          totalCost: modelComparison.totalCost,
          mostEfficientModel: modelComparison.mostEfficientModel,
          alertCount: activeAlerts.length,
          criticalAlerts: activeAlerts.filter(alert => alert.severity === 'CRITICAL').length
        }
      };
    } catch (error) {
      logger.error('Failed to get performance overview:', error);
      throw error;
    }
  }

  /**
   * Get comprehensive dashboard metrics for GPT-o1 Integration Enhancement
   */
  async getDashboardMetrics(timeRange: string = '24h'): Promise<{
    overview: any;
    gptO1Analytics: any;
    cachingEfficiency: any;
    modelComparison: any;
    realTimeMonitoring: any;
  }> {
    try {
      const [
        overview,
        modelComparison,
        usagePatterns,
        costAnalysis,
        activeAlerts
      ] = await Promise.all([
        this.getPerformanceOverview(timeRange),
        this.getModelPerformanceComparison(timeRange),
        this.getUsagePatterns(7),
        this.getCostAnalysis(),
        this.getActiveAlerts()
      ]);

      // GPT-o1 specific analytics
      const gptO1Analytics = await this.getGPTO1Analytics(timeRange);

      // Caching efficiency metrics
      const cachingEfficiency = await this.getCachingEfficiencyMetrics();

      // Real-time monitoring data
      const realTimeMonitoring = await this.getRealTimeMonitoringData();

      return {
        overview: {
          ...overview,
          usagePatterns,
          activeAlerts
        },
        gptO1Analytics,
        cachingEfficiency,
        modelComparison: {
          ...modelComparison,
          costAnalysis
        },
        realTimeMonitoring
      };
    } catch (error) {
      logger.error('Failed to get dashboard metrics:', error);
      throw error;
    }
  }

  /**
   * Get GPT-o1 specific analytics with reasoning chain performance
   */
  async getGPTO1Analytics(timeRange: string): Promise<{
    reasoningChainPerformance: any;
    complexityAnalysis: any;
    tokenUsageOptimization: any;
    accuracyMetrics: any;
  }> {
    try {
      // Mock data for now - in production this would query actual metrics
      const reasoningChainPerformance = {
        averageStepsPerChain: 8.5,
        averageStepCompletionTime: 2.3, // seconds
        complexityDistribution: {
          low: 25,
          medium: 45,
          high: 30
        },
        stepSuccessRate: 0.94,
        chainCompletionRate: 0.91,
        recentChains: [
          {
            id: 'chain_001',
            analysisType: '3d_spatial_reconstruction',
            steps: 12,
            completionTime: 28.5,
            complexity: 'high',
            accuracy: 0.92
          },
          {
            id: 'chain_002',
            analysisType: 'layout_optimization',
            steps: 6,
            completionTime: 14.2,
            complexity: 'medium',
            accuracy: 0.89
          }
        ]
      };

      const complexityAnalysis = {
        averageComplexityScore: 0.72,
        complexityTrends: this.generateTimeSeriesData(timeRange, 'complexity'),
        factorsInfluencingComplexity: [
          { factor: 'Image Resolution', impact: 0.35 },
          { factor: 'Analysis Depth', impact: 0.28 },
          { factor: 'Multi-feature Analysis', impact: 0.22 },
          { factor: 'Spatial Complexity', impact: 0.15 }
        ]
      };

      const tokenUsageOptimization = {
        averageTokensPerAnalysis: 4250,
        tokenEfficiencyScore: 0.87,
        costPerAnalysis: 0.064, // USD
        optimizationRecommendations: [
          'Consider prompt optimization for 15% token reduction',
          'Use semantic caching for similar analyses',
          'Implement progressive analysis for complex scenarios'
        ]
      };

      const accuracyMetrics = {
        overallAccuracy: 0.91,
        accuracyByComplexity: {
          low: 0.95,
          medium: 0.92,
          high: 0.88
        },
        confidenceDistribution: this.generateConfidenceDistribution(),
        accuracyTrends: this.generateTimeSeriesData(timeRange, 'accuracy')
      };

      return {
        reasoningChainPerformance,
        complexityAnalysis,
        tokenUsageOptimization,
        accuracyMetrics
      };
    } catch (error) {
      logger.error('Failed to get GPT-o1 analytics:', error);
      throw error;
    }
  }

  /**
   * Get caching efficiency metrics with semantic similarity data
   */
  async getCachingEfficiencyMetrics(): Promise<{
    semanticSimilarity: any;
    apiCallReduction: any;
    cacheWarming: any;
    costSavings: any;
  }> {
    try {
      const cacheMetrics = this.cacheService.getMetrics();

      const semanticSimilarity = {
        hitRate: cacheMetrics.hitRate,
        semanticHitRate: cacheMetrics.semanticHits / Math.max(cacheMetrics.cacheHits, 1),
        exactHitRate: cacheMetrics.exactHits / Math.max(cacheMetrics.cacheHits, 1),
        averageSimilarityScore: cacheMetrics.semanticMetrics.averageSimilarityScore,
        embeddingGenerationTime: cacheMetrics.semanticMetrics.embeddingGenerationTime,
        similarityCalculationTime: cacheMetrics.semanticMetrics.similarityCalculationTime,
        similarityDistribution: this.generateSimilarityDistribution()
      };

      const apiCallReduction = {
        totalRequests: cacheMetrics.totalRequests,
        cacheHits: cacheMetrics.cacheHits,
        reductionPercentage: Math.round((cacheMetrics.cacheHits / Math.max(cacheMetrics.totalRequests, 1)) * 100),
        targetReduction: 70, // 60-80% target
        trend: this.generateTimeSeriesData('7d', 'cache_reduction'),
        projectedMonthlySavings: cacheMetrics.costSavings.estimatedCostSaved * 30
      };

      const cacheWarming = {
        warmupEffectiveness: 0.78,
        preGeneratedEmbeddings: 45,
        warmupPatterns: ['kitchen_analysis', 'cabinet_count', 'material_recognition'],
        lastWarmupTime: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
        nextScheduledWarmup: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString() // 18 hours from now
      };

      const costSavings = {
        totalSaved: cacheMetrics.costSavings.estimatedCostSaved,
        tokensAvoided: cacheMetrics.costSavings.tokensAvoided,
        monthlySavingsProjection: cacheMetrics.costSavings.estimatedCostSaved * 30,
        savingsBreakdown: {
          'GPT-o1': cacheMetrics.costSavings.estimatedCostSaved * 0.6,
          'GPT-4o': cacheMetrics.costSavings.estimatedCostSaved * 0.3,
          'GPT-4o-mini': cacheMetrics.costSavings.estimatedCostSaved * 0.1
        }
      };

      return {
        semanticSimilarity,
        apiCallReduction,
        cacheWarming,
        costSavings
      };
    } catch (error) {
      logger.error('Failed to get caching efficiency metrics:', error);
      throw error;
    }
  }

  /**
   * Get real-time monitoring data for all AI endpoints
   */
  async getRealTimeMonitoringData(): Promise<{
    systemHealth: any;
    endpointStatus: any;
    performanceAlerts: any;
    liveMetrics: any;
  }> {
    try {
      const systemHealth = {
        overallStatus: 'operational',
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        cpuUsage: 0.45, // Mock data
        activeConnections: this.socketManager?.getConnectedClientsCount() || 0,
        lastHealthCheck: new Date().toISOString()
      };

      const endpointStatus = {
        'Blackveil.openai.azure.com': {
          status: 'operational',
          responseTime: 1250,
          successRate: 0.98,
          lastCheck: new Date().toISOString(),
          models: ['GPT-4o', 'GPT-o1']
        },
        'ai-opsec9314ai985446969955.openai.azure.com': {
          status: 'operational',
          responseTime: 890,
          successRate: 0.99,
          lastCheck: new Date().toISOString(),
          models: ['GPT-4o-mini']
        }
      };

      const performanceAlerts = Array.from(this.activeAlerts.values()).map(alert => ({
        id: alert.id,
        type: alert.type,
        severity: alert.severity,
        message: alert.message,
        threshold: alert.threshold,
        currentValue: alert.currentValue,
        timestamp: alert.timestamp
      }));

      const liveMetrics = {
        requestsPerMinute: 12,
        averageResponseTime: 2.1,
        errorRate: 0.02,
        cacheHitRate: 0.73,
        costPerHour: 0.45,
        timestamp: new Date().toISOString()
      };

      return {
        systemHealth,
        endpointStatus,
        performanceAlerts,
        liveMetrics
      };
    } catch (error) {
      logger.error('Failed to get real-time monitoring data:', error);
      throw error;
    }
  }

  /**
   * Generate confidence distribution data
   */
  private generateConfidenceDistribution(): Array<{ range: string; count: number }> {
    return [
      { range: '0.9-1.0', count: 45 },
      { range: '0.8-0.9', count: 32 },
      { range: '0.7-0.8', count: 18 },
      { range: '0.6-0.7', count: 8 },
      { range: '0.5-0.6', count: 3 }
    ];
  }

  /**
   * Generate similarity distribution data
   */
  private generateSimilarityDistribution(): Array<{ range: string; count: number }> {
    return [
      { range: '0.95-1.0', count: 28 },
      { range: '0.90-0.95', count: 35 },
      { range: '0.85-0.90', count: 22 },
      { range: '0.80-0.85', count: 12 },
      { range: '0.75-0.80', count: 6 }
    ];
  }

  /**
   * Generate time series data for charts
   */
  private generateTimeSeriesData(timeRange: string, metric: string): Array<{ time: string; value: number }> {
    const now = new Date();
    const data: Array<{ time: string; value: number }> = [];

    let points = 24; // Default for 24h
    let interval = 60 * 60 * 1000; // 1 hour in milliseconds

    switch (timeRange) {
      case '1h':
        points = 12;
        interval = 5 * 60 * 1000; // 5 minutes
        break;
      case '7d':
        points = 7;
        interval = 24 * 60 * 60 * 1000; // 1 day
        break;
      case '30d':
        points = 30;
        interval = 24 * 60 * 60 * 1000; // 1 day
        break;
    }

    for (let i = points - 1; i >= 0; i--) {
      const time = new Date(now.getTime() - (i * interval));
      let value = 0;

      // Generate realistic mock data based on metric type
      switch (metric) {
        case 'complexity':
          value = 0.6 + (Math.random() * 0.3); // 0.6-0.9 range
          break;
        case 'accuracy':
          value = 0.85 + (Math.random() * 0.1); // 0.85-0.95 range
          break;
        case 'cache_reduction':
          value = 60 + (Math.random() * 20); // 60-80% range
          break;
        default:
          value = Math.random();
      }

      data.push({
        time: time.toISOString(),
        value: Math.round(value * 100) / 100
      });
    }

    return data;
  }

  /**
   * Get alert history for specified number of days
   */
  async getAlertHistory(days: number): Promise<Array<any>> {
    try {
      // Mock data for now - in production this would query actual alert history
      return [
        {
          id: 'alert_001',
          type: 'response_time',
          severity: 'warning',
          message: 'Response time exceeded threshold',
          threshold: 5000,
          currentValue: 5200,
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
          acknowledged: true,
          resolvedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'alert_002',
          type: 'error_rate',
          severity: 'high',
          message: 'Error rate spike detected',
          threshold: 0.05,
          currentValue: 0.08,
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
          acknowledged: true,
          resolvedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
        }
      ];
    } catch (error) {
      logger.error('Failed to get alert history:', error);
      return [];
    }
  }

  /**
   * Export metrics data in specified format
   */
  async exportMetricsData(format: 'CSV' | 'JSON', timeRange: string): Promise<string> {
    try {
      const overview = await this.getPerformanceOverview(timeRange);

      if (format === 'CSV') {
        // Convert to CSV format
        const headers = ['Metric', 'Value', 'Timestamp'];
        const rows = [
          ['Total Requests', overview.summary.totalRequests.toString(), new Date().toISOString()],
          ['Total Cost', overview.summary.totalCost.toString(), new Date().toISOString()],
          ['Most Efficient Model', overview.summary.mostEfficientModel, new Date().toISOString()],
          ['Alert Count', overview.summary.alertCount.toString(), new Date().toISOString()]
        ];

        return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
      } else {
        // Return JSON format
        return JSON.stringify({
          exportedAt: new Date().toISOString(),
          timeRange,
          format,
          data: overview
        }, null, 2);
      }
    } catch (error) {
      logger.error('Failed to export metrics data:', error);
      throw error;
    }
  }

  /**
   * Set socket manager for real-time updates
   */
  setSocketManager(socketManager: SocketManager): void {
    this.socketManager = socketManager;
    logger.info('Socket manager set for performance metrics service');
  }
}
