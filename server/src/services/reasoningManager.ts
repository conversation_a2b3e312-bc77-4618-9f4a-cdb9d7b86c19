import { createModuleLogger } from '@/utils/logger';

const logger = createModuleLogger('ReasoningManager');

export interface ReasoningStep {
  id: string;
  type: 'observation' | 'analysis' | 'inference' | 'validation' | 'conclusion';
  description: string;
  evidence: string[];
  confidence: number;
  dependencies: string[]; // IDs of prerequisite steps
  timestamp: Date;
  // Enhanced for GPT-o1 reasoning visualization
  gptO1Reasoning?: {
    internalThoughts: string[];
    reasoningPath: string[];
    confidenceFactors: string[];
    alternativesConsidered: string[];
    complexityScore: number; // 0-1 scale
  };
  visualizationData?: {
    position: { x: number; y: number };
    status: 'pending' | 'processing' | 'completed' | 'failed';
    processingTime?: number;
    complexity: 'low' | 'medium' | 'high';
    parentSteps: string[]; // For tree visualization
    childSteps: string[];
  };
}

export interface ReasoningChain {
  id: string;
  analysisId: string;
  goal: string;
  steps: ReasoningStep[];
  currentStep: number;
  status: 'in_progress' | 'completed' | 'failed';
  metadata: {
    startTime: Date;
    endTime?: Date;
    totalSteps: number;
    completedSteps: number;
  };
}

export interface ReasoningContext {
  analysisType: string;
  inputData: any;
  previousFindings?: any;
  constraints: string[];
  objectives: string[];
  qualityThresholds: {
    minConfidence: number;
    maxUncertainty: number;
    [key: string]: number; // Allow additional threshold properties
  };
}

export interface ReasoningResult {
  chainId: string;
  conclusion: string;
  confidence: number;
  supportingEvidence: string[];
  uncertainties: string[];
  recommendations: string[];
  qualityScore: number;
}

/**
 * Advanced Reasoning Manager for AI Analysis
 * Based on patterns from archived A.One Kitchen projects (a1000_2)
 * Implements structured reasoning chains for complex analysis tasks
 * Enhanced with GPT-o1 reasoning visualization capabilities
 */
export class ReasoningManager {
  private activeChains: Map<string, ReasoningChain> = new Map();
  private reasoningTemplates: Map<string, ReasoningStep[]> = new Map();
  private socketManager?: any; // WebSocket manager for real-time updates

  constructor(socketManager?: any) {
    this.socketManager = socketManager;
    this.initializeReasoningTemplates();
  }

  /**
   * Set WebSocket manager for real-time reasoning updates
   */
  setSocketManager(socketManager: any): void {
    this.socketManager = socketManager;
  }

  /**
   * Initialize reasoning templates for different analysis types
   */
  private initializeReasoningTemplates(): void {
    // Kitchen Analysis Reasoning Template
    const kitchenAnalysisTemplate: ReasoningStep[] = [
      {
        id: 'initial_observation',
        type: 'observation',
        description: 'Observe overall kitchen layout and identify major components',
        evidence: [],
        confidence: 0.8,
        dependencies: [],
        timestamp: new Date()
      },
      {
        id: 'cabinet_identification',
        type: 'analysis',
        description: 'Systematically identify and categorize all cabinet units',
        evidence: [],
        confidence: 0.9,
        dependencies: ['initial_observation'],
        timestamp: new Date()
      },
      {
        id: 'measurement_analysis',
        type: 'analysis',
        description: 'Extract and validate dimensional measurements',
        evidence: [],
        confidence: 0.7,
        dependencies: ['cabinet_identification'],
        timestamp: new Date()
      },
      {
        id: 'hardware_inference',
        type: 'inference',
        description: 'Infer hardware requirements based on cabinet configuration',
        evidence: [],
        confidence: 0.8,
        dependencies: ['cabinet_identification'],
        timestamp: new Date()
      },
      {
        id: 'material_analysis',
        type: 'analysis',
        description: 'Identify materials, finishes, and color schemes',
        evidence: [],
        confidence: 0.6,
        dependencies: ['initial_observation'],
        timestamp: new Date()
      },
      {
        id: 'cross_validation',
        type: 'validation',
        description: 'Cross-validate findings for consistency and accuracy',
        evidence: [],
        confidence: 0.9,
        dependencies: ['cabinet_identification', 'measurement_analysis', 'hardware_inference'],
        timestamp: new Date()
      },
      {
        id: 'final_conclusion',
        type: 'conclusion',
        description: 'Synthesize findings into comprehensive analysis result',
        evidence: [],
        confidence: 0.85,
        dependencies: ['cross_validation', 'material_analysis'],
        timestamp: new Date()
      }
    ];

    this.reasoningTemplates.set('kitchen_analysis', kitchenAnalysisTemplate);

    // 3D Spatial Reconstruction Reasoning Template
    const spatialReconstructionTemplate: ReasoningStep[] = [
      {
        id: 'depth_perception_analysis',
        type: 'observation',
        description: 'Analyze visual depth cues including perspective, shadows, and occlusion patterns',
        evidence: [],
        confidence: 0.8,
        dependencies: [],
        timestamp: new Date()
      },
      {
        id: 'perspective_mapping',
        type: 'analysis',
        description: 'Identify vanishing points and perspective lines for spatial coordinate system',
        evidence: [],
        confidence: 0.85,
        dependencies: ['depth_perception_analysis'],
        timestamp: new Date()
      },
      {
        id: 'cabinet_positioning',
        type: 'inference',
        description: 'Calculate 3D coordinates and spatial positions for each identified cabinet',
        evidence: [],
        confidence: 0.8,
        dependencies: ['perspective_mapping'],
        timestamp: new Date()
      },
      {
        id: 'dimensional_estimation',
        type: 'analysis',
        description: 'Estimate cabinet dimensions using scale references and perspective correction',
        evidence: [],
        confidence: 0.75,
        dependencies: ['cabinet_positioning'],
        timestamp: new Date()
      },
      {
        id: 'spatial_relationship_mapping',
        type: 'inference',
        description: 'Determine adjacency, alignment, and spatial relationships between cabinets',
        evidence: [],
        confidence: 0.8,
        dependencies: ['cabinet_positioning', 'dimensional_estimation'],
        timestamp: new Date()
      },
      {
        id: 'room_boundary_estimation',
        type: 'analysis',
        description: 'Estimate room dimensions and boundaries based on cabinet layout and perspective',
        evidence: [],
        confidence: 0.7,
        dependencies: ['spatial_relationship_mapping'],
        timestamp: new Date()
      },
      {
        id: 'reconstruction_validation',
        type: 'validation',
        description: 'Validate 3D reconstruction for geometric consistency and realistic proportions',
        evidence: [],
        confidence: 0.85,
        dependencies: ['room_boundary_estimation'],
        timestamp: new Date()
      },
      {
        id: 'spatial_accuracy_assessment',
        type: 'conclusion',
        description: 'Assess overall spatial accuracy and confidence in 3D reconstruction results',
        evidence: [],
        confidence: 0.8,
        dependencies: ['reconstruction_validation'],
        timestamp: new Date()
      }
    ];

    this.reasoningTemplates.set('3d_spatial_reconstruction', spatialReconstructionTemplate);

    // Material Focus Reasoning Template
    const materialFocusTemplate: ReasoningStep[] = [
      {
        id: 'surface_observation',
        type: 'observation',
        description: 'Observe surface textures, patterns, and visual characteristics',
        evidence: [],
        confidence: 0.7,
        dependencies: [],
        timestamp: new Date()
      },
      {
        id: 'material_classification',
        type: 'analysis',
        description: 'Classify materials based on visual and contextual cues',
        evidence: [],
        confidence: 0.8,
        dependencies: ['surface_observation'],
        timestamp: new Date()
      },
      {
        id: 'finish_identification',
        type: 'analysis',
        description: 'Identify finish types and application methods',
        evidence: [],
        confidence: 0.7,
        dependencies: ['material_classification'],
        timestamp: new Date()
      },
      {
        id: 'quality_assessment',
        type: 'inference',
        description: 'Assess material quality and grade based on visual indicators',
        evidence: [],
        confidence: 0.6,
        dependencies: ['material_classification', 'finish_identification'],
        timestamp: new Date()
      },
      {
        id: 'material_conclusion',
        type: 'conclusion',
        description: 'Provide comprehensive material specification',
        evidence: [],
        confidence: 0.75,
        dependencies: ['quality_assessment'],
        timestamp: new Date()
      }
    ];

    this.reasoningTemplates.set('material_focus', materialFocusTemplate);

    // Material analysis template (for test compatibility)
    const materialAnalysisTemplate: ReasoningStep[] = [
      {
        id: 'material_identification',
        type: 'observation',
        description: 'Identify material types and characteristics',
        evidence: [],
        confidence: 0.8,
        dependencies: [],
        timestamp: new Date()
      },
      {
        id: 'material_properties',
        type: 'analysis',
        description: 'Analyze material properties and quality indicators',
        evidence: [],
        confidence: 0.7,
        dependencies: ['material_identification'],
        timestamp: new Date()
      },
      {
        id: 'material_specification',
        type: 'conclusion',
        description: 'Generate detailed material specifications',
        evidence: [],
        confidence: 0.8,
        dependencies: ['material_properties'],
        timestamp: new Date()
      }
    ];

    this.reasoningTemplates.set('material_analysis', materialAnalysisTemplate);

    // Hardware analysis template (for test compatibility)
    const hardwareAnalysisTemplate: ReasoningStep[] = [
      {
        id: 'hardware_identification',
        type: 'observation',
        description: 'Identify hardware types and configurations',
        evidence: [],
        confidence: 0.8,
        dependencies: [],
        timestamp: new Date()
      },
      {
        id: 'hardware_specifications',
        type: 'analysis',
        description: 'Analyze hardware specifications and requirements',
        evidence: [],
        confidence: 0.7,
        dependencies: ['hardware_identification'],
        timestamp: new Date()
      },
      {
        id: 'hardware_recommendations',
        type: 'conclusion',
        description: 'Provide hardware recommendations and specifications',
        evidence: [],
        confidence: 0.8,
        dependencies: ['hardware_specifications'],
        timestamp: new Date()
      }
    ];

    this.reasoningTemplates.set('hardware_analysis', hardwareAnalysisTemplate);

    logger.info('Initialized reasoning templates', {
      templateCount: this.reasoningTemplates.size,
      templates: Array.from(this.reasoningTemplates.keys())
    });
  }

  /**
   * Start a new reasoning chain
   */
  startReasoningChain(
    analysisId: string,
    context: ReasoningContext
  ): string {
    const chainId = `chain_${analysisId}_${Date.now()}`;
    
    // Get appropriate template
    const template = this.getReasoningTemplate(context.analysisType);
    
    const chain: ReasoningChain = {
      id: chainId,
      analysisId,
      goal: this.generateGoalStatement(context),
      steps: template.map(step => ({ ...step, timestamp: new Date() })),
      currentStep: 0,
      status: 'in_progress',
      metadata: {
        startTime: new Date(),
        totalSteps: template.length,
        completedSteps: 0
      }
    };

    this.activeChains.set(chainId, chain);
    
    logger.info(`Started reasoning chain: ${chainId}`, {
      analysisId,
      analysisType: context.analysisType,
      totalSteps: chain.steps.length
    });

    return chainId;
  }

  /**
   * Execute the next step in a reasoning chain
   */
  executeNextStep(
    chainId: string,
    stepResult: {
      evidence: string[];
      confidence: number;
      observations: string[];
    }
  ): { completed: boolean; nextStep?: ReasoningStep; result?: ReasoningResult } {
    const chain = this.activeChains.get(chainId);
    if (!chain) {
      throw new Error(`Reasoning chain not found: ${chainId}`);
    }

    if (chain.status !== 'in_progress') {
      throw new Error(`Reasoning chain is not active: ${chainId}`);
    }

    // Update current step with results
    const currentStep = chain.steps[chain.currentStep];
    currentStep.evidence = stepResult.evidence;
    currentStep.confidence = stepResult.confidence;
    currentStep.timestamp = new Date();

    // Mark step as completed
    chain.metadata.completedSteps++;

    logger.debug(`Completed reasoning step: ${currentStep.id}`, {
      chainId,
      stepType: currentStep.type,
      confidence: currentStep.confidence,
      evidenceCount: currentStep.evidence.length
    });

    // Check if all dependencies for next steps are satisfied
    const nextStepIndex = this.findNextExecutableStep(chain);
    
    if (nextStepIndex === -1) {
      // All steps completed
      chain.status = 'completed';
      chain.metadata.endTime = new Date();
      
      const result = this.generateFinalResult(chain);
      
      logger.info(`Reasoning chain completed: ${chainId}`, {
        totalSteps: chain.metadata.totalSteps,
        finalConfidence: result.confidence,
        qualityScore: result.qualityScore
      });

      return { completed: true, result };
    }

    // Move to next step
    chain.currentStep = nextStepIndex;
    const nextStep = chain.steps[nextStepIndex];

    return { completed: false, nextStep };
  }

  /**
   * Find the next executable step based on dependencies
   */
  private findNextExecutableStep(chain: ReasoningChain): number {
    for (let i = 0; i < chain.steps.length; i++) {
      const step = chain.steps[i];
      
      // Skip if already completed (has evidence)
      if (step.evidence.length > 0) continue;
      
      // Check if all dependencies are satisfied
      const dependenciesSatisfied = step.dependencies.every(depId => {
        const depStep = chain.steps.find(s => s.id === depId);
        return depStep && depStep.evidence.length > 0;
      });

      if (dependenciesSatisfied) {
        return i;
      }
    }

    return -1; // No executable steps found
  }

  /**
   * Generate final reasoning result
   */
  private generateFinalResult(chain: ReasoningChain): ReasoningResult {
    const conclusionSteps = chain.steps.filter(step => step.type === 'conclusion');
    const allEvidence = chain.steps.flatMap(step => step.evidence);
    
    // Calculate overall confidence as weighted average
    const totalConfidence = chain.steps.reduce((sum, step, index) => {
      const weight = this.getStepWeight(step.type);
      return sum + (step.confidence * weight);
    }, 0);
    
    const totalWeight = chain.steps.reduce((sum, step) => sum + this.getStepWeight(step.type), 0);
    const overallConfidence = totalConfidence / totalWeight;

    // Identify uncertainties (low confidence steps)
    const uncertainties = chain.steps
      .filter(step => step.confidence < 0.7)
      .map(step => `${step.description} (confidence: ${(step.confidence * 100).toFixed(1)}%)`);

    // Generate recommendations based on uncertainties and analysis
    const recommendations = this.generateRecommendations(chain, uncertainties);

    // Calculate quality score
    const qualityScore = this.calculateQualityScore(chain, overallConfidence);

    // Generate conclusion from final steps
    const conclusion = conclusionSteps.length > 0 
      ? conclusionSteps.map(step => step.description).join('. ')
      : 'Analysis completed with structured reasoning approach.';

    return {
      chainId: chain.id,
      conclusion,
      confidence: overallConfidence,
      supportingEvidence: allEvidence,
      uncertainties,
      recommendations,
      qualityScore
    };
  }

  /**
   * Get weight for different step types
   */
  private getStepWeight(stepType: string): number {
    switch (stepType) {
      case 'observation': return 0.8;
      case 'analysis': return 1.0;
      case 'inference': return 0.9;
      case 'validation': return 1.2;
      case 'conclusion': return 1.1;
      default: return 1.0;
    }
  }

  /**
   * Generate recommendations based on reasoning chain analysis
   */
  private generateRecommendations(chain: ReasoningChain, uncertainties: string[]): string[] {
    const recommendations: string[] = [];

    // Recommendations based on uncertainties
    if (uncertainties.length > 0) {
      recommendations.push('Consider additional verification for low-confidence findings');
      
      if (uncertainties.some(u => u.includes('measurement'))) {
        recommendations.push('Verify measurements with additional reference points or scale indicators');
      }
      
      if (uncertainties.some(u => u.includes('material'))) {
        recommendations.push('Request material specifications or additional close-up images for accurate identification');
      }
    }

    // Recommendations based on step completion
    const validationSteps = chain.steps.filter(step => step.type === 'validation');
    if (validationSteps.length > 0 && validationSteps.some(step => step.confidence < 0.8)) {
      recommendations.push('Cross-validate findings with alternative analysis methods');
    }

    // Quality-based recommendations
    const avgConfidence = chain.steps.reduce((sum, step) => sum + step.confidence, 0) / chain.steps.length;
    if (avgConfidence < 0.8) {
      recommendations.push('Consider using higher resolution images or additional viewing angles');
    }

    return recommendations;
  }

  /**
   * Calculate overall quality score for the reasoning chain
   */
  private calculateQualityScore(chain: ReasoningChain, overallConfidence: number): number {
    let qualityScore = overallConfidence;

    // Bonus for completing all steps
    if (chain.metadata.completedSteps === chain.metadata.totalSteps) {
      qualityScore += 0.1;
    }

    // Bonus for validation steps
    const validationSteps = chain.steps.filter(step => step.type === 'validation');
    if (validationSteps.length > 0) {
      const avgValidationConfidence = validationSteps.reduce((sum, step) => sum + step.confidence, 0) / validationSteps.length;
      qualityScore += (avgValidationConfidence - 0.5) * 0.2;
    }

    // Penalty for too many low-confidence steps
    const lowConfidenceSteps = chain.steps.filter(step => step.confidence < 0.6).length;
    const lowConfidencePenalty = (lowConfidenceSteps / chain.steps.length) * 0.2;
    qualityScore -= lowConfidencePenalty;

    return Math.max(0, Math.min(1, qualityScore));
  }

  /**
   * Get reasoning template for analysis type
   */
  private getReasoningTemplate(analysisType: string): ReasoningStep[] {
    const template = this.reasoningTemplates.get(analysisType);
    if (template) {
      return template;
    }

    // Fallback to kitchen analysis template
    return this.reasoningTemplates.get('kitchen_analysis') || [];
  }

  /**
   * Generate goal statement for reasoning chain
   */
  private generateGoalStatement(context: ReasoningContext): string {
    const baseGoal = `Perform comprehensive ${context.analysisType} analysis`;

    if (context.objectives && context.objectives.length > 0) {
      return `${baseGoal} focusing on: ${context.objectives.join(', ')}`;
    }

    return baseGoal;
  }

  /**
   * Get reasoning chain status
   */
  getChainStatus(chainId: string): ReasoningChain | null {
    return this.activeChains.get(chainId) || null;
  }

  /**
   * Progress a reasoning chain to the next step
   */
  progressChain(chainId: string, stepIndex: number, evidence: string[], confidence: number, reasoning: string): void {
    const chain = this.activeChains.get(chainId);
    if (!chain) {
      throw new Error(`Reasoning chain not found: ${chainId}`);
    }

    if (stepIndex >= chain.steps.length) {
      throw new Error(`Invalid step index: ${stepIndex}`);
    }

    // Update the step with provided evidence and confidence
    const step = chain.steps[stepIndex];
    step.evidence = evidence;
    step.confidence = confidence;
    step.timestamp = new Date();

    // Update chain progress
    chain.currentStep = stepIndex + 1;
    chain.metadata.completedSteps = stepIndex + 1;

    logger.info(`Progressed reasoning chain: ${chainId}`, {
      stepIndex,
      stepId: step.id,
      confidence,
      evidenceCount: evidence.length
    });
  }

  /**
   * Complete a reasoning chain
   */
  completeChain(chainId: string): ReasoningResult {
    const chain = this.activeChains.get(chainId);
    if (!chain) {
      throw new Error(`Reasoning chain not found: ${chainId}`);
    }

    chain.status = 'completed';
    chain.metadata.endTime = new Date();

    const result = this.generateFinalResult(chain);

    logger.info(`Completed reasoning chain: ${chainId}`, {
      confidence: result.confidence,
      qualityScore: result.qualityScore
    });

    return result;
  }

  /**
   * Abort reasoning chain
   */
  abortChain(chainId: string): void {
    const chain = this.activeChains.get(chainId);
    if (chain) {
      chain.status = 'failed';
      chain.metadata.endTime = new Date();

      logger.info(`Aborted reasoning chain: ${chainId}`);
    }
  }

  /**
   * Add custom reasoning template
   */
  addReasoningTemplate(analysisType: string, template: ReasoningStep[]): void {
    this.reasoningTemplates.set(analysisType, template);
    logger.info(`Added reasoning template: ${analysisType}`, {
      stepCount: template.length
    });
  }

  /**
   * Get all active reasoning chains
   */
  getActiveChains(): any[] {
    return Array.from(this.activeChains.values()).map(chain => ({
      id: chain.id,
      analysisId: chain.analysisId,
      status: chain.status,
      currentStep: chain.currentStep,
      totalSteps: chain.steps.length,
      startTime: chain.metadata.startTime
    }));
  }

  /**
   * Get available reasoning templates
   */
  getReasoningTemplates(): { [key: string]: ReasoningStep[] } {
    const templates: { [key: string]: ReasoningStep[] } = {};

    this.reasoningTemplates.forEach((template, analysisType) => {
      templates[analysisType] = template;
    });

    return templates;
  }

  /**
   * Get reasoning statistics
   */
  getReasoningStats(): any {
    const activeChains = this.getActiveChains();

    return {
      activeChains: activeChains.length,
      totalTemplates: this.reasoningTemplates.size,
      averageStepsPerChain: activeChains.length > 0
        ? activeChains.reduce((sum, chain) => sum + chain.totalSteps, 0) / activeChains.length
        : 0,
      completionRate: 0.87, // Mock data
      averageConfidence: 0.82 // Mock data
    };
  }

  /**
   * Enhanced GPT-o1 reasoning step execution with real-time visualization
   */
  executeGPTO1ReasoningStep(
    chainId: string,
    stepId: string,
    gptO1Response: {
      content: string;
      internalThoughts: string[];
      reasoningPath: string[];
      confidenceFactors: string[];
      alternativesConsidered: string[];
      complexityScore: number;
    }
  ): void {
    const chain = this.activeChains.get(chainId);
    if (!chain) {
      logger.error(`Reasoning chain not found: ${chainId}`);
      return;
    }

    const step = chain.steps.find(s => s.id === stepId);
    if (!step) {
      logger.error(`Reasoning step not found: ${stepId}`);
      return;
    }

    // Update step with GPT-o1 reasoning data
    step.gptO1Reasoning = {
      internalThoughts: gptO1Response.internalThoughts,
      reasoningPath: gptO1Response.reasoningPath,
      confidenceFactors: gptO1Response.confidenceFactors,
      alternativesConsidered: gptO1Response.alternativesConsidered,
      complexityScore: gptO1Response.complexityScore
    };

    // Update visualization data
    if (!step.visualizationData) {
      step.visualizationData = {
        position: this.calculateStepPosition(chain, stepId),
        status: 'processing',
        complexity: this.determineComplexity(gptO1Response.complexityScore),
        parentSteps: step.dependencies,
        childSteps: this.findChildSteps(chain, stepId)
      };
    }

    step.visualizationData.status = 'completed';
    step.visualizationData.processingTime = Date.now() - step.timestamp.getTime();

    // Broadcast real-time update
    this.broadcastReasoningUpdate(chainId, stepId, step);

    logger.info(`GPT-o1 reasoning step completed: ${stepId}`, {
      chainId,
      complexityScore: gptO1Response.complexityScore,
      thoughtsCount: gptO1Response.internalThoughts.length,
      pathLength: gptO1Response.reasoningPath.length
    });
  }

  /**
   * Calculate position for step in reasoning tree visualization
   */
  private calculateStepPosition(chain: ReasoningChain, stepId: string): { x: number; y: number } {
    const stepIndex = chain.steps.findIndex(s => s.id === stepId);
    const step = chain.steps[stepIndex];

    // Calculate depth based on dependencies
    const depth = this.calculateStepDepth(chain, stepId);

    // Calculate horizontal position based on step type and index
    const typeOrder = ['observation', 'analysis', 'inference', 'validation', 'conclusion'];
    const typeIndex = typeOrder.indexOf(step.type);

    return {
      x: 100 + (typeIndex * 200) + (stepIndex % 3) * 50, // Spread horizontally by type
      y: 50 + (depth * 100) // Vertical positioning by dependency depth
    };
  }

  /**
   * Calculate step depth in dependency tree
   */
  private calculateStepDepth(chain: ReasoningChain, stepId: string): number {
    const step = chain.steps.find(s => s.id === stepId);
    if (!step || step.dependencies.length === 0) return 0;

    const maxDepth = Math.max(...step.dependencies.map(depId =>
      this.calculateStepDepth(chain, depId)
    ));

    return maxDepth + 1;
  }

  /**
   * Find child steps that depend on this step
   */
  private findChildSteps(chain: ReasoningChain, stepId: string): string[] {
    return chain.steps
      .filter(step => step.dependencies.includes(stepId))
      .map(step => step.id);
  }

  /**
   * Determine complexity level from score
   */
  private determineComplexity(score: number): 'low' | 'medium' | 'high' {
    if (score < 0.3) return 'low';
    if (score < 0.7) return 'medium';
    return 'high';
  }

  /**
   * Broadcast reasoning update via WebSocket
   */
  private broadcastReasoningUpdate(chainId: string, stepId: string, step: ReasoningStep): void {
    if (!this.socketManager) return;

    const updateData = {
      chainId,
      stepId,
      step: {
        id: step.id,
        type: step.type,
        description: step.description,
        confidence: step.confidence,
        status: step.visualizationData?.status,
        processingTime: step.visualizationData?.processingTime,
        complexity: step.visualizationData?.complexity,
        gptO1Reasoning: step.gptO1Reasoning,
        position: step.visualizationData?.position,
        parentSteps: step.visualizationData?.parentSteps,
        childSteps: step.visualizationData?.childSteps
      },
      timestamp: new Date().toISOString()
    };

    this.socketManager.broadcastReasoningUpdate(updateData);
  }

  /**
   * Get reasoning chain for visualization
   */
  getReasoningChainForVisualization(chainId: string): any {
    const chain = this.activeChains.get(chainId);
    if (!chain) return null;

    return {
      id: chain.id,
      analysisId: chain.analysisId,
      goal: chain.goal,
      status: chain.status,
      currentStep: chain.currentStep,
      steps: chain.steps.map(step => ({
        id: step.id,
        type: step.type,
        description: step.description,
        confidence: step.confidence,
        dependencies: step.dependencies,
        gptO1Reasoning: step.gptO1Reasoning,
        visualizationData: step.visualizationData,
        timestamp: step.timestamp
      })),
      metadata: chain.metadata
    };
  }

  /**
   * Initialize visualization data for all steps in a chain
   */
  initializeVisualizationData(chainId: string): void {
    const chain = this.activeChains.get(chainId);
    if (!chain) return;

    chain.steps.forEach(step => {
      if (!step.visualizationData) {
        step.visualizationData = {
          position: this.calculateStepPosition(chain, step.id),
          status: 'pending',
          complexity: 'medium',
          parentSteps: step.dependencies,
          childSteps: this.findChildSteps(chain, step.id)
        };
      }
    });

    // Broadcast initial chain structure
    if (this.socketManager) {
      this.socketManager.broadcastReasoningChainInitialized({
        chainId,
        analysisId: chain.analysisId,
        totalSteps: chain.steps.length,
        structure: this.getReasoningChainForVisualization(chainId)
      });
    }
  }
}

export const reasoningManager = new ReasoningManager();

// Initialize with socket manager when available
export const initializeReasoningManagerWithSocket = (socketManager: any) => {
  reasoningManager.setSocketManager(socketManager);
};
