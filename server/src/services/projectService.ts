import { v4 as uuidv4 } from 'uuid';
import { DatabaseService } from './databaseService';
import { logger } from '@/utils/logger';

export interface Project {
  id: string;
  name: string;
  description?: string;
  organizationId?: string;
  ownerId: string;
  ownerName: string;
  ownerEmail: string;
  visibility: 'public' | 'private' | 'organization';
  status: 'active' | 'archived' | 'deleted';
  tags?: string[];
  folderPath?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectPermission {
  id: string;
  projectId: string;
  userId?: string;
  email?: string;
  permissionLevel: 'view' | 'comment' | 'edit' | 'admin';
  grantedBy: string;
  expiresAt?: Date;
  passwordHash?: string;
  createdAt: Date;
}

export interface CreateProjectData {
  name: string;
  description?: string;
  organizationId?: string;
  ownerId: string;
  visibility?: 'public' | 'private' | 'organization';
  tags?: string[];
  folderPath?: string;
}

export interface UpdateProjectData {
  name?: string;
  description?: string;
  visibility?: 'public' | 'private' | 'organization';
  status?: 'active' | 'archived' | 'deleted';
  tags?: string[];
  folderPath?: string;
}

export interface ShareProjectData {
  projectId: string;
  userId?: string;
  email?: string;
  permissionLevel: 'view' | 'comment' | 'edit' | 'admin';
  grantedBy: string;
  expiresAt?: Date;
  password?: string;
}

export class ProjectService {
  private db: DatabaseService;

  constructor() {
    this.db = DatabaseService.getInstance();
  }

  /**
   * Create a new project
   */
  public async createProject(projectData: CreateProjectData): Promise<Project> {
    try {
      const projectId = uuidv4();
      
      this.db.createProject({
        id: projectId,
        name: projectData.name,
        description: projectData.description,
        organizationId: projectData.organizationId,
        ownerId: projectData.ownerId,
        visibility: projectData.visibility || 'private',
        tags: projectData.tags
      });

      // Log activity
      this.db.logActivity({
        id: uuidv4(),
        userId: projectData.ownerId,
        actionType: 'create',
        resourceType: 'project',
        resourceId: projectId,
        details: {
          name: projectData.name,
          visibility: projectData.visibility || 'private'
        }
      });

      // Get the created project
      const project = this.db.getProjectById(projectId);
      if (!project) {
        throw new Error('Failed to retrieve created project');
      }

      logger.info('Project created successfully', {
        projectId,
        name: projectData.name,
        ownerId: projectData.ownerId
      });

      return this.formatProject(project);
    } catch (error) {
      logger.error('Failed to create project', { error, projectData });
      throw error;
    }
  }

  /**
   * Get project by ID
   */
  public async getProjectById(projectId: string, userId?: string): Promise<Project | null> {
    try {
      const project = this.db.getProjectById(projectId);
      if (!project) {
        return null;
      }

      // Check permissions if userId is provided
      if (userId && !await this.hasProjectAccess(projectId, userId, 'view')) {
        throw new Error('Access denied');
      }

      return this.formatProject(project);
    } catch (error) {
      logger.error('Failed to get project', { error, projectId, userId });
      throw error;
    }
  }

  /**
   * Get projects for a user
   */
  public async getProjectsByUser(userId: string): Promise<Project[]> {
    try {
      const projects = this.db.getProjectsByUser(userId);
      return projects.map(project => this.formatProject(project));
    } catch (error) {
      logger.error('Failed to get user projects', { error, userId });
      throw error;
    }
  }

  /**
   * Update project
   */
  public async updateProject(projectId: string, updates: UpdateProjectData, userId: string): Promise<Project> {
    try {
      // Check permissions
      if (!await this.hasProjectAccess(projectId, userId, 'edit')) {
        throw new Error('Access denied');
      }

      // Check if project exists
      const existingProject = this.db.getProjectById(projectId);
      if (!existingProject) {
        throw new Error('Project not found');
      }

      // Update project in database
      this.db.updateProject(projectId, updates);

      // Log activity
      this.db.logActivity({
        id: uuidv4(),
        userId,
        actionType: 'update',
        resourceType: 'project',
        resourceId: projectId,
        details: updates
      });

      // Get updated project
      const updatedProject = this.db.getProjectById(projectId);
      if (!updatedProject) {
        throw new Error('Failed to retrieve updated project');
      }

      logger.info('Project updated successfully', { projectId, updates, userId });

      return this.formatProject(updatedProject);
    } catch (error) {
      logger.error('Failed to update project', { error, projectId, updates, userId });
      throw error;
    }
  }

  /**
   * Delete project
   */
  public async deleteProject(projectId: string, userId: string): Promise<void> {
    try {
      // Check permissions (only owner or admin can delete)
      if (!await this.hasProjectAccess(projectId, userId, 'admin')) {
        throw new Error('Access denied');
      }

      // Check if project exists
      const existingProject = this.db.getProjectById(projectId);
      if (!existingProject) {
        throw new Error('Project not found');
      }

      // Soft delete by updating status
      this.db.deleteProject(projectId);

      // Log activity
      this.db.logActivity({
        id: uuidv4(),
        userId,
        actionType: 'delete',
        resourceType: 'project',
        resourceId: projectId
      });

      logger.info('Project deleted successfully', { projectId, userId });
    } catch (error) {
      logger.error('Failed to delete project', { error, projectId, userId });
      throw error;
    }
  }

  /**
   * Share project with user or email
   */
  public async shareProject(shareData: ShareProjectData): Promise<ProjectPermission> {
    try {
      // Check if granter has admin access
      if (!await this.hasProjectAccess(shareData.projectId, shareData.grantedBy, 'admin')) {
        throw new Error('Access denied');
      }

      const permissionId = uuidv4();
      
      // Create project permission (would need to add this method to DatabaseService)
      // For now, create a mock permission
      const permission: ProjectPermission = {
        id: permissionId,
        projectId: shareData.projectId,
        userId: shareData.userId,
        email: shareData.email,
        permissionLevel: shareData.permissionLevel,
        grantedBy: shareData.grantedBy,
        expiresAt: shareData.expiresAt,
        createdAt: new Date()
      };

      // Log activity
      this.db.logActivity({
        id: uuidv4(),
        userId: shareData.grantedBy,
        actionType: 'share',
        resourceType: 'project',
        resourceId: shareData.projectId,
        details: {
          sharedWith: shareData.userId || shareData.email,
          permissionLevel: shareData.permissionLevel
        }
      });

      logger.info('Project shared successfully', {
        projectId: shareData.projectId,
        sharedWith: shareData.userId || shareData.email,
        permissionLevel: shareData.permissionLevel
      });

      return permission;
    } catch (error) {
      logger.error('Failed to share project', { error, shareData });
      throw error;
    }
  }

  /**
   * Get project permissions
   */
  public async getProjectPermissions(projectId: string, userId: string): Promise<ProjectPermission[]> {
    try {
      // Check if user has admin access
      if (!await this.hasProjectAccess(projectId, userId, 'admin')) {
        throw new Error('Access denied');
      }

      // Get permissions from database (would need to add this method to DatabaseService)
      // For now, return empty array
      return [];
    } catch (error) {
      logger.error('Failed to get project permissions', { error, projectId, userId });
      throw error;
    }
  }

  /**
   * Remove project permission
   */
  public async removeProjectPermission(permissionId: string, userId: string): Promise<void> {
    try {
      // Get permission details and check if user can remove it
      // (would need to implement this logic)
      
      // Log activity
      this.db.logActivity({
        id: uuidv4(),
        userId,
        actionType: 'update',
        resourceType: 'project_permission',
        resourceId: permissionId,
        details: { action: 'removed' }
      });

      logger.info('Project permission removed', { permissionId, userId });
    } catch (error) {
      logger.error('Failed to remove project permission', { error, permissionId, userId });
      throw error;
    }
  }

  /**
   * Add analysis to project
   */
  public async addAnalysisToProject(projectId: string, analysisId: string, userId: string): Promise<void> {
    try {
      // Check permissions
      if (!await this.hasProjectAccess(projectId, userId, 'edit')) {
        throw new Error('Access denied');
      }

      // Add analysis to project (using existing method)
      // This would create a project_analyses relationship
      
      // Log activity
      this.db.logActivity({
        id: uuidv4(),
        userId,
        actionType: 'create',
        resourceType: 'project_analysis',
        resourceId: `${projectId}-${analysisId}`,
        details: {
          projectId,
          analysisId
        }
      });

      logger.info('Analysis added to project', { projectId, analysisId, userId });
    } catch (error) {
      logger.error('Failed to add analysis to project', { error, projectId, analysisId, userId });
      throw error;
    }
  }

  /**
   * Bulk update projects
   */
  public async bulkUpdateProjects(projectIds: string[], updates: {
    status?: 'active' | 'archived' | 'deleted';
    visibility?: 'public' | 'private' | 'organization';
    tags?: string[];
  }, userId: string): Promise<void> {
    try {
      // Check permissions for all projects
      for (const projectId of projectIds) {
        if (!await this.hasProjectAccess(projectId, userId, 'edit')) {
          throw new Error(`Access denied for project ${projectId}`);
        }
      }

      // Perform bulk update
      this.db.bulkUpdateProjects(projectIds, updates);

      // Log activity for each project
      for (const projectId of projectIds) {
        this.db.logActivity({
          id: uuidv4(),
          userId,
          actionType: 'bulk_update',
          resourceType: 'project',
          resourceId: projectId,
          details: updates
        });
      }

      logger.info('Bulk project update completed', { projectIds, updates, userId });
    } catch (error) {
      logger.error('Failed to bulk update projects', { error, projectIds, updates, userId });
      throw error;
    }
  }

  /**
   * Check if user has access to project
   */
  public async hasProjectAccess(projectId: string, userId: string, requiredLevel: 'view' | 'comment' | 'edit' | 'admin'): Promise<boolean> {
    try {
      const project = this.db.getProjectById(projectId);
      if (!project) {
        return false;
      }

      // Owner has full access
      if (project.owner_id === userId) {
        return true;
      }

      // Check project permissions (would need to implement this in DatabaseService)
      // For now, return true for demonstration
      return true;
    } catch (error) {
      logger.error('Failed to check project access', { error, projectId, userId, requiredLevel });
      return false;
    }
  }

  /**
   * Format project data for API response
   */
  private formatProject(project: any): Project {
    return {
      id: project.id,
      name: project.name,
      description: project.description,
      organizationId: project.organization_id,
      ownerId: project.owner_id,
      ownerName: `${project.first_name} ${project.last_name}`,
      ownerEmail: project.owner_email,
      visibility: project.visibility,
      status: project.status,
      tags: project.tags ? JSON.parse(project.tags) : undefined,
      folderPath: project.folder_path,
      createdAt: new Date(project.created_at),
      updatedAt: new Date(project.updated_at)
    };
  }
}
