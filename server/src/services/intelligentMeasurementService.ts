import { logger } from '../utils/logger';
import { promptOptimizationService } from './promptOptimizationService';
import { reasoningManager } from './reasoningManager';
import { enhancedPdfProcessor } from './enhancedPdfProcessor';

export interface MeasurementPoint {
  x: number;
  y: number;
  label?: string;
  confidence: number;
}

export interface ScaleReference {
  type: 'DIMENSION_LINE' | 'SCALE_BAR' | 'GRID' | 'ANNOTATION';
  value: number;
  unit: 'mm' | 'cm' | 'm' | 'in' | 'ft';
  pixelLength: number;
  confidence: number;
  location: MeasurementPoint[];
}

export interface RoomLayout {
  boundaries: MeasurementPoint[];
  workTriangle: {
    sink: MeasurementPoint;
    stove: MeasurementPoint;
    refrigerator: MeasurementPoint;
    efficiency: number;
  };
  trafficFlow: {
    primaryPaths: MeasurementPoint[][];
    bottlenecks: MeasurementPoint[];
    clearanceIssues: string[];
  };
  zones: {
    cooking: MeasurementPoint[];
    preparation: MeasurementPoint[];
    storage: MeasurementPoint[];
    cleanup: MeasurementPoint[];
  };
}

export interface SpaceOptimization {
  currentEfficiency: number;
  recommendations: {
    type: 'LAYOUT' | 'STORAGE' | 'WORKFLOW' | 'ACCESSIBILITY';
    priority: 'HIGH' | 'MEDIUM' | 'LOW';
    description: string;
    expectedImprovement: number;
    implementationComplexity: 'LOW' | 'MEDIUM' | 'HIGH';
    estimatedCost?: number;
  }[];
  alternativeLayouts?: {
    name: string;
    description: string;
    efficiency: number;
    changes: string[];
  }[];
}

export interface MeasurementValidation {
  accuracy: number;
  crossReferences: {
    source1: string;
    source2: string;
    variance: number;
    confidence: number;
  }[];
  inconsistencies: {
    description: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH';
    suggestions: string[];
  }[];
}

export interface IntelligentMeasurementResult {
  scaleDetection: {
    detectedScales: ScaleReference[];
    primaryScale: ScaleReference | null;
    confidence: number;
  };
  roomLayout: RoomLayout;
  spaceOptimization: SpaceOptimization;
  measurementValidation: MeasurementValidation;
  processingMetrics: {
    analysisTime: number;
    confidenceScore: number;
    featuresDetected: string[];
  };
}

export interface MeasurementConfig {
  enableAutoScale: boolean;
  enableLayoutAnalysis: boolean;
  enableSpaceOptimization: boolean;
  enableMeasurementValidation: boolean;
  accuracyThreshold: number;
  optimizationLevel: 'BASIC' | 'ADVANCED' | 'COMPREHENSIVE';
}

/**
 * Intelligent Measurement System Service
 * 
 * Implements Priority 1 Enhanced Analysis Engine feature for advanced
 * measurement detection, auto-scaling, room layout analysis, and space optimization.
 * 
 * Leverages existing Azure OpenAI integration and advanced AI services
 * for superior measurement accuracy and spatial understanding.
 */
export class IntelligentMeasurementService {
  private defaultConfig: MeasurementConfig = {
    enableAutoScale: true,
    enableLayoutAnalysis: true,
    enableSpaceOptimization: true,
    enableMeasurementValidation: true,
    accuracyThreshold: 0.85,
    optimizationLevel: 'ADVANCED'
  };

  /**
   * Perform comprehensive intelligent measurement analysis
   */
  async analyzeMeasurements(
    imagePaths: string[],
    analysisId: string,
    config: Partial<MeasurementConfig> = {}
  ): Promise<IntelligentMeasurementResult> {
    const startTime = Date.now();
    const finalConfig = { ...this.defaultConfig, ...config };

    logger.info(`Starting intelligent measurement analysis: ${analysisId}`, {
      imageCount: imagePaths.length,
      config: finalConfig
    });

    try {
      // Step 1: Auto-scale detection
      const scaleDetection = finalConfig.enableAutoScale 
        ? await this.detectAutoScale(imagePaths, analysisId, finalConfig)
        : { detectedScales: [], primaryScale: null, confidence: 0 };

      // Step 2: Room layout analysis
      const roomLayout = finalConfig.enableLayoutAnalysis
        ? await this.analyzeRoomLayout(imagePaths, analysisId, scaleDetection.primaryScale, finalConfig)
        : this.getDefaultRoomLayout();

      // Step 3: Space optimization analysis
      const spaceOptimization = finalConfig.enableSpaceOptimization
        ? await this.analyzeSpaceOptimization(roomLayout, analysisId, finalConfig)
        : this.getDefaultSpaceOptimization();

      // Step 4: Measurement validation
      const measurementValidation = finalConfig.enableMeasurementValidation
        ? await this.validateMeasurements(imagePaths, scaleDetection, analysisId, finalConfig)
        : this.getDefaultMeasurementValidation();

      const processingTime = Date.now() - startTime;
      const confidenceScore = this.calculateOverallConfidence(
        scaleDetection,
        roomLayout,
        spaceOptimization,
        measurementValidation
      );

      const result: IntelligentMeasurementResult = {
        scaleDetection,
        roomLayout,
        spaceOptimization,
        measurementValidation,
        processingMetrics: {
          analysisTime: processingTime,
          confidenceScore,
          featuresDetected: this.getDetectedFeatures(finalConfig)
        }
      };

      logger.info(`Intelligent measurement analysis completed: ${analysisId}`, {
        processingTime,
        confidenceScore,
        scalesDetected: scaleDetection.detectedScales.length,
        recommendationsCount: spaceOptimization.recommendations.length
      });

      return result;

    } catch (error) {
      logger.error(`Intelligent measurement analysis failed: ${analysisId}`, error);
      throw new Error(`Measurement analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Detect automatic scale from drawing annotations and dimension lines
   */
  private async detectAutoScale(
    imagePaths: string[],
    analysisId: string,
    config: MeasurementConfig
  ): Promise<{ detectedScales: ScaleReference[]; primaryScale: ScaleReference | null; confidence: number }> {
    try {
      // Generate optimized prompt for scale detection
      const scalePrompt = await this.generateScaleDetectionPrompt(config);

      // Start reasoning chain for scale detection
      const reasoningChainId = await this.initiateScaleReasoning(analysisId, config);

      // Simulate scale detection analysis (in production, this would use GPT-4o vision)
      const detectedScales: ScaleReference[] = [
        {
          type: 'DIMENSION_LINE',
          value: 1000,
          unit: 'mm',
          pixelLength: 150,
          confidence: 0.92,
          location: [
            { x: 100, y: 200, confidence: 0.9 },
            { x: 250, y: 200, confidence: 0.9 }
          ]
        },
        {
          type: 'SCALE_BAR',
          value: 500,
          unit: 'mm',
          pixelLength: 75,
          confidence: 0.88,
          location: [
            { x: 50, y: 50, confidence: 0.85 }
          ]
        }
      ];

      // Select primary scale (highest confidence)
      const primaryScale = detectedScales.reduce((best, current) => 
        current.confidence > (best?.confidence || 0) ? current : best, null as ScaleReference | null
      );

      const overallConfidence = detectedScales.length > 0 
        ? detectedScales.reduce((sum, scale) => sum + scale.confidence, 0) / detectedScales.length
        : 0;

      logger.info(`Scale detection completed: ${analysisId}`, {
        scalesFound: detectedScales.length,
        primaryScale: primaryScale?.type,
        confidence: overallConfidence
      });

      return {
        detectedScales,
        primaryScale,
        confidence: overallConfidence
      };

    } catch (error) {
      logger.error(`Scale detection failed: ${analysisId}`, error);
      return { detectedScales: [], primaryScale: null, confidence: 0 };
    }
  }

  /**
   * Generate optimized prompt for scale detection
   */
  private async generateScaleDetectionPrompt(config: MeasurementConfig): Promise<string> {
    const basePrompt = `
INTELLIGENT SCALE DETECTION ANALYSIS

OBJECTIVE: Detect and analyze drawing scales, dimension lines, and measurement references in kitchen design drawings.

SCALE DETECTION PRIORITIES:
1. DIMENSION LINES: Look for lines with measurement annotations (e.g., "1000mm", "3'-6"")
2. SCALE BARS: Identify dedicated scale reference bars with unit markings
3. GRID SYSTEMS: Detect grid patterns with known spacing
4. ANNOTATION SCALES: Find scale ratios in drawing titles or legends (e.g., "1:50", "1/4" = 1'-0"")

MEASUREMENT ANALYSIS:
- Extract numerical values and their associated units
- Identify pixel distances corresponding to real-world measurements
- Calculate scale ratios and conversion factors
- Assess measurement consistency across the drawing

ACCURACY REQUIREMENTS:
- Minimum confidence threshold: ${config.accuracyThreshold}
- Cross-validate multiple scale references when available
- Flag inconsistent or conflicting scale information
- Prioritize dimension lines over scale bars for accuracy

OUTPUT FORMAT:
For each detected scale reference, provide:
- Type (dimension_line, scale_bar, grid, annotation)
- Real-world value and unit
- Pixel measurement
- Confidence score (0.0-1.0)
- Location coordinates
- Validation notes

CRITICAL: Focus on kitchen-specific measurements (cabinet dimensions, room sizes, appliance clearances).
`;

    // Optimize the prompt using existing optimization service
    const optimizationResult = await promptOptimizationService.optimizePrompt(
      basePrompt,
      {
        analysisType: 'intelligent_measurement_scale_detection',
        targetMetrics: {
          minAccuracy: config.accuracyThreshold,
          maxResponseTime: 6000
        },
        commonErrors: [
          'Misidentifying decorative lines as dimension lines',
          'Incorrect unit conversion between metric and imperial',
          'Confusing scale bars with other drawing elements'
        ]
      }
    );

    return optimizationResult.optimizedPrompt;
  }

  /**
   * Initiate scale detection reasoning chain
   */
  private async initiateScaleReasoning(
    analysisId: string,
    config: MeasurementConfig
  ): Promise<string> {
    try {
      const reasoningChainId = reasoningManager.startReasoningChain(analysisId, {
        analysisType: 'intelligent_scale_detection',
        inputData: { config },
        constraints: [
          'accurate_scale_identification',
          'consistent_unit_handling',
          'reliable_pixel_measurement'
        ],
        objectives: [
          'dimension_line_detection',
          'scale_bar_identification',
          'measurement_validation',
          'scale_accuracy_assessment'
        ],
        qualityThresholds: {
          minConfidence: config.accuracyThreshold,
          maxUncertainty: 0.2,
          measurementAccuracy: 0.9
        }
      });

      logger.info(`Started scale detection reasoning chain: ${reasoningChainId}`);
      return reasoningChainId;

    } catch (error) {
      logger.error('Failed to start scale detection reasoning chain:', error);
      throw error;
    }
  }

  /**
   * Analyze room layout with traffic flow and work triangle
   */
  private async analyzeRoomLayout(
    imagePaths: string[],
    analysisId: string,
    primaryScale: ScaleReference | null,
    config: MeasurementConfig
  ): Promise<RoomLayout> {
    try {
      // Generate optimized prompt for layout analysis
      const layoutPrompt = await this.generateLayoutAnalysisPrompt(config, primaryScale);

      // Start reasoning chain for layout analysis
      const reasoningChainId = await this.initiateLayoutReasoning(analysisId, config);

      // Simulate room layout analysis (in production, this would use GPT-4o vision)
      const roomLayout: RoomLayout = {
        boundaries: [
          { x: 0, y: 0, label: 'corner_1', confidence: 0.95 },
          { x: 400, y: 0, label: 'corner_2', confidence: 0.95 },
          { x: 400, y: 300, label: 'corner_3', confidence: 0.95 },
          { x: 0, y: 300, label: 'corner_4', confidence: 0.95 }
        ],
        workTriangle: {
          sink: { x: 100, y: 150, label: 'sink', confidence: 0.9 },
          stove: { x: 300, y: 150, label: 'stove', confidence: 0.9 },
          refrigerator: { x: 200, y: 50, label: 'refrigerator', confidence: 0.9 },
          efficiency: 0.85
        },
        trafficFlow: {
          primaryPaths: [
            [
              { x: 50, y: 200, confidence: 0.8 },
              { x: 350, y: 200, confidence: 0.8 }
            ]
          ],
          bottlenecks: [
            { x: 150, y: 100, label: 'narrow_passage', confidence: 0.7 }
          ],
          clearanceIssues: [
            'Insufficient clearance between island and wall cabinets',
            'Refrigerator door swing conflicts with traffic path'
          ]
        },
        zones: {
          cooking: [
            { x: 280, y: 130, confidence: 0.9 },
            { x: 320, y: 170, confidence: 0.9 }
          ],
          preparation: [
            { x: 180, y: 130, confidence: 0.85 },
            { x: 220, y: 170, confidence: 0.85 }
          ],
          storage: [
            { x: 80, y: 130, confidence: 0.8 },
            { x: 120, y: 170, confidence: 0.8 }
          ],
          cleanup: [
            { x: 80, y: 130, confidence: 0.9 },
            { x: 120, y: 170, confidence: 0.9 }
          ]
        }
      };

      logger.info(`Room layout analysis completed: ${analysisId}`, {
        workTriangleEfficiency: roomLayout.workTriangle.efficiency,
        clearanceIssues: roomLayout.trafficFlow.clearanceIssues.length,
        bottlenecks: roomLayout.trafficFlow.bottlenecks.length
      });

      return roomLayout;

    } catch (error) {
      logger.error(`Room layout analysis failed: ${analysisId}`, error);
      return this.getDefaultRoomLayout();
    }
  }

  /**
   * Generate optimized prompt for layout analysis
   */
  private async generateLayoutAnalysisPrompt(
    config: MeasurementConfig,
    primaryScale: ScaleReference | null
  ): Promise<string> {
    const scaleInfo = primaryScale
      ? `Using detected scale: ${primaryScale.value}${primaryScale.unit} = ${primaryScale.pixelLength}px`
      : 'No scale detected - use relative measurements';

    const basePrompt = `
INTELLIGENT ROOM LAYOUT ANALYSIS

OBJECTIVE: Analyze kitchen layout for traffic flow, work triangle efficiency, and functional zones.

SCALE REFERENCE: ${scaleInfo}

LAYOUT ANALYSIS PRIORITIES:
1. WORK TRIANGLE: Identify sink, stove, and refrigerator positions
2. TRAFFIC FLOW: Map primary and secondary circulation paths
3. FUNCTIONAL ZONES: Define cooking, preparation, storage, and cleanup areas
4. CLEARANCES: Measure aisle widths and door swing clearances
5. BOTTLENECKS: Identify circulation constraints and conflicts

WORK TRIANGLE EVALUATION:
- Measure distances between sink, stove, and refrigerator
- Calculate triangle perimeter (ideal: 12-26 feet / 3.6-7.9m)
- Assess triangle efficiency (no obstacles crossing paths)
- Evaluate accessibility and workflow logic

TRAFFIC FLOW ANALYSIS:
- Primary paths: Main circulation routes through kitchen
- Secondary paths: Access to storage and work areas
- Minimum clearances: 36" (914mm) for aisles, 42" (1067mm) for work aisles
- Door swing conflicts and interference points

FUNCTIONAL ZONE MAPPING:
- Cooking zone: Range, oven, microwave, ventilation
- Preparation zone: Counter space, cutting boards, small appliances
- Storage zone: Pantry, cabinets, refrigerator access
- Cleanup zone: Sink, dishwasher, trash, recycling

OPTIMIZATION LEVEL: ${config.optimizationLevel}
ACCURACY THRESHOLD: ${config.accuracyThreshold}
`;

    const optimizationResult = await promptOptimizationService.optimizePrompt(
      basePrompt,
      {
        analysisType: 'intelligent_layout_analysis',
        targetMetrics: {
          minAccuracy: config.accuracyThreshold,
          maxResponseTime: 8000
        },
        commonErrors: [
          'Misidentifying appliances and their locations',
          'Incorrect clearance measurements',
          'Missing traffic flow conflicts'
        ]
      }
    );

    return optimizationResult.optimizedPrompt;
  }

  /**
   * Initiate layout analysis reasoning chain
   */
  private async initiateLayoutReasoning(
    analysisId: string,
    config: MeasurementConfig
  ): Promise<string> {
    try {
      const reasoningChainId = reasoningManager.startReasoningChain(analysisId, {
        analysisType: 'intelligent_layout_analysis',
        inputData: { config },
        constraints: [
          'accurate_appliance_identification',
          'realistic_clearance_measurements',
          'valid_traffic_flow_patterns'
        ],
        objectives: [
          'work_triangle_optimization',
          'traffic_flow_analysis',
          'functional_zone_mapping',
          'clearance_validation'
        ],
        qualityThresholds: {
          minConfidence: config.accuracyThreshold,
          maxUncertainty: 0.25,
          layoutEfficiency: 0.8
        }
      });

      logger.info(`Started layout analysis reasoning chain: ${reasoningChainId}`);
      return reasoningChainId;

    } catch (error) {
      logger.error('Failed to start layout analysis reasoning chain:', error);
      throw error;
    }
  }

  /**
   * Analyze space optimization opportunities
   */
  private async analyzeSpaceOptimization(
    roomLayout: RoomLayout,
    analysisId: string,
    config: MeasurementConfig
  ): Promise<SpaceOptimization> {
    try {
      // Generate optimized prompt for space optimization
      const optimizationPrompt = await this.generateSpaceOptimizationPrompt(config, roomLayout);

      // Start reasoning chain for space optimization
      const reasoningChainId = await this.initiateOptimizationReasoning(analysisId, config);

      // Calculate current efficiency based on work triangle and traffic flow
      const currentEfficiency = this.calculateLayoutEfficiency(roomLayout);

      // Generate optimization recommendations
      const recommendations = await this.generateOptimizationRecommendations(roomLayout, config);

      // Generate alternative layouts if comprehensive optimization is enabled
      const alternativeLayouts = config.optimizationLevel === 'COMPREHENSIVE'
        ? await this.generateAlternativeLayouts(roomLayout, config)
        : undefined;

      const spaceOptimization: SpaceOptimization = {
        currentEfficiency,
        recommendations,
        alternativeLayouts
      };

      logger.info(`Space optimization analysis completed: ${analysisId}`, {
        currentEfficiency,
        recommendationsCount: recommendations.length,
        alternativeLayoutsCount: alternativeLayouts?.length || 0
      });

      return spaceOptimization;

    } catch (error) {
      logger.error(`Space optimization analysis failed: ${analysisId}`, error);
      return this.getDefaultSpaceOptimization();
    }
  }

  /**
   * Generate space optimization prompt
   */
  private async generateSpaceOptimizationPrompt(
    config: MeasurementConfig,
    roomLayout: RoomLayout
  ): Promise<string> {
    const workTriangleInfo = `Work triangle efficiency: ${roomLayout.workTriangle.efficiency}`;
    const clearanceIssues = roomLayout.trafficFlow.clearanceIssues.join(', ');

    const basePrompt = `
INTELLIGENT SPACE OPTIMIZATION ANALYSIS

OBJECTIVE: Analyze kitchen layout for optimization opportunities and provide actionable recommendations.

CURRENT LAYOUT STATUS:
${workTriangleInfo}
Clearance Issues: ${clearanceIssues}
Bottlenecks: ${roomLayout.trafficFlow.bottlenecks.length} identified

OPTIMIZATION PRIORITIES:
1. WORKFLOW EFFICIENCY: Improve work triangle and task sequences
2. STORAGE OPTIMIZATION: Maximize storage capacity and accessibility
3. TRAFFIC FLOW: Eliminate bottlenecks and improve circulation
4. ACCESSIBILITY: Ensure universal design principles
5. SPACE UTILIZATION: Optimize counter space and storage density

RECOMMENDATION CATEGORIES:
- LAYOUT: Cabinet repositioning, island modifications, appliance relocation
- STORAGE: Cabinet upgrades, pantry optimization, vertical storage solutions
- WORKFLOW: Task zone improvements, prep space optimization
- ACCESSIBILITY: ADA compliance, aging-in-place modifications

OPTIMIZATION LEVEL: ${config.optimizationLevel}
ACCURACY THRESHOLD: ${config.accuracyThreshold}

CRITICAL: Provide specific, actionable recommendations with estimated costs and implementation complexity.
`;

    const optimizationResult = await promptOptimizationService.optimizePrompt(
      basePrompt,
      {
        analysisType: 'intelligent_space_optimization',
        targetMetrics: {
          minAccuracy: config.accuracyThreshold,
          maxResponseTime: 10000
        },
        commonErrors: [
          'Unrealistic renovation suggestions',
          'Ignoring structural constraints',
          'Overlooking budget considerations'
        ]
      }
    );

    return optimizationResult.optimizedPrompt;
  }

  /**
   * Calculate layout efficiency score
   */
  private calculateLayoutEfficiency(roomLayout: RoomLayout): number {
    let efficiency = 0;

    // Work triangle efficiency (40% weight)
    efficiency += roomLayout.workTriangle.efficiency * 0.4;

    // Traffic flow efficiency (30% weight)
    const trafficEfficiency = Math.max(0, 1 - (roomLayout.trafficFlow.bottlenecks.length * 0.2));
    efficiency += trafficEfficiency * 0.3;

    // Clearance issues penalty (20% weight)
    const clearanceEfficiency = Math.max(0, 1 - (roomLayout.trafficFlow.clearanceIssues.length * 0.15));
    efficiency += clearanceEfficiency * 0.2;

    // Zone organization (10% weight)
    const zoneEfficiency = 0.8; // Simplified calculation
    efficiency += zoneEfficiency * 0.1;

    return Math.min(efficiency, 1.0);
  }

  /**
   * Generate optimization recommendations
   */
  private async generateOptimizationRecommendations(
    roomLayout: RoomLayout,
    config: MeasurementConfig
  ): Promise<SpaceOptimization['recommendations']> {
    const recommendations: SpaceOptimization['recommendations'] = [];

    // Work triangle optimization
    if (roomLayout.workTriangle.efficiency < 0.8) {
      recommendations.push({
        type: 'LAYOUT',
        priority: 'HIGH',
        description: 'Optimize work triangle by repositioning appliances for better workflow efficiency',
        expectedImprovement: 0.15,
        implementationComplexity: 'HIGH',
        estimatedCost: 15000
      });
    }

    // Traffic flow improvements
    if (roomLayout.trafficFlow.bottlenecks.length > 0) {
      recommendations.push({
        type: 'LAYOUT',
        priority: 'MEDIUM',
        description: 'Widen aisles and remove traffic bottlenecks for better circulation',
        expectedImprovement: 0.12,
        implementationComplexity: 'MEDIUM',
        estimatedCost: 8000
      });
    }

    // Storage optimization
    recommendations.push({
      type: 'STORAGE',
      priority: 'MEDIUM',
      description: 'Add pull-out drawers and vertical storage solutions to maximize capacity',
      expectedImprovement: 0.08,
      implementationComplexity: 'LOW',
      estimatedCost: 3000
    });

    // Clearance issues
    if (roomLayout.trafficFlow.clearanceIssues.length > 0) {
      recommendations.push({
        type: 'WORKFLOW',
        priority: 'HIGH',
        description: 'Address clearance issues to improve safety and accessibility',
        expectedImprovement: 0.10,
        implementationComplexity: 'MEDIUM',
        estimatedCost: 5000
      });
    }

    // Accessibility improvements
    if (config.optimizationLevel === 'COMPREHENSIVE') {
      recommendations.push({
        type: 'ACCESSIBILITY',
        priority: 'LOW',
        description: 'Implement universal design features for aging-in-place compatibility',
        expectedImprovement: 0.05,
        implementationComplexity: 'MEDIUM',
        estimatedCost: 7000
      });
    }

    return recommendations;
  }

  /**
   * Initiate optimization reasoning chain
   */
  private async initiateOptimizationReasoning(
    analysisId: string,
    config: MeasurementConfig
  ): Promise<string> {
    try {
      const reasoningChainId = reasoningManager.startReasoningChain(analysisId, {
        analysisType: 'intelligent_space_optimization',
        inputData: { config },
        constraints: [
          'realistic_renovation_scope',
          'budget_conscious_recommendations',
          'structural_feasibility'
        ],
        objectives: [
          'workflow_efficiency_improvement',
          'storage_capacity_optimization',
          'accessibility_enhancement',
          'cost_effectiveness_analysis'
        ],
        qualityThresholds: {
          minConfidence: config.accuracyThreshold,
          maxUncertainty: 0.3,
          improvementPotential: 0.1
        }
      });

      logger.info(`Started optimization reasoning chain: ${reasoningChainId}`);
      return reasoningChainId;

    } catch (error) {
      logger.error('Failed to start optimization reasoning chain:', error);
      throw error;
    }
  }

  /**
   * Generate alternative layout options
   */
  private async generateAlternativeLayouts(
    roomLayout: RoomLayout,
    config: MeasurementConfig
  ): Promise<SpaceOptimization['alternativeLayouts']> {
    const alternatives: SpaceOptimization['alternativeLayouts'] = [];

    // Galley layout alternative
    alternatives.push({
      name: 'Galley Layout',
      description: 'Linear kitchen with parallel work surfaces for maximum efficiency',
      efficiency: 0.88,
      changes: [
        'Relocate appliances to opposite walls',
        'Create central aisle for traffic flow',
        'Maximize counter space on both sides'
      ]
    });

    // L-shaped layout alternative
    alternatives.push({
      name: 'L-Shaped Layout',
      description: 'Corner configuration optimizing work triangle and storage',
      efficiency: 0.85,
      changes: [
        'Position appliances at corner intersection',
        'Add corner storage solutions',
        'Open up traffic flow to adjacent rooms'
      ]
    });

    // Island layout alternative (if space permits)
    if (this.hasSpaceForIsland(roomLayout)) {
      alternatives.push({
        name: 'Island Layout',
        description: 'Central island for additional prep space and storage',
        efficiency: 0.92,
        changes: [
          'Add central island with prep sink',
          'Relocate cooktop to island',
          'Create multiple work zones'
        ]
      });
    }

    return alternatives;
  }

  /**
   * Validate measurements across multiple sources
   */
  private async validateMeasurements(
    imagePaths: string[],
    scaleDetection: { detectedScales: ScaleReference[]; primaryScale: ScaleReference | null; confidence: number },
    analysisId: string,
    config: MeasurementConfig
  ): Promise<MeasurementValidation> {
    try {
      // Extract dimensions from PDF/images using existing enhanced processor
      const dimensionData = await this.extractDimensionsForValidation(imagePaths);

      // Cross-reference measurements
      const crossReferences = this.performCrossReferenceValidation(dimensionData, scaleDetection);

      // Identify inconsistencies
      const inconsistencies = this.identifyMeasurementInconsistencies(crossReferences, config);

      // Calculate overall accuracy
      const accuracy = this.calculateMeasurementAccuracy(crossReferences, inconsistencies);

      const validation: MeasurementValidation = {
        accuracy,
        crossReferences,
        inconsistencies
      };

      logger.info(`Measurement validation completed: ${analysisId}`, {
        accuracy,
        crossReferencesCount: crossReferences.length,
        inconsistenciesCount: inconsistencies.length
      });

      return validation;

    } catch (error) {
      logger.error(`Measurement validation failed: ${analysisId}`, error);
      return this.getDefaultMeasurementValidation();
    }
  }

  /**
   * Extract dimensions for validation
   */
  private async extractDimensionsForValidation(imagePaths: string[]): Promise<any[]> {
    const allDimensions: any[] = [];

    for (const imagePath of imagePaths) {
      try {
        // Use existing enhanced PDF processor for dimension extraction
        const dimensions = await enhancedPdfProcessor.detectDimensions(
          await enhancedPdfProcessor.extractTextContent(imagePath)
        );
        allDimensions.push(...dimensions);
      } catch (error) {
        logger.warn(`Failed to extract dimensions from ${imagePath}:`, error);
      }
    }

    return allDimensions;
  }

  /**
   * Perform cross-reference validation
   */
  private performCrossReferenceValidation(
    dimensionData: any[],
    scaleDetection: { detectedScales: ScaleReference[]; primaryScale: ScaleReference | null; confidence: number }
  ): MeasurementValidation['crossReferences'] {
    const crossReferences: MeasurementValidation['crossReferences'] = [];

    // Compare dimensions from different sources
    for (let i = 0; i < dimensionData.length; i++) {
      for (let j = i + 1; j < dimensionData.length; j++) {
        const dim1 = dimensionData[i];
        const dim2 = dimensionData[j];

        // Check if dimensions are measuring similar features
        if (this.areSimilarMeasurements(dim1, dim2)) {
          const variance = Math.abs(dim1.value - dim2.value) / Math.max(dim1.value, dim2.value);
          const confidence = Math.max(0, 1 - variance * 2); // Higher variance = lower confidence

          crossReferences.push({
            source1: `Dimension ${i + 1}`,
            source2: `Dimension ${j + 1}`,
            variance,
            confidence
          });
        }
      }
    }

    return crossReferences;
  }

  /**
   * Check if two measurements are similar
   */
  private areSimilarMeasurements(dim1: any, dim2: any): boolean {
    // Simple heuristic: measurements are similar if they're within 20% of each other
    const ratio = Math.min(dim1.value, dim2.value) / Math.max(dim1.value, dim2.value);
    return ratio > 0.8;
  }

  /**
   * Identify measurement inconsistencies
   */
  private identifyMeasurementInconsistencies(
    crossReferences: MeasurementValidation['crossReferences'],
    config: MeasurementConfig
  ): MeasurementValidation['inconsistencies'] {
    const inconsistencies: MeasurementValidation['inconsistencies'] = [];

    for (const ref of crossReferences) {
      if (ref.variance > 0.1) { // 10% variance threshold
        const severity: 'LOW' | 'MEDIUM' | 'HIGH' =
          ref.variance > 0.3 ? 'HIGH' : ref.variance > 0.2 ? 'MEDIUM' : 'LOW';

        inconsistencies.push({
          description: `Significant variance (${(ref.variance * 100).toFixed(1)}%) between ${ref.source1} and ${ref.source2}`,
          severity,
          suggestions: [
            'Verify measurement accuracy in source documents',
            'Check for different measurement standards (metric vs imperial)',
            'Consider scale factor discrepancies'
          ]
        });
      }
    }

    return inconsistencies;
  }

  /**
   * Calculate measurement accuracy
   */
  private calculateMeasurementAccuracy(
    crossReferences: MeasurementValidation['crossReferences'],
    inconsistencies: MeasurementValidation['inconsistencies']
  ): number {
    if (crossReferences.length === 0) return 0.5; // Default when no cross-references available

    const avgConfidence = crossReferences.reduce((sum, ref) => sum + ref.confidence, 0) / crossReferences.length;
    const inconsistencyPenalty = inconsistencies.length * 0.1;

    return Math.max(0, Math.min(1, avgConfidence - inconsistencyPenalty));
  }

  /**
   * Helper methods for default values
   */
  private getDefaultRoomLayout(): RoomLayout {
    return {
      boundaries: [],
      workTriangle: {
        sink: { x: 0, y: 0, confidence: 0 },
        stove: { x: 0, y: 0, confidence: 0 },
        refrigerator: { x: 0, y: 0, confidence: 0 },
        efficiency: 0.5
      },
      trafficFlow: {
        primaryPaths: [],
        bottlenecks: [],
        clearanceIssues: []
      },
      zones: {
        cooking: [],
        preparation: [],
        storage: [],
        cleanup: []
      }
    };
  }

  private getDefaultSpaceOptimization(): SpaceOptimization {
    return {
      currentEfficiency: 0.5,
      recommendations: []
    };
  }

  private getDefaultMeasurementValidation(): MeasurementValidation {
    return {
      accuracy: 0.5,
      crossReferences: [],
      inconsistencies: []
    };
  }

  private calculateOverallConfidence(
    scaleDetection: { confidence: number },
    roomLayout: RoomLayout,
    spaceOptimization: SpaceOptimization,
    measurementValidation: MeasurementValidation
  ): number {
    const weights = {
      scale: 0.3,
      layout: 0.3,
      optimization: 0.2,
      validation: 0.2
    };

    return (
      scaleDetection.confidence * weights.scale +
      roomLayout.workTriangle.efficiency * weights.layout +
      spaceOptimization.currentEfficiency * weights.optimization +
      measurementValidation.accuracy * weights.validation
    );
  }

  private getDetectedFeatures(config: MeasurementConfig): string[] {
    const features: string[] = [];

    if (config.enableAutoScale) features.push('Auto-Scale Detection');
    if (config.enableLayoutAnalysis) features.push('Room Layout Analysis');
    if (config.enableSpaceOptimization) features.push('Space Optimization');
    if (config.enableMeasurementValidation) features.push('Measurement Validation');

    return features;
  }

  private hasSpaceForIsland(roomLayout: RoomLayout): boolean {
    // Simple heuristic: check if room boundaries suggest sufficient space
    const boundaries = roomLayout.boundaries;
    if (boundaries.length < 4) return false;

    // Calculate approximate room area (simplified)
    const width = Math.max(...boundaries.map(b => b.x)) - Math.min(...boundaries.map(b => b.x));
    const height = Math.max(...boundaries.map(b => b.y)) - Math.min(...boundaries.map(b => b.y));

    // Require minimum 12x12 feet (approximately 144 square feet) for island
    return width * height > 20000; // Assuming pixel measurements
  }
}

// Export singleton instance
export const intelligentMeasurementService = new IntelligentMeasurementService();
