import { PricingDatabaseService, MaterialPricing, HardwarePricing, LaborRate, RegionalFactor } from './pricingDatabaseService';
import { createModuleLogger } from '../utils/logger';
import Decimal from 'decimal.js';
import Currency from 'currency.js';
import { v4 as uuidv4 } from 'uuid';

const logger = createModuleLogger('QuotationService');

// Configure Currency.js for NZD
const NZD = (value: any) => Currency(value, {
  symbol: 'NZD $',
  precision: 2,
  separator: ',',
  decimal: '.',
  formatWithSymbol: true
});

export interface CabinetComponent {
  type: 'upper' | 'lower' | 'pantry' | 'island' | 'vanity';
  count: number;
  dimensions?: {
    width: number;
    height: number;
    depth: number;
  };
  material?: string;
  finish?: string;
  hardware?: string[];
  confidence: number;
}

export interface AnalysisInput {
  cabinets: CabinetComponent[];
  materials: {
    primary: string;
    secondary?: string;
    finish: string;
    quality: 'basic' | 'standard' | 'premium' | 'luxury';
  };
  hardware: {
    hinges: string[];
    handles: string[];
    slides: string[];
    other: string[];
  };
  measurements: {
    totalLinearFeet?: number;
    squareFootage?: number;
    complexity: 'simple' | 'moderate' | 'complex' | 'very_complex';
  };
  installation: {
    complexity: 'basic' | 'standard' | 'complex' | 'custom';
    accessRequirements: string[];
  };
}

export interface QuoteTier {
  tier: 'basic' | 'premium' | 'luxury';
  name: string;
  description: string;
  materials: {
    cost: Decimal;
    items: Array<{
      description: string;
      quantity: number;
      unitPrice: Decimal;
      totalPrice: Decimal;
    }>;
  };
  hardware: {
    cost: Decimal;
    items: Array<{
      description: string;
      quantity: number;
      unitPrice: Decimal;
      totalPrice: Decimal;
    }>;
  };
  labor: {
    cost: Decimal;
    items: Array<{
      description: string;
      hours: number;
      hourlyRate: Decimal;
      totalPrice: Decimal;
    }>;
  };
  subtotal: Decimal;
  taxes: Decimal;
  total: Decimal;
  confidence: number;
}

export interface QuoteResult {
  id: string;
  analysisId: string;
  projectId?: string;
  tiers: QuoteTier[];
  summary: {
    cabinetCount: number;
    linearFeet: number;
    complexity: string;
    estimatedTimeframe: string;
  };
  alternatives: Array<{
    description: string;
    impact: string;
    costDifference: Decimal;
  }>;
  confidence: number;
  createdAt: Date;
  validUntil: Date;
}

export class QuotationService {
  private static instance: QuotationService;
  private pricingDb: PricingDatabaseService;
  private defaultRegion = 'NZ_NORTH'; // Default to North Island NZ

  private constructor() {
    this.pricingDb = PricingDatabaseService.getInstance();
  }

  public static getInstance(): QuotationService {
    if (!QuotationService.instance) {
      QuotationService.instance = new QuotationService();
    }
    return QuotationService.instance;
  }

  /**
   * Generate comprehensive quote from AI analysis results
   */
  public async generateQuote(
    analysisInput: AnalysisInput,
    analysisId: string,
    projectId?: string,
    regionCode?: string
  ): Promise<QuoteResult> {
    const startTime = Date.now();
    
    logger.info(`Generating quote for analysis: ${analysisId}`, {
      cabinetCount: analysisInput.cabinets.reduce((sum, c) => sum + c.count, 0),
      complexity: analysisInput.measurements.complexity
    });

    // Check if pricing database is available
    if (!(await this.pricingDb.isAvailable())) {
      throw new Error('Pricing database unavailable - quotation service disabled');
    }

    try {
      // Get regional factors
      const region = await this.getRegionalFactor(regionCode || this.defaultRegion);
      
      // Generate all three tiers
      const basicTier = await this.generateTier('basic', analysisInput, region);
      const premiumTier = await this.generateTier('premium', analysisInput, region);
      const luxuryTier = await this.generateTier('luxury', analysisInput, region);

      // Calculate summary metrics
      const summary = this.calculateSummary(analysisInput);
      
      // Generate alternatives
      const alternatives = await this.generateAlternatives(analysisInput, basicTier);

      // Calculate overall confidence
      const confidence = this.calculateOverallConfidence(analysisInput, [basicTier, premiumTier, luxuryTier]);

      const quote: QuoteResult = {
        id: uuidv4(),
        analysisId,
        projectId,
        tiers: [basicTier, premiumTier, luxuryTier],
        summary,
        alternatives,
        confidence,
        createdAt: new Date(),
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
      };

      const processingTime = Date.now() - startTime;
      logger.info(`Quote generated successfully in ${processingTime}ms`, {
        quoteId: quote.id,
        basicTotal: this.pricingDb.formatNZD(basicTier.total),
        premiumTotal: this.pricingDb.formatNZD(premiumTier.total),
        luxuryTotal: this.pricingDb.formatNZD(luxuryTier.total),
        confidence: confidence
      });

      return quote;

    } catch (error) {
      logger.error(`Quote generation failed for analysis: ${analysisId}`, error);
      throw error;
    }
  }

  /**
   * Generate a specific pricing tier
   */
  private async generateTier(
    tier: 'basic' | 'premium' | 'luxury',
    analysisInput: AnalysisInput,
    region: RegionalFactor
  ): Promise<QuoteTier> {
    
    // Calculate materials cost
    const materials = await this.calculateMaterialsCost(analysisInput, tier, region);
    
    // Calculate hardware cost
    const hardware = await this.calculateHardwareCost(analysisInput, tier, region);
    
    // Calculate labor cost
    const labor = await this.calculateLaborCost(analysisInput, tier, region);

    // Calculate subtotal
    const subtotal = materials.cost.plus(hardware.cost).plus(labor.cost);
    
    // Apply regional tax rate
    const taxes = subtotal.times(region.tax_rate);
    
    // Calculate total
    const total = subtotal.plus(taxes);

    // Calculate tier-specific confidence
    const confidence = this.calculateTierConfidence(analysisInput, tier);

    return {
      tier,
      name: this.getTierName(tier),
      description: this.getTierDescription(tier),
      materials,
      hardware,
      labor,
      subtotal,
      taxes,
      total,
      confidence
    };
  }

  /**
   * Calculate materials cost for a tier
   */
  private async calculateMaterialsCost(
    analysisInput: AnalysisInput,
    tier: 'basic' | 'premium' | 'luxury',
    region: RegionalFactor
  ): Promise<QuoteTier['materials']> {
    const items: QuoteTier['materials']['items'] = [];
    let totalCost = new Decimal(0);

    // Map AI-detected materials to pricing database
    const materialSearchTerms = this.extractMaterialSearchTerms(analysisInput, tier);
    
    for (const searchTerm of materialSearchTerms) {
      const materials = await this.pricingDb.searchMaterials(searchTerm, 3);
      
      if (materials.length > 0) {
        // Select material based on tier
        const selectedMaterial = this.selectMaterialByTier(materials, tier);
        
        // Calculate quantity based on cabinet count and dimensions
        const quantity = this.calculateMaterialQuantity(analysisInput, selectedMaterial);
        
        // Apply regional multipliers
        const adjustedPrice = selectedMaterial.base_price
          .times(region.cost_of_living_multiplier)
          .times(region.shipping_multiplier);
        
        const itemTotal = adjustedPrice.times(quantity);
        
        items.push({
          description: `${selectedMaterial.material_type} - ${selectedMaterial.finish || selectedMaterial.grade}`,
          quantity,
          unitPrice: adjustedPrice,
          totalPrice: itemTotal
        });
        
        totalCost = totalCost.plus(itemTotal);
      }
    }

    // Add fallback pricing if no materials found
    if (items.length === 0) {
      const fallbackCost = this.calculateFallbackMaterialCost(analysisInput, tier, region);
      items.push({
        description: `${tier.charAt(0).toUpperCase() + tier.slice(1)} Cabinet Materials (estimated)`,
        quantity: analysisInput.cabinets.reduce((sum, c) => sum + c.count, 0),
        unitPrice: fallbackCost.unitPrice,
        totalPrice: fallbackCost.total
      });
      totalCost = fallbackCost.total;
    }

    return {
      cost: totalCost,
      items
    };
  }

  /**
   * Calculate hardware cost for a tier
   */
  private async calculateHardwareCost(
    analysisInput: AnalysisInput,
    tier: 'basic' | 'premium' | 'luxury',
    region: RegionalFactor
  ): Promise<QuoteTier['hardware']> {
    const items: QuoteTier['hardware']['items'] = [];
    let totalCost = new Decimal(0);

    // Map AI-detected hardware to pricing database
    const hardwareSearchTerms = this.extractHardwareSearchTerms(analysisInput, tier);
    
    for (const searchTerm of hardwareSearchTerms) {
      const hardware = await this.pricingDb.searchHardware(searchTerm, 3);
      
      if (hardware.length > 0) {
        // Select hardware based on tier
        const selectedHardware = this.selectHardwareByTier(hardware, tier);
        
        // Calculate quantity based on cabinet count and type
        const quantity = this.calculateHardwareQuantity(analysisInput, selectedHardware);
        
        // Apply regional multipliers
        const adjustedPrice = selectedHardware.unit_price
          .times(region.cost_of_living_multiplier)
          .times(region.shipping_multiplier);
        
        const itemTotal = adjustedPrice.times(quantity);
        
        items.push({
          description: `${selectedHardware.brand} ${selectedHardware.model || selectedHardware.category}`,
          quantity,
          unitPrice: adjustedPrice,
          totalPrice: itemTotal
        });
        
        totalCost = totalCost.plus(itemTotal);
      }
    }

    // Add fallback pricing if no hardware found
    if (items.length === 0) {
      const fallbackCost = this.calculateFallbackHardwareCost(analysisInput, tier, region);
      items.push({
        description: `${tier.charAt(0).toUpperCase() + tier.slice(1)} Cabinet Hardware (estimated)`,
        quantity: analysisInput.cabinets.reduce((sum, c) => sum + c.count, 0),
        unitPrice: fallbackCost.unitPrice,
        totalPrice: fallbackCost.total
      });
      totalCost = fallbackCost.total;
    }

    return {
      cost: totalCost,
      items
    };
  }

  /**
   * Calculate labor cost for a tier
   */
  private async calculateLaborCost(
    analysisInput: AnalysisInput,
    tier: 'basic' | 'premium' | 'luxury',
    region: RegionalFactor
  ): Promise<QuoteTier['labor']> {
    const items: QuoteTier['labor']['items'] = [];
    let totalCost = new Decimal(0);

    // Get labor rates for cabinet installation
    const laborRates = await this.pricingDb.getLaborRates('installation', region.id);
    
    if (laborRates.length > 0) {
      // Select appropriate skill level based on tier and complexity
      const skillLevel = this.getRequiredSkillLevel(tier, analysisInput.installation.complexity);
      const laborRate = laborRates.find(lr => lr.skill_level === skillLevel) || laborRates[0];
      
      // Calculate hours based on cabinet count and complexity
      const estimatedHours = this.calculateLaborHours(analysisInput, tier);
      
      // Apply complexity multiplier
      const adjustedRate = laborRate.hourly_rate.times(laborRate.complexity_multiplier);
      const itemTotal = adjustedRate.times(estimatedHours);
      
      items.push({
        description: `Cabinet Installation (${skillLevel} level)`,
        hours: estimatedHours.toNumber(),
        hourlyRate: adjustedRate,
        totalPrice: itemTotal
      });
      
      totalCost = totalCost.plus(itemTotal);
    } else {
      // Fallback labor calculation
      const fallbackCost = this.calculateFallbackLaborCost(analysisInput, tier, region);
      items.push({
        description: `Cabinet Installation (estimated)`,
        hours: fallbackCost.hours,
        hourlyRate: fallbackCost.hourlyRate,
        totalPrice: fallbackCost.total
      });
      totalCost = fallbackCost.total;
    }

    return {
      cost: totalCost,
      items
    };
  }

  // Helper methods will be added in the next part due to file size limit
  private getTierName(tier: 'basic' | 'premium' | 'luxury'): string {
    const names = {
      basic: 'Essential Package',
      premium: 'Professional Package', 
      luxury: 'Designer Package'
    };
    return names[tier];
  }

  private getTierDescription(tier: 'basic' | 'premium' | 'luxury'): string {
    const descriptions = {
      basic: 'Quality materials and standard hardware for budget-conscious projects',
      premium: 'Enhanced materials and premium hardware for superior durability and style',
      luxury: 'Top-tier materials and designer hardware for the ultimate kitchen experience'
    };
    return descriptions[tier];
  }

  private async getRegionalFactor(regionCode: string): Promise<RegionalFactor> {
    const region = await this.pricingDb.getRegionalFactor(regionCode);

    if (!region) {
      // Return default NZ factors if region not found
      return {
        id: 1,
        region_code: 'NZ_DEFAULT',
        region_name: 'New Zealand Default',
        cost_of_living_multiplier: new Decimal(1.0),
        tax_rate: new Decimal(0.15), // 15% GST
        shipping_multiplier: new Decimal(1.0),
        market_conditions: 'average',
        seasonal_adjustment: new Decimal(1.0)
      };
    }

    return region;
  }

  // AI Analysis to Pricing Mapping Methods
  private extractMaterialSearchTerms(analysisInput: AnalysisInput, tier: 'basic' | 'premium' | 'luxury'): string[] {
    const terms: string[] = [];

    // Primary material mapping
    const materialMap: Record<string, string[]> = {
      'mdf': ['MDF', 'Medium Density Fibreboard'],
      'plywood': ['Plywood', 'Ply'],
      'melamine': ['Melamine', 'Mel'],
      'veneer': ['Veneer', 'Wood Grain'],
      'paint': ['Paint', 'Painted'],
      'laminate': ['Laminate', 'HPL'],
      'solid wood': ['Solid Wood', 'Timber'],
      'thermofoil': ['Thermofoil', 'Vinyl Wrap']
    };

    // Extract from primary material
    const primaryMaterial = analysisInput.materials.primary.toLowerCase();
    for (const [key, searchTerms] of Object.entries(materialMap)) {
      if (primaryMaterial.includes(key)) {
        terms.push(...searchTerms);
      }
    }

    // Add finish-specific terms
    const finish = analysisInput.materials.finish.toLowerCase();
    if (finish.includes('white')) terms.push('White', 'Wh');
    if (finish.includes('grey') || finish.includes('gray')) terms.push('Grey', 'Gray');
    if (finish.includes('black')) terms.push('Black', 'Anthracite');
    if (finish.includes('wood')) terms.push('Wood Grain', 'Timber');

    // Add tier-specific material preferences
    if (tier === 'basic') {
      terms.push('16mm', '18mm', 'Standard');
    } else if (tier === 'premium') {
      terms.push('18mm', '20mm', 'Premium');
    } else if (tier === 'luxury') {
      terms.push('20mm', '25mm', 'Designer', 'High Grade');
    }

    return [...new Set(terms)]; // Remove duplicates
  }

  private extractHardwareSearchTerms(analysisInput: AnalysisInput, tier: 'basic' | 'premium' | 'luxury'): string[] {
    const terms: string[] = [];

    // Brand mapping from Enhanced Smart Hardware Recognition
    const brandMap: Record<string, string[]> = {
      'blum': ['Blum'],
      'hettich': ['Hettich'],
      'grass': ['Grass'],
      'salice': ['Salice'],
      'hafele': ['Hafele', 'Häfele']
    };

    // Extract hardware brands
    const allHardware = [
      ...analysisInput.hardware.hinges,
      ...analysisInput.hardware.handles,
      ...analysisInput.hardware.slides,
      ...analysisInput.hardware.other
    ];

    for (const hardware of allHardware) {
      const hardwareLower = hardware.toLowerCase();
      for (const [key, searchTerms] of Object.entries(brandMap)) {
        if (hardwareLower.includes(key)) {
          terms.push(...searchTerms);
        }
      }
    }

    // Add generic hardware terms based on tier
    if (tier === 'basic') {
      terms.push('Hinge', 'Handle', 'Slide', 'Standard');
    } else if (tier === 'premium') {
      terms.push('Soft Close', 'Premium', 'European');
    } else if (tier === 'luxury') {
      terms.push('Designer', 'High End', 'Luxury', 'Custom');
    }

    return [...new Set(terms)];
  }

  private selectMaterialByTier(materials: MaterialPricing[], tier: 'basic' | 'premium' | 'luxury'): MaterialPricing {
    if (materials.length === 1) return materials[0];

    // Sort by price
    const sorted = materials.sort((a, b) => a.base_price.comparedTo(b.base_price));

    switch (tier) {
      case 'basic':
        return sorted[0]; // Lowest price
      case 'premium':
        return sorted[Math.floor(sorted.length / 2)]; // Middle price
      case 'luxury':
        return sorted[sorted.length - 1]; // Highest price
    }
  }

  private selectHardwareByTier(hardware: HardwarePricing[], tier: 'basic' | 'premium' | 'luxury'): HardwarePricing {
    if (hardware.length === 1) return hardware[0];

    // Sort by price
    const sorted = hardware.sort((a, b) => a.unit_price.comparedTo(b.unit_price));

    switch (tier) {
      case 'basic':
        return sorted[0]; // Lowest price
      case 'premium':
        return sorted[Math.floor(sorted.length / 2)]; // Middle price
      case 'luxury':
        return sorted[sorted.length - 1]; // Highest price
    }
  }

  private calculateMaterialQuantity(analysisInput: AnalysisInput, material: MaterialPricing): number {
    const totalCabinets = analysisInput.cabinets.reduce((sum, c) => sum + c.count, 0);

    // Base quantity calculation
    let quantity = totalCabinets;

    // Adjust based on unit of measure
    if (material.unit_of_measure === 'sheet') {
      // Estimate sheets needed based on cabinet count and size
      quantity = Math.ceil(totalCabinets * 0.8); // Average 0.8 sheets per cabinet
    } else if (material.unit_of_measure === 'linear_meter') {
      // Use linear feet if available, otherwise estimate
      quantity = analysisInput.measurements.totalLinearFeet || totalCabinets * 2;
    }

    return Math.max(1, quantity);
  }

  private calculateHardwareQuantity(analysisInput: AnalysisInput, hardware: HardwarePricing): number {
    const totalCabinets = analysisInput.cabinets.reduce((sum, c) => sum + c.count, 0);

    // Hardware quantity multipliers based on type
    const multipliers: Record<string, number> = {
      'hinge': 2, // 2 hinges per door on average
      'handle': 1, // 1 handle per door/drawer
      'slide': 1, // 1 slide per drawer
      'knob': 1,
      'pull': 1
    };

    const category = hardware.category.toLowerCase();
    const multiplier = multipliers[category] || 1;

    return Math.max(1, totalCabinets * multiplier);
  }

  private getRequiredSkillLevel(tier: 'basic' | 'premium' | 'luxury', complexity: string): string {
    const skillMap = {
      basic: { basic: 'apprentice', standard: 'journeyman', complex: 'journeyman', custom: 'journeyman' },
      premium: { basic: 'journeyman', standard: 'journeyman', complex: 'expert', custom: 'expert' },
      luxury: { basic: 'journeyman', standard: 'expert', complex: 'expert', custom: 'master' }
    };

    return skillMap[tier][complexity as keyof typeof skillMap.basic] || 'journeyman';
  }

  private calculateLaborHours(analysisInput: AnalysisInput, tier: 'basic' | 'premium' | 'luxury'): Decimal {
    const totalCabinets = analysisInput.cabinets.reduce((sum, c) => sum + c.count, 0);

    // Base hours per cabinet
    const baseHours = {
      basic: 2,
      premium: 3,
      luxury: 4
    };

    // Complexity multipliers
    const complexityMultipliers = {
      simple: 1.0,
      moderate: 1.3,
      complex: 1.6,
      very_complex: 2.0
    };

    const hours = totalCabinets * baseHours[tier] * complexityMultipliers[analysisInput.measurements.complexity];
    return new Decimal(Math.max(4, hours)); // Minimum 4 hours
  }

  // Fallback pricing methods for when database lookups fail
  private calculateFallbackMaterialCost(
    analysisInput: AnalysisInput,
    tier: 'basic' | 'premium' | 'luxury',
    region: RegionalFactor
  ): { unitPrice: Decimal; total: Decimal } {
    const totalCabinets = analysisInput.cabinets.reduce((sum, c) => sum + c.count, 0);

    // Fallback pricing per cabinet based on tier (NZD)
    const fallbackPrices = {
      basic: 800,
      premium: 1200,
      luxury: 1800
    };

    const basePrice = new Decimal(fallbackPrices[tier]);
    const adjustedPrice = basePrice
      .times(region.cost_of_living_multiplier)
      .times(region.shipping_multiplier);

    return {
      unitPrice: adjustedPrice,
      total: adjustedPrice.times(totalCabinets)
    };
  }

  private calculateFallbackHardwareCost(
    analysisInput: AnalysisInput,
    tier: 'basic' | 'premium' | 'luxury',
    region: RegionalFactor
  ): { unitPrice: Decimal; total: Decimal } {
    const totalCabinets = analysisInput.cabinets.reduce((sum, c) => sum + c.count, 0);

    // Fallback hardware pricing per cabinet based on tier (NZD)
    const fallbackPrices = {
      basic: 150,
      premium: 300,
      luxury: 500
    };

    const basePrice = new Decimal(fallbackPrices[tier]);
    const adjustedPrice = basePrice
      .times(region.cost_of_living_multiplier)
      .times(region.shipping_multiplier);

    return {
      unitPrice: adjustedPrice,
      total: adjustedPrice.times(totalCabinets)
    };
  }

  private calculateFallbackLaborCost(
    analysisInput: AnalysisInput,
    tier: 'basic' | 'premium' | 'luxury',
    region: RegionalFactor
  ): { hours: number; hourlyRate: Decimal; total: Decimal } {
    const estimatedHours = this.calculateLaborHours(analysisInput, tier);

    // Fallback hourly rates based on tier (NZD)
    const fallbackRates = {
      basic: 45,
      premium: 65,
      luxury: 85
    };

    const hourlyRate = new Decimal(fallbackRates[tier])
      .times(region.cost_of_living_multiplier);

    return {
      hours: estimatedHours.toNumber(),
      hourlyRate,
      total: hourlyRate.times(estimatedHours)
    };
  }

  // Summary and confidence calculation methods
  private calculateSummary(analysisInput: AnalysisInput): QuoteResult['summary'] {
    const cabinetCount = analysisInput.cabinets.reduce((sum, c) => sum + c.count, 0);
    const linearFeet = analysisInput.measurements.totalLinearFeet || cabinetCount * 2;

    // Estimate timeframe based on complexity and cabinet count
    const timeframeWeeks = Math.ceil((cabinetCount * 0.5) +
      (analysisInput.measurements.complexity === 'very_complex' ? 2 :
       analysisInput.measurements.complexity === 'complex' ? 1 : 0));

    return {
      cabinetCount,
      linearFeet,
      complexity: analysisInput.measurements.complexity,
      estimatedTimeframe: `${timeframeWeeks}-${timeframeWeeks + 1} weeks`
    };
  }

  private async generateAlternatives(
    analysisInput: AnalysisInput,
    basicTier: QuoteTier
  ): Promise<QuoteResult['alternatives']> {
    const alternatives: QuoteResult['alternatives'] = [];

    // Material alternatives
    if (analysisInput.materials.quality !== 'basic') {
      const savings = basicTier.materials.cost.times(0.15); // 15% savings estimate
      alternatives.push({
        description: 'Use standard melamine instead of premium materials',
        impact: 'Reduced material cost with standard durability',
        costDifference: savings.negated()
      });
    }

    // Hardware alternatives
    const hardwareSavings = basicTier.hardware.cost.times(0.25); // 25% savings estimate
    alternatives.push({
      description: 'Standard hinges instead of soft-close hardware',
      impact: 'Lower cost but reduced convenience features',
      costDifference: hardwareSavings.negated()
    });

    // Installation alternatives
    if (analysisInput.installation.complexity !== 'basic') {
      const laborSavings = basicTier.labor.cost.times(0.20); // 20% savings estimate
      alternatives.push({
        description: 'Simplified installation approach',
        impact: 'Reduced labor cost with standard installation methods',
        costDifference: laborSavings.negated()
      });
    }

    return alternatives;
  }

  private calculateTierConfidence(analysisInput: AnalysisInput, tier: 'basic' | 'premium' | 'luxury'): number {
    let confidence = 0.8; // Base confidence

    // Adjust based on material recognition confidence
    const avgMaterialConfidence = analysisInput.cabinets.reduce((sum, c) => sum + c.confidence, 0) / analysisInput.cabinets.length;
    confidence *= avgMaterialConfidence;

    // Adjust based on measurement availability
    if (analysisInput.measurements.totalLinearFeet) {
      confidence += 0.1;
    }
    if (analysisInput.measurements.squareFootage) {
      confidence += 0.05;
    }

    // Adjust based on hardware recognition
    const hardwareCount = Object.values(analysisInput.hardware).flat().length;
    if (hardwareCount > 0) {
      confidence += Math.min(0.1, hardwareCount * 0.02);
    }

    return Math.min(0.95, Math.max(0.6, confidence));
  }

  private calculateOverallConfidence(analysisInput: AnalysisInput, tiers: QuoteTier[]): number {
    const tierConfidences = tiers.map(t => t.confidence);
    const avgTierConfidence = tierConfidences.reduce((sum, c) => sum + c, 0) / tierConfidences.length;

    // Overall confidence is slightly lower than individual tier confidence
    return Math.max(0.6, avgTierConfidence * 0.95);
  }

  /**
   * Convert AI analysis results to QuotationService input format
   */
  public static convertAnalysisToInput(analysisData: any): AnalysisInput {
    // Extract cabinet information
    const cabinets: CabinetComponent[] = [];

    if (analysisData.cabinetCount) {
      // Parse cabinet count data
      const cabinetTypes = ['upper', 'lower', 'pantry', 'island', 'vanity'] as const;

      for (const type of cabinetTypes) {
        const count = analysisData.cabinetCount[type] || 0;
        if (count > 0) {
          cabinets.push({
            type,
            count,
            dimensions: analysisData.dimensions?.[type],
            material: analysisData.materials?.primary,
            finish: analysisData.materials?.finish,
            hardware: analysisData.hardware ? Object.values(analysisData.hardware).flat() : [],
            confidence: analysisData.confidence || 0.8
          });
        }
      }
    }

    // Fallback if no specific cabinet data found
    if (cabinets.length === 0) {
      const totalCount = analysisData.totalCabinets || 10; // Default estimate
      cabinets.push({
        type: 'lower',
        count: Math.ceil(totalCount * 0.6), // 60% lower cabinets
        material: analysisData.materials?.primary || 'melamine',
        finish: analysisData.materials?.finish || 'white',
        hardware: [],
        confidence: 0.7
      });
      cabinets.push({
        type: 'upper',
        count: Math.floor(totalCount * 0.4), // 40% upper cabinets
        material: analysisData.materials?.primary || 'melamine',
        finish: analysisData.materials?.finish || 'white',
        hardware: [],
        confidence: 0.7
      });
    }

    // Extract materials information
    const materials = {
      primary: analysisData.materials?.primary || 'melamine',
      secondary: analysisData.materials?.secondary,
      finish: analysisData.materials?.finish || 'white',
      quality: (analysisData.materials?.quality || 'standard') as 'basic' | 'standard' | 'premium' | 'luxury'
    };

    // Extract hardware information
    const hardware = {
      hinges: analysisData.hardware?.hinges || [],
      handles: analysisData.hardware?.handles || [],
      slides: analysisData.hardware?.slides || [],
      other: analysisData.hardware?.other || []
    };

    // Extract measurements
    const measurements = {
      totalLinearFeet: analysisData.measurements?.linearFeet,
      squareFootage: analysisData.measurements?.squareFootage,
      complexity: (analysisData.complexity || 'moderate') as 'simple' | 'moderate' | 'complex' | 'very_complex'
    };

    // Extract installation requirements
    const installation = {
      complexity: (analysisData.installation?.complexity || 'standard') as 'basic' | 'standard' | 'complex' | 'custom',
      accessRequirements: analysisData.installation?.accessRequirements || []
    };

    return {
      cabinets,
      materials,
      hardware,
      measurements,
      installation
    };
  }
}
