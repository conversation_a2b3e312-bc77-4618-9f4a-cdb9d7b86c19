/**
 * OpenAI Services Module
 * 
 * Modular OpenAI service architecture providing:
 * - Client management and initialization
 * - Intelligent model selection
 * - Complex reasoning capabilities
 * - Enhanced analysis orchestration
 * - Configuration management
 * 
 * This module replaces the monolithic OpenAI service with a clean,
 * maintainable, and testable architecture while preserving backward compatibility.
 */

// Core Services
export { OpenAIClientManager, openAIClientManager } from './OpenAIClientManager';
export { ModelSelectionService, modelSelectionService } from './ModelSelectionService';
export { ComplexReasoningService, complexReasoningService } from './ComplexReasoningService';
export { OpenAIAnalysisService, openAIAnalysisService } from './OpenAIAnalysisService';
export { OpenAIConfigService, openAIConfigService } from './OpenAIConfigService';

// Main Service (Facade)
export { OpenAIService, openaiService } from './OpenAIService';

// Types and Interfaces
export type {
  ClientConfiguration,
  ClientInstances
} from './OpenAIClientManager';

export type {
  AnalysisConfig,
  ModelSelection,
  ComplexityAnalysis
} from './ModelSelectionService';

export type {
  VisionAnalysisResult,
  ReasoningContext,
  ReasoningVisualizationData,
  ReasoningStep
} from './ComplexReasoningService';

export type {
  EnhancedAnalysisResult
} from './OpenAIAnalysisService';

export type {
  OpenAIEnvironmentConfig,
  ServiceDefaults,
  ConfigValidationResult
} from './OpenAIConfigService';

// Default export for backward compatibility
export { openaiService as default } from './OpenAIService';
