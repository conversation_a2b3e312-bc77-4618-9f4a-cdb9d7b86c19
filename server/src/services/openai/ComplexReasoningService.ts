import fs from 'fs';
import { createModuleLogger } from '@/utils/logger';
import { modelSelectionService, AnalysisConfig } from './ModelSelectionService';
import { GPTCacheService } from '../gptCacheService';

const logger = createModuleLogger('ComplexReasoningService');

export interface VisionAnalysisResult {
  content: string;
  model: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  confidence: number;
  processingTime: number;
}

export interface ReasoningContext {
  analysisType: string;
  complexityFactors: string[];
  expectedOutcomes: string[];
}

export interface ReasoningVisualizationData {
  chainId: string;
  steps: ReasoningStep[];
  processingTime: number;
  confidence: number;
}

export interface ReasoningStep {
  step: string;
  reasoning: string;
  confidence: number;
  data: any;
  timestamp: number;
}

/**
 * Complex Reasoning Service
 * 
 * Handles GPT-o1 specific complex reasoning analysis with reasoning chain visualization.
 * Provides advanced multi-step reasoning capabilities for complex kitchen analysis tasks.
 * 
 * Responsibilities:
 * - GPT-o1 complex reasoning analysis
 * - Reasoning chain visualization
 * - Advanced prompt building for complex scenarios
 * - Cache integration for reasoning results
 */
export class ComplexReasoningService {
  private cacheService: GPTCacheService;

  constructor() {
    this.cacheService = new GPTCacheService({
      defaultTTL: 24 * 60 * 60, // 24 hours
      enableEncryption: true,
      similarityThreshold: 0.85,
      warmupPatterns: ['complex_reasoning', 'gpt_o1_analysis', 'multi_step_reasoning']
    });
  }

  /**
   * Perform GPT-o1 complex reasoning analysis
   */
  async analyzeWithComplexReasoning(
    imagePaths: string[],
    analysisId: string,
    config: AnalysisConfig,
    reasoningContext: ReasoningContext
  ): Promise<VisionAnalysisResult> {
    const startTime = Date.now();

    // Get GPT-o1 model selection
    const modelSelection = modelSelectionService.determineOptimalModel({
      ...config,
      useGPTO1: true,
      modelSelection: 'GPTO1',
      complexReasoningRequired: true
    });

    if (modelSelection.modelType !== 'GPTO1') {
      logger.warn('GPT-o1 client not available, falling back to standard analysis');
      throw new Error('GPT-o1 client not available for complex reasoning');
    }

    logger.info(`Starting GPT-o1 complex reasoning analysis: ${analysisId}`, {
      imageCount: imagePaths.length,
      analysisType: reasoningContext.analysisType,
      complexityFactors: reasoningContext.complexityFactors
    });

    // Initialize reasoning chain for visualization
    const chainId = await this.initializeReasoningChain(analysisId, reasoningContext, config);

    try {
      // Step 1: Check cache for existing results
      const cacheKey = await this.cacheService.generateCacheKey(
        imagePaths,
        config,
        reasoningContext,
        'GPTO1'
      );

      // Try exact match first
      let cachedResult = await this.cacheService.get(cacheKey);

      // If no exact match, try semantic similarity matching
      if (!cachedResult) {
        cachedResult = await this.cacheService.findSimilar(
          imagePaths,
          config,
          reasoningContext,
          'GPTO1'
        );
      }

      if (cachedResult) {
        const cacheHitTime = Date.now() - startTime;
        logger.info(`Cache hit for GPT-o1 analysis: ${analysisId}`, {
          cacheKey: cacheKey.substring(0, 16) + '...',
          hitCount: cachedResult.cacheMetadata?.hitCount || 0,
          responseTime: cacheHitTime
        });

        return {
          content: cachedResult.content,
          model: cachedResult.model,
          usage: cachedResult.usage,
          confidence: cachedResult.confidence,
          processingTime: cacheHitTime
        };
      }

      // Step 2: Build specialized prompt for GPT-o1
      const complexReasoningPrompt = this.buildComplexReasoningPrompt(config, reasoningContext);

      // Step 3: Prepare messages for GPT-o1
      const messages = [
        {
          role: "user" as const,
          content: [
            {
              type: "text" as const,
              text: complexReasoningPrompt
            },
            ...imagePaths.map(imagePath => ({
              type: "image_url" as const,
              image_url: {
                url: `data:image/jpeg;base64,${fs.readFileSync(imagePath, 'base64')}`,
                detail: "high" as const
              }
            }))
          ]
        }
      ];

      // Step 4: Make API call with retry logic
      const response = await this.makeAPICallWithRetry(async () => {
        return await modelSelection.client.chat.completions.create({
          model: modelSelection.model,
          messages,
          max_completion_tokens: 6000, // Higher token limit for complex reasoning
          // Note: GPT-o1 doesn't support temperature parameter
        });
      });

      const processingTime = Date.now() - startTime;

      logger.info(`GPT-o1 complex reasoning completed in ${processingTime}ms`, {
        model: modelSelection.model,
        usage: response.usage,
        analysisType: reasoningContext.analysisType
      });

      const result: VisionAnalysisResult = {
        content: response.choices[0]?.message?.content || '',
        model: modelSelection.model,
        usage: response.usage || { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 },
        confidence: 0.9, // GPT-o1 typically provides high-confidence reasoning
        processingTime
      };

      // Step 5: Process reasoning for visualization
      await this.processReasoningForVisualization(
        chainId,
        response.choices[0]?.message?.content || '',
        reasoningContext,
        processingTime
      );

      // Step 6: Cache the result
      try {
        await this.cacheService.set(cacheKey, result);
        logger.info(`Cached GPT-o1 result: ${analysisId}`, {
          cacheKey: cacheKey.substring(0, 16) + '...',
          tokensUsed: result.usage.total_tokens,
          processingTime
        });
      } catch (cacheError) {
        logger.warn('Failed to cache GPT-o1 result:', cacheError);
      }

      return result;

    } catch (error) {
      logger.error(`GPT-o1 complex reasoning failed: ${analysisId}`, error);
      throw error;
    }
  }

  /**
   * Build specialized prompt for GPT-o1's complex reasoning capabilities
   */
  buildComplexReasoningPrompt(config: AnalysisConfig, reasoningContext: ReasoningContext): string {
    const basePrompt = `You are an expert kitchen design analyst with advanced spatial reasoning capabilities.

ANALYSIS CONTEXT:
- Analysis Type: ${reasoningContext.analysisType}
- Complexity Factors: ${reasoningContext.complexityFactors.join(', ')}
- Expected Outcomes: ${reasoningContext.expectedOutcomes.join(', ')}

REASONING APPROACH:
Use your advanced reasoning capabilities to perform multi-step analysis:

1. SPATIAL UNDERSTANDING: Analyze the 3D spatial relationships, depth, and layout
2. COMPONENT IDENTIFICATION: Identify all cabinets, hardware, materials, and fixtures
3. MEASUREMENT ANALYSIS: Estimate dimensions and spatial relationships
4. QUALITY ASSESSMENT: Evaluate materials, construction, and design quality
5. OPTIMIZATION OPPORTUNITIES: Identify potential improvements and optimizations

ENHANCED FEATURES ENABLED:`;

    const features = [];
    if (config.enable3DReconstruction) features.push(`- 3D Reconstruction (${config.spatialResolution} resolution)`);
    if (config.enableIntelligentMeasurement) features.push('- Intelligent Measurement Analysis');
    if (config.enableEnhancedHardwareRecognition) features.push('- Enhanced Hardware Recognition');
    if (config.enableMaterialRecognition) features.push(`- Material Recognition (${config.materialRecognitionDepth} depth)`);
    if (config.enableCostEstimation) features.push('- Cost Estimation Analysis');

    return basePrompt + '\n' + features.join('\n') + `

CRITICAL REQUIREMENTS:
- Provide detailed step-by-step reasoning for all conclusions
- Include confidence scores for each major finding
- Identify any uncertainties or limitations in the analysis
- Suggest additional data that would improve accuracy
- Focus on actionable insights and recommendations

Please analyze the provided kitchen images with this comprehensive approach.`;
  }

  /**
   * Initialize reasoning chain for visualization
   */
  private async initializeReasoningChain(
    analysisId: string,
    reasoningContext: ReasoningContext,
    config: AnalysisConfig
  ): Promise<string> {
    try {
      const { reasoningManager } = require('../reasoningManager');
      const chainId = reasoningManager.startReasoningChain(analysisId, {
        analysisType: reasoningContext.analysisType,
        inputData: { config },
        constraints: reasoningContext.complexityFactors,
        objectives: reasoningContext.expectedOutcomes,
        qualityThresholds: {
          minConfidence: 0.8,
          maxUncertainty: 0.2
        }
      });

      // Initialize visualization data
      reasoningManager.initializeVisualizationData(chainId);
      return chainId;
    } catch (error) {
      logger.warn('Failed to initialize reasoning chain:', error);
      return `fallback_${analysisId}_${Date.now()}`;
    }
  }

  /**
   * Process GPT-o1 reasoning for visualization
   */
  private async processReasoningForVisualization(
    chainId: string,
    content: string,
    reasoningContext: ReasoningContext,
    processingTime: number
  ): Promise<void> {
    try {
      const { reasoningManager } = require('../reasoningManager');

      // Add reasoning steps based on content analysis
      reasoningManager.addReasoningStep(chainId, {
        step: 'gpt_o1_analysis_complete',
        reasoning: `GPT-o1 completed complex reasoning analysis for ${reasoningContext.analysisType}`,
        confidence: 0.9,
        data: {
          analysisType: reasoningContext.analysisType,
          processingTime,
          contentLength: content.length,
          complexityFactors: reasoningContext.complexityFactors
        }
      });

      // Extract reasoning steps from content if possible
      this.extractReasoningStepsFromContent(chainId, content, reasoningManager);

    } catch (error) {
      logger.warn('Failed to process reasoning for visualization:', error);
    }
  }

  /**
   * Extract reasoning steps from GPT-o1 content
   */
  private extractReasoningStepsFromContent(chainId: string, content: string, reasoningManager: any): void {
    // Simple pattern matching for reasoning steps
    const stepPatterns = [
      /SPATIAL UNDERSTANDING[:\s]*(.*?)(?=\n\n|\n[A-Z]|$)/s,
      /COMPONENT IDENTIFICATION[:\s]*(.*?)(?=\n\n|\n[A-Z]|$)/s,
      /MEASUREMENT ANALYSIS[:\s]*(.*?)(?=\n\n|\n[A-Z]|$)/s,
      /QUALITY ASSESSMENT[:\s]*(.*?)(?=\n\n|\n[A-Z]|$)/s,
      /OPTIMIZATION OPPORTUNITIES[:\s]*(.*?)(?=\n\n|\n[A-Z]|$)/s
    ];

    const stepNames = [
      'spatial_understanding',
      'component_identification',
      'measurement_analysis',
      'quality_assessment',
      'optimization_opportunities'
    ];

    stepPatterns.forEach((pattern, index) => {
      const match = content.match(pattern);
      if (match && match[1]) {
        reasoningManager.addReasoningStep(chainId, {
          step: stepNames[index],
          reasoning: match[1].trim(),
          confidence: 0.85,
          data: { extractedFromContent: true }
        });
      }
    });
  }

  /**
   * Make API call with retry logic
   */
  private async makeAPICallWithRetry<T>(apiCall: () => Promise<T>, maxRetries: number = 3): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall();
      } catch (error) {
        lastError = error as Error;
        logger.warn(`API call attempt ${attempt}/${maxRetries} failed:`, error);

        if (attempt < maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError!;
  }
}

// Export singleton instance
export const complexReasoningService = new ComplexReasoningService();
