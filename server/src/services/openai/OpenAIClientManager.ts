import OpenAI from 'openai';
import { createModuleLogger } from '@/utils/logger';

const logger = createModuleLogger('OpenAIClientManager');

export interface ClientConfiguration {
  isAzure: boolean;
  azureDeployments: {
    gpt4o: string;
    gpt4oMini: string;
    gptO1: string;
  };
  rateLimitDelay: number;
  maxRetries: number;
}

export interface ClientInstances {
  client: OpenAI | null;
  miniClient: OpenAI | null;
  o1Client: OpenAI | null;
}

/**
 * OpenAI Client Manager
 * 
 * Handles initialization and management of OpenAI clients for different models.
 * Supports both Azure OpenAI and standard OpenAI configurations.
 * 
 * Responsibilities:
 * - Client initialization (Azure/Standard OpenAI)
 * - Configuration management
 * - Client instance management
 * - Connection validation
 */
export class OpenAIClientManager {
  private clients: ClientInstances = {
    client: null,
    miniClient: null,
    o1Client: null
  };

  private config: ClientConfiguration = {
    isAzure: false,
    azureDeployments: {
      gpt4o: 'gpt-4o',
      gpt4oMini: 'gpt-4o-mini',
      gptO1: 'o1-preview'
    },
    rateLimitDelay: 1000,
    maxRetries: 3
  };

  private initialized = false;

  constructor() {
    // Don't initialize immediately - wait for first access
    logger.info('OpenAI Client Manager created - will initialize on first access');
  }

  /**
   * Get client instances
   */
  getClients(): ClientInstances {
    this.ensureInitialized();
    return this.clients;
  }

  /**
   * Get configuration
   */
  getConfiguration(): ClientConfiguration {
    return { ...this.config };
  }

  /**
   * Check if clients are properly initialized
   */
  isInitialized(): boolean {
    this.ensureInitialized();
    return this.initialized && (this.clients.client !== null || this.clients.miniClient !== null);
  }

  /**
   * Ensure clients are initialized (lazy initialization)
   */
  private ensureInitialized(): void {
    if (!this.initialized) {
      // Check if environment variables are available
      // This prevents initialization during module import time before dotenv.config()
      const hasAnyEnvVars = process.env.AZURE_OPENAI_API_KEY || process.env.OPENAI_API_KEY;

      if (!hasAnyEnvVars) {
        logger.warn('Environment variables not yet available - deferring OpenAI client initialization');
        return;
      }

      this.initializeClients();
      this.initialized = true;
    }
  }

  /**
   * Initialize all OpenAI clients
   */
  private initializeClients(): void {
    // Try Azure OpenAI first (recommended)
    if (this.initializeAzureOpenAI()) {
      return;
    }

    // Fall back to standard OpenAI
    if (this.initializeStandardOpenAI()) {
      return;
    }

    // No valid configuration found
    logger.warn('No valid OpenAI configuration found - clients will be null');
  }

  /**
   * Initialize Azure OpenAI clients
   */
  private initializeAzureOpenAI(): boolean {
    const azureApiKey = process.env.AZURE_OPENAI_API_KEY;
    const azureEndpoint = process.env.AZURE_OPENAI_ENDPOINT;
    const azureApiVersion = process.env.AZURE_OPENAI_API_VERSION || '2025-01-01-preview';

    logger.info('Attempting Azure OpenAI initialization...', {
      hasApiKey: !!azureApiKey,
      hasEndpoint: !!azureEndpoint,
      apiVersion: azureApiVersion,
      endpoint: azureEndpoint,
      actualApiKey: azureApiKey ? `${azureApiKey.substring(0, 10)}...` : 'undefined',
      actualEndpoint: azureEndpoint || 'undefined',
      processEnvKeys: Object.keys(process.env).filter(key => key.includes('AZURE_OPENAI')),
      nodeEnv: process.env.NODE_ENV
    });

    if (!azureApiKey || !azureEndpoint) {
      logger.info('Azure OpenAI credentials not found, trying standard OpenAI...');
      return false;
    }

    try {
      // Clean up the endpoint URL
      const cleanEndpoint = azureEndpoint.replace(/\/$/, '');

      // Get deployment names
      this.config.azureDeployments.gpt4o = process.env.AZURE_OPENAI_DEPLOYMENT_GPT4O || 'gpt-4o';
      this.config.azureDeployments.gpt4oMini = process.env.AZURE_OPENAI_DEPLOYMENT_GPT4O_MINI || 'gpt-4o-mini';
      this.config.azureDeployments.gptO1 = process.env.AZURE_OPENAI_DEPLOYMENT_GPTO1 || 'o1-preview';

      logger.info('Deployment configurations:', {
        gpt4o: this.config.azureDeployments.gpt4o,
        gpt4oMini: this.config.azureDeployments.gpt4oMini,
        gptO1: this.config.azureDeployments.gptO1
      });

      // Initialize main client for GPT-4o
      const gpt4oBaseURL = `${cleanEndpoint}/openai/deployments/${this.config.azureDeployments.gpt4o}`;
      logger.info('Initializing GPT-4o client...', { baseURL: gpt4oBaseURL });

      this.clients.client = new OpenAI({
        apiKey: azureApiKey,
        baseURL: gpt4oBaseURL,
        defaultHeaders: { 'api-key': azureApiKey },
        defaultQuery: { 'api-version': azureApiVersion }
      });

      logger.info('GPT-4o client created successfully');

      // Initialize GPT-4o-mini client
      this.initializeAzureMiniClient(cleanEndpoint, azureApiKey, azureApiVersion);

      // Initialize GPT-o1 client with specific API version
      const gptO1BaseURL = `${cleanEndpoint}/openai/deployments/${this.config.azureDeployments.gptO1}`;
      logger.info('Initializing GPT-o1 client...', { baseURL: gptO1BaseURL });

      this.clients.o1Client = new OpenAI({
        apiKey: azureApiKey,
        baseURL: gptO1BaseURL,
        defaultHeaders: { 'api-key': azureApiKey },
        defaultQuery: { 'api-version': '2024-12-01-preview' } // GPT-o1 specific API version
      });

      logger.info('GPT-o1 client created successfully');

      this.config.isAzure = true;
      logger.info('Azure OpenAI configuration successful', {
        endpoint: cleanEndpoint,
        gpt4oDeployment: this.config.azureDeployments.gpt4o,
        gpt4oMiniDeployment: this.config.azureDeployments.gpt4oMini,
        gptO1Deployment: this.config.azureDeployments.gptO1,
        apiVersion: azureApiVersion,
        clientsInitialized: {
          gpt4o: !!this.clients.client,
          gpt4oMini: !!this.clients.miniClient,
          gptO1: !!this.clients.o1Client
        }
      });
      return true;

    } catch (error) {
      logger.error('Failed to initialize Azure OpenAI client:', error);
      return false;
    }
  }

  /**
   * Initialize Azure GPT-4o-mini client (separate or shared endpoint)
   */
  private initializeAzureMiniClient(cleanEndpoint: string, azureApiKey: string, azureApiVersion: string): void {
    // Check if we have separate mini configuration
    const azureMiniApiKey = process.env.AZURE_OPENAI_API_KEY_MINI;
    const azureMiniEndpoint = process.env.AZURE_OPENAI_ENDPOINT_MINI;

    logger.info('Initializing GPT-4o-mini client...', {
      hasSeparateMiniConfig: !!(azureMiniApiKey && azureMiniEndpoint),
      miniEndpoint: azureMiniEndpoint,
      deployment: this.config.azureDeployments.gpt4oMini
    });

    if (azureMiniApiKey && azureMiniEndpoint) {
      // Use separate endpoint for GPT-4o-mini
      const cleanMiniEndpoint = azureMiniEndpoint.replace(/\/$/, '');
      const miniBaseURL = `${cleanMiniEndpoint}/openai/deployments/${this.config.azureDeployments.gpt4oMini}`;

      logger.info('Using separate mini endpoint', { baseURL: miniBaseURL });

      this.clients.miniClient = new OpenAI({
        apiKey: azureMiniApiKey,
        baseURL: miniBaseURL,
        defaultHeaders: { 'api-key': azureMiniApiKey },
        defaultQuery: { 'api-version': azureApiVersion }
      });
      logger.info('Azure OpenAI clients initialized with separate mini endpoint');
    } else {
      // Use same endpoint for both models
      const sharedBaseURL = `${cleanEndpoint}/openai/deployments/${this.config.azureDeployments.gpt4oMini}`;

      logger.info('Using shared endpoint for mini client', { baseURL: sharedBaseURL });

      this.clients.miniClient = new OpenAI({
        apiKey: azureApiKey,
        baseURL: sharedBaseURL,
        defaultHeaders: { 'api-key': azureApiKey },
        defaultQuery: { 'api-version': azureApiVersion }
      });
      logger.info('Azure OpenAI clients initialized with shared endpoint');
    }

    logger.info('GPT-4o-mini client created successfully');
  }

  /**
   * Initialize standard OpenAI clients
   */
  private initializeStandardOpenAI(): boolean {
    const apiKey = process.env.OPENAI_API_KEY;

    if (!apiKey) {
      return false;
    }

    try {
      this.clients.client = new OpenAI({
        apiKey,
        organization: process.env.OPENAI_ORG_ID,
      });
      this.clients.miniClient = this.clients.client; // Use same client for both models
      this.clients.o1Client = this.clients.client; // Use same client for o1 model
      this.config.isAzure = false;
      logger.info('Standard OpenAI client initialized');
      return true;
    } catch (error) {
      logger.error('Failed to initialize standard OpenAI client:', error);
      return false;
    }
  }

  /**
   * Validate client connections
   */
  async validateConnections(): Promise<{
    gpt4o: boolean;
    gpt4oMini: boolean;
    gptO1: boolean;
  }> {
    this.ensureInitialized();

    const results = {
      gpt4o: false,
      gpt4oMini: false,
      gptO1: false
    };

    // Test GPT-4o client
    if (this.clients.client) {
      try {
        await this.clients.client.models.list();
        results.gpt4o = true;
        logger.info('GPT-4o client connection validated');
      } catch (error) {
        logger.warn('GPT-4o client validation failed:', error);
      }
    }

    // Test GPT-4o-mini client
    if (this.clients.miniClient) {
      try {
        await this.clients.miniClient.models.list();
        results.gpt4oMini = true;
        logger.info('GPT-4o-mini client connection validated');
      } catch (error) {
        logger.warn('GPT-4o-mini client validation failed:', error);
      }
    }

    // Test GPT-o1 client
    if (this.clients.o1Client) {
      try {
        await this.clients.o1Client.models.list();
        results.gptO1 = true;
        logger.info('GPT-o1 client connection validated');
      } catch (error) {
        logger.warn('GPT-o1 client validation failed:', error);
      }
    }

    return results;
  }

  /**
   * Reinitialize clients (useful for configuration changes)
   */
  reinitialize(): void {
    this.clients = {
      client: null,
      miniClient: null,
      o1Client: null
    };
    this.initialized = false;
    this.ensureInitialized();
  }
}

// Export singleton instance
export const openAIClientManager = new OpenAIClientManager();
