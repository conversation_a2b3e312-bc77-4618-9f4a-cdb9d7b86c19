import OpenAI from 'openai';
import { createModuleLogger } from '@/utils/logger';
import { openAIClientManager, ClientInstances, ClientConfiguration } from './OpenAIClientManager';

const logger = createModuleLogger('ModelSelectionService');

export interface AnalysisConfig {
  useGPT4o: boolean;
  useReasoning: boolean;
  useGPTO1?: boolean;
  modelSelection?: 'AUTO' | 'GPT4O' | 'GPT4O_MINI' | 'GPTO1';
  focusOnMaterials: boolean;
  focusOnHardware: boolean;
  enableMultiView: boolean;
  enable3DReconstruction?: boolean;
  spatialResolution?: 'LOW' | 'MEDIUM' | 'HIGH';
  includeHardwarePositioning?: boolean;
  enableIntelligentMeasurement?: boolean;
  enableEnhancedHardwareRecognition?: boolean;
  enableMaterialRecognition?: boolean;
  enableCostEstimation?: boolean;
  materialRecognitionDepth?: 'BASIC' | 'DETAILED' | 'COMPREHENSIVE';
  costEstimationRegion?: string;
  promptId?: string;
  promptVersion?: string | number;
  enhancedMode?: boolean;
  optimizedPrompt?: string;
  reasoningChainId?: string;
  pdfProcessingResults?: any;

  // Phase 1: Advanced AI Intelligence enhancements (backward compatible)
  enableAdvancedSpatialReasoning?: boolean; // Enhanced spatial intelligence for complex layouts
  enableMultiModelEnsemble?: boolean; // Coordinate multiple models for improved accuracy
  enableContextualDesignAnalysis?: boolean; // Deep understanding of design context and style
  enableIntelligentCabinetCounting?: boolean; // Smart cabinet counting with spatial relationships
  enableSpatialRelationshipAnalysis?: boolean; // Analyze spatial relationships between components
  enableAdvancedMeasurementValidation?: boolean; // Geometric validation and cross-checking
  enableConfidenceScoring?: boolean; // Provide confidence scores for analysis results
  enableUncertaintyQuantification?: boolean; // Quantify uncertainty in analysis results
  intelligenceLevel?: 'STANDARD' | 'ENHANCED' | 'ADVANCED'; // Overall intelligence level
  adaptiveAnalysisDepth?: boolean; // Adapt analysis depth based on image quality/complexity
  abTestVariant?: string;
  complexReasoningRequired?: boolean;
  enableLayoutOptimization?: boolean;
}

export interface ModelSelection {
  model: string;
  client: OpenAI;
  modelType: 'GPT4O' | 'GPT4O_MINI' | 'GPTO1';
}

export interface ComplexityAnalysis {
  requiresComplexReasoning: boolean;
  complexityScore: number;
  reasoningFactors: string[];
  recommendedModel: 'GPT4O' | 'GPT4O_MINI' | 'GPTO1';
}

/**
 * Model Selection Service
 * 
 * Determines the optimal OpenAI model based on analysis requirements.
 * Handles complexity analysis and intelligent model routing.
 * 
 * Responsibilities:
 * - Analyze task complexity
 * - Select optimal model based on requirements
 * - Handle explicit model selection
 * - Provide model recommendations
 */
export class ModelSelectionService {
  private clients: ClientInstances;
  private config: ClientConfiguration;

  constructor() {
    this.clients = openAIClientManager.getClients();
    this.config = openAIClientManager.getConfiguration();
  }

  /**
   * Determine optimal model based on analysis requirements
   */
  determineOptimalModel(config: AnalysisConfig): ModelSelection {
    // Explicit model selection takes precedence
    if (config.modelSelection && config.modelSelection !== 'AUTO') {
      const explicitSelection = this.handleExplicitModelSelection(config.modelSelection);
      if (explicitSelection) {
        logger.info(`Using explicit model selection: ${config.modelSelection}`);
        return explicitSelection;
      }
    }

    // Auto-selection based on analysis complexity
    const complexityAnalysis = this.analyzeComplexity(config);
    const selectedModel = this.selectModelByComplexity(complexityAnalysis, config);

    logger.info('Model selected via complexity analysis', {
      recommendedModel: complexityAnalysis.recommendedModel,
      complexityScore: complexityAnalysis.complexityScore,
      reasoningFactors: complexityAnalysis.reasoningFactors,
      selectedModel: selectedModel.modelType
    });

    return selectedModel;
  }

  /**
   * Handle explicit model selection
   */
  private handleExplicitModelSelection(modelSelection: string): ModelSelection | null {
    switch (modelSelection) {
      case 'GPTO1':
        if (this.clients.o1Client) {
          return {
            model: this.config.isAzure ? this.config.azureDeployments.gptO1 : 'o1-preview',
            client: this.clients.o1Client,
            modelType: 'GPTO1'
          };
        }
        logger.warn('GPT-o1 client not available, falling back to auto-selection');
        break;

      case 'GPT4O':
        if (this.clients.client) {
          return {
            model: this.config.isAzure ? this.config.azureDeployments.gpt4o : 'gpt-4o',
            client: this.clients.client,
            modelType: 'GPT4O'
          };
        }
        logger.warn('GPT-4o client not available, falling back to auto-selection');
        break;

      case 'GPT4O_MINI':
        if (this.clients.miniClient) {
          return {
            model: this.config.isAzure ? this.config.azureDeployments.gpt4oMini : 'gpt-4o-mini',
            client: this.clients.miniClient,
            modelType: 'GPT4O_MINI'
          };
        }
        logger.warn('GPT-4o-mini client not available, falling back to auto-selection');
        break;
    }

    return null;
  }

  /**
   * Analyze task complexity to determine optimal model
   * Enhanced with advanced AI intelligence capabilities
   */
  analyzeComplexity(config: AnalysisConfig): ComplexityAnalysis {
    const complexityFactors: string[] = [];
    let complexityScore = 0;

    // Phase 1: Advanced AI Intelligence - Enhanced complexity analysis

    // High complexity scenarios (existing)
    if (config.enable3DReconstruction && config.spatialResolution === 'HIGH') {
      complexityFactors.push('high_resolution_3d_reconstruction');
      complexityScore += 3;
    }

    if (config.enableIntelligentMeasurement && config.enableLayoutOptimization) {
      complexityFactors.push('intelligent_measurement_with_layout_optimization');
      complexityScore += 3;
    }

    if (config.enableMaterialRecognition && config.materialRecognitionDepth === 'COMPREHENSIVE') {
      complexityFactors.push('comprehensive_material_recognition');
      complexityScore += 2;
    }

    // Phase 1: New advanced intelligence factors
    if (config.enableAdvancedSpatialReasoning) {
      complexityFactors.push('advanced_spatial_reasoning');
      complexityScore += 4; // High complexity for spatial intelligence
    }

    if (config.enableMultiModelEnsemble) {
      complexityFactors.push('multi_model_ensemble_reasoning');
      complexityScore += 3; // Requires coordination between models
    }

    if (config.enableContextualDesignAnalysis) {
      complexityFactors.push('contextual_design_intelligence');
      complexityScore += 2; // Enhanced design understanding
    }

    if (config.enableIntelligentCabinetCounting && config.enableSpatialRelationshipAnalysis) {
      complexityFactors.push('intelligent_cabinet_spatial_analysis');
      complexityScore += 3; // Complex spatial reasoning for cabinet relationships
    }

    if (config.enableAdvancedMeasurementValidation) {
      complexityFactors.push('advanced_measurement_validation');
      complexityScore += 2; // Geometric validation and cross-checking
    }

    if (config.complexReasoningRequired) {
      complexityFactors.push('explicit_complex_reasoning_required');
      complexityScore += 4;
    }

    if (config.enableEnhancedHardwareRecognition && config.enableCostEstimation) {
      complexityFactors.push('hardware_recognition_with_cost_estimation');
      complexityScore += 2;
    }

    // Medium complexity scenarios
    if (config.enable3DReconstruction && config.spatialResolution !== 'HIGH') {
      complexityFactors.push('standard_3d_reconstruction');
      complexityScore += 1;
    }

    if (config.enableMaterialRecognition && config.materialRecognitionDepth !== 'COMPREHENSIVE') {
      complexityFactors.push('standard_material_recognition');
      complexityScore += 1;
    }

    if (config.enableMultiView) {
      complexityFactors.push('multi_view_analysis');
      complexityScore += 1;
    }

    // Determine if complex reasoning is required
    const requiresComplexReasoning = complexityScore >= 4 || complexityFactors.length >= 3;

    // Recommend model based on complexity
    let recommendedModel: 'GPT4O' | 'GPT4O_MINI' | 'GPTO1';
    if (requiresComplexReasoning || config.useGPTO1) {
      recommendedModel = 'GPTO1';
    } else if (config.useGPT4o || complexityScore >= 2) {
      recommendedModel = 'GPT4O';
    } else {
      recommendedModel = 'GPT4O_MINI';
    }

    return {
      requiresComplexReasoning,
      complexityScore,
      reasoningFactors: complexityFactors,
      recommendedModel
    };
  }

  /**
   * Select model based on complexity analysis
   */
  private selectModelByComplexity(complexityAnalysis: ComplexityAnalysis, config: AnalysisConfig): ModelSelection {
    // Try to use recommended model first
    const recommendedSelection = this.getModelSelection(complexityAnalysis.recommendedModel);
    if (recommendedSelection) {
      return recommendedSelection;
    }

    // Fallback hierarchy: GPT4O -> GPT4O_MINI -> any available
    const fallbackOrder: Array<'GPT4O' | 'GPT4O_MINI' | 'GPTO1'> = ['GPT4O', 'GPT4O_MINI', 'GPTO1'];
    
    for (const modelType of fallbackOrder) {
      const selection = this.getModelSelection(modelType);
      if (selection) {
        logger.warn(`Falling back to ${modelType} (recommended: ${complexityAnalysis.recommendedModel})`);
        return selection;
      }
    }

    // Last resort: throw error if no clients available
    throw new Error('No OpenAI clients available for analysis');
  }

  /**
   * Get model selection for specific model type
   */
  private getModelSelection(modelType: 'GPT4O' | 'GPT4O_MINI' | 'GPTO1'): ModelSelection | null {
    switch (modelType) {
      case 'GPTO1':
        if (this.clients.o1Client) {
          return {
            model: this.config.isAzure ? this.config.azureDeployments.gptO1 : 'o1-preview',
            client: this.clients.o1Client,
            modelType: 'GPTO1'
          };
        }
        break;

      case 'GPT4O':
        if (this.clients.client) {
          return {
            model: this.config.isAzure ? this.config.azureDeployments.gpt4o : 'gpt-4o',
            client: this.clients.client,
            modelType: 'GPT4O'
          };
        }
        break;

      case 'GPT4O_MINI':
        if (this.clients.miniClient) {
          return {
            model: this.config.isAzure ? this.config.azureDeployments.gpt4oMini : 'gpt-4o-mini',
            client: this.clients.miniClient,
            modelType: 'GPT4O_MINI'
          };
        }
        break;
    }

    return null;
  }

  /**
   * Get available models
   */
  getAvailableModels(): Array<'GPT4O' | 'GPT4O_MINI' | 'GPTO1'> {
    const available: Array<'GPT4O' | 'GPT4O_MINI' | 'GPTO1'> = [];

    // Check if clients exist (they are created successfully even if validation fails)
    if (this.clients.client) {
      available.push('GPT4O');
      logger.info('GPT4O model available (client exists)');
    }
    if (this.clients.miniClient) {
      available.push('GPT4O_MINI');
      logger.info('GPT4O_MINI model available (client exists)');
    }
    if (this.clients.o1Client) {
      available.push('GPTO1');
      logger.info('GPTO1 model available (client exists)');
    }

    logger.info('Available models determined by client existence', {
      available,
      clientsExist: {
        gpt4o: !!this.clients.client,
        gpt4oMini: !!this.clients.miniClient,
        gptO1: !!this.clients.o1Client
      }
    });

    return available;
  }

  /**
   * Get model capabilities
   */
  getModelCapabilities(modelType: 'GPT4O' | 'GPT4O_MINI' | 'GPTO1'): {
    supportsVision: boolean;
    supportsComplexReasoning: boolean;
    maxTokens: number;
    supportsTemperature: boolean;
  } {
    switch (modelType) {
      case 'GPTO1':
        return {
          supportsVision: true,
          supportsComplexReasoning: true,
          maxTokens: 6000,
          supportsTemperature: false
        };

      case 'GPT4O':
        return {
          supportsVision: true,
          supportsComplexReasoning: false,
          maxTokens: 4000,
          supportsTemperature: true
        };

      case 'GPT4O_MINI':
        return {
          supportsVision: true,
          supportsComplexReasoning: false,
          maxTokens: 4000,
          supportsTemperature: false
        };

      default:
        throw new Error(`Unknown model type: ${modelType}`);
    }
  }
}

// Export singleton instance
export const modelSelectionService = new ModelSelectionService();
