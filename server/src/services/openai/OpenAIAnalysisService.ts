import fs from 'fs';
import { createModuleLogger } from '@/utils/logger';
import { modelSelectionService, AnalysisConfig, ModelSelection } from './ModelSelectionService';
import { complexReasoningService, VisionAnalysisResult, ReasoningContext } from './ComplexReasoningService';
import { AdvancedIntelligenceService, IntelligenceAnalysisResult } from '../ai/AdvancedIntelligenceService';

const logger = createModuleLogger('OpenAIAnalysisService');

export interface EnhancedAnalysisResult extends VisionAnalysisResult {
  optimizationApplied: boolean;
  reasoningChainId?: string;
  abTestVariant?: string;

  // Phase 1: Advanced AI Intelligence enhancements (backward compatible)
  advancedIntelligence?: IntelligenceAnalysisResult;
}

/**
 * OpenAI Analysis Service
 * 
 * Main orchestration service for OpenAI-based image analysis.
 * Coordinates between different analysis types and model selections.
 * 
 * Responsibilities:
 * - Standard image analysis
 * - Enhanced analysis with optimization
 * - Complex reasoning coordination
 * - Analysis result processing
 */
export class OpenAIAnalysisService {
  private advancedIntelligenceService: AdvancedIntelligenceService;

  constructor() {
    this.advancedIntelligenceService = AdvancedIntelligenceService.getInstance();
  }
  /**
   * Analyze images using optimal model selection
   */
  async analyzeImages(
    imagePaths: string[],
    prompt: string,
    config: AnalysisConfig
  ): Promise<VisionAnalysisResult> {
    const startTime = Date.now();
    
    // Get optimal model selection
    const modelSelection = modelSelectionService.determineOptimalModel(config);
    
    if (!modelSelection.client) {
      logger.info('No OpenAI client available - returning mock response');
      return this.generateMockVisionResponse(prompt, config);
    }

    logger.info(`Starting image analysis with ${modelSelection.modelType}`, {
      imageCount: imagePaths.length,
      model: modelSelection.model,
      promptLength: prompt.length
    });

    try {
      // Prepare messages for the API call
      const messages = this.prepareMessages(imagePaths, prompt);

      // Get model capabilities and prepare API parameters
      const capabilities = modelSelectionService.getModelCapabilities(modelSelection.modelType);
      const apiParams = this.prepareAPIParameters(modelSelection, capabilities);

      // Make API call with retry logic
      const response = await this.makeAPICallWithRetry(async () => {
        return await modelSelection.client.chat.completions.create({
          ...apiParams,
          messages
        });
      });

      const processingTime = Date.now() - startTime;

      logger.info(`Image analysis completed in ${processingTime}ms`, {
        model: modelSelection.model,
        usage: response.usage,
        modelType: modelSelection.modelType
      });

      return {
        content: response.choices[0]?.message?.content || '',
        model: modelSelection.model,
        usage: response.usage || { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 },
        confidence: this.calculateConfidence(modelSelection.modelType, response),
        processingTime
      };

    } catch (error) {
      logger.error('Image analysis failed:', error);
      throw error;
    }
  }

  /**
   * Enhanced analysis with integrated optimization and reasoning
   */
  async analyzeImagesEnhanced(
    imagePaths: string[],
    analysisId: string,
    config: AnalysisConfig
  ): Promise<EnhancedAnalysisResult> {
    const startTime = Date.now();

    logger.info(`Starting enhanced analysis: ${analysisId}`, {
      imageCount: imagePaths.length,
      config: this.sanitizeConfigForLogging(config)
    });

    try {
      // Step 1: Get optimized prompt via A/B testing
      const { prompt, optimizationApplied, abTestVariant } = await this.getOptimizedPrompt(analysisId);

      // Step 2: Start reasoning chain if enabled
      const reasoningChainId = config.useReasoning 
        ? await this.initializeReasoningChain(analysisId, config)
        : undefined;

      // Step 3: Determine if complex reasoning is needed
      const complexityAnalysis = modelSelectionService.analyzeComplexity(config);
      
      let result: VisionAnalysisResult;

      if (complexityAnalysis.requiresComplexReasoning && config.useGPTO1) {
        // Use complex reasoning service for advanced analysis
        const reasoningContext: ReasoningContext = {
          analysisType: 'enhanced_kitchen_analysis',
          complexityFactors: complexityAnalysis.reasoningFactors,
          expectedOutcomes: [
            'detailed_component_analysis',
            'spatial_relationship_mapping',
            'optimization_recommendations'
          ]
        };

        result = await complexReasoningService.analyzeWithComplexReasoning(
          imagePaths,
          analysisId,
          config,
          reasoningContext
        );
      } else {
        // Use standard analysis
        result = await this.analyzeImages(imagePaths, prompt, config);
      }

      // Step 4: Process additional analysis features
      await this.processAdditionalFeatures(imagePaths, analysisId, config, result);

      // Step 5: Phase 1 - Advanced AI Intelligence Analysis (backward compatible)
      let advancedIntelligence: IntelligenceAnalysisResult | null = null;
      try {
        advancedIntelligence = await this.advancedIntelligenceService.performAdvancedAnalysis(
          imagePaths,
          analysisId,
          config,
          result
        );
      } catch (error) {
        logger.warn('Advanced intelligence analysis failed, continuing with standard analysis:', error);
      }

      const totalProcessingTime = Date.now() - startTime;

      logger.info(`Enhanced analysis completed: ${analysisId}`, {
        processingTime: totalProcessingTime,
        optimizationApplied,
        reasoningChainId,
        modelUsed: result.model,
        advancedIntelligenceEnabled: !!advancedIntelligence
      });

      return {
        ...result,
        optimizationApplied,
        reasoningChainId,
        abTestVariant,
        processingTime: totalProcessingTime,
        advancedIntelligence: advancedIntelligence || undefined
      };

    } catch (error) {
      logger.error(`Enhanced analysis failed: ${analysisId}`, error);
      throw error;
    }
  }

  /**
   * Prepare messages for API call
   */
  private prepareMessages(imagePaths: string[], prompt: string) {
    return [
      {
        role: "user" as const,
        content: [
          {
            type: "text" as const,
            text: prompt
          },
          ...imagePaths.map(imagePath => ({
            type: "image_url" as const,
            image_url: {
              url: `data:image/jpeg;base64,${fs.readFileSync(imagePath, 'base64')}`,
              detail: "high" as const
            }
          }))
        ]
      }
    ];
  }

  /**
   * Prepare API parameters based on model capabilities
   */
  private prepareAPIParameters(modelSelection: ModelSelection, capabilities: any) {
    const apiParams: any = {
      model: modelSelection.model,
      max_completion_tokens: capabilities.maxTokens
    };

    // Model-specific parameter handling
    if (modelSelection.modelType === 'GPT4O' && capabilities.supportsTemperature) {
      apiParams.temperature = 0.1; // Low temperature for consistent analysis
    } else if (modelSelection.modelType === 'GPTO1') {
      // GPT-o1 doesn't support temperature parameter
      logger.info('Using GPT-o1 with internal reasoning optimization');
    }
    // GPT4O_MINI only supports default temperature (1), no custom values

    return apiParams;
  }

  /**
   * Calculate confidence score based on model type and response
   */
  private calculateConfidence(modelType: string, response: any): number {
    // Base confidence by model type
    let baseConfidence = 0.8;
    
    switch (modelType) {
      case 'GPTO1':
        baseConfidence = 0.9; // GPT-o1 typically provides high-confidence reasoning
        break;
      case 'GPT4O':
        baseConfidence = 0.85;
        break;
      case 'GPT4O_MINI':
        baseConfidence = 0.8;
        break;
    }

    // Adjust based on response characteristics
    const content = response.choices[0]?.message?.content || '';
    const contentLength = content.length;
    
    // Longer, more detailed responses typically indicate higher confidence
    if (contentLength > 2000) {
      baseConfidence += 0.05;
    } else if (contentLength < 500) {
      baseConfidence -= 0.1;
    }

    // Check for uncertainty indicators
    const uncertaintyIndicators = ['might', 'possibly', 'unclear', 'difficult to determine'];
    const uncertaintyCount = uncertaintyIndicators.reduce((count, indicator) => {
      return count + (content.toLowerCase().split(indicator).length - 1);
    }, 0);

    if (uncertaintyCount > 0) {
      baseConfidence -= Math.min(uncertaintyCount * 0.05, 0.2);
    }

    return Math.max(0.1, Math.min(1.0, baseConfidence));
  }

  /**
   * Get optimized prompt via A/B testing
   */
  private async getOptimizedPrompt(analysisId: string): Promise<{
    prompt: string;
    optimizationApplied: boolean;
    abTestVariant: string;
  }> {
    try {
      const { abTestManager } = require('../abTestManager');
      const { promptOptimizationService } = require('../promptOptimizationService');

      // Get A/B test variant
      const promptVariant = abTestManager.getPromptVariant('kitchen_analysis', analysisId);
      let finalPrompt = promptVariant.prompt;
      let optimizationApplied = false;

      // Apply prompt optimization if using default prompt
      if (promptVariant.variantId === 'default') {
        const optimizationResult = await promptOptimizationService.optimizePrompt(
          promptVariant.prompt,
          {
            analysisType: 'kitchen_analysis',
            targetMetrics: {
              minAccuracy: 0.85,
              maxResponseTime: 8000
            }
          }
        );

        if (optimizationResult.optimizedPrompt !== promptVariant.prompt) {
          finalPrompt = optimizationResult.optimizedPrompt;
          optimizationApplied = true;
        }
      }

      return {
        prompt: finalPrompt,
        optimizationApplied,
        abTestVariant: promptVariant.variantId
      };
    } catch (error) {
      logger.warn('Failed to get optimized prompt, using default:', error);
      const { promptService } = require('../promptService');
      return {
        prompt: promptService.getKitchenAnalysisPrompt(),
        optimizationApplied: false,
        abTestVariant: 'fallback'
      };
    }
  }

  /**
   * Initialize reasoning chain
   */
  private async initializeReasoningChain(analysisId: string, config: AnalysisConfig): Promise<string | undefined> {
    try {
      const { reasoningManager } = require('../reasoningManager');
      const chainId = reasoningManager.startReasoningChain(analysisId, {
        analysisType: 'kitchen_analysis',
        inputData: { config },
        constraints: ['accurate_counting', 'realistic_measurements'],
        objectives: ['cabinet_identification', 'hardware_calculation', 'measurement_extraction'],
        qualityThresholds: {
          minConfidence: 0.7,
          maxUncertainty: 0.3
        }
      });

      logger.info(`Started reasoning chain: ${chainId}`);
      return chainId;
    } catch (error) {
      logger.warn('Failed to start reasoning chain:', error);
      return undefined;
    }
  }

  /**
   * Process additional analysis features
   */
  private async processAdditionalFeatures(
    imagePaths: string[],
    analysisId: string,
    config: AnalysisConfig,
    result: VisionAnalysisResult
  ): Promise<void> {
    // This method would coordinate with other services for additional features
    // Implementation would be added based on specific feature requirements
    logger.info(`Processing additional features for analysis: ${analysisId}`, {
      enable3DReconstruction: config.enable3DReconstruction,
      enableMaterialRecognition: config.enableMaterialRecognition,
      enableIntelligentMeasurement: config.enableIntelligentMeasurement
    });
  }

  /**
   * Generate mock response for testing/fallback
   */
  private generateMockVisionResponse(prompt: string, config: AnalysisConfig): VisionAnalysisResult {
    return {
      content: `Mock analysis response for prompt: ${prompt.substring(0, 100)}...`,
      model: 'mock-gpt-4o',
      usage: {
        prompt_tokens: 100,
        completion_tokens: 200,
        total_tokens: 300
      },
      confidence: 0.7,
      processingTime: 1000
    };
  }

  /**
   * Sanitize config for logging (remove sensitive data)
   */
  private sanitizeConfigForLogging(config: AnalysisConfig): Partial<AnalysisConfig> {
    const { optimizedPrompt, ...sanitized } = config;
    return sanitized;
  }

  /**
   * Make API call with retry logic
   */
  private async makeAPICallWithRetry<T>(apiCall: () => Promise<T>, maxRetries: number = 3): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall();
      } catch (error) {
        lastError = error as Error;
        logger.warn(`API call attempt ${attempt}/${maxRetries} failed:`, error);

        if (attempt < maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError!;
  }
}

// Export singleton instance
export const openAIAnalysisService = new OpenAIAnalysisService();
