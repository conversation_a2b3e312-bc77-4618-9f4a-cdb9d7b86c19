import { createModuleLogger } from '@/utils/logger';
import { openAIClientManager } from './OpenAIClientManager';
import { modelSelectionService, AnalysisConfig } from './ModelSelectionService';
import { complexReasoningService, VisionAnalysisResult, ReasoningContext } from './ComplexReasoningService';
import { openAIAnalysisService, EnhancedAnalysisResult } from './OpenAIAnalysisService';
import { openAIConfigService } from './OpenAIConfigService';

const logger = createModuleLogger('OpenAIService');

/**
 * OpenAI Service - Main Facade
 * 
 * Provides a unified interface for all OpenAI functionality while maintaining
 * backward compatibility with the existing API. Orchestrates the modular services.
 * 
 * This service acts as a facade pattern, delegating to specialized services:
 * - OpenAIClientManager: Client initialization and management
 * - ModelSelectionService: Optimal model selection
 * - ComplexReasoningService: GPT-o1 complex reasoning
 * - OpenAIAnalysisService: Standard and enhanced analysis
 * - OpenAIConfigService: Configuration management
 */
export class OpenAIService {
  private static instance: OpenAIService;

  constructor() {
    this.validateInitialization();
  }

  /**
   * Get singleton instance (for backward compatibility)
   */
  static getInstance(): OpenAIService {
    if (!OpenAIService.instance) {
      OpenAIService.instance = new OpenAIService();
    }
    return OpenAIService.instance;
  }

  /**
   * Analyze images using GPT-4o Vision (backward compatible method)
   */
  async analyzeImages(
    imagePaths: string[],
    prompt: string,
    config: AnalysisConfig
  ): Promise<VisionAnalysisResult> {
    logger.info('Starting image analysis (backward compatible method)', {
      imageCount: imagePaths.length,
      promptLength: prompt.length,
      config: this.sanitizeConfigForLogging(config)
    });

    return await openAIAnalysisService.analyzeImages(imagePaths, prompt, config);
  }

  /**
   * Enhanced analysis with integrated optimization and reasoning
   */
  async analyzeImagesEnhanced(
    imagePaths: string[],
    analysisId: string,
    config: AnalysisConfig
  ): Promise<EnhancedAnalysisResult> {
    logger.info('Starting enhanced analysis', {
      analysisId,
      imageCount: imagePaths.length,
      config: this.sanitizeConfigForLogging(config)
    });

    return await openAIAnalysisService.analyzeImagesEnhanced(imagePaths, analysisId, config);
  }

  /**
   * GPT-o1 specific complex reasoning analysis
   */
  async analyzeWithComplexReasoning(
    imagePaths: string[],
    analysisId: string,
    config: AnalysisConfig,
    reasoningContext: ReasoningContext
  ): Promise<VisionAnalysisResult> {
    logger.info('Starting complex reasoning analysis', {
      analysisId,
      imageCount: imagePaths.length,
      analysisType: reasoningContext.analysisType,
      complexityFactors: reasoningContext.complexityFactors
    });

    return await complexReasoningService.analyzeWithComplexReasoning(
      imagePaths,
      analysisId,
      config,
      reasoningContext
    );
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    services: {
      clientManager: boolean;
      modelSelection: boolean;
      complexReasoning: boolean;
      analysis: boolean;
      config: any;
    };
    clients: {
      gpt4o: boolean;
      gpt4oMini: boolean;
      gptO1: boolean;
    };
  }> {
    try {
      // Check client connections
      const clientValidation = await openAIClientManager.validateConnections();
      
      // Check configuration health
      const configHealth = await openAIConfigService.checkHealth();
      
      // Check service availability (prioritize client existence over validation)
      const availableModels = modelSelectionService.getAvailableModels();
      const services = {
        clientManager: openAIClientManager.isInitialized(),
        modelSelection: availableModels.length > 0,
        complexReasoning: availableModels.includes('GPTO1'), // Use client existence instead of validation
        analysis: availableModels.includes('GPT4O') || availableModels.includes('GPT4O_MINI'), // Use client existence instead of validation
        config: configHealth
      };

      // Determine overall status (prioritize client existence over validation)
      const clientsExist = availableModels.length > 0;
      const configHealthy = configHealth.status !== 'unhealthy';

      let status: 'healthy' | 'degraded' | 'unhealthy';
      if (!clientsExist || !configHealthy) {
        status = 'unhealthy';
      } else if (configHealth.status === 'degraded') {
        status = 'degraded';
      } else {
        status = 'healthy'; // Clients exist and config is good, even if validation fails
      }

      return {
        status,
        services,
        clients: clientValidation
      };
    } catch (error) {
      logger.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        services: {
          clientManager: false,
          modelSelection: false,
          complexReasoning: false,
          analysis: false,
          config: { status: 'unhealthy', details: { error: error.message } }
        },
        clients: {
          gpt4o: false,
          gpt4oMini: false,
          gptO1: false
        }
      };
    }
  }

  /**
   * Get available models
   */
  getAvailableModels(): Array<'GPT4O' | 'GPT4O_MINI' | 'GPTO1'> {
    return modelSelectionService.getAvailableModels();
  }

  /**
   * Get model capabilities
   */
  getModelCapabilities(modelType: 'GPT4O' | 'GPT4O_MINI' | 'GPTO1') {
    return modelSelectionService.getModelCapabilities(modelType);
  }

  /**
   * Analyze task complexity
   */
  analyzeComplexity(config: AnalysisConfig) {
    return modelSelectionService.analyzeComplexity(config);
  }

  /**
   * Get optimal model for configuration
   */
  determineOptimalModel(config: AnalysisConfig) {
    return modelSelectionService.determineOptimalModel(config);
  }

  /**
   * Get current configuration
   */
  getConfiguration() {
    return openAIConfigService.getConfig();
  }

  /**
   * Reload configuration
   */
  reloadConfiguration(): void {
    openAIConfigService.reloadConfiguration();
    openAIClientManager.reinitialize();
  }

  /**
   * Validate service initialization
   */
  private validateInitialization(): void {
    const isInitialized = openAIClientManager.isInitialized();
    
    if (!isInitialized) {
      logger.warn('OpenAI clients not properly initialized - some functionality may be limited');
    } else {
      logger.info('OpenAI service successfully initialized');
    }
  }

  /**
   * Sanitize config for logging (remove sensitive data)
   */
  private sanitizeConfigForLogging(config: AnalysisConfig): Partial<AnalysisConfig> {
    const { optimizedPrompt, ...sanitized } = config;
    return sanitized;
  }

  // Backward compatibility methods (delegating to appropriate services)

  /**
   * @deprecated Use analyzeImages instead
   */
  async processImages(imagePaths: string[], prompt: string, config: AnalysisConfig): Promise<VisionAnalysisResult> {
    logger.warn('processImages is deprecated, use analyzeImages instead');
    return this.analyzeImages(imagePaths, prompt, config);
  }

  /**
   * @deprecated Use getHealthStatus instead
   */
  async checkConnection(): Promise<boolean> {
    logger.warn('checkConnection is deprecated, use getHealthStatus instead');
    const health = await this.getHealthStatus();
    return health.status !== 'unhealthy';
  }

  /**
   * @deprecated Use getAvailableModels instead
   */
  isClientAvailable(): boolean {
    logger.warn('isClientAvailable is deprecated, use getAvailableModels instead');
    return this.getAvailableModels().length > 0;
  }
}

// Export interfaces for backward compatibility
export { AnalysisConfig, VisionAnalysisResult, ReasoningContext, EnhancedAnalysisResult };

// Export singleton instance for backward compatibility
export const openaiService = OpenAIService.getInstance();

// Default export for backward compatibility
export default openaiService;
