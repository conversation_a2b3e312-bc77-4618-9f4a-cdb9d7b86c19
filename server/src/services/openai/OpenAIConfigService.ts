import { createModuleLogger } from '@/utils/logger';

const logger = createModuleLogger('OpenAIConfigService');

export interface OpenAIEnvironmentConfig {
  // Azure OpenAI Configuration
  azureApiKey?: string;
  azureEndpoint?: string;
  azureApiVersion?: string;
  azureDeploymentGPT4O?: string;
  azureDeploymentGPT4OMini?: string;
  azureDeploymentGPTO1?: string;

  // Separate Azure Mini Configuration (optional)
  azureMiniApiKey?: string;
  azureMiniEndpoint?: string;

  // Standard OpenAI Configuration
  openaiApiKey?: string;
  openaiOrgId?: string;

  // Service Configuration
  rateLimitDelay?: number;
  maxRetries?: number;
  defaultTTL?: number;
  enableEncryption?: boolean;
  similarityThreshold?: number;

  // Phase 1: Security Hardening (backward compatible additions)
  enableSecurityValidation?: boolean;
  enableApiKeyRotation?: boolean;
  securityAuditLevel?: 'basic' | 'enhanced' | 'strict';
  enableConfigurationMasking?: boolean;
}

export interface ServiceDefaults {
  rateLimitDelay: number;
  maxRetries: number;
  defaultTTL: number;
  enableEncryption: boolean;
  similarityThreshold: number;
  warmupPatterns: string[];
  azureApiVersion: string;
  gptO1ApiVersion: string;

  // Phase 1: Security Hardening defaults (backward compatible)
  enableSecurityValidation: boolean;
  enableApiKeyRotation: boolean;
  securityAuditLevel: 'basic' | 'enhanced' | 'strict';
  enableConfigurationMasking: boolean;
}

export interface ConfigValidationResult {
  isValid: boolean;
  hasAzureConfig: boolean;
  hasStandardConfig: boolean;
  missingRequired: string[];
  warnings: string[];

  // Phase 1: Security Hardening validation results (backward compatible)
  securityValidation?: {
    apiKeyStrength: 'weak' | 'moderate' | 'strong';
    endpointSecurity: 'insecure' | 'secure';
    configurationExposure: 'high' | 'medium' | 'low';
    recommendations: string[];
  };
}

/**
 * OpenAI Configuration Service
 * 
 * Centralizes configuration management for OpenAI services.
 * Handles environment variable validation and default values.
 * 
 * Responsibilities:
 * - Environment configuration loading
 * - Configuration validation
 * - Default value management
 * - Configuration health checks
 */
export class OpenAIConfigService {
  private static instance: OpenAIConfigService;
  private config: OpenAIEnvironmentConfig | null = null;
  private defaults: ServiceDefaults;
  private initialized = false;

  constructor() {
    this.defaults = {
      rateLimitDelay: 1000,
      maxRetries: 3,
      defaultTTL: 24 * 60 * 60, // 24 hours
      enableEncryption: true,
      similarityThreshold: 0.85,
      warmupPatterns: ['kitchen_analysis', 'cabinet_count', 'material_recognition'],
      azureApiVersion: '2025-01-01-preview',
      gptO1ApiVersion: '2024-12-01-preview',

      // Phase 1: Security Hardening defaults (backward compatible)
      enableSecurityValidation: true,
      enableApiKeyRotation: false, // Conservative default
      securityAuditLevel: 'basic',
      enableConfigurationMasking: true
    };

    // Don't initialize immediately - wait for first access
    logger.info('OpenAI Config Service created - will initialize on first access');
  }

  /**
   * Get singleton instance
   */
  static getInstance(): OpenAIConfigService {
    if (!OpenAIConfigService.instance) {
      OpenAIConfigService.instance = new OpenAIConfigService();
    }
    return OpenAIConfigService.instance;
  }

  /**
   * Ensure configuration is initialized (lazy initialization)
   */
  private ensureInitialized(): void {
    if (!this.initialized) {
      this.config = this.loadConfiguration();
      this.initialized = true;
      // Validate after initialization to avoid circular dependency
      this.validateConfigurationInternal();
    }
  }

  /**
   * Internal validation method (doesn't call ensureInitialized)
   */
  private validateConfigurationInternal(): ConfigValidationResult {
    const result: ConfigValidationResult = {
      isValid: false,
      hasAzureConfig: false,
      hasStandardConfig: false,
      missingRequired: [],
      warnings: []
    };

    // Check Azure configuration
    if (this.config!.azureApiKey && this.config!.azureEndpoint) {
      result.hasAzureConfig = true;
      logger.info('Azure OpenAI configuration detected');
    } else {
      if (this.config!.azureApiKey && !this.config!.azureEndpoint) {
        result.missingRequired.push('AZURE_OPENAI_ENDPOINT');
      }
      if (!this.config!.azureApiKey && this.config!.azureEndpoint) {
        result.missingRequired.push('AZURE_OPENAI_API_KEY');
      }
    }

    // Check standard OpenAI configuration
    if (this.config!.openaiApiKey) {
      result.hasStandardConfig = true;
      logger.info('Standard OpenAI configuration detected');
    }

    // Validate that at least one configuration is available
    result.isValid = result.hasAzureConfig || result.hasStandardConfig;

    if (!result.isValid) {
      result.missingRequired.push('Either Azure OpenAI or Standard OpenAI configuration required');
    }

    // Check for warnings
    if (result.hasAzureConfig && !this.config!.azureDeploymentGPT4O) {
      result.warnings.push('AZURE_OPENAI_DEPLOYMENT_GPT4O not set, using default: gpt-4o');
    }

    if (result.hasAzureConfig && !this.config!.azureDeploymentGPT4OMini) {
      result.warnings.push('AZURE_OPENAI_DEPLOYMENT_GPT4O_MINI not set, using default: gpt-4o-mini');
    }

    if (result.hasAzureConfig && !this.config!.azureDeploymentGPTO1) {
      result.warnings.push('AZURE_OPENAI_DEPLOYMENT_GPTO1 not set, using default: o1-preview');
    }

    // Log validation results
    if (result.isValid) {
      logger.info('OpenAI configuration validation passed', {
        hasAzureConfig: result.hasAzureConfig,
        hasStandardConfig: result.hasStandardConfig,
        warnings: result.warnings
      });
    } else {
      logger.error('OpenAI configuration validation failed', {
        missingRequired: result.missingRequired
      });
    }

    return result;
  }

  /**
   * Get current configuration
   */
  getConfig(): OpenAIEnvironmentConfig {
    this.ensureInitialized();
    return { ...this.config! };
  }

  /**
   * Get service defaults
   */
  getDefaults(): ServiceDefaults {
    return { ...this.defaults };
  }

  /**
   * Get Azure configuration
   */
  getAzureConfig(): {
    apiKey?: string;
    endpoint?: string;
    apiVersion: string;
    deployments: {
      gpt4o: string;
      gpt4oMini: string;
      gptO1: string;
    };
    miniConfig?: {
      apiKey: string;
      endpoint: string;
    };
  } {
    this.ensureInitialized();
    return {
      apiKey: this.config!.azureApiKey,
      endpoint: this.config!.azureEndpoint,
      apiVersion: this.config!.azureApiVersion || this.defaults.azureApiVersion,
      deployments: {
        gpt4o: this.config!.azureDeploymentGPT4O || 'gpt-4o',
        gpt4oMini: this.config!.azureDeploymentGPT4OMini || 'gpt-4o-mini',
        gptO1: this.config!.azureDeploymentGPTO1 || 'o1-preview'
      },
      miniConfig: this.config!.azureMiniApiKey && this.config!.azureMiniEndpoint ? {
        apiKey: this.config!.azureMiniApiKey,
        endpoint: this.config!.azureMiniEndpoint
      } : undefined
    };
  }

  /**
   * Get standard OpenAI configuration
   */
  getStandardConfig(): {
    apiKey?: string;
    orgId?: string;
  } {
    this.ensureInitialized();
    return {
      apiKey: this.config!.openaiApiKey,
      orgId: this.config!.openaiOrgId
    };
  }

  /**
   * Get cache configuration
   */
  getCacheConfig(): {
    defaultTTL: number;
    enableEncryption: boolean;
    similarityThreshold: number;
    warmupPatterns: string[];
  } {
    this.ensureInitialized();
    return {
      defaultTTL: this.config!.defaultTTL || this.defaults.defaultTTL,
      enableEncryption: this.config!.enableEncryption ?? this.defaults.enableEncryption,
      similarityThreshold: this.config!.similarityThreshold || this.defaults.similarityThreshold,
      warmupPatterns: this.defaults.warmupPatterns
    };
  }

  /**
   * Get retry configuration
   */
  getRetryConfig(): {
    rateLimitDelay: number;
    maxRetries: number;
  } {
    this.ensureInitialized();
    return {
      rateLimitDelay: this.config!.rateLimitDelay || this.defaults.rateLimitDelay,
      maxRetries: this.config!.maxRetries || this.defaults.maxRetries
    };
  }

  /**
   * Validate current configuration
   */
  validateConfiguration(): ConfigValidationResult {
    this.ensureInitialized();
    return this.validateConfigurationInternal();
  }

  /**
   * Check configuration health
   */
  async checkHealth(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details: {
      configValid: boolean;
      azureAvailable: boolean;
      standardAvailable: boolean;
      issues: string[];
    };
  }> {
    const validation = this.validateConfiguration();
    const issues: string[] = [];

    // Check if configuration is valid
    if (!validation.isValid) {
      issues.push('Invalid configuration: ' + validation.missingRequired.join(', '));
    }

    // Add warnings as issues
    issues.push(...validation.warnings);

    // Determine overall health status
    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (!validation.isValid) {
      status = 'unhealthy';
    } else if (issues.length > 0) {
      status = 'degraded';
    } else {
      status = 'healthy';
    }

    return {
      status,
      details: {
        configValid: validation.isValid,
        azureAvailable: validation.hasAzureConfig,
        standardAvailable: validation.hasStandardConfig,
        issues
      }
    };
  }

  /**
   * Reload configuration from environment
   */
  reloadConfiguration(): void {
    logger.info('Reloading OpenAI configuration from environment');
    this.initialized = false;
    this.config = null;
    this.ensureInitialized();
  }

  /**
   * Load configuration from environment variables
   */
  private loadConfiguration(): OpenAIEnvironmentConfig {
    return {
      // Azure OpenAI Configuration
      azureApiKey: process.env.AZURE_OPENAI_API_KEY,
      azureEndpoint: process.env.AZURE_OPENAI_ENDPOINT,
      azureApiVersion: process.env.AZURE_OPENAI_API_VERSION,
      azureDeploymentGPT4O: process.env.AZURE_OPENAI_DEPLOYMENT_GPT4O,
      azureDeploymentGPT4OMini: process.env.AZURE_OPENAI_DEPLOYMENT_GPT4O_MINI,
      azureDeploymentGPTO1: process.env.AZURE_OPENAI_DEPLOYMENT_GPTO1,

      // Separate Azure Mini Configuration
      azureMiniApiKey: process.env.AZURE_OPENAI_API_KEY_MINI,
      azureMiniEndpoint: process.env.AZURE_OPENAI_ENDPOINT_MINI,
      
      // Standard OpenAI Configuration
      openaiApiKey: process.env.OPENAI_API_KEY,
      openaiOrgId: process.env.OPENAI_ORG_ID,
      
      // Service Configuration
      rateLimitDelay: process.env.OPENAI_RATE_LIMIT_DELAY ? parseInt(process.env.OPENAI_RATE_LIMIT_DELAY) : undefined,
      maxRetries: process.env.OPENAI_MAX_RETRIES ? parseInt(process.env.OPENAI_MAX_RETRIES) : undefined,
      defaultTTL: process.env.OPENAI_CACHE_TTL ? parseInt(process.env.OPENAI_CACHE_TTL) : undefined,
      enableEncryption: process.env.OPENAI_CACHE_ENCRYPTION ? process.env.OPENAI_CACHE_ENCRYPTION === 'true' : undefined,
      similarityThreshold: process.env.OPENAI_SIMILARITY_THRESHOLD ? parseFloat(process.env.OPENAI_SIMILARITY_THRESHOLD) : undefined,

      // Phase 1: Security Hardening configuration (backward compatible)
      enableSecurityValidation: process.env.OPENAI_ENABLE_SECURITY_VALIDATION ? process.env.OPENAI_ENABLE_SECURITY_VALIDATION === 'true' : undefined,
      enableApiKeyRotation: process.env.OPENAI_ENABLE_API_KEY_ROTATION ? process.env.OPENAI_ENABLE_API_KEY_ROTATION === 'true' : undefined,
      securityAuditLevel: (process.env.OPENAI_SECURITY_AUDIT_LEVEL as 'basic' | 'enhanced' | 'strict') || undefined,
      enableConfigurationMasking: process.env.OPENAI_ENABLE_CONFIG_MASKING ? process.env.OPENAI_ENABLE_CONFIG_MASKING === 'true' : undefined
    };
  }

  /**
   * Phase 1: Security Hardening - Validate API key strength
   * Backward compatible - only runs if security validation is enabled
   */
  private validateApiKeyStrength(apiKey: string): 'weak' | 'moderate' | 'strong' {
    if (!apiKey) return 'weak';

    // Azure OpenAI keys are typically 32 characters
    if (apiKey.length < 20) return 'weak';
    if (apiKey.length < 32) return 'moderate';

    // Check for complexity (mix of characters)
    const hasLowerCase = /[a-z]/.test(apiKey);
    const hasUpperCase = /[A-Z]/.test(apiKey);
    const hasNumbers = /\d/.test(apiKey);
    const hasSpecialChars = /[^a-zA-Z0-9]/.test(apiKey);

    const complexityScore = [hasLowerCase, hasUpperCase, hasNumbers, hasSpecialChars].filter(Boolean).length;

    if (complexityScore >= 3 && apiKey.length >= 32) return 'strong';
    if (complexityScore >= 2 && apiKey.length >= 24) return 'moderate';
    return 'weak';
  }

  /**
   * Phase 1: Security Hardening - Validate endpoint security
   * Backward compatible - only runs if security validation is enabled
   */
  private validateEndpointSecurity(endpoint: string): 'insecure' | 'secure' {
    if (!endpoint) return 'insecure';

    // Must use HTTPS
    if (!endpoint.startsWith('https://')) return 'insecure';

    // Must be Azure OpenAI endpoint
    if (!endpoint.includes('openai.azure.com')) return 'insecure';

    return 'secure';
  }

  /**
   * Phase 1: Security Hardening - Assess configuration exposure risk
   * Backward compatible - only runs if security validation is enabled
   */
  private assessConfigurationExposure(): 'high' | 'medium' | 'low' {
    const config = this.config!;

    // Check if masking is enabled
    const maskingEnabled = config.enableConfigurationMasking ?? this.defaults.enableConfigurationMasking;
    if (!maskingEnabled) return 'high';

    // Check if we're in production
    const isProduction = process.env.NODE_ENV === 'production';
    if (!isProduction) return 'medium';

    return 'low';
  }

  /**
   * Phase 1: Security Hardening - Enhanced validation with security checks
   * Backward compatible - extends existing validation without breaking changes
   */
  validateConfigurationWithSecurity(): ConfigValidationResult {
    // Start with existing validation
    const baseValidation = this.validateConfigurationInternal();

    // Check if security validation is enabled
    const securityValidationEnabled = this.config?.enableSecurityValidation ?? this.defaults.enableSecurityValidation;

    if (!securityValidationEnabled) {
      // Return base validation if security validation is disabled
      return baseValidation;
    }

    // Perform security validation
    const securityValidation = {
      apiKeyStrength: 'weak' as 'weak' | 'moderate' | 'strong',
      endpointSecurity: 'insecure' as 'insecure' | 'secure',
      configurationExposure: this.assessConfigurationExposure(),
      recommendations: [] as string[]
    };

    // Validate Azure API key strength
    if (this.config?.azureApiKey) {
      securityValidation.apiKeyStrength = this.validateApiKeyStrength(this.config.azureApiKey);
      if (securityValidation.apiKeyStrength === 'weak') {
        securityValidation.recommendations.push('Consider using a stronger API key');
      }
    }

    // Validate endpoint security
    if (this.config?.azureEndpoint) {
      securityValidation.endpointSecurity = this.validateEndpointSecurity(this.config.azureEndpoint);
      if (securityValidation.endpointSecurity === 'insecure') {
        securityValidation.recommendations.push('Use HTTPS endpoints for secure communication');
      }
    }

    // Add configuration exposure recommendations
    if (securityValidation.configurationExposure === 'high') {
      securityValidation.recommendations.push('Enable configuration masking for production use');
    }

    return {
      ...baseValidation,
      securityValidation
    };
  }

  /**
   * Phase 1: Security Hardening - Get masked configuration for logging
   * Backward compatible - provides secure logging without breaking existing functionality
   */
  getMaskedConfiguration(): Partial<OpenAIEnvironmentConfig> {
    this.ensureInitialized();

    const maskingEnabled = this.config?.enableConfigurationMasking ?? this.defaults.enableConfigurationMasking;

    if (!maskingEnabled) {
      // Return full config if masking is disabled (backward compatibility)
      return { ...this.config! };
    }

    // Return masked configuration
    const masked = { ...this.config! };

    // Mask sensitive fields
    if (masked.azureApiKey) {
      masked.azureApiKey = `${masked.azureApiKey.substring(0, 4)}****${masked.azureApiKey.substring(masked.azureApiKey.length - 4)}`;
    }
    if (masked.azureMiniApiKey) {
      masked.azureMiniApiKey = `${masked.azureMiniApiKey.substring(0, 4)}****${masked.azureMiniApiKey.substring(masked.azureMiniApiKey.length - 4)}`;
    }
    if (masked.openaiApiKey) {
      masked.openaiApiKey = `${masked.openaiApiKey.substring(0, 4)}****${masked.openaiApiKey.substring(masked.openaiApiKey.length - 4)}`;
    }

    return masked;
  }
}

// Export singleton instance
export const openAIConfigService = OpenAIConfigService.getInstance();
