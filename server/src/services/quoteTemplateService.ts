import { Pool } from 'pg';
import { createModuleLogger } from '../utils/logger';
import { PricingDatabaseService } from './pricingDatabaseService';
import Joi from 'joi';

const logger = createModuleLogger('QuoteTemplateService');

export interface CustomerSegment {
  id: number;
  segment_code: string;
  segment_name: string;
  description: string;
  target_market: string;
  pricing_tier: string;
  default_markup_percentage: number;
  created_at: Date;
  updated_at: Date;
}

export interface QuoteTemplate {
  id: number;
  template_code: string;
  template_name: string;
  description: string;
  version: number;
  parent_template_id?: number;
  customer_segment_id?: number;
  is_default: boolean;
  is_active: boolean;
  template_config: any;
  sections_config: any;
  styling_config: any;
  created_by?: string;
  created_at: Date;
  updated_at: Date;
  customer_segment?: CustomerSegment;
  parent_template?: QuoteTemplate;
}

export interface TemplateInheritance {
  id: number;
  child_template_id: number;
  parent_template_id: number;
  inheritance_type: string;
  inherited_sections?: any;
  created_at: Date;
}

export interface TemplateUsage {
  id: number;
  template_id: number;
  quote_id?: string;
  usage_date: Date;
  user_id?: string;
  success: boolean;
  performance_metrics?: any;
}

export interface CreateTemplateRequest {
  template_code: string;
  template_name: string;
  description?: string;
  parent_template_id?: number;
  customer_segment_id?: number;
  is_default?: boolean;
  template_config: any;
  sections_config: any;
  styling_config?: any;
  created_by?: string;
}

export interface UpdateTemplateRequest {
  template_name?: string;
  description?: string;
  customer_segment_id?: number;
  is_default?: boolean;
  is_active?: boolean;
  template_config?: any;
  sections_config?: any;
  styling_config?: any;
}

// Validation schemas
const createTemplateSchema = Joi.object({
  template_code: Joi.string().min(3).max(50).required(),
  template_name: Joi.string().min(3).max(100).required(),
  description: Joi.string().max(500).optional(),
  parent_template_id: Joi.number().integer().positive().optional(),
  customer_segment_id: Joi.number().integer().positive().optional(),
  is_default: Joi.boolean().optional(),
  template_config: Joi.object().required(),
  sections_config: Joi.object().required(),
  styling_config: Joi.object().optional(),
  created_by: Joi.string().optional()
});

const updateTemplateSchema = Joi.object({
  template_name: Joi.string().min(3).max(100).optional(),
  description: Joi.string().max(500).optional(),
  customer_segment_id: Joi.number().integer().positive().optional(),
  is_default: Joi.boolean().optional(),
  is_active: Joi.boolean().optional(),
  template_config: Joi.object().optional(),
  sections_config: Joi.object().optional(),
  styling_config: Joi.object().optional()
});

export class QuoteTemplateService {
  private static instance: QuoteTemplateService;
  private pool: Pool;
  private isInitialized = false;

  private constructor() {
    this.pool = PricingDatabaseService.getInstance().getPool();
  }

  public static getInstance(): QuoteTemplateService {
    if (!QuoteTemplateService.instance) {
      QuoteTemplateService.instance = new QuoteTemplateService();
    }
    return QuoteTemplateService.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Test database connection
      const client = await this.pool.connect();
      await client.query('SELECT 1');
      client.release();
      
      this.isInitialized = true;
      logger.info('Quote Template Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Quote Template Service:', error);
      throw error;
    }
  }

  public async isAvailable(): Promise<boolean> {
    if (!this.isInitialized) {
      return false;
    }

    try {
      const client = await this.pool.connect();
      await client.query('SELECT 1 FROM quote_templates LIMIT 1');
      client.release();
      return true;
    } catch (error) {
      logger.error('Quote Template Service availability check failed:', error);
      return false;
    }
  }

  // Customer Segments Management
  public async getCustomerSegments(): Promise<CustomerSegment[]> {
    const query = `
      SELECT * FROM customer_segments 
      ORDER BY target_market, pricing_tier, segment_name
    `;
    
    const result = await this.pool.query(query);
    return result.rows.map(this.mapCustomerSegmentRow);
  }

  public async getCustomerSegmentById(id: number): Promise<CustomerSegment | null> {
    const query = `SELECT * FROM customer_segments WHERE id = $1`;
    const result = await this.pool.query(query, [id]);
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return this.mapCustomerSegmentRow(result.rows[0]);
  }

  public async getCustomerSegmentByCode(segmentCode: string): Promise<CustomerSegment | null> {
    const query = `SELECT * FROM customer_segments WHERE segment_code = $1`;
    const result = await this.pool.query(query, [segmentCode]);
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return this.mapCustomerSegmentRow(result.rows[0]);
  }

  // Quote Templates Management
  public async getTemplates(includeInactive: boolean = false): Promise<QuoteTemplate[]> {
    let query = `
      SELECT 
        qt.*,
        cs.segment_code, cs.segment_name, cs.target_market, cs.pricing_tier,
        pt.template_name as parent_template_name
      FROM quote_templates qt
      LEFT JOIN customer_segments cs ON qt.customer_segment_id = cs.id
      LEFT JOIN quote_templates pt ON qt.parent_template_id = pt.id
    `;
    
    if (!includeInactive) {
      query += ` WHERE qt.is_active = true`;
    }
    
    query += ` ORDER BY qt.is_default DESC, cs.target_market, qt.template_name`;
    
    const result = await this.pool.query(query);
    return result.rows.map(this.mapQuoteTemplateRow);
  }

  public async getTemplateById(id: number): Promise<QuoteTemplate | null> {
    const query = `
      SELECT 
        qt.*,
        cs.segment_code, cs.segment_name, cs.target_market, cs.pricing_tier,
        pt.template_name as parent_template_name
      FROM quote_templates qt
      LEFT JOIN customer_segments cs ON qt.customer_segment_id = cs.id
      LEFT JOIN quote_templates pt ON qt.parent_template_id = pt.id
      WHERE qt.id = $1
    `;
    
    const result = await this.pool.query(query, [id]);
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return this.mapQuoteTemplateRow(result.rows[0]);
  }

  public async getTemplateByCode(templateCode: string): Promise<QuoteTemplate | null> {
    const query = `
      SELECT 
        qt.*,
        cs.segment_code, cs.segment_name, cs.target_market, cs.pricing_tier,
        pt.template_name as parent_template_name
      FROM quote_templates qt
      LEFT JOIN customer_segments cs ON qt.customer_segment_id = cs.id
      LEFT JOIN quote_templates pt ON qt.parent_template_id = pt.id
      WHERE qt.template_code = $1 AND qt.is_active = true
    `;
    
    const result = await this.pool.query(query, [templateCode]);
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return this.mapQuoteTemplateRow(result.rows[0]);
  }

  public async getDefaultTemplateForSegment(segmentId: number): Promise<QuoteTemplate | null> {
    const query = `
      SELECT 
        qt.*,
        cs.segment_code, cs.segment_name, cs.target_market, cs.pricing_tier
      FROM quote_templates qt
      LEFT JOIN customer_segments cs ON qt.customer_segment_id = cs.id
      WHERE qt.customer_segment_id = $1 AND qt.is_default = true AND qt.is_active = true
      LIMIT 1
    `;
    
    const result = await this.pool.query(query, [segmentId]);
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return this.mapQuoteTemplateRow(result.rows[0]);
  }

  public async createTemplate(templateData: CreateTemplateRequest): Promise<QuoteTemplate> {
    // Validate input
    const { error, value } = createTemplateSchema.validate(templateData);
    if (error) {
      throw new Error(`Invalid template data: ${error.details.map(d => d.message).join(', ')}`);
    }

    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');

      // Check if template code already exists
      const existingCheck = await client.query(
        'SELECT id FROM quote_templates WHERE template_code = $1',
        [value.template_code]
      );
      
      if (existingCheck.rows.length > 0) {
        throw new Error(`Template with code '${value.template_code}' already exists`);
      }

      // If this is set as default, unset other defaults for the same segment
      if (value.is_default && value.customer_segment_id) {
        await client.query(
          'UPDATE quote_templates SET is_default = false WHERE customer_segment_id = $1',
          [value.customer_segment_id]
        );
      }

      // Insert new template
      const insertQuery = `
        INSERT INTO quote_templates (
          template_code, template_name, description, parent_template_id,
          customer_segment_id, is_default, template_config, sections_config,
          styling_config, created_by
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING id
      `;
      
      const insertResult = await client.query(insertQuery, [
        value.template_code,
        value.template_name,
        value.description || null,
        value.parent_template_id || null,
        value.customer_segment_id || null,
        value.is_default || false,
        JSON.stringify(value.template_config),
        JSON.stringify(value.sections_config),
        JSON.stringify(value.styling_config || {}),
        value.created_by || null
      ]);

      const templateId = insertResult.rows[0].id;

      // Create inheritance relationship if parent template is specified
      if (value.parent_template_id) {
        await client.query(
          `INSERT INTO template_inheritance (child_template_id, parent_template_id, inheritance_type)
           VALUES ($1, $2, 'full')`,
          [templateId, value.parent_template_id]
        );
      }

      await client.query('COMMIT');
      
      logger.info(`Template created successfully: ${value.template_code}`, { templateId });
      
      // Return the created template
      const template = await this.getTemplateById(templateId);
      if (!template) {
        throw new Error('Failed to retrieve created template');
      }
      
      return template;

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Failed to create template:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  public async updateTemplate(id: number, updateData: UpdateTemplateRequest): Promise<QuoteTemplate> {
    // Validate input
    const { error, value } = updateTemplateSchema.validate(updateData);
    if (error) {
      throw new Error(`Invalid update data: ${error.details.map(d => d.message).join(', ')}`);
    }

    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Check if template exists
      const existingTemplate = await this.getTemplateById(id);
      if (!existingTemplate) {
        throw new Error(`Template with ID ${id} not found`);
      }

      // If this is set as default, unset other defaults for the same segment
      if (value.is_default && value.customer_segment_id) {
        await client.query(
          'UPDATE quote_templates SET is_default = false WHERE customer_segment_id = $1 AND id != $2',
          [value.customer_segment_id, id]
        );
      }

      // Build update query dynamically
      const updateFields: string[] = [];
      const updateValues: any[] = [];
      let paramIndex = 1;

      if (value.template_name !== undefined) {
        updateFields.push(`template_name = $${paramIndex++}`);
        updateValues.push(value.template_name);
      }
      if (value.description !== undefined) {
        updateFields.push(`description = $${paramIndex++}`);
        updateValues.push(value.description);
      }
      if (value.customer_segment_id !== undefined) {
        updateFields.push(`customer_segment_id = $${paramIndex++}`);
        updateValues.push(value.customer_segment_id);
      }
      if (value.is_default !== undefined) {
        updateFields.push(`is_default = $${paramIndex++}`);
        updateValues.push(value.is_default);
      }
      if (value.is_active !== undefined) {
        updateFields.push(`is_active = $${paramIndex++}`);
        updateValues.push(value.is_active);
      }
      if (value.template_config !== undefined) {
        updateFields.push(`template_config = $${paramIndex++}`);
        updateValues.push(JSON.stringify(value.template_config));
      }
      if (value.sections_config !== undefined) {
        updateFields.push(`sections_config = $${paramIndex++}`);
        updateValues.push(JSON.stringify(value.sections_config));
      }
      if (value.styling_config !== undefined) {
        updateFields.push(`styling_config = $${paramIndex++}`);
        updateValues.push(JSON.stringify(value.styling_config));
      }

      if (updateFields.length === 0) {
        throw new Error('No fields to update');
      }

      // Add updated_at field
      updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
      updateValues.push(id);

      const updateQuery = `
        UPDATE quote_templates
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
      `;

      await client.query(updateQuery, updateValues);
      await client.query('COMMIT');

      logger.info(`Template updated successfully: ${id}`);

      // Return the updated template
      const template = await this.getTemplateById(id);
      if (!template) {
        throw new Error('Failed to retrieve updated template');
      }

      return template;

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Failed to update template:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  public async deleteTemplate(id: number): Promise<void> {
    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Check if template exists
      const existingTemplate = await this.getTemplateById(id);
      if (!existingTemplate) {
        throw new Error(`Template with ID ${id} not found`);
      }

      // Check if template is being used
      const usageCheck = await client.query(
        'SELECT COUNT(*) as usage_count FROM template_usage WHERE template_id = $1',
        [id]
      );

      const usageCount = parseInt(usageCheck.rows[0].usage_count);
      if (usageCount > 0) {
        // Soft delete - mark as inactive instead of hard delete
        await client.query(
          'UPDATE quote_templates SET is_active = false WHERE id = $1',
          [id]
        );
        logger.info(`Template soft deleted (marked inactive): ${id}`);
      } else {
        // Hard delete if no usage history
        await client.query('DELETE FROM quote_templates WHERE id = $1', [id]);
        logger.info(`Template hard deleted: ${id}`);
      }

      await client.query('COMMIT');

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Failed to delete template:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  public async recordTemplateUsage(
    templateId: number,
    quoteId?: string,
    userId?: string,
    success: boolean = true,
    performanceMetrics?: any
  ): Promise<void> {
    try {
      const query = `
        INSERT INTO template_usage (template_id, quote_id, user_id, success, performance_metrics)
        VALUES ($1, $2, $3, $4, $5)
      `;

      await this.pool.query(query, [
        templateId,
        quoteId || null,
        userId || null,
        success,
        performanceMetrics ? JSON.stringify(performanceMetrics) : null
      ]);

      logger.debug(`Template usage recorded: ${templateId}`);
    } catch (error) {
      logger.error('Failed to record template usage:', error);
      // Don't throw - usage tracking shouldn't break the main flow
    }
  }

  public async getTemplateUsageStats(templateId: number): Promise<any> {
    const query = `
      SELECT
        COUNT(*) as total_usage,
        COUNT(CASE WHEN success = true THEN 1 END) as successful_usage,
        AVG(CASE WHEN performance_metrics->>'generation_time' IS NOT NULL
            THEN (performance_metrics->>'generation_time')::numeric END) as avg_generation_time,
        MAX(usage_date) as last_used
      FROM template_usage
      WHERE template_id = $1
    `;

    const result = await this.pool.query(query, [templateId]);
    return result.rows[0];
  }

  // Helper methods for row mapping
  private mapCustomerSegmentRow(row: any): CustomerSegment {
    return {
      id: row.id,
      segment_code: row.segment_code,
      segment_name: row.segment_name,
      description: row.description,
      target_market: row.target_market,
      pricing_tier: row.pricing_tier,
      default_markup_percentage: parseFloat(row.default_markup_percentage),
      created_at: new Date(row.created_at),
      updated_at: new Date(row.updated_at)
    };
  }

  private mapQuoteTemplateRow(row: any): QuoteTemplate {
    return {
      id: row.id,
      template_code: row.template_code,
      template_name: row.template_name,
      description: row.description,
      version: row.version,
      parent_template_id: row.parent_template_id,
      customer_segment_id: row.customer_segment_id,
      is_default: row.is_default,
      is_active: row.is_active,
      template_config: row.template_config,
      sections_config: row.sections_config,
      styling_config: row.styling_config,
      created_by: row.created_by,
      created_at: new Date(row.created_at),
      updated_at: new Date(row.updated_at),
      customer_segment: row.segment_code ? {
        id: row.customer_segment_id,
        segment_code: row.segment_code,
        segment_name: row.segment_name,
        target_market: row.target_market,
        pricing_tier: row.pricing_tier,
        description: '',
        default_markup_percentage: 0,
        created_at: new Date(),
        updated_at: new Date()
      } : undefined,
      parent_template: row.parent_template_name ? {
        template_name: row.parent_template_name
      } as QuoteTemplate : undefined
    };
  }
}
