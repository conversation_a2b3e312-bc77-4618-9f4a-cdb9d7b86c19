import { createModuleLogger } from '@/utils/logger';
import { AnalysisConfig } from './openaiService';
import { StyleAnalysisPrompts } from './styleAnalysisPrompts';

const logger = createModuleLogger('PromptService');

export interface PromptTemplate {
  id: string;
  version: number;
  text: string;
  performance?: {
    accuracy: {
      cabinet_count: number;
      cabinet_types: number;
      hardware: number;
      linear_metres: number;
    };
    overall_score: number;
  };
  timestamp: string;
}

export class PromptService {
  private prompts: Map<string, PromptTemplate[]> = new Map();

  constructor() {
    this.initializePrompts();
  }

  /**
   * Initialize prompts based on archived A.One Kitchen project prompts
   */
  private initializePrompts() {
    // Kitchen Analysis Prompt (based on kitchen_analysis_v3.json)
    const kitchenAnalysisPrompt: PromptTemplate = {
      id: 'kitchen_analysis',
      version: 3,
      text: `You are an expert in analyzing kitchen design drawings. Your task is to extract detailed information from this kitchen design drawing, focusing on COMPLETE and ACCURATE cabinet counts and measurements.

CRITICAL INSTRUCTION: Count EVERY INDIVIDUAL CABINET in the kitchen. Do not group cabinets together.

Please analyze this kitchen design and provide a detailed JSON response with the following structure:

{
  "analysis_type": "kitchen_design_analysis",
  "cabinets": {
    "total_count": <number>,
    "base_cabinets": <number>,
    "wall_cabinets": <number>,
    "tall_cabinets": <number>,
    "pantry_cabinets": <number>,
    "island_cabinets": <number>,
    "details": [
      {
        "id": "cabinet_1",
        "type": "base|wall|tall|pantry|island",
        "width": <number_in_mm>,
        "height": <number_in_mm>,
        "depth": <number_in_mm>,
        "position": {"x": <number>, "y": <number>},
        "doors": <number>,
        "drawers": <number>,
        "materials": ["material1", "material2"],
        "confidence": <0.0-1.0>
      }
    ]
  },
  "hardware": {
    "hinges": {
      "count": <number>,
      "type": "soft_close|standard",
      "confidence": <0.0-1.0>
    },
    "handles": {
      "count": <number>,
      "style": "bar|knob|integrated",
      "length": <number_in_mm>,
      "confidence": <0.0-1.0>
    },
    "drawer_slides": {
      "count": <number>,
      "type": "soft_close|standard|full_extension",
      "confidence": <0.0-1.0>
    },
    "shelves": {
      "count": <number>,
      "adjustable": <boolean>,
      "confidence": <0.0-1.0>
    }
  },
  "measurements": {
    "linear_meters": <number>,
    "total_area_sqm": <number>,
    "kitchen_dimensions": {
      "length": <number_in_mm>,
      "width": <number_in_mm>,
      "height": <number_in_mm>
    },
    "confidence": <0.0-1.0>
  },
  "materials": {
    "cabinet_finish": ["finish1", "finish2"],
    "cabinet_color": ["color1", "color2"],
    "countertop": "material_type",
    "backsplash": "material_type",
    "flooring": "material_type",
    "confidence": <0.0-1.0>
  },
  "overall_confidence": <0.0-1.0>,
  "notes": "Any additional observations or uncertainties"
}

MEASUREMENT GUIDELINES:
- Measure each cabinet individually
- Standard base cabinet height: 720mm
- Standard wall cabinet height: 720mm
- Standard base cabinet depth: 560mm
- Standard wall cabinet depth: 320mm
- Calculate linear meters by summing cabinet widths

HARDWARE CALCULATION RULES:
- Each cabinet door needs 2 hinges
- Each cabinet needs 1 handle (doors and drawers)
- Each drawer needs 1 set of slides
- Count carefully and double-check totals

Be extremely thorough and accurate. If you're uncertain about any measurement, indicate this in the confidence score and notes.`,
      performance: {
        accuracy: {
          cabinet_count: 96.55,
          cabinet_types: 97.92,
          hardware: 94.33,
          linear_metres: 96.55
        },
        overall_score: 96.295
      },
      timestamp: new Date().toISOString()
    };

    // Material Focus Prompt
    const materialFocusPrompt: PromptTemplate = {
      id: 'material_focus',
      version: 1,
      text: `You are a materials expert specializing in kitchen design analysis. Focus specifically on identifying and analyzing materials, finishes, and colors in this kitchen design.

Pay special attention to:
- Cabinet door materials (solid wood, MDF, plywood, etc.)
- Cabinet finishes (painted, stained, laminate, etc.)
- Cabinet colors and color schemes
- Countertop materials (granite, quartz, laminate, etc.)
- Backsplash materials and patterns
- Hardware finishes (brushed nickel, chrome, black, etc.)
- Flooring materials visible in the design

Provide detailed material specifications with confidence scores for each identification.`,
      timestamp: new Date().toISOString()
    };

    // Hardware Focus Prompt
    const hardwareFocusPrompt: PromptTemplate = {
      id: 'hardware_focus',
      version: 1,
      text: `You are a hardware specialist focusing on kitchen cabinet hardware analysis. Your expertise is in identifying and counting all hardware components accurately.

HARDWARE ANALYSIS PRIORITIES:
1. HINGES: Count every hinge needed (2 per door)
2. HANDLES/KNOBS: Count every handle or knob (1 per door/drawer)
3. DRAWER SLIDES: Count every drawer slide set needed
4. SOFT-CLOSE MECHANISMS: Identify soft-close hinges and slides
5. SPECIALTY HARDWARE: Corner cabinet hardware, lazy susans, etc.

COUNTING METHODOLOGY:
- Examine each cabinet individually
- Count doors and drawers separately
- Apply standard hardware ratios
- Verify totals against cabinet count
- Note any specialty or premium hardware

Provide exact counts with high confidence scores. Include hardware specifications and estimated costs where possible.`,
      timestamp: new Date().toISOString()
    };

    // Store prompts
    this.prompts.set('kitchen_analysis', [kitchenAnalysisPrompt]);
    this.prompts.set('material_focus', [materialFocusPrompt]);
    this.prompts.set('hardware_focus', [hardwareFocusPrompt]);

    logger.info('Initialized prompt templates', {
      promptCount: this.prompts.size,
      prompts: Array.from(this.prompts.keys())
    });
  }

  /**
   * Get the appropriate prompt based on configuration
   * Enhanced with adaptive prompt engineering patterns from archived A.One Kitchen projects
   */
  getAnalysisPrompt(config: AnalysisConfig): string {
    const version = config.promptVersion ? parseInt(config.promptVersion.toString()) : undefined;
    let basePrompt = this.getPrompt('kitchen_analysis', version);

    // Apply advanced prompt engineering techniques
    basePrompt = this.enhancePromptWithContextualFraming(basePrompt, config);

    // Add focus-specific enhancements with improved integration
    if (config.focusOnMaterials) {
      const materialPrompt = this.getPrompt('material_focus');
      basePrompt = this.integrateSpecializedPrompt(basePrompt, materialPrompt, 'MATERIALS');
    }

    if (config.focusOnHardware) {
      const hardwarePrompt = this.getPrompt('hardware_focus');
      basePrompt = this.integrateSpecializedPrompt(basePrompt, hardwarePrompt, 'HARDWARE');
    }

    if (config.enableMultiView) {
      basePrompt = this.addMultiViewInstructions(basePrompt);
    }

    // Add confidence scoring instructions (pattern from archived projects)
    basePrompt = this.addConfidenceScoringInstructions(basePrompt);

    return basePrompt;
  }

  /**
   * Get enhanced style analysis prompt (Priority 1 Enhancement)
   */
  async getEnhancedStyleAnalysisPrompt(config: AnalysisConfig): Promise<string> {
    logger.info('Generating enhanced style analysis prompt', { config });

    // Get the optimized style analysis prompt
    const stylePrompt = await StyleAnalysisPrompts.generateStyleAnalysisPrompt({
      commonErrors: config.commonErrors || [],
      previousPerformance: config.previousPerformance
    });

    // Integrate with existing base prompt if needed
    if (config.integrateWithBase) {
      const basePrompt = this.getAnalysisPrompt(config);
      return this.integrateStyleAnalysisPrompt(basePrompt, stylePrompt);
    }

    return stylePrompt;
  }

  /**
   * Integrate style analysis prompt with base analysis prompt
   */
  private integrateStyleAnalysisPrompt(basePrompt: string, stylePrompt: string): string {
    // Find the appropriate insertion point in the base prompt
    const insertionPoint = basePrompt.indexOf('ANALYSIS REQUIREMENTS:');

    if (insertionPoint !== -1) {
      // Insert style analysis before analysis requirements
      const beforeInsertion = basePrompt.substring(0, insertionPoint);
      const afterInsertion = basePrompt.substring(insertionPoint);

      return `${beforeInsertion}

ENHANCED STYLE ANALYSIS INTEGRATION:
${stylePrompt}

${afterInsertion}`;
    }

    // Fallback: append to end
    return `${basePrompt}

ENHANCED STYLE ANALYSIS:
${stylePrompt}`;
  }

  /**
   * Enhance prompt with contextual framing for better AI understanding
   */
  private enhancePromptWithContextualFraming(basePrompt: string, config: AnalysisConfig): string {
    const contextualFraming = `
ANALYSIS CONTEXT:
- You are an expert kitchen design analyst with 20+ years of experience
- Your analysis will be used for accurate cost estimation and material planning
- Precision in cabinet counts and measurements is critical for project success
- Consider standard kitchen design principles and industry best practices

ANALYSIS APPROACH:
- Use systematic visual inspection methodology
- Cross-reference measurements across multiple visual cues
- Apply domain expertise to validate unusual findings
- Provide confidence levels for each major finding

`;

    return contextualFraming + basePrompt;
  }

  /**
   * Integrate specialized prompts with better context preservation
   */
  private integrateSpecializedPrompt(basePrompt: string, specializedPrompt: string, focusArea: string): string {
    const integration = `

ENHANCED FOCUS AREA - ${focusArea}:
${specializedPrompt}

INTEGRATION INSTRUCTIONS:
- Prioritize ${focusArea.toLowerCase()} analysis while maintaining overall assessment quality
- Provide detailed ${focusArea.toLowerCase()} specifications in addition to standard analysis
- Cross-validate ${focusArea.toLowerCase()} findings with overall design coherence

`;

    return basePrompt + integration;
  }

  /**
   * Add advanced multi-view analysis instructions
   */
  private addMultiViewInstructions(basePrompt: string): string {
    const multiViewInstructions = `

MULTI-VIEW ANALYSIS PROTOCOL:
1. SYSTEMATIC CORRELATION: Analyze each image individually, then correlate findings
2. CONSISTENCY VALIDATION: Verify cabinet counts and measurements across all views
3. PERSPECTIVE COMPENSATION: Account for viewing angles and perspective distortion
4. COMPLETENESS CHECK: Ensure no cabinets are missed or double-counted
5. CONFIDENCE WEIGHTING: Use clearest views for primary measurements, others for validation

MULTI-VIEW SPECIFIC TASKS:
- Create a mental 3D model from all provided views
- Use different angles to verify hidden or partially obscured cabinets
- Cross-reference linear measurements from multiple perspectives
- Identify and resolve any discrepancies between views

`;

    return basePrompt + multiViewInstructions;
  }

  /**
   * Add confidence scoring instructions based on archived project patterns
   */
  private addConfidenceScoringInstructions(basePrompt: string): string {
    const confidenceInstructions = `

CONFIDENCE SCORING METHODOLOGY:
For each analysis component, provide confidence scores (0.0-1.0) based on:

CABINET COUNTS (0.9-1.0: All cabinets clearly visible, 0.7-0.8: Some partially obscured, 0.5-0.6: Significant uncertainty):
- Visual clarity and completeness of cabinet views
- Consistency across multiple viewing angles
- Ability to distinguish individual cabinet units

MEASUREMENTS (0.9-1.0: Clear reference points, 0.7-0.8: Estimated from context, 0.5-0.6: Rough approximation):
- Presence of dimensional annotations or scale references
- Clarity of cabinet boundaries and proportions
- Consistency with standard kitchen dimensions

HARDWARE (0.8-1.0: Hardware clearly visible, 0.6-0.7: Inferred from cabinet types, 0.4-0.5: Standard assumptions):
- Visibility of actual hardware in images
- Consistency with cabinet door/drawer configurations
- Alignment with typical hardware specifications

MATERIALS (0.8-1.0: Materials clearly identifiable, 0.6-0.7: Inferred from visual cues, 0.4-0.5: Standard assumptions):
- Visual clarity of material textures and finishes
- Consistency with typical kitchen material choices
- Presence of material specifications or labels

`;

    return basePrompt + confidenceInstructions;
  }

  /**
   * Get a specific prompt by ID and version
   */
  getPrompt(promptId: string, version?: number): string {
    const promptVersions = this.prompts.get(promptId);
    
    if (!promptVersions || promptVersions.length === 0) {
      logger.warn(`Prompt not found: ${promptId}`);
      return this.getDefaultPrompt();
    }
    
    if (version !== undefined) {
      const specificVersion = promptVersions.find(p => p.version === version);
      if (specificVersion) {
        return specificVersion.text;
      }
      logger.warn(`Prompt version not found: ${promptId} v${version}, using latest`);
    }
    
    // Return latest version (highest version number)
    const latestPrompt = promptVersions.reduce((latest, current) => 
      current.version > latest.version ? current : latest
    );
    
    return latestPrompt.text;
  }

  /**
   * Get validation prompt for dual-model analysis
   */
  getValidationPrompt(primaryAnalysis: string, originalConfig: AnalysisConfig): string {
    return `You are a senior kitchen design analyst performing quality assurance on an AI analysis.

VALIDATION TASK:
Review the following kitchen analysis for accuracy, completeness, and consistency.

ORIGINAL ANALYSIS:
${primaryAnalysis}

VALIDATION CHECKLIST:
1. CABINET COUNT VERIFICATION:
   - Are all cabinets counted individually?
   - Do base/wall/tall counts add up to total?
   - Are cabinet types correctly identified?

2. MEASUREMENT ACCURACY:
   - Are dimensions realistic for kitchen cabinets?
   - Do linear meter calculations match cabinet widths?
   - Are proportions consistent with standard kitchen layouts?

3. HARDWARE CALCULATIONS:
   - Hinges: 2 per door (check against door count)
   - Handles: 1 per door/drawer (check against total)
   - Drawer slides: 1 per drawer (check against drawer count)

4. MATERIAL IDENTIFICATION:
   - Are material identifications reasonable?
   - Are finishes and colors accurately described?
   - Are confidence scores appropriate?

5. OVERALL CONSISTENCY:
   - Do all numbers add up correctly?
   - Are confidence scores realistic?
   - Are there any obvious errors or omissions?

PROVIDE YOUR VALIDATION AS:
{
  "validation_result": "PASS|FAIL|NEEDS_REVIEW",
  "confidence_score": <0.0-1.0>,
  "corrections": [
    {
      "field": "field_name",
      "original_value": "original",
      "corrected_value": "corrected",
      "reason": "explanation"
    }
  ],
  "accuracy_assessment": {
    "cabinet_count": <0.0-1.0>,
    "measurements": <0.0-1.0>,
    "hardware": <0.0-1.0>,
    "materials": <0.0-1.0>
  },
  "overall_quality": <0.0-1.0>,
  "notes": "Additional observations"
}

Be thorough and critical in your validation. Flag any inconsistencies or unrealistic values.`;
  }

  /**
   * Get default fallback prompt
   */
  private getDefaultPrompt(): string {
    return `Analyze this kitchen design image and provide detailed information about:
1. Cabinet count and types
2. Hardware components
3. Measurements and dimensions
4. Materials and finishes

Provide your analysis in a structured JSON format with confidence scores.`;
  }

  /**
   * Add a new prompt template
   */
  addPrompt(prompt: PromptTemplate): void {
    const existing = this.prompts.get(prompt.id) || [];
    existing.push(prompt);
    this.prompts.set(prompt.id, existing);
    
    logger.info(`Added new prompt: ${prompt.id} v${prompt.version}`);
  }

  /**
   * Get prompt performance metrics
   */
  getPromptPerformance(promptId: string, version?: number): any {
    const prompt = this.prompts.get(promptId)?.find(p => 
      version ? p.version === version : true
    );
    
    return prompt?.performance || null;
  }

  /**
   * List available prompts
   */
  listPrompts(): Array<{id: string, versions: number[], latest: number}> {
    return Array.from(this.prompts.entries()).map(([id, versions]) => ({
      id,
      versions: versions.map(v => v.version).sort((a, b) => b - a),
      latest: Math.max(...versions.map(v => v.version))
    }));
  }
}

export const promptService = new PromptService();
