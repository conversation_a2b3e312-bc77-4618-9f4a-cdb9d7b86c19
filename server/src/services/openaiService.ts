/**
 * OpenAI Service - Backward Compatibility Layer
 *
 * This file maintains backward compatibility with the existing OpenAI service API
 * while delegating to the new modular architecture. All new functionality should
 * use the modular services directly from './openai/'.
 *
 * @deprecated Use the modular services from './openai/' for new development
 */

import { createModuleLogger } from '@/utils/logger';
import {
  openaiService as newOpenAIService,
  AnalysisConfig,
  VisionAnalysisResult,
  ReasoningContext,
  EnhancedAnalysisResult
} from './openai';

const logger = createModuleLogger('OpenAIService-Legacy');

// Re-export types for backward compatibility
export { AnalysisConfig, VisionAnalysisResult, ReasoningContext, EnhancedAnalysisResult };

/**
 * OpenAI Service - Backward Compatibility Wrapper
 *
 * @deprecated This class is maintained for backward compatibility only.
 * Use the modular services from './openai/' for new development.
 */
export class OpenAIService {
  constructor() {
    logger.warn('Using legacy OpenAI service - consider migrating to modular services');
  }

  /**
   * Analyze images using GPT-4o Vision (backward compatible method)
   */
  async analyzeImages(
    imagePaths: string[],
    prompt: string,
    config: AnalysisConfig
  ): Promise<VisionAnalysisResult> {
    return await newOpenAIService.analyzeImages(imagePaths, prompt, config);
  }

  /**
   * Enhanced analysis with integrated optimization and reasoning
   */
  async analyzeImagesEnhanced(
    imagePaths: string[],
    analysisId: string,
    config: AnalysisConfig
  ): Promise<EnhancedAnalysisResult> {
    return await newOpenAIService.analyzeImagesEnhanced(imagePaths, analysisId, config);
  }

  /**
   * GPT-o1 specific complex reasoning analysis
   */
  async analyzeWithComplexReasoning(
    imagePaths: string[],
    analysisId: string,
    config: AnalysisConfig,
    reasoningContext: ReasoningContext
  ): Promise<VisionAnalysisResult> {
    return await newOpenAIService.analyzeWithComplexReasoning(imagePaths, analysisId, config, reasoningContext);
  }

  /**
   * Perform reasoning analysis (backward compatibility method)
   */
  async performReasoning(
    primaryContent: string,
    validationPrompt: string,
    config: AnalysisConfig
  ): Promise<VisionAnalysisResult> {
    // Use the complex reasoning service for validation
    const reasoningContext: ReasoningContext = {
      primaryAnalysis: primaryContent,
      validationPrompt,
      analysisType: 'validation',
      confidenceThreshold: 0.7
    };

    // For backward compatibility, we'll use the enhanced analysis with reasoning
    const result = await newOpenAIService.analyzeImagesEnhanced([], 'validation-' + Date.now(), {
      ...config,
      useReasoning: true,
      reasoningContext
    });

    // Convert enhanced result to vision result format for backward compatibility
    return {
      content: result.reasoning?.finalConclusion || primaryContent,
      model: result.model,
      usage: result.usage,
      processingTime: result.processingTime,
      confidence: result.confidence,
      timestamp: result.timestamp
    };
  }

  // Backward compatibility methods

  /**
   * @deprecated Use getHealthStatus instead
   */
  async checkConnection(): Promise<boolean> {
    const health = await newOpenAIService.getHealthStatus();
    return health.status !== 'unhealthy';
  }

  /**
   * @deprecated Use getAvailableModels instead
   */
  isClientAvailable(): boolean {
    return newOpenAIService.getAvailableModels().length > 0;
  }

  /**
   * Get service health status
   */
  async getHealthStatus() {
    return await newOpenAIService.getHealthStatus();
  }

  /**
   * Get available models
   */
  getAvailableModels() {
    return newOpenAIService.getAvailableModels();
  }

  /**
   * Get model capabilities
   */
  getModelCapabilities(modelType: 'GPT4O' | 'GPT4O_MINI' | 'GPTO1') {
    return newOpenAIService.getModelCapabilities(modelType);
  }

  /**
   * Analyze task complexity
   */
  analyzeComplexity(config: AnalysisConfig) {
    return newOpenAIService.analyzeComplexity(config);
  }

  /**
   * Get optimal model for configuration
   */
  determineOptimalModel(config: AnalysisConfig) {
    return newOpenAIService.determineOptimalModel(config);
  }

  /**
   * Get current configuration
   */
  getConfiguration() {
    return newOpenAIService.getConfiguration();
  }

  /**
   * Reload configuration
   */
  reloadConfiguration(): void {
    newOpenAIService.reloadConfiguration();
  }
}

// Export singleton instance for backward compatibility
export const openaiService = new OpenAIService();

// Default export for backward compatibility
export default openaiService;
