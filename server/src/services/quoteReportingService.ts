import jsPDF from 'jspdf';
import fs from 'fs';
import path from 'path';
import { QuoteResult, QuoteTier } from './quotationService';
import { PricingDatabaseService } from './pricingDatabaseService';
import { createModuleLogger } from '../utils/logger';

const logger = createModuleLogger('QuoteReportingService');

export interface QuoteReportOptions {
  template: 'basic' | 'detailed' | 'professional';
  includeBranding: boolean;
  includeAlternatives: boolean;
  includeTermsAndConditions: boolean;
  customLogo?: string;
  companyInfo?: {
    name: string;
    address: string;
    phone: string;
    email: string;
    website: string;
  };
}

export class QuoteReportingService {
  private static instance: QuoteReportingService;
  private pricingDb: PricingDatabaseService;

  private constructor() {
    this.pricingDb = PricingDatabaseService.getInstance();
  }

  public static getInstance(): QuoteReportingService {
    if (!QuoteReportingService.instance) {
      QuoteReportingService.instance = new QuoteReportingService();
    }
    return QuoteReportingService.instance;
  }

  /**
   * Generate PDF quote report
   */
  public async generateQuotePDF(
    quote: QuoteResult,
    options: QuoteReportOptions = {
      template: 'detailed',
      includeBranding: true,
      includeAlternatives: true,
      includeTermsAndConditions: true
    }
  ): Promise<{ buffer: Buffer; filename: string }> {
    const startTime = Date.now();
    
    logger.info(`Generating PDF quote: ${quote.id}`, {
      template: options.template,
      tierCount: quote.tiers.length
    });

    try {
      const doc = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      // Set up document properties
      doc.setProperties({
        title: `Kitchen Cabinet Quote - ${quote.id}`,
        subject: 'Kitchen Cabinet Quotation',
        author: options.companyInfo?.name || 'Cabinet Insight Pro',
        creator: 'Cabinet Insight Pro Quotation System'
      });

      let currentY = 20;

      // Add header and branding
      if (options.includeBranding) {
        currentY = this.addHeader(doc, currentY, options);
      }

      // Add quote information
      currentY = this.addQuoteInfo(doc, currentY, quote);

      // Add project summary
      currentY = this.addProjectSummary(doc, currentY, quote);

      // Add pricing tiers based on template
      switch (options.template) {
        case 'basic':
          currentY = this.addBasicPricing(doc, currentY, quote);
          break;
        case 'detailed':
          currentY = this.addDetailedPricing(doc, currentY, quote);
          break;
        case 'professional':
          currentY = this.addProfessionalPricing(doc, currentY, quote);
          break;
      }

      // Add alternatives if requested
      if (options.includeAlternatives && quote.alternatives.length > 0) {
        currentY = this.addAlternatives(doc, currentY, quote);
      }

      // Add terms and conditions
      if (options.includeTermsAndConditions) {
        currentY = this.addTermsAndConditions(doc, currentY);
      }

      // Add footer
      this.addFooter(doc, options);

      // Generate buffer
      const buffer = Buffer.from(doc.output('arraybuffer'));
      const filename = `quote_${quote.id}_${Date.now()}.pdf`;

      const processingTime = Date.now() - startTime;
      logger.info(`PDF quote generated successfully in ${processingTime}ms`, {
        quoteId: quote.id,
        fileSize: buffer.length,
        template: options.template
      });

      return { buffer, filename };

    } catch (error) {
      logger.error(`PDF quote generation failed: ${quote.id}`, error);
      throw error;
    }
  }

  private addHeader(doc: jsPDF, startY: number, options: QuoteReportOptions): number {
    let currentY = startY;

    // Company logo (if provided)
    if (options.customLogo && fs.existsSync(options.customLogo)) {
      try {
        doc.addImage(options.customLogo, 'PNG', 20, currentY, 40, 20);
      } catch (error) {
        logger.warn('Failed to add logo to PDF', error);
      }
    }

    // Company information
    if (options.companyInfo) {
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text(options.companyInfo.name, 70, currentY + 8);
      
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text(options.companyInfo.address, 70, currentY + 15);
      doc.text(`Phone: ${options.companyInfo.phone}`, 70, currentY + 20);
      doc.text(`Email: ${options.companyInfo.email}`, 70, currentY + 25);
      if (options.companyInfo.website) {
        doc.text(`Website: ${options.companyInfo.website}`, 70, currentY + 30);
      }
    } else {
      // Default branding
      doc.setFontSize(18);
      doc.setFont('helvetica', 'bold');
      doc.text('Cabinet Insight Pro', 20, currentY + 8);
      
      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      doc.text('AI-Powered Kitchen Cabinet Analysis & Quotation', 20, currentY + 18);
    }

    // Title
    doc.setFontSize(20);
    doc.setFont('helvetica', 'bold');
    doc.text('KITCHEN CABINET QUOTATION', 20, currentY + 45);

    return currentY + 60;
  }

  private addQuoteInfo(doc: jsPDF, startY: number, quote: QuoteResult): number {
    let currentY = startY;

    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Quote Information', 20, currentY);

    doc.setFont('helvetica', 'normal');
    doc.setFontSize(10);
    
    const quoteInfo = [
      `Quote ID: ${quote.id}`,
      `Analysis ID: ${quote.analysisId}`,
      `Generated: ${new Date(quote.createdAt).toLocaleDateString('en-NZ')}`,
      `Valid Until: ${new Date(quote.validUntil).toLocaleDateString('en-NZ')}`,
      `Confidence Level: ${(quote.confidence * 100).toFixed(1)}%`
    ];

    quoteInfo.forEach((info, index) => {
      doc.text(info, 20, currentY + 10 + (index * 5));
    });

    return currentY + 40;
  }

  private addProjectSummary(doc: jsPDF, startY: number, quote: QuoteResult): number {
    let currentY = startY;

    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Project Summary', 20, currentY);

    doc.setFont('helvetica', 'normal');
    doc.setFontSize(10);

    const summary = [
      `Total Cabinets: ${quote.summary.cabinetCount}`,
      `Linear Feet: ${quote.summary.linearFeet}`,
      `Complexity: ${quote.summary.complexity.charAt(0).toUpperCase() + quote.summary.complexity.slice(1)}`,
      `Estimated Timeframe: ${quote.summary.estimatedTimeframe}`
    ];

    summary.forEach((item, index) => {
      doc.text(item, 20, currentY + 10 + (index * 5));
    });

    return currentY + 35;
  }

  private addBasicPricing(doc: jsPDF, startY: number, quote: QuoteResult): number {
    let currentY = startY;

    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Pricing Options', 20, currentY);
    currentY += 15;

    quote.tiers.forEach((tier, index) => {
      doc.setFontSize(11);
      doc.setFont('helvetica', 'bold');
      doc.text(`${tier.name}`, 20, currentY);
      
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(10);
      doc.text(tier.description, 20, currentY + 5);
      
      // Total price
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text(`Total: ${this.pricingDb.formatNZD(tier.total)}`, 140, currentY + 5);
      
      currentY += 20;
    });

    return currentY + 10;
  }

  private addDetailedPricing(doc: jsPDF, startY: number, quote: QuoteResult): number {
    let currentY = startY;

    quote.tiers.forEach((tier, tierIndex) => {
      // Check if we need a new page
      if (currentY > 250) {
        doc.addPage();
        currentY = 20;
      }

      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text(`${tier.name}`, 20, currentY);
      
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text(tier.description, 20, currentY + 7);
      currentY += 20;

      // Materials section
      doc.setFontSize(11);
      doc.setFont('helvetica', 'bold');
      doc.text('Materials', 25, currentY);
      currentY += 8;

      tier.materials.items.forEach(item => {
        doc.setFontSize(9);
        doc.setFont('helvetica', 'normal');
        doc.text(`${item.description}`, 30, currentY);
        doc.text(`${item.quantity} × ${this.pricingDb.formatNZD(item.unitPrice)}`, 120, currentY);
        doc.text(`${this.pricingDb.formatNZD(item.totalPrice)}`, 170, currentY);
        currentY += 5;
      });

      doc.setFont('helvetica', 'bold');
      doc.text(`Materials Subtotal: ${this.pricingDb.formatNZD(tier.materials.cost)}`, 120, currentY + 3);
      currentY += 12;

      // Hardware section
      doc.setFontSize(11);
      doc.setFont('helvetica', 'bold');
      doc.text('Hardware', 25, currentY);
      currentY += 8;

      tier.hardware.items.forEach(item => {
        doc.setFontSize(9);
        doc.setFont('helvetica', 'normal');
        doc.text(`${item.description}`, 30, currentY);
        doc.text(`${item.quantity} × ${this.pricingDb.formatNZD(item.unitPrice)}`, 120, currentY);
        doc.text(`${this.pricingDb.formatNZD(item.totalPrice)}`, 170, currentY);
        currentY += 5;
      });

      doc.setFont('helvetica', 'bold');
      doc.text(`Hardware Subtotal: ${this.pricingDb.formatNZD(tier.hardware.cost)}`, 120, currentY + 3);
      currentY += 12;

      // Labor section
      doc.setFontSize(11);
      doc.setFont('helvetica', 'bold');
      doc.text('Installation', 25, currentY);
      currentY += 8;

      tier.labor.items.forEach(item => {
        doc.setFontSize(9);
        doc.setFont('helvetica', 'normal');
        doc.text(`${item.description}`, 30, currentY);
        doc.text(`${item.hours}h × ${this.pricingDb.formatNZD(item.hourlyRate)}`, 120, currentY);
        doc.text(`${this.pricingDb.formatNZD(item.totalPrice)}`, 170, currentY);
        currentY += 5;
      });

      doc.setFont('helvetica', 'bold');
      doc.text(`Labor Subtotal: ${this.pricingDb.formatNZD(tier.labor.cost)}`, 120, currentY + 3);
      currentY += 12;

      // Totals
      doc.setFontSize(12);
      doc.text(`Subtotal: ${this.pricingDb.formatNZD(tier.subtotal)}`, 120, currentY);
      doc.text(`GST (15%): ${this.pricingDb.formatNZD(tier.taxes)}`, 120, currentY + 7);
      
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text(`TOTAL: ${this.pricingDb.formatNZD(tier.total)}`, 120, currentY + 17);
      
      currentY += 35;
    });

    return currentY;
  }

  private addProfessionalPricing(doc: jsPDF, startY: number, quote: QuoteResult): number {
    // Professional template includes detailed pricing plus additional sections
    let currentY = this.addDetailedPricing(doc, startY, quote);

    // Add confidence and quality metrics
    doc.addPage();
    currentY = 20;

    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('Quality Assurance & Confidence Metrics', 20, currentY);
    currentY += 15;

    quote.tiers.forEach(tier => {
      doc.setFontSize(11);
      doc.setFont('helvetica', 'bold');
      doc.text(`${tier.name}:`, 25, currentY);
      
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text(`Confidence Level: ${(tier.confidence * 100).toFixed(1)}%`, 30, currentY + 7);
      
      currentY += 15;
    });

    return currentY + 20;
  }

  private addAlternatives(doc: jsPDF, startY: number, quote: QuoteResult): number {
    let currentY = startY;

    if (currentY > 240) {
      doc.addPage();
      currentY = 20;
    }

    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Cost-Saving Alternatives', 20, currentY);
    currentY += 15;

    quote.alternatives.forEach(alternative => {
      doc.setFontSize(10);
      doc.setFont('helvetica', 'bold');
      doc.text(`• ${alternative.description}`, 25, currentY);
      
      doc.setFont('helvetica', 'normal');
      doc.text(`Impact: ${alternative.impact}`, 30, currentY + 5);
      doc.text(`Cost Difference: ${this.pricingDb.formatNZD(alternative.costDifference)}`, 30, currentY + 10);
      
      currentY += 20;
    });

    return currentY + 10;
  }

  private addTermsAndConditions(doc: jsPDF, startY: number): number {
    let currentY = startY;

    if (currentY > 200) {
      doc.addPage();
      currentY = 20;
    }

    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Terms and Conditions', 20, currentY);
    currentY += 15;

    const terms = [
      '1. This quote is valid for 30 days from the date of issue.',
      '2. Prices are in New Zealand Dollars (NZD) and include GST.',
      '3. Final pricing may vary based on site conditions and material availability.',
      '4. Installation timeline estimates are subject to scheduling availability.',
      '5. A 50% deposit is required to commence work.',
      '6. All materials and workmanship are guaranteed for 12 months.',
      '7. Changes to specifications may result in price adjustments.',
      '8. Site access requirements must be confirmed before installation.'
    ];

    doc.setFontSize(9);
    doc.setFont('helvetica', 'normal');

    terms.forEach(term => {
      doc.text(term, 20, currentY, { maxWidth: 170 });
      currentY += 8;
    });

    return currentY + 10;
  }

  private addFooter(doc: jsPDF, options: QuoteReportOptions): void {
    const pageCount = doc.getNumberOfPages();
    
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      
      doc.setFontSize(8);
      doc.setFont('helvetica', 'normal');
      doc.text(`Page ${i} of ${pageCount}`, 20, 285);
      doc.text(`Generated by Cabinet Insight Pro - ${new Date().toLocaleDateString('en-NZ')}`, 120, 285);
    }
  }
}
