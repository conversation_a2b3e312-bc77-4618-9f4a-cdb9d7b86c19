import express from 'express';
import { createModuleLogger } from '@/utils/logger';
import { GPTCacheService } from '@/services/gptCacheService';

const router = express.Router();
const logger = createModuleLogger('CacheMetricsAPI');

// Initialize cache service for metrics
const cacheService = new GPTCacheService();

/**
 * GET /api/cache/metrics
 * Get comprehensive cache performance metrics
 */
router.get('/metrics', async (req, res) => {
  try {
    const metrics = cacheService.getMetrics();
    
    // Calculate additional derived metrics
    const derivedMetrics = {
      ...metrics,
      performance: {
        hitRatePercentage: Math.round(metrics.hitRate * 100),
        missRatePercentage: Math.round((1 - metrics.hitRate) * 100),
        averageResponseTimeImprovement: metrics.averageResponseTime.uncached > 0
          ? Math.round(((metrics.averageResponseTime.uncached - metrics.averageResponseTime.cached) / metrics.averageResponseTime.uncached) * 100)
          : 0,
        costSavingsPercentage: metrics.totalRequests > 0
          ? Math.round((metrics.cacheHits / metrics.totalRequests) * 100)
          : 0,
        semanticHitRate: metrics.cacheHits > 0
          ? Math.round((metrics.semanticHits / metrics.cacheHits) * 100)
          : 0,
        exactHitRate: metrics.cacheHits > 0
          ? Math.round((metrics.exactHits / metrics.cacheHits) * 100)
          : 0
      },
      efficiency: {
        tokensPerRequest: metrics.totalRequests > 0
          ? Math.round(metrics.costSavings.tokensAvoided / metrics.totalRequests)
          : 0,
        avgCostSavingsPerHit: metrics.cacheHits > 0
          ? (metrics.costSavings.estimatedCostSaved / metrics.cacheHits).toFixed(4)
          : '0.0000',
        memoryEfficiency: metrics.storageStats.totalKeys > 0
          ? Math.round(metrics.storageStats.memoryUsage / metrics.storageStats.totalKeys)
          : 0,
        semanticEfficiency: {
          averageSimilarityScore: Math.round(metrics.semanticMetrics.averageSimilarityScore * 100) / 100,
          avgEmbeddingTime: Math.round(metrics.semanticMetrics.embeddingGenerationTime),
          avgSimilarityCalcTime: Math.round(metrics.semanticMetrics.similarityCalculationTime)
        }
      }
    };

    logger.info('Cache metrics requested', {
      hitRate: metrics.hitRate,
      totalRequests: metrics.totalRequests,
      costSaved: metrics.costSavings.estimatedCostSaved
    });

    res.json({
      success: true,
      data: derivedMetrics,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Failed to get cache metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve cache metrics',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/cache/health
 * Check cache service health status
 */
router.get('/health', async (req, res) => {
  try {
    const isHealthy = await cacheService.healthCheck();
    
    res.json({
      success: true,
      data: {
        status: isHealthy ? 'healthy' : 'unhealthy',
        connected: isHealthy,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Cache health check failed:', error);
    res.status(503).json({
      success: false,
      data: {
        status: 'unhealthy',
        connected: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * POST /api/cache/invalidate
 * Invalidate cache entries by pattern
 */
router.post('/invalidate', async (req, res) => {
  try {
    const { pattern } = req.body;
    
    if (!pattern || typeof pattern !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Pattern is required and must be a string',
        timestamp: new Date().toISOString()
      });
    }

    // Security check: only allow specific patterns
    const allowedPatterns = [
      'gpt_cache:gpto1:*',
      'gpt_cache:gpt4o:*',
      'gpt_cache:gpt4o_mini:*',
      'gpt_cache:*'
    ];

    if (!allowedPatterns.includes(pattern)) {
      return res.status(403).json({
        success: false,
        error: 'Pattern not allowed. Use one of: ' + allowedPatterns.join(', '),
        timestamp: new Date().toISOString()
      });
    }

    const deletedCount = await cacheService.invalidate(pattern);
    
    logger.info(`Cache invalidation completed`, {
      pattern,
      deletedCount
    });

    res.json({
      success: true,
      data: {
        pattern,
        deletedCount,
        message: `Invalidated ${deletedCount} cache entries`
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Cache invalidation failed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to invalidate cache',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * POST /api/cache/warmup
 * Trigger cache warmup for common patterns
 */
router.post('/warmup', async (req, res) => {
  try {
    await cacheService.warmupCache();
    
    logger.info('Cache warmup initiated');

    res.json({
      success: true,
      data: {
        message: 'Cache warmup initiated successfully'
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Cache warmup failed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to initiate cache warmup',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * DELETE /api/cache/clear
 * Clear all cache data (admin only)
 */
router.delete('/clear', async (req, res) => {
  try {
    // In production, you might want to add authentication/authorization here
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Authorization required',
        timestamp: new Date().toISOString()
      });
    }

    const success = await cacheService.clear();
    
    if (success) {
      logger.warn('Cache cleared by admin request');
      res.json({
        success: true,
        data: {
          message: 'Cache cleared successfully'
        },
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to clear cache',
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    logger.error('Cache clear failed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear cache',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/cache/stats/realtime
 * Get real-time cache statistics for monitoring
 */
router.get('/stats/realtime', async (req, res) => {
  try {
    const metrics = cacheService.getMetrics();
    const isHealthy = await cacheService.healthCheck();
    
    // Real-time stats optimized for monitoring dashboards
    const realtimeStats = {
      status: isHealthy ? 'operational' : 'degraded',
      hitRate: metrics.hitRate,
      totalRequests: metrics.totalRequests,
      cacheHits: metrics.cacheHits,
      cacheMisses: metrics.cacheMisses,
      costSavings: {
        totalSaved: metrics.costSavings.estimatedCostSaved,
        tokensAvoided: metrics.costSavings.tokensAvoided
      },
      performance: {
        avgCachedResponseTime: metrics.averageResponseTime.cached,
        avgUncachedResponseTime: metrics.averageResponseTime.uncached
      },
      storage: {
        totalKeys: metrics.storageStats.totalKeys,
        memoryUsage: metrics.storageStats.memoryUsage
      },
      timestamp: new Date().toISOString()
    };

    res.json({
      success: true,
      data: realtimeStats
    });

  } catch (error) {
    logger.error('Failed to get real-time cache stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve real-time statistics',
      timestamp: new Date().toISOString()
    });
  }
});

export default router;
