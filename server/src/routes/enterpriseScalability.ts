import express from 'express';
import { PerformanceMonitoringService } from '../services/performanceMonitoringService';
import { asyncHandler } from '@/middleware/errorHandler';
import { createModuleLogger } from '@/utils/logger';

const logger = createModuleLogger('EnterpriseScalability');

const router = express.Router();

/**
 * GET /api/enterprise/scalability/metrics
 * Get enterprise-grade scalability metrics
 * Phase 3: Enterprise Scalability & Monitoring
 */
router.get('/metrics', asyncHandler(async (req, res) => {
  try {
    const performanceService = PerformanceMonitoringService.getInstance();
    const scalabilityMetrics = await performanceService.getEnterpriseScalabilityMetrics();

    res.json({
      success: true,
      data: {
        ...scalabilityMetrics,
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      }
    });
  } catch (error) {
    logger.error('Failed to get enterprise scalability metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve enterprise scalability metrics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

/**
 * GET /api/enterprise/scalability/health
 * Get enterprise scalability health check
 * Phase 3: Enterprise Scalability & Monitoring
 */
router.get('/health', asyncHandler(async (req, res) => {
  try {
    const performanceService = PerformanceMonitoringService.getInstance();
    const scalabilityMetrics = await performanceService.getEnterpriseScalabilityMetrics();
    
    const healthStatus = {
      status: scalabilityMetrics.enterpriseReadiness.isEnterpriseReady ? 'enterprise-ready' : 'scaling-required',
      readinessScore: scalabilityMetrics.enterpriseReadiness.readinessScore,
      scalabilityScore: scalabilityMetrics.scalabilityHealth.scalabilityScore,
      currentConcurrentUsers: scalabilityMetrics.scalabilityHealth.currentConcurrentUsers,
      maxSupportedUsers: scalabilityMetrics.scalabilityHealth.maxConcurrentUsersSupported,
      criticalIssues: scalabilityMetrics.enterpriseReadiness.criticalIssues,
      recommendations: scalabilityMetrics.enterpriseReadiness.recommendations,
      timestamp: new Date().toISOString()
    };

    const statusCode = scalabilityMetrics.enterpriseReadiness.isEnterpriseReady ? 200 : 206; // 206 = Partial Content

    res.status(statusCode).json({
      success: true,
      data: healthStatus
    });
  } catch (error) {
    logger.error('Failed to get enterprise scalability health:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve enterprise scalability health',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

/**
 * GET /api/enterprise/scalability/targets
 * Get performance targets and current achievement
 * Phase 3: Enterprise Scalability & Monitoring
 */
router.get('/targets', asyncHandler(async (req, res) => {
  try {
    const performanceService = PerformanceMonitoringService.getInstance();
    const scalabilityMetrics = await performanceService.getEnterpriseScalabilityMetrics();
    
    const targets = scalabilityMetrics.performanceTargets;
    const achievements = {
      successRateAchievement: (targets.currentSuccessRate / targets.successRateTarget) * 100,
      responseTimeAchievement: Math.max(0, 100 - ((targets.averageResponseTime / targets.responseTimeTarget) * 100)),
      apiCallReductionAchievement: (targets.currentApiCallReduction / targets.apiCallReductionTarget) * 100
    };

    res.json({
      success: true,
      data: {
        targets,
        achievements,
        overallAchievement: (achievements.successRateAchievement + achievements.responseTimeAchievement + achievements.apiCallReductionAchievement) / 3,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to get enterprise scalability targets:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve enterprise scalability targets',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

/**
 * GET /api/enterprise/scalability/capacity
 * Get current capacity and utilization metrics
 * Phase 3: Enterprise Scalability & Monitoring
 */
router.get('/capacity', asyncHandler(async (req, res) => {
  try {
    const performanceService = PerformanceMonitoringService.getInstance();
    const scalabilityMetrics = await performanceService.getEnterpriseScalabilityMetrics();
    
    const capacity = {
      currentUsers: scalabilityMetrics.scalabilityHealth.currentConcurrentUsers,
      maxUsers: scalabilityMetrics.scalabilityHealth.maxConcurrentUsersSupported,
      utilizationPercentage: (scalabilityMetrics.scalabilityHealth.currentConcurrentUsers / scalabilityMetrics.scalabilityHealth.maxConcurrentUsersSupported) * 100,
      availableCapacity: scalabilityMetrics.scalabilityHealth.maxConcurrentUsersSupported - scalabilityMetrics.scalabilityHealth.currentConcurrentUsers,
      resourceUtilization: scalabilityMetrics.scalabilityHealth.resourceUtilization,
      scalabilityScore: scalabilityMetrics.scalabilityHealth.scalabilityScore,
      timestamp: new Date().toISOString()
    };

    res.json({
      success: true,
      data: capacity
    });
  } catch (error) {
    logger.error('Failed to get enterprise scalability capacity:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve enterprise scalability capacity',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

/**
 * GET /api/enterprise/scalability/recommendations
 * Get actionable recommendations for scaling improvements
 * Phase 3: Enterprise Scalability & Monitoring
 */
router.get('/recommendations', asyncHandler(async (req, res) => {
  try {
    const performanceService = PerformanceMonitoringService.getInstance();
    const scalabilityMetrics = await performanceService.getEnterpriseScalabilityMetrics();
    
    const recommendations = {
      critical: scalabilityMetrics.enterpriseReadiness.criticalIssues,
      improvements: scalabilityMetrics.enterpriseReadiness.recommendations,
      priority: scalabilityMetrics.enterpriseReadiness.criticalIssues.length > 0 ? 'high' : 'medium',
      estimatedImpact: {
        performanceImprovement: scalabilityMetrics.enterpriseReadiness.criticalIssues.length > 0 ? '15-30%' : '5-15%',
        capacityIncrease: scalabilityMetrics.enterpriseReadiness.criticalIssues.length > 0 ? '25-50%' : '10-25%',
        costOptimization: '10-20%'
      },
      implementationComplexity: scalabilityMetrics.enterpriseReadiness.criticalIssues.length > 0 ? 'medium' : 'low',
      timestamp: new Date().toISOString()
    };

    res.json({
      success: true,
      data: recommendations
    });
  } catch (error) {
    logger.error('Failed to get enterprise scalability recommendations:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve enterprise scalability recommendations',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

/**
 * POST /api/enterprise/scalability/test-load
 * Simulate load testing for scalability assessment
 * Phase 3: Enterprise Scalability & Monitoring
 */
router.post('/test-load', asyncHandler(async (req, res) => {
  try {
    const { simulatedUsers = 100, duration = 60 } = req.body;
    
    // Validate input
    if (simulatedUsers < 1 || simulatedUsers > 1000) {
      return res.status(400).json({
        success: false,
        error: 'Invalid simulated users count (1-1000 allowed)'
      });
    }

    if (duration < 10 || duration > 300) {
      return res.status(400).json({
        success: false,
        error: 'Invalid duration (10-300 seconds allowed)'
      });
    }

    // Simulate load test results (in production, this would trigger actual load testing)
    const loadTestResults = {
      testId: `load-test-${Date.now()}`,
      simulatedUsers,
      duration,
      results: {
        averageResponseTime: Math.floor(Math.random() * 2000) + 500, // 500-2500ms
        successRate: Math.max(85, 100 - (simulatedUsers / 50)), // Decreases with more users
        peakConcurrentUsers: Math.floor(simulatedUsers * 0.8),
        resourceUtilization: {
          cpu: Math.min(95, (simulatedUsers / 10) + 20),
          memory: Math.min(90, (simulatedUsers / 15) + 30),
          network: Math.min(85, (simulatedUsers / 20) + 25)
        },
        recommendations: simulatedUsers > 500 ? [
          'Consider implementing horizontal scaling',
          'Optimize database connection pooling',
          'Enable advanced caching strategies'
        ] : [
          'Current capacity sufficient for target load',
          'Monitor resource usage during peak hours'
        ]
      },
      timestamp: new Date().toISOString()
    };

    logger.info(`Load test simulation completed: ${simulatedUsers} users for ${duration}s`);

    res.json({
      success: true,
      data: loadTestResults
    });
  } catch (error) {
    logger.error('Failed to simulate load test:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to simulate load test',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

export { router as enterpriseScalabilityRoutes };
