import express from 'express';
import multer from 'multer';
import { v4 as uuidv4 } from 'uuid';
import { materialRecognitionService } from '../services/materialRecognitionService';
import { createModuleLogger } from '../utils/logger';

const router = express.Router();
const logger = createModuleLogger('MaterialRecognitionRoutes');

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
    files: 10 // Maximum 10 files
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'image/png',
      'image/jpeg',
      'image/jpg',
      'image/gif',
      'image/webp'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only PDF and image files are allowed.'));
    }
  }
});

/**
 * POST /api/analysis/material-recognition
 * Analyze materials in kitchen images with cost estimation
 */
router.post('/material-recognition', upload.array('files', 10), async (req, res) => {
  const analysisId = uuidv4();

  try {
    if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No files uploaded'
      });
    }

    logger.info(`Starting material recognition analysis: ${analysisId}`, {
      fileCount: req.files.length
    });

    // Emit analysis start
    const socketManager = req.app.locals.socketManager;
    if (socketManager) {
      socketManager.sendAnalysisProgress({
        analysisId,
        step: 'material_recognition_start',
        progress: 0,
        message: 'Starting material recognition analysis',
        timestamp: new Date().toISOString()
      });
    }

    // Parse configuration from request body
    const config = {
      enableBrandRecognition: req.body.enableBrandRecognition === 'true',
      enableCostEstimation: req.body.enableCostEstimation === 'true',
      enableQualityAssessment: req.body.enableQualityAssessment === 'true',
      enableSpecificationExtraction: req.body.enableSpecificationExtraction === 'true',
      confidenceThreshold: parseFloat(req.body.confidenceThreshold) || 0.75,
      analysisDepth: req.body.analysisDepth || 'DETAILED',
      costEstimationRegion: req.body.costEstimationRegion || 'US_NATIONAL',
      includeAlternatives: req.body.includeAlternatives === 'true'
    };

    // Get file paths
    const imagePaths = req.files.map(file => file.path);

    // Emit progress update
    if (socketManager) {
      socketManager.sendAnalysisProgress({
        analysisId,
        step: 'material_identification',
        progress: 25,
        message: 'Identifying materials and finishes',
        timestamp: new Date().toISOString()
      });
    }

    // Perform material recognition analysis
    const result = await materialRecognitionService.analyzeMaterials(
      imagePaths,
      analysisId,
      config
    );

    // Emit completion
    if (socketManager) {
      socketManager.sendAnalysisProgress({
        analysisId,
        step: 'material_recognition_complete',
        progress: 100,
        message: 'Completed material recognition and cost estimation',
        timestamp: new Date().toISOString()
      });
    }

    res.json({
      success: true,
      data: {
        analysisId,
        materialRecognition: result,
        processingTime: result.processingMetrics.analysisTime,
        confidence: result.processingMetrics.confidenceScore,
        message: 'Material recognition analysis completed successfully'
      }
    });

  } catch (error) {
    logger.error(`Material recognition analysis failed: ${analysisId}`, { error });

    if (socketManager) {
      socketManager.sendAnalysisProgress({
        analysisId,
        step: 'material_recognition_error',
        progress: 0,
        message: `Analysis failed: ${error.message}`,
        timestamp: new Date().toISOString()
      });
    }

    res.status(500).json({
      success: false,
      error: 'Material recognition analysis failed',
      details: error.message
    });
  }
});

/**
 * POST /api/analysis/material-recognition/batch
 * Batch process multiple material recognition analyses
 */
router.post('/material-recognition/batch', upload.array('files', 50), async (req, res) => {
  const batchId = uuidv4();

  try {
    if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No files uploaded'
      });
    }

    logger.info(`Starting batch material recognition analysis: ${batchId}`, {
      fileCount: req.files.length
    });

    // Parse configuration
    const config = {
      enableBrandRecognition: req.body.enableBrandRecognition === 'true',
      enableCostEstimation: req.body.enableCostEstimation === 'true',
      enableQualityAssessment: req.body.enableQualityAssessment === 'true',
      enableSpecificationExtraction: req.body.enableSpecificationExtraction === 'true',
      confidenceThreshold: parseFloat(req.body.confidenceThreshold) || 0.75,
      analysisDepth: req.body.analysisDepth || 'DETAILED',
      costEstimationRegion: req.body.costEstimationRegion || 'US_NATIONAL',
      includeAlternatives: req.body.includeAlternatives === 'true'
    };

    // Group files for batch processing (process in groups of 5)
    const fileGroups = [];
    for (let i = 0; i < req.files.length; i += 5) {
      fileGroups.push(req.files.slice(i, i + 5));
    }

    const results = [];
    let completedGroups = 0;

    for (const group of fileGroups) {
      const groupAnalysisId = `${batchId}_group_${completedGroups + 1}`;
      const imagePaths = group.map(file => file.path);

      // Emit progress
      const progress = Math.round((completedGroups / fileGroups.length) * 100);
      const socketManager = req.app.locals.socketManager;
      if (socketManager) {
        socketManager.sendAnalysisProgress({
          analysisId: batchId,
          step: 'batch_processing',
          progress,
          message: `Processing group ${completedGroups + 1} of ${fileGroups.length}`,
          timestamp: new Date().toISOString()
        });
      }

      try {
        const result = await materialRecognitionService.analyzeMaterials(
          imagePaths,
          groupAnalysisId,
          config
        );

        results.push({
          groupId: groupAnalysisId,
          files: group.map(f => f.originalname),
          result
        });

        completedGroups++;
      } catch (error) {
        logger.error(`Batch group analysis failed: ${groupAnalysisId}`, { error });
        results.push({
          groupId: groupAnalysisId,
          files: group.map(f => f.originalname),
          error: error.message
        });
      }
    }

    // Calculate overall metrics
    const successfulResults = results.filter(r => !r.error);
    const overallConfidence = successfulResults.length > 0
      ? successfulResults.reduce((sum, r) => sum + r.result.processingMetrics.confidenceScore, 0) / successfulResults.length
      : 0;

    const socketManager = req.app.locals.socketManager;
    if (socketManager) {
      socketManager.sendAnalysisProgress({
        analysisId: batchId,
        step: 'batch_complete',
        progress: 100,
        message: `Batch processing completed: ${successfulResults.length}/${results.length} successful`,
        timestamp: new Date().toISOString()
      });
    }

    res.json({
      success: true,
      data: {
        batchId,
        results,
        summary: {
          totalGroups: fileGroups.length,
          successfulGroups: successfulResults.length,
          failedGroups: results.length - successfulResults.length,
          overallConfidence,
          totalFiles: req.files.length
        }
      }
    });

  } catch (error) {
    logger.error(`Batch material recognition analysis failed: ${batchId}`, { error });
    res.status(500).json({
      success: false,
      error: 'Batch material recognition analysis failed',
      details: error.message
    });
  }
});

/**
 * GET /api/analysis/material-recognition/config
 * Get default configuration options for material recognition
 */
router.get('/material-recognition/config', (req, res) => {
  res.json({
    success: true,
    data: {
      defaultConfig: materialRecognitionService.getDefaultConfig(),
      configOptions: {
        analysisDepths: ['BASIC', 'DETAILED', 'COMPREHENSIVE'],
        confidenceThresholds: [0.6, 0.65, 0.7, 0.75, 0.8, 0.85, 0.9],
        costEstimationRegions: [
          'US_NATIONAL',
          'US_WEST_COAST',
          'US_NORTHEAST',
          'US_SOUTH',
          'US_MIDWEST'
        ],
        supportedFileTypes: [
          'application/pdf',
          'image/png',
          'image/jpeg',
          'image/jpg',
          'image/gif',
          'image/webp'
        ],
        maxFileSize: '50MB',
        maxFiles: 10,
        features: {
          brandRecognition: {
            name: 'Brand Recognition',
            description: 'Identify material brands and manufacturers'
          },
          costEstimation: {
            name: 'Cost Estimation',
            description: 'Generate detailed cost estimates with regional factors'
          },
          qualityAssessment: {
            name: 'Quality Assessment',
            description: 'Evaluate material quality and durability'
          },
          specificationExtraction: {
            name: 'Specification Extraction',
            description: 'Extract technical specifications and dimensions'
          },
          alternatives: {
            name: 'Alternative Materials',
            description: 'Suggest alternative materials with cost comparisons'
          }
        }
      },
      materialDatabase: materialRecognitionService.getMaterialDatabase()
    }
  });
});

/**
 * GET /api/analysis/material-recognition/database
 * Get material database information
 */
router.get('/material-recognition/database', (req, res) => {
  res.json({
    success: true,
    data: materialRecognitionService.getMaterialDatabase()
  });
});

export default router;
