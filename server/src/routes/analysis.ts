/**
 * Analysis Routes - Backward Compatibility Facade
 *
 * Part of Phase 3.3 Analysis Routes Refactoring
 * Original file: 545 lines → 4 specialized handlers (~120-150 lines each)
 *
 * This file maintains backward compatibility with existing route structure
 * while delegating to the new modular architecture. All new functionality should
 * use the modular handlers directly from './analysis/'.
 *
 * @deprecated Use the modular handlers from './analysis/' for new development
 */

import express from 'express';
import { createModuleLogger } from '@/utils/logger';
import { asyncHandler } from '@/middleware/errorHandler';
import { UploadHandler } from './analysis/UploadHandler';
import { AnalysisHandler } from './analysis/AnalysisHandler';
import { StatusHandler } from './analysis/StatusHandler';
import { ConfigHandler } from './analysis/ConfigHandler';

const logger = createModuleLogger('AnalysisRoutes-Legacy');
const router = express.Router();


logger.warn('Using legacy analysis routes - consider migrating to modular handlers');

// ============================================================================
// BACKWARD COMPATIBILITY ROUTES
// ============================================================================

/**
 * POST /api/analysis/enhanced
 * Enhanced analysis with all advanced AI services
 * @deprecated Use UploadHandler.processEnhancedAnalysis directly
 */
router.post('/enhanced', UploadHandler.enhancedAnalysis, asyncHandler(UploadHandler.processEnhancedAnalysis));

/**
 * POST /api/analysis/upload
 * Upload file and start analysis
 * @deprecated Use UploadHandler.processStandardUpload directly
 */
router.post('/upload', UploadHandler.standardUpload, asyncHandler(UploadHandler.processStandardUpload));

/**
 * POST /api/analysis/3d-reconstruction
 * Generate 3D cabinet reconstruction from uploaded images
 * @deprecated Use AnalysisHandler.process3DReconstruction directly
 */
router.post('/3d-reconstruction', AnalysisHandler.reconstruction3D, asyncHandler(AnalysisHandler.process3DReconstruction));

/**
 * GET /api/analysis/queue/status
 * Get queue status
 * @deprecated Use AnalysisHandler.getQueueStats directly
 */
router.get('/queue/status', asyncHandler(AnalysisHandler.getQueueStats));

/**
 * GET /api/analysis/prompts
 * Get available prompts
 * @deprecated Use ConfigHandler.getPrompts directly
 */
router.get('/prompts', asyncHandler(ConfigHandler.getPrompts));

/**
 * GET /api/analysis/prompts/:id
 * Get specific prompt details
 * @deprecated Use ConfigHandler.getPromptDetails directly
 */
router.get('/prompts/:id', asyncHandler(ConfigHandler.getPromptDetails));

/**
 * GET /api/analysis/config/defaults
 * Get default analysis configuration
 * @deprecated Use ConfigHandler.getDefaultConfig directly
 */
router.get('/config/defaults', asyncHandler(ConfigHandler.getDefaultConfig));

/**
 * GET /api/analysis/:id/status
 * Get analysis status and progress
 * @deprecated Use StatusHandler.getAnalysisStatus directly
 */
router.get('/:id/status', asyncHandler(StatusHandler.getAnalysisStatus));

/**
 * GET /api/analysis/:id/results
 * Get analysis results
 * @deprecated Use StatusHandler.getAnalysisResults directly
 */
router.get('/:id/results', asyncHandler(StatusHandler.getAnalysisResults));

/**
 * POST /api/analysis/:id/cancel
 * Cancel analysis (if still in queue)
 * @deprecated Use AnalysisHandler.cancelAnalysis directly
 */
router.post('/:id/cancel', asyncHandler(AnalysisHandler.cancelAnalysis));

// ============================================================================
// ADDITIONAL ROUTES (New modular handlers)
// ============================================================================

/**
 * POST /api/analysis/:id/retry
 * Retry failed analysis
 */
router.post('/:id/retry', asyncHandler(AnalysisHandler.retryAnalysis));

/**
 * GET /api/analysis/:id/metrics
 * Get detailed analysis metrics
 */
router.get('/:id/metrics', asyncHandler(AnalysisHandler.getAnalysisMetrics));

/**
 * POST /api/analysis/batch/status
 * Get multiple analysis statuses (batch operation)
 */
router.post('/batch/status', asyncHandler(StatusHandler.getBatchStatus));

/**
 * GET /api/analysis/history
 * Get analysis history for debugging and monitoring
 */
router.get('/history', asyncHandler(StatusHandler.getAnalysisHistory));

/**
 * GET /api/analysis/system/stats
 * Get system-wide analysis statistics
 */
router.get('/system/stats', asyncHandler(StatusHandler.getSystemStats));

/**
 * POST /api/analysis/config/validate
 * Validate analysis configuration
 */
router.post('/config/validate', asyncHandler(ConfigHandler.validateConfig));

/**
 * GET /api/analysis/config/presets
 * Get configuration presets
 */
router.get('/config/presets', asyncHandler(ConfigHandler.getConfigPresets));

/**
 * GET /api/analysis/upload/config
 * Get upload configuration and limits
 */
router.get('/upload/config', asyncHandler(UploadHandler.getUploadConfig));

/**
 * POST /api/analysis/test-openai
 * Test OpenAI client availability (debugging endpoint)
 */
router.post('/test-openai', async (req, res) => {
  try {
    const { openaiService } = require('../services/openaiService');

    // Check environment variables directly
    const envVars = {
      AZURE_OPENAI_API_KEY: !!process.env.AZURE_OPENAI_API_KEY,
      AZURE_OPENAI_ENDPOINT: !!process.env.AZURE_OPENAI_ENDPOINT,
      AZURE_OPENAI_API_VERSION: process.env.AZURE_OPENAI_API_VERSION,
      OPENAI_API_KEY: !!process.env.OPENAI_API_KEY,
      NODE_ENV: process.env.NODE_ENV
    };

    // Check if clients are available
    const availableModels = openaiService.getAvailableModels();
    const healthStatus = await openaiService.getHealthStatus();

    res.json({
      success: true,
      data: {
        environmentVariables: envVars,
        availableModels,
        healthStatus,
        clientsInitialized: availableModels.length > 0,
        message: availableModels.length > 0
          ? 'OpenAI clients are properly initialized'
          : 'No OpenAI clients available for analysis'
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Failed to check OpenAI client status'
    });
  }
});

export { router as analysisRoutes };
