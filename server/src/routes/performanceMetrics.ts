import express from 'express';
import { createModuleLogger } from '@/utils/logger';
import { PerformanceMetricsService } from '@/services/performanceMetricsService';
import { asyncHandler } from '@/middleware/asyncHandler';

const router = express.Router();
const logger = createModuleLogger('PerformanceMetricsAPI');

// Initialize performance metrics service
const performanceMetricsService = new PerformanceMetricsService();

/**
 * GET /api/performance/overview
 * Get comprehensive performance overview with model comparisons
 */
router.get('/overview', asyncHandler(async (req, res) => {
  try {
    const timeRange = req.query.timeRange as string || '24h';
    
    const [
      modelComparison,
      usagePatterns,
      costAnalysis,
      activeAlerts
    ] = await Promise.all([
      performanceMetricsService.getModelPerformanceComparison(timeRange),
      performanceMetricsService.getUsagePatterns(7),
      performanceMetricsService.getCostAnalysis(),
      performanceMetricsService.getActiveAlerts()
    ]);

    const overview = {
      modelComparison,
      usagePatterns: usagePatterns.slice(0, 24), // Last 24 hours
      costAnalysis,
      activeAlerts,
      summary: {
        totalRequests: modelComparison.totalRequests,
        totalCost: modelComparison.totalCost,
        mostEfficientModel: modelComparison.mostEfficientModel,
        alertCount: activeAlerts.length,
        criticalAlerts: activeAlerts.filter(alert => alert.severity === 'CRITICAL').length
      }
    };

    logger.info('Performance overview requested', {
      timeRange,
      totalRequests: overview.summary.totalRequests,
      alertCount: overview.summary.alertCount
    });

    res.json({
      success: true,
      data: overview,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Failed to get performance overview:', error);
    throw error;
  }
}));

/**
 * GET /api/performance/models/comparison
 * Get detailed model performance comparison
 */
router.get('/models/comparison', asyncHandler(async (req, res) => {
  try {
    const timeRange = req.query.timeRange as string || '24h';
    const comparison = await performanceMetricsService.getModelPerformanceComparison(timeRange);

    logger.info('Model comparison requested', {
      timeRange,
      modelCount: comparison.models.length,
      totalRequests: comparison.totalRequests
    });

    res.json({
      success: true,
      data: comparison,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Failed to get model comparison:', error);
    throw error;
  }
}));

/**
 * GET /api/performance/usage-patterns
 * Get usage patterns analysis
 */
router.get('/usage-patterns', asyncHandler(async (req, res) => {
  try {
    const days = parseInt(req.query.days as string) || 7;
    const patterns = await performanceMetricsService.getUsagePatterns(days);

    logger.info('Usage patterns requested', {
      days,
      patternCount: patterns.length
    });

    res.json({
      success: true,
      data: {
        patterns,
        summary: {
          totalDataPoints: patterns.length,
          timeRange: `${days} days`,
          peakUsageHour: patterns.reduce((peak, current) => 
            current.requests > peak.requests ? current : peak
          ).hour
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Failed to get usage patterns:', error);
    throw error;
  }
}));

/**
 * GET /api/performance/cost-analysis
 * Get comprehensive cost analysis
 */
router.get('/cost-analysis', asyncHandler(async (req, res) => {
  try {
    const costAnalysis = await performanceMetricsService.getCostAnalysis();

    logger.info('Cost analysis requested', {
      dailyCost: costAnalysis.dailyCost,
      monthlyCost: costAnalysis.monthlyCost,
      trend: costAnalysis.costTrend
    });

    res.json({
      success: true,
      data: costAnalysis,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Failed to get cost analysis:', error);
    throw error;
  }
}));

/**
 * GET /api/performance/alerts
 * Get active performance alerts
 */
router.get('/alerts', asyncHandler(async (req, res) => {
  try {
    const alerts = performanceMetricsService.getActiveAlerts();
    const thresholds = performanceMetricsService.getAlertThresholds();

    logger.info('Performance alerts requested', {
      activeAlerts: alerts.length,
      thresholds: thresholds.length
    });

    res.json({
      success: true,
      data: {
        alerts,
        thresholds,
        summary: {
          total: alerts.length,
          critical: alerts.filter(a => a.severity === 'CRITICAL').length,
          high: alerts.filter(a => a.severity === 'HIGH').length,
          medium: alerts.filter(a => a.severity === 'MEDIUM').length,
          low: alerts.filter(a => a.severity === 'LOW').length,
          unacknowledged: alerts.filter(a => !a.acknowledged).length
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Failed to get performance alerts:', error);
    throw error;
  }
}));

/**
 * POST /api/performance/alerts/:alertId/acknowledge
 * Acknowledge a performance alert
 */
router.post('/alerts/:alertId/acknowledge', asyncHandler(async (req, res) => {
  try {
    const { alertId } = req.params;
    const success = performanceMetricsService.acknowledgeAlert(alertId);

    if (success) {
      logger.info('Alert acknowledged', { alertId });
      res.json({
        success: true,
        message: 'Alert acknowledged successfully',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Alert not found',
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    logger.error('Failed to acknowledge alert:', error);
    throw error;
  }
}));

/**
 * PUT /api/performance/thresholds/:thresholdId
 * Update alert threshold configuration
 */
router.put('/thresholds/:thresholdId', asyncHandler(async (req, res) => {
  try {
    const { thresholdId } = req.params;
    const updates = req.body;

    const success = performanceMetricsService.updateAlertThreshold(thresholdId, updates);

    if (success) {
      logger.info('Alert threshold updated', { thresholdId, updates });
      res.json({
        success: true,
        message: 'Threshold updated successfully',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Threshold not found',
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    logger.error('Failed to update threshold:', error);
    throw error;
  }
}));

/**
 * GET /api/performance/dashboard
 * Get comprehensive dashboard metrics for GPT-o1 Integration Enhancement
 */
router.get('/dashboard', asyncHandler(async (req, res) => {
  try {
    const timeRange = req.query.timeRange as string || '24h';

    const dashboardMetrics = await performanceMetricsService.getDashboardMetrics(timeRange);

    logger.info('Dashboard metrics requested', {
      timeRange,
      sections: Object.keys(dashboardMetrics)
    });

    res.json({
      success: true,
      data: dashboardMetrics,
      timeRange,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get dashboard metrics:', error);
    throw error;
  }
}));

/**
 * GET /api/performance/gpt-o1-analytics
 * Get detailed GPT-o1 specific analytics
 */
router.get('/gpt-o1-analytics', asyncHandler(async (req, res) => {
  try {
    const timeRange = req.query.timeRange as string || '24h';

    const gptO1Analytics = await performanceMetricsService.getGPTO1Analytics(timeRange);

    res.json({
      success: true,
      data: gptO1Analytics,
      timeRange,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get GPT-o1 analytics:', error);
    throw error;
  }
}));

/**
 * GET /api/performance/caching-efficiency
 * Get detailed caching efficiency metrics
 */
router.get('/caching-efficiency', asyncHandler(async (req, res) => {
  try {
    const cachingEfficiency = await performanceMetricsService.getCachingEfficiencyMetrics();

    res.json({
      success: true,
      data: cachingEfficiency,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get caching efficiency metrics:', error);
    throw error;
  }
}));

/**
 * POST /api/performance/alerts/thresholds
 * Update alert thresholds
 */
router.post('/alerts/thresholds', asyncHandler(async (req, res) => {
  try {
    const { metric, threshold, enabled } = req.body;

    if (!metric || !threshold) {
      return res.status(400).json({
        success: false,
        error: 'Metric and threshold are required'
      });
    }

    performanceMetricsService.setAlertThreshold(metric, {
      threshold,
      enabled: enabled !== false,
      severity: req.body.severity || 'warning'
    });

    logger.info('Alert threshold updated', { metric, threshold, enabled });

    res.json({
      success: true,
      message: 'Alert threshold updated successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to update alert threshold:', error);
    throw error;
  }
}));

/**
 * GET /api/performance/export
 * Export performance metrics data
 */
router.get('/export', asyncHandler(async (req, res) => {
  try {
    const format = (req.query.format as string)?.toUpperCase() as 'CSV' | 'JSON' || 'JSON';
    const timeRange = req.query.timeRange as string || '24h';

    const exportData = await performanceMetricsService.exportMetricsData(format, timeRange);

    const filename = `performance-metrics-${timeRange}-${new Date().toISOString().split('T')[0]}.${format.toLowerCase()}`;

    logger.info('Performance metrics export requested', {
      format,
      timeRange,
      filename
    });

    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', format === 'CSV' ? 'text/csv' : 'application/json');
    res.send(exportData);

  } catch (error) {
    logger.error('Failed to export performance metrics:', error);
    throw error;
  }
}));

export { router as performanceMetricsRoutes };
