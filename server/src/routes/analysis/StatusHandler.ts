/**
 * Status Handler - Specialized route handler for analysis status and results
 * 
 * Part of Phase 3.3 Analysis Routes Refactoring
 * Extracted from analysis.ts (545 lines → modular architecture)
 * 
 * Responsibilities:
 * - Analysis status tracking and reporting
 * - Results retrieval and formatting
 * - Progress monitoring and updates
 * - Error status management
 */

import express, { Request, Response } from 'express';
import { createModuleLogger } from '@/utils/logger';
import { asyncHandler, ValidationError } from '@/middleware/errorHandler';
import { AnalysisQueue } from '@/services/analysisQueue';

const logger = createModuleLogger('StatusHandler');

export class StatusHandler {
  /**
   * Get analysis status and progress
   */
  static async getAnalysisStatus(req: Request, res: Response): Promise<void> {
    const analysisId = req.params.id;
    if (!analysisId) {
      throw new ValidationError('Analysis ID is required');
    }

    logger.debug('Getting analysis status', { analysisId });

    try {
      const analysisQueue: AnalysisQueue = req.app.locals.analysisQueue;
      const job = analysisQueue.getJobStatus(analysisId);
      
      if (!job) {
        throw new ValidationError('Analysis not found');
      }

      const response = {
        success: true,
        data: {
          analysisId: job.id,
          status: job.status,
          progress: job.progress,
          currentStep: job.currentStep,
          createdAt: job.createdAt,
          startedAt: job.startedAt,
          completedAt: job.completedAt,
          error: job.error,
          config: job.config,
          // Additional computed fields
          isCompleted: job.status === 'COMPLETED',
          isFailed: job.status === 'FAILED',
          isProcessing: job.status === 'PROCESSING',
          isQueued: job.status === 'QUEUED',
          processingTime: job.startedAt && job.completedAt ? 
            job.completedAt.getTime() - job.startedAt.getTime() : null,
          queueTime: job.startedAt ? 
            job.startedAt.getTime() - job.createdAt.getTime() : null,
          totalTime: job.completedAt ? 
            job.completedAt.getTime() - job.createdAt.getTime() : null
        }
      };

      res.json(response);

    } catch (error) {
      logger.error('Failed to get analysis status', { analysisId, error });
      throw error;
    }
  }

  /**
   * Get analysis results
   */
  static async getAnalysisResults(req: Request, res: Response): Promise<void> {
    const analysisId = req.params.id;
    if (!analysisId) {
      throw new ValidationError('Analysis ID is required');
    }

    logger.debug('Getting analysis results', { analysisId });

    try {
      const analysisQueue: AnalysisQueue = req.app.locals.analysisQueue;
      const job = analysisQueue.getJobStatus(analysisId);
      
      if (!job) {
        throw new ValidationError('Analysis not found');
      }

      if (job.status !== 'COMPLETED') {
        throw new ValidationError(`Analysis not completed. Current status: ${job.status}`);
      }

      const response = {
        success: true,
        data: {
          analysisId: job.id,
          results: job.results,
          completedAt: job.completedAt,
          processingTime: job.startedAt && job.completedAt ? 
            job.completedAt.getTime() - job.startedAt.getTime() : null,
          totalTime: job.completedAt ? 
            job.completedAt.getTime() - job.createdAt.getTime() : null,
          config: job.config,
          // Results metadata
          hasResults: !!job.results,
          resultKeys: job.results ? Object.keys(job.results) : [],
          resultSize: job.results ? JSON.stringify(job.results).length : 0
        }
      };

      res.json(response);

    } catch (error) {
      logger.error('Failed to get analysis results', { analysisId, error });
      throw error;
    }
  }

  /**
   * Get multiple analysis statuses (batch operation)
   */
  static async getBatchStatus(req: Request, res: Response): Promise<void> {
    const analysisIds = req.body.analysisIds;
    
    if (!analysisIds || !Array.isArray(analysisIds)) {
      throw new ValidationError('analysisIds array is required');
    }

    if (analysisIds.length === 0) {
      throw new ValidationError('At least one analysis ID is required');
    }

    if (analysisIds.length > 50) {
      throw new ValidationError('Maximum 50 analysis IDs allowed per batch request');
    }

    logger.debug('Getting batch analysis status', { count: analysisIds.length });

    try {
      const analysisQueue: AnalysisQueue = req.app.locals.analysisQueue;
      const results = [];

      for (const analysisId of analysisIds) {
        try {
          const job = analysisQueue.getJobStatus(analysisId);
          
          if (job) {
            results.push({
              analysisId: job.id,
              status: job.status,
              progress: job.progress,
              currentStep: job.currentStep,
              createdAt: job.createdAt,
              startedAt: job.startedAt,
              completedAt: job.completedAt,
              error: job.error,
              isCompleted: job.status === 'COMPLETED',
              isFailed: job.status === 'FAILED',
              isProcessing: job.status === 'PROCESSING',
              isQueued: job.status === 'QUEUED'
            });
          } else {
            results.push({
              analysisId,
              status: 'NOT_FOUND',
              error: 'Analysis not found'
            });
          }
        } catch (error) {
          results.push({
            analysisId,
            status: 'ERROR',
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      res.json({
        success: true,
        data: {
          results,
          total: results.length,
          found: results.filter(r => r.status !== 'NOT_FOUND' && r.status !== 'ERROR').length,
          notFound: results.filter(r => r.status === 'NOT_FOUND').length,
          errors: results.filter(r => r.status === 'ERROR').length
        }
      });

    } catch (error) {
      logger.error('Failed to get batch analysis status', error);
      throw error;
    }
  }

  /**
   * Get analysis history for debugging and monitoring
   */
  static async getAnalysisHistory(req: Request, res: Response): Promise<void> {
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;
    const status = req.query.status as string;

    if (limit > 100) {
      throw new ValidationError('Maximum limit is 100');
    }

    logger.debug('Getting analysis history', { limit, offset, status });

    try {
      const analysisQueue: AnalysisQueue = req.app.locals.analysisQueue;
      
      // Get all jobs (this would need to be implemented in AnalysisQueue)
      // For now, we'll return a placeholder response
      const jobs = []; // analysisQueue.getJobHistory(limit, offset, status);

      const response = {
        success: true,
        data: {
          jobs: jobs.map(job => ({
            analysisId: job.id,
            status: job.status,
            filename: job.filename,
            createdAt: job.createdAt,
            startedAt: job.startedAt,
            completedAt: job.completedAt,
            processingTime: job.startedAt && job.completedAt ? 
              job.completedAt.getTime() - job.startedAt.getTime() : null,
            config: {
              useGPT4o: job.config.useGPT4o,
              useReasoning: job.config.useReasoning,
              enable3DReconstruction: job.config.enable3DReconstruction,
              enableMaterialRecognition: job.config.enableMaterialRecognition
            }
          })),
          pagination: {
            limit,
            offset,
            total: jobs.length,
            hasMore: jobs.length === limit
          },
          filters: {
            status
          }
        }
      };

      res.json(response);

    } catch (error) {
      logger.error('Failed to get analysis history', error);
      throw error;
    }
  }

  /**
   * Get system-wide analysis statistics
   */
  static async getSystemStats(req: Request, res: Response): Promise<void> {
    logger.debug('Getting system analysis statistics');

    try {
      const analysisQueue: AnalysisQueue = req.app.locals.analysisQueue;
      const queueStats = analysisQueue.getQueueStats();

      // Calculate additional system metrics
      const systemStats = {
        queue: queueStats,
        performance: {
          averageProcessingTime: 0, // Would calculate from job history
          successRate: 0, // Would calculate from job history
          throughput: 0 // Jobs per hour
        },
        resources: {
          memoryUsage: process.memoryUsage(),
          uptime: process.uptime(),
          nodeVersion: process.version
        },
        timestamp: new Date().toISOString()
      };

      res.json({
        success: true,
        data: systemStats
      });

    } catch (error) {
      logger.error('Failed to get system stats', error);
      throw error;
    }
  }
}
