/**
 * Analysis Handler - Specialized route handler for analysis operations
 * 
 * Part of Phase 3.3 Analysis Routes Refactoring
 * Extracted from analysis.ts (545 lines → modular architecture)
 * 
 * Responsibilities:
 * - 3D reconstruction analysis
 * - Specialized analysis workflows
 * - Analysis cancellation and management
 * - Real-time progress tracking via WebSocket
 */

import express, { Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { createModuleLogger } from '@/utils/logger';
import { asyncHandler, ValidationError } from '@/middleware/errorHandler';
import { AnalysisQueue } from '@/services/analysisQueue';
import { SocketManager } from '@/services/socketManager';
import { EnhancedPdfProcessor } from '@/services/enhancedPdfProcessor';
import { cabinetReconstructionService } from '@/services/cabinetReconstructionService';

const logger = createModuleLogger('AnalysisHandler');
const enhancedPdfProcessor = new EnhancedPdfProcessor();

// Reuse upload configuration from UploadHandler
const uploadDir = process.env.UPLOAD_DIR || './uploads';
const maxFileSize = parseInt(process.env.MAX_FILE_SIZE || '52428800');

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = `${Date.now()}_${uuidv4()}`;
    const ext = path.extname(file.originalname);
    cb(null, `analysis_${uniqueSuffix}${ext}`);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: maxFileSize, files: 1 },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new ValidationError(`Unsupported file type: ${file.mimetype}`));
    }
  }
});

export class AnalysisHandler {
  /**
   * 3D cabinet reconstruction analysis
   */
  static reconstruction3D = upload.single('file');
  
  static async process3DReconstruction(req: Request, res: Response): Promise<void> {
    if (!req.file) {
      throw new ValidationError('No file uploaded');
    }

    const analysisId = uuidv4();

    // Parse 3D reconstruction configuration
    const reconstructionConfig = {
      enableDepthEstimation: req.body.enableDepthEstimation !== 'false',
      spatialResolution: req.body.spatialResolution || 'HIGH',
      includeHardwarePositioning: req.body.includeHardwarePositioning === 'true',
      optimizeForAccuracy: req.body.optimizeForAccuracy !== 'false',
      generateWireframe: req.body.generateWireframe === 'true'
    };

    logger.info('Starting 3D cabinet reconstruction', {
      analysisId,
      filename: req.file.originalname,
      size: req.file.size,
      config: reconstructionConfig
    });

    try {
      // Emit progress update via WebSocket
      const socketManager: SocketManager = req.app.locals.socketManager;
      socketManager.sendAnalysisProgress({
        analysisId,
        step: '3d_reconstruction_start',
        progress: 10,
        message: 'Starting 3D cabinet reconstruction...',
        timestamp: new Date().toISOString()
      });

      // Process the uploaded file for 3D reconstruction
      const imagePaths = [req.file.path];

      // If it's a PDF, convert to images first
      if (req.file.mimetype === 'application/pdf') {
        const tempDir = path.join(path.dirname(req.file.path), '3d_processing');
        const pdfResults = await enhancedPdfProcessor.processPdf(req.file.path, tempDir, {
          outputFormat: 'png',
          quality: 95,
          density: 300,
          optimizeForAnalysis: true
        });

        imagePaths.length = 0; // Clear original path
        imagePaths.push(...pdfResults.pages.map(page => page.imagePath));
      }

      // Emit progress update
      socketManager.sendAnalysisProgress({
        analysisId,
        step: 'spatial_analysis',
        progress: 30,
        message: 'Performing spatial analysis and depth estimation...',
        timestamp: new Date().toISOString()
      });

      // Perform 3D reconstruction
      const reconstructionResult = await cabinetReconstructionService.reconstructCabinets(
        imagePaths,
        analysisId,
        reconstructionConfig
      );

      // Emit completion
      socketManager.sendAnalysisProgress({
        analysisId,
        step: '3d_reconstruction_complete',
        progress: 100,
        message: 'Completed 3D cabinet reconstruction',
        timestamp: new Date().toISOString()
      });

      res.json({
        success: true,
        data: {
          analysisId,
          reconstruction3D: reconstructionResult,
          processingTime: reconstructionResult.reconstructionMetrics.reconstructionTime,
          confidence: reconstructionResult.confidence,
          message: '3D cabinet reconstruction completed successfully'
        }
      });

    } catch (error) {
      // Clean up uploaded file on error
      fs.unlink(req.file.path, (unlinkError) => {
        if (unlinkError) {
          logger.warn('Failed to cleanup uploaded file:', unlinkError);
        }
      });

      // Emit error via WebSocket
      const socketManager: SocketManager = req.app.locals.socketManager;
      socketManager.sendAnalysisError(analysisId, error instanceof Error ? error.message : 'Unknown error');

      throw error;
    }
  }

  /**
   * Cancel analysis (if still in queue)
   */
  static async cancelAnalysis(req: Request, res: Response): Promise<void> {
    const analysisId = req.params.id;
    if (!analysisId) {
      throw new ValidationError('Analysis ID is required');
    }

    logger.info('Cancelling analysis', { analysisId });

    try {
      const analysisQueue: AnalysisQueue = req.app.locals.analysisQueue;
      const cancelled = analysisQueue.cancelJob(analysisId);

      if (cancelled) {
        // Emit cancellation via WebSocket
        const socketManager: SocketManager = req.app.locals.socketManager;
        socketManager.sendAnalysisProgress({
          analysisId,
          step: 'cancelled',
          progress: 0,
          message: 'Analysis cancelled by user',
          timestamp: new Date().toISOString()
        });

        res.json({
          success: true,
          data: {
            analysisId,
            status: 'CANCELLED',
            message: 'Analysis cancelled successfully'
          }
        });
      } else {
        throw new ValidationError('Analysis not found or cannot be cancelled');
      }

    } catch (error) {
      logger.error('Failed to cancel analysis', { analysisId, error });
      throw error;
    }
  }

  /**
   * Retry failed analysis
   */
  static async retryAnalysis(req: Request, res: Response): Promise<void> {
    const analysisId = req.params.id;
    if (!analysisId) {
      throw new ValidationError('Analysis ID is required');
    }

    logger.info('Retrying analysis', { analysisId });

    try {
      const analysisQueue: AnalysisQueue = req.app.locals.analysisQueue;
      const job = analysisQueue.getJobStatus(analysisId);

      if (!job) {
        throw new ValidationError('Analysis not found');
      }

      if (job.status !== 'FAILED') {
        throw new ValidationError(`Cannot retry analysis with status: ${job.status}`);
      }

      // Create new job with same configuration
      const newJobId = await analysisQueue.addJob(
        job.filePath,
        job.filename,
        job.config
      );

      res.json({
        success: true,
        data: {
          originalAnalysisId: analysisId,
          newAnalysisId: newJobId,
          status: 'QUEUED',
          message: 'Analysis retry initiated successfully'
        }
      });

    } catch (error) {
      logger.error('Failed to retry analysis', { analysisId, error });
      throw error;
    }
  }

  /**
   * Get analysis queue statistics
   */
  static async getQueueStats(req: Request, res: Response): Promise<void> {
    try {
      const analysisQueue: AnalysisQueue = req.app.locals.analysisQueue;
      const stats = analysisQueue.getQueueStats();

      res.json({
        success: true,
        data: {
          queue: stats,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('Failed to get queue stats', error);
      throw error;
    }
  }

  /**
   * Get detailed analysis metrics
   */
  static async getAnalysisMetrics(req: Request, res: Response): Promise<void> {
    const analysisId = req.params.id;
    if (!analysisId) {
      throw new ValidationError('Analysis ID is required');
    }

    try {
      const analysisQueue: AnalysisQueue = req.app.locals.analysisQueue;
      const job = analysisQueue.getJobStatus(analysisId);

      if (!job) {
        throw new ValidationError('Analysis not found');
      }

      // Calculate detailed metrics
      const metrics = {
        analysisId: job.id,
        status: job.status,
        progress: job.progress,
        currentStep: job.currentStep,
        createdAt: job.createdAt,
        startedAt: job.startedAt,
        completedAt: job.completedAt,
        processingTime: job.startedAt && job.completedAt ? 
          job.completedAt.getTime() - job.startedAt.getTime() : null,
        queueTime: job.startedAt ? 
          job.startedAt.getTime() - job.createdAt.getTime() : null,
        config: job.config,
        error: job.error
      };

      res.json({
        success: true,
        data: metrics
      });

    } catch (error) {
      logger.error('Failed to get analysis metrics', { analysisId, error });
      throw error;
    }
  }
}
