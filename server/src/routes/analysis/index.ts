/**
 * Analysis Routes - Modular Architecture Index
 * 
 * Part of Phase 3.3 Analysis Routes Refactoring
 * Extracted from analysis.ts (545 lines → modular architecture)
 * 
 * This file provides a clean interface to all analysis route handlers
 * and maintains backward compatibility with existing route structure.
 */

export { UploadHandler } from './UploadHandler';
export { AnalysisHandler } from './AnalysisHandler';
export { StatusHandler } from './StatusHandler';
export { ConfigHandler } from './ConfigHandler';

// Re-export common types for convenience
export type { AnalysisConfig } from '@/services/openaiService';
