/**
 * Config <PERSON> - Specialized route handler for analysis configuration
 * 
 * Part of Phase 3.3 Analysis Routes Refactoring
 * Extracted from analysis.ts (545 lines → modular architecture)
 * 
 * Responsibilities:
 * - Analysis configuration management
 * - Prompt management and retrieval
 * - Default configuration provision
 * - Configuration validation and optimization
 */

import express, { Request, Response } from 'express';
import { createModuleLogger } from '@/utils/logger';
import { asyncHandler, ValidationError } from '@/middleware/errorHandler';
import { AnalysisConfig } from '@/services/openaiService';
import { promptService } from '@/services/promptService';

const logger = createModuleLogger('ConfigHandler');

export class ConfigHandler {
  /**
   * Get available prompts
   */
  static async getPrompts(req: Request, res: Response): Promise<void> {
    logger.debug('Getting available prompts');

    try {
      const prompts = promptService.listPrompts();

      res.json({
        success: true,
        data: {
          prompts,
          total: prompts.length,
          categories: [...new Set(prompts.map(p => p.category))],
          versions: prompts.reduce((acc, p) => {
            acc[p.id] = p.versions || 1;
            return acc;
          }, {} as Record<string, number>)
        }
      });

    } catch (error) {
      logger.error('Failed to get prompts', error);
      throw error;
    }
  }

  /**
   * Get specific prompt details
   */
  static async getPromptDetails(req: Request, res: Response): Promise<void> {
    const promptId = req.params.id;
    if (!promptId) {
      throw new ValidationError('Prompt ID is required');
    }

    const version = req.query.version ? parseInt(req.query.version as string) : undefined;

    logger.debug('Getting prompt details', { promptId, version });

    try {
      const promptText = promptService.getPrompt(promptId, version);
      const performance = promptService.getPromptPerformance(promptId, version);

      res.json({
        success: true,
        data: {
          promptId,
          version,
          text: promptText,
          performance,
          metadata: {
            length: promptText.length,
            wordCount: promptText.split(/\s+/).length,
            hasVariables: promptText.includes('{{'),
            category: 'analysis' // Default category
          }
        }
      });

    } catch (error) {
      logger.error('Failed to get prompt details', { promptId, version, error });
      throw error;
    }
  }

  /**
   * Get default analysis configuration
   */
  static async getDefaultConfig(req: Request, res: Response): Promise<void> {
    logger.debug('Getting default analysis configuration');

    try {
      const defaultConfig: AnalysisConfig = {
        useGPT4o: true,
        useReasoning: true,
        focusOnMaterials: false,
        focusOnHardware: false,
        enableMultiView: true,
        enable3DReconstruction: false,
        spatialResolution: 'HIGH',
        includeHardwarePositioning: false,
        enableIntelligentMeasurement: false,
        enableEnhancedHardwareRecognition: false,
        enableMaterialRecognition: false,
        enableCostEstimation: false,
        materialRecognitionDepth: 'DETAILED',
        costEstimationRegion: 'US_NATIONAL',
        promptId: 'kitchen_analysis'
      };

      res.json({
        success: true,
        data: {
          config: defaultConfig,
          description: 'Default analysis configuration optimized for general kitchen analysis',
          options: {
            useGPT4o: {
              description: 'Use GPT-4o for primary analysis (recommended)',
              type: 'boolean',
              default: true
            },
            useReasoning: {
              description: 'Use GPT-4o-mini for validation and reasoning',
              type: 'boolean',
              default: true
            },
            focusOnMaterials: {
              description: 'Enhanced focus on material identification',
              type: 'boolean',
              default: false
            },
            focusOnHardware: {
              description: 'Enhanced focus on hardware analysis',
              type: 'boolean',
              default: false
            },
            enableMultiView: {
              description: 'Enable multi-view correlation for PDFs',
              type: 'boolean',
              default: true
            },
            enable3DReconstruction: {
              description: 'Generate 3D cabinet reconstruction',
              type: 'boolean',
              default: false
            },
            spatialResolution: {
              description: 'Spatial analysis resolution',
              type: 'enum',
              options: ['LOW', 'MEDIUM', 'HIGH', 'ULTRA'],
              default: 'HIGH'
            },
            enableMaterialRecognition: {
              description: 'Enable advanced material recognition',
              type: 'boolean',
              default: false
            },
            materialRecognitionDepth: {
              description: 'Material recognition analysis depth',
              type: 'enum',
              options: ['BASIC', 'DETAILED', 'COMPREHENSIVE'],
              default: 'DETAILED'
            },
            enableCostEstimation: {
              description: 'Enable cost estimation analysis',
              type: 'boolean',
              default: false
            },
            costEstimationRegion: {
              description: 'Cost estimation region',
              type: 'enum',
              options: ['US_NATIONAL', 'US_WEST', 'US_EAST', 'US_SOUTH', 'US_MIDWEST'],
              default: 'US_NATIONAL'
            },
            promptId: {
              description: 'Prompt template to use for analysis',
              type: 'string',
              default: 'kitchen_analysis'
            }
          },
          presets: {
            basic: {
              name: 'Basic Analysis',
              description: 'Fast analysis with essential features',
              config: {
                ...defaultConfig,
                useGPT4o: false,
                useReasoning: false,
                enableMultiView: false
              }
            },
            standard: {
              name: 'Standard Analysis',
              description: 'Balanced analysis with core features',
              config: defaultConfig
            },
            comprehensive: {
              name: 'Comprehensive Analysis',
              description: 'Full analysis with all advanced features',
              config: {
                ...defaultConfig,
                focusOnMaterials: true,
                focusOnHardware: true,
                enable3DReconstruction: true,
                enableIntelligentMeasurement: true,
                enableEnhancedHardwareRecognition: true,
                enableMaterialRecognition: true,
                enableCostEstimation: true,
                spatialResolution: 'ULTRA',
                materialRecognitionDepth: 'COMPREHENSIVE'
              }
            }
          }
        }
      });

    } catch (error) {
      logger.error('Failed to get default config', error);
      throw error;
    }
  }

  /**
   * Validate analysis configuration
   */
  static async validateConfig(req: Request, res: Response): Promise<void> {
    const config = req.body.config;
    
    if (!config) {
      throw new ValidationError('Configuration is required');
    }

    logger.debug('Validating analysis configuration', { config });

    try {
      const validation = {
        isValid: true,
        errors: [] as string[],
        warnings: [] as string[],
        suggestions: [] as string[]
      };

      // Validate required fields
      if (typeof config.useGPT4o !== 'boolean') {
        validation.errors.push('useGPT4o must be a boolean');
        validation.isValid = false;
      }

      if (typeof config.useReasoning !== 'boolean') {
        validation.errors.push('useReasoning must be a boolean');
        validation.isValid = false;
      }

      // Validate enum fields
      if (config.spatialResolution && !['LOW', 'MEDIUM', 'HIGH', 'ULTRA'].includes(config.spatialResolution)) {
        validation.errors.push('spatialResolution must be one of: LOW, MEDIUM, HIGH, ULTRA');
        validation.isValid = false;
      }

      if (config.materialRecognitionDepth && !['BASIC', 'DETAILED', 'COMPREHENSIVE'].includes(config.materialRecognitionDepth)) {
        validation.errors.push('materialRecognitionDepth must be one of: BASIC, DETAILED, COMPREHENSIVE');
        validation.isValid = false;
      }

      // Performance warnings
      if (config.enable3DReconstruction && config.spatialResolution === 'ULTRA') {
        validation.warnings.push('ULTRA spatial resolution with 3D reconstruction may take significantly longer');
      }

      if (config.enableMaterialRecognition && config.materialRecognitionDepth === 'COMPREHENSIVE') {
        validation.warnings.push('Comprehensive material recognition increases processing time');
      }

      // Optimization suggestions
      if (!config.useGPT4o && config.enable3DReconstruction) {
        validation.suggestions.push('Consider enabling GPT-4o for better 3D reconstruction accuracy');
      }

      if (config.enableCostEstimation && !config.enableMaterialRecognition) {
        validation.suggestions.push('Enable material recognition for more accurate cost estimation');
      }

      res.json({
        success: true,
        data: {
          validation,
          optimizedConfig: validation.isValid ? config : null,
          estimatedProcessingTime: this.estimateProcessingTime(config)
        }
      });

    } catch (error) {
      logger.error('Failed to validate config', error);
      throw error;
    }
  }

  /**
   * Get configuration presets
   */
  static async getConfigPresets(req: Request, res: Response): Promise<void> {
    logger.debug('Getting configuration presets');

    try {
      const presets = {
        quick: {
          name: 'Quick Analysis',
          description: 'Fast analysis for basic insights',
          estimatedTime: '30-45 seconds',
          config: {
            useGPT4o: false,
            useReasoning: false,
            focusOnMaterials: false,
            focusOnHardware: false,
            enableMultiView: false,
            enable3DReconstruction: false,
            enableMaterialRecognition: false,
            spatialResolution: 'MEDIUM',
            promptId: 'kitchen_analysis_basic'
          }
        },
        standard: {
          name: 'Standard Analysis',
          description: 'Balanced analysis with core features',
          estimatedTime: '60-90 seconds',
          config: {
            useGPT4o: true,
            useReasoning: true,
            focusOnMaterials: false,
            focusOnHardware: false,
            enableMultiView: true,
            enable3DReconstruction: false,
            enableMaterialRecognition: false,
            spatialResolution: 'HIGH',
            promptId: 'kitchen_analysis'
          }
        },
        professional: {
          name: 'Professional Analysis',
          description: 'Comprehensive analysis for professional use',
          estimatedTime: '120-180 seconds',
          config: {
            useGPT4o: true,
            useReasoning: true,
            focusOnMaterials: true,
            focusOnHardware: true,
            enableMultiView: true,
            enable3DReconstruction: true,
            enableMaterialRecognition: true,
            enableCostEstimation: true,
            spatialResolution: 'HIGH',
            materialRecognitionDepth: 'DETAILED',
            promptId: 'kitchen_analysis_professional'
          }
        }
      };

      res.json({
        success: true,
        data: {
          presets,
          total: Object.keys(presets).length,
          categories: ['quick', 'standard', 'professional']
        }
      });

    } catch (error) {
      logger.error('Failed to get config presets', error);
      throw error;
    }
  }

  /**
   * Estimate processing time for configuration
   */
  private static estimateProcessingTime(config: AnalysisConfig): number {
    let baseTime = 30; // Base processing time in seconds

    if (config.useGPT4o) baseTime += 20;
    if (config.useReasoning) baseTime += 15;
    if (config.enable3DReconstruction) baseTime += 30;
    if (config.enableMaterialRecognition) baseTime += 25;
    if (config.enableCostEstimation) baseTime += 10;
    if (config.spatialResolution === 'ULTRA') baseTime += 20;
    if (config.materialRecognitionDepth === 'COMPREHENSIVE') baseTime += 15;

    return baseTime;
  }
}
