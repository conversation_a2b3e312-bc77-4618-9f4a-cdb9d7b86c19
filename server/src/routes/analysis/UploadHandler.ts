/**
 * Upload Handler - Specialized route handler for file uploads and analysis initiation
 * 
 * Part of Phase 3.3 Analysis Routes Refactoring
 * Extracted from analysis.ts (545 lines → modular architecture)
 * 
 * Responsibilities:
 * - File upload handling and validation
 * - Analysis configuration parsing
 * - Queue job creation and management
 * - Enhanced analysis workflow initiation
 */

import express, { Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { createModuleLogger } from '@/utils/logger';
import { asyncHandler, ValidationError } from '@/middleware/errorHandler';
import { AnalysisQueue } from '@/services/analysisQueue';
import { SocketManager } from '@/services/socketManager';
import { AnalysisConfig } from '@/services/openaiService';
import { promptService } from '@/services/promptService';
import { promptOptimizationService } from '@/services/promptOptimizationService';
import { abTestManager } from '@/services/abTestManager';
import { reasoningManager } from '@/services/reasoningManager';
import { EnhancedPdfProcessor } from '@/services/enhancedPdfProcessor';
import { cabinetReconstructionService } from '@/services/cabinetReconstructionService';

const logger = createModuleLogger('UploadHandler');
const enhancedPdfProcessor = new EnhancedPdfProcessor();

// Configure multer for file uploads
const uploadDir = process.env.UPLOAD_DIR || './uploads';
const maxFileSize = parseInt(process.env.MAX_FILE_SIZE || '52428800'); // 50MB

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = `${Date.now()}_${uuidv4()}`;
    const ext = path.extname(file.originalname);
    cb(null, `upload_${uniqueSuffix}${ext}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: maxFileSize,
    files: 1
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new ValidationError(`Unsupported file type: ${file.mimetype}`));
    }
  }
});

export class UploadHandler {
  /**
   * Enhanced analysis with all advanced AI services
   */
  static enhancedAnalysis = upload.single('file');
  
  static async processEnhancedAnalysis(req: Request, res: Response): Promise<void> {
    if (!req.file) {
      throw new ValidationError('No file uploaded');
    }

    const analysisId = uuidv4();

    logger.info('Starting enhanced analysis', {
      analysisId,
      filename: req.file.originalname,
      size: req.file.size
    });

    try {
      // Step 1: Optimize prompt using prompt optimization service
      const basePrompt = promptService.getAnalysisPrompt({
        useGPT4o: true,
        useReasoning: true,
        focusOnMaterials: true,
        focusOnHardware: true,
        enableMultiView: true,
        promptId: 'kitchen_analysis'
      });

      const optimizedPrompt = await promptOptimizationService.optimizePrompt(
        basePrompt,
        'kitchen_analysis',
        { analysisId, filename: req.file.originalname }
      );

      // Step 2: Start reasoning chain
      const reasoningChain = await reasoningManager.startReasoningChain(
        analysisId,
        {
          analysisType: 'kitchen_analysis',
          objectives: ['material_identification', 'hardware_analysis', 'measurement_extraction']
        }
      );

      // Step 3: Process PDF with enhanced processor
      const tempDir = path.join(path.dirname(req.file.path), 'enhanced_processing');
      const pdfResults = await enhancedPdfProcessor.processPdf(req.file.path, tempDir, {
        outputFormat: 'png',
        quality: 95,
        density: 300,
        optimizeForAnalysis: true,
        generateThumbnails: true
      });

      // Step 4: Create A/B test variant for analysis approach
      const testVariant = abTestManager.getPromptVariant('enhanced_analysis_approach', analysisId);

      // Step 5: Add to analysis queue with enhanced configuration
      const analysisQueue: AnalysisQueue = req.app.locals.analysisQueue;
      const jobId = await analysisQueue.addJob(req.file.path, req.file.originalname, {
        useGPT4o: true,
        useReasoning: true,
        focusOnMaterials: true,
        focusOnHardware: true,
        enableMultiView: true,
        promptId: 'kitchen_analysis',
        enhancedMode: true,
        optimizedPrompt,
        reasoningChainId: reasoningChain.id,
        pdfProcessingResults: pdfResults,
        abTestVariant: testVariant
      });

      res.json({
        success: true,
        data: {
          analysisId: jobId,
          status: 'PROCESSING',
          message: 'Enhanced analysis started with all advanced AI services',
          filePath: req.file.path,
          filename: req.file.originalname,
          fileSize: req.file.size,
          services: {
            promptOptimization: true,
            reasoningChain: true,
            enhancedPdfProcessing: true,
            abTesting: true
          },
          reasoningChainId: reasoningChain.id,
          abTestVariant: testVariant,
          estimatedDuration: 120 // seconds for enhanced analysis
        }
      });

    } catch (error) {
      // Clean up uploaded file on error
      fs.unlink(req.file.path, (unlinkError) => {
        if (unlinkError) {
          logger.warn('Failed to cleanup uploaded file:', unlinkError);
        }
      });

      throw error;
    }
  }

  /**
   * Standard file upload and analysis
   */
  static standardUpload = upload.single('file');
  
  static async processStandardUpload(req: Request, res: Response): Promise<void> {
    if (!req.file) {
      throw new ValidationError('No file uploaded');
    }

    // Parse analysis configuration
    const config: AnalysisConfig = {
      useGPT4o: req.body.useGPT4o === 'true' || req.body.useGPT4o === true,
      useReasoning: req.body.useReasoning === 'true' || req.body.useReasoning === true,
      focusOnMaterials: req.body.focusOnMaterials === 'true' || req.body.focusOnMaterials === true,
      focusOnHardware: req.body.focusOnHardware === 'true' || req.body.focusOnHardware === true,
      enableMultiView: req.body.enableMultiView === 'true' || req.body.enableMultiView === true,
      enable3DReconstruction: req.body.enable3DReconstruction === 'true' || req.body.enable3DReconstruction === true,
      spatialResolution: req.body.spatialResolution || 'HIGH',
      includeHardwarePositioning: req.body.includeHardwarePositioning === 'true' || req.body.includeHardwarePositioning === true,
      enableIntelligentMeasurement: req.body.enableIntelligentMeasurement === 'true' || req.body.enableIntelligentMeasurement === true,
      enableEnhancedHardwareRecognition: req.body.enableEnhancedHardwareRecognition === 'true' || req.body.enableEnhancedHardwareRecognition === true,
      enableMaterialRecognition: req.body.enableMaterialRecognition === 'true' || req.body.enableMaterialRecognition === true,
      enableCostEstimation: req.body.enableCostEstimation === 'true' || req.body.enableCostEstimation === true,
      materialRecognitionDepth: req.body.materialRecognitionDepth || 'DETAILED',
      costEstimationRegion: req.body.costEstimationRegion || 'US_NATIONAL',
      promptId: req.body.promptId || 'kitchen_analysis',
      promptVersion: req.body.promptVersion ? req.body.promptVersion.toString() : undefined
    };

    logger.info('File uploaded for analysis', {
      filename: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype,
      config
    });

    try {
      // Add job to analysis queue
      const analysisQueue: AnalysisQueue = req.app.locals.analysisQueue;
      const jobId = await analysisQueue.addJob(req.file.path, req.file.originalname, config);

      res.json({
        success: true,
        data: {
          analysisId: jobId,
          status: 'QUEUED',
          message: 'File uploaded successfully and queued for analysis',
          estimatedDuration: 60, // seconds
          filePath: req.file.path,
          filename: req.file.originalname,
          fileSize: req.file.size,
          config
        }
      });

    } catch (error) {
      // Clean up uploaded file on error
      fs.unlink(req.file.path, (unlinkError) => {
        if (unlinkError) {
          logger.warn('Failed to cleanup uploaded file:', unlinkError);
        }
      });
      
      throw error;
    }
  }

  /**
   * Get upload configuration and limits
   */
  static async getUploadConfig(req: Request, res: Response): Promise<void> {
    res.json({
      success: true,
      data: {
        maxFileSize,
        uploadDir: uploadDir.replace(process.cwd(), '.'),
        allowedTypes: [
          'application/pdf',
          'image/jpeg',
          'image/jpg', 
          'image/png',
          'image/gif',
          'image/webp'
        ],
        limits: {
          fileSize: maxFileSize,
          files: 1
        }
      }
    });
  }
}
