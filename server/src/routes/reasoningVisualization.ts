import express from 'express';
import { createModuleLogger } from '@/utils/logger';
import { reasoningManager } from '@/services/reasoningManager';

const router = express.Router();
const logger = createModuleLogger('ReasoningVisualizationAPI');

/**
 * GET /api/reasoning/chains/:chainId
 * Get reasoning chain data for visualization
 */
router.get('/chains/:chainId', async (req, res) => {
  try {
    const { chainId } = req.params;
    
    const chainData = reasoningManager.getReasoningChainForVisualization(chainId);
    
    if (!chainData) {
      return res.status(404).json({
        success: false,
        error: 'Reasoning chain not found',
        chainId
      });
    }

    logger.info(`Reasoning chain data retrieved: ${chainId}`, {
      totalSteps: chainData.steps.length,
      status: chainData.status,
      currentStep: chainData.currentStep
    });

    res.json({
      success: true,
      data: chainData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Failed to retrieve reasoning chain:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve reasoning chain data',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/reasoning/chains/analysis/:analysisId
 * Get all reasoning chains for a specific analysis
 */
router.get('/chains/analysis/:analysisId', async (req, res) => {
  try {
    const { analysisId } = req.params;
    
    const activeChains = reasoningManager.getActiveChains();
    const analysisChains = activeChains.filter(chain => chain.analysisId === analysisId);
    
    const chainsData = analysisChains.map(chain => 
      reasoningManager.getReasoningChainForVisualization(chain.id)
    ).filter(Boolean);

    logger.info(`Retrieved reasoning chains for analysis: ${analysisId}`, {
      chainCount: chainsData.length
    });

    res.json({
      success: true,
      data: {
        analysisId,
        chains: chainsData,
        totalChains: chainsData.length
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Failed to retrieve analysis reasoning chains:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve analysis reasoning chains',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * POST /api/reasoning/chains/:chainId/initialize-visualization
 * Initialize visualization data for a reasoning chain
 */
router.post('/chains/:chainId/initialize-visualization', async (req, res) => {
  try {
    const { chainId } = req.params;
    
    reasoningManager.initializeVisualizationData(chainId);
    
    const chainData = reasoningManager.getReasoningChainForVisualization(chainId);
    
    if (!chainData) {
      return res.status(404).json({
        success: false,
        error: 'Reasoning chain not found',
        chainId
      });
    }

    logger.info(`Visualization initialized for chain: ${chainId}`, {
      totalSteps: chainData.steps.length
    });

    res.json({
      success: true,
      data: {
        chainId,
        initialized: true,
        totalSteps: chainData.steps.length,
        structure: chainData
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Failed to initialize visualization:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to initialize reasoning visualization',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/reasoning/steps/:stepId/details
 * Get detailed information about a specific reasoning step
 */
router.get('/steps/:stepId/details', async (req, res) => {
  try {
    const { stepId } = req.params;
    const { chainId } = req.query;
    
    if (!chainId) {
      return res.status(400).json({
        success: false,
        error: 'Chain ID is required'
      });
    }

    const chainData = reasoningManager.getReasoningChainForVisualization(chainId as string);
    
    if (!chainData) {
      return res.status(404).json({
        success: false,
        error: 'Reasoning chain not found',
        chainId
      });
    }

    const step = chainData.steps.find((s: any) => s.id === stepId);
    
    if (!step) {
      return res.status(404).json({
        success: false,
        error: 'Reasoning step not found',
        stepId
      });
    }

    logger.debug(`Step details retrieved: ${stepId}`, {
      chainId,
      stepType: step.type,
      hasGPTO1Reasoning: !!step.gptO1Reasoning
    });

    res.json({
      success: true,
      data: {
        step,
        chainContext: {
          chainId: chainData.id,
          analysisId: chainData.analysisId,
          goal: chainData.goal
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Failed to retrieve step details:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve reasoning step details',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/reasoning/stats
 * Get reasoning visualization statistics
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = reasoningManager.getReasoningStats();
    const activeChains = reasoningManager.getActiveChains();
    
    // Calculate GPT-o1 specific statistics
    const gptO1Stats = {
      chainsWithGPTO1: activeChains.filter(chain => 
        chain.steps?.some((step: any) => step.gptO1Reasoning)
      ).length,
      totalGPTO1Steps: activeChains.reduce((total, chain) => 
        total + (chain.steps?.filter((step: any) => step.gptO1Reasoning).length || 0), 0
      ),
      averageComplexityScore: 0.7, // Mock data - would calculate from actual steps
      visualizationEnabled: activeChains.filter(chain =>
        chain.steps?.some((step: any) => step.visualizationData)
      ).length
    };

    logger.info('Reasoning visualization stats requested', {
      activeChains: stats.activeChains,
      gptO1Chains: gptO1Stats.chainsWithGPTO1
    });

    res.json({
      success: true,
      data: {
        ...stats,
        gptO1Stats,
        visualization: {
          enabledChains: gptO1Stats.visualizationEnabled,
          totalActiveChains: stats.activeChains
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Failed to retrieve reasoning stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve reasoning statistics',
      timestamp: new Date().toISOString()
    });
  }
});

export default router;
