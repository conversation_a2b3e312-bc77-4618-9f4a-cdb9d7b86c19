import express from 'express';
import { QuotationService, QuoteResult } from '../services/quotationService';
import { PricingDatabaseService } from '../services/pricingDatabaseService';
import { QuoteReportingService } from '../services/quoteReportingService';
import { DatabaseService } from '../services/databaseService';
import { authMiddleware } from '../middleware/authMiddleware';
import { Request, Response, NextFunction } from 'express';

// Simple async handler wrapper
const asyncHandler = (fn: (req: Request, res: Response, next: NextFunction) => Promise<any>) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
import { createModuleLogger } from '../utils/logger';
import { socketManager } from '../services/socketManager';
import Joi from 'joi';

const router = express.Router();
const logger = createModuleLogger('QuotationRoutes');

// Validation schemas
const generateQuoteSchema = Joi.object({
  analysisId: Joi.string().required(),
  projectId: Joi.string().optional(),
  regionCode: Joi.string().optional(),
  analysisData: Joi.object().required()
});

const updateQuoteSchema = Joi.object({
  tier: Joi.string().valid('basic', 'premium', 'luxury').optional(),
  customizations: Joi.object().optional(),
  notes: Joi.string().optional()
});

/**
 * POST /api/quotation/generate
 * Generate a comprehensive quote from analysis results
 */
router.post('/generate', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { error, value } = generateQuoteSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: 'Invalid request data',
      details: error.details
    });
  }

  const { analysisId, projectId, regionCode, analysisData } = value;
  const userId = req.user?.id;

  logger.info(`Generating quote for analysis: ${analysisId}`, {
    userId,
    projectId,
    regionCode
  });

  try {
    // Check if pricing database is available
    const pricingDb = PricingDatabaseService.getInstance();
    if (!(await pricingDb.isAvailable())) {
      return res.status(503).json({
        success: false,
        error: 'Quotation service temporarily unavailable',
        message: 'Pricing database is not accessible. Please try again later.'
      });
    }

    // Convert analysis data to quotation input format
    const quotationInput = QuotationService.convertAnalysisToInput(analysisData);

    // Generate quote
    const quotationService = QuotationService.getInstance();
    const quote = await quotationService.generateQuote(
      quotationInput,
      analysisId,
      projectId,
      regionCode
    );

    // Store quote in database
    const db = DatabaseService.getInstance();
    await db.createAnalysisResult({
      id: quote.id,
      projectId,
      filename: `quote_${quote.id}.json`,
      filePath: `/quotes/${quote.id}`,
      fileSize: JSON.stringify(quote).length,
      analysisData: quote,
      createdBy: userId
    });

    // Emit real-time update
    if (projectId) {
      socketManager.emitToProject(projectId, 'quote:generated', {
        quoteId: quote.id,
        analysisId,
        summary: quote.summary,
        tiers: quote.tiers.map(tier => ({
          tier: tier.tier,
          name: tier.name,
          total: pricingDb.formatNZD(tier.total),
          confidence: tier.confidence
        }))
      });
    }

    // Format response with NZD currency
    const formattedQuote = {
      ...quote,
      tiers: quote.tiers.map(tier => ({
        ...tier,
        materials: {
          ...tier.materials,
          cost: pricingDb.formatNZD(tier.materials.cost),
          items: tier.materials.items.map(item => ({
            ...item,
            unitPrice: pricingDb.formatNZD(item.unitPrice),
            totalPrice: pricingDb.formatNZD(item.totalPrice)
          }))
        },
        hardware: {
          ...tier.hardware,
          cost: pricingDb.formatNZD(tier.hardware.cost),
          items: tier.hardware.items.map(item => ({
            ...item,
            unitPrice: pricingDb.formatNZD(item.unitPrice),
            totalPrice: pricingDb.formatNZD(item.totalPrice)
          }))
        },
        labor: {
          ...tier.labor,
          cost: pricingDb.formatNZD(tier.labor.cost),
          items: tier.labor.items.map(item => ({
            ...item,
            hourlyRate: pricingDb.formatNZD(item.hourlyRate),
            totalPrice: pricingDb.formatNZD(item.totalPrice)
          }))
        },
        subtotal: pricingDb.formatNZD(tier.subtotal),
        taxes: pricingDb.formatNZD(tier.taxes),
        total: pricingDb.formatNZD(tier.total)
      })),
      alternatives: quote.alternatives.map(alt => ({
        ...alt,
        costDifference: pricingDb.formatNZD(alt.costDifference)
      }))
    };

    res.json({
      success: true,
      data: formattedQuote
    });

  } catch (error) {
    logger.error(`Quote generation failed for analysis: ${analysisId}`, error);
    res.status(500).json({
      success: false,
      error: 'Quote generation failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
}));

/**
 * GET /api/quotation/:quoteId
 * Retrieve a specific quote by ID
 */
router.get('/:quoteId', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { quoteId } = req.params;
  const userId = req.user?.id;

  logger.info(`Retrieving quote: ${quoteId}`, { userId });

  try {
    const db = DatabaseService.getInstance();
    const result = await db.getAnalysisById(quoteId);

    if (!result) {
      return res.status(404).json({
        success: false,
        error: 'Quote not found'
      });
    }

    // Check if user has access to this quote
    if (result.created_by !== userId && result.project_id) {
      // Check project permissions
      const project = await db.getProjectById(result.project_id);
      if (!project || project.owner_id !== userId) {
        return res.status(403).json({
          success: false,
          error: 'Access denied'
        });
      }
    }

    const quote = JSON.parse(result.analysis_data) as QuoteResult;
    
    // Format with NZD currency
    const pricingDb = PricingDatabaseService.getInstance();
    const formattedQuote = {
      ...quote,
      tiers: quote.tiers.map(tier => ({
        ...tier,
        materials: {
          ...tier.materials,
          cost: pricingDb.formatNZD(tier.materials.cost),
          items: tier.materials.items.map(item => ({
            ...item,
            unitPrice: pricingDb.formatNZD(item.unitPrice),
            totalPrice: pricingDb.formatNZD(item.totalPrice)
          }))
        },
        hardware: {
          ...tier.hardware,
          cost: pricingDb.formatNZD(tier.hardware.cost),
          items: tier.hardware.items.map(item => ({
            ...item,
            unitPrice: pricingDb.formatNZD(item.unitPrice),
            totalPrice: pricingDb.formatNZD(item.totalPrice)
          }))
        },
        labor: {
          ...tier.labor,
          cost: pricingDb.formatNZD(tier.labor.cost),
          items: tier.labor.items.map(item => ({
            ...item,
            hourlyRate: pricingDb.formatNZD(item.hourlyRate),
            totalPrice: pricingDb.formatNZD(item.totalPrice)
          }))
        },
        subtotal: pricingDb.formatNZD(tier.subtotal),
        taxes: pricingDb.formatNZD(tier.taxes),
        total: pricingDb.formatNZD(tier.total)
      })),
      alternatives: quote.alternatives.map(alt => ({
        ...alt,
        costDifference: pricingDb.formatNZD(alt.costDifference)
      }))
    };

    res.json({
      success: true,
      data: formattedQuote
    });

  } catch (error) {
    logger.error(`Failed to retrieve quote: ${quoteId}`, error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve quote',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
}));

/**
 * GET /api/quotation/project/:projectId
 * Get all quotes for a specific project
 */
router.get('/project/:projectId', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const userId = req.user?.id;

  logger.info(`Retrieving quotes for project: ${projectId}`, { userId });

  try {
    const db = DatabaseService.getInstance();
    
    // Check project access
    const project = await db.getProjectById(projectId);
    if (!project || project.owner_id !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Get all analysis results for the project that contain quotes
    const results = await db.getDatabase().prepare(`
      SELECT * FROM analysis_results 
      WHERE project_id = ? AND file_path LIKE '/quotes/%'
      ORDER BY created_at DESC
    `).all(projectId);

    const quotes = results.map(result => {
      const quote = JSON.parse(result.analysis_data) as QuoteResult;
      return {
        id: quote.id,
        analysisId: quote.analysisId,
        summary: quote.summary,
        confidence: quote.confidence,
        createdAt: quote.createdAt,
        validUntil: quote.validUntil,
        tierSummary: quote.tiers.map(tier => ({
          tier: tier.tier,
          name: tier.name,
          total: PricingDatabaseService.getInstance().formatNZD(tier.total),
          confidence: tier.confidence
        }))
      };
    });

    res.json({
      success: true,
      data: quotes
    });

  } catch (error) {
    logger.error(`Failed to retrieve quotes for project: ${projectId}`, error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve quotes',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
}));

/**
 * PUT /api/quotation/:quoteId
 * Update quote with customizations or notes
 */
router.put('/:quoteId', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { quoteId } = req.params;
  const { error, value } = updateQuoteSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: 'Invalid request data',
      details: error.details
    });
  }

  const userId = req.user?.id;
  const { tier, customizations, notes } = value;

  logger.info(`Updating quote: ${quoteId}`, { userId, tier, hasCustomizations: !!customizations });

  try {
    const db = DatabaseService.getInstance();
    const result = await db.getAnalysisById(quoteId);

    if (!result) {
      return res.status(404).json({
        success: false,
        error: 'Quote not found'
      });
    }

    // Check permissions
    if (result.created_by !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    const quote = JSON.parse(result.analysis_data) as QuoteResult;
    
    // Update quote with customizations
    const updatedQuote = {
      ...quote,
      customizations,
      notes,
      updatedAt: new Date()
    };

    // Save updated quote
    await db.updateAnalysisResult(quoteId, {
      analysisData: updatedQuote
    });

    // Emit real-time update
    if (quote.projectId) {
      socketManager.emitToProject(quote.projectId, 'quote:updated', {
        quoteId,
        customizations,
        notes
      });
    }

    res.json({
      success: true,
      message: 'Quote updated successfully'
    });

  } catch (error) {
    logger.error(`Failed to update quote: ${quoteId}`, error);
    res.status(500).json({
      success: false,
      error: 'Failed to update quote',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
}));

/**
 * GET /api/quotation/:quoteId/pdf
 * Generate and download PDF quote
 */
router.get('/:quoteId/pdf', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { quoteId } = req.params;
  const { template = 'detailed', includeBranding = 'true', includeAlternatives = 'true' } = req.query;
  const userId = req.user?.id;

  logger.info(`Generating PDF for quote: ${quoteId}`, { userId, template });

  try {
    const db = DatabaseService.getInstance();
    const result = await db.getAnalysisById(quoteId);

    if (!result) {
      return res.status(404).json({
        success: false,
        error: 'Quote not found'
      });
    }

    // Check permissions
    if (result.created_by !== userId && result.project_id) {
      const project = await db.getProjectById(result.project_id);
      if (!project || project.owner_id !== userId) {
        return res.status(403).json({
          success: false,
          error: 'Access denied'
        });
      }
    }

    const quote = JSON.parse(result.analysis_data) as QuoteResult;

    // Generate PDF
    const reportingService = QuoteReportingService.getInstance();
    const { buffer, filename } = await reportingService.generateQuotePDF(quote, {
      template: template as 'basic' | 'detailed' | 'professional',
      includeBranding: includeBranding === 'true',
      includeAlternatives: includeAlternatives === 'true',
      includeTermsAndConditions: true,
      companyInfo: {
        name: 'Cabinet Insight Pro',
        address: 'New Zealand',
        phone: '+64 XXX XXX XXX',
        email: '<EMAIL>',
        website: 'www.cabinetinsightpro.com'
      }
    });

    // Set response headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', buffer.length);

    // Emit real-time update
    if (quote.projectId) {
      socketManager.emitToProject(quote.projectId, 'quote:pdf_generated', {
        quoteId,
        filename,
        template
      });
    }

    res.send(buffer);

  } catch (error) {
    logger.error(`PDF generation failed for quote: ${quoteId}`, error);
    res.status(500).json({
      success: false,
      error: 'PDF generation failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
}));

/**
 * GET /api/quotation/pricing/materials
 * Get available materials for pricing
 */
router.get('/pricing/materials', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { category, search, limit = '20' } = req.query;

  try {
    const pricingDb = PricingDatabaseService.getInstance();

    if (!(await pricingDb.isAvailable())) {
      return res.status(503).json({
        success: false,
        error: 'Pricing database unavailable'
      });
    }

    let materials;
    if (search) {
      materials = await pricingDb.searchMaterials(search as string, parseInt(limit as string));
    } else if (category) {
      materials = await pricingDb.getMaterialsByCategory(category as string);
    } else {
      return res.status(400).json({
        success: false,
        error: 'Either category or search parameter is required'
      });
    }

    // Format prices for display
    const formattedMaterials = materials.map(material => ({
      ...material,
      base_price: pricingDb.formatNZD(material.base_price),
      min_price: material.min_price ? pricingDb.formatNZD(material.min_price) : null,
      max_price: material.max_price ? pricingDb.formatNZD(material.max_price) : null
    }));

    res.json({
      success: true,
      data: formattedMaterials
    });

  } catch (error) {
    logger.error('Failed to retrieve materials pricing', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve materials pricing',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
}));

/**
 * GET /api/quotation/pricing/hardware
 * Get available hardware for pricing
 */
router.get('/pricing/hardware', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { brand, search, limit = '20' } = req.query;

  try {
    const pricingDb = PricingDatabaseService.getInstance();

    if (!(await pricingDb.isAvailable())) {
      return res.status(503).json({
        success: false,
        error: 'Pricing database unavailable'
      });
    }

    let hardware;
    if (search) {
      hardware = await pricingDb.searchHardware(search as string, parseInt(limit as string));
    } else if (brand) {
      hardware = await pricingDb.getHardwareByBrand(brand as string);
    } else {
      return res.status(400).json({
        success: false,
        error: 'Either brand or search parameter is required'
      });
    }

    // Format prices for display
    const formattedHardware = hardware.map(item => ({
      ...item,
      unit_price: pricingDb.formatNZD(item.unit_price)
    }));

    res.json({
      success: true,
      data: formattedHardware
    });

  } catch (error) {
    logger.error('Failed to retrieve hardware pricing', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve hardware pricing',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
}));

/**
 * GET /api/quotation/:quoteId/comparison
 * Generate comparison data for quote tiers
 */
router.get('/:quoteId/comparison', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { quoteId } = req.params;
  const userId = req.user?.id;

  try {
    const db = DatabaseService.getInstance();

    // Get the quote data
    const quote = await db.getAnalysisResult(quoteId);
    if (!quote) {
      return res.status(404).json({
        success: false,
        error: 'Quote not found',
        message: `Quote with ID ${quoteId} does not exist`
      });
    }

    // Parse quote data
    const quoteData = JSON.parse(quote.analysis_data);

    // Generate comparison features
    const features = [
      {
        feature: 'Cabinet Material',
        basic: 'MDF with laminate finish',
        premium: 'Plywood with wood veneer',
        luxury: 'Solid hardwood construction',
        category: 'materials',
        importance: 'high',
        description: 'Core cabinet box construction material'
      },
      {
        feature: 'Door Style',
        basic: 'Flat panel (Shaker style)',
        premium: 'Raised panel with decorative edge',
        luxury: 'Custom millwork with hand-carved details',
        category: 'materials',
        importance: 'medium',
        description: 'Cabinet door design and construction'
      },
      {
        feature: 'Hinges',
        basic: 'Standard European hinges',
        premium: 'Soft-close European hinges',
        luxury: 'Premium soft-close with lifetime warranty',
        category: 'hardware',
        importance: 'high',
        description: 'Cabinet door hinge quality and features'
      },
      {
        feature: 'Installation',
        basic: 'Standard installation',
        premium: 'Professional installation with cleanup',
        luxury: 'White-glove installation with project manager',
        category: 'installation',
        importance: 'high',
        description: 'Installation service level and support'
      },
      {
        feature: 'Warranty Period',
        basic: '1 year limited warranty',
        premium: '3 year comprehensive warranty',
        luxury: '5 year comprehensive warranty',
        category: 'warranty',
        importance: 'high',
        description: 'Warranty coverage period and scope'
      }
    ];

    // Generate value propositions
    const valuePropositions = [
      {
        tier: 'basic',
        title: 'Budget-Friendly Choice',
        description: 'Quality cabinets at an affordable price point with essential features and reliable construction.',
        best_value: false,
        recommended: false
      },
      {
        tier: 'premium',
        title: 'Best Value Package',
        description: 'Perfect balance of quality, features, and price. Includes premium hardware and enhanced warranty.',
        best_value: true,
        recommended: true
      },
      {
        tier: 'luxury',
        title: 'Premium Experience',
        description: 'Top-tier materials, custom features, and white-glove service for the ultimate kitchen transformation.',
        best_value: false,
        recommended: false
      }
    ];

    // Calculate price range
    const basicTier = quoteData.tiers.find((t: any) => t.tier === 'basic');
    const luxuryTier = quoteData.tiers.find((t: any) => t.tier === 'luxury');

    const minPrice = basicTier ? parseFloat(basicTier.total.replace(/[^\d.-]/g, '')) : 0;
    const maxPrice = luxuryTier ? parseFloat(luxuryTier.total.replace(/[^\d.-]/g, '')) : 0;

    const comparisonData = {
      quote_id: quoteId,
      tiers: quoteData.tiers,
      features,
      value_propositions: valuePropositions,
      recommendations: {
        best_value: 'premium',
        most_popular: 'premium',
        premium_choice: 'luxury'
      },
      summary: {
        price_range: {
          min: minPrice,
          max: maxPrice,
          currency: 'NZD'
        },
        savings_potential: `Up to ${Math.round((maxPrice - minPrice) / maxPrice * 100)}% savings with basic tier`,
        upgrade_benefits: [
          'Enhanced durability and longevity',
          'Premium hardware and finishes',
          'Extended warranty coverage',
          'Professional design support',
          'Priority installation scheduling'
        ]
      }
    };

    logger.info(`Quote comparison generated: ${quoteId}`, { userId });

    res.json({
      success: true,
      data: comparisonData
    });

  } catch (error) {
    logger.error(`Failed to generate quote comparison: ${quoteId}`, error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate quote comparison',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
}));

/**
 * POST /api/quotation/:quoteId/comparison/export
 * Export quote comparison data
 */
router.post('/:quoteId/comparison/export', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { quoteId } = req.params;
  const { format = 'pdf', include_features = true, include_pricing = true, include_recommendations = true } = req.body;
  const userId = req.user?.id;

  try {
    const db = DatabaseService.getInstance();

    // Get the quote data
    const quote = await db.getAnalysisResult(quoteId);
    if (!quote) {
      return res.status(404).json({
        success: false,
        error: 'Quote not found'
      });
    }

    // For now, return a mock download URL
    // In a real implementation, this would generate the actual file
    const filename = `quote_comparison_${quoteId}.${format}`;
    const downloadUrl = `/api/quotation/${quoteId}/comparison/download/${filename}`;

    logger.info(`Quote comparison export requested: ${quoteId}`, { userId, format });

    res.json({
      success: true,
      data: {
        download_url: downloadUrl,
        filename: filename
      }
    });

  } catch (error) {
    logger.error(`Failed to export quote comparison: ${quoteId}`, error);
    res.status(500).json({
      success: false,
      error: 'Failed to export quote comparison',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
}));

export default router;
