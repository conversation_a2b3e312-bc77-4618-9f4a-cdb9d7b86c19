import express, { Request, Response } from 'express';
import { createModuleLogger } from '@/utils/logger';
import { asyncHandler } from '@/middleware/errorHandler';
import { promptOptimizationService } from '@/services/promptOptimizationService';
import { abTestManager } from '@/services/abTestManager';
import { reasoningManager } from '@/services/reasoningManager';
import { EnhancedPdfProcessor } from '@/services/enhancedPdfProcessor';

const logger = createModuleLogger('HealthRoutes');
const router = express.Router();

/**
 * GET /api/health
 * Basic health check
 */
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const uptime = process.uptime();
  const memory = process.memoryUsage();
  
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: Math.floor(uptime),
    memory: {
      used: Math.round(memory.heapUsed / 1024 / 1024),
      total: Math.round(memory.heapTotal / 1024 / 1024),
      external: Math.round(memory.external / 1024 / 1024),
      rss: Math.round(memory.rss / 1024 / 1024)
    },
    environment: process.env.NODE_ENV || 'development',
    version: process.env.npm_package_version || '1.0.0'
  };

  res.json({
    success: true,
    data: health
  });
}));

/**
 * GET /api/health/detailed
 * Detailed health check with service status
 */
router.get('/detailed', asyncHandler(async (req: Request, res: Response) => {
  const uptime = process.uptime();
  const memory = process.memoryUsage();
  
  // Check OpenAI API availability (Azure OpenAI or Standard OpenAI)
  const hasAzureConfig = process.env.AZURE_OPENAI_API_KEY && process.env.AZURE_OPENAI_ENDPOINT;
  const hasOpenAIConfig = process.env.OPENAI_API_KEY;
  const openaiStatus = hasAzureConfig || hasOpenAIConfig ? 'configured' : 'not_configured';
  const openaiType = hasAzureConfig ? 'azure' : hasOpenAIConfig ? 'standard' : 'none';
  
  // Check queue status
  const analysisQueue = req.app.locals.analysisQueue;
  const queueStats = analysisQueue ? analysisQueue.getQueueStats() : null;
  
  // Check socket connections
  const socketManager = req.app.locals.socketManager;
  const socketStats = socketManager ? {
    connectedClients: socketManager.getConnectedClientsCount(),
    activeAnalyses: socketManager.getActiveAnalyses().length
  } : null;

  const detailedHealth = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: Math.floor(uptime),
    memory: {
      used: Math.round(memory.heapUsed / 1024 / 1024),
      total: Math.round(memory.heapTotal / 1024 / 1024),
      external: Math.round(memory.external / 1024 / 1024),
      rss: Math.round(memory.rss / 1024 / 1024)
    },
    environment: process.env.NODE_ENV || 'development',
    version: process.env.npm_package_version || '1.0.0',
    services: {
      openai: {
        status: openaiStatus,
        configured: openaiStatus === 'configured',
        type: openaiType,
        endpoint: hasAzureConfig ? process.env.AZURE_OPENAI_ENDPOINT : undefined
      },
      queue: queueStats ? {
        status: 'active',
        stats: queueStats
      } : {
        status: 'not_available'
      },
      websocket: socketStats ? {
        status: 'active',
        stats: socketStats
      } : {
        status: 'not_available'
      }
    },
    configuration: {
      maxFileSize: process.env.MAX_FILE_SIZE || '52428800',
      maxConcurrentAnalyses: process.env.MAX_CONCURRENT_ANALYSES || '3',
      uploadDir: process.env.UPLOAD_DIR || './uploads',
      tempDir: process.env.TEMP_DIR || './temp'
    }
  };

  res.json({
    success: true,
    data: detailedHealth
  });
}));

/**
 * GET /api/health/ready
 * Readiness probe for container orchestration
 */
router.get('/ready', asyncHandler(async (req: Request, res: Response) => {
  // Check if essential services are ready
  const hasAzureConfig = !!(process.env.AZURE_OPENAI_API_KEY && process.env.AZURE_OPENAI_ENDPOINT);
  const hasOpenAIConfig = !!process.env.OPENAI_API_KEY;
  const openaiReady = hasAzureConfig || hasOpenAIConfig;

  const checks = {
    server: true,
    openai: openaiReady,
    queue: !!req.app.locals.analysisQueue,
    socket: !!req.app.locals.socketManager
  };

  const allReady = Object.values(checks).every(check => check);
  const status = allReady ? 'ready' : 'not_ready';

  res.status(allReady ? 200 : 503).json({
    success: allReady,
    data: {
      status,
      checks,
      timestamp: new Date().toISOString()
    }
  });
}));

/**
 * GET /api/health/live
 * Liveness probe for container orchestration
 */
router.get('/live', asyncHandler(async (req: Request, res: Response) => {
  // Simple liveness check - if we can respond, we're alive
  res.json({
    success: true,
    data: {
      status: 'alive',
      timestamp: new Date().toISOString()
    }
  });
}));

/**
 * GET /api/health/advanced-services
 * Health check for all advanced AI services
 */
router.get('/advanced-services', asyncHandler(async (req: Request, res: Response) => {
  try {
    const services = {
      promptOptimization: {
        status: 'healthy',
        heuristics: promptOptimizationService.getAvailableHeuristics().length,
        lastOptimization: new Date().toISOString()
      },
      abTesting: {
        status: 'healthy',
        activeTests: abTestManager.getActiveTests().length,
        totalTests: 0 // Would be tracked in real implementation
      },
      reasoning: {
        status: 'healthy',
        activeChains: reasoningManager.getActiveChains().length,
        templates: Object.keys(reasoningManager.getReasoningTemplates()).length
      },
      enhancedPdfProcessor: {
        status: 'healthy',
        features: ['OCR', 'dimension detection', 'kitchen analysis'],
        supportedFormats: ['pdf']
      }
    };

    res.json({
      success: true,
      data: {
        status: 'healthy',
        services,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Advanced services health check failed:', error);
    res.status(503).json({
      success: false,
      data: {
        status: 'unhealthy',
        error: 'One or more advanced services are not responding',
        timestamp: new Date().toISOString()
      }
    });
  }
}));



export { router as healthRoutes };
