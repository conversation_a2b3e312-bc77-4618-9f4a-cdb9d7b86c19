import express from 'express';
import { authMiddleware } from '@/middleware/authMiddleware';
import { AuthService } from '@/services/authService';
import { logger } from '@/utils/logger';
import { asyncHandler } from '@/middleware/asyncHandler';

const router = express.Router();
const authService = new AuthService();

/**
 * POST /api/auth/register
 * Register a new user
 */
router.post('/register', 
  authMiddleware.authRateLimit,
  authMiddleware.validateOrigin,
  asyncHandler(async (req, res) => {
    const { email, password, firstName, lastName, role } = req.body;

    // Validation
    if (!email || !password || !firstName || !lastName) {
      return res.status(400).json({
        success: false,
        error: 'Email, password, first name, and last name are required'
      });
    }

    if (password.length < 8) {
      return res.status(400).json({
        success: false,
        error: 'Password must be at least 8 characters long'
      });
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid email format'
      });
    }

    if (role && !['admin', 'designer', 'collaborator', 'viewer'].includes(role)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid role'
      });
    }

    try {
      const result = await authService.register({
        email: email.toLowerCase().trim(),
        password,
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        role
      });

      res.status(201).json({
        success: true,
        data: {
          user: result.user,
          tokens: result.tokens
        }
      });
    } catch (error: any) {
      if (error.message === 'User with this email already exists') {
        return res.status(409).json({
          success: false,
          error: error.message
        });
      }

      logger.error('Registration error', { error, email });
      res.status(500).json({
        success: false,
        error: 'Registration failed'
      });
    }
  })
);

/**
 * POST /api/auth/login
 * Login user
 */
router.post('/login',
  authMiddleware.authRateLimit,
  authMiddleware.validateOrigin,
  authMiddleware.logAuthEvent('login'),
  asyncHandler(async (req, res) => {
    const { email, password } = req.body;

    // Validation
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required'
      });
    }

    try {
      const result = await authService.login({
        email: email.toLowerCase().trim(),
        password
      });

      res.json({
        success: true,
        data: {
          user: result.user,
          tokens: result.tokens
        }
      });
    } catch (error: any) {
      if (error.message === 'Invalid email or password' || error.message === 'Account is deactivated') {
        return res.status(401).json({
          success: false,
          error: error.message
        });
      }

      logger.error('Login error', { error, email });
      res.status(500).json({
        success: false,
        error: 'Login failed'
      });
    }
  })
);

/**
 * POST /api/auth/refresh
 * Refresh access token
 */
router.post('/refresh', asyncHandler(async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    return res.status(400).json({
      success: false,
      error: 'Refresh token is required'
    });
  }

  try {
    const tokens = await authService.refreshToken(refreshToken);

    res.json({
      success: true,
      data: { tokens }
    });
  } catch (error: any) {
    res.status(401).json({
      success: false,
      error: 'Invalid refresh token'
    });
  }
}));

/**
 * GET /api/auth/me
 * Get current user profile
 */
router.get('/me', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  res.json({
    success: true,
    data: { user: req.user }
  });
}));

/**
 * PUT /api/auth/profile
 * Update user profile
 */
router.put('/profile', 
  authMiddleware.authenticate,
  authMiddleware.requireActiveAccount,
  asyncHandler(async (req, res) => {
    const { firstName, lastName, avatarUrl } = req.body;
    const userId = req.user!.id;

    // Validation
    if (firstName && firstName.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'First name cannot be empty'
      });
    }

    if (lastName && lastName.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Last name cannot be empty'
      });
    }

    try {
      const user = await authService.updateUserProfile(userId, {
        firstName: firstName?.trim(),
        lastName: lastName?.trim(),
        avatarUrl
      });

      res.json({
        success: true,
        data: { user }
      });
    } catch (error: any) {
      logger.error('Profile update error', { error, userId });
      res.status(500).json({
        success: false,
        error: 'Profile update failed'
      });
    }
  })
);

/**
 * PUT /api/auth/password
 * Change user password
 */
router.put('/password',
  authMiddleware.authenticate,
  authMiddleware.requireActiveAccount,
  authMiddleware.validateOrigin,
  asyncHandler(async (req, res) => {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user!.id;

    // Validation
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        error: 'Current password and new password are required'
      });
    }

    if (newPassword.length < 8) {
      return res.status(400).json({
        success: false,
        error: 'New password must be at least 8 characters long'
      });
    }

    try {
      await authService.changePassword(userId, currentPassword, newPassword);

      res.json({
        success: true,
        message: 'Password changed successfully'
      });
    } catch (error: any) {
      if (error.message === 'Current password is incorrect') {
        return res.status(400).json({
          success: false,
          error: error.message
        });
      }

      logger.error('Password change error', { error, userId });
      res.status(500).json({
        success: false,
        error: 'Password change failed'
      });
    }
  })
);

/**
 * POST /api/auth/logout
 * Logout user (invalidate tokens)
 */
router.post('/logout',
  authMiddleware.authenticate,
  authMiddleware.logAuthEvent('logout'),
  asyncHandler(async (req, res) => {
    // In a real implementation, you would invalidate the tokens
    // For now, just return success
    res.json({
      success: true,
      message: 'Logged out successfully'
    });
  })
);

/**
 * GET /api/auth/verify
 * Verify token validity
 */
router.get('/verify', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  res.json({
    success: true,
    data: {
      valid: true,
      user: req.user
    }
  });
}));

export default router;
