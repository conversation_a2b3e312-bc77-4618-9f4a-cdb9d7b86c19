import express from 'express';
import { Request, Response } from 'express';
import { authMiddleware } from '../middleware/authMiddleware';
import { asyncHandler } from '../middleware/asyncHandler';
import { QuoteTemplateService, CreateTemplateRequest, UpdateTemplateRequest } from '../services/quoteTemplateService';
import { createModuleLogger } from '../utils/logger';
import { socketManager } from '../services/socketManager';
import Joi from 'joi';

const router = express.Router();
const logger = createModuleLogger('QuoteTemplateRoutes');

// Validation schemas
const createTemplateSchema = Joi.object({
  template_code: Joi.string().min(3).max(50).required(),
  template_name: Joi.string().min(3).max(100).required(),
  description: Joi.string().max(500).optional(),
  parent_template_id: Joi.number().integer().positive().optional(),
  customer_segment_id: Joi.number().integer().positive().optional(),
  is_default: Joi.boolean().optional(),
  template_config: Joi.object().required(),
  sections_config: Joi.object().required(),
  styling_config: Joi.object().optional()
});

const updateTemplateSchema = Joi.object({
  template_name: Joi.string().min(3).max(100).optional(),
  description: Joi.string().max(500).optional(),
  customer_segment_id: Joi.number().integer().positive().optional(),
  is_default: Joi.boolean().optional(),
  is_active: Joi.boolean().optional(),
  template_config: Joi.object().optional(),
  sections_config: Joi.object().optional(),
  styling_config: Joi.object().optional()
});

/**
 * GET /api/quotation/templates
 * Get all quote templates
 */
router.get('/', authMiddleware.authenticate, asyncHandler(async (req: Request, res: Response) => {
  const { include_inactive = 'false', segment_id } = req.query;

  try {
    const templateService = QuoteTemplateService.getInstance();
    
    if (!(await templateService.isAvailable())) {
      return res.status(503).json({
        success: false,
        error: 'Template service temporarily unavailable',
        message: 'Quote template database is not accessible. Please try again later.'
      });
    }

    let templates = await templateService.getTemplates(include_inactive === 'true');

    // Filter by customer segment if specified
    if (segment_id) {
      const segmentIdNum = parseInt(segment_id as string);
      templates = templates.filter(t => t.customer_segment_id === segmentIdNum);
    }

    res.json({
      success: true,
      data: {
        templates,
        total: templates.length
      }
    });

  } catch (error) {
    logger.error('Failed to retrieve quote templates:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve quote templates',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
}));

/**
 * GET /api/quotation/templates/:id
 * Get a specific quote template by ID
 */
router.get('/:id', authMiddleware.authenticate, asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  try {
    const templateService = QuoteTemplateService.getInstance();
    
    if (!(await templateService.isAvailable())) {
      return res.status(503).json({
        success: false,
        error: 'Template service temporarily unavailable'
      });
    }

    const template = await templateService.getTemplateById(parseInt(id));
    
    if (!template) {
      return res.status(404).json({
        success: false,
        error: 'Template not found',
        message: `Quote template with ID ${id} does not exist`
      });
    }

    // Get usage statistics
    const usageStats = await templateService.getTemplateUsageStats(template.id);

    res.json({
      success: true,
      data: {
        template,
        usage_stats: usageStats
      }
    });

  } catch (error) {
    logger.error(`Failed to retrieve quote template: ${id}`, error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve quote template',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
}));

/**
 * POST /api/quotation/templates
 * Create a new quote template
 */
router.post('/', authMiddleware.authenticate, asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = createTemplateSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: 'Invalid template data',
      details: error.details.map(d => ({ field: d.path.join('.'), message: d.message }))
    });
  }

  const userId = req.user?.id;

  try {
    const templateService = QuoteTemplateService.getInstance();
    
    if (!(await templateService.isAvailable())) {
      return res.status(503).json({
        success: false,
        error: 'Template service temporarily unavailable'
      });
    }

    const templateData: CreateTemplateRequest = {
      ...value,
      created_by: userId
    };

    const template = await templateService.createTemplate(templateData);

    // Emit real-time update
    socketManager.emitToAll('template:created', {
      templateId: template.id,
      templateCode: template.template_code,
      templateName: template.template_name,
      customerSegment: template.customer_segment?.segment_name
    });

    logger.info(`Quote template created: ${template.template_code}`, { 
      templateId: template.id, 
      userId 
    });

    res.status(201).json({
      success: true,
      data: template,
      message: 'Quote template created successfully'
    });

  } catch (error) {
    logger.error('Failed to create quote template:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create quote template',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
}));

/**
 * PUT /api/quotation/templates/:id
 * Update an existing quote template
 */
router.put('/:id', authMiddleware.authenticate, asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { error, value } = updateTemplateSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: 'Invalid update data',
      details: error.details.map(d => ({ field: d.path.join('.'), message: d.message }))
    });
  }

  const userId = req.user?.id;

  try {
    const templateService = QuoteTemplateService.getInstance();
    
    if (!(await templateService.isAvailable())) {
      return res.status(503).json({
        success: false,
        error: 'Template service temporarily unavailable'
      });
    }

    const template = await templateService.updateTemplate(parseInt(id), value);

    // Emit real-time update
    socketManager.emitToAll('template:updated', {
      templateId: template.id,
      templateCode: template.template_code,
      templateName: template.template_name,
      isActive: template.is_active
    });

    logger.info(`Quote template updated: ${id}`, { userId });

    res.json({
      success: true,
      data: template,
      message: 'Quote template updated successfully'
    });

  } catch (error) {
    logger.error(`Failed to update quote template: ${id}`, error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: 'Template not found',
        message: error.message
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to update quote template',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
}));

/**
 * DELETE /api/quotation/templates/:id
 * Delete a quote template
 */
router.delete('/:id', authMiddleware.authenticate, asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user?.id;

  try {
    const templateService = QuoteTemplateService.getInstance();
    
    if (!(await templateService.isAvailable())) {
      return res.status(503).json({
        success: false,
        error: 'Template service temporarily unavailable'
      });
    }

    // Get template info before deletion for logging
    const template = await templateService.getTemplateById(parseInt(id));
    if (!template) {
      return res.status(404).json({
        success: false,
        error: 'Template not found',
        message: `Quote template with ID ${id} does not exist`
      });
    }

    await templateService.deleteTemplate(parseInt(id));

    // Emit real-time update
    socketManager.emitToAll('template:deleted', {
      templateId: parseInt(id),
      templateCode: template.template_code,
      templateName: template.template_name
    });

    logger.info(`Quote template deleted: ${id}`, { 
      templateCode: template.template_code,
      userId 
    });

    res.json({
      success: true,
      message: 'Quote template deleted successfully'
    });

  } catch (error) {
    logger.error(`Failed to delete quote template: ${id}`, error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: 'Template not found',
        message: error.message
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to delete quote template',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
}));

/**
 * GET /api/quotation/templates/segments
 * Get all customer segments
 */
router.get('/segments/list', authMiddleware.authenticate, asyncHandler(async (req: Request, res: Response) => {
  try {
    const templateService = QuoteTemplateService.getInstance();
    
    if (!(await templateService.isAvailable())) {
      return res.status(503).json({
        success: false,
        error: 'Template service temporarily unavailable'
      });
    }

    const segments = await templateService.getCustomerSegments();

    res.json({
      success: true,
      data: {
        segments,
        total: segments.length
      }
    });

  } catch (error) {
    logger.error('Failed to retrieve customer segments:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve customer segments',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
}));

export default router;
