import express from 'express';
import multer from 'multer';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { createModuleLogger } from '@/utils/logger';
import { documentIntelligenceService, enhancedAnalysisIntegration } from '@/services/documentIntelligence';
import { SocketManager } from '@/services/socketManager';
import { DocumentProcessingOptions } from '@/services/documentIntelligence/types';
import { AnalysisConfig } from '@/services/openaiService';

const logger = createModuleLogger('DocumentIntelligenceRoutes');
const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/document-intelligence/',
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/tiff',
      'image/bmp'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Unsupported file type. Please upload PDF or image files.'));
    }
  }
});

/**
 * POST /api/document-intelligence/analyze
 * Analyze document using Azure Document Intelligence
 */
router.post('/analyze', upload.single('document'), async (req, res) => {
  const analysisId = uuidv4();
  const socketManager: SocketManager = req.app.locals.socketManager;

  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No document file provided'
      });
    }

    logger.info('Starting document intelligence analysis', {
      analysisId,
      filename: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size
    });

    // Parse processing options from request
    const options: DocumentProcessingOptions = {
      modelType: req.body.modelType || 'prebuilt-layout',
      extractTables: req.body.extractTables === 'true',
      extractKeyValuePairs: req.body.extractKeyValuePairs === 'true',
      enableKitchenAnalysis: req.body.enableKitchenAnalysis === 'true',
      confidenceThreshold: parseFloat(req.body.confidenceThreshold) || 0.7,
      // Add file metadata for proper format detection
      originalFilename: req.file.originalname,
      mimetype: req.file.mimetype
    };

    // Send initial progress update (only if socketManager is available)
    if (socketManager) {
      socketManager.sendAnalysisProgress({
        analysisId,
        step: 'document_upload',
        progress: 10,
        message: 'Document uploaded, starting analysis...',
        timestamp: new Date().toISOString()
      });
    }

    // Check if service is available
    const isAvailable = await documentIntelligenceService.isAvailable();
    if (!isAvailable) {
      return res.status(503).json({
        success: false,
        error: 'Document Intelligence service is not available',
        details: 'Please check service configuration'
      });
    }

    // Perform document analysis
    const result = await documentIntelligenceService.analyzeDocument(
      req.file.path,
      options
    );

    // Send completion update (only if socketManager is available)
    if (socketManager) {
      socketManager.sendAnalysisProgress({
        analysisId,
        step: 'analysis_complete',
        progress: 100,
        message: 'Document analysis completed successfully',
        timestamp: new Date().toISOString()
      });
    }

    logger.info('Document intelligence analysis completed', {
      analysisId,
      documentType: result.documentType,
      confidence: result.confidence,
      processingTime: result.processingTime
    });

    res.json({
      success: true,
      data: {
        analysisId,
        result,
        metadata: {
          filename: req.file.originalname,
          fileSize: req.file.size,
          processingOptions: options
        }
      }
    });

  } catch (error) {
    logger.error('Document intelligence analysis failed', {
      analysisId,
      error: error.message,
      filename: req.file?.originalname
    });

    // Send error update (only if socketManager is available)
    if (socketManager) {
      socketManager.sendAnalysisProgress({
        analysisId,
        step: 'analysis_error',
        progress: 0,
        message: `Analysis failed: ${error.message}`,
        timestamp: new Date().toISOString()
      });
    }

    res.status(500).json({
      success: false,
      error: 'Document analysis failed',
      details: error.message,
      analysisId
    });
  }
});

/**
 * POST /api/document-intelligence/analyze-enhanced
 * Enhanced analysis combining Document Intelligence with GPT-4o vision
 */
router.post('/analyze-enhanced', upload.single('document'), async (req, res) => {
  const analysisId = uuidv4();
  
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No document file provided'
      });
    }

    logger.info('Starting enhanced document analysis', {
      analysisId,
      filename: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size
    });

    // Parse configuration from request
    const config: AnalysisConfig & { documentOptions?: DocumentProcessingOptions } = {
      useGPT4o: req.body.useGPT4o !== 'false',
      useReasoning: req.body.useReasoning === 'true',
      focusOnMaterials: req.body.focusOnMaterials === 'true',
      focusOnHardware: req.body.focusOnHardware === 'true',
      enableMultiView: req.body.enableMultiView === 'true',
      documentOptions: {
        modelType: req.body.modelType || 'prebuilt-layout',
        extractTables: req.body.extractTables === 'true',
        extractKeyValuePairs: req.body.extractKeyValuePairs === 'true',
        enableKitchenAnalysis: req.body.enableKitchenAnalysis === 'true',
        confidenceThreshold: parseFloat(req.body.confidenceThreshold) || 0.7
      }
    };

    // Perform enhanced analysis
    const result = await enhancedAnalysisIntegration.analyzeDocumentEnhanced(
      req.file.path,
      analysisId,
      config
    );

    logger.info('Enhanced document analysis completed', {
      analysisId,
      qualityScore: result.combinedInsights.qualityScore,
      totalProcessingTime: result.processingMetrics.totalProcessingTime
    });

    res.json({
      success: true,
      data: {
        analysisId,
        result,
        metadata: {
          filename: req.file.originalname,
          fileSize: req.file.size,
          processingConfig: config
        }
      }
    });

  } catch (error) {
    logger.error('Enhanced document analysis failed', {
      analysisId,
      error: error.message,
      filename: req.file?.originalname
    });

    res.status(500).json({
      success: false,
      error: 'Enhanced analysis failed',
      details: error.message,
      analysisId
    });
  }
});

/**
 * POST /api/document-intelligence/analyze-kitchen
 * Kitchen-specific document analysis
 */
router.post('/analyze-kitchen', upload.single('document'), async (req, res) => {
  const analysisId = uuidv4();
  
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No document file provided'
      });
    }

    logger.info('Starting kitchen document analysis', {
      analysisId,
      filename: req.file.originalname
    });

    const config: AnalysisConfig & { documentOptions?: DocumentProcessingOptions } = {
      useGPT4o: true,
      useReasoning: false,
      focusOnMaterials: true,
      focusOnHardware: true,
      enableMultiView: false,
      documentOptions: {
        modelType: 'prebuilt-layout',
        extractTables: true,
        extractKeyValuePairs: true,
        enableKitchenAnalysis: true,
        confidenceThreshold: 0.7
      }
    };

    const result = await enhancedAnalysisIntegration.analyzeKitchenDocument(
      req.file.path,
      analysisId,
      config
    );

    logger.info('Kitchen document analysis completed', {
      analysisId,
      documentType: result.documentType,
      totalCabinets: result.specifications.totalCabinets
    });

    res.json({
      success: true,
      data: {
        analysisId,
        result,
        metadata: {
          filename: req.file.originalname,
          fileSize: req.file.size
        }
      }
    });

  } catch (error) {
    logger.error('Kitchen document analysis failed', {
      analysisId,
      error: error.message,
      filename: req.file?.originalname
    });

    res.status(500).json({
      success: false,
      error: 'Kitchen analysis failed',
      details: error.message,
      analysisId
    });
  }
});

/**
 * GET /api/document-intelligence/health
 * Get Document Intelligence service health status
 */
router.get('/health', async (req, res) => {
  try {
    const healthStatus = await documentIntelligenceService.getHealthStatus();
    
    res.json({
      success: true,
      data: healthStatus
    });

  } catch (error) {
    logger.error('Health check failed', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: 'Health check failed',
      details: error.message
    });
  }
});

/**
 * GET /api/document-intelligence/models
 * Get available Document Intelligence models
 */
router.get('/models', (req, res) => {
  const availableModels = [
    {
      id: 'prebuilt-layout',
      name: 'Layout Analysis',
      description: 'Extract text, tables, and layout structure',
      capabilities: ['text', 'tables', 'layout', 'key-value-pairs']
    },
    {
      id: 'prebuilt-read',
      name: 'Read Model',
      description: 'Optimized for text extraction',
      capabilities: ['text', 'handwriting']
    },
    {
      id: 'prebuilt-document',
      name: 'General Document',
      description: 'General purpose document analysis',
      capabilities: ['text', 'tables', 'key-value-pairs']
    },
    {
      id: 'prebuilt-invoice',
      name: 'Invoice Analysis',
      description: 'Specialized for invoice processing',
      capabilities: ['text', 'tables', 'invoice-fields']
    }
  ];

  res.json({
    success: true,
    data: {
      models: availableModels,
      defaultModel: 'prebuilt-layout'
    }
  });
});

/**
 * Error handling middleware
 */
router.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: 'File too large',
        details: 'Maximum file size is 50MB'
      });
    }
  }

  logger.error('Document Intelligence route error', {
    error: error.message,
    path: req.path,
    method: req.method
  });

  res.status(500).json({
    success: false,
    error: 'Internal server error',
    details: error.message
  });
});

export default router;
