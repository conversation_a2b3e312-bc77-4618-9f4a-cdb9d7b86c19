import express, { Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { createModuleLogger } from '@/utils/logger';
import { asyncHandler, ValidationError } from '@/middleware/errorHandler';
import { EnhancedPdfProcessor } from '@/services/enhancedPdfProcessor';

const logger = createModuleLogger('PDFRoutes');
const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = process.env.UPLOAD_DIR || './server/uploads';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${file.originalname}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 1
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new ValidationError('Only PDF files are allowed'));
    }
  }
});

const enhancedPdfProcessor = new EnhancedPdfProcessor();

/**
 * POST /api/pdf/process-enhanced
 * Process PDF with enhanced capabilities
 */
router.post('/process-enhanced', upload.single('file'), asyncHandler(async (req: Request, res: Response) => {
  if (!req.file) {
    throw new ValidationError('No PDF file uploaded');
  }

  const options = req.body.options ? JSON.parse(req.body.options) : {};
  const outputDir = path.join(process.env.TEMP_DIR || './server/temp', uuidv4());

  try {
    // Ensure output directory exists
    await fs.promises.mkdir(outputDir, { recursive: true });

    // Process PDF with enhanced capabilities
    const processingResult = await enhancedPdfProcessor.processPdf(req.file.path, outputDir, options);

    // Extract text content
    const textContent = await enhancedPdfProcessor.extractTextContent(req.file.path);

    // Detect dimensions
    const dimensions = enhancedPdfProcessor.detectDimensions(textContent);

    // Analyze kitchen-specific content
    const kitchenAnalysis = await enhancedPdfProcessor.analyzeKitchenContent(req.file.path);

    res.json({
      success: true,
      data: {
        pages: processingResult.pages,
        textContent,
        dimensions,
        kitchenAnalysis,
        metadata: processingResult.metadata
      }
    });

  } catch (error) {
    logger.error('Enhanced PDF processing failed:', error);
    throw error;
  } finally {
    // Cleanup uploaded file
    fs.unlink(req.file.path, (err) => {
      if (err) logger.warn('Failed to cleanup uploaded file:', err);
    });

    // Cleanup output directory
    fs.rmdir(outputDir, { recursive: true }, (err) => {
      if (err) logger.warn('Failed to cleanup output directory:', err);
    });
  }
}));

/**
 * POST /api/pdf/extract-text
 * Extract text content from PDF
 */
router.post('/extract-text', upload.single('file'), asyncHandler(async (req: Request, res: Response) => {
  if (!req.file) {
    throw new ValidationError('No PDF file uploaded');
  }

  try {
    const textContent = await enhancedPdfProcessor.extractTextContent(req.file.path);
    const extractionMethod = textContent.length > 100 ? 'direct' : 'ocr';
    const confidence = textContent.length > 100 ? 0.95 : 0.8;

    res.json({
      success: true,
      data: {
        textContent,
        extractionMethod,
        confidence,
        length: textContent.length
      }
    });

  } catch (error) {
    logger.error('Text extraction failed:', error);
    throw error;
  } finally {
    // Cleanup uploaded file
    fs.unlink(req.file.path, (err) => {
      if (err) logger.warn('Failed to cleanup uploaded file:', err);
    });
  }
}));

/**
 * POST /api/pdf/detect-dimensions
 * Detect dimensions in PDF text content
 */
router.post('/detect-dimensions', asyncHandler(async (req: Request, res: Response) => {
  const { textContent } = req.body;

  if (!textContent) {
    throw new ValidationError('Text content is required');
  }

  try {
    const dimensions = enhancedPdfProcessor.detectDimensions(textContent);

    res.json({
      success: true,
      data: {
        dimensions,
        total: dimensions.length
      }
    });

  } catch (error) {
    logger.error('Dimension detection failed:', error);
    throw error;
  }
}));

/**
 * POST /api/pdf/analyze-kitchen-content
 * Analyze kitchen-specific content in PDF
 */
router.post('/analyze-kitchen-content', upload.single('file'), asyncHandler(async (req: Request, res: Response) => {
  if (!req.file) {
    throw new ValidationError('No PDF file uploaded');
  }

  try {
    const kitchenAnalysis = await enhancedPdfProcessor.analyzeKitchenContent(req.file.path);

    res.json({
      success: true,
      data: kitchenAnalysis
    });

  } catch (error) {
    logger.error('Kitchen content analysis failed:', error);
    throw error;
  } finally {
    // Cleanup uploaded file
    fs.unlink(req.file.path, (err) => {
      if (err) logger.warn('Failed to cleanup uploaded file:', err);
    });
  }
}));

/**
 * GET /api/pdf/processing-stats
 * Get PDF processing statistics
 */
router.get('/processing-stats', asyncHandler(async (req: Request, res: Response) => {
  try {
    const stats = {
      totalProcessed: 0, // Would be tracked in a real implementation
      averageProcessingTime: 0,
      successRate: 0.95,
      supportedFormats: ['pdf'],
      features: [
        'OCR text extraction',
        'Dimension detection',
        'Kitchen content analysis',
        'Quality optimization',
        'Thumbnail generation'
      ]
    };

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    logger.error('Failed to get PDF processing stats:', error);
    throw error;
  }
}));

export default router;
