import express, { Request, Response } from 'express';
import { createModuleLogger } from '@/utils/logger';
import { asyncHandler, ValidationError } from '@/middleware/errorHandler';
import { reasoningManager } from '@/services/reasoningManager';

const logger = createModuleLogger('ReasoningRoutes');
const router = express.Router();

/**
 * POST /api/reasoning/start
 * Start a new reasoning chain
 */
router.post('/start', asyncHandler(async (req: Request, res: Response) => {
  const { analysisId, context } = req.body;

  if (!analysisId) {
    throw new ValidationError('Analysis ID is required');
  }

  if (!context || !context.analysisType) {
    throw new ValidationError('Context with analysisType is required');
  }

  try {
    const chainId = reasoningManager.startReasoningChain(analysisId, context);
    const chain = reasoningManager.getChainStatus(chainId);

    res.json({
      success: true,
      data: {
        chainId,
        goal: chain.goal,
        steps: chain.steps,
        status: chain.status
      }
    });
  } catch (error) {
    logger.error('Failed to start reasoning chain:', error);
    throw error;
  }
}));

/**
 * POST /api/reasoning/:chainId/progress
 * Progress a reasoning chain to the next step
 */
router.post('/:chainId/progress', asyncHandler(async (req: Request, res: Response) => {
  const { chainId } = req.params;
  const { stepIndex, evidence, confidence, reasoning } = req.body;

  if (!chainId) {
    throw new ValidationError('Chain ID is required');
  }

  if (stepIndex === undefined) {
    throw new ValidationError('Step index is required');
  }

  try {
    reasoningManager.progressChain(chainId, stepIndex, evidence, confidence, reasoning);
    const status = reasoningManager.getChainStatus(chainId);

    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    logger.error('Failed to progress reasoning chain:', error);
    throw error;
  }
}));

/**
 * GET /api/reasoning/:chainId/status
 * Get reasoning chain status
 */
router.get('/:chainId/status', asyncHandler(async (req: Request, res: Response) => {
  const { chainId } = req.params;

  if (!chainId) {
    throw new ValidationError('Chain ID is required');
  }

  try {
    const status = reasoningManager.getChainStatus(chainId);

    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    logger.error('Failed to get reasoning chain status:', error);
    throw error;
  }
}));

/**
 * POST /api/reasoning/:chainId/complete
 * Complete a reasoning chain
 */
router.post('/:chainId/complete', asyncHandler(async (req: Request, res: Response) => {
  const { chainId } = req.params;

  if (!chainId) {
    throw new ValidationError('Chain ID is required');
  }

  try {
    const result = reasoningManager.completeChain(chainId);

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Failed to complete reasoning chain:', error);
    throw error;
  }
}));

/**
 * GET /api/reasoning/active
 * Get all active reasoning chains
 */
router.get('/active', asyncHandler(async (req: Request, res: Response) => {
  try {
    const activeChains = reasoningManager.getActiveChains();

    res.json({
      success: true,
      data: {
        chains: activeChains,
        total: activeChains.length
      }
    });
  } catch (error) {
    logger.error('Failed to get active reasoning chains:', error);
    throw error;
  }
}));

/**
 * GET /api/reasoning/templates
 * Get available reasoning templates
 */
router.get('/templates', asyncHandler(async (req: Request, res: Response) => {
  try {
    const templates = reasoningManager.getReasoningTemplates();

    res.json({
      success: true,
      data: {
        templates,
        total: Object.keys(templates).length
      }
    });
  } catch (error) {
    logger.error('Failed to get reasoning templates:', error);
    throw error;
  }
}));

/**
 * POST /api/reasoning/:chainId/abort
 * Abort a reasoning chain
 */
router.post('/:chainId/abort', asyncHandler(async (req: Request, res: Response) => {
  const { chainId } = req.params;

  if (!chainId) {
    throw new ValidationError('Chain ID is required');
  }

  try {
    reasoningManager.abortChain(chainId);

    res.json({
      success: true,
      data: {
        chainId,
        status: 'aborted',
        message: 'Reasoning chain aborted successfully'
      }
    });
  } catch (error) {
    logger.error('Failed to abort reasoning chain:', error);
    throw error;
  }
}));

/**
 * GET /api/reasoning/stats
 * Get reasoning statistics
 */
router.get('/stats', asyncHandler(async (req: Request, res: Response) => {
  try {
    const stats = reasoningManager.getReasoningStats();

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Failed to get reasoning stats:', error);
    throw error;
  }
}));

export default router;
