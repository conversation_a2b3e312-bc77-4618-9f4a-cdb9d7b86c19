import express, { Request, Response } from 'express';
import { createModuleLogger } from '@/utils/logger';
import { asyncHandler, ValidationError } from '@/middleware/errorHandler';
import { promptOptimizationService } from '@/services/promptOptimizationService';

const logger = createModuleLogger('OptimizationRoutes');
const router = express.Router();

/**
 * POST /api/optimization/optimize
 * Optimize a prompt using available heuristics
 */
router.post('/optimize', asyncHandler(async (req: Request, res: Response) => {
  const { originalPrompt, context } = req.body;

  if (!originalPrompt) {
    throw new ValidationError('Original prompt is required');
  }

  if (!context || !context.analysisType) {
    throw new ValidationError('Context with analysisType is required');
  }

  try {
    const result = await promptOptimizationService.optimizePrompt(originalPrompt, context);

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Prompt optimization failed:', error);
    throw error;
  }
}));

/**
 * GET /api/optimization/history/:analysisType
 * Get optimization history for a specific analysis type
 */
router.get('/history/:analysisType', asyncHandler(async (req: Request, res: Response) => {
  const { analysisType } = req.params;
  const limit = parseInt(req.query.limit as string) || 50;

  if (!analysisType) {
    throw new ValidationError('Analysis type is required');
  }

  try {
    const history = promptOptimizationService.getOptimizationHistory(analysisType, limit);

    res.json({
      success: true,
      data: {
        history,
        analysisType,
        total: history.length
      }
    });
  } catch (error) {
    logger.error('Failed to get optimization history:', error);
    throw error;
  }
}));

/**
 * GET /api/optimization/heuristics
 * Get available optimization heuristics
 */
router.get('/heuristics', asyncHandler(async (req: Request, res: Response) => {
  try {
    const heuristics = promptOptimizationService.getAvailableHeuristics();

    res.json({
      success: true,
      data: {
        heuristics,
        total: heuristics.length
      }
    });
  } catch (error) {
    logger.error('Failed to get heuristics:', error);
    throw error;
  }
}));

/**
 * POST /api/optimization/evaluate
 * Evaluate prompt performance
 */
router.post('/evaluate', asyncHandler(async (req: Request, res: Response) => {
  const { prompt, context, metrics } = req.body;

  if (!prompt) {
    throw new ValidationError('Prompt is required');
  }

  if (!context) {
    throw new ValidationError('Context is required');
  }

  try {
    const evaluation = promptOptimizationService.evaluatePrompt(prompt, context, metrics);

    res.json({
      success: true,
      data: evaluation
    });
  } catch (error) {
    logger.error('Prompt evaluation failed:', error);
    throw error;
  }
}));

/**
 * GET /api/optimization/stats
 * Get optimization statistics
 */
router.get('/stats', asyncHandler(async (req: Request, res: Response) => {
  try {
    const stats = promptOptimizationService.getOptimizationStats();

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Failed to get optimization stats:', error);
    throw error;
  }
}));

export default router;
