import express, { Request, Response } from 'express';
import { createModuleLogger } from '@/utils/logger';
import { asyncHandler, ValidationError } from '@/middleware/errorHandler';
import { abTestManager } from '@/services/abTestManager';

const logger = createModuleLogger('ABTestRoutes');
const router = express.Router();

/**
 * POST /api/ab-tests/create
 * Create a new A/B test
 */
router.post('/create', asyncHandler(async (req: Request, res: Response) => {
  const testConfig = req.body;

  if (!testConfig.name) {
    throw new ValidationError('Test name is required');
  }

  if (!testConfig.variants || !Array.isArray(testConfig.variants) || testConfig.variants.length < 2) {
    throw new ValidationError('At least 2 variants are required');
  }

  if (!testConfig.trafficAllocation) {
    throw new ValidationError('Traffic allocation is required');
  }

  try {
    const testId = await abTestManager.createTest(testConfig);

    res.json({
      success: true,
      data: {
        testId,
        message: 'A/B test created successfully'
      }
    });
  } catch (error) {
    logger.error('Failed to create A/B test:', error);
    throw error;
  }
}));

/**
 * POST /api/ab-tests/:testId/start
 * Start an A/B test
 */
router.post('/:testId/start', asyncHandler(async (req: Request, res: Response) => {
  const { testId } = req.params;

  if (!testId) {
    throw new ValidationError('Test ID is required');
  }

  try {
    await abTestManager.startTest(testId);

    res.json({
      success: true,
      data: {
        testId,
        status: 'running',
        message: 'A/B test started successfully'
      }
    });
  } catch (error) {
    logger.error('Failed to start A/B test:', error);
    throw error;
  }
}));

/**
 * POST /api/ab-tests/:testId/stop
 * Stop an A/B test
 */
router.post('/:testId/stop', asyncHandler(async (req: Request, res: Response) => {
  const { testId } = req.params;

  if (!testId) {
    throw new ValidationError('Test ID is required');
  }

  try {
    await abTestManager.stopTest(testId);

    res.json({
      success: true,
      data: {
        testId,
        status: 'completed',
        message: 'A/B test stopped successfully'
      }
    });
  } catch (error) {
    logger.error('Failed to stop A/B test:', error);
    throw error;
  }
}));

/**
 * GET /api/ab-tests/:testId/status
 * Get A/B test status
 */
router.get('/:testId/status', asyncHandler(async (req: Request, res: Response) => {
  const { testId } = req.params;

  if (!testId) {
    throw new ValidationError('Test ID is required');
  }

  try {
    const status = abTestManager.getTestStatus(testId);

    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    logger.error('Failed to get A/B test status:', error);
    throw error;
  }
}));

/**
 * GET /api/ab-tests/active
 * Get all active A/B tests
 */
router.get('/active', asyncHandler(async (req: Request, res: Response) => {
  try {
    const activeTests = abTestManager.getActiveTests();

    res.json({
      success: true,
      data: {
        tests: activeTests,
        total: activeTests.length
      }
    });
  } catch (error) {
    logger.error('Failed to get active A/B tests:', error);
    throw error;
  }
}));

/**
 * GET /api/ab-tests/variant/:analysisType/:analysisId
 * Get prompt variant for analysis
 */
router.get('/variant/:analysisType/:analysisId', asyncHandler(async (req: Request, res: Response) => {
  const { analysisType, analysisId } = req.params;

  if (!analysisType || !analysisId) {
    throw new ValidationError('Analysis type and analysis ID are required');
  }

  try {
    const variant = abTestManager.getPromptVariant(analysisType, analysisId);

    res.json({
      success: true,
      data: variant
    });
  } catch (error) {
    logger.error('Failed to get prompt variant:', error);
    throw error;
  }
}));

/**
 * POST /api/ab-tests/record-result
 * Record A/B test result
 */
router.post('/record-result', asyncHandler(async (req: Request, res: Response) => {
  const result = req.body;

  if (!result.variantId || !result.analysisId) {
    throw new ValidationError('Variant ID and analysis ID are required');
  }

  if (!result.metrics) {
    throw new ValidationError('Metrics are required');
  }

  try {
    await abTestManager.recordResult(result);

    res.json({
      success: true,
      data: {
        message: 'Result recorded successfully'
      }
    });
  } catch (error) {
    logger.error('Failed to record A/B test result:', error);
    throw error;
  }
}));

/**
 * GET /api/ab-tests/:testId/results
 * Get A/B test results and analysis
 */
router.get('/:testId/results', asyncHandler(async (req: Request, res: Response) => {
  const { testId } = req.params;

  if (!testId) {
    throw new ValidationError('Test ID is required');
  }

  try {
    const results = await abTestManager.getTestResults(testId);

    res.json({
      success: true,
      data: results
    });
  } catch (error) {
    logger.error('Failed to get A/B test results:', error);
    throw error;
  }
}));

export default router;
