import express from 'express';
import { PerformanceMonitoringService, TestMetrics, TestSuiteResult } from '../services/performanceMonitoringService';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

const router = express.Router();
const performanceMonitoring = PerformanceMonitoringService.getInstance();

/**
 * POST /api/performance-monitoring/test-metrics
 * Record individual test metrics from Playwright execution
 */
router.post('/test-metrics', async (req, res) => {
  try {
    const {
      testName,
      testCategory,
      browser,
      duration,
      success,
      retryCount = 0,
      errorType,
      errorMessage,
      featureVersion,
      buildId,
      networkCondition,
      resourceUsage,
      metadata
    } = req.body;

    // Validate required fields
    if (!testName || !testCategory || !browser || duration === undefined || success === undefined) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: testName, testCategory, browser, duration, success'
      });
    }

    const testMetrics: TestMetrics = {
      id: uuidv4(),
      testName,
      testCategory,
      browser,
      duration: parseInt(duration),
      success: Boolean(success),
      retryCount: parseInt(retryCount) || 0,
      errorType,
      errorMessage,
      timestamp: new Date(),
      featureVersion,
      buildId,
      networkCondition,
      resourceUsage,
      metadata
    };

    await performanceMonitoring.recordTestMetrics(testMetrics);

    res.json({
      success: true,
      data: {
        testId: testMetrics.id,
        recorded: true
      }
    });
  } catch (error) {
    logger.error('Failed to record test metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to record test metrics'
    });
  }
});

/**
 * POST /api/performance-monitoring/test-suite-results
 * Record test suite execution results
 */
router.post('/test-suite-results', async (req, res) => {
  try {
    const {
      suiteId,
      suiteName,
      totalTests,
      passedTests,
      failedTests,
      skippedTests,
      successRate,
      duration,
      featureVersion,
      buildId,
      browser,
      testCategory
    } = req.body;

    // Validate required fields
    if (!suiteId || !suiteName || totalTests === undefined || passedTests === undefined || 
        failedTests === undefined || successRate === undefined || duration === undefined ||
        !browser || !testCategory) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields for test suite result'
      });
    }

    const testSuiteResult: TestSuiteResult = {
      id: uuidv4(),
      suiteId,
      suiteName,
      totalTests: parseInt(totalTests),
      passedTests: parseInt(passedTests),
      failedTests: parseInt(failedTests),
      skippedTests: parseInt(skippedTests) || 0,
      successRate: parseFloat(successRate),
      duration: parseInt(duration),
      timestamp: new Date(),
      featureVersion,
      buildId,
      browser,
      testCategory
    };

    await performanceMonitoring.recordTestSuiteResult(testSuiteResult);

    res.json({
      success: true,
      data: {
        suiteResultId: testSuiteResult.id,
        recorded: true
      }
    });
  } catch (error) {
    logger.error('Failed to record test suite result:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to record test suite result'
    });
  }
});

/**
 * GET /api/performance-monitoring/dashboard
 * Get comprehensive dashboard data for performance monitoring
 */
router.get('/dashboard', async (req, res) => {
  try {
    const dashboardData = await performanceMonitoring.getDashboardData();

    res.json({
      success: true,
      data: dashboardData
    });
  } catch (error) {
    logger.error('Failed to get dashboard data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get dashboard data'
    });
  }
});

/**
 * POST /api/performance-monitoring/feature-baseline
 * Create baseline metrics for a new feature version
 */
router.post('/feature-baseline', async (req, res) => {
  try {
    const { featureVersion } = req.body;

    if (!featureVersion) {
      return res.status(400).json({
        success: false,
        error: 'Feature version is required'
      });
    }

    await performanceMonitoring.createFeatureBaseline(featureVersion);

    res.json({
      success: true,
      data: {
        featureVersion,
        baselineCreated: true
      }
    });
  } catch (error) {
    logger.error('Failed to create feature baseline:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create feature baseline'
    });
  }
});

/**
 * GET /api/performance-monitoring/feature-impact/:version
 * Get feature impact analysis for a specific version
 */
router.get('/feature-impact/:version', async (req, res) => {
  try {
    const { version } = req.params;
    const analysis = await performanceMonitoring.getFeatureImpactAnalysis(version);

    if (!analysis) {
      return res.status(404).json({
        success: false,
        error: 'Feature impact analysis not found'
      });
    }

    res.json({
      success: true,
      data: analysis
    });
  } catch (error) {
    logger.error('Failed to get feature impact analysis:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get feature impact analysis'
    });
  }
});

/**
 * POST /api/performance-monitoring/analyze-feature-impact
 * Analyze feature impact after deployment
 */
router.post('/analyze-feature-impact', async (req, res) => {
  try {
    const { featureVersion } = req.body;

    if (!featureVersion) {
      return res.status(400).json({
        success: false,
        error: 'Feature version is required'
      });
    }

    const analysis = await performanceMonitoring.analyzeFeatureImpact(featureVersion);

    res.json({
      success: true,
      data: analysis
    });
  } catch (error) {
    logger.error('Failed to analyze feature impact:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to analyze feature impact'
    });
  }
});

/**
 * PUT /api/performance-monitoring/alerts/:alertId/resolve
 * Resolve a performance alert
 */
router.put('/alerts/:alertId/resolve', async (req, res) => {
  try {
    const { alertId } = req.params;

    await performanceMonitoring.resolveAlert(alertId);

    res.json({
      success: true,
      data: {
        alertId,
        resolved: true
      }
    });
  } catch (error) {
    logger.error('Failed to resolve alert:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to resolve alert'
    });
  }
});

/**
 * GET /api/performance-monitoring/test-history/:testName
 * Get execution history for a specific test
 */
router.get('/test-history/:testName', async (req, res) => {
  try {
    const { testName } = req.params;
    const { days = '7' } = req.query;

    const history = await performanceMonitoring.getTestHistory(testName, parseInt(days as string));

    res.json({
      success: true,
      data: {
        testName,
        history,
        totalExecutions: history.length
      }
    });
  } catch (error) {
    logger.error('Failed to get test history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get test history'
    });
  }
});

/**
 * GET /api/performance-monitoring/health
 * Health check endpoint for performance monitoring service
 */
router.get('/health', async (req, res) => {
  try {
    // Simple health check - could be expanded to check database connectivity, etc.
    res.json({
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'performance-monitoring'
      }
    });
  } catch (error) {
    logger.error('Performance monitoring health check failed:', error);
    res.status(500).json({
      success: false,
      error: 'Performance monitoring service unhealthy'
    });
  }
});

export default router;
