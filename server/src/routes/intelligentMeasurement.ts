import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';
import { intelligentMeasurementService } from '../services/intelligentMeasurementService';
import { enhancedPdfProcessor } from '../services/enhancedPdfProcessor';

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'image/png',
      'image/jpeg',
      'image/jpg',
      'image/gif',
      'image/webp'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only PDF and image files are allowed.'));
    }
  }
});

/**
 * POST /api/analysis/intelligent-measurement
 * Perform intelligent measurement analysis on uploaded files
 */
router.post('/intelligent-measurement', upload.single('file'), async (req, res) => {
  const analysisId = uuidv4();
  let uploadedFilePath: string | undefined;
  let processedImagePaths: string[] = [];

  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded'
      });
    }

    uploadedFilePath = req.file.path;
    const originalName = req.file.originalname;
    const fileExtension = path.extname(originalName).toLowerCase();

    logger.info(`Starting intelligent measurement analysis: ${analysisId}`, {
      originalName,
      fileSize: req.file.size,
      mimeType: req.file.mimetype
    });

    // Parse configuration from request body
    const config = {
      enableAutoScale: req.body.enableAutoScale === 'true',
      enableLayoutAnalysis: req.body.enableLayoutAnalysis === 'true',
      enableSpaceOptimization: req.body.enableSpaceOptimization === 'true',
      enableMeasurementValidation: req.body.enableMeasurementValidation === 'true',
      accuracyThreshold: parseFloat(req.body.accuracyThreshold) || 0.85,
      optimizationLevel: req.body.optimizationLevel || 'ADVANCED'
    };

    // Process file based on type
    if (fileExtension === '.pdf') {
      // Process PDF to images
      const outputDir = path.join('uploads', `pdf_${analysisId}`);
      await fs.promises.mkdir(outputDir, { recursive: true });

      const processingResult = await enhancedPdfProcessor.processPdf(uploadedFilePath, outputDir, {
        outputFormat: 'png',
        quality: 95,
        density: 300,
        optimizeForAnalysis: true
      });

      processedImagePaths = processingResult.pages.map(page => page.imagePath);

    } else {
      // Use image file directly
      processedImagePaths = [uploadedFilePath];
    }

    // Perform intelligent measurement analysis
    const measurementResult = await intelligentMeasurementService.analyzeMeasurements(
      processedImagePaths,
      analysisId,
      config
    );

    logger.info(`Intelligent measurement analysis completed: ${analysisId}`, {
      processingTime: measurementResult.processingMetrics.analysisTime,
      confidenceScore: measurementResult.processingMetrics.confidenceScore,
      featuresDetected: measurementResult.processingMetrics.featuresDetected.length
    });

    res.json({
      success: true,
      data: {
        analysisId,
        intelligentMeasurement: measurementResult,
        metadata: {
          originalFileName: originalName,
          fileType: req.file.mimetype,
          fileSize: req.file.size,
          processingTime: measurementResult.processingMetrics.analysisTime,
          imagesProcessed: processedImagePaths.length
        }
      }
    });

  } catch (error) {
    logger.error(`Intelligent measurement analysis failed: ${analysisId}`, error);

    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      analysisId
    });

  } finally {
    // Cleanup uploaded and processed files
    try {
      if (uploadedFilePath && fs.existsSync(uploadedFilePath)) {
        await fs.promises.unlink(uploadedFilePath);
      }

      // Clean up processed images (except original if it was an image)
      for (const imagePath of processedImagePaths) {
        if (imagePath !== uploadedFilePath && fs.existsSync(imagePath)) {
          await fs.promises.unlink(imagePath);
        }
      }

      // Clean up PDF output directory if created
      const outputDir = path.join('uploads', `pdf_${analysisId}`);
      if (fs.existsSync(outputDir)) {
        await fs.promises.rmdir(outputDir, { recursive: true });
      }

    } catch (cleanupError) {
      logger.warn(`Cleanup failed for analysis: ${analysisId}`, cleanupError);
    }
  }
});

/**
 * GET /api/analysis/intelligent-measurement/config
 * Get default configuration options for intelligent measurement
 */
router.get('/intelligent-measurement/config', (req, res) => {
  res.json({
    success: true,
    data: {
      defaultConfig: {
        enableAutoScale: true,
        enableLayoutAnalysis: true,
        enableSpaceOptimization: true,
        enableMeasurementValidation: true,
        accuracyThreshold: 0.85,
        optimizationLevel: 'ADVANCED'
      },
      configOptions: {
        optimizationLevels: ['BASIC', 'ADVANCED', 'COMPREHENSIVE'],
        accuracyThresholds: [0.7, 0.75, 0.8, 0.85, 0.9, 0.95],
        supportedFileTypes: [
          'application/pdf',
          'image/png',
          'image/jpeg',
          'image/jpg',
          'image/gif',
          'image/webp'
        ],
        maxFileSize: '50MB',
        features: {
          autoScale: {
            name: 'Auto-Scale Detection',
            description: 'Automatically detect drawing scales from dimension lines and annotations'
          },
          layoutAnalysis: {
            name: 'Room Layout Analysis',
            description: 'Analyze kitchen layout, work triangle, and traffic flow patterns'
          },
          spaceOptimization: {
            name: 'Space Optimization',
            description: 'Generate recommendations for improving kitchen efficiency and layout'
          },
          measurementValidation: {
            name: 'Measurement Validation',
            description: 'Cross-validate measurements from multiple sources for accuracy'
          }
        }
      }
    }
  });
});

/**
 * POST /api/analysis/intelligent-measurement/batch
 * Process multiple files for intelligent measurement analysis
 */
router.post('/intelligent-measurement/batch', upload.array('files', 10), async (req, res) => {
  const batchId = uuidv4();
  const uploadedFiles: string[] = [];
  const results: any[] = [];

  try {
    if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No files uploaded'
      });
    }

    logger.info(`Starting batch intelligent measurement analysis: ${batchId}`, {
      fileCount: req.files.length
    });

    // Parse configuration
    const config = {
      enableAutoScale: req.body.enableAutoScale === 'true',
      enableLayoutAnalysis: req.body.enableLayoutAnalysis === 'true',
      enableSpaceOptimization: req.body.enableSpaceOptimization === 'true',
      enableMeasurementValidation: req.body.enableMeasurementValidation === 'true',
      accuracyThreshold: parseFloat(req.body.accuracyThreshold) || 0.85,
      optimizationLevel: req.body.optimizationLevel || 'ADVANCED'
    };

    // Process each file
    for (const file of req.files) {
      const analysisId = uuidv4();
      uploadedFiles.push(file.path);

      try {
        let processedImagePaths: string[] = [];
        const fileExtension = path.extname(file.originalname).toLowerCase();

        if (fileExtension === '.pdf') {
          const outputDir = path.join('uploads', `pdf_${analysisId}`);
          await fs.promises.mkdir(outputDir, { recursive: true });

          const processingResult = await enhancedPdfProcessor.processPdf(file.path, outputDir, {
            outputFormat: 'png',
            quality: 95,
            density: 300,
            optimizeForAnalysis: true
          });

          processedImagePaths = processingResult.pages.map(page => page.imagePath);
        } else {
          processedImagePaths = [file.path];
        }

        const measurementResult = await intelligentMeasurementService.analyzeMeasurements(
          processedImagePaths,
          analysisId,
          config
        );

        results.push({
          analysisId,
          fileName: file.originalname,
          success: true,
          intelligentMeasurement: measurementResult
        });

      } catch (error) {
        logger.error(`Batch file processing failed: ${analysisId}`, error);
        results.push({
          analysisId,
          fileName: file.originalname,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    logger.info(`Batch intelligent measurement analysis completed: ${batchId}`, {
      totalFiles: req.files.length,
      successCount,
      failureCount
    });

    res.json({
      success: true,
      data: {
        batchId,
        results,
        summary: {
          totalFiles: req.files.length,
          successCount,
          failureCount,
          successRate: successCount / req.files.length
        }
      }
    });

  } catch (error) {
    logger.error(`Batch intelligent measurement analysis failed: ${batchId}`, error);

    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      batchId
    });

  } finally {
    // Cleanup uploaded files
    for (const filePath of uploadedFiles) {
      try {
        if (fs.existsSync(filePath)) {
          await fs.promises.unlink(filePath);
        }
      } catch (cleanupError) {
        logger.warn(`File cleanup failed: ${filePath}`, cleanupError);
      }
    }
  }
});

export default router;
