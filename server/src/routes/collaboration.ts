import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import { authMiddleware } from '@/middleware/authMiddleware';
import { CollaborationService } from '@/services/collaborationService';
import { ProjectService } from '@/services/projectService';
import { AuthService } from '@/services/authService';
import { logger } from '@/utils/logger';
import { asyncHandler } from '@/middleware/asyncHandler';

const router = express.Router();

// Initialize services
const collaborationService = new CollaborationService();
const projectService = new ProjectService();
const authService = new AuthService();

/**
 * POST /api/collaboration/projects
 * Create a new project
 */
router.post('/projects', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { name, description, organizationId, visibility, tags, folderPath } = req.body;
  const userId = req.user!.id;

  if (!name || name.trim().length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Project name is required'
    });
  }

  const project = await projectService.createProject({
    name: name.trim(),
    description: description?.trim(),
    organizationId,
    ownerId: userId,
    visibility: visibility || 'private',
    tags,
    folderPath
  });

  res.status(201).json({
    success: true,
    data: project
  });
}));

/**
 * GET /api/collaboration/projects
 * Get user's projects
 */
router.get('/projects', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  
  const projects = await projectService.getProjectsByUser(userId);

  res.json({
    success: true,
    data: projects
  });
}));

/**
 * GET /api/collaboration/projects/:projectId
 * Get project by ID
 */
router.get('/projects/:projectId', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const userId = req.user!.id;

  if (!projectId) {
    return res.status(400).json({
      success: false,
      error: 'Project ID is required'
    });
  }

  const project = await projectService.getProjectById(projectId, userId);
  
  if (!project) {
    return res.status(404).json({
      success: false,
      error: 'Project not found'
    });
  }

  res.json({
    success: true,
    data: project
  });
}));

/**
 * PUT /api/collaboration/projects/:projectId
 * Update project
 */
router.put('/projects/:projectId', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const { name, description, visibility, status, tags, folderPath } = req.body;
  const userId = req.user!.id;

  if (!projectId) {
    return res.status(400).json({
      success: false,
      error: 'Project ID is required'
    });
  }

  const project = await projectService.updateProject(projectId, {
    name: name?.trim(),
    description: description?.trim(),
    visibility,
    status,
    tags,
    folderPath
  }, userId);

  res.json({
    success: true,
    data: project
  });
}));

/**
 * DELETE /api/collaboration/projects/:projectId
 * Delete project
 */
router.delete('/projects/:projectId', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const userId = req.user!.id;

  await projectService.deleteProject(projectId, userId);

  res.json({
    success: true,
    message: 'Project deleted successfully'
  });
}));

/**
 * PUT /api/collaboration/projects/bulk
 * Bulk update projects
 */
router.put('/projects/bulk', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { projectIds, updates } = req.body;
  const userId = req.user!.id;

  if (!Array.isArray(projectIds) || projectIds.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Project IDs array is required'
    });
  }

  if (!updates || Object.keys(updates).length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Updates object is required'
    });
  }

  await projectService.bulkUpdateProjects(projectIds, updates, userId);

  res.json({
    success: true,
    message: `Successfully updated ${projectIds.length} projects`
  });
}));

/**
 * POST /api/collaboration/projects/:projectId/share
 * Share project with user or email
 */
router.post('/projects/:projectId/share', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const { userId: targetUserId, email, permissionLevel, expiresAt, password } = req.body;
  const grantedBy = req.user!.id;

  if (!targetUserId && !email) {
    return res.status(400).json({
      success: false,
      error: 'Either userId or email is required'
    });
  }

  if (!['view', 'comment', 'edit', 'admin'].includes(permissionLevel)) {
    return res.status(400).json({
      success: false,
      error: 'Invalid permission level'
    });
  }

  const permission = await projectService.shareProject({
    projectId,
    userId: targetUserId,
    email,
    permissionLevel,
    grantedBy,
    expiresAt: expiresAt ? new Date(expiresAt) : undefined,
    password
  });

  res.status(201).json({
    success: true,
    data: permission
  });
}));

/**
 * GET /api/collaboration/projects/:projectId/permissions
 * Get project permissions
 */
router.get('/projects/:projectId/permissions', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const userId = req.user!.id;

  const permissions = await projectService.getProjectPermissions(projectId, userId);

  res.json({
    success: true,
    data: permissions
  });
}));

/**
 * POST /api/collaboration/comments
 * Create a new comment
 */
router.post('/comments', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { projectId, analysisId, parentCommentId, content, positionData, mentions } = req.body;
  const authorId = req.user!.id;

  if (!content || content.trim().length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Comment content is required'
    });
  }

  if (!projectId && !analysisId) {
    return res.status(400).json({
      success: false,
      error: 'Either projectId or analysisId is required'
    });
  }

  const comment = await collaborationService.createComment({
    projectId,
    analysisId,
    parentCommentId,
    content: content.trim(),
    authorId,
    positionData,
    mentions
  });

  res.status(201).json({
    success: true,
    data: comment
  });
}));

/**
 * GET /api/collaboration/analysis/:analysisId/comments
 * Get comments for an analysis
 */
router.get('/analysis/:analysisId/comments', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { analysisId } = req.params;

  const comments = await collaborationService.getCommentsByAnalysis(analysisId);

  res.json({
    success: true,
    data: comments
  });
}));

/**
 * PUT /api/collaboration/comments/:commentId/status
 * Update comment status
 */
router.put('/comments/:commentId/status', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { commentId } = req.params;
  const { status } = req.body;

  if (!['open', 'in_progress', 'resolved'].includes(status)) {
    return res.status(400).json({
      success: false,
      error: 'Invalid status'
    });
  }

  await collaborationService.updateCommentStatus(commentId, status);

  res.json({
    success: true,
    message: 'Comment status updated successfully'
  });
}));

/**
 * GET /api/collaboration/comments/:commentId
 * Get single comment by ID
 */
router.get('/comments/:commentId', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { commentId } = req.params;

  const comment = await collaborationService.getCommentById(commentId);

  if (!comment) {
    return res.status(404).json({
      success: false,
      error: 'Comment not found'
    });
  }

  res.json({
    success: true,
    data: comment
  });
}));

/**
 * PUT /api/collaboration/comments/:commentId
 * Update comment content
 */
router.put('/comments/:commentId', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { commentId } = req.params;
  const { content, mentions } = req.body;

  if (!content || content.trim().length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Comment content is required'
    });
  }

  const comment = await collaborationService.updateComment(commentId, content.trim(), mentions);

  res.json({
    success: true,
    data: comment
  });
}));

/**
 * DELETE /api/collaboration/comments/:commentId
 * Delete comment
 */
router.delete('/comments/:commentId', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { commentId } = req.params;
  const userId = req.user!.id;

  await collaborationService.deleteComment(commentId, userId);

  res.json({
    success: true,
    message: 'Comment deleted successfully'
  });
}));

/**
 * GET /api/collaboration/projects/:projectId/comments
 * Get comments for a project
 */
router.get('/projects/:projectId/comments', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { projectId } = req.params;

  const comments = await collaborationService.getCommentsByProject(projectId);

  res.json({
    success: true,
    data: comments
  });
}));

/**
 * POST /api/collaboration/annotations
 * Create a visual annotation
 */
router.post('/annotations', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { commentId, analysisId, annotationType, coordinates, styleData } = req.body;
  const createdBy = req.user!.id;

  if (!analysisId) {
    return res.status(400).json({
      success: false,
      error: 'Analysis ID is required'
    });
  }

  if (!['point', 'rectangle', 'circle', 'polygon', 'arrow'].includes(annotationType)) {
    return res.status(400).json({
      success: false,
      error: 'Invalid annotation type'
    });
  }

  if (!coordinates) {
    return res.status(400).json({
      success: false,
      error: 'Coordinates are required'
    });
  }

  const annotation = await collaborationService.createAnnotation({
    commentId,
    analysisId,
    annotationType,
    coordinates,
    styleData,
    createdBy
  });

  res.status(201).json({
    success: true,
    data: annotation
  });
}));

/**
 * GET /api/collaboration/projects/:projectId/presence
 * Get active users in project
 */
router.get('/projects/:projectId/presence', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const userId = req.user!.id;

  // Check if user has access to project
  const hasAccess = await projectService.hasProjectAccess(projectId, userId, 'view');
  if (!hasAccess) {
    return res.status(403).json({
      success: false,
      error: 'Access denied'
    });
  }

  const presence = collaborationService.getProjectPresence(projectId);

  res.json({
    success: true,
    data: presence
  });
}));

/**
 * POST /api/collaboration/projects/:projectId/analyses/:analysisId
 * Add analysis to project
 */
router.post('/projects/:projectId/analyses/:analysisId', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const { projectId, analysisId } = req.params;
  const userId = req.user!.id;

  await projectService.addAnalysisToProject(projectId, analysisId, userId);

  res.json({
    success: true,
    message: 'Analysis added to project successfully'
  });
}));

export default router;
