/**
 * Enhanced Reporting Routes
 * 
 * Priority 2 Enhanced Analysis Engine Feature 3
 * API endpoints for comprehensive PDF report generation with all analysis data
 */

import express, { Request, Response } from 'express';
import { asyncHandler } from '@/middleware/asyncHandler';
import { ValidationError } from '@/utils/errors';
import { logger } from '@/utils/logger';
import { EnhancedReportingService, ReportConfig } from '@/services/enhancedReportingService';
import { AnalysisResults } from '@/services/aiAnalysisService';
import fs from 'fs';
import path from 'path';

const router = express.Router();
const enhancedReportingService = new EnhancedReportingService();

/**
 * POST /api/reports/generate
 * Generate comprehensive PDF report from analysis results
 */
router.post('/generate', asyncHandler(async (req: Request, res: Response) => {
  const { analysisId, templateId, customization, options } = req.body;

  if (!analysisId || !templateId) {
    throw new ValidationError('Analysis ID and template ID are required');
  }

  try {
    // Load analysis results (in a real implementation, this would come from a database)
    // For now, we'll create mock data based on the analysisId
    const analysisResults: AnalysisResults = await loadAnalysisResults(analysisId);

    const config: ReportConfig = {
      templateId,
      analysisId,
      customization,
      includeRawData: options?.includeRawData || false,
      includeCharts: options?.includeCharts !== false,
      include3DVisualization: options?.include3DVisualization !== false,
      format: options?.format || 'pdf',
      quality: options?.quality || 'standard'
    };

    const reportResult = await enhancedReportingService.generateReport(analysisResults, config);

    res.json({
      success: true,
      data: {
        reportId: reportResult.reportId,
        filePath: reportResult.filePath,
        fileSize: reportResult.fileSize,
        pageCount: reportResult.pageCount,
        generationTime: reportResult.generationTime,
        sections: reportResult.sections,
        metadata: reportResult.metadata,
        downloadUrl: `/api/reports/download/${reportResult.reportId}`
      }
    });

  } catch (error) {
    logger.error('Report generation failed:', {
      analysisId,
      templateId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}));

/**
 * GET /api/reports/templates
 * Get available report templates
 */
router.get('/templates', asyncHandler(async (req: Request, res: Response) => {
  try {
    const templates = enhancedReportingService.getTemplates();

    res.json({
      success: true,
      data: {
        templates,
        total: templates.length
      }
    });

  } catch (error) {
    logger.error('Failed to get report templates:', error);
    throw error;
  }
}));

/**
 * GET /api/reports/history
 * Get report generation history
 */
router.get('/history', asyncHandler(async (req: Request, res: Response) => {
  try {
    const history = enhancedReportingService.getReportHistory();

    res.json({
      success: true,
      data: {
        reports: history,
        total: history.length
      }
    });

  } catch (error) {
    logger.error('Failed to get report history:', error);
    throw error;
  }
}));

/**
 * GET /api/reports/download/:reportId
 * Download generated report
 */
router.get('/download/:reportId', asyncHandler(async (req: Request, res: Response) => {
  const { reportId } = req.params;

  if (!reportId) {
    throw new ValidationError('Report ID is required');
  }

  try {
    const report = enhancedReportingService.getReport(reportId);
    if (!report) {
      return res.status(404).json({
        success: false,
        error: 'Report not found'
      });
    }

    // Check if file exists
    if (!fs.existsSync(report.filePath)) {
      return res.status(404).json({
        success: false,
        error: 'Report file not found'
      });
    }

    // Set appropriate headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="cabinet-analysis-${reportId}.pdf"`);
    res.setHeader('Content-Length', report.fileSize);

    // Stream the file
    const fileStream = fs.createReadStream(report.filePath);
    fileStream.pipe(res);

  } catch (error) {
    logger.error('Report download failed:', {
      reportId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}));

/**
 * POST /api/reports/schedule
 * Schedule automated report generation
 */
router.post('/schedule', asyncHandler(async (req: Request, res: Response) => {
  const { analysisId, templateId, frequency, recipients, nextRun } = req.body;

  if (!analysisId || !templateId || !frequency) {
    throw new ValidationError('Analysis ID, template ID, and frequency are required');
  }

  try {
    const scheduleId = await enhancedReportingService.scheduleReport({
      analysisId,
      templateId,
      frequency,
      nextRun: new Date(nextRun),
      recipients: recipients || [],
      active: true
    });

    res.json({
      success: true,
      data: {
        scheduleId,
        message: 'Report scheduled successfully'
      }
    });

  } catch (error) {
    logger.error('Report scheduling failed:', {
      analysisId,
      templateId,
      frequency,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}));

/**
 * GET /api/reports/scheduled
 * Get scheduled reports
 */
router.get('/scheduled', asyncHandler(async (req: Request, res: Response) => {
  try {
    const scheduledReports = enhancedReportingService.getScheduledReports();

    res.json({
      success: true,
      data: {
        schedules: scheduledReports,
        total: scheduledReports.length
      }
    });

  } catch (error) {
    logger.error('Failed to get scheduled reports:', error);
    throw error;
  }
}));

/**
 * DELETE /api/reports/scheduled/:scheduleId
 * Cancel scheduled report
 */
router.delete('/scheduled/:scheduleId', asyncHandler(async (req: Request, res: Response) => {
  const { scheduleId } = req.params;

  if (!scheduleId) {
    throw new ValidationError('Schedule ID is required');
  }

  try {
    const cancelled = enhancedReportingService.cancelScheduledReport(scheduleId);

    if (!cancelled) {
      return res.status(404).json({
        success: false,
        error: 'Scheduled report not found'
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Scheduled report cancelled successfully'
      }
    });

  } catch (error) {
    logger.error('Failed to cancel scheduled report:', {
      scheduleId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}));

/**
 * DELETE /api/reports/:reportId
 * Delete generated report
 */
router.delete('/:reportId', asyncHandler(async (req: Request, res: Response) => {
  const { reportId } = req.params;

  if (!reportId) {
    throw new ValidationError('Report ID is required');
  }

  try {
    const deleted = await enhancedReportingService.deleteReport(reportId);

    if (!deleted) {
      return res.status(404).json({
        success: false,
        error: 'Report not found'
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Report deleted successfully'
      }
    });

  } catch (error) {
    logger.error('Failed to delete report:', {
      reportId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}));

/**
 * GET /api/reports/statistics
 * Get report generation statistics
 */
router.get('/statistics', asyncHandler(async (req: Request, res: Response) => {
  try {
    const statistics = enhancedReportingService.getReportStatistics();

    res.json({
      success: true,
      data: statistics
    });

  } catch (error) {
    logger.error('Failed to get report statistics:', error);
    throw error;
  }
}));

/**
 * Helper function to load analysis results
 * In a real implementation, this would query a database
 */
async function loadAnalysisResults(analysisId: string): Promise<AnalysisResults> {
  // Mock analysis results for demonstration
  // In production, this would load from database or file system
  return {
    id: analysisId,
    cabinets: [
      {
        id: 'cabinet_1',
        type: 'BASE',
        dimensions: { width: 36, height: 34.5, depth: 24 },
        confidence: 0.92,
        position: { x: 0, y: 0 },
        materials: { door: 'Maple', frame: 'Plywood' }
      },
      {
        id: 'cabinet_2',
        type: 'WALL',
        dimensions: { width: 30, height: 30, depth: 12 },
        confidence: 0.88,
        position: { x: 36, y: 0 },
        materials: { door: 'Maple', frame: 'Plywood' }
      }
    ],
    measurements: {
      totalCabinets: 2,
      cabinetsByType: { base: 1, wall: 1 },
      overallWidth: 66,
      overallHeight: 84,
      overallDepth: 24,
      totalLinearFeet: 5.5
    },
    hardware: [
      {
        type: 'HINGE',
        brand: 'Blum',
        model: 'Clip Top',
        confidence: 0.85,
        position: { x: 0, y: 0 }
      }
    ],
    style: {
      primary: 'SHAKER',
      confidence: 0.90,
      characteristics: ['Clean lines', 'Recessed panel', 'Traditional proportions']
    },
    confidence: {
      overall: 0.89,
      cabinets: 0.90,
      measurements: 0.88,
      hardware: 0.85,
      style: 0.90
    },
    processingTime: 45000,
    timestamp: new Date().toISOString()
  };
}

export default router;
