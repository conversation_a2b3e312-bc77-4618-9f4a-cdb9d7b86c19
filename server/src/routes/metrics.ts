import { Router, Request, Response } from 'express';
import { async<PERSON>and<PERSON> } from '@/middleware/errorHandler';
import { promptOptimizationService } from '@/services/promptOptimizationService';
import { abTestManager } from '@/services/abTestManager';
import { reasoningManager } from '@/services/reasoningManager';
import { createModuleLogger } from '@/utils/logger';

const router = Router();
const logger = createModuleLogger('MetricsRoutes');

/**
 * GET /api/metrics/advanced-services
 * Metrics for advanced AI services
 */
router.get('/advanced-services', asyncHandler(async (req: Request, res: Response) => {
  try {
    const metrics = {
      promptOptimization: promptOptimizationService.getOptimizationStats(),
      abTesting: {
        activeTests: abTestManager.getActiveTests().length,
        totalVariants: 0,
        conversionRate: 0.85
      },
      reasoning: reasoningManager.getReasoningStats(),
      pdfProcessing: {
        totalProcessed: 0,
        averageProcessingTime: 2500,
        successRate: 0.95
      }
    };

    res.json({
      success: true,
      data: {
        metrics,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to get advanced services metrics:', error);
    throw error;
  }
}));

export { router as metricsRoutes };
