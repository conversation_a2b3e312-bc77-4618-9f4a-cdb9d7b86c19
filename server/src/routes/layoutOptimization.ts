import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';
import { layoutOptimizationService } from '../services/layout';
import { socketManager } from '../services/socketManager';

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
    files: 10 // Maximum 10 files
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'image/png',
      'image/jpeg',
      'image/jpg',
      'image/gif',
      'image/webp'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Unsupported file type: ${file.mimetype}`));
    }
  }
});

/**
 * POST /api/analysis/layout-optimization
 * Perform layout optimization analysis on uploaded files
 */
router.post('/layout-optimization', upload.array('files', 10), async (req, res) => {
  const analysisId = uuidv4();
  
  try {
    logger.info(`Starting layout optimization analysis: ${analysisId}`, {
      fileCount: req.files?.length || 0,
      userAgent: req.get('User-Agent')
    });

    // Validate files
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No files uploaded'
      });
    }

    const files = req.files as Express.Multer.File[];
    const imagePaths = files.map(file => file.path);

    // Parse configuration
    const config = {
      enableWorkflowAnalysis: req.body.enableWorkflowAnalysis === 'true',
      enableSpaceUtilization: req.body.enableSpaceUtilization === 'true',
      enableErgonomicAssessment: req.body.enableErgonomicAssessment === 'true',
      enableTrafficFlowAnalysis: req.body.enableTrafficFlowAnalysis === 'true',
      enableCostBenefitAnalysis: req.body.enableCostBenefitAnalysis === 'true',
      optimizationLevel: req.body.optimizationLevel || 'DETAILED',
      confidenceThreshold: parseFloat(req.body.confidenceThreshold) || 0.8,
      includeAccessibilityFeatures: req.body.includeAccessibilityFeatures === 'true',
      maxLayoutAlternatives: parseInt(req.body.maxLayoutAlternatives) || 3
    };

    // Send initial progress update
    socketManager.sendAnalysisProgress(analysisId, {
      step: 'layout_optimization_started',
      progress: 0,
      message: 'Starting layout optimization analysis...',
      timestamp: new Date().toISOString()
    });

    // Perform layout optimization analysis
    const result = await layoutOptimizationService.optimizeLayout(imagePaths, analysisId, config);

    // Send completion update
    socketManager.sendAnalysisProgress(analysisId, {
      step: 'layout_optimization_completed',
      progress: 100,
      message: 'Layout optimization analysis completed successfully',
      timestamp: new Date().toISOString()
    });

    // Clean up uploaded files
    files.forEach(file => {
      try {
        fs.unlinkSync(file.path);
      } catch (error) {
        logger.warn(`Failed to delete uploaded file: ${file.path}`, error);
      }
    });

    logger.info(`Layout optimization analysis completed: ${analysisId}`, {
      processingTime: result.processingMetrics.analysisTime,
      confidenceScore: result.processingMetrics.confidenceScore,
      featuresAnalyzed: result.processingMetrics.featuresAnalyzed.length
    });

    res.json({
      success: true,
      data: {
        analysisId,
        layoutOptimization: result,
        processingMetrics: result.processingMetrics
      }
    });

  } catch (error) {
    logger.error(`Layout optimization analysis failed: ${analysisId}`, error);

    // Clean up uploaded files on error
    if (req.files) {
      const files = req.files as Express.Multer.File[];
      files.forEach(file => {
        try {
          fs.unlinkSync(file.path);
        } catch (cleanupError) {
          logger.warn(`Failed to delete uploaded file on error: ${file.path}`, cleanupError);
        }
      });
    }

    // Send error update
    socketManager.sendAnalysisProgress(analysisId, {
      step: 'layout_optimization_error',
      progress: 0,
      message: `Layout optimization analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      timestamp: new Date().toISOString()
    });

    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Layout optimization analysis failed'
    });
  }
});

/**
 * POST /api/analysis/layout-optimization/batch
 * Batch process multiple layout optimization analyses
 */
router.post('/layout-optimization/batch', upload.array('files', 50), async (req, res) => {
  const batchId = uuidv4();
  
  try {
    logger.info(`Starting batch layout optimization: ${batchId}`, {
      fileCount: req.files?.length || 0
    });

    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No files uploaded for batch processing'
      });
    }

    const files = req.files as Express.Multer.File[];
    const config = {
      enableWorkflowAnalysis: req.body.enableWorkflowAnalysis === 'true',
      enableSpaceUtilization: req.body.enableSpaceUtilization === 'true',
      enableErgonomicAssessment: req.body.enableErgonomicAssessment === 'true',
      enableTrafficFlowAnalysis: req.body.enableTrafficFlowAnalysis === 'true',
      enableCostBenefitAnalysis: req.body.enableCostBenefitAnalysis === 'true',
      optimizationLevel: req.body.optimizationLevel || 'DETAILED',
      confidenceThreshold: parseFloat(req.body.confidenceThreshold) || 0.8,
      includeAccessibilityFeatures: req.body.includeAccessibilityFeatures === 'true',
      maxLayoutAlternatives: parseInt(req.body.maxLayoutAlternatives) || 3
    };

    // Group files for batch processing (process in groups of 5)
    const fileGroups = [];
    for (let i = 0; i < files.length; i += 5) {
      fileGroups.push(files.slice(i, i + 5));
    }

    const results = [];
    let processedGroups = 0;

    for (const group of fileGroups) {
      const groupAnalysisId = `${batchId}_group_${processedGroups + 1}`;
      const imagePaths = group.map(file => file.path);

      // Send progress update
      const progress = Math.round((processedGroups / fileGroups.length) * 100);
      socketManager.sendAnalysisProgress(batchId, {
        step: 'batch_processing',
        progress,
        message: `Processing group ${processedGroups + 1} of ${fileGroups.length}...`,
        timestamp: new Date().toISOString()
      });

      try {
        const result = await layoutOptimizationService.optimizeLayout(imagePaths, groupAnalysisId, config);
        results.push({
          groupId: groupAnalysisId,
          files: group.map(f => f.originalname),
          result,
          success: true
        });
      } catch (error) {
        logger.error(`Batch group processing failed: ${groupAnalysisId}`, error);
        results.push({
          groupId: groupAnalysisId,
          files: group.map(f => f.originalname),
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false
        });
      }

      processedGroups++;
    }

    // Clean up uploaded files
    files.forEach(file => {
      try {
        fs.unlinkSync(file.path);
      } catch (error) {
        logger.warn(`Failed to delete uploaded file: ${file.path}`, error);
      }
    });

    // Send completion update
    socketManager.sendAnalysisProgress(batchId, {
      step: 'batch_completed',
      progress: 100,
      message: `Batch processing completed: ${results.filter(r => r.success).length}/${results.length} successful`,
      timestamp: new Date().toISOString()
    });

    logger.info(`Batch layout optimization completed: ${batchId}`, {
      totalGroups: fileGroups.length,
      successfulGroups: results.filter(r => r.success).length,
      failedGroups: results.filter(r => !r.success).length
    });

    res.json({
      success: true,
      data: {
        batchId,
        results,
        summary: {
          totalGroups: fileGroups.length,
          successfulGroups: results.filter(r => r.success).length,
          failedGroups: results.filter(r => !r.success).length
        }
      }
    });

  } catch (error) {
    logger.error(`Batch layout optimization failed: ${batchId}`, error);

    // Clean up uploaded files on error
    if (req.files) {
      const files = req.files as Express.Multer.File[];
      files.forEach(file => {
        try {
          fs.unlinkSync(file.path);
        } catch (cleanupError) {
          logger.warn(`Failed to delete uploaded file on error: ${file.path}`, cleanupError);
        }
      });
    }

    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Batch layout optimization failed'
    });
  }
});

/**
 * GET /api/analysis/layout-optimization/config
 * Get default configuration options for layout optimization
 */
router.get('/layout-optimization/config', (req, res) => {
  res.json({
    success: true,
    data: {
      defaultConfig: layoutOptimizationService.getDefaultConfig(),
      configOptions: {
        optimizationLevels: ['BASIC', 'DETAILED', 'COMPREHENSIVE'],
        confidenceThresholds: [0.6, 0.65, 0.7, 0.75, 0.8, 0.85, 0.9],
        maxLayoutAlternatives: [1, 2, 3, 4, 5],
        supportedFileTypes: [
          'application/pdf',
          'image/png',
          'image/jpeg',
          'image/jpg',
          'image/gif',
          'image/webp'
        ],
        maxFileSize: '50MB',
        maxFiles: 10,
        features: {
          workflowAnalysis: {
            name: 'Workflow Analysis',
            description: 'Analyze kitchen workflow patterns and optimize work triangle efficiency'
          },
          spaceUtilization: {
            name: 'Space Utilization',
            description: 'Identify space optimization opportunities and storage improvements'
          },
          ergonomicAssessment: {
            name: 'Ergonomic Assessment',
            description: 'Evaluate ergonomics and accessibility with ADA compliance analysis'
          },
          trafficFlowAnalysis: {
            name: 'Traffic Flow Analysis',
            description: 'Analyze movement patterns and identify congestion points'
          },
          costBenefitAnalysis: {
            name: 'Cost-Benefit Analysis',
            description: 'Calculate ROI and payback periods for layout improvements'
          },
          accessibilityFeatures: {
            name: 'Accessibility Features',
            description: 'Include universal design and ADA compliance recommendations'
          }
        }
      }
    }
  });
});

/**
 * GET /api/analysis/layout-optimization/templates
 * Get layout optimization templates and best practices
 */
router.get('/layout-optimization/templates', (req, res) => {
  res.json({
    success: true,
    data: {
      layoutTemplates: [
        {
          id: 'galley',
          name: 'Galley Kitchen',
          description: 'Efficient linear layout for narrow spaces',
          workTriangleOptimal: '12-26 feet',
          bestFor: ['Small spaces', 'Narrow kitchens', 'Efficiency-focused designs'],
          considerations: ['Limited counter space', 'Traffic flow through kitchen', 'Storage optimization']
        },
        {
          id: 'l_shaped',
          name: 'L-Shaped Kitchen',
          description: 'Versatile corner layout with good workflow',
          workTriangleOptimal: '12-26 feet',
          bestFor: ['Medium spaces', 'Open floor plans', 'Corner utilization'],
          considerations: ['Corner cabinet access', 'Work triangle efficiency', 'Island potential']
        },
        {
          id: 'u_shaped',
          name: 'U-Shaped Kitchen',
          description: 'Maximum storage and counter space',
          workTriangleOptimal: '12-26 feet',
          bestFor: ['Large spaces', 'Storage needs', 'Multiple cooks'],
          considerations: ['Traffic flow', 'Corner accessibility', 'Work triangle distances']
        },
        {
          id: 'island',
          name: 'Island Kitchen',
          description: 'Central workspace with enhanced functionality',
          workTriangleOptimal: '12-26 feet',
          bestFor: ['Large spaces', 'Entertainment', 'Multiple work zones'],
          considerations: ['Clearance requirements', 'Utilities routing', 'Traffic patterns']
        },
        {
          id: 'peninsula',
          name: 'Peninsula Kitchen',
          description: 'Connected island alternative for smaller spaces',
          workTriangleOptimal: '12-26 feet',
          bestFor: ['Medium spaces', 'Open concepts', 'Additional seating'],
          considerations: ['Traffic flow', 'Sight lines', 'Storage access']
        }
      ],
      workflowPrinciples: [
        {
          principle: 'Work Triangle',
          description: 'Optimize distances between sink, stove, and refrigerator',
          idealRange: '12-26 feet total perimeter',
          importance: 'HIGH'
        },
        {
          principle: 'Zone Planning',
          description: 'Organize kitchen into functional zones (prep, cook, clean, store)',
          idealRange: 'Adjacent zones with minimal overlap',
          importance: 'HIGH'
        },
        {
          principle: 'Clearance Standards',
          description: 'Maintain adequate clearances for safety and accessibility',
          idealRange: '36-42 inches for walkways, 30 inches for work areas',
          importance: 'CRITICAL'
        },
        {
          principle: 'Storage Hierarchy',
          description: 'Place frequently used items in easily accessible locations',
          idealRange: 'Eye level to waist level for daily items',
          importance: 'MEDIUM'
        }
      ],
      ergonomicGuidelines: [
        {
          guideline: 'Counter Heights',
          standard: '36 inches standard, 30-42 inches range',
          accessibility: 'Consider adjustable or varied heights',
          recommendation: 'Multiple work surface heights for different tasks'
        },
        {
          guideline: 'Upper Cabinet Heights',
          standard: '18 inches above counter, 54 inches from floor',
          accessibility: 'Lower for universal access',
          recommendation: 'Adjustable shelving and pull-down mechanisms'
        },
        {
          guideline: 'Reach Zones',
          standard: '15-48 inches from floor for comfortable reach',
          accessibility: 'Consider seated users (15-44 inches)',
          recommendation: 'Pull-out drawers and lazy Susans for deep storage'
        }
      ]
    }
  });
});

/**
 * Error handling middleware
 */
router.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: 'File size too large. Maximum size is 50MB per file.'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        error: 'Too many files. Maximum is 10 files per request.'
      });
    }
  }

  logger.error('Layout optimization route error:', error);
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

export default router;
