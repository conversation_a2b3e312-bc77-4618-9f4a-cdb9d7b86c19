import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';
import { enhancedHardwareRecognitionService } from '../services/enhancedHardwareRecognitionService';
import { enhancedPdfProcessor } from '../services/enhancedPdfProcessor';

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'image/png',
      'image/jpeg',
      'image/jpg',
      'image/gif',
      'image/webp'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only PDF and image files are allowed.'));
    }
  }
});

/**
 * POST /api/analysis/enhanced-hardware
 * Perform enhanced hardware recognition on uploaded files
 */
router.post('/enhanced-hardware', upload.single('file'), async (req, res) => {
  const analysisId = uuidv4();
  let uploadedFilePath: string | undefined;
  let processedImagePaths: string[] = [];

  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded'
      });
    }

    uploadedFilePath = req.file.path;
    const originalName = req.file.originalname;
    const fileExtension = path.extname(originalName).toLowerCase();

    logger.info(`Starting enhanced hardware recognition: ${analysisId}`, {
      originalName,
      fileSize: req.file.size,
      mimeType: req.file.mimetype
    });

    // Parse configuration from request body
    const config = {
      enableBrandRecognition: req.body.enableBrandRecognition === 'true',
      enableModelIdentification: req.body.enableModelIdentification === 'true',
      enableCompatibilityAnalysis: req.body.enableCompatibilityAnalysis === 'true',
      enablePricingLookup: req.body.enablePricingLookup === 'true',
      enableUpgradeRecommendations: req.body.enableUpgradeRecommendations === 'true',
      confidenceThreshold: parseFloat(req.body.confidenceThreshold) || 0.7,
      analysisDepth: req.body.analysisDepth || 'DETAILED'
    };

    // Process file based on type
    if (fileExtension === '.pdf') {
      // Process PDF to images
      const outputDir = path.join('uploads', `pdf_${analysisId}`);
      await fs.promises.mkdir(outputDir, { recursive: true });

      const processingResult = await enhancedPdfProcessor.processPdf(uploadedFilePath, outputDir, {
        outputFormat: 'png',
        quality: 95,
        density: 300,
        optimizeForAnalysis: true
      });

      processedImagePaths = processingResult.pages.map(page => page.imagePath);

    } else {
      // Use image file directly
      processedImagePaths = [uploadedFilePath];
    }

    // Perform enhanced hardware recognition
    const hardwareResult = await enhancedHardwareRecognitionService.recognizeHardware(
      processedImagePaths,
      analysisId,
      config
    );

    logger.info(`Enhanced hardware recognition completed: ${analysisId}`, {
      processingTime: hardwareResult.processingMetrics.analysisTime,
      hardwareItemsDetected: hardwareResult.processingMetrics.hardwareItemsDetected,
      brandsIdentified: hardwareResult.processingMetrics.brandsIdentified,
      confidenceScore: hardwareResult.processingMetrics.confidenceScore
    });

    res.json({
      success: true,
      data: {
        analysisId,
        enhancedHardware: hardwareResult,
        metadata: {
          originalFileName: originalName,
          fileType: req.file.mimetype,
          fileSize: req.file.size,
          processingTime: hardwareResult.processingMetrics.analysisTime,
          imagesProcessed: processedImagePaths.length
        }
      }
    });

  } catch (error) {
    logger.error(`Enhanced hardware recognition failed: ${analysisId}`, error);

    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      analysisId
    });

  } finally {
    // Cleanup uploaded and processed files
    try {
      if (uploadedFilePath && fs.existsSync(uploadedFilePath)) {
        await fs.promises.unlink(uploadedFilePath);
      }

      // Clean up processed images (except original if it was an image)
      for (const imagePath of processedImagePaths) {
        if (imagePath !== uploadedFilePath && fs.existsSync(imagePath)) {
          await fs.promises.unlink(imagePath);
        }
      }

      // Clean up PDF output directory if created
      const outputDir = path.join('uploads', `pdf_${analysisId}`);
      if (fs.existsSync(outputDir)) {
        await fs.promises.rmdir(outputDir, { recursive: true });
      }

    } catch (cleanupError) {
      logger.warn(`Cleanup failed for analysis: ${analysisId}`, cleanupError);
    }
  }
});

/**
 * GET /api/analysis/enhanced-hardware/config
 * Get default configuration options for enhanced hardware recognition
 */
router.get('/enhanced-hardware/config', (req, res) => {
  res.json({
    success: true,
    data: {
      defaultConfig: {
        enableBrandRecognition: true,
        enableModelIdentification: true,
        enableCompatibilityAnalysis: true,
        enablePricingLookup: false,
        enableUpgradeRecommendations: true,
        confidenceThreshold: 0.7,
        analysisDepth: 'DETAILED'
      },
      configOptions: {
        analysisDepths: ['BASIC', 'DETAILED', 'COMPREHENSIVE'],
        confidenceThresholds: [0.5, 0.6, 0.7, 0.75, 0.8, 0.85, 0.9],
        supportedFileTypes: [
          'application/pdf',
          'image/png',
          'image/jpeg',
          'image/jpg',
          'image/gif',
          'image/webp'
        ],
        maxFileSize: '50MB',
        features: {
          brandRecognition: {
            name: 'Brand Recognition',
            description: 'Identify hardware brands (Blum, Hettich, Grass, Salice, Hafele)'
          },
          modelIdentification: {
            name: 'Model Identification',
            description: 'Identify specific hardware models and part numbers'
          },
          compatibilityAnalysis: {
            name: 'Compatibility Analysis',
            description: 'Analyze hardware compatibility with cabinet styles and types'
          },
          pricingLookup: {
            name: 'Pricing Lookup',
            description: 'Estimate hardware costs and replacement pricing'
          },
          upgradeRecommendations: {
            name: 'Upgrade Recommendations',
            description: 'Suggest hardware upgrades and improvements'
          }
        },
        supportedBrands: [
          { name: 'Blum', country: 'Austria', specialties: ['Hinges', 'Drawer Slides', 'Lift Systems'] },
          { name: 'Hettich', country: 'Germany', specialties: ['ArciTech Drawers', 'Sensys Hinges', 'Push-to-Open'] },
          { name: 'Grass', country: 'Austria', specialties: ['Tiomos Hinges', 'DWD Slides', 'Nova Pro'] },
          { name: 'Salice', country: 'Italy', specialties: ['Silentia Hinges', 'Futura Slides', 'Air Systems'] },
          { name: 'Hafele', country: 'Germany', specialties: ['Loox Lighting', 'Free Systems', 'Architectural Hardware'] }
        ],
        hardwareTypes: [
          'HINGE',
          'HANDLE',
          'DRAWER_SLIDE',
          'KNOB',
          'PULL',
          'SOFT_CLOSE',
          'LAZY_SUSAN',
          'SHELF_PIN'
        ]
      }
    }
  });
});

/**
 * GET /api/analysis/enhanced-hardware/brands
 * Get detailed information about supported hardware brands
 */
router.get('/enhanced-hardware/brands', (req, res) => {
  res.json({
    success: true,
    data: {
      brands: [
        {
          name: 'Blum',
          country: 'Austria',
          founded: 1952,
          specialties: ['Hinges', 'Drawer Slides', 'Lift Systems', 'Soft-Close Technology'],
          qualityRating: 0.95,
          priceRange: 'PREMIUM',
          popularProducts: [
            'Clip Top Blumotion Hinges',
            'Tandem Drawer Slides',
            'Aventos Lift Systems'
          ],
          website: 'https://www.blum.com'
        },
        {
          name: 'Hettich',
          country: 'Germany',
          founded: 1888,
          specialties: ['ArciTech Drawers', 'Sensys Hinges', 'Push-to-Open', 'Sliding Systems'],
          qualityRating: 0.92,
          priceRange: 'PREMIUM',
          popularProducts: [
            'ArciTech Drawer System',
            'Sensys Hinges',
            'Push to Open Silent'
          ],
          website: 'https://www.hettich.com'
        },
        {
          name: 'Grass',
          country: 'Austria',
          founded: 1948,
          specialties: ['Tiomos Hinges', 'DWD Slides', 'Nova Pro', 'Vionaro Drawers'],
          qualityRating: 0.90,
          priceRange: 'PREMIUM',
          popularProducts: [
            'Tiomos Self-Close Hinges',
            'DWD XP Slides',
            'Vionaro Drawer System'
          ],
          website: 'https://www.grass.eu'
        },
        {
          name: 'Salice',
          country: 'Italy',
          founded: 1957,
          specialties: ['Silentia Hinges', 'Futura Slides', 'Air Systems', 'Folding Doors'],
          qualityRating: 0.88,
          priceRange: 'MID_RANGE',
          popularProducts: [
            'Silentia+ Hinges',
            'Futura Soft-Close Slides',
            'Air Lift Systems'
          ],
          website: 'https://www.salice.it'
        },
        {
          name: 'Hafele',
          country: 'Germany',
          founded: 1923,
          specialties: ['Loox Lighting', 'Free Systems', 'Architectural Hardware', 'Sliding Systems'],
          qualityRating: 0.85,
          priceRange: 'MID_RANGE',
          popularProducts: [
            'Loox LED Lighting',
            'Free Flap Systems',
            'Matrix Box Drawers'
          ],
          website: 'https://www.hafele.com'
        }
      ]
    }
  });
});

/**
 * POST /api/analysis/enhanced-hardware/batch
 * Process multiple files for enhanced hardware recognition
 */
router.post('/enhanced-hardware/batch', upload.array('files', 10), async (req, res) => {
  const batchId = uuidv4();
  const uploadedFiles: string[] = [];
  const results: any[] = [];

  try {
    if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No files uploaded'
      });
    }

    logger.info(`Starting batch enhanced hardware recognition: ${batchId}`, {
      fileCount: req.files.length
    });

    // Parse configuration
    const config = {
      enableBrandRecognition: req.body.enableBrandRecognition === 'true',
      enableModelIdentification: req.body.enableModelIdentification === 'true',
      enableCompatibilityAnalysis: req.body.enableCompatibilityAnalysis === 'true',
      enablePricingLookup: req.body.enablePricingLookup === 'true',
      enableUpgradeRecommendations: req.body.enableUpgradeRecommendations === 'true',
      confidenceThreshold: parseFloat(req.body.confidenceThreshold) || 0.7,
      analysisDepth: req.body.analysisDepth || 'DETAILED'
    };

    // Process each file
    for (const file of req.files) {
      const analysisId = uuidv4();
      uploadedFiles.push(file.path);

      try {
        let processedImagePaths: string[] = [];
        const fileExtension = path.extname(file.originalname).toLowerCase();

        if (fileExtension === '.pdf') {
          const outputDir = path.join('uploads', `pdf_${analysisId}`);
          await fs.promises.mkdir(outputDir, { recursive: true });

          const processingResult = await enhancedPdfProcessor.processPdf(file.path, outputDir, {
            outputFormat: 'png',
            quality: 95,
            density: 300,
            optimizeForAnalysis: true
          });

          processedImagePaths = processingResult.pages.map(page => page.imagePath);
        } else {
          processedImagePaths = [file.path];
        }

        const hardwareResult = await enhancedHardwareRecognitionService.recognizeHardware(
          processedImagePaths,
          analysisId,
          config
        );

        results.push({
          analysisId,
          fileName: file.originalname,
          success: true,
          enhancedHardware: hardwareResult
        });

      } catch (error) {
        logger.error(`Batch file processing failed: ${analysisId}`, error);
        results.push({
          analysisId,
          fileName: file.originalname,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    logger.info(`Batch enhanced hardware recognition completed: ${batchId}`, {
      totalFiles: req.files.length,
      successCount,
      failureCount
    });

    res.json({
      success: true,
      data: {
        batchId,
        results,
        summary: {
          totalFiles: req.files.length,
          successCount,
          failureCount,
          successRate: successCount / req.files.length
        }
      }
    });

  } catch (error) {
    logger.error(`Batch enhanced hardware recognition failed: ${batchId}`, error);

    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      batchId
    });

  } finally {
    // Cleanup uploaded files
    for (const filePath of uploadedFiles) {
      try {
        if (fs.existsSync(filePath)) {
          await fs.promises.unlink(filePath);
        }
      } catch (cleanupError) {
        logger.warn(`File cleanup failed: ${filePath}`, cleanupError);
      }
    }
  }
});

export default router;
