# Azure OpenAI Configuration (Recommended)
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com/
AZURE_OPENAI_API_VERSION=2025-01-01-preview
AZURE_OPENAI_DEPLOYMENT_GPT4O=gpt-4o
AZURE_OPENAI_DEPLOYMENT_GPT4O_MINI=gpt-4o-mini
AZURE_OPENAI_DEPLOYMENT_GPTO1=o1-preview

# Azure OpenAI Mini Configuration (Optional - for separate GPT-4o-mini endpoint)
AZURE_OPENAI_API_KEY_MINI=your_azure_openai_mini_api_key_here
AZURE_OPENAI_ENDPOINT_MINI=https://your-mini-resource-name.openai.azure.com/

# Standard OpenAI Configuration (Fallback)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ORG_ID=your_openai_org_id_here

# Server Configuration
PORT=3001
NODE_ENV=development
CORS_ORIGIN=http://localhost:8080

# Redis Configuration (optional, for caching and rate limiting)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# File Upload Configuration
MAX_FILE_SIZE=52428800
UPLOAD_DIR=./uploads
TEMP_DIR=./temp

# Analysis Configuration
MAX_CONCURRENT_ANALYSES=5
ANALYSIS_TIMEOUT=300000
ENABLE_RATE_LIMITING=true

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/server.log

# Security Configuration
JWT_SECRET=your_jwt_secret_here
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Test Environment Configuration
ENABLE_TEST_AUTH_BYPASS=false
ENABLE_TEST_ENDPOINTS=false
SKIP_AUTH_IN_DEV=false

# Phase 1: Security Hardening Configuration (Optional)
# Enable enhanced security validation for API keys and endpoints
OPENAI_ENABLE_SECURITY_VALIDATION=true
# Enable API key rotation support (requires external key management)
OPENAI_ENABLE_API_KEY_ROTATION=false
# Security audit level: basic, enhanced, strict
OPENAI_SECURITY_AUDIT_LEVEL=basic
# Enable configuration masking in logs and responses
OPENAI_ENABLE_CONFIG_MASKING=true

# Phase 2: Performance Optimization & Intelligent Caching (Optional)
# Enable adaptive TTL based on usage patterns
CACHE_ENABLE_ADAPTIVE_TTL=true
# Enable predictive caching for likely requests (resource intensive)
CACHE_ENABLE_PREDICTIVE_CACHING=false
# Enable compression optimization for storage efficiency
CACHE_ENABLE_COMPRESSION_OPTIMIZATION=true
# Maximum concurrent embedding generations
CACHE_MAX_CONCURRENT_EMBEDDINGS=3
# Cache warmup schedule (cron format)
CACHE_WARMUP_SCHEDULE=0 2 * * *
# Target API call reduction percentage (60-80% recommended)
CACHE_PERFORMANCE_TARGET_REDUCTION=70

# Phase 1: Advanced AI Intelligence & Analytical Capabilities (Optional)
# Enable advanced spatial reasoning for complex layouts
AI_ENABLE_ADVANCED_SPATIAL_REASONING=true
# Enable multi-model ensemble for improved accuracy
AI_ENABLE_MULTI_MODEL_ENSEMBLE=false
# Enable contextual design analysis
AI_ENABLE_CONTEXTUAL_DESIGN_ANALYSIS=true
# Enable intelligent cabinet counting with spatial relationships
AI_ENABLE_INTELLIGENT_CABINET_COUNTING=true
# Enable spatial relationship analysis
AI_ENABLE_SPATIAL_RELATIONSHIP_ANALYSIS=true
# Enable advanced measurement validation
AI_ENABLE_ADVANCED_MEASUREMENT_VALIDATION=true
# Enable confidence scoring for analysis results
AI_ENABLE_CONFIDENCE_SCORING=true
# Enable uncertainty quantification
AI_ENABLE_UNCERTAINTY_QUANTIFICATION=false
# Overall intelligence level: STANDARD, ENHANCED, ADVANCED
AI_INTELLIGENCE_LEVEL=ENHANCED
# Enable adaptive analysis depth based on image quality/complexity
AI_ENABLE_ADAPTIVE_ANALYSIS_DEPTH=true
