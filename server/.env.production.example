# Cabinet Insight Pro Backend - Production Environment Configuration
# ===================================================================
# Copy this file to .env and configure with your production values

# Application Configuration
# -------------------------
NODE_ENV=production
PORT=3001

# CORS Configuration
# ------------------
# Set to your frontend domain in production
CORS_ORIGIN=https://your-frontend-domain.com

# File Upload Configuration
# -------------------------
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=52428800
# 52428800 = 50MB in bytes

# Azure OpenAI Configuration
# ---------------------------
# Primary endpoint (GPT-4o)
AZURE_OPENAI_API_KEY=your_primary_api_key_here
AZURE_OPENAI_ENDPOINT=https://blackveil.openai.azure.com/
AZURE_OPENAI_API_VERSION=2025-01-01-preview
AZURE_OPENAI_GPT4O_DEPLOYMENT_NAME=gpt-4o

# Secondary endpoint (GPT-4o-mini)
AZURE_OPENAI_GPT4O_MINI_DEPLOYMENT_NAME=gpt-4o-mini
# Note: Uses same API key and endpoint as primary

# Alternative endpoint configuration (if using separate endpoints)
# AZURE_OPENAI_MINI_API_KEY=your_mini_api_key_here
# AZURE_OPENAI_MINI_ENDPOINT=https://ai-opsec9314ai985446969955.openai.azure.com/

# Redis Configuration
# -------------------
REDIS_URL=redis://localhost:6379
# For Redis with authentication:
# REDIS_URL=redis://username:password@localhost:6379
# For Redis Cluster:
# REDIS_URL=redis://node1:6379,node2:6379,node3:6379

# Logging Configuration
# ---------------------
LOG_LEVEL=info
LOG_FILE=./logs/app.log
# Levels: error, warn, info, debug

# Security Configuration
# -----------------------
# Rate limiting (requests per minute)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Session configuration
SESSION_SECRET=your_very_secure_session_secret_here_change_this_in_production

# PDF Processing Configuration
# ----------------------------
# PDF processing quality (1-100)
PDF_QUALITY=85
# Maximum PDF page dimensions
PDF_MAX_WIDTH=2048
PDF_MAX_HEIGHT=2048
# PDF processing DPI
PDF_DPI=150

# Image Processing Configuration (Sharp)
# --------------------------------------
SHARP_CACHE_SIZE=50
SHARP_CONCURRENCY=4

# AI Processing Configuration
# ---------------------------
# Default model for analysis
DEFAULT_AI_MODEL=gpt-4o-mini
# Timeout for AI requests (milliseconds)
AI_REQUEST_TIMEOUT=120000
# Maximum retries for failed AI requests
AI_MAX_RETRIES=3

# Queue Configuration
# -------------------
# Maximum concurrent analysis jobs
MAX_CONCURRENT_JOBS=5
# Job timeout (milliseconds)
JOB_TIMEOUT=300000
# Queue cleanup interval (milliseconds)
QUEUE_CLEANUP_INTERVAL=3600000

# Health Check Configuration
# --------------------------
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Monitoring and Analytics
# ------------------------
# Enable performance monitoring
ENABLE_MONITORING=true
# Enable detailed request logging
ENABLE_REQUEST_LOGGING=true

# Error Handling
# --------------
# Enable error reporting to external service
ENABLE_ERROR_REPORTING=false
# Sentry DSN (if using Sentry for error tracking)
# SENTRY_DSN=https://your-sentry-dsn-here

# Database Configuration (if using database)
# ------------------------------------------
# DATABASE_URL=postgresql://username:password@localhost:5432/cabinet_insight_pro

# SSL/TLS Configuration
# ---------------------
# Enable HTTPS (if handling SSL at application level)
ENABLE_HTTPS=false
# SSL certificate paths (if ENABLE_HTTPS=true)
# SSL_CERT_PATH=./ssl/cert.pem
# SSL_KEY_PATH=./ssl/key.pem

# Backup and Cleanup Configuration
# ---------------------------------
# Automatic cleanup of old files (hours)
CLEANUP_MAX_AGE_HOURS=24
# Cleanup interval (milliseconds)
CLEANUP_INTERVAL=3600000

# Development/Testing Overrides
# -----------------------------
# These should be false in production
ENABLE_DEBUG_ROUTES=false
ENABLE_TEST_ENDPOINTS=false
SKIP_AUTH_IN_DEV=false

# Performance Tuning
# ------------------
# Node.js memory limit (MB)
NODE_MAX_OLD_SPACE_SIZE=4096
# UV thread pool size (for file operations)
UV_THREADPOOL_SIZE=16

# Feature Flags
# -------------
ENABLE_ADVANCED_AI_FEATURES=true
ENABLE_OCR_PROCESSING=true
ENABLE_REASONING_CHAINS=true
ENABLE_AB_TESTING=true
ENABLE_PROMPT_OPTIMIZATION=true

# External Service Configuration
# ------------------------------
# If using external storage (S3, etc.)
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_REGION=us-east-1
# S3_BUCKET_NAME=cabinet-insight-uploads

# If using external monitoring
# NEW_RELIC_LICENSE_KEY=your_new_relic_key
# DATADOG_API_KEY=your_datadog_key

# Webhook Configuration
# ---------------------
# Webhook URL for analysis completion notifications
# WEBHOOK_URL=https://your-webhook-endpoint.com/analysis-complete
# WEBHOOK_SECRET=your_webhook_secret

# Timezone Configuration
# ----------------------
TZ=UTC

# Production Validation
# ---------------------
# These environment variables are checked at startup in production
# Ensure all required variables are set before deployment
