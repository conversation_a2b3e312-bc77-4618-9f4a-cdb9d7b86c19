# Cabinet Insight Pro Backend Server - Production Requirements
# =============================================================
# This file documents all system-level and Node.js dependencies required
# for production-grade PDF processing and AI analysis capabilities.

# SYSTEM-LEVEL DEPENDENCIES (Install via package manager)
# ========================================================

# PDF Processing Tools (Required for pdf-poppler)
# ------------------------------------------------
# Ubuntu/Debian: sudo apt-get install poppler-utils
# CentOS/RHEL:   sudo yum install poppler-utils
# macOS:         brew install poppler
# Alpine:        apk add poppler-utils
# 
# Provides: pdftoppm, pdftotext, pdfinfo, pdftops
# Version: >= 0.86.0 (recommended: latest stable)

# OCR Processing (Required for tesseract integration)
# --------------------------------------------------
# Ubuntu/Debian: sudo apt-get install tesseract-ocr tesseract-ocr-eng
# CentOS/RHEL:   sudo yum install tesseract tesseract-langpack-eng
# macOS:         brew install tesseract
# Alpine:        apk add tesseract-ocr
#
# Version: >= 4.1.0 (recommended: 5.x for best accuracy)

# Image Processing Libraries (Required for Sharp)
# -----------------------------------------------
# Ubuntu/Debian: sudo apt-get install libvips-dev
# CentOS/RHEL:   sudo yum install vips-devel
# macOS:         brew install vips
# Alpine:        apk add vips-dev
#
# Version: >= 8.10.0 (recommended: latest stable)

# Additional System Dependencies
# ------------------------------
# Ubuntu/Debian: sudo apt-get install build-essential python3-dev
# CentOS/RHEL:   sudo yum groupinstall "Development Tools" python3-devel
# macOS:         xcode-select --install
# Alpine:        apk add build-base python3-dev

# NODE.JS DEPENDENCIES (Managed via package.json)
# ===============================================

# Core Framework Dependencies
express==4.18.2
cors==2.8.5
helmet==7.1.0
dotenv==16.3.1
express-rate-limit==7.1.5

# AI and OpenAI Integration
openai==4.24.1

# PDF Processing
pdf-poppler==0.2.1
pdf-parse==1.1.1

# Image Processing
sharp==0.33.1

# File Upload and Processing
multer==1.4.5-lts.1

# Real-time Communication
socket.io==4.7.4

# Data Storage and Caching
redis==4.6.11
ioredis==5.3.2

# Validation and Utilities
joi==17.11.0
uuid==9.0.1

# Logging
winston==3.11.0

# Development Dependencies
tsx==4.6.2
typescript==5.3.3
@types/node==20.10.5
@types/express==4.17.21
@types/cors==2.8.17
@types/multer==1.4.11
@types/uuid==9.0.7
@types/jest==29.5.8

# Testing
jest==29.7.0

# Code Quality
eslint==8.56.0
@typescript-eslint/eslint-plugin==6.15.0
@typescript-eslint/parser==6.15.0

# ENVIRONMENT VARIABLES (Required)
# ================================
# AZURE_OPENAI_API_KEY=your_api_key_here
# AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com/
# AZURE_OPENAI_API_VERSION=2025-01-01-preview
# AZURE_OPENAI_GPT4O_DEPLOYMENT_NAME=gpt-4o
# AZURE_OPENAI_GPT4O_MINI_DEPLOYMENT_NAME=gpt-4o-mini
# NODE_ENV=production
# PORT=3001
# CORS_ORIGIN=https://your-frontend-domain.com
# UPLOAD_DIR=./uploads
# MAX_FILE_SIZE=52428800
# REDIS_URL=redis://localhost:6379

# DOCKER CONSIDERATIONS
# ====================
# When using Docker, ensure the base image includes all system dependencies.
# Recommended base images:
# - node:18-bullseye (includes build tools)
# - node:18-alpine (lightweight, requires manual dependency installation)

# PRODUCTION DEPLOYMENT CHECKLIST
# ===============================
# 1. Install all system-level dependencies listed above
# 2. Verify PDF processing: pdftoppm --help
# 3. Verify OCR capability: tesseract --version
# 4. Verify image processing: vips --version
# 5. Set all required environment variables
# 6. Configure Redis instance
# 7. Set up proper file upload directory with write permissions
# 8. Configure reverse proxy (nginx/apache) for file uploads
# 9. Set up SSL/TLS certificates
# 10. Configure log rotation for winston logs
# 11. Set up monitoring and health checks
# 12. Test PDF processing with sample files
# 13. Verify Azure OpenAI connectivity
# 14. Run Playwright test suite to validate 91.7% success rate

# PERFORMANCE OPTIMIZATION
# ========================
# 1. Configure Sharp for production: SHARP_CACHE_SIZE=50
# 2. Set appropriate worker processes for PDF processing
# 3. Configure Redis for session storage and caching
# 4. Implement file cleanup for temporary uploads
# 5. Set up CDN for processed images (optional)
# 6. Configure rate limiting for API endpoints
# 7. Implement request timeout handling
# 8. Set up proper error monitoring (Sentry, etc.)

# SECURITY CONSIDERATIONS
# ======================
# 1. Validate all file uploads (type, size, content)
# 2. Sanitize file names and paths
# 3. Implement proper CORS configuration
# 4. Use helmet for security headers
# 5. Configure rate limiting
# 6. Implement proper authentication/authorization
# 7. Secure environment variable storage
# 8. Regular security updates for all dependencies
# 9. File upload virus scanning (optional)
# 10. Implement proper logging without sensitive data

# MONITORING AND HEALTH CHECKS
# ============================
# Health check endpoints should verify:
# 1. PDF processing tools availability (pdftoppm)
# 2. OCR tools availability (tesseract)
# 3. Image processing capability (Sharp/vips)
# 4. Azure OpenAI connectivity
# 5. Redis connectivity
# 6. File system write permissions
# 7. Memory and CPU usage
# 8. Queue processing status

# QUICK START COMMANDS
# ===================
# 1. Install system dependencies:
#    ./scripts/setup-system-deps.sh
#
# 2. Install Node.js dependencies:
#    npm install
#
# 3. Configure environment:
#    cp .env.production.example .env
#    # Edit .env with your values
#
# 4. Validate production setup:
#    npm run validate:production
#
# 5. Build and start:
#    npm run build
#    npm start

# TROUBLESHOOTING
# ==============
# Common issues and solutions:
# 1. "pdftoppm not found" - Run: ./scripts/setup-system-deps.sh
# 2. "Sharp installation failed" - Install libvips-dev
# 3. "Permission denied" - Check file upload directory permissions
# 4. "Azure OpenAI timeout" - Check network connectivity and API keys
# 5. "Redis connection failed" - Verify Redis server status
# 6. "PDF processing hangs" - Check system resources and timeouts
# 7. "OCR not working" - Install tesseract-ocr and language packs
# 8. "Validation failed" - Run: npm run validate:production
