#!/bin/bash

# Cabinet Insight Pro - System Dependencies Setup Script
# ======================================================
# This script installs all required system-level dependencies for
# production-grade PDF processing and AI analysis capabilities.

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Detect operating system
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if [ -f /etc/debian_version ]; then
            OS="debian"
        elif [ -f /etc/redhat-release ]; then
            OS="redhat"
        elif [ -f /etc/alpine-release ]; then
            OS="alpine"
        else
            OS="linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    else
        OS="unknown"
    fi
}

# Install dependencies for Debian/Ubuntu
install_debian_deps() {
    log_info "Installing dependencies for Debian/Ubuntu..."
    
    # Update package list
    sudo apt-get update
    
    # Install PDF processing tools
    log_info "Installing PDF processing tools (poppler-utils)..."
    sudo apt-get install -y poppler-utils
    
    # Install OCR tools
    log_info "Installing OCR tools (tesseract)..."
    sudo apt-get install -y tesseract-ocr tesseract-ocr-eng
    
    # Install image processing libraries
    log_info "Installing image processing libraries (libvips)..."
    sudo apt-get install -y libvips-dev
    
    # Install build tools
    log_info "Installing build tools..."
    sudo apt-get install -y build-essential python3-dev
    
    # Install additional useful tools
    sudo apt-get install -y curl wget git
    
    log_success "Debian/Ubuntu dependencies installed successfully!"
}

# Install dependencies for RedHat/CentOS/Fedora
install_redhat_deps() {
    log_info "Installing dependencies for RedHat/CentOS/Fedora..."
    
    # Determine package manager
    if command -v dnf &> /dev/null; then
        PKG_MGR="dnf"
    else
        PKG_MGR="yum"
    fi
    
    # Install PDF processing tools
    log_info "Installing PDF processing tools (poppler-utils)..."
    sudo $PKG_MGR install -y poppler-utils
    
    # Install OCR tools
    log_info "Installing OCR tools (tesseract)..."
    sudo $PKG_MGR install -y tesseract tesseract-langpack-eng
    
    # Install image processing libraries
    log_info "Installing image processing libraries (vips)..."
    sudo $PKG_MGR install -y vips-devel
    
    # Install build tools
    log_info "Installing build tools..."
    sudo $PKG_MGR groupinstall -y "Development Tools"
    sudo $PKG_MGR install -y python3-devel
    
    log_success "RedHat/CentOS/Fedora dependencies installed successfully!"
}

# Install dependencies for Alpine Linux
install_alpine_deps() {
    log_info "Installing dependencies for Alpine Linux..."
    
    # Update package index
    sudo apk update
    
    # Install PDF processing tools
    log_info "Installing PDF processing tools (poppler-utils)..."
    sudo apk add poppler-utils
    
    # Install OCR tools
    log_info "Installing OCR tools (tesseract)..."
    sudo apk add tesseract-ocr
    
    # Install image processing libraries
    log_info "Installing image processing libraries (vips)..."
    sudo apk add vips-dev
    
    # Install build tools
    log_info "Installing build tools..."
    sudo apk add build-base python3-dev
    
    log_success "Alpine Linux dependencies installed successfully!"
}

# Install dependencies for macOS
install_macos_deps() {
    log_info "Installing dependencies for macOS..."
    
    # Check if Homebrew is installed
    if ! command -v brew &> /dev/null; then
        log_error "Homebrew is not installed. Please install it first:"
        log_info "Visit: https://brew.sh/"
        exit 1
    fi
    
    # Update Homebrew
    brew update
    
    # Install PDF processing tools
    log_info "Installing PDF processing tools (poppler)..."
    brew install poppler
    
    # Install OCR tools
    log_info "Installing OCR tools (tesseract)..."
    brew install tesseract
    
    # Install image processing libraries
    log_info "Installing image processing libraries (vips)..."
    brew install vips
    
    # Install build tools (if not already installed)
    if ! xcode-select -p &> /dev/null; then
        log_info "Installing Xcode command line tools..."
        xcode-select --install
    fi
    
    log_success "macOS dependencies installed successfully!"
}

# Verify installations
verify_installations() {
    log_info "Verifying installations..."
    
    # Check PDF tools
    if command -v pdftoppm &> /dev/null; then
        VERSION=$(pdftoppm -v 2>&1 | head -n1)
        log_success "PDF tools installed: $VERSION"
    else
        log_error "PDF tools (pdftoppm) not found!"
        return 1
    fi
    
    # Check OCR tools
    if command -v tesseract &> /dev/null; then
        VERSION=$(tesseract --version 2>&1 | head -n1)
        log_success "OCR tools installed: $VERSION"
    else
        log_error "OCR tools (tesseract) not found!"
        return 1
    fi
    
    # Check image processing
    if command -v vips &> /dev/null; then
        VERSION=$(vips --version 2>&1 | head -n1)
        log_success "Image processing tools installed: $VERSION"
    else
        log_warning "VIPS command not found, but libvips may still be available for Sharp"
    fi
    
    log_success "All verifications completed!"
}

# Main installation function
main() {
    log_info "Cabinet Insight Pro - System Dependencies Setup"
    log_info "=============================================="
    
    # Detect OS
    detect_os
    log_info "Detected OS: $OS"
    
    # Install based on OS
    case $OS in
        "debian")
            install_debian_deps
            ;;
        "redhat")
            install_redhat_deps
            ;;
        "alpine")
            install_alpine_deps
            ;;
        "macos")
            install_macos_deps
            ;;
        *)
            log_error "Unsupported operating system: $OSTYPE"
            log_info "Please install the following packages manually:"
            log_info "- poppler-utils (for PDF processing)"
            log_info "- tesseract-ocr (for OCR)"
            log_info "- libvips-dev (for image processing)"
            log_info "- build-essential (for compilation)"
            exit 1
            ;;
    esac
    
    # Verify installations
    verify_installations
    
    log_success "System dependencies setup completed successfully!"
    log_info "Next steps:"
    log_info "1. Install Node.js dependencies: npm install"
    log_info "2. Set up environment variables (see requirements.txt)"
    log_info "3. Start Redis server"
    log_info "4. Run the application: npm run dev"
}

# Run main function
main "$@"
