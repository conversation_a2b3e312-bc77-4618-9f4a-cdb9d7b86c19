#!/usr/bin/env node

/**
 * Cabinet Insight Pro - Production Validation Script
 * ==================================================
 * Validates all dependencies and configuration for production deployment
 */

const { exec } = require('child_process');
const { promisify } = require('util');
const fs = require('fs');
const path = require('path');

const execAsync = promisify(exec);

// Colors for output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

// Logging functions
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`)
};

// Validation results
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  checks: []
};

// Add check result
function addCheck(name, status, message, recommendation = null) {
  results.checks.push({ name, status, message, recommendation });
  if (status === 'PASS') results.passed++;
  else if (status === 'FAIL') results.failed++;
  else if (status === 'WARN') results.warnings++;
}

// Check if command exists
async function checkCommand(command, name, customPaths = []) {
  // First try standard which command
  try {
    await execAsync(`which ${command}`);
    return await getCommandVersion(command, name);
  } catch (error) {
    // If which fails, try custom paths
    for (const customPath of customPaths) {
      if (fs.existsSync(customPath)) {
        return await getCommandVersion(customPath, name);
      }
    }

    addCheck(name, 'FAIL', 'Not found', `Install ${name} using system package manager`);
    return false;
  }
}

// Get version information for a command
async function getCommandVersion(commandPath, name) {
  try {
    // Try different version flags in order of preference
    let version = 'Available';
    try {
      const { stdout: versionOut } = await execAsync(`${commandPath} --version 2>&1`);
      if (versionOut && !versionOut.includes('Error') && !versionOut.includes('No such file')) {
        version = versionOut.split('\n')[0].trim();
      } else {
        throw new Error('--version failed');
      }
    } catch {
      try {
        const { stdout: vOut } = await execAsync(`${commandPath} -v 2>&1`);
        if (vOut && vOut.trim()) {
          version = vOut.split('\n')[0].trim();
        } else {
          throw new Error('-v failed');
        }
      } catch {
        try {
          const { stdout: helpOut } = await execAsync(`${commandPath} -h 2>&1 | head -1`);
          if (helpOut && helpOut.trim()) {
            version = helpOut.split('\n')[0].trim();
          } else {
            version = 'Available (version unknown)';
          }
        } catch {
          // Command exists but no version info available
          version = 'Available (version unknown)';
        }
      }
    }

    addCheck(name, 'PASS', version);
    return true;
  } catch (error) {
    addCheck(name, 'FAIL', 'Not found', `Install ${name} using system package manager`);
    return false;
  }
}

// Check Node.js dependencies
async function checkNodeDependencies() {
  log.info('Checking Node.js dependencies...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const nodeModulesExists = fs.existsSync('node_modules');
    
    if (!nodeModulesExists) {
      addCheck('Node Dependencies', 'FAIL', 'node_modules not found', 'Run: npm install');
      return false;
    }
    
    // Check critical dependencies
    const criticalDeps = ['express', 'openai', 'sharp', 'pdf-poppler', 'redis', 'socket.io'];
    let allPresent = true;
    
    for (const dep of criticalDeps) {
      const depPath = path.join('node_modules', dep);
      if (fs.existsSync(depPath)) {
        addCheck(`Dependency: ${dep}`, 'PASS', 'Installed');
      } else {
        addCheck(`Dependency: ${dep}`, 'FAIL', 'Missing', `Run: npm install ${dep}`);
        allPresent = false;
      }
    }
    
    return allPresent;
  } catch (error) {
    addCheck('Node Dependencies', 'FAIL', `Error checking dependencies: ${error.message}`);
    return false;
  }
}

// Check system dependencies
async function checkSystemDependencies() {
  log.info('Checking system dependencies...');
  
  const checks = [
    { command: 'pdftoppm', name: 'PDF Processing (poppler-utils)', paths: ['/opt/homebrew/bin/pdftoppm', '/usr/local/bin/pdftoppm'] },
    { command: 'tesseract', name: 'OCR Processing (tesseract)', paths: ['/opt/homebrew/bin/tesseract', '/usr/local/bin/tesseract'] },
    { command: 'node', name: 'Node.js Runtime' },
    { command: 'npm', name: 'NPM Package Manager' }
  ];
  
  let allPresent = true;
  for (const check of checks) {
    const present = await checkCommand(check.command, check.name, check.paths || []);
    if (!present) allPresent = false;
  }
  
  return allPresent;
}

// Check environment variables
function checkEnvironmentVariables() {
  log.info('Checking environment variables...');
  
  const requiredVars = [
    'AZURE_OPENAI_API_KEY',
    'AZURE_OPENAI_ENDPOINT',
    'AZURE_OPENAI_GPT4O_DEPLOYMENT_NAME',
    'AZURE_OPENAI_GPT4O_MINI_DEPLOYMENT_NAME'
  ];
  
  const optionalVars = [
    'NODE_ENV',
    'PORT',
    'CORS_ORIGIN',
    'REDIS_URL',
    'UPLOAD_DIR'
  ];
  
  let allRequired = true;
  
  // Check required variables
  for (const varName of requiredVars) {
    if (process.env[varName]) {
      addCheck(`Env Var: ${varName}`, 'PASS', 'Set');
    } else {
      addCheck(`Env Var: ${varName}`, 'FAIL', 'Missing', `Set ${varName} in environment`);
      allRequired = false;
    }
  }
  
  // Check optional variables
  for (const varName of optionalVars) {
    if (process.env[varName]) {
      addCheck(`Env Var: ${varName}`, 'PASS', 'Set');
    } else {
      addCheck(`Env Var: ${varName}`, 'WARN', 'Not set', `Consider setting ${varName}`);
    }
  }
  
  return allRequired;
}

// Check file system permissions
function checkFileSystemPermissions() {
  log.info('Checking file system permissions...');
  
  const uploadDir = process.env.UPLOAD_DIR || './uploads';
  const logsDir = './logs';
  
  try {
    // Check upload directory
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    // Test write permissions
    const testFile = path.join(uploadDir, 'test-write.tmp');
    fs.writeFileSync(testFile, 'test');
    fs.unlinkSync(testFile);
    addCheck('Upload Directory', 'PASS', `Writable: ${uploadDir}`);
    
    // Check logs directory
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    
    const testLogFile = path.join(logsDir, 'test-write.tmp');
    fs.writeFileSync(testLogFile, 'test');
    fs.unlinkSync(testLogFile);
    addCheck('Logs Directory', 'PASS', `Writable: ${logsDir}`);
    
    return true;
  } catch (error) {
    addCheck('File System Permissions', 'FAIL', `Permission error: ${error.message}`);
    return false;
  }
}

// Check Redis connectivity
async function checkRedisConnectivity() {
  log.info('Checking Redis connectivity...');
  
  try {
    const redis = require('redis');
    const client = redis.createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    });
    
    await client.connect();
    await client.ping();
    await client.disconnect();
    
    addCheck('Redis Connectivity', 'PASS', 'Connected successfully');
    return true;
  } catch (error) {
    addCheck('Redis Connectivity', 'FAIL', `Connection failed: ${error.message}`, 'Check Redis server and REDIS_URL');
    return false;
  }
}

// Check Azure OpenAI connectivity
async function checkAzureOpenAIConnectivity() {
  log.info('Checking Azure OpenAI connectivity...');
  
  try {
    const { OpenAI } = require('openai');
    
    const client = new OpenAI({
      apiKey: process.env.AZURE_OPENAI_API_KEY_MINI || process.env.AZURE_OPENAI_API_KEY,
      baseURL: `${process.env.AZURE_OPENAI_ENDPOINT_MINI || process.env.AZURE_OPENAI_ENDPOINT}/openai/deployments/${process.env.AZURE_OPENAI_GPT4O_MINI_DEPLOYMENT_NAME}`,
      defaultQuery: { 'api-version': process.env.AZURE_OPENAI_API_VERSION || '2025-01-01-preview' },
      defaultHeaders: {
        'api-key': process.env.AZURE_OPENAI_API_KEY_MINI || process.env.AZURE_OPENAI_API_KEY,
      }
    });
    
    // Test with a simple completion
    const response = await client.chat.completions.create({
      model: process.env.AZURE_OPENAI_GPT4O_MINI_DEPLOYMENT_NAME,
      messages: [{ role: 'user', content: 'Test connection' }],
      max_completion_tokens: 5
    });
    
    if (response.choices && response.choices.length > 0) {
      addCheck('Azure OpenAI Connectivity', 'PASS', 'API responding correctly');
      return true;
    } else {
      addCheck('Azure OpenAI Connectivity', 'FAIL', 'Unexpected API response');
      return false;
    }
  } catch (error) {
    addCheck('Azure OpenAI Connectivity', 'FAIL', `API error: ${error.message}`, 'Check API key and endpoint configuration');
    return false;
  }
}

// Generate report
function generateReport() {
  console.log('\n' + '='.repeat(60));
  console.log('CABINET INSIGHT PRO - PRODUCTION VALIDATION REPORT');
  console.log('='.repeat(60));
  
  console.log(`\nSUMMARY:`);
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`⚠️  Warnings: ${results.warnings}`);
  console.log(`📊 Total Checks: ${results.checks.length}`);
  
  console.log(`\nDETAILED RESULTS:`);
  console.log('-'.repeat(60));
  
  for (const check of results.checks) {
    const icon = check.status === 'PASS' ? '✅' : check.status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${icon} ${check.name}: ${check.message}`);
    if (check.recommendation) {
      console.log(`   💡 Recommendation: ${check.recommendation}`);
    }
  }
  
  console.log('\n' + '='.repeat(60));
  
  if (results.failed === 0) {
    log.success('🎉 All critical checks passed! Ready for production deployment.');
    return true;
  } else {
    log.error(`❌ ${results.failed} critical checks failed. Fix these issues before deployment.`);
    return false;
  }
}

// Main validation function
async function main() {
  console.log('Cabinet Insight Pro - Production Validation');
  console.log('==========================================\n');
  
  try {
    // Load environment variables
    require('dotenv').config();
    
    // Run all checks
    await checkSystemDependencies();
    await checkNodeDependencies();
    checkEnvironmentVariables();
    checkFileSystemPermissions();
    
    // Optional checks (don't fail deployment)
    try {
      await checkRedisConnectivity();
    } catch (error) {
      log.warning('Redis check skipped - install redis package if needed');
    }
    
    try {
      await checkAzureOpenAIConnectivity();
    } catch (error) {
      log.warning('Azure OpenAI check skipped - install openai package if needed');
    }
    
    // Generate final report
    const success = generateReport();
    process.exit(success ? 0 : 1);
    
  } catch (error) {
    log.error(`Validation failed: ${error.message}`);
    process.exit(1);
  }
}

// Run validation
if (require.main === module) {
  main();
}

module.exports = { main, checkCommand, addCheck };
