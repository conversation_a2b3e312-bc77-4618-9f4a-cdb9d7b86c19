#!/usr/bin/env node

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration
const dbConfig = {
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || '5432'),
  database: process.env.POSTGRES_DB || 'cabinet_pricing',
  user: process.env.POSTGRES_USER || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'postgres123',
};

async function runMigration() {
  console.log('🚀 Starting Quote Templates Migration...');
  
  const pool = new Pool(dbConfig);
  
  try {
    // Test connection
    console.log('🔌 Testing database connection...');
    const client = await pool.connect();
    await client.query('SELECT 1');
    client.release();
    console.log('✅ Database connection successful');
    
    // Read migration file
    const migrationPath = path.join(__dirname, '../../migrations/002_create_quote_templates.sql');
    console.log(`📋 Reading migration file: ${migrationPath}`);
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`Migration file not found: ${migrationPath}`);
    }
    
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log('📝 Migration file loaded successfully');
    
    // Execute migration
    console.log('⚡ Executing migration...');
    await pool.query(migrationSQL);
    console.log('✅ Migration executed successfully');
    
    // Verify tables were created
    console.log('🔍 Verifying table creation...');
    const tableCheck = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('customer_segments', 'quote_templates', 'template_inheritance', 'template_usage')
      ORDER BY table_name
    `);
    
    console.log('📊 Created tables:');
    tableCheck.rows.forEach(row => {
      console.log(`  ✓ ${row.table_name}`);
    });
    
    // Check data insertion
    console.log('📋 Checking default data...');
    const segmentCount = await pool.query('SELECT COUNT(*) as count FROM customer_segments');
    const templateCount = await pool.query('SELECT COUNT(*) as count FROM quote_templates');
    
    console.log(`📊 Data summary:`);
    console.log(`  Customer Segments: ${segmentCount.rows[0].count}`);
    console.log(`  Quote Templates: ${templateCount.rows[0].count}`);
    
    if (parseInt(segmentCount.rows[0].count) > 0) {
      console.log('📋 Sample customer segments:');
      const segments = await pool.query('SELECT segment_code, segment_name, target_market FROM customer_segments LIMIT 3');
      segments.rows.forEach(segment => {
        console.log(`  • ${segment.segment_code}: ${segment.segment_name} (${segment.target_market})`);
      });
    }
    
    if (parseInt(templateCount.rows[0].count) > 0) {
      console.log('📋 Sample quote templates:');
      const templates = await pool.query('SELECT template_code, template_name, is_default FROM quote_templates LIMIT 3');
      templates.rows.forEach(template => {
        console.log(`  • ${template.template_code}: ${template.template_name}${template.is_default ? ' (default)' : ''}`);
      });
    }
    
    console.log('🎉 Quote Templates Migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run migration if called directly
if (require.main === module) {
  runMigration().catch(console.error);
}

module.exports = { runMigration };
