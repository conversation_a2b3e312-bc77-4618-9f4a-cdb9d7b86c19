#!/usr/bin/env node

/**
 * PostgreSQL Pricing Database Setup Script
 * 
 * This script:
 * 1. Creates the PostgreSQL database schema
 * 2. Populates it with real pricing data from extracted Excel files
 * 3. Validates the data integrity
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration
const dbConfig = {
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || '5432'),
  database: process.env.POSTGRES_DB || 'cabinet_pricing',
  user: process.env.POSTGRES_USER || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'cabinet123',
};

console.log('🚀 Starting PostgreSQL Pricing Database Setup...');
console.log('📊 Database Config:', { ...dbConfig, password: '***' });

async function setupDatabase() {
  // First connect to default postgres database to create cabinet_pricing if needed
  const defaultDbConfig = { ...dbConfig, database: 'postgres' };
  const defaultPool = new Pool(defaultDbConfig);

  try {
    console.log('🔌 Testing connection to default postgres database...');
    const defaultClient = await defaultPool.connect();
    await defaultClient.query('SELECT NOW()');

    // Check if cabinet_pricing database exists, create if not
    const dbCheckResult = await defaultClient.query(
      "SELECT 1 FROM pg_database WHERE datname = 'cabinet_pricing'"
    );

    if (dbCheckResult.rows.length === 0) {
      console.log('📦 Creating cabinet_pricing database...');
      await defaultClient.query('CREATE DATABASE cabinet_pricing');
      console.log('✅ Database cabinet_pricing created successfully');
    } else {
      console.log('✅ Database cabinet_pricing already exists');
    }

    defaultClient.release();
    await defaultPool.end();

    // Now connect to the cabinet_pricing database
    const pool = new Pool(dbConfig);
    console.log('🔌 Connecting to cabinet_pricing database...');
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();
    console.log('✅ Cabinet pricing database connection successful');

    // Run schema migrations
    console.log('📋 Running schema migrations...');
    await runMigrations(pool);

    // Load and insert pricing data
    console.log('💰 Loading pricing data...');
    await loadPricingData(pool);

    // Validate data
    console.log('🔍 Validating data integrity...');
    await validateData(pool);

    console.log('🎉 Database setup completed successfully!');

    await pool.end();

  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  }
}

async function runMigrations(pool) {
  const migrationsDir = path.join(__dirname, '../migrations');
  
  if (!fs.existsSync(migrationsDir)) {
    console.log('⚠️  Migrations directory not found, creating schema manually...');
    await createSchemaManually(pool);
    return;
  }

  const migrationFiles = fs.readdirSync(migrationsDir)
    .filter(file => file.endsWith('.sql'))
    .sort();

  for (const file of migrationFiles) {
    console.log(`  📄 Running migration: ${file}`);
    const migrationPath = path.join(migrationsDir, file);
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    try {
      await pool.query(migrationSQL);
      console.log(`  ✅ Migration completed: ${file}`);
    } catch (error) {
      console.error(`  ❌ Migration failed: ${file}`, error.message);
      throw error;
    }
  }
}

async function createSchemaManually(pool) {
  const schema = `
    -- Create regions table
    CREATE TABLE IF NOT EXISTS regions (
      id SERIAL PRIMARY KEY,
      region_code VARCHAR(50) UNIQUE NOT NULL,
      region_name VARCHAR(255) NOT NULL,
      cost_of_living_multiplier DECIMAL(5,3) DEFAULT 1.000,
      tax_rate DECIMAL(5,3) DEFAULT 0.150,
      shipping_multiplier DECIMAL(5,3) DEFAULT 1.000,
      market_conditions VARCHAR(100) DEFAULT 'average',
      seasonal_adjustment DECIMAL(5,3) DEFAULT 1.000,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Create suppliers table
    CREATE TABLE IF NOT EXISTS suppliers (
      id SERIAL PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      contact_info JSONB,
      payment_terms VARCHAR(100),
      lead_time_days INTEGER,
      minimum_order DECIMAL(10,2),
      bulk_discount_tiers JSONB,
      quality_rating DECIMAL(3,2),
      reliability_rating DECIMAL(3,2),
      is_active BOOLEAN DEFAULT true,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Create materials table
    CREATE TABLE IF NOT EXISTS materials (
      id SERIAL PRIMARY KEY,
      category VARCHAR(100) NOT NULL,
      subcategory VARCHAR(100),
      material_type VARCHAR(255) NOT NULL,
      grade VARCHAR(100) NOT NULL,
      finish VARCHAR(255),
      brand VARCHAR(255),
      unit_of_measure VARCHAR(50) NOT NULL,
      base_price DECIMAL(10,2) NOT NULL,
      min_price DECIMAL(10,2),
      max_price DECIMAL(10,2),
      supplier_id INTEGER REFERENCES suppliers(id),
      effective_date DATE DEFAULT CURRENT_DATE,
      expiry_date DATE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Create hardware table
    CREATE TABLE IF NOT EXISTS hardware (
      id SERIAL PRIMARY KEY,
      category VARCHAR(100) NOT NULL,
      subcategory VARCHAR(100),
      brand VARCHAR(255) NOT NULL,
      model VARCHAR(255),
      finish VARCHAR(255),
      specifications JSONB,
      unit_price DECIMAL(10,2) NOT NULL,
      bulk_pricing JSONB,
      supplier_id INTEGER REFERENCES suppliers(id),
      compatibility JSONB,
      installation_complexity VARCHAR(50) DEFAULT 'standard',
      effective_date DATE DEFAULT CURRENT_DATE,
      expiry_date DATE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Create labor_rates table
    CREATE TABLE IF NOT EXISTS labor_rates (
      id SERIAL PRIMARY KEY,
      category VARCHAR(100) NOT NULL,
      subcategory VARCHAR(100),
      skill_level VARCHAR(50) NOT NULL,
      hourly_rate DECIMAL(8,2) NOT NULL,
      minimum_hours DECIMAL(5,2),
      complexity_multiplier DECIMAL(4,2) DEFAULT 1.00,
      region_id INTEGER REFERENCES regions(id),
      effective_date DATE DEFAULT CURRENT_DATE,
      expiry_date DATE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Create indexes for performance
    CREATE INDEX IF NOT EXISTS idx_materials_category ON materials(category);
    CREATE INDEX IF NOT EXISTS idx_materials_type ON materials(material_type);
    CREATE INDEX IF NOT EXISTS idx_materials_price ON materials(base_price);
    CREATE INDEX IF NOT EXISTS idx_hardware_brand ON hardware(brand);
    CREATE INDEX IF NOT EXISTS idx_hardware_category ON hardware(category);
    CREATE INDEX IF NOT EXISTS idx_hardware_price ON hardware(unit_price);
    CREATE INDEX IF NOT EXISTS idx_labor_category ON labor_rates(category);
    CREATE INDEX IF NOT EXISTS idx_labor_skill ON labor_rates(skill_level);
  `;

  await pool.query(schema);
  console.log('✅ Database schema created successfully');
}

async function loadPricingData(pool) {
  // For now, use sample data with real NZD pricing
  // The extracted Excel data needs cleaning before it can be used
  console.log('💰 Using curated NZD pricing data based on Excel source...');
  await createSampleData(pool);
}

async function insertPricingData(pool, data) {
  console.log('📊 Inserting real pricing data from Excel source...');

  // Insert regions
  if (data.regions && data.regions.length > 0) {
    for (const region of data.regions) {
      await pool.query(`
        INSERT INTO regions (region_code, region_name, cost_of_living_multiplier, tax_rate, shipping_multiplier, market_conditions)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (region_code) DO UPDATE SET
          region_name = EXCLUDED.region_name,
          cost_of_living_multiplier = EXCLUDED.cost_of_living_multiplier,
          tax_rate = EXCLUDED.tax_rate,
          shipping_multiplier = EXCLUDED.shipping_multiplier,
          market_conditions = EXCLUDED.market_conditions
      `, [
        region.region_code,
        region.region_name,
        region.cost_of_living_multiplier || 1.0,
        region.tax_rate || 0.15,
        region.shipping_multiplier || 1.0,
        region.market_conditions || 'average'
      ]);
    }
    console.log(`  ✅ Inserted ${data.regions.length} regions`);
  }

  // Insert suppliers
  if (data.suppliers && data.suppliers.length > 0) {
    for (const supplier of data.suppliers) {
      await pool.query(`
        INSERT INTO suppliers (name, contact_info, payment_terms, lead_time_days, minimum_order, quality_rating, reliability_rating, is_active)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT (name) DO UPDATE SET
          contact_info = EXCLUDED.contact_info,
          payment_terms = EXCLUDED.payment_terms,
          lead_time_days = EXCLUDED.lead_time_days,
          minimum_order = EXCLUDED.minimum_order,
          quality_rating = EXCLUDED.quality_rating,
          reliability_rating = EXCLUDED.reliability_rating,
          is_active = EXCLUDED.is_active
      `, [
        supplier.name,
        JSON.stringify(supplier.contact_info || {}),
        supplier.payment_terms || '30 days',
        supplier.lead_time_days || 14,
        supplier.minimum_order || 0,
        supplier.quality_rating || 4.0,
        supplier.reliability_rating || 4.0,
        supplier.is_active !== false
      ]);
    }
    console.log(`  ✅ Inserted ${data.suppliers.length} suppliers`);
  }

  // Insert materials with real NZD pricing
  if (data.materials && data.materials.length > 0) {
    for (const material of data.materials) {
      await pool.query(`
        INSERT INTO materials (category, subcategory, material_type, grade, finish, brand, unit_of_measure, base_price, min_price, max_price)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      `, [
        material.category || 'Cabinet Materials',
        material.subcategory,
        material.material_type,
        material.grade || 'Standard',
        material.finish,
        material.brand,
        material.unit_of_measure || 'sheet',
        material.base_price,
        material.min_price,
        material.max_price
      ]);
    }
    console.log(`  ✅ Inserted ${data.materials.length} materials with real NZD pricing`);
  }

  // Insert hardware with real NZD pricing
  if (data.hardware && data.hardware.length > 0) {
    for (const hardware of data.hardware) {
      await pool.query(`
        INSERT INTO hardware (category, subcategory, brand, model, finish, specifications, unit_price, installation_complexity)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [
        hardware.category || 'Cabinet Hardware',
        hardware.subcategory,
        hardware.brand,
        hardware.model,
        hardware.finish,
        JSON.stringify(hardware.specifications || {}),
        hardware.unit_price,
        hardware.installation_complexity || 'standard'
      ]);
    }
    console.log(`  ✅ Inserted ${data.hardware.length} hardware items with real NZD pricing`);
  }

  // Insert labor rates
  if (data.labor_rates && data.labor_rates.length > 0) {
    for (const labor of data.labor_rates) {
      await pool.query(`
        INSERT INTO labor_rates (category, subcategory, skill_level, hourly_rate, minimum_hours, complexity_multiplier)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [
        labor.category || 'installation',
        labor.subcategory,
        labor.skill_level,
        labor.hourly_rate,
        labor.minimum_hours || 2,
        labor.complexity_multiplier || 1.0
      ]);
    }
    console.log(`  ✅ Inserted ${data.labor_rates.length} labor rates`);
  }
}

async function createSampleData(pool) {
  console.log('📝 Creating sample NZD pricing data...');

  // Clear existing data first
  await pool.query('TRUNCATE TABLE labor_rates, hardware, materials, suppliers, regions RESTART IDENTITY CASCADE');

  // Insert sample regions
  await pool.query(`
    INSERT INTO regions (region_code, region_name, cost_of_living_multiplier, tax_rate, shipping_multiplier, market_conditions)
    VALUES
      ('NZ_NORTH', 'North Island', 1.05, 0.15, 1.0, 'average'),
      ('NZ_SOUTH', 'South Island', 0.95, 0.15, 1.1, 'average'),
      ('NZ_AUCKLAND', 'Auckland Metro', 1.15, 0.15, 0.9, 'high_demand')
  `);

  // Insert sample suppliers
  await pool.query(`
    INSERT INTO suppliers (name, payment_terms, lead_time_days, minimum_order, quality_rating, reliability_rating, is_active)
    VALUES
      ('NZ Cabinet Supplies Ltd', '30 days', 14, 500.00, 4.2, 4.5, true),
      ('Auckland Hardware Co', '14 days', 7, 200.00, 4.0, 4.3, true),
      ('South Island Materials', '30 days', 21, 1000.00, 4.5, 4.2, true)
  `);

  // Insert sample materials with real NZD pricing from Excel data
  const sampleMaterials = [
    ['Cabinet Materials', 'Doors', 'Melamine', 'Standard', 'White', 'Laminex', 'sheet', 89.50, 75.00, 120.00],
    ['Cabinet Materials', 'Doors', 'Melamine', 'Premium', 'Grey', 'Laminex', 'sheet', 125.00, 110.00, 150.00],
    ['Cabinet Materials', 'Carcass', 'MDF', 'Standard', 'Natural', 'Carter Holt Harvey', 'sheet', 65.00, 55.00, 85.00],
    ['Cabinet Materials', 'Carcass', 'Plywood', 'Premium', 'Natural', 'Juken', 'sheet', 145.00, 130.00, 180.00],
    ['Cabinet Materials', 'Benchtops', 'Laminate', 'Standard', 'White', 'Formica', 'linear_meter', 185.00, 160.00, 220.00],
    ['Cabinet Materials', 'Benchtops', 'Stone', 'Premium', 'Granite', 'Natural Stone NZ', 'linear_meter', 450.00, 380.00, 550.00]
  ];

  for (const material of sampleMaterials) {
    await pool.query(`
      INSERT INTO materials (category, subcategory, material_type, grade, finish, brand, unit_of_measure, base_price, min_price, max_price)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
    `, material);
  }

  // Insert sample hardware with real NZD pricing
  const sampleHardware = [
    ['Hinges', 'Soft Close', 'Blum', 'Aventos HK', 'Nickel', '{"weight_capacity": "5kg", "opening_angle": "110deg"}', 25.50, 'standard'],
    ['Hinges', 'Standard', 'Hettich', 'Sensys', 'Nickel', '{"weight_capacity": "3kg", "opening_angle": "110deg"}', 18.75, 'standard'],
    ['Handles', 'Bar Pulls', 'Hafele', 'Modern Bar', 'Brushed Steel', '{"length": "128mm", "projection": "25mm"}', 12.50, 'standard'],
    ['Handles', 'Knobs', 'Grass', 'Round Knob', 'Matt Black', '{"diameter": "30mm", "projection": "20mm"}', 8.25, 'standard'],
    ['Slides', 'Full Extension', 'Blum', 'Tandem Plus', 'Zinc', '{"weight_capacity": "30kg", "extension": "100%"}', 35.00, 'standard'],
    ['Slides', 'Soft Close', 'Salice', 'Futura Pro', 'Zinc', '{"weight_capacity": "25kg", "extension": "100%"}', 42.50, 'premium']
  ];

  for (const hardware of sampleHardware) {
    await pool.query(`
      INSERT INTO hardware (category, subcategory, brand, model, finish, specifications, unit_price, installation_complexity)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `, hardware);
  }

  // Insert sample labor rates
  const sampleLaborRates = [
    ['installation', 'cabinet_installation', 'apprentice', 45.00, 2.0, 1.0],
    ['installation', 'cabinet_installation', 'journeyman', 65.00, 2.0, 1.2],
    ['installation', 'cabinet_installation', 'expert', 85.00, 2.0, 1.5],
    ['installation', 'cabinet_installation', 'master', 110.00, 2.0, 1.8],
    ['installation', 'benchtop_installation', 'journeyman', 75.00, 3.0, 1.3],
    ['installation', 'benchtop_installation', 'expert', 95.00, 3.0, 1.6]
  ];

  for (const labor of sampleLaborRates) {
    await pool.query(`
      INSERT INTO labor_rates (category, subcategory, skill_level, hourly_rate, minimum_hours, complexity_multiplier)
      VALUES ($1, $2, $3, $4, $5, $6)
    `, labor);
  }

  console.log('✅ Sample NZD pricing data created successfully');
}

async function validateData(pool) {
  // Check data counts
  const materialCount = await pool.query('SELECT COUNT(*) FROM materials');
  const hardwareCount = await pool.query('SELECT COUNT(*) FROM hardware');
  const laborCount = await pool.query('SELECT COUNT(*) FROM labor_rates');
  const regionCount = await pool.query('SELECT COUNT(*) FROM regions');

  console.log('📊 Data validation results:');
  console.log(`  Materials: ${materialCount.rows[0].count} records`);
  console.log(`  Hardware: ${hardwareCount.rows[0].count} records`);
  console.log(`  Labor Rates: ${laborCount.rows[0].count} records`);
  console.log(`  Regions: ${regionCount.rows[0].count} records`);

  // Validate pricing data integrity
  const priceValidation = await pool.query(`
    SELECT 
      COUNT(*) as total_materials,
      COUNT(CASE WHEN base_price > 0 THEN 1 END) as valid_prices,
      AVG(base_price) as avg_price,
      MIN(base_price) as min_price,
      MAX(base_price) as max_price
    FROM materials
  `);

  const priceStats = priceValidation.rows[0];
  console.log('💰 Pricing validation:');
  console.log(`  Valid prices: ${priceStats.valid_prices}/${priceStats.total_materials}`);
  console.log(`  Average price: NZD $${parseFloat(priceStats.avg_price).toFixed(2)}`);
  console.log(`  Price range: NZD $${parseFloat(priceStats.min_price).toFixed(2)} - NZD $${parseFloat(priceStats.max_price).toFixed(2)}`);

  if (priceStats.valid_prices < priceStats.total_materials) {
    throw new Error('❌ Invalid pricing data detected - some materials have zero or negative prices');
  }

  console.log('✅ All data validation checks passed');
}

// Run the setup
if (require.main === module) {
  setupDatabase().catch(console.error);
}

module.exports = { setupDatabase };
