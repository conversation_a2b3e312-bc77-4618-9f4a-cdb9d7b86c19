{"name": "blackveil-design-mind-server", "version": "1.0.0", "description": "Backend server for Blackveil Design Mind AI analysis system", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "dev:cluster": "tsx src/cluster.ts", "build": "tsc", "start": "node dist/index.js", "start:cluster": "node dist/cluster.js", "test": "jest", "lint": "eslint src --ext .ts", "setup:system": "./scripts/setup-system-deps.sh", "validate:production": "node scripts/validate-production.js", "clean": "rm -rf dist/ uploads/upload_* temp/optimized_* logs/*.log data/ab_tests/tests/test_* data/ab_tests/results/result_*", "clean:uploads": "find uploads/ -name 'upload_*' -delete 2>/dev/null || true", "clean:temp": "find temp/ -name 'optimized_*' -delete 2>/dev/null || true", "clean:logs": "find logs/ -name '*.log' -delete 2>/dev/null || true", "clean:all": "npm run clean && npm run clean:uploads && npm run clean:temp && npm run clean:logs", "prestart": "npm run validate:production", "docker:build": "docker build -f Dockerfile.production -t blackveil-design-mind .", "docker:run": "docker-compose -f docker-compose.production.yml up -d", "docker:stop": "docker-compose -f docker-compose.production.yml down", "deploy:check": "npm run lint && npm run build && npm run validate:production", "scalability:setup": "cd ../infrastructure && ./scripts/setup-scalability.sh", "scalability:start": "cd ../infrastructure && docker-compose -f docker-compose.scalability.yml up -d", "scalability:stop": "cd ../infrastructure && docker-compose -f docker-compose.scalability.yml down", "scalability:logs": "cd ../infrastructure && docker-compose -f docker-compose.scalability.yml logs -f", "scalability:status": "cd ../infrastructure && docker-compose -f docker-compose.scalability.yml ps", "redis:cluster:init": "cd ../infrastructure && ./scripts/init-redis-cluster.sh", "postgres:migrate": "cd ../infrastructure && ./scripts/migrate-to-postgres.sh", "load:test": "artillery run ../infrastructure/load-testing/load-test.yml", "monitoring:setup": "cd ../infrastructure && ./scripts/setup-monitoring.sh"}, "keywords": ["kitchen-design", "ai-analysis", "openai", "gpt-4o", "cabinet-analysis"], "author": "Blackveil <<EMAIL>>", "license": "PROPRIETARY", "dependencies": {"@azure/ai-form-recognizer": "^5.1.0", "@azure/identity": "^4.10.0", "@tensorflow/tfjs-node": "^4.22.0", "bcryptjs": "^2.4.3", "better-sqlite3": "^11.5.0", "bull": "^4.16.5", "cluster": "^0.7.7", "cors": "^2.8.5", "currency.js": "^2.0.4", "decimal.js": "^10.4.3", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "ml-distance": "^4.0.1", "multer": "^1.4.5-lts.1", "node-tesseract-ocr": "^2.2.1", "openai": "^4.24.1", "pdf-parse": "^1.1.1", "pdf-poppler": "^0.2.1", "pdf-table-extractor": "^1.0.3", "pdf2pic": "^3.2.0", "pg": "^8.16.0", "redis": "^4.6.11", "sharp": "^0.33.1", "socket.io": "^4.7.4", "socket.io-client": "^4.8.1", "table-extractor": "^0.0.1", "tesseract.js": "^6.0.1", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/better-sqlite3": "^7.6.11", "@types/bull": "^4.10.4", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.6", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/pdf-parse": "^1.1.5", "@types/pg": "^8.15.2", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}