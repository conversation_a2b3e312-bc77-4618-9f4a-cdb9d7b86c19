# Blackveil Design Mind Backend - Production Dockerfile
# ==================================================
# Multi-stage build for production-grade PDF processing

# Stage 1: System Dependencies
FROM node:18-bullseye as system-deps

# Install system-level dependencies for PDF processing
RUN apt-get update && apt-get install -y \
    # PDF processing tools
    poppler-utils \
    # OCR tools
    tesseract-ocr \
    tesseract-ocr-eng \
    # Image processing libraries
    libvips-dev \
    # Build tools
    build-essential \
    python3-dev \
    # Additional utilities
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Verify installations
RUN pdftoppm -v && tesseract --version && vips --version

# Stage 2: Node.js Dependencies
FROM system-deps as node-deps

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install Node.js dependencies
RUN npm ci --only=production && npm cache clean --force

# Stage 3: Application Build
FROM node-deps as app-build

# Copy TypeScript configuration
COPY tsconfig.json ./

# Copy source code
COPY src/ ./src/

# Install dev dependencies for build
RUN npm ci

# Build the application
RUN npm run build

# Stage 4: Production Runtime
FROM system-deps as production

# Create app user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Copy built application
COPY --from=app-build /app/dist ./dist
COPY --from=node-deps /app/node_modules ./node_modules
COPY package*.json ./

# Copy additional required files for scalability
COPY scripts/ ./scripts/
COPY data/ ./data/

# Create necessary directories
RUN mkdir -p uploads temp logs && \
    chown -R appuser:appuser /app

# Set environment variables for scalability
ENV NODE_ENV=production
ENV PORT=3001
ENV UPLOAD_DIR=/app/uploads
ENV ENABLE_CLUSTERING=true
ENV MAX_WORKERS=2
ENV REDIS_NODE_1_HOST=redis-node-1
ENV REDIS_NODE_2_HOST=redis-node-2
ENV REDIS_NODE_3_HOST=redis-node-3
ENV POSTGRES_HOST=pgbouncer
ENV POSTGRES_PORT=5432

# Install dumb-init for proper signal handling
RUN apt-get update && apt-get install -y dumb-init && rm -rf /var/lib/apt/lists/*

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3001/api/health || exit 1

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 3001

# Use dumb-init for proper signal handling and start with clustering
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/cluster.js"]

# Labels for metadata
LABEL maintainer="Blackveil <<EMAIL>>"
LABEL description="Blackveil Design Mind Backend Server with PDF Processing"
LABEL version="1.0.0"
