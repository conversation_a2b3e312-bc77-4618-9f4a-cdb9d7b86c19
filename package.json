{"name": "blackveil-design-mind", "description": "Blackveil Design Mind - AI-powered kitchen design analysis platform", "version": "0.1.0", "private": true, "type": "module", "author": "Blackveil <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/MadaBurns/blackveil-design-mind.git"}, "keywords": ["kitchen-design", "ai-analysis", "cabinet-analysis", "design-automation", "react", "typescript", "vite"], "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "test": "playwright test", "test:headed": "playwright test --headed", "test:ui": "playwright test --ui", "test:api": "playwright test tests/api", "test:performance-api": "playwright test tests/api/performance-metrics.spec.ts --reporter=list", "test:frontend": "playwright test tests/frontend", "test:integration": "playwright test tests/integration", "test:azure": "playwright test tests/integration/azure-openai.spec.ts", "test:debug": "playwright test --debug", "test:report": "playwright show-report", "test:monitoring": "playwright test tests/integration/enhanced-monitoring-demo.spec.ts", "test:performance": "playwright test --reporter=json --output-dir=test-results/performance", "test:performance-monitoring": "playwright test tests/performance-monitoring.spec.ts", "test:with-monitoring": "FEATURE_VERSION=test-$(date +%s) playwright test --reporter=./tests/utils/performance-reporter.ts", "test:compatibility": "playwright test --project=chromium --project=firefox --project=webkit", "test:scalability": "playwright test tests/scalability/", "test:visual-experience": "playwright test tests/visual-experience-system.spec.ts", "scalability:setup": "cd infrastructure && ./scripts/setup-scalability.sh", "scalability:start": "cd infrastructure && docker-compose -f docker-compose.scalability.yml up -d", "scalability:stop": "cd infrastructure && docker-compose -f docker-compose.scalability.yml down", "scalability:logs": "cd infrastructure && docker-compose -f docker-compose.scalability.yml logs -f", "scalability:status": "cd infrastructure && docker-compose -f docker-compose.scalability.yml ps", "redis:cluster:init": "cd infrastructure && ./scripts/init-redis-cluster.sh", "postgres:migrate": "cd infrastructure && ./scripts/migrate-to-postgres.sh", "load:test": "cd infrastructure && artillery run load-testing/load-test.yml", "monitoring:setup": "cd infrastructure && ./scripts/setup-monitoring.sh"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@react-spring/three": "^9.7.5", "@use-gesture/react": "^10.3.1", "@tanstack/react-query": "^5.56.2", "@types/peerjs": "^0.0.30", "@types/socket.io-client": "^1.4.36", "@types/three": "^0.168.0", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "diff2html": "^3.4.51", "embla-carousel-react": "^8.3.0", "html2canvas": "^1.4.1", "input-otp": "^1.2.4", "jspdf": "^3.0.1", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "peerjs": "^1.5.4", "puppeteer": "^24.9.0", "react": "^18.3.1", "react-avatar-editor": "^13.0.2", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.53.0", "react-hotkeys-hook": "^5.1.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "reactflow": "^11.11.4", "recharts": "^2.12.7", "socket.io-client": "^4.8.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "three": "^0.168.0", "uuid": "^11.1.0", "vaul": "^0.9.3", "workbox-webpack-plugin": "^7.3.0", "xlsx": "^0.18.5", "y-websocket": "^3.0.0", "yjs": "^13.6.27", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@playwright/test": "^1.52.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/puppeteer": "^5.4.7", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "leva": "^0.9.35", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}