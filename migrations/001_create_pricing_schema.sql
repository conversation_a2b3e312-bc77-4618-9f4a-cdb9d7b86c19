-- PostgreSQL Pricing Database Schema
-- Generated from LM3.20.xlsm extraction

-- Create database (run separately if needed)
-- CREATE DATABASE cabinet_pricing;

-- Materials pricing table
CREATE TABLE IF NOT EXISTS materials (
    id SERIAL PRIMARY KEY,
    category VARCHAR(50) NOT NULL,
    subcategory VARCHAR(50),
    material_type VARCHAR(100) NOT NULL,
    grade VARCHAR(20) NOT NULL DEFAULT 'standard',
    finish VARCHAR(100),
    brand VARCHAR(100),
    unit_of_measure VARCHAR(20) NOT NULL DEFAULT 'each',
    base_price DECIMAL(10,2) NOT NULL,
    min_price DECIMAL(10,2),
    max_price DECIMAL(10,2),
    supplier_id INTEGER,
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiry_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Hardware pricing table
CREATE TABLE IF NOT EXISTS hardware (
    id SERIAL PRIMARY KEY,
    category VARCHAR(50) NOT NULL,
    subcategory VARCHAR(50),
    brand VARCHAR(100) NOT NULL,
    model VARCHAR(100),
    finish VARCHAR(50),
    specifications JSONB,
    unit_price DECIMAL(10,2) NOT NULL,
    bulk_pricing JSONB,
    supplier_id INTEGER,
    compatibility JSONB,
    installation_complexity VARCHAR(20) DEFAULT 'medium',
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiry_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Labor rates table
CREATE TABLE IF NOT EXISTS labor_rates (
    id SERIAL PRIMARY KEY,
    category VARCHAR(50) NOT NULL,
    subcategory VARCHAR(50),
    skill_level VARCHAR(20) NOT NULL DEFAULT 'journeyman',
    hourly_rate DECIMAL(8,2) NOT NULL,
    minimum_hours DECIMAL(4,2),
    complexity_multiplier DECIMAL(3,2) DEFAULT 1.0,
    region_id INTEGER,
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiry_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Regional factors table
CREATE TABLE IF NOT EXISTS regions (
    id SERIAL PRIMARY KEY,
    region_code VARCHAR(20) UNIQUE NOT NULL,
    region_name VARCHAR(100) NOT NULL,
    cost_of_living_multiplier DECIMAL(3,2) NOT NULL DEFAULT 1.0,
    tax_rate DECIMAL(4,4) NOT NULL DEFAULT 0.0,
    shipping_multiplier DECIMAL(3,2) DEFAULT 1.0,
    market_conditions VARCHAR(20) DEFAULT 'average',
    seasonal_adjustment DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Suppliers table
CREATE TABLE IF NOT EXISTS suppliers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    contact_info JSONB,
    payment_terms VARCHAR(100),
    lead_time_days INTEGER,
    minimum_order DECIMAL(10,2),
    bulk_discount_tiers JSONB,
    quality_rating DECIMAL(2,1),
    reliability_rating DECIMAL(2,1),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_materials_category ON materials(category);
CREATE INDEX IF NOT EXISTS idx_materials_type_grade ON materials(material_type, grade);
CREATE INDEX IF NOT EXISTS idx_hardware_category_brand ON hardware(category, brand);
CREATE INDEX IF NOT EXISTS idx_labor_category_region ON labor_rates(category, region_id);
CREATE INDEX IF NOT EXISTS idx_regions_code ON regions(region_code);
CREATE INDEX IF NOT EXISTS idx_suppliers_active ON suppliers(is_active);

-- Foreign key constraints
ALTER TABLE materials ADD CONSTRAINT fk_materials_supplier
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id);
ALTER TABLE hardware ADD CONSTRAINT fk_hardware_supplier
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id);
ALTER TABLE labor_rates ADD CONSTRAINT fk_labor_region
    FOREIGN KEY (region_id) REFERENCES regions(id);
