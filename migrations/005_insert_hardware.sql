-- Insert data into hardware
INSERT INTO hardware (category, subcategory, brand, model, finish, specifications, unit_price, bulk_pricing, supplier_id, compatibility, installation_complexity, effective_date) VALUES
  ('Handles', 'handle', 'LM Kitchen Supplies', 'Standard Handle', 'standard', '{}', 13, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Handles', 'handle', 'LM Kitchen Supplies', 'Premium Handle', 'standard', '{}', 16, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Handles', 'handle', 'LM Kitchen Supplies', 'Deluxe Handle', 'standard', '{}', 25, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Handles', 'handle', 'LM Kitchen Supplies', 'Designer Handle', 'standard', '{}', 50, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Handles', 'handle', 'LM Kitchen Supplies', 'Sharknose', 'standard', '{}', 30, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Hinges', 'hinge', 'LM Kitchen Supplies', '107 degree', 'standard', '{}', 107, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Hinges', 'hinge', 'LM Kitchen Supplies', '155 degree', 'standard', '{}', 155, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Hinges', 'hinge', 'LM Kitchen Supplies', '155 THIN', 'standard', '{}', 155, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Drawers', 'drawer_box', 'LM Kitchen Supplies', 'Tandembox', 'standard', '{}', 56.66, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Drawers', 'drawer_box', 'LM Kitchen Supplies', 'Legrabox', 'standard', '{}', 121.67, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Drawers', 'drawer_box', 'LM Kitchen Supplies', 'Merivobox', 'standard', '{}', 80.52, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Drawers', 'drawer', 'LM Kitchen Supplies', 'Multitech', 'standard', '{}', 25.5, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Drawers', 'drawer', 'LM Kitchen Supplies', 'Alto', 'standard', '{}', 34.95, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Drawers', 'drawer', 'LM Kitchen Supplies', 'Tekform', 'standard', '{}', 36.33, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Internal_Drawers', 'drawer_box', 'LM Kitchen Supplies', 'Tandembox', 'standard', '{}', 86, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Internal_Drawers', 'drawer_box', 'LM Kitchen Supplies', 'Legrabox', 'standard', '{}', 133.84, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Internal_Drawers', 'drawer_box', 'LM Kitchen Supplies', 'Merivobox', 'standard', '{}', 100.35, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Internal_Drawers', 'drawer', 'LM Kitchen Supplies', 'Multitech', 'standard', '{}', 28.05, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Internal_Drawers', 'drawer', 'LM Kitchen Supplies', 'Alto', 'standard', '{}', 41.94, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Internal_Drawers', 'drawer', 'LM Kitchen Supplies', 'Tekform', 'standard', '{}', 61, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Accessories', 'standard', 'LM Kitchen Supplies', 'Bifold', 'standard', '{}', 45, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Accessories', 'standard', 'LM Kitchen Supplies', 'Liftup', 'standard', '{}', 20, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Accessories', 'standard', 'LM Kitchen Supplies', 'Hanging Rail', 'standard', '{}', 22, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Accessories', 'standard', 'LM Kitchen Supplies', 'Mitred Boxed Ends', 'standard', '{}', 91, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31'),
  ('Accessories', 'standard', 'LM Kitchen Supplies', 'Single Sided Boxed End', 'standard', '{}', 67, NULL, 1, '{"cabinet_types":["all"]}', 'medium', '2025-05-31')
ON CONFLICT DO NOTHING;
