-- Quote Templates System Migration
-- Adds customizable quote templates with versioning and inheritance

-- Customer segments for template targeting
CREATE TABLE IF NOT EXISTS customer_segments (
    id SERIAL PRIMARY KEY,
    segment_code VARCHAR(50) UNIQUE NOT NULL,
    segment_name VARCHAR(100) NOT NULL,
    description TEXT,
    target_market VARCHAR(50), -- 'residential', 'commercial', 'luxury', 'budget'
    pricing_tier VARCHAR(20) DEFAULT 'standard', -- 'budget', 'standard', 'premium', 'luxury'
    default_markup_percentage DECIMAL(5,2) DEFAULT 0.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Quote templates with versioning and inheritance
CREATE TABLE IF NOT EXISTS quote_templates (
    id SERIAL PRIMARY KEY,
    template_code VARCHAR(50) UNIQUE NOT NULL,
    template_name VARCHAR(100) NOT NULL,
    description TEXT,
    version INTEGER NOT NULL DEFAULT 1,
    parent_template_id INTEGER REFERENCES quote_templates(id),
    customer_segment_id INTEGER REFERENCES customer_segments(id),
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    
    -- Template configuration as JSONB
    template_config JSONB NOT NULL DEFAULT '{}',
    
    -- Template sections configuration
    sections_config JSONB NOT NULL DEFAULT '{
        "header": {"enabled": true, "customizable": true},
        "branding": {"enabled": true, "customizable": true},
        "pricing": {"enabled": true, "customizable": false},
        "materials": {"enabled": true, "customizable": true},
        "terms": {"enabled": true, "customizable": true},
        "delivery": {"enabled": true, "customizable": true}
    }',
    
    -- Styling and layout configuration
    styling_config JSONB DEFAULT '{
        "colors": {"primary": "#2563eb", "secondary": "#64748b", "accent": "#f59e0b"},
        "fonts": {"heading": "Inter", "body": "Inter", "accent": "Inter"},
        "layout": {"margins": 20, "spacing": 10, "columns": 1}
    }',
    
    created_by VARCHAR(255), -- Link to SQLite users
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Template inheritance tracking
CREATE TABLE IF NOT EXISTS template_inheritance (
    id SERIAL PRIMARY KEY,
    child_template_id INTEGER NOT NULL REFERENCES quote_templates(id) ON DELETE CASCADE,
    parent_template_id INTEGER NOT NULL REFERENCES quote_templates(id) ON DELETE CASCADE,
    inheritance_type VARCHAR(50) NOT NULL, -- 'full', 'partial', 'styling_only', 'sections_only'
    inherited_sections JSONB, -- Which sections are inherited
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(child_template_id, parent_template_id)
);

-- Template usage tracking for analytics
CREATE TABLE IF NOT EXISTS template_usage (
    id SERIAL PRIMARY KEY,
    template_id INTEGER NOT NULL REFERENCES quote_templates(id) ON DELETE CASCADE,
    quote_id VARCHAR(255), -- Link to generated quotes
    usage_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id VARCHAR(255), -- Link to SQLite users
    success BOOLEAN DEFAULT true,
    performance_metrics JSONB -- Generation time, file size, etc.
);

-- Insert default customer segments
INSERT INTO customer_segments (segment_code, segment_name, description, target_market, pricing_tier, default_markup_percentage) VALUES
('RESIDENTIAL_BUDGET', 'Budget Residential', 'Cost-conscious homeowners seeking quality at affordable prices', 'residential', 'budget', 15.0),
('RESIDENTIAL_STANDARD', 'Standard Residential', 'Typical homeowners looking for quality and value balance', 'residential', 'standard', 25.0),
('RESIDENTIAL_LUXURY', 'Luxury Residential', 'High-end homeowners seeking premium materials and finishes', 'residential', 'luxury', 40.0),
('COMMERCIAL_SMALL', 'Small Commercial', 'Small businesses and cafes with budget constraints', 'commercial', 'standard', 20.0),
('COMMERCIAL_ENTERPRISE', 'Enterprise Commercial', 'Large commercial projects with premium requirements', 'commercial', 'premium', 35.0),
('BUILDER_VOLUME', 'Volume Builder', 'High-volume builders needing competitive pricing', 'commercial', 'budget', 12.0);

-- Insert default quote templates
INSERT INTO quote_templates (template_code, template_name, description, customer_segment_id, is_default, template_config, sections_config) VALUES
('DEFAULT_BASIC', 'Basic Quote Template', 'Simple, clean template for budget-conscious customers', 
 (SELECT id FROM customer_segments WHERE segment_code = 'RESIDENTIAL_BUDGET'), true,
 '{"format": "basic", "include_images": false, "include_alternatives": true, "include_warranty": false}',
 '{"header": {"enabled": true, "show_logo": true}, "pricing": {"show_breakdown": false}, "terms": {"simplified": true}}'),

('DEFAULT_STANDARD', 'Standard Quote Template', 'Comprehensive template for typical residential projects',
 (SELECT id FROM customer_segments WHERE segment_code = 'RESIDENTIAL_STANDARD'), true,
 '{"format": "detailed", "include_images": true, "include_alternatives": true, "include_warranty": true}',
 '{"header": {"enabled": true, "show_logo": true}, "pricing": {"show_breakdown": true}, "terms": {"detailed": true}}'),

('DEFAULT_LUXURY', 'Luxury Quote Template', 'Premium template with full customization options',
 (SELECT id FROM customer_segments WHERE segment_code = 'RESIDENTIAL_LUXURY'), true,
 '{"format": "professional", "include_images": true, "include_alternatives": true, "include_warranty": true, "include_3d_renders": true}',
 '{"header": {"enabled": true, "show_logo": true, "show_certifications": true}, "pricing": {"show_breakdown": true, "show_alternatives": true}, "terms": {"comprehensive": true}}'),

('COMMERCIAL_STANDARD', 'Commercial Quote Template', 'Professional template for commercial projects',
 (SELECT id FROM customer_segments WHERE segment_code = 'COMMERCIAL_ENTERPRISE'), true,
 '{"format": "commercial", "include_images": true, "include_alternatives": false, "include_warranty": true, "include_compliance": true}',
 '{"header": {"enabled": true, "show_logo": true, "show_certifications": true}, "pricing": {"show_breakdown": true, "show_labor_breakdown": true}, "terms": {"commercial": true}}');

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_quote_templates_segment ON quote_templates(customer_segment_id);
CREATE INDEX IF NOT EXISTS idx_quote_templates_active ON quote_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_quote_templates_default ON quote_templates(is_default);
CREATE INDEX IF NOT EXISTS idx_template_usage_template ON template_usage(template_id);
CREATE INDEX IF NOT EXISTS idx_template_usage_date ON template_usage(usage_date);
CREATE INDEX IF NOT EXISTS idx_template_inheritance_child ON template_inheritance(child_template_id);
CREATE INDEX IF NOT EXISTS idx_template_inheritance_parent ON template_inheritance(parent_template_id);

-- Create updated_at trigger for quote_templates
CREATE OR REPLACE FUNCTION update_quote_templates_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_quote_templates_updated_at
    BEFORE UPDATE ON quote_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_quote_templates_updated_at();

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create updated_at trigger for customer_segments
CREATE TRIGGER update_customer_segments_updated_at
    BEFORE UPDATE ON customer_segments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
