#!/bin/bash
# Complete PostgreSQL Migration Script for LM3.20 Pricing Database
# Generated: 2025-05-31T05:25:26.199Z

echo "🚀 Starting PostgreSQL pricing database migration..."

# Set database connection parameters for Docker setup
DB_NAME=${PRICING_DB_NAME:-blackveil_design_mind}
DB_USER=${PRICING_DB_USER:-postgres}
DB_HOST=${PRICING_DB_HOST:-localhost}
DB_PORT=${PRICING_DB_PORT:-5433}
PGPASSWORD=${PRICING_DB_PASSWORD:-blackveil_dev_2024}
CONTAINER_NAME=${POSTGRES_CONTAINER:-blackveil-postgres-dev}

# Export password for PostgreSQL commands
export PGPASSWORD

# Check if PostgreSQL container is running
echo "🔍 Checking PostgreSQL container..."
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo "❌ PostgreSQL container is not running. Please start the database services first."
    echo "💡 Run: docker-compose -f docker-compose.dev.yml up -d"
    exit 1
fi

# Wait for PostgreSQL to be ready inside container
echo "⏳ Waiting for PostgreSQL to be ready..."
for i in {1..30}; do
    if docker exec $CONTAINER_NAME pg_isready -U $DB_USER -d $DB_NAME > /dev/null 2>&1; then
        echo "✅ PostgreSQL is ready!"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ PostgreSQL failed to start within 30 seconds"
        exit 1
    fi
    sleep 1
done

echo "📊 Database: $DB_NAME"
echo "👤 User: $DB_USER"
echo "🌐 Host: $DB_HOST:$DB_PORT"

# Database is already created by Docker Compose
echo "✅ Using existing database: $DB_NAME"

# Run migration scripts in order using Docker exec
echo "📋 Running schema creation..."
docker exec -i $CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < 001_create_pricing_schema.sql

echo "🌍 Inserting regions..."
docker exec -i $CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < 002_insert_regions.sql

echo "🏢 Inserting suppliers..."
docker exec -i $CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < 003_insert_suppliers.sql

if [ -f "004_insert_materials.sql" ]; then
    echo "🪵 Inserting materials..."
    docker exec -i $CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < 004_insert_materials.sql
fi

if [ -f "005_insert_hardware.sql" ]; then
    echo "🔧 Inserting hardware..."
    docker exec -i $CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < 005_insert_hardware.sql
fi

if [ -f "006_insert_labor_rates.sql" ]; then
    echo "👷 Inserting labor rates..."
    docker exec -i $CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < 006_insert_labor_rates.sql
fi

# Verify migration
echo "✅ Verifying migration..."
docker exec $CONTAINER_NAME psql -U $DB_USER -d $DB_NAME -c "
SELECT
    'materials' as table_name, COUNT(*) as record_count FROM materials
UNION ALL
SELECT
    'hardware' as table_name, COUNT(*) as record_count FROM hardware
UNION ALL
SELECT
    'labor_rates' as table_name, COUNT(*) as record_count FROM labor_rates
UNION ALL
SELECT
    'regions' as table_name, COUNT(*) as record_count FROM regions
UNION ALL
SELECT
    'suppliers' as table_name, COUNT(*) as record_count FROM suppliers;
"

echo "🎉 Migration completed successfully!"
echo "📊 Database ready for quotation system integration"
