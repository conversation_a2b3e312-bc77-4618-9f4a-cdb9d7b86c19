{"worksheets": [{"name": "Master", "range": "A1:AM108", "totalRows": 108, "totalCols": 39, "dataRows": 108, "recordCount": 99, "headers": [null, "Lineal Mtr Calculator 3.2 30/04/25", "Pricing Date:", "XXXXXX", "Job Number:", "XXXXXX", "Priced By:", "Bronwyn"], "sampleData": [[null, "Lineal Mtr Calculator 3.2 30/04/25", "Pricing Date:", "XXXXXX", "Job Number:", "XXXXXX", "Priced By:", "Bronwyn"], [], [null, null, "Height", "UNIT Depth", "Panel Depth"], [null, "Base Units", 880, 580, 580], [null, "Tall Units", 2300, 580, 690]], "columns": [{"name": "Pricing Date:", "sampleValues": ["Height", 880, 2300], "uniqueValues": 18, "nullCount": 44}, {"name": "XXXXXX", "sampleValues": ["UNIT Depth", 580, 580], "uniqueValues": 16, "nullCount": 47}, {"name": "Job Number:", "sampleValues": ["Panel Depth", 580, 690], "uniqueValues": 21, "nullCount": 12}], "dataTypes": {"Pricing Date:": "text", "XXXXXX": "text", "Job Number:": "text"}, "category": "unknown"}, {"name": "Master Unit Width Calc", "range": "A1:W24", "totalRows": 24, "totalCols": 23, "dataRows": 24, "recordCount": 23, "headers": ["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], "sampleData": [["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], ["Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1"], ["Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2"], ["Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3"], ["Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4"]], "columns": [{"name": "Unit Type", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_1", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_2", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_3", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_4", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_5", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_6", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_7", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}], "dataTypes": {"Unit Type": "text", "Unit Type_1": "text", "Unit Type_2": "text", "Unit Type_3": "text", "Unit Type_4": "text", "Unit Type_5": "text", "Unit Type_6": "text", "Unit Type_7": "text"}, "category": "unknown"}, {"name": "Unit Width Calc", "range": "A1:W24", "totalRows": 24, "totalCols": 23, "dataRows": 24, "recordCount": 23, "headers": ["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], "sampleData": [["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], ["Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1"], ["Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2"], ["Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3"], ["Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4"]], "columns": [{"name": "Unit Type", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_1", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_2", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_3", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_4", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_5", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_6", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_7", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}], "dataTypes": {"Unit Type": "text", "Unit Type_1": "text", "Unit Type_2": "text", "Unit Type_3": "text", "Unit Type_4": "text", "Unit Type_5": "text", "Unit Type_6": "text", "Unit Type_7": "text"}, "category": "unknown"}, {"name": "Unit Width Calc (2)", "range": "A1:W24", "totalRows": 24, "totalCols": 23, "dataRows": 24, "recordCount": 23, "headers": ["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], "sampleData": [["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], ["Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1"], ["Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2"], ["Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3"], ["Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4"]], "columns": [{"name": "Unit Type", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_1", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_2", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_3", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_4", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_5", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_6", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_7", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}], "dataTypes": {"Unit Type": "text", "Unit Type_1": "text", "Unit Type_2": "text", "Unit Type_3": "text", "Unit Type_4": "text", "Unit Type_5": "text", "Unit Type_6": "text", "Unit Type_7": "text"}, "category": "unknown"}, {"name": "Unit Width Calc (3)", "range": "A1:W24", "totalRows": 24, "totalCols": 23, "dataRows": 24, "recordCount": 23, "headers": ["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], "sampleData": [["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], ["Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1"], ["Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2"], ["Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3"], ["Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4"]], "columns": [{"name": "Unit Type", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_1", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_2", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_3", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_4", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_5", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_6", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_7", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}], "dataTypes": {"Unit Type": "text", "Unit Type_1": "text", "Unit Type_2": "text", "Unit Type_3": "text", "Unit Type_4": "text", "Unit Type_5": "text", "Unit Type_6": "text", "Unit Type_7": "text"}, "category": "unknown"}, {"name": "Unit Width Calc (4)", "range": "A1:W24", "totalRows": 24, "totalCols": 23, "dataRows": 24, "recordCount": 23, "headers": ["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], "sampleData": [["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], ["Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1"], ["Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2"], ["Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3"], ["Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4"]], "columns": [{"name": "Unit Type", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_1", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_2", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_3", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_4", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_5", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_6", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_7", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}], "dataTypes": {"Unit Type": "text", "Unit Type_1": "text", "Unit Type_2": "text", "Unit Type_3": "text", "Unit Type_4": "text", "Unit Type_5": "text", "Unit Type_6": "text", "Unit Type_7": "text"}, "category": "unknown"}, {"name": "Unit Width Calc (5)", "range": "A1:W24", "totalRows": 24, "totalCols": 23, "dataRows": 24, "recordCount": 23, "headers": ["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], "sampleData": [["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], ["Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1"], ["Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2"], ["Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3"], ["Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4"]], "columns": [{"name": "Unit Type", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_1", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_2", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_3", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_4", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_5", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_6", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_7", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}], "dataTypes": {"Unit Type": "text", "Unit Type_1": "text", "Unit Type_2": "text", "Unit Type_3": "text", "Unit Type_4": "text", "Unit Type_5": "text", "Unit Type_6": "text", "Unit Type_7": "text"}, "category": "unknown"}, {"name": "Materials", "range": "A1:AY164", "totalRows": 164, "totalCols": 51, "dataRows": 164, "recordCount": 163, "headers": ["Board", null, null, null, null, null, null, null, "PVC", null, null, null, "<PERSON><PERSON>", null, null, null, "Hinges", null, null, null, "Drawers", null, null, null, "Internal_Drawers", null, null, null, "Machining Cost", null, null, null, "Cabinets", null, null, null, "Panels", null, null, null, "Door Fitting", null, null, null, "Yes/No", null, "Accessories", null, null, null, "Who"], "sampleData": [["Board", null, null, null, null, null, null, null, "PVC", null, null, null, "<PERSON><PERSON>", null, null, null, "Hinges", null, null, null, "Drawers", null, null, null, "Internal_Drawers", null, null, null, "Machining Cost", null, null, null, "Cabinets", null, null, null, "Panels", null, null, null, "Door Fitting", null, null, null, "Yes/No", null, "Accessories", null, null, null, "Who"], [null, "Material", null, "Waste Factor", "Length", "Height", "Labour", null, null, "Material", "Labour", null, null, "Material", "Labour", null, null, "Material", "Labour", null, null, "Material", "Labour", null, null, "Material", "Labour", null, null, "Material", "Labour", null, null, "Material", "Labour", null, null, "Material", "Labour", null, null, "Material", "Labour", null, null, null, null, "Material", "Labour"], ["Material Costs:"], ["None", 0, 0, 0, 0, 0, 0, null, "1mm White PVC", 1.4, 0.59, null, "Standard Handle", 13, 3.15, null, "107 degree", 6.96, 2.5, null, "Tandembox", 56.66, 27.445, null, "Tandembox", 86, 28.05, null, "Machining Cost", 0, 13.6, null, "Base Cabinets (1)", 0, 22.8, null, "Base Panels (1)", 0, 5, null, "Base Cabinets", 0, 5, null, "No", null, "Bifold", 45, 20, null, "Bronwyn"], ["16mm Carcase Wh MDF", 16.5, "$63.00", 0.1, 2400, 1200, 15.5, null, "1mm Colour PVC", 2.4, 0.59, null, "Premium Handle", 16, 3.15, null, "155 degree", 14.46, 2.5, null, "Legrabox", 121.67, 34.705000000000005, null, "Legrabox", 133.84, 34.705000000000005, null, null, null, null, null, "Wall Cabinets (1) (Allowing for underpanel)", 0, 28.5, null, "Base Panels (2)", 0, 5, null, "Wall Cabinets", 0, 5, null, "Yes", null, "Liftup", 20, 10, null, "<PERSON><PERSON>"]], "columns": [{"name": "__EMPTY", "sampleValues": ["Material", 0], "uniqueValues": 20, "nullCount": 34}, {"name": "__EMPTY_2", "sampleValues": ["Waste Factor", 0], "uniqueValues": 5, "nullCount": 1}, {"name": "__EMPTY_3", "sampleValues": ["Length", 0], "uniqueValues": 6, "nullCount": 1}, {"name": "__EMPTY_4", "sampleValues": ["Height", 0], "uniqueValues": 3, "nullCount": 1}, {"name": "__EMPTY_5", "sampleValues": ["Labour", 0], "uniqueValues": 3, "nullCount": 1}, {"name": "__EMPTY_7", "sampleValues": ["Material", 1.4], "uniqueValues": 7, "nullCount": 155}, {"name": "__EMPTY_8", "sampleValues": ["Labour", 0.59], "uniqueValues": 3, "nullCount": 155}, {"name": "__EMPTY_10", "sampleValues": ["Material", 13], "uniqueValues": 6, "nullCount": 156}, {"name": "__EMPTY_11", "sampleValues": ["Labour", 3.15], "uniqueValues": 4, "nullCount": 156}, {"name": "__EMPTY_13", "sampleValues": ["Material", 6.96], "uniqueValues": 5, "nullCount": 158}, {"name": "__EMPTY_14", "sampleValues": ["Labour", 2.5], "uniqueValues": 3, "nullCount": 158}, {"name": "__EMPTY_16", "sampleValues": ["Material", 56.66], "uniqueValues": 8, "nullCount": 155}, {"name": "__EMPTY_17", "sampleValues": ["Labour", 27.445], "uniqueValues": 6, "nullCount": 155}, {"name": "__EMPTY_19", "sampleValues": ["Material", 86], "uniqueValues": 8, "nullCount": 155}, {"name": "__EMPTY_20", "sampleValues": ["Labour", 28.05], "uniqueValues": 4, "nullCount": 155}, {"name": "__EMPTY_22", "sampleValues": ["Material", 0], "uniqueValues": 2, "nullCount": 161}, {"name": "__EMPTY_23", "sampleValues": ["Labour", 13.6], "uniqueValues": 2, "nullCount": 161}, {"name": "__EMPTY_25", "sampleValues": ["Material", 0], "uniqueValues": 2, "nullCount": 153}, {"name": "__EMPTY_26", "sampleValues": ["Labour", 22.8], "uniqueValues": 7, "nullCount": 153}, {"name": "__EMPTY_28", "sampleValues": ["Material", 0], "uniqueValues": 2, "nullCount": 138}, {"name": "__EMPTY_29", "sampleValues": ["Labour", 5], "uniqueValues": 3, "nullCount": 138}, {"name": "__EMPTY_31", "sampleValues": ["Material", 0], "uniqueValues": 2, "nullCount": 159}, {"name": "__EMPTY_32", "sampleValues": ["Labour", 5], "uniqueValues": 3, "nullCount": 159}, {"name": "__EMPTY_35", "sampleValues": ["Material", 45], "uniqueValues": 6, "nullCount": 157}, {"name": "__EMPTY_36", "sampleValues": ["Labour", 20], "uniqueValues": 6, "nullCount": 157}], "dataTypes": {"__EMPTY": "text", "__EMPTY_2": "text", "__EMPTY_3": "text", "__EMPTY_4": "text", "__EMPTY_5": "text", "__EMPTY_7": "text", "__EMPTY_8": "text", "__EMPTY_10": "text", "__EMPTY_11": "text", "__EMPTY_13": "text", "__EMPTY_14": "text", "__EMPTY_16": "text", "__EMPTY_17": "text", "__EMPTY_19": "text", "__EMPTY_20": "text", "__EMPTY_22": "text", "__EMPTY_23": "text", "__EMPTY_25": "text", "__EMPTY_26": "text", "__EMPTY_28": "text", "__EMPTY_29": "text", "__EMPTY_31": "text", "__EMPTY_32": "text", "__EMPTY_35": "text", "__EMPTY_36": "text"}, "category": "materials"}], "totalSheets": 8, "dataStructure": {"Master": {"name": "Master", "range": "A1:AM108", "totalRows": 108, "totalCols": 39, "dataRows": 108, "recordCount": 99, "headers": [null, "Lineal Mtr Calculator 3.2 30/04/25", "Pricing Date:", "XXXXXX", "Job Number:", "XXXXXX", "Priced By:", "Bronwyn"], "sampleData": [[null, "Lineal Mtr Calculator 3.2 30/04/25", "Pricing Date:", "XXXXXX", "Job Number:", "XXXXXX", "Priced By:", "Bronwyn"], [], [null, null, "Height", "UNIT Depth", "Panel Depth"], [null, "Base Units", 880, 580, 580], [null, "Tall Units", 2300, 580, 690]], "columns": [{"name": "Pricing Date:", "sampleValues": ["Height", 880, 2300], "uniqueValues": 18, "nullCount": 44}, {"name": "XXXXXX", "sampleValues": ["UNIT Depth", 580, 580], "uniqueValues": 16, "nullCount": 47}, {"name": "Job Number:", "sampleValues": ["Panel Depth", 580, 690], "uniqueValues": 21, "nullCount": 12}], "dataTypes": {"Pricing Date:": "text", "XXXXXX": "text", "Job Number:": "text"}, "category": "unknown"}, "Master Unit Width Calc": {"name": "Master Unit Width Calc", "range": "A1:W24", "totalRows": 24, "totalCols": 23, "dataRows": 24, "recordCount": 23, "headers": ["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], "sampleData": [["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], ["Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1"], ["Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2"], ["Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3"], ["Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4"]], "columns": [{"name": "Unit Type", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_1", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_2", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_3", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_4", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_5", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_6", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_7", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}], "dataTypes": {"Unit Type": "text", "Unit Type_1": "text", "Unit Type_2": "text", "Unit Type_3": "text", "Unit Type_4": "text", "Unit Type_5": "text", "Unit Type_6": "text", "Unit Type_7": "text"}, "category": "unknown"}, "Unit Width Calc": {"name": "Unit Width Calc", "range": "A1:W24", "totalRows": 24, "totalCols": 23, "dataRows": 24, "recordCount": 23, "headers": ["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], "sampleData": [["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], ["Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1"], ["Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2"], ["Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3"], ["Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4"]], "columns": [{"name": "Unit Type", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_1", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_2", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_3", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_4", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_5", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_6", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_7", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}], "dataTypes": {"Unit Type": "text", "Unit Type_1": "text", "Unit Type_2": "text", "Unit Type_3": "text", "Unit Type_4": "text", "Unit Type_5": "text", "Unit Type_6": "text", "Unit Type_7": "text"}, "category": "unknown"}, "Unit Width Calc (2)": {"name": "Unit Width Calc (2)", "range": "A1:W24", "totalRows": 24, "totalCols": 23, "dataRows": 24, "recordCount": 23, "headers": ["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], "sampleData": [["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], ["Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1"], ["Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2"], ["Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3"], ["Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4"]], "columns": [{"name": "Unit Type", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_1", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_2", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_3", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_4", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_5", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_6", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_7", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}], "dataTypes": {"Unit Type": "text", "Unit Type_1": "text", "Unit Type_2": "text", "Unit Type_3": "text", "Unit Type_4": "text", "Unit Type_5": "text", "Unit Type_6": "text", "Unit Type_7": "text"}, "category": "unknown"}, "Unit Width Calc (3)": {"name": "Unit Width Calc (3)", "range": "A1:W24", "totalRows": 24, "totalCols": 23, "dataRows": 24, "recordCount": 23, "headers": ["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], "sampleData": [["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], ["Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1"], ["Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2"], ["Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3"], ["Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4"]], "columns": [{"name": "Unit Type", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_1", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_2", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_3", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_4", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_5", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_6", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_7", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}], "dataTypes": {"Unit Type": "text", "Unit Type_1": "text", "Unit Type_2": "text", "Unit Type_3": "text", "Unit Type_4": "text", "Unit Type_5": "text", "Unit Type_6": "text", "Unit Type_7": "text"}, "category": "unknown"}, "Unit Width Calc (4)": {"name": "Unit Width Calc (4)", "range": "A1:W24", "totalRows": 24, "totalCols": 23, "dataRows": 24, "recordCount": 23, "headers": ["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], "sampleData": [["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], ["Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1"], ["Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2"], ["Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3"], ["Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4"]], "columns": [{"name": "Unit Type", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_1", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_2", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_3", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_4", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_5", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_6", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_7", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}], "dataTypes": {"Unit Type": "text", "Unit Type_1": "text", "Unit Type_2": "text", "Unit Type_3": "text", "Unit Type_4": "text", "Unit Type_5": "text", "Unit Type_6": "text", "Unit Type_7": "text"}, "category": "unknown"}, "Unit Width Calc (5)": {"name": "Unit Width Calc (5)", "range": "A1:W24", "totalRows": 24, "totalCols": 23, "dataRows": 24, "recordCount": 23, "headers": ["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], "sampleData": [["Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)", null, "Unit Type", "Width (mm)"], ["Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1", null, null, "Unit 1"], ["Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2", null, null, "Unit 2"], ["Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3", null, null, "Unit 3"], ["Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4", null, null, "Unit 4"]], "columns": [{"name": "Unit Type", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_1", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_2", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_3", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_4", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_5", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_6", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}, {"name": "Unit Type_7", "sampleValues": ["Unit 1", "Unit 2", "Unit 3"], "uniqueValues": 23, "nullCount": 0}], "dataTypes": {"Unit Type": "text", "Unit Type_1": "text", "Unit Type_2": "text", "Unit Type_3": "text", "Unit Type_4": "text", "Unit Type_5": "text", "Unit Type_6": "text", "Unit Type_7": "text"}, "category": "unknown"}, "Materials": {"name": "Materials", "range": "A1:AY164", "totalRows": 164, "totalCols": 51, "dataRows": 164, "recordCount": 163, "headers": ["Board", null, null, null, null, null, null, null, "PVC", null, null, null, "<PERSON><PERSON>", null, null, null, "Hinges", null, null, null, "Drawers", null, null, null, "Internal_Drawers", null, null, null, "Machining Cost", null, null, null, "Cabinets", null, null, null, "Panels", null, null, null, "Door Fitting", null, null, null, "Yes/No", null, "Accessories", null, null, null, "Who"], "sampleData": [["Board", null, null, null, null, null, null, null, "PVC", null, null, null, "<PERSON><PERSON>", null, null, null, "Hinges", null, null, null, "Drawers", null, null, null, "Internal_Drawers", null, null, null, "Machining Cost", null, null, null, "Cabinets", null, null, null, "Panels", null, null, null, "Door Fitting", null, null, null, "Yes/No", null, "Accessories", null, null, null, "Who"], [null, "Material", null, "Waste Factor", "Length", "Height", "Labour", null, null, "Material", "Labour", null, null, "Material", "Labour", null, null, "Material", "Labour", null, null, "Material", "Labour", null, null, "Material", "Labour", null, null, "Material", "Labour", null, null, "Material", "Labour", null, null, "Material", "Labour", null, null, "Material", "Labour", null, null, null, null, "Material", "Labour"], ["Material Costs:"], ["None", 0, 0, 0, 0, 0, 0, null, "1mm White PVC", 1.4, 0.59, null, "Standard Handle", 13, 3.15, null, "107 degree", 6.96, 2.5, null, "Tandembox", 56.66, 27.445, null, "Tandembox", 86, 28.05, null, "Machining Cost", 0, 13.6, null, "Base Cabinets (1)", 0, 22.8, null, "Base Panels (1)", 0, 5, null, "Base Cabinets", 0, 5, null, "No", null, "Bifold", 45, 20, null, "Bronwyn"], ["16mm Carcase Wh MDF", 16.5, "$63.00", 0.1, 2400, 1200, 15.5, null, "1mm Colour PVC", 2.4, 0.59, null, "Premium Handle", 16, 3.15, null, "155 degree", 14.46, 2.5, null, "Legrabox", 121.67, 34.705000000000005, null, "Legrabox", 133.84, 34.705000000000005, null, null, null, null, null, "Wall Cabinets (1) (Allowing for underpanel)", 0, 28.5, null, "Base Panels (2)", 0, 5, null, "Wall Cabinets", 0, 5, null, "Yes", null, "Liftup", 20, 10, null, "<PERSON><PERSON>"]], "columns": [{"name": "__EMPTY", "sampleValues": ["Material", 0], "uniqueValues": 20, "nullCount": 34}, {"name": "__EMPTY_2", "sampleValues": ["Waste Factor", 0], "uniqueValues": 5, "nullCount": 1}, {"name": "__EMPTY_3", "sampleValues": ["Length", 0], "uniqueValues": 6, "nullCount": 1}, {"name": "__EMPTY_4", "sampleValues": ["Height", 0], "uniqueValues": 3, "nullCount": 1}, {"name": "__EMPTY_5", "sampleValues": ["Labour", 0], "uniqueValues": 3, "nullCount": 1}, {"name": "__EMPTY_7", "sampleValues": ["Material", 1.4], "uniqueValues": 7, "nullCount": 155}, {"name": "__EMPTY_8", "sampleValues": ["Labour", 0.59], "uniqueValues": 3, "nullCount": 155}, {"name": "__EMPTY_10", "sampleValues": ["Material", 13], "uniqueValues": 6, "nullCount": 156}, {"name": "__EMPTY_11", "sampleValues": ["Labour", 3.15], "uniqueValues": 4, "nullCount": 156}, {"name": "__EMPTY_13", "sampleValues": ["Material", 6.96], "uniqueValues": 5, "nullCount": 158}, {"name": "__EMPTY_14", "sampleValues": ["Labour", 2.5], "uniqueValues": 3, "nullCount": 158}, {"name": "__EMPTY_16", "sampleValues": ["Material", 56.66], "uniqueValues": 8, "nullCount": 155}, {"name": "__EMPTY_17", "sampleValues": ["Labour", 27.445], "uniqueValues": 6, "nullCount": 155}, {"name": "__EMPTY_19", "sampleValues": ["Material", 86], "uniqueValues": 8, "nullCount": 155}, {"name": "__EMPTY_20", "sampleValues": ["Labour", 28.05], "uniqueValues": 4, "nullCount": 155}, {"name": "__EMPTY_22", "sampleValues": ["Material", 0], "uniqueValues": 2, "nullCount": 161}, {"name": "__EMPTY_23", "sampleValues": ["Labour", 13.6], "uniqueValues": 2, "nullCount": 161}, {"name": "__EMPTY_25", "sampleValues": ["Material", 0], "uniqueValues": 2, "nullCount": 153}, {"name": "__EMPTY_26", "sampleValues": ["Labour", 22.8], "uniqueValues": 7, "nullCount": 153}, {"name": "__EMPTY_28", "sampleValues": ["Material", 0], "uniqueValues": 2, "nullCount": 138}, {"name": "__EMPTY_29", "sampleValues": ["Labour", 5], "uniqueValues": 3, "nullCount": 138}, {"name": "__EMPTY_31", "sampleValues": ["Material", 0], "uniqueValues": 2, "nullCount": 159}, {"name": "__EMPTY_32", "sampleValues": ["Labour", 5], "uniqueValues": 3, "nullCount": 159}, {"name": "__EMPTY_35", "sampleValues": ["Material", 45], "uniqueValues": 6, "nullCount": 157}, {"name": "__EMPTY_36", "sampleValues": ["Labour", 20], "uniqueValues": 6, "nullCount": 157}], "dataTypes": {"__EMPTY": "text", "__EMPTY_2": "text", "__EMPTY_3": "text", "__EMPTY_4": "text", "__EMPTY_5": "text", "__EMPTY_7": "text", "__EMPTY_8": "text", "__EMPTY_10": "text", "__EMPTY_11": "text", "__EMPTY_13": "text", "__EMPTY_14": "text", "__EMPTY_16": "text", "__EMPTY_17": "text", "__EMPTY_19": "text", "__EMPTY_20": "text", "__EMPTY_22": "text", "__EMPTY_23": "text", "__EMPTY_25": "text", "__EMPTY_26": "text", "__EMPTY_28": "text", "__EMPTY_29": "text", "__EMPTY_31": "text", "__EMPTY_32": "text", "__EMPTY_35": "text", "__EMPTY_36": "text"}, "category": "materials"}}, "summary": {"totalRecords": 400, "categories": {"unknown": {"sheets": ["Master", "Master Unit Width Calc", "Unit Width Calc", "Unit Width Calc (2)", "Unit Width Calc (3)", "Unit Width Calc (4)", "Unit Width Calc (5)"], "totalRecords": 237}, "materials": {"sheets": ["Materials"], "totalRecords": 163}}, "recommendedMapping": {"unknown": {"targetTable": "unknown", "sheets": ["Master", "Master Unit Width Calc", "Unit Width Calc", "Unit Width Calc (2)", "Unit Width Calc (3)", "Unit Width Calc (4)", "Unit Width Calc (5)"], "priority": "review"}, "materials": {"targetTable": "materials", "sheets": ["Materials"], "priority": "high"}}}}