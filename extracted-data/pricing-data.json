{"materials": [{"id": "board_4", "category": "Board", "subcategory": "mdf", "name": "16mm Carcase Wh MDF", "description": "16mm Carcase Wh MDF", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 4, "sourceCategory": "Board"}, {"id": "board_5", "category": "Board", "subcategory": "board", "name": "16mm Mel <PERSON> 1", "description": "16mm Mel <PERSON> 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 5, "sourceCategory": "Board"}, {"id": "board_6", "category": "Board", "subcategory": "board", "name": "18mm Mel <PERSON> 1", "description": "18mm Mel <PERSON> 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 6, "sourceCategory": "Board"}, {"id": "board_7", "category": "Board", "subcategory": "board", "name": "16mm Wood Grain 1", "description": "16mm Wood Grain 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 7, "sourceCategory": "Board"}, {"id": "board_8", "category": "Board", "subcategory": "board", "name": "18mm Wood Grain 1", "description": "18mm Wood Grain 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 8, "sourceCategory": "Board"}, {"id": "board_9", "category": "Board", "subcategory": "board", "name": "16mm Wood Grain #1 3.6", "description": "16mm Wood Grain #1 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 9, "sourceCategory": "Board"}, {"id": "board_10", "category": "Board", "subcategory": "board", "name": "18mm Wood Grain #1 3.6", "description": "18mm Wood Grain #1 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 10, "sourceCategory": "Board"}, {"id": "board_11", "category": "Board", "subcategory": "board", "name": "18mm Paint", "description": "18mm Paint", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 11, "sourceCategory": "Board"}, {"id": "board_12", "category": "Board", "subcategory": "board", "name": "18mm Paint 3.6", "description": "18mm Paint 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 12, "sourceCategory": "Board"}, {"id": "board_13", "category": "Board", "subcategory": "board", "name": "12.5mm White", "description": "12.5mm White", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 13, "sourceCategory": "Board"}, {"id": "board_14", "category": "Board", "subcategory": "board", "name": "12.5mm Ply", "description": "12.5mm Ply", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 14, "sourceCategory": "Board"}, {"id": "board_15", "category": "Board", "subcategory": "mdf", "name": "12mm MDF", "description": "12mm MDF", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 15, "sourceCategory": "Board"}, {"id": "board_16", "category": "Board", "subcategory": "board", "name": "12mm Paint", "description": "12mm Paint", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 16, "sourceCategory": "Board"}, {"id": "board_17", "category": "Board", "subcategory": "board", "name": "12mm Veneer 1", "description": "12mm Veneer 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 17, "sourceCategory": "Board"}, {"id": "board_18", "category": "Board", "subcategory": "board", "name": "12mm Veneer 1 2.7", "description": "12mm Veneer 1 2.7", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 18, "sourceCategory": "Board"}, {"id": "board_19", "category": "Board", "subcategory": "board", "name": "13mm Compact Col 1", "description": "13mm Compact Col 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 19, "sourceCategory": "Board"}, {"id": "board_20", "category": "Board", "subcategory": "plywood", "name": "13mm Plywood", "description": "13mm Plywood", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 20, "sourceCategory": "Board"}, {"id": "board_21", "category": "Board", "subcategory": "board", "name": "15.5mm Scrap Materia!", "description": "15.5mm Scrap Materia!", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 21, "sourceCategory": "Board"}, {"id": "board_22", "category": "Board", "subcategory": "board", "name": "16mm Anthracite", "description": "16mm Anthracite", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 22, "sourceCategory": "Board"}, {"id": "board_23", "category": "Board", "subcategory": "mdf", "name": "16mm Carcase Grey MDF", "description": "16mm Carcase Grey MDF", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 23, "sourceCategory": "Board"}, {"id": "board_24", "category": "Board", "subcategory": "board", "name": "16mm Carcase Grey PB", "description": "16mm Carcase Grey PB", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 24, "sourceCategory": "Board"}, {"id": "board_25", "category": "Board", "subcategory": "board", "name": "16mm Carcase Wh 3.6", "description": "16mm Carcase Wh 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 25, "sourceCategory": "Board"}, {"id": "board_26", "category": "Board", "subcategory": "board", "name": "16mm Carcase Wh PB", "description": "16mm Carcase Wh PB", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 26, "sourceCategory": "Board"}, {"id": "board_27", "category": "Board", "subcategory": "board", "name": "16mm HPL Black Ply", "description": "16mm HPL Black Ply", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 27, "sourceCategory": "Board"}, {"id": "board_28", "category": "Board", "subcategory": "board", "name": "16mm HPL Supawhite", "description": "16mm HPL Supawhite", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 28, "sourceCategory": "Board"}, {"id": "board_29", "category": "Board", "subcategory": "mdf", "name": "16mm MDF", "description": "16mm MDF", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 29, "sourceCategory": "Board"}, {"id": "board_30", "category": "Board", "subcategory": "board", "name": "16mm Mel col 2", "description": "16mm Mel col 2", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 30, "sourceCategory": "Board"}, {"id": "board_31", "category": "Board", "subcategory": "board", "name": "16mm Mel col 3", "description": "16mm Mel col 3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 31, "sourceCategory": "Board"}, {"id": "board_32", "category": "Board", "subcategory": "board", "name": "16mm Mel col1 PB", "description": "16mm Mel col1 PB", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 32, "sourceCategory": "Board"}, {"id": "board_33", "category": "Board", "subcategory": "board", "name": "16mm Paint", "description": "16mm Paint", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 33, "sourceCategory": "Board"}, {"id": "board_34", "category": "Board", "subcategory": "board", "name": "16mm Plastic Board", "description": "16mm Plastic Board", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 34, "sourceCategory": "Board"}, {"id": "board_35", "category": "Board", "subcategory": "board", "name": "16mm Plastic Board NP", "description": "16mm Plastic Board NP", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 35, "sourceCategory": "Board"}, {"id": "board_36", "category": "Board", "subcategory": "board", "name": "16mm Rift 1", "description": "16mm Rift 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 36, "sourceCategory": "Board"}, {"id": "board_37", "category": "Board", "subcategory": "board", "name": "16mm Rift 2", "description": "16mm Rift 2", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 37, "sourceCategory": "Board"}, {"id": "board_38", "category": "Board", "subcategory": "board", "name": "16mm Scrap Material", "description": "16mm Scrap Material", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 38, "sourceCategory": "Board"}, {"id": "board_39", "category": "Board", "subcategory": "board", "name": "16mm Wood Grain 1", "description": "16mm Wood Grain 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 39, "sourceCategory": "Board"}, {"id": "board_40", "category": "Board", "subcategory": "board", "name": "16mm Wood Grain 3", "description": "16mm Wood Grain 3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 40, "sourceCategory": "Board"}, {"id": "board_41", "category": "Board", "subcategory": "board", "name": "16mm Scrap Material", "description": "16mm Scrap Material", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 41, "sourceCategory": "Board"}, {"id": "board_42", "category": "Board", "subcategory": "board", "name": "17.5UltGlaze Col #1", "description": "17.5UltGlaze Col #1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 42, "sourceCategory": "Board"}, {"id": "board_43", "category": "Board", "subcategory": "board", "name": "17.5UltGlaze Col #2", "description": "17.5UltGlaze Col #2", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 43, "sourceCategory": "Board"}, {"id": "board_44", "category": "Board", "subcategory": "board", "name": "17.5UltGlaze Col #3", "description": "17.5UltGlaze Col #3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 44, "sourceCategory": "Board"}, {"id": "board_45", "category": "Board", "subcategory": "board", "name": "17.5UltGlaze Col #4", "description": "17.5UltGlaze Col #4", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 45, "sourceCategory": "Board"}, {"id": "board_46", "category": "Board", "subcategory": "board", "name": "17.5UltGlaze Col#1  2.7", "description": "17.5UltGlaze Col#1  2.7", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 46, "sourceCategory": "Board"}, {"id": "board_47", "category": "Board", "subcategory": "board", "name": "17.5UltGlaze Col#3 2.7", "description": "17.5UltGlaze Col#3 2.7", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 47, "sourceCategory": "Board"}, {"id": "board_48", "category": "Board", "subcategory": "board", "name": "17mm Veneer 1", "description": "17mm Veneer 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 48, "sourceCategory": "Board"}, {"id": "board_49", "category": "Board", "subcategory": "board", "name": "17mm Veneer 2", "description": "17mm Veneer 2", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 49, "sourceCategory": "Board"}, {"id": "board_50", "category": "Board", "subcategory": "board", "name": "17UltGlaze Metalic Coll", "description": "17UltGlaze Metalic Coll", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 50, "sourceCategory": "Board"}, {"id": "board_51", "category": "Board", "subcategory": "board", "name": "18.4mm FM AR + Laminate", "description": "18.4mm FM AR + Laminate", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 51, "sourceCategory": "Board"}, {"id": "board_52", "category": "Board", "subcategory": "board", "name": "18.5mm Hoop Ply", "description": "18.5mm Hoop Ply", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 52, "sourceCategory": "Board"}, {"id": "board_53", "category": "Board", "subcategory": "board", "name": "18.5mm Ply", "description": "18.5mm Ply", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 53, "sourceCategory": "Board"}, {"id": "board_54", "category": "Board", "subcategory": "board", "name": "18MeltGIFiniChocolate", "description": "18MeltGIFiniChocolate", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 54, "sourceCategory": "Board"}, {"id": "board_55", "category": "Board", "subcategory": "board", "name": "18MeltGIFlefinedOak", "description": "18MeltGIFlefinedOak", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 55, "sourceCategory": "Board"}, {"id": "board_56", "category": "Board", "subcategory": "board", "name": "18MeltGIMistGrey", "description": "18MeltGIMistGrey", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 56, "sourceCategory": "Board"}, {"id": "board_57", "category": "Board", "subcategory": "board", "name": "18MeltGISnowdrift", "description": "18MeltGISnowdrift", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 57, "sourceCategory": "Board"}, {"id": "board_58", "category": "Board", "subcategory": "board", "name": "18MeltGIVicenzaWalnut", "description": "18MeltGIVicenzaWalnut", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 58, "sourceCategory": "Board"}, {"id": "board_59", "category": "Board", "subcategory": "board", "name": "18MeltGIWamnWhite", "description": "18MeltGIWamnWhite", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 59, "sourceCategory": "Board"}, {"id": "board_60", "category": "Board", "subcategory": "board", "name": "18mm Birch Elite PF", "description": "18mm Birch Elite PF", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 60, "sourceCategory": "Board"}, {"id": "board_61", "category": "Board", "subcategory": "board", "name": "18mm Carcase Wh PB", "description": "18mm Carcase Wh PB", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 61, "sourceCategory": "Board"}, {"id": "board_62", "category": "Board", "subcategory": "board", "name": "18mm HPL Black Ply", "description": "18mm HPL Black Ply", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 62, "sourceCategory": "Board"}, {"id": "board_63", "category": "Board", "subcategory": "board", "name": "18mm HPL Futura #1", "description": "18mm HPL Futura #1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 63, "sourceCategory": "Board"}, {"id": "board_64", "category": "Board", "subcategory": "board", "name": "18mm HPL Futura #3", "description": "18mm HPL Futura #3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 64, "sourceCategory": "Board"}, {"id": "board_65", "category": "Board", "subcategory": "board", "name": "18mm HPL Futura Mat #1", "description": "18mm HPL Futura Mat #1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 65, "sourceCategory": "Board"}, {"id": "board_66", "category": "Board", "subcategory": "board", "name": "18mm HPL Italia PLY 1", "description": "18mm HPL Italia PLY 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 66, "sourceCategory": "Board"}, {"id": "board_67", "category": "Board", "subcategory": "board", "name": "18mm HPL Italia PLY 2", "description": "18mm HPL Italia PLY 2", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 67, "sourceCategory": "Board"}, {"id": "board_68", "category": "Board", "subcategory": "board", "name": "18mm HPL Supawhite Ply", "description": "18mm HPL Supawhite Ply", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 68, "sourceCategory": "Board"}, {"id": "board_69", "category": "Board", "subcategory": "board", "name": "18mm Lam Acrylic Col#1", "description": "18mm Lam Acrylic Col#1", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 69, "sourceCategory": "Board"}, {"id": "board_70", "category": "Board", "subcategory": "board", "name": "18mm Lam Acrylic Col#2", "description": "18mm Lam Acrylic Col#2", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 70, "sourceCategory": "Board"}, {"id": "board_71", "category": "Board", "subcategory": "board", "name": "18mm Lam Acrylic Col#3", "description": "18mm Lam Acrylic Col#3", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 71, "sourceCategory": "Board"}, {"id": "board_72", "category": "Board", "subcategory": "board", "name": "18mm Lam Acrylic Col#3", "description": "18mm Lam Acrylic Col#3", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 72, "sourceCategory": "Board"}, {"id": "board_73", "category": "Board", "subcategory": "board", "name": "18mm Lam Acrylic WG #1", "description": "18mm Lam Acrylic WG #1", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 73, "sourceCategory": "Board"}, {"id": "board_74", "category": "Board", "subcategory": "board", "name": "18mm Lam Acrylic WG#1", "description": "18mm Lam Acrylic WG#1", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 74, "sourceCategory": "Board"}, {"id": "board_75", "category": "Board", "subcategory": "mdf", "name": "18mm MDF", "description": "18mm MDF", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 75, "sourceCategory": "Board"}, {"id": "board_76", "category": "Board", "subcategory": "mdf", "name": "18mm MDF 3.6", "description": "18mm MDF 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 76, "sourceCategory": "Board"}, {"id": "board_77", "category": "Board", "subcategory": "board", "name": "18mm <PERSON> 2", "description": "18mm <PERSON> 2", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 77, "sourceCategory": "Board"}, {"id": "board_78", "category": "Board", "subcategory": "board", "name": "18mm <PERSON> 2 3.6", "description": "18mm <PERSON> 2 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 78, "sourceCategory": "Board"}, {"id": "board_79", "category": "Board", "subcategory": "board", "name": "18mm Mel Col 3", "description": "18mm Mel Col 3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 79, "sourceCategory": "Board"}, {"id": "board_80", "category": "Board", "subcategory": "board", "name": "18mm <PERSON> 3 3.6", "description": "18mm <PERSON> 3 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 80, "sourceCategory": "Board"}, {"id": "board_81", "category": "Board", "subcategory": "board", "name": "18mm PA 2.4 Col#1", "description": "18mm PA 2.4 Col#1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 81, "sourceCategory": "Board"}, {"id": "board_82", "category": "Board", "subcategory": "board", "name": "18mm PA 2.4 Col#2", "description": "18mm PA 2.4 Col#2", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 82, "sourceCategory": "Board"}, {"id": "board_83", "category": "Board", "subcategory": "board", "name": "18mm PA 2.4 Col#3", "description": "18mm PA 2.4 Col#3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 83, "sourceCategory": "Board"}, {"id": "board_84", "category": "Board", "subcategory": "board", "name": "18mm PA 2.4 Metal#1", "description": "18mm PA 2.4 Metal#1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 84, "sourceCategory": "Board"}, {"id": "board_85", "category": "Board", "subcategory": "board", "name": "18mm PA 2.4 Metal#3", "description": "18mm PA 2.4 Metal#3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 85, "sourceCategory": "Board"}, {"id": "board_86", "category": "Board", "subcategory": "board", "name": "18mm PA 2.7 Col#1", "description": "18mm PA 2.7 Col#1", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 86, "sourceCategory": "Board"}, {"id": "board_87", "category": "Board", "subcategory": "board", "name": "18mm PA 2.7 Col#3", "description": "18mm PA 2.7 Col#3", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 87, "sourceCategory": "Board"}, {"id": "board_88", "category": "Board", "subcategory": "board", "name": "18mm PA 2.7 Met Col#1", "description": "18mm PA 2.7 Met Col#1", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 88, "sourceCategory": "Board"}, {"id": "board_89", "category": "Board", "subcategory": "board", "name": "18mm PA 2.7 Met Col#3", "description": "18mm PA 2.7 Met Col#3", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 89, "sourceCategory": "Board"}, {"id": "board_90", "category": "Board", "subcategory": "board", "name": "18mm Paint", "description": "18mm Paint", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 90, "sourceCategory": "Board"}, {"id": "board_91", "category": "Board", "subcategory": "board", "name": "18mm Paint 3.6", "description": "18mm Paint 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 91, "sourceCategory": "Board"}, {"id": "board_92", "category": "Board", "subcategory": "board", "name": "18mm P<PERSON> #1", "description": "18mm P<PERSON> #1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 92, "sourceCategory": "Board"}, {"id": "board_93", "category": "Board", "subcategory": "board", "name": "18mm Wood Grain 2", "description": "18mm Wood Grain 2", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 93, "sourceCategory": "Board"}, {"id": "board_94", "category": "Board", "subcategory": "board", "name": "18mm Wood Grain 3", "description": "18mm Wood Grain 3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 94, "sourceCategory": "Board"}, {"id": "board_95", "category": "Board", "subcategory": "board", "name": "18mm Wood Grain #2 3.6", "description": "18mm Wood Grain #2 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 95, "sourceCategory": "Board"}, {"id": "board_96", "category": "Board", "subcategory": "board", "name": "18mm Wood Grain #3 3.6", "description": "18mm Wood Grain #3 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 96, "sourceCategory": "Board"}, {"id": "board_97", "category": "Board", "subcategory": "board", "name": "18mmArborlineGlossWhtCr", "description": "18mmArborlineGlossWhtCr", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 97, "sourceCategory": "Board"}, {"id": "board_98", "category": "Board", "subcategory": "board", "name": "18mmArborlineStd", "description": "18mmArborlineStd", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 98, "sourceCategory": "Board"}, {"id": "board_99", "category": "Board", "subcategory": "board", "name": "18mnn Plymasters <PERSON><PERSON><PERSON>", "description": "18mnn Plymasters <PERSON><PERSON><PERSON>", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 99, "sourceCategory": "Board"}, {"id": "board_100", "category": "Board", "subcategory": "board", "name": "18mnn Wood Grain 2", "description": "18mnn Wood Grain 2", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 100, "sourceCategory": "Board"}, {"id": "board_101", "category": "Board", "subcategory": "board", "name": "18nrinnArborlineGlossOther", "description": "18nrinnArborlineGlossOther", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 101, "sourceCategory": "Board"}, {"id": "board_102", "category": "Board", "subcategory": "board", "name": "19mm FIPL Polaris Ply #1", "description": "19mm FIPL Polaris Ply #1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 102, "sourceCategory": "Board"}, {"id": "board_103", "category": "Board", "subcategory": "board", "name": "19mm FIPL Polaris Ply #2", "description": "19mm FIPL Polaris Ply #2", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 103, "sourceCategory": "Board"}, {"id": "board_104", "category": "Board", "subcategory": "board", "name": "19mm Veneer 1", "description": "19mm Veneer 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 104, "sourceCategory": "Board"}, {"id": "board_105", "category": "Board", "subcategory": "board", "name": "19mm Veneer 1 2.8", "description": "19mm Veneer 1 2.8", "type": "material", "basePrice": 2800, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 105, "sourceCategory": "Board"}, {"id": "board_106", "category": "Board", "subcategory": "board", "name": "19mm Veneer 2", "description": "19mm Veneer 2", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 106, "sourceCategory": "Board"}, {"id": "board_107", "category": "Board", "subcategory": "board", "name": "19mm Veneer 2 2.8", "description": "19mm Veneer 2 2.8", "type": "material", "basePrice": 2800, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 107, "sourceCategory": "Board"}, {"id": "board_108", "category": "Board", "subcategory": "board", "name": "19mm Veneer Rustica", "description": "19mm Veneer Rustica", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 108, "sourceCategory": "Board"}, {"id": "board_109", "category": "Board", "subcategory": "board", "name": "19mm Veneer Rustica", "description": "19mm Veneer Rustica", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 109, "sourceCategory": "Board"}, {"id": "board_110", "category": "Board", "subcategory": "board", "name": "1BMeltGIAntWilunaWhite", "description": "1BMeltGIAntWilunaWhite", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 110, "sourceCategory": "Board"}, {"id": "board_111", "category": "Board", "subcategory": "board", "name": "1BMeltGIBirch", "description": "1BMeltGIBirch", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 111, "sourceCategory": "Board"}, {"id": "board_112", "category": "Board", "subcategory": "board", "name": "24mm Hoop Pine", "description": "24mm Hoop Pine", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 112, "sourceCategory": "Board"}, {"id": "board_113", "category": "Board", "subcategory": "board", "name": "24mm HPL Futura  PLY #1", "description": "24mm HPL Futura  PLY #1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 113, "sourceCategory": "Board"}, {"id": "board_114", "category": "Board", "subcategory": "mdf", "name": "25mm Carcase Wh MDF", "description": "25mm Carcase Wh MDF", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 114, "sourceCategory": "Board"}, {"id": "board_115", "category": "Board", "subcategory": "board", "name": "25mm Hoop Pine Ply", "description": "25mm Hoop Pine Ply", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 115, "sourceCategory": "Board"}, {"id": "board_116", "category": "Board", "subcategory": "board", "name": "25mm Lam Acrylic WG #3", "description": "25mm Lam Acrylic WG #3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 116, "sourceCategory": "Board"}, {"id": "board_117", "category": "Board", "subcategory": "mdf", "name": "25mm MDF", "description": "25mm MDF", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 117, "sourceCategory": "Board"}, {"id": "board_118", "category": "Board", "subcategory": "board", "name": "25mm Mel <PERSON> #1 3.6", "description": "25mm Mel <PERSON> #1 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 118, "sourceCategory": "Board"}, {"id": "board_119", "category": "Board", "subcategory": "board", "name": "25mm Mel <PERSON> #2 3.6", "description": "25mm Mel <PERSON> #2 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 119, "sourceCategory": "Board"}, {"id": "board_120", "category": "Board", "subcategory": "board", "name": "25mm Mel <PERSON> #3 3.6", "description": "25mm Mel <PERSON> #3 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 120, "sourceCategory": "Board"}, {"id": "board_121", "category": "Board", "subcategory": "board", "name": "25mm Mel <PERSON> 1", "description": "25mm Mel <PERSON> 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 121, "sourceCategory": "Board"}, {"id": "board_122", "category": "Board", "subcategory": "board", "name": "25mm <PERSON> 2", "description": "25mm <PERSON> 2", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 122, "sourceCategory": "Board"}, {"id": "board_123", "category": "Board", "subcategory": "board", "name": "25mm Mel Col 3", "description": "25mm Mel Col 3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 123, "sourceCategory": "Board"}, {"id": "board_124", "category": "Board", "subcategory": "board", "name": "25mm Paint", "description": "25mm Paint", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 124, "sourceCategory": "Board"}, {"id": "board_125", "category": "Board", "subcategory": "board", "name": "25mm Paint 3.6", "description": "25mm Paint 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 125, "sourceCategory": "Board"}, {"id": "board_126", "category": "Board", "subcategory": "board", "name": "25mm Wood Grain #1 3.6", "description": "25mm Wood Grain #1 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 126, "sourceCategory": "Board"}, {"id": "board_127", "category": "Board", "subcategory": "board", "name": "25mm Wood Grain #2 3.6", "description": "25mm Wood Grain #2 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 127, "sourceCategory": "Board"}, {"id": "board_128", "category": "Board", "subcategory": "board", "name": "25mm Wood Grain #3 3.6", "description": "25mm Wood Grain #3 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 128, "sourceCategory": "Board"}, {"id": "board_129", "category": "Board", "subcategory": "board", "name": "25mm Wood Grain 1", "description": "25mm Wood Grain 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 129, "sourceCategory": "Board"}, {"id": "board_130", "category": "Board", "subcategory": "board", "name": "25mm Wood Grain 2", "description": "25mm Wood Grain 2", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 130, "sourceCategory": "Board"}, {"id": "board_131", "category": "Board", "subcategory": "board", "name": "25mm Wood Grain 3", "description": "25mm Wood Grain 3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 131, "sourceCategory": "Board"}, {"id": "board_132", "category": "Board", "subcategory": "board", "name": "26mm Lam Acrylic #3", "description": "26mm Lam Acrylic #3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 132, "sourceCategory": "Board"}, {"id": "board_133", "category": "Board", "subcategory": "board", "name": "26mm Veneer 1", "description": "26mm Veneer 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 133, "sourceCategory": "Board"}, {"id": "board_134", "category": "Board", "subcategory": "board", "name": "27mm PA 2.4 Col#1", "description": "27mm PA 2.4 Col#1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 134, "sourceCategory": "Board"}, {"id": "board_135", "category": "Board", "subcategory": "board", "name": "27mm PA 2.4 Col#3", "description": "27mm PA 2.4 Col#3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 135, "sourceCategory": "Board"}, {"id": "board_136", "category": "Board", "subcategory": "board", "name": "27mm PA 2.4 Metal#1", "description": "27mm PA 2.4 Metal#1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 136, "sourceCategory": "Board"}, {"id": "board_137", "category": "Board", "subcategory": "board", "name": "27mm PA 2.4 Metal#3", "description": "27mm PA 2.4 Metal#3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 137, "sourceCategory": "Board"}, {"id": "board_138", "category": "Board", "subcategory": "board", "name": "27mm PA 2.7 Col#1", "description": "27mm PA 2.7 Col#1", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 138, "sourceCategory": "Board"}, {"id": "board_139", "category": "Board", "subcategory": "mdf", "name": "30mm MDF", "description": "30mm MDF", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 139, "sourceCategory": "Board"}, {"id": "board_140", "category": "Board", "subcategory": "board", "name": "30mm Mel <PERSON> 1", "description": "30mm Mel <PERSON> 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 140, "sourceCategory": "Board"}, {"id": "board_141", "category": "Board", "subcategory": "board", "name": "30mm <PERSON> 2", "description": "30mm <PERSON> 2", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 141, "sourceCategory": "Board"}, {"id": "board_142", "category": "Board", "subcategory": "board", "name": "30mm Mel Col 3", "description": "30mm Mel Col 3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 142, "sourceCategory": "Board"}, {"id": "board_143", "category": "Board", "subcategory": "board", "name": "30mm Paint", "description": "30mm Paint", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 143, "sourceCategory": "Board"}, {"id": "board_144", "category": "Board", "subcategory": "board", "name": "30mm Paint 3.0", "description": "30mm Paint 3.0", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 144, "sourceCategory": "Board"}, {"id": "board_145", "category": "Board", "subcategory": "board", "name": "30mm Paint 3.6", "description": "30mm Paint 3.6", "type": "material", "basePrice": 3600, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 145, "sourceCategory": "Board"}, {"id": "board_146", "category": "Board", "subcategory": "board", "name": "30mm Rift 1", "description": "30mm Rift 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 146, "sourceCategory": "Board"}, {"id": "board_147", "category": "Board", "subcategory": "board", "name": "30mm Rift 2", "description": "30mm Rift 2", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 147, "sourceCategory": "Board"}, {"id": "board_148", "category": "Board", "subcategory": "board", "name": "30mm Wood Grain 1", "description": "30mm Wood Grain 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 148, "sourceCategory": "Board"}, {"id": "board_149", "category": "Board", "subcategory": "board", "name": "30mm Wood Grain 2", "description": "30mm Wood Grain 2", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 149, "sourceCategory": "Board"}, {"id": "board_150", "category": "Board", "subcategory": "board", "name": "30mm Wood Grain 3", "description": "30mm Wood Grain 3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 150, "sourceCategory": "Board"}, {"id": "board_151", "category": "Board", "subcategory": "board", "name": "31mm Veneer 1", "description": "31mm Veneer 1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 151, "sourceCategory": "Board"}, {"id": "board_152", "category": "Board", "subcategory": "board", "name": "32mm PA 2.4 Col#1", "description": "32mm PA 2.4 Col#1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 152, "sourceCategory": "Board"}, {"id": "board_153", "category": "Board", "subcategory": "board", "name": "32mm PA 2.4 Col#3", "description": "32mm PA 2.4 Col#3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 153, "sourceCategory": "Board"}, {"id": "board_154", "category": "Board", "subcategory": "board", "name": "32mm PA 2.4 Metal#2", "description": "32mm PA 2.4 Metal#2", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 154, "sourceCategory": "Board"}, {"id": "board_155", "category": "Board", "subcategory": "board", "name": "32mm PA 2.4 Metal#3", "description": "32mm PA 2.4 Metal#3", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 155, "sourceCategory": "Board"}, {"id": "board_156", "category": "Board", "subcategory": "board", "name": "32mm PA 2.7 Col#1", "description": "32mm PA 2.7 Col#1", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 156, "sourceCategory": "Board"}, {"id": "board_157", "category": "Board", "subcategory": "board", "name": "32mm PA 2.7 Col#3", "description": "32mm PA 2.7 Col#3", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 157, "sourceCategory": "Board"}, {"id": "board_158", "category": "Board", "subcategory": "board", "name": "32mm PA 2.7 MET Col#1", "description": "32mm PA 2.7 MET Col#1", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 158, "sourceCategory": "Board"}, {"id": "board_159", "category": "Board", "subcategory": "board", "name": "32mm PA 2.7 MET Col#3", "description": "32mm PA 2.7 MET Col#3", "type": "material", "basePrice": 2700, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 159, "sourceCategory": "Board"}, {"id": "board_160", "category": "Board", "subcategory": "board", "name": "33mm DSW Paint", "description": "33mm DSW Paint", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 160, "sourceCategory": "Board"}, {"id": "board_161", "category": "Board", "subcategory": "board", "name": "35mm HPL Futura PLY #1", "description": "35mm HPL Futura PLY #1", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 161, "sourceCategory": "Board"}, {"id": "board_162", "category": "Board", "subcategory": "mdf", "name": "3mm MDF", "description": "3mm MDF", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 162, "sourceCategory": "Board"}, {"id": "board_163", "category": "Board", "subcategory": "board", "name": "41mm Veneer", "description": "41mm Veneer", "type": "material", "basePrice": 2400, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 163, "sourceCategory": "Board"}, {"id": "pvc_3", "category": "PVC", "subcategory": "standard", "name": "1mm White PVC", "description": "1mm White PVC", "type": "material", "basePrice": 1.4, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 3, "sourceCategory": "PVC"}, {"id": "pvc_4", "category": "PVC", "subcategory": "standard", "name": "1mm Colour PVC", "description": "1mm Colour PVC", "type": "material", "basePrice": 2.4, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 4, "sourceCategory": "PVC"}, {"id": "pvc_5", "category": "PVC", "subcategory": "standard", "name": "1mm Woodgrain PVC", "description": "1mm Woodgrain PVC", "type": "material", "basePrice": 2.4, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 5, "sourceCategory": "PVC"}, {"id": "pvc_6", "category": "PVC", "subcategory": "standard", "name": "1mm Acrylic", "description": "1mm Acrylic", "type": "material", "basePrice": 3.7, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 6, "sourceCategory": "PVC"}, {"id": "pvc_7", "category": "PVC", "subcategory": "standard", "name": "0.6mm <PERSON><PERSON><PERSON>", "description": "0.6mm <PERSON><PERSON><PERSON>", "type": "material", "basePrice": 4, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 7, "sourceCategory": "PVC"}, {"id": "pvc_8", "category": "PVC", "subcategory": "standard", "name": "1mm HiGloss PVC", "description": "1mm HiGloss PVC", "type": "material", "basePrice": 2.6, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 8, "sourceCategory": "PVC"}, {"id": "cabinets_3", "category": "Cabinets", "subcategory": "standard", "name": "Base Cabinets (1)", "description": "Base Cabinets (1)", "type": "material", "basePrice": 22.8, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 3, "sourceCategory": "Cabinets"}, {"id": "cabinets_4", "category": "Cabinets", "subcategory": "standard", "name": "Wall Cabinets (1) (Allowing for underpanel)", "description": "Wall Cabinets (1) (Allowing for underpanel)", "type": "material", "basePrice": 28.5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 4, "sourceCategory": "Cabinets"}, {"id": "cabinets_5", "category": "Cabinets", "subcategory": "standard", "name": "Tall Cabinets (1)", "description": "Tall Cabinets (1)", "type": "material", "basePrice": 39, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 5, "sourceCategory": "Cabinets"}, {"id": "cabinets_6", "category": "Cabinets", "subcategory": "standard", "name": "Division (1)", "description": "Division (1)", "type": "material", "basePrice": 5.25, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 6, "sourceCategory": "Cabinets"}, {"id": "cabinets_7", "category": "Cabinets", "subcategory": "standard", "name": "Floating Shelves (1)", "description": "Floating Shelves (1)", "type": "material", "basePrice": 25.5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 7, "sourceCategory": "Cabinets"}, {"id": "cabinets_8", "category": "Cabinets", "subcategory": "standard", "name": "Base Cabinets (2)", "description": "Base Cabinets (2)", "type": "material", "basePrice": 22.8, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 8, "sourceCategory": "Cabinets"}, {"id": "cabinets_9", "category": "Cabinets", "subcategory": "standard", "name": "Wall Cabinets (2) (Allowing for underpanel)", "description": "Wall Cabinets (2) (Allowing for underpanel)", "type": "material", "basePrice": 14.25, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 9, "sourceCategory": "Cabinets"}, {"id": "cabinets_10", "category": "Cabinets", "subcategory": "standard", "name": "Tall Cabinets (2)", "description": "Tall Cabinets (2)", "type": "material", "basePrice": 39, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 10, "sourceCategory": "Cabinets"}, {"id": "cabinets_11", "category": "Cabinets", "subcategory": "standard", "name": "Floating Shelves (2)", "description": "Floating Shelves (2)", "type": "material", "basePrice": 25.5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 11, "sourceCategory": "Cabinets"}, {"id": "panels_3", "category": "Panels", "subcategory": "standard", "name": "Base Panels (1)", "description": "Base Panels (1)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 3, "sourceCategory": "Panels"}, {"id": "panels_4", "category": "Panels", "subcategory": "standard", "name": "Base Panels (2)", "description": "Base Panels (2)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 4, "sourceCategory": "Panels"}, {"id": "panels_5", "category": "Panels", "subcategory": "standard", "name": "Tall Panels (1)", "description": "Tall Panels (1)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 5, "sourceCategory": "Panels"}, {"id": "panels_6", "category": "Panels", "subcategory": "standard", "name": "Tall Panels (2)", "description": "Tall Panels (2)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 6, "sourceCategory": "Panels"}, {"id": "panels_7", "category": "Panels", "subcategory": "standard", "name": "Wall Panels (1)", "description": "Wall Panels (1)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 7, "sourceCategory": "Panels"}, {"id": "panels_8", "category": "Panels", "subcategory": "standard", "name": "Wall Panels (2)", "description": "Wall Panels (2)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 8, "sourceCategory": "Panels"}, {"id": "panels_9", "category": "Panels", "subcategory": "standard", "name": "Base Scribers (1)", "description": "Base Scribers (1)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 9, "sourceCategory": "Panels"}, {"id": "panels_10", "category": "Panels", "subcategory": "standard", "name": "Base Scribers (2)", "description": "Base Scribers (2)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 10, "sourceCategory": "Panels"}, {"id": "panels_11", "category": "Panels", "subcategory": "standard", "name": "<PERSON> (1)", "description": "<PERSON> (1)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 11, "sourceCategory": "Panels"}, {"id": "panels_12", "category": "Panels", "subcategory": "standard", "name": "<PERSON> (2)", "description": "<PERSON> (2)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 12, "sourceCategory": "Panels"}, {"id": "panels_13", "category": "Panels", "subcategory": "standard", "name": "Back Panels (1)", "description": "Back Panels (1)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 13, "sourceCategory": "Panels"}, {"id": "panels_14", "category": "Panels", "subcategory": "standard", "name": "Back Panels (2)", "description": "Back Panels (2)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 14, "sourceCategory": "Panels"}, {"id": "panels_15", "category": "Panels", "subcategory": "standard", "name": "Double Sided Base Panels (Lacquer Only) (1)", "description": "Double Sided Base Panels (Lacquer Only) (1)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 15, "sourceCategory": "Panels"}, {"id": "panels_16", "category": "Panels", "subcategory": "standard", "name": "Double Sided Base Panels (Lacquer Only) (2)", "description": "Double Sided Base Panels (Lacquer Only) (2)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 16, "sourceCategory": "Panels"}, {"id": "panels_17", "category": "Panels", "subcategory": "standard", "name": "Double Sided Tall Panels (Lacquer Only) (1)", "description": "Double Sided Tall Panels (Lacquer Only) (1)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 17, "sourceCategory": "Panels"}, {"id": "panels_18", "category": "Panels", "subcategory": "standard", "name": "Double Sided Tall Panels (Lacquer Only) (2)", "description": "Double Sided Tall Panels (Lacquer Only) (2)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 18, "sourceCategory": "Panels"}, {"id": "panels_19", "category": "Panels", "subcategory": "standard", "name": "Double Sided Wall Panels (Lacquer Only) (1)", "description": "Double Sided Wall Panels (Lacquer Only) (1)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 19, "sourceCategory": "Panels"}, {"id": "panels_20", "category": "Panels", "subcategory": "standard", "name": "Double Sided Wall Panels (Lacquer Only) (2)", "description": "Double Sided Wall Panels (Lacquer Only) (2)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 20, "sourceCategory": "Panels"}, {"id": "panels_21", "category": "Panels", "subcategory": "standard", "name": "Double Sided Back Panels (Lacquer Only) (1)", "description": "Double Sided Back Panels (Lacquer Only) (1)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 21, "sourceCategory": "Panels"}, {"id": "panels_22", "category": "Panels", "subcategory": "standard", "name": "Double Sided Back Panels (Lacquer Only) (2)", "description": "Double Sided Back Panels (Lacquer Only) (2)", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 22, "sourceCategory": "Panels"}, {"id": "panels_23", "category": "Panels", "subcategory": "standard", "name": "Toekicks (1)", "description": "Toekicks (1)", "type": "material", "basePrice": 2, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 23, "sourceCategory": "Panels"}, {"id": "panels_24", "category": "Panels", "subcategory": "standard", "name": "Toekicks (2)", "description": "Toekicks (2)", "type": "material", "basePrice": 2, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 24, "sourceCategory": "Panels"}, {"id": "panels_25", "category": "Panels", "subcategory": "standard", "name": "Fascia (1)", "description": "Fascia (1)", "type": "material", "basePrice": 2, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 25, "sourceCategory": "Panels"}, {"id": "panels_26", "category": "Panels", "subcategory": "standard", "name": "Fascia (2)", "description": "Fascia (2)", "type": "material", "basePrice": 2, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 26, "sourceCategory": "Panels"}, {"id": "door_fitting_3", "category": "Door Fitting", "subcategory": "standard", "name": "Base Cabinets", "description": "Base Cabinets", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 3, "sourceCategory": "Door Fitting"}, {"id": "door_fitting_4", "category": "Door Fitting", "subcategory": "standard", "name": "Wall Cabinets", "description": "Wall Cabinets", "type": "material", "basePrice": 5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 4, "sourceCategory": "Door Fitting"}, {"id": "door_fitting_5", "category": "Door Fitting", "subcategory": "standard", "name": "Tall Cabinets", "description": "Tall Cabinets", "type": "material", "basePrice": 25, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 5, "sourceCategory": "Door Fitting"}], "hardware": [{"id": "handles_3", "category": "<PERSON><PERSON>", "subcategory": "handle", "name": "Standard Handle", "description": "Standard Handle", "type": "hardware", "basePrice": 13, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 3, "sourceCategory": "<PERSON><PERSON>"}, {"id": "handles_4", "category": "<PERSON><PERSON>", "subcategory": "handle", "name": "Premium Handle", "description": "Premium Handle", "type": "hardware", "basePrice": 16, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 4, "sourceCategory": "<PERSON><PERSON>"}, {"id": "handles_5", "category": "<PERSON><PERSON>", "subcategory": "handle", "name": "Deluxe Handle", "description": "Deluxe Handle", "type": "hardware", "basePrice": 25, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 5, "sourceCategory": "<PERSON><PERSON>"}, {"id": "handles_6", "category": "<PERSON><PERSON>", "subcategory": "handle", "name": "Designer <PERSON><PERSON>", "description": "Designer <PERSON><PERSON>", "type": "hardware", "basePrice": 50, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 6, "sourceCategory": "<PERSON><PERSON>"}, {"id": "handles_8", "category": "<PERSON><PERSON>", "subcategory": "handle", "name": "Sharknose", "description": "Sharknose", "type": "hardware", "basePrice": 30, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 8, "sourceCategory": "<PERSON><PERSON>"}, {"id": "hinges_3", "category": "Hinges", "subcategory": "hinge", "name": "107 degree", "description": "107 degree", "type": "hardware", "basePrice": 107, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 3, "sourceCategory": "Hinges"}, {"id": "hinges_4", "category": "Hinges", "subcategory": "hinge", "name": "155 degree", "description": "155 degree", "type": "hardware", "basePrice": 155, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 4, "sourceCategory": "Hinges"}, {"id": "hinges_5", "category": "Hinges", "subcategory": "hinge", "name": "155 THIN", "description": "155 THIN", "type": "hardware", "basePrice": 155, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 5, "sourceCategory": "Hinges"}, {"id": "drawers_3", "category": "Drawers", "subcategory": "drawer_box", "name": "Tandembox", "description": "Tandembox", "type": "hardware", "basePrice": 56.66, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 3, "sourceCategory": "Drawers"}, {"id": "drawers_4", "category": "Drawers", "subcategory": "drawer_box", "name": "Legrabox", "description": "Legrabox", "type": "hardware", "basePrice": 121.67, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 4, "sourceCategory": "Drawers"}, {"id": "drawers_5", "category": "Drawers", "subcategory": "drawer_box", "name": "Merivobox", "description": "Merivobox", "type": "hardware", "basePrice": 80.52, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 5, "sourceCategory": "Drawers"}, {"id": "drawers_6", "category": "Drawers", "subcategory": "drawer", "name": "Multitech", "description": "Multitech", "type": "hardware", "basePrice": 25.5, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 6, "sourceCategory": "Drawers"}, {"id": "drawers_7", "category": "Drawers", "subcategory": "drawer", "name": "Alto", "description": "Alto", "type": "hardware", "basePrice": 34.95, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 7, "sourceCategory": "Drawers"}, {"id": "drawers_8", "category": "Drawers", "subcategory": "drawer", "name": "Tekform", "description": "Tekform", "type": "hardware", "basePrice": 36.33, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 8, "sourceCategory": "Drawers"}, {"id": "internal_drawers_3", "category": "Internal_Drawers", "subcategory": "drawer_box", "name": "Tandembox", "description": "Tandembox", "type": "hardware", "basePrice": 86, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 3, "sourceCategory": "Internal_Drawers"}, {"id": "internal_drawers_4", "category": "Internal_Drawers", "subcategory": "drawer_box", "name": "Legrabox", "description": "Legrabox", "type": "hardware", "basePrice": 133.84, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 4, "sourceCategory": "Internal_Drawers"}, {"id": "internal_drawers_5", "category": "Internal_Drawers", "subcategory": "drawer_box", "name": "Merivobox", "description": "Merivobox", "type": "hardware", "basePrice": 100.35, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 5, "sourceCategory": "Internal_Drawers"}, {"id": "internal_drawers_6", "category": "Internal_Drawers", "subcategory": "drawer", "name": "Multitech", "description": "Multitech", "type": "hardware", "basePrice": 28.05, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 6, "sourceCategory": "Internal_Drawers"}, {"id": "internal_drawers_7", "category": "Internal_Drawers", "subcategory": "drawer", "name": "Alto", "description": "Alto", "type": "hardware", "basePrice": 41.94, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 7, "sourceCategory": "Internal_Drawers"}, {"id": "internal_drawers_8", "category": "Internal_Drawers", "subcategory": "drawer", "name": "Tekform", "description": "Tekform", "type": "hardware", "basePrice": 61, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 8, "sourceCategory": "Internal_Drawers"}, {"id": "accessories_3", "category": "Accessories", "subcategory": "standard", "name": "Bifold", "description": "Bifold", "type": "hardware", "basePrice": 45, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 3, "sourceCategory": "Accessories"}, {"id": "accessories_4", "category": "Accessories", "subcategory": "standard", "name": "Liftup", "description": "Liftup", "type": "hardware", "basePrice": 20, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 4, "sourceCategory": "Accessories"}, {"id": "accessories_5", "category": "Accessories", "subcategory": "standard", "name": "Hanging Rail", "description": "Hanging Rail", "type": "hardware", "basePrice": 22, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 5, "sourceCategory": "Accessories"}, {"id": "accessories_6", "category": "Accessories", "subcategory": "standard", "name": "Mitred Boxed Ends", "description": "Mitred Boxed Ends", "type": "hardware", "basePrice": 91, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 6, "sourceCategory": "Accessories"}, {"id": "accessories_7", "category": "Accessories", "subcategory": "standard", "name": "Single Sided Boxed End", "description": "Single Sided Boxed End", "type": "hardware", "basePrice": 67, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 7, "sourceCategory": "Accessories"}], "laborRates": [{"id": "machining_cost_3", "category": "Machining Cost", "subcategory": "standard", "name": "Machining Cost", "description": "Machining Cost", "type": "labor", "basePrice": 13.6, "unitOfMeasure": "each", "specifications": {}, "sourceRow": 3, "sourceCategory": "Machining Cost"}], "regions": [{"id": 1, "regionCode": "AU_NATIONAL", "regionName": "Australia National", "costOfLivingMultiplier": 1, "taxRate": 0.1, "shippingMultiplier": 1, "marketConditions": "average", "seasonalAdjustment": 1}, {"id": 2, "regionCode": "AU_SYDNEY", "regionName": "Sydney Metro", "costOfLivingMultiplier": 1.25, "taxRate": 0.1, "shippingMultiplier": 1.1, "marketConditions": "high", "seasonalAdjustment": 1}, {"id": 3, "regionCode": "AU_MELBOURNE", "regionName": "Melbourne Metro", "costOfLivingMultiplier": 1.2, "taxRate": 0.1, "shippingMultiplier": 1.05, "marketConditions": "high", "seasonalAdjustment": 1}], "suppliers": [{"id": 1, "name": "LM Kitchen Supplies", "contactInfo": {"email": "<EMAIL>", "phone": "+61-xxx-xxx-xxx"}, "paymentTerms": "30 days", "leadTimeDays": 14, "minimumOrder": 500, "qualityRating": 4.5, "reliabilityRating": 4.8, "isActive": true}], "rawData": {"materials": [{"__EMPTY": "Material", "__EMPTY_2": "Waste Factor", "__EMPTY_3": "Length", "__EMPTY_4": "Height", "__EMPTY_5": "Labour", "__EMPTY_7": "Material", "__EMPTY_8": "Labour", "__EMPTY_10": "Material", "__EMPTY_11": "Labour", "__EMPTY_13": "Material", "__EMPTY_14": "Labour", "__EMPTY_16": "Material", "__EMPTY_17": "Labour", "__EMPTY_19": "Material", "__EMPTY_20": "Labour", "__EMPTY_22": "Material", "__EMPTY_23": "Labour", "__EMPTY_25": "Material", "__EMPTY_26": "Labour", "__EMPTY_28": "Material", "__EMPTY_29": "Labour", "__EMPTY_31": "Material", "__EMPTY_32": "Labour", "__EMPTY_35": "Material", "__EMPTY_36": "Labour"}, {"Board": "Material Costs:"}, {"Board": "None", "__EMPTY": 0, "__EMPTY_1": 0, "__EMPTY_2": 0, "__EMPTY_3": 0, "__EMPTY_4": 0, "__EMPTY_5": 0, "PVC": "1mm White PVC", "__EMPTY_7": 1.4, "__EMPTY_8": 0.59, "Handles": "Standard Handle", "__EMPTY_10": 13, "__EMPTY_11": 3.15, "Hinges": "107 degree", "__EMPTY_13": 6.96, "__EMPTY_14": 2.5, "Drawers": "Tandembox", "__EMPTY_16": 56.66, "__EMPTY_17": 27.445, "Internal_Drawers": "Tandembox", "__EMPTY_19": 86, "__EMPTY_20": 28.05, "Machining Cost": "Machining Cost", "__EMPTY_22": 0, "__EMPTY_23": 13.6, "Cabinets": "Base Cabinets (1)", "__EMPTY_25": 0, "__EMPTY_26": 22.8, "Panels": "Base Panels (1)", "__EMPTY_28": 0, "__EMPTY_29": 5, "Door Fitting": "Base Cabinets", "__EMPTY_31": 0, "__EMPTY_32": 5, "Yes/No": "No", "Accessories": "Bifold", "__EMPTY_35": 45, "__EMPTY_36": 20, "Who": "Bronwyn"}, {"Board": "16mm Carcase Wh MDF", "__EMPTY": 16.5, "__EMPTY_1": "$63.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "PVC": "1mm Colour PVC", "__EMPTY_7": 2.4, "__EMPTY_8": 0.59, "Handles": "Premium Handle", "__EMPTY_10": 16, "__EMPTY_11": 3.15, "Hinges": "155 degree", "__EMPTY_13": 14.46, "__EMPTY_14": 2.5, "Drawers": "Legrabox", "__EMPTY_16": 121.67, "__EMPTY_17": 34.705000000000005, "Internal_Drawers": "Legrabox", "__EMPTY_19": 133.84, "__EMPTY_20": 34.705000000000005, "Cabinets": "Wall Cabinets (1) (Allowing for underpanel)", "__EMPTY_25": 0, "__EMPTY_26": 28.5, "Panels": "Base Panels (2)", "__EMPTY_28": 0, "__EMPTY_29": 5, "Door Fitting": "Wall Cabinets", "__EMPTY_31": 0, "__EMPTY_32": 5, "Yes/No": "Yes", "Accessories": "Liftup", "__EMPTY_35": 20, "__EMPTY_36": 10, "Who": "<PERSON><PERSON>"}, {"Board": "16mm Mel <PERSON> 1", "__EMPTY": 16.5, "__EMPTY_1": "$130.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "PVC": "1mm Woodgrain PVC", "__EMPTY_7": 2.4, "__EMPTY_8": 0.59, "Handles": "Deluxe Handle", "__EMPTY_10": 25, "__EMPTY_11": 3.15, "Hinges": "155 THIN", "__EMPTY_13": 22.16, "__EMPTY_14": 2.5, "Drawers": "Merivobox", "__EMPTY_16": 80.52, "__EMPTY_17": 27.445, "Internal_Drawers": "Merivobox", "__EMPTY_19": 100.35, "__EMPTY_20": 28.05, "Cabinets": "Tall Cabinets (1)", "__EMPTY_25": 0, "__EMPTY_26": 39, "Panels": "Tall Panels (1)", "__EMPTY_28": 0, "__EMPTY_29": 5, "Door Fitting": "Tall Cabinets", "__EMPTY_31": 0, "__EMPTY_32": 25, "Accessories": "Hanging Rail", "__EMPTY_35": 22, "__EMPTY_36": 5, "Who": "<PERSON>"}, {"Board": "18mm Mel <PERSON> 1", "__EMPTY": 18.5, "__EMPTY_1": "$136.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "PVC": "1mm Acrylic", "__EMPTY_7": 3.7, "__EMPTY_8": 0.59, "Handles": "Designer <PERSON><PERSON>", "__EMPTY_10": 50, "__EMPTY_11": 3.15, "Hinges": "None", "__EMPTY_13": 0, "__EMPTY_14": 0, "Drawers": "Multitech", "__EMPTY_16": 25.5, "__EMPTY_17": 24.618000000000002, "Internal_Drawers": "Multitech", "__EMPTY_19": 21.85, "__EMPTY_20": 28.05, "Cabinets": "Division (1)", "__EMPTY_25": 0, "__EMPTY_26": 5.25, "Panels": "Tall Panels (2)", "__EMPTY_28": 0, "__EMPTY_29": 5, "Accessories": "Mitred Boxed Ends", "__EMPTY_35": 19.2, "__EMPTY_36": 91}, {"Board": "16mm Wood Grain 1", "__EMPTY": 16.5, "__EMPTY_1": "$130.00", "__EMPTY_2": 0.15, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "PVC": "0.6mm <PERSON><PERSON><PERSON>", "__EMPTY_7": 4, "__EMPTY_8": 0.59, "Handles": "None", "__EMPTY_10": 0, "__EMPTY_11": 0, "Drawers": "Alto", "__EMPTY_16": 34.95, "__EMPTY_17": 29.865000000000002, "Internal_Drawers": "Alto", "__EMPTY_19": 41.94, "__EMPTY_20": 28.05, "Cabinets": "Floating Shelves (1)", "__EMPTY_25": 0, "__EMPTY_26": 25.5, "Panels": "Wall Panels (1)", "__EMPTY_28": 0, "__EMPTY_29": 5, "Accessories": "Single Sided Boxed End", "__EMPTY_35": 12, "__EMPTY_36": 67}, {"Board": "18mm Wood Grain 1", "__EMPTY": 18.5, "__EMPTY_1": "$136.00", "__EMPTY_2": 0.15, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "PVC": "1mm HiGloss PVC", "__EMPTY_7": 2.6, "__EMPTY_8": 0.59, "Handles": "Sharknose", "__EMPTY_10": 0, "__EMPTY_11": 30, "Drawers": "Tekform", "__EMPTY_16": 36.33, "__EMPTY_17": 29.865000000000002, "Internal_Drawers": "Tekform", "__EMPTY_19": 61, "__EMPTY_20": 28.05, "Cabinets": "Base Cabinets (2)", "__EMPTY_25": 0, "__EMPTY_26": 22.8, "Panels": "Wall Panels (2)", "__EMPTY_28": 0, "__EMPTY_29": 5, "Yes/No": "Unit Type"}, {"Board": "16mm Wood Grain #1 3.6", "__EMPTY": 16.5, "__EMPTY_1": 200, "__EMPTY_2": 0.15, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "PVC": "None", "__EMPTY_7": 0, "__EMPTY_8": 0, "Drawers": "None", "__EMPTY_16": 0, "__EMPTY_17": 0, "Internal_Drawers": "None", "__EMPTY_19": 0, "__EMPTY_20": 0, "Cabinets": "Wall Cabinets (2) (Allowing for underpanel)", "__EMPTY_25": 0, "__EMPTY_26": 14.25, "Panels": "Base Scribers (1)", "__EMPTY_28": 0, "__EMPTY_29": 5, "Yes/No": "Base Units"}, {"Board": "18mm Wood Grain #1 3.6", "__EMPTY": 18.5, "__EMPTY_1": "$205.00", "__EMPTY_2": 0.15, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "Cabinets": "Tall Cabinets (2)", "__EMPTY_25": 0, "__EMPTY_26": 39, "Panels": "Base Scribers (2)", "__EMPTY_28": 0, "__EMPTY_29": 5, "Yes/No": "Tall Units"}, {"Board": "18mm Paint", "__EMPTY": 18.5, "__EMPTY_1": "$75.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "Cabinets": "Floating Shelves (2)", "__EMPTY_25": 0, "__EMPTY_26": 25.5, "Panels": "<PERSON> (1)", "__EMPTY_28": 0, "__EMPTY_29": 5, "Yes/No": "Wall Units"}, {"Board": "18mm Paint 3.6", "__EMPTY": 18.5, "__EMPTY_1": "$88.00", "__EMPTY_2": 0.1, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "Panels": "<PERSON> (2)", "__EMPTY_28": 0, "__EMPTY_29": 5}, {"Board": "12.5mm White", "__EMPTY": 12.5, "__EMPTY_1": "$58.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "Panels": "Back Panels (1)", "__EMPTY_28": 0, "__EMPTY_29": 5}, {"Board": "12.5mm Ply", "__EMPTY": 12.5, "__EMPTY_1": "$200.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "Panels": "Back Panels (2)", "__EMPTY_28": 0, "__EMPTY_29": 5}, {"Board": "12mm MDF", "__EMPTY": 12, "__EMPTY_1": "$44.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "Panels": "Double Sided Base Panels (Lacquer Only) (1)", "__EMPTY_28": 0, "__EMPTY_29": 5}, {"Board": "12mm Paint", "__EMPTY": 12.5, "__EMPTY_1": "$46.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "Panels": "Double Sided Base Panels (Lacquer Only) (2)", "__EMPTY_28": 0, "__EMPTY_29": 5}, {"Board": "12mm Veneer 1", "__EMPTY": 12, "__EMPTY_1": "$180.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "Panels": "Double Sided Tall Panels (Lacquer Only) (1)", "__EMPTY_28": 0, "__EMPTY_29": 5}, {"Board": "12mm Veneer 1 2.7", "__EMPTY": 12, "__EMPTY_1": "$170.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "Panels": "Double Sided Tall Panels (Lacquer Only) (2)", "__EMPTY_28": 0, "__EMPTY_29": 5}, {"Board": "13mm Compact Col 1", "__EMPTY": 13, "__EMPTY_1": "$1,250.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "Panels": "Double Sided Wall Panels (Lacquer Only) (1)", "__EMPTY_28": 0, "__EMPTY_29": 5}, {"Board": "13mm Plywood", "__EMPTY": 13, "__EMPTY_1": "$60.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "Panels": "Double Sided Wall Panels (Lacquer Only) (2)", "__EMPTY_28": 0, "__EMPTY_29": 5}, {"Board": "15.5mm Scrap Materia!", "__EMPTY": 15.5, "__EMPTY_1": "$10.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "Panels": "Double Sided Back Panels (Lacquer Only) (1)", "__EMPTY_28": 0, "__EMPTY_29": 5}, {"Board": "16mm Anthracite", "__EMPTY": 16.5, "__EMPTY_1": "$113.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "Panels": "Double Sided Back Panels (Lacquer Only) (2)", "__EMPTY_28": 0, "__EMPTY_29": 5}, {"Board": "16mm Carcase Grey MDF", "__EMPTY": 16.5, "__EMPTY_1": "$78.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "Panels": "Toekicks (1)", "__EMPTY_28": 0, "__EMPTY_29": 2}, {"Board": "16mm Carcase Grey PB", "__EMPTY": 16.5, "__EMPTY_1": "$73.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "Panels": "Toekicks (2)", "__EMPTY_28": 0, "__EMPTY_29": 2}, {"Board": "16mm Carcase Wh 3.6", "__EMPTY": 16.5, "__EMPTY_1": "$113.00", "__EMPTY_2": 0.1, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "Panels": "Fascia (1)", "__EMPTY_28": 0, "__EMPTY_29": 2}, {"Board": "16mm Carcase Wh PB", "__EMPTY": 16.5, "__EMPTY_1": "$63.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5, "Panels": "Fascia (2)", "__EMPTY_28": 0, "__EMPTY_29": 2}, {"Board": "16mm HPL Black Ply", "__EMPTY": 16, "__EMPTY_1": "$114.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "16mm HPL Supawhite", "__EMPTY": 16, "__EMPTY_1": "$87.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "16mm MDF", "__EMPTY": 16.5, "__EMPTY_1": "$63.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "16mm Mel col 2", "__EMPTY": 16.5, "__EMPTY_1": "$130.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "16mm Mel col 3", "__EMPTY": 16.5, "__EMPTY_1": "$130.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "16mm Mel col1 PB", "__EMPTY": 16.5, "__EMPTY_1": "$106.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "16mm Paint", "__EMPTY": 16.5, "__EMPTY_1": "$63.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "16mm Plastic Board", "__EMPTY": 16.5, "__EMPTY_1": "$170.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "16mm Plastic Board NP", "__EMPTY": 16.5, "__EMPTY_1": "$170.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "16mm Rift 1", "__EMPTY": 16.5, "__EMPTY_1": "$117.00", "__EMPTY_2": 0.15, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "16mm Rift 2", "__EMPTY": 16.5, "__EMPTY_1": "$117.00", "__EMPTY_2": 0.15, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "16mm Scrap Material", "__EMPTY": 16.5, "__EMPTY_1": "$10.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "16mm Wood Grain 1", "__EMPTY": 16.5, "__EMPTY_1": "$130.00", "__EMPTY_2": 0.15, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "16mm Wood Grain 3", "__EMPTY": 16.5, "__EMPTY_1": "$130.00", "__EMPTY_2": 0.15, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "16mm Scrap Material", "__EMPTY": 16.5, "__EMPTY_1": "$10.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "17.5UltGlaze Col #1", "__EMPTY": 17.5, "__EMPTY_1": "$360.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "17.5UltGlaze Col #2", "__EMPTY": 17.5, "__EMPTY_1": "$360.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "17.5UltGlaze Col #3", "__EMPTY": 17.5, "__EMPTY_1": "$360.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "17.5UltGlaze Col #4", "__EMPTY": 17.5, "__EMPTY_1": "$360.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "17.5UltGlaze Col#1  2.7", "__EMPTY": 17.5, "__EMPTY_1": "$410.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "17.5UltGlaze Col#3 2.7", "__EMPTY": 17.5, "__EMPTY_1": "$410.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "17mm Veneer 1", "__EMPTY": 17.5, "__EMPTY_1": "$230.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "17mm Veneer 2", "__EMPTY": 17.5, "__EMPTY_1": "$1.000.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "17UltGlaze Metalic Coll", "__EMPTY": 17.5, "__EMPTY_1": "$320.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18.4mm FM AR + Laminate", "__EMPTY": 18.4, "__EMPTY_1": "$450.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18.5mm Hoop Ply", "__EMPTY": 18.5, "__EMPTY_1": "$280.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18.5mm Ply", "__EMPTY": 18.5, "__EMPTY_1": "$280.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18MeltGIFiniChocolate", "__EMPTY": 18.5, "__EMPTY_1": "$151.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18MeltGIFlefinedOak", "__EMPTY": 18.5, "__EMPTY_1": "$151.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18MeltGIMistGrey", "__EMPTY": 18.5, "__EMPTY_1": "$151.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18MeltGISnowdrift", "__EMPTY": 18.5, "__EMPTY_1": "$151.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18MeltGIVicenzaWalnut", "__EMPTY": 18.5, "__EMPTY_1": "$151.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18MeltGIWamnWhite", "__EMPTY": 18.5, "__EMPTY_1": "$151.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm Birch Elite PF", "__EMPTY": 18.4, "__EMPTY_1": "$470.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm Carcase Wh PB", "__EMPTY": 18.5, "__EMPTY_1": "$65.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm HPL Black Ply", "__EMPTY": 18, "__EMPTY_1": "$124.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm HPL Futura #1", "__EMPTY": 18, "__EMPTY_1": "$380.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm HPL Futura #3", "__EMPTY": 18, "__EMPTY_1": "$380.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm HPL Futura Mat #1", "__EMPTY": 18, "__EMPTY_1": "$505.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm HPL Italia PLY 1", "__EMPTY": 18.5, "__EMPTY_1": "$315.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm HPL Italia PLY 2", "__EMPTY": 18.5, "__EMPTY_1": "$315.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm HPL Supawhite Ply", "__EMPTY": 18, "__EMPTY_1": "$107.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm Lam Acrylic Col#1", "__EMPTY": 18, "__EMPTY_1": "$427.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm Lam Acrylic Col#2", "__EMPTY": 18, "__EMPTY_1": "$427.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm Lam Acrylic Col#3", "__EMPTY": 18, "__EMPTY_1": "$427.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm Lam Acrylic Col#3", "__EMPTY": 18, "__EMPTY_1": "$427.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm Lam Acrylic WG #1", "__EMPTY": 18, "__EMPTY_1": "$427.00", "__EMPTY_2": 0.15, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm Lam Acrylic WG#1", "__EMPTY": 18, "__EMPTY_1": "$427.00", "__EMPTY_2": 0.15, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm MDF", "__EMPTY": 18.5, "__EMPTY_1": "$76.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm MDF 3.6", "__EMPTY": 18.5, "__EMPTY_1": "$88.00", "__EMPTY_2": 0.1, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm <PERSON> 2", "__EMPTY": 18.5, "__EMPTY_1": "$136.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm <PERSON> 2 3.6", "__EMPTY": 18.5, "__EMPTY_1": "$205.00", "__EMPTY_2": 0.1, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm Mel Col 3", "__EMPTY": 18.5, "__EMPTY_1": "$136.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm <PERSON> 3 3.6", "__EMPTY": 18.5, "__EMPTY_1": "$205.00", "__EMPTY_2": 0.1, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm PA 2.4 Col#1", "__EMPTY": 18, "__EMPTY_1": "$330.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm PA 2.4 Col#2", "__EMPTY": 18, "__EMPTY_1": "$330.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm PA 2.4 Col#3", "__EMPTY": 18, "__EMPTY_1": "$330.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm PA 2.4 Metal#1", "__EMPTY": 18, "__EMPTY_1": "$380.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm PA 2.4 Metal#3", "__EMPTY": 18, "__EMPTY_1": "$380.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm PA 2.7 Col#1", "__EMPTY": 18, "__EMPTY_1": "$410.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm PA 2.7 Col#3", "__EMPTY": 18, "__EMPTY_1": "$410.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm PA 2.7 Met Col#1", "__EMPTY": 18, "__EMPTY_1": "$450.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm PA 2.7 Met Col#3", "__EMPTY": 18, "__EMPTY_1": "$450.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm Paint", "__EMPTY": 18.5, "__EMPTY_1": "$75.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm Paint 3.6", "__EMPTY": 18.5, "__EMPTY_1": "$88.00", "__EMPTY_2": 0.1, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm P<PERSON> #1", "__EMPTY": 18.5, "__EMPTY_1": "$675.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm Wood Grain 2", "__EMPTY": 18.5, "__EMPTY_1": "$136.00", "__EMPTY_2": 0.15, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm Wood Grain 3", "__EMPTY": 18.5, "__EMPTY_1": "$136.00", "__EMPTY_2": 0.15, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm Wood Grain #2 3.6", "__EMPTY": 18.5, "__EMPTY_1": "$205.00", "__EMPTY_2": 0.15, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mm Wood Grain #3 3.6", "__EMPTY": 18.5, "__EMPTY_1": "$205.00", "__EMPTY_2": 0.15, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mmArborlineGlossWhtCr", "__EMPTY": 18.5, "__EMPTY_1": "$0.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mmArborlineStd", "__EMPTY": 18.5, "__EMPTY_1": "$0.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mnn Plymasters <PERSON><PERSON><PERSON>", "__EMPTY_1": 726, "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18mnn Wood Grain 2", "__EMPTY": 16.5, "__EMPTY_1": "$130.00", "__EMPTY_2": 0.15, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "18nrinnArborlineGlossOther", "__EMPTY": 18.5, "__EMPTY_1": "$0.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "19mm FIPL Polaris Ply #1", "__EMPTY": 19, "__EMPTY_1": "$820.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "19mm FIPL Polaris Ply #2", "__EMPTY": 19, "__EMPTY_1": "$820.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "19mm Veneer 1", "__EMPTY": 19, "__EMPTY_1": "$230.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "19mm Veneer 1 2.8", "__EMPTY": 19, "__EMPTY_1": "$510.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2800, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "19mm Veneer 2", "__EMPTY": 19, "__EMPTY_1": "$230.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "19mm Veneer 2 2.8", "__EMPTY": 19, "__EMPTY_1": "$510.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2800, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "19mm Veneer Rustica", "__EMPTY_1": 726, "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "19mm Veneer Rustica", "__EMPTY": 19, "__EMPTY_1": "$726.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "1BMeltGIAntWilunaWhite", "__EMPTY": 18.5, "__EMPTY_1": "$151.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "1BMeltGIBirch", "__EMPTY": 18.5, "__EMPTY_1": "$151.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "24mm Hoop Pine", "__EMPTY_1": 355, "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "24mm HPL Futura  PLY #1 ", "__EMPTY_1": 30, "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm Carcase Wh MDF ", "__EMPTY_1": 85, "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm Hoop Pine Ply  ", "__EMPTY_1": 355, "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm Lam Acrylic WG #3 ", "__EMPTY_1": 435, "__EMPTY_2": 0.15, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm MDF", "__EMPTY_1": 76, "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm Mel <PERSON> #1 3.6", "__EMPTY_1": 267, "__EMPTY_2": 0.1, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm Mel <PERSON> #2 3.6 ", "__EMPTY_1": 267, "__EMPTY_2": 0.1, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm Mel <PERSON> #3 3.6 ", "__EMPTY_1": 267, "__EMPTY_2": 0.1, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm Mel <PERSON> 1 ", "__EMPTY_1": 170, "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm <PERSON> 2", "__EMPTY_1": 170, "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm Mel Col 3", "__EMPTY_1": 170, "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm Paint", "__EMPTY_1": 90, "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm Paint 3.6", "__EMPTY_1": 130, "__EMPTY_2": 0.1, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm Wood Grain #1 3.6", "__EMPTY_1": 267, "__EMPTY_2": 0.15, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm Wood Grain #2 3.6", "__EMPTY_1": 267, "__EMPTY_2": 0.15, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm Wood Grain #3 3.6", "__EMPTY_1": 267, "__EMPTY_2": 0.15, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm Wood Grain 1 ", "__EMPTY_1": 170, "__EMPTY_2": 0.15, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm Wood Grain 2", "__EMPTY_1": 170, "__EMPTY_2": 0.15, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "25mm Wood Grain 3 ", "__EMPTY_1": 170, "__EMPTY_2": 0.15, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "26mm Lam Acrylic #3 ", "__EMPTY_1": 435, "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "26mm Veneer 1", "__EMPTY_1": 290, "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "27mm PA 2.4 Col#1", "__EMPTY_1": 420, "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "27mm PA 2.4 Col#3", "__EMPTY_1": 420, "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "27mm PA 2.4 Metal#1 ", "__EMPTY_1": 440, "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "27mm PA 2.4 Metal#3", "__EMPTY_1": 440, "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "27mm PA 2.7 Col#1", "__EMPTY_1": 435, "__EMPTY_2": 0.1, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "30mm MDF", "__EMPTY_1": 90, "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "30mm Mel <PERSON> 1 ", "__EMPTY_1": 198, "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "30mm <PERSON> 2", "__EMPTY": 30.5, "__EMPTY_1": "$198.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "30mm Mel Col 3", "__EMPTY": 30.5, "__EMPTY_1": "$198.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "30mm Paint", "__EMPTY": 30.5, "__EMPTY_1": "$99.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "30mm Paint 3.0", "__EMPTY": 30.5, "__EMPTY_1": "$171.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "30mm Paint 3.6", "__EMPTY_1": 171, "__EMPTY_2": 0.1, "__EMPTY_3": 3600, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "30mm Rift 1", "__EMPTY": 30.5, "__EMPTY_1": "$180.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "30mm Rift 2", "__EMPTY": 30.5, "__EMPTY_1": "$180.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "30mm Wood Grain 1", "__EMPTY": 30.5, "__EMPTY_1": "$198.00", "__EMPTY_2": 0.15, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "30mm Wood Grain 2", "__EMPTY_1": "$198.00", "__EMPTY_2": 0.15, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "30mm Wood Grain 3", "__EMPTY": 30.5, "__EMPTY_1": "$198.00", "__EMPTY_2": 0.15, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "31mm Veneer 1", "__EMPTY": 31, "__EMPTY_1": "$270.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "32mm PA 2.4 Col#1", "__EMPTY": 32, "__EMPTY_1": "$435.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "32mm PA 2.4 Col#3", "__EMPTY": 32, "__EMPTY_1": "$435.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "32mm PA 2.4 Metal#2", "__EMPTY": 32, "__EMPTY_1": "$460.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "32mm PA 2.4 Metal#3", "__EMPTY": 32, "__EMPTY_1": "$460.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "32mm PA 2.7 Col#1", "__EMPTY": 32, "__EMPTY_1": "$455.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "32mm PA 2.7 Col#3", "__EMPTY": 32, "__EMPTY_1": "$455.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "32mm PA 2.7 MET Col#1", "__EMPTY": 32, "__EMPTY_1": "$490.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "32mm PA 2.7 MET Col#3", "__EMPTY": 32, "__EMPTY_1": "$490.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2700, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "33mm DSW Paint", "__EMPTY": 33, "__EMPTY_1": "$130.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "35mm HPL Futura PLY #1", "__EMPTY": 35, "__EMPTY_1": "$450.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "3mm MDF", "__EMPTY": 3, "__EMPTY_1": "$36.00", "__EMPTY_2": 0.1, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}, {"Board": "41mm Veneer", "__EMPTY": 41, "__EMPTY_1": "$1.000.00", "__EMPTY_2": 0.2, "__EMPTY_3": 2400, "__EMPTY_4": 1200, "__EMPTY_5": 15.5}], "master": [[null, "Lineal Mtr Calculator 3.2 30/04/25", "Pricing Date:", "XXXXXX", "Job Number:", "XXXXXX", "Priced By:", "Bronwyn"], [], [null, null, "Height", "UNIT Depth", "Panel Depth"], [null, "Base Units", 880, 580, 580], [null, "Tall Units", 2300, 580, 690], [null, "Wall Units", 750, 400, 400], [], [null, null, null, "Material Cost", "Labour / Machining Cost", "Sheet <PERSON>ze <PERSON>", "<PERSON>et <PERSON>", "Waste Factor"], [1, "Carcase Material (1)", "16mm Carcase Wh MDF", "$63.00", 15.5, 2400, 1200, 0.1], [1, "Carcase Material (2)", "16mm Carcase Wh MDF", "$63.00", 15.5, 2400, 1200, 0.1], [1, "Carcase Edging (1)", "1mm White PVC", 1.4, 0.59], [1, "Carcase Edging (2)", "1mm White PVC", 1.4, 0.59], [1, "Front Set Material (1)", "16mm Mel <PERSON> 1", "$130.00", 15.5, 2400, 1200, 0.1], [1, "Front Set Material (2)", "16mm Mel <PERSON> 1", "$130.00", 15.5, 2400, 1200, 0.1], [1, "Front Set Edging (1)", "1mm Colour PVC", 2.4, 0.59], [1, "Front Set Edging (2)", "1mm Woodgrain PVC", 2.4, 0.59], [1, "Drawer Type (1)", "Tandembox", 56.66, 27.445], [1, "Drawer Type (2)", "Tandembox", 56.66, 27.445], [1, "Drawer Base (1)", "16mm Carcase Wh MDF", "$63.00", 15.5, 2400, 1200, 0.1], [1, "Drawer Base (2)", "16mm Carcase Wh MDF", "$63.00", 15.5, 2400, 1200, 0.1], [1, "Internal Drawer Type (1)", "Tandembox", 86, 28.05], [1, "Internal Drawer Type (2)", "Tandembox", 86, 28.05], [1, "Hinge Type (1)", "107 degree", 6.96, 2.5], [1, "Hinge Type (2)", "155 degree", 14.46, 2.5], [1, "Handle Type (1)", "Standard Handle", 13, 3.15], [1, "Handle Type (2)", "Deluxe Handle", 25, 3.15], [], [null, "Description:", "Height", "<PERSON><PERSON><PERSON>", "De<PERSON><PERSON>", "Qty of shelves (Per Unit)", "Qty of Doors (Total)", "Qty of Drawer Fronts (Total)", "Qty of divisions (Per Unit)", "Qty of items", "Open Unit ?", "No Back", "Lacquer Sqm", "<PERSON>quer to how many sides", "Carcase Board (1)", "Carcase Board (2)", "Front set board (1)", "Front set board (2)", "Material Cost (1)", "Material Cost (2)", "Assembly Cost", "Machining Cost Carcase (1)", "Machining Cost Carcase (2)", "Machining Cost Front Set (1)", "Machining Cost Front Set (2)", "Cubic Volume", "Carcase PVC (1)", "Carcase PVC (2)", "Front Set PVC (1)", "Front Set PVC (2)", "Edging Labour Carcase (1)", "Edging Labour Carcase (2)", "Edging Labour Front Set (1)", "Edging Labour Front Set (2)", "Lacquer SqM (1)", "Lacquer SqM (2)", "Accesory Materials", "Accesory Labour", "Open Lacquer CuM"], [1, "Base Cabinets (1)", 880, 0, 580, null, null, null, null, null, "No", "No", "No", 2, 0, null, 0, null, null, null, 0, 0, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null, null, null, 0], [1, "Base Cabinets (2)", 880, 0, 580, null, null, null, null, null, "No", "No", "No", 2, null, 0, null, 0, null, null, 0, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null, 0, null, null, 0], [1, "Tall Cabinets (1)", 2300, 0, 580, null, null, null, null, null, "No", "No", "No", 2, 0, null, 0, null, null, null, 0, 0, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null, null, null, 0], [1, "Tall Cabinets (2)", 2300, 0, 580, null, null, null, null, null, "No", "No", "No", 2, null, 0, null, 0, null, null, 0, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null, 0, null, null, 0], [1, "Wall Cabinets (1) (Allowing for underpanel)", 750, 0, 400, null, null, null, null, null, "No", "No", "No", 2, 0, null, 0, null, null, null, 0, 0, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null, null, null, 0], [1, "Wall Cabinets (2) (Allowing for underpanel)", 750, 0, 400, null, null, null, null, null, "No", "No", "No", 2, null, 0, null, 0, null, null, 0, null, 0, null, 0, 0, null, 0, null, 0, null, 0, null, 0, null, 0, null, null, 0], [1, "Base Panels (1)", 880, 0, 580, null, null, null, null, null, null, null, "No", 2, null, null, 0, null, null, null, 0, 0, null, 0, null, 0, null, null, 0, null, null, null, 0, null, 0], [1, "Base Panels (2)", 880, 0, 580, null, null, null, null, null, null, null, "No", 2, null, null, null, 0, null, null, 0, null, 0, null, 0, 0, null, null, null, 0, null, null, null, 0, null, 0], [1, "Base Scribers (1)", 880, 0, 100, null, null, null, null, null, null, null, "No", 2, null, null, 0, null, null, null, 0, 0, null, 0, null, 0, null, null, 0, null, null, null, 0, null, 0], [1, "Base Scribers (2)", 880, 0, 100, null, null, null, null, null, null, null, "No", 2, null, null, null, 0, null, null, 0, null, 0, null, 0, 0, null, null, null, 0, null, null, null, 0, null, 0], [1, "Tall Panels (1)", 2300, 0, 690, null, null, null, null, null, null, null, "No", 2, null, null, 0, null, null, null, 0, 0, null, 0, null, 0, null, null, 0, null, null, null, 0, null, 0], [1, "Tall Panels (2)", 2300, 0, 690, null, null, null, null, null, null, null, "No", 2, null, null, null, 0, null, null, 0, null, 0, null, 0, 0, null, null, null, 0, null, null, null, 0, null, 0], [1, "<PERSON> (1)", 2300, 0, 100, null, null, null, null, null, null, null, "No", 2, null, null, 0, null, null, null, 0, 0, null, 0, null, 0, null, null, 0, null, null, null, 0, null, 0], [1, "<PERSON> (2)", 2300, 0, 100, null, null, null, null, null, null, null, "No", 2, null, null, null, 0, null, null, 0, null, 0, null, 0, 0, null, null, null, 0, null, null, null, 0, null, 0], [1, "Wall Panels (1)", 750, 0, 400, null, null, null, null, null, null, null, "No", 2, null, null, 0, null, null, null, 0, 0, null, 0, null, 0, null, null, 0, null, null, null, 0, null, 0], [1, "Wall Panels (2)", 750, 0, 400, null, null, null, null, null, null, null, "No", 2, null, null, null, 0, null, null, 0, null, 0, null, 0, 0, null, null, null, 0, null, null, null, 0, null, 0], [1, "Back Panels (1)", 880, 0, 16.5, null, null, null, null, null, null, null, "No", 1, null, null, 0, null, null, null, 0, 0, null, 0, null, 0, null, null, 0, null, null, null, 0, null, 0], [1, "Back Panels (2)", 880, 0, 16.5, null, null, null, null, null, null, null, "No", 1, null, null, null, 0, null, null, 0, null, 0, null, 0, 0, null, null, null, 0, null, null, null, 0, null, 0], [1, "Toekicks (1)", 120, 0, 2400, null, null, null, null, null, null, null, "No", 1, null, null, 0, null, null, null, 0, 0, null, 0, null, 0, null, null, null, null, null, null, null, null, 0], [1, "Toekicks (2)", 120, 0, 2400, null, null, null, null, null, null, null, "No", 1, null, null, null, 0, null, null, 0, null, 0, null, 0, 0, null, null, null, null, null, null, null, null, null, 0], [1, "Fascia (1)", 120, 0, 2400, null, null, null, null, null, null, null, "No", 1, null, null, 0, null, null, null, 0, 0, null, 0, null, 0, null, null, null, null, null, null, null, null, 0], [1, "Fascia (2)", 120, 0, 2400, null, null, null, null, null, null, null, "No", 1, null, null, null, 0, null, null, 0, null, 0, null, 0, 0, null, null, null, null, null, null, null, null, null, 0], [1, "Floating Shelves (1)", 50, 0, 300, null, null, null, null, null, null, null, "No", 2, null, null, 0, null, null, null, 0, 0, null, 0, null, 0, null, null, null, null, null, null, null, null, 0], [1, "Floating Shelves (2)", 50, 0, 300, null, null, null, null, null, null, null, "No", 2, null, null, null, 0, null, null, 0, null, 0, null, 0, 0, null, null, null, null, null, null, null, null, null, 0], [1, "Drawers (1)", 0, 0, 500, null, null, null, null, null, null, null, null, null, 0, null, null, null, 0, null, 0, 0, null, 0, null, 0], [1, "Drawers (2)", 0, 0, 500, null, null, null, null, null, null, null, null, null, null, 0, null, null, null, 0, 0, null, 0, 0, null, 0], [1, "Internal Drawers (1)", 0, 0, 500, null, null, null, null, null, null, null, null, null, 0, null, null, null, 0, null, 0, 0, null, 0, null, 0], [1, "Internal Drawers (2)", 0, 0, 500, null, null, null, null, null, null, null, null, null, null, 0, null, null, null, 0, 0, null, 0, 0, null, 0], [1, "Hinges (1)", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, 0, null, null, 0, null, 0], [1, "Hinges (2)", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, null, null, 0, null, 0], [1, "Handles (1)", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, 0, null, null, 0, null, 0], [1, "<PERSON><PERSON> (2)", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, null, null, 0, null, 0], [null, "<PERSON><PERSON><PERSON> (Per Pair)", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0], [null, "Lift up Flap", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0], [null, "Hanging Rail per LM", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0], [null, "Mitred Boxed Ends", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0], [null, "Single Sided Box Ends", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0], [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0], [], [null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0], [null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0], [], [], [null, null, "Materials", null, "Labour", null, "Notes:"], [null, "Colour 1", 0, null, 0], [null, "Colour 2", 0, null, 0], [null, "Total:", 0, null, 0], [null, "Cubic M:", null, null, 0], [], [null, "Sheet Count <PERSON>", null, null, 0], [null, "Sheet Count Front Set", null, null, 0], [null, "Labour Minutes", null, null, 0], [null, "Unit Count", null, null, 0], [null, "Panel Count", null, null, 0], [null, "Lacquer SqM (1)", null, null, 0], [null, "Lacquer SqM (2)", null, null, 0], [], [null, "QA Check:", null, null, "Is there ? :"], [null, "Base Cabinets ", null, null, "No"], [null, "Tall Cabinets ", null, null, "No"], [null, "Wall Cabinets  (Allowing for underpanel)", null, null, "No"], [null, "Base Panels ", null, null, "No"], [null, "Base Scribers ", null, null, "No"], [null, "Tall Panels ", null, null, "No"], [null, "Tall Scribers ", null, null, "No"], [null, "Wall Panels ", null, null, "No"], [null, "Back Panels ", null, null, "No"], [null, "<PERSON><PERSON> allowed for ?", null, null, "No"], [null, "Toekicks ", null, null, "No"], [null, "Fascia", null, null, "No"], [null, "Floating Shelves ", null, null, "No"], [null, "Drawers ", null, null, "No"], [null, "Internal Drawers ", null, null, "No"], [null, "Hinges ", null, null, "No"], [null, "<PERSON><PERSON> ", null, null, "No"], [null, "<PERSON><PERSON><PERSON> (Per Pair)", null, null, "No"], [null, "Lift up Flap", null, null, "No"], [null, "Hanging Rail per LM", null, null, "No"], [null, "Mitred Boxed Ends", null, null, "No"], [null, "Single Sided Box Ends", null, null, "No"]]}}