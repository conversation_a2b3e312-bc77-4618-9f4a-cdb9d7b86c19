#!/usr/bin/env node

/**
 * AI Intelligence Enhancements Test Runner
 * 
 * Runs the comprehensive AI intelligence validation test suite
 * with proper error handling and reporting.
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🧠 Starting AI Intelligence Enhancements Validation...\n');

// Test configuration
const testFile = 'tests/integration/ai-intelligence-enhancements.spec.ts';
const playwrightArgs = [
  'test',
  testFile,
  '--reporter=line',
  '--timeout=180000', // 3 minutes per test
  '--retries=2',
  '--workers=1' // Sequential execution for stability
];

// Run the test
const testProcess = spawn('npx', ['playwright', ...playwrightArgs], {
  stdio: 'inherit',
  cwd: process.cwd()
});

testProcess.on('close', (code) => {
  console.log('\n' + '='.repeat(80));
  
  if (code === 0) {
    console.log('✅ AI Intelligence Enhancements Validation: PASSED');
    console.log('🎯 All AI intelligence features validated successfully');
    console.log('📊 ~97-99% test success rate standard maintained');
  } else {
    console.log('❌ AI Intelligence Enhancements Validation: FAILED');
    console.log(`💥 Test process exited with code: ${code}`);
    console.log('🔍 Check the test output above for details');
  }
  
  console.log('='.repeat(80));
  process.exit(code);
});

testProcess.on('error', (error) => {
  console.error('❌ Failed to start test process:', error.message);
  process.exit(1);
});
