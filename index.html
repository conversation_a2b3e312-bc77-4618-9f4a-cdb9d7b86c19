<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <title>Blackveil Design Mind</title>
    <meta name="description" content="AI-powered kitchen design analysis platform with mobile optimization and PWA capabilities" />
    <meta name="author" content="Blackveil" />

    <!-- PWA Configuration -->
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#6B7A4F" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Blackveil Design Mind" />
    <meta name="mobile-web-app-capable" content="yes" />

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/favicon-16x16.png" />

    <!-- Performance Optimization -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="dns-prefetch" href="//localhost:3001" />

    <!-- Development: Suppress browser extension warnings -->
    <script>
      // Suppress source map warnings and browser extension noise
      if (typeof window !== 'undefined') {
        const originalConsoleWarn = console.warn;
        console.warn = function(...args) {
          const message = args.join(' ');
          if (message.includes('Source map') ||
              message.includes('installHook.js') ||
              message.includes('passkeys.js') ||
              message.includes('nordpass-script.js') ||
              message.includes('calendly.js') ||
              message.includes('__REACT_DEVTOOLS_GLOBAL_HOOK__')) {
            return;
          }
          originalConsoleWarn.apply(console, args);
        };
      }
    </script>

    <meta property="og:title" content="Blackveil Design Mind" />
    <meta property="og:description" content="AI-powered kitchen design analysis platform with mobile optimization and PWA capabilities" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@blackveil_design" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />
  </head>

  <body>
    <div id="root"></div>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
