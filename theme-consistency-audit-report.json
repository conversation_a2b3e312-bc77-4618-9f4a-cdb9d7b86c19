{"themeConsistency": {"score": 100, "issues": [{"type": "HARDCODED_COLOR", "severity": "MEDIUM", "file": "src/components/ui/chart.tsx", "message": "Hardcoded color found: #ccc", "line": 53, "suggestion": "Consider using theme variables or Tailwind classes"}, {"type": "HARDCODED_COLOR", "severity": "MEDIUM", "file": "src/components/ui/chart.tsx", "message": "Hardcoded color found: #ccc", "line": 53, "suggestion": "Consider using theme variables or Tailwind classes"}, {"type": "HARDCODED_COLOR", "severity": "MEDIUM", "file": "src/components/ui/chart.tsx", "message": "Hardcoded color found: #ccc", "line": 53, "suggestion": "Consider using theme variables or Tailwind classes"}, {"type": "HARDCODED_COLOR", "severity": "MEDIUM", "file": "src/components/visualization/AIProcessVisualization.tsx", "message": "Hardcoded color found: #4A90E2", "line": 51, "suggestion": "Consider using theme variables or Tailwind classes"}, {"type": "HARDCODED_COLOR", "severity": "MEDIUM", "file": "src/components/visualization/AIProcessVisualization.tsx", "message": "Hardcoded color found: #F5A623", "line": 57, "suggestion": "Consider using theme variables or Tailwind classes"}, {"type": "HARDCODED_COLOR", "severity": "MEDIUM", "file": "src/components/visualization/CinematicCabinetViewer.tsx", "message": "Hardcoded color found: #8B9A6F", "line": 80, "suggestion": "Consider using theme variables or Tailwind classes"}, {"type": "HARDCODED_COLOR", "severity": "MEDIUM", "file": "src/components/visualization/CinematicCabinetViewer.tsx", "message": "Hardcoded color found: #4A5A3F", "line": 81, "suggestion": "Consider using theme variables or Tailwind classes"}, {"type": "HARDCODED_COLOR", "severity": "MEDIUM", "file": "src/components/visualization/CinematicCabinetViewer.tsx", "message": "Hardcoded color found: #7A8A5F", "line": 82, "suggestion": "Consider using theme variables or Tailwind classes"}, {"type": "HARDCODED_COLOR", "severity": "MEDIUM", "file": "src/components/visualization/CinematicCabinetViewer.tsx", "message": "Hardcoded color found: #2A3A1F", "line": 110, "suggestion": "Consider using theme variables or Tailwind classes"}, {"type": "HARDCODED_COLOR", "severity": "MEDIUM", "file": "src/components/visualization/CinematicCabinetViewer.tsx", "message": "Hardcoded color found: #374151", "line": 127, "suggestion": "Consider using theme variables or Tailwind classes"}, {"type": "HARDCODED_COLOR", "severity": "MEDIUM", "file": "src/components/visualization/CinematicCabinetViewer.tsx", "message": "Hardcoded color found: #4A5A3F", "line": 81, "suggestion": "Consider using theme variables or Tailwind classes"}], "recommendations": [{"priority": "MEDIUM", "action": "Replace hardcoded colors with theme variables", "description": "Convert hardcoded color values to use CSS custom properties or Tailwind classes"}]}, "shadcnIntegration": {"score": 100, "issues": [], "recommendations": []}, "cssArchitecture": {"score": 62, "issues": [{"type": "DUPLICATE_CSS", "severity": "MEDIUM", "message": "Found 19 duplicate CSS rules", "details": [{"selector": ".logo", "files": ["src/App.css", "src/App.css"]}, {"selector": ".dark", "files": ["src/index.css", "src/index.css"]}, {"selector": ".dark", "files": ["src/index.css", "src/index.css"]}, {"selector": ".glass-effect", "files": ["src/index.css", "src/index.css"]}, {"selector": ".aone-input", "files": ["src/index.css", "src/index.css"]}]}], "recommendations": [{"priority": "MEDIUM", "action": "Consolidate duplicate CSS rules", "description": "Create reusable utility classes to eliminate CSS duplication"}]}, "aoneCompliance": {"score": 80, "issues": [{"type": "LOW_AONE_USAGE", "severity": "MEDIUM", "message": "Low A.ONE design system usage: 8.2%", "suggestion": "Increase usage of A.ONE design system classes"}], "recommendations": [{"priority": "MEDIUM", "action": "Increase A.ONE design system adoption", "description": "Replace generic classes with A.ONE design system classes for better consistency"}], "metrics": {"complianceRatio": "8.2", "aoneClassUsage": 276, "totalClassUsage": 3371}}, "accessibility": {"score": 100, "issues": [], "recommendations": []}, "overall": {"score": 88, "summary": "", "criticalIssues": [], "prioritizedRecommendations": [{"priority": "MEDIUM", "action": "Replace hardcoded colors with theme variables", "description": "Convert hardcoded color values to use CSS custom properties or Tailwind classes"}, {"priority": "MEDIUM", "action": "Consolidate duplicate CSS rules", "description": "Create reusable utility classes to eliminate CSS duplication"}, {"priority": "MEDIUM", "action": "Increase A.ONE design system adoption", "description": "Replace generic classes with A.ONE design system classes for better consistency"}]}}